package com.xinghuo.project.template.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.project.template.entity.IssueLibraryEntity;
import com.xinghuo.project.template.model.IssueLibraryPagination;
import com.xinghuo.project.template.model.vo.IssueLibraryVO;
import com.xinghuo.project.template.model.vo.IssueLibrarySelectVO;
import com.xinghuo.project.template.service.IssueLibraryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 标准项目问题库管理控制器
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Slf4j
@Tag(name = "标准项目问题库管理", description = "标准项目问题库管理相关接口")
@RestController
@RequestMapping("/api/project/template/issueLibrary")
public class IssueLibraryController {

    @Resource
    private IssueLibraryService issueLibraryService;

    /**
     * 获取问题库列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取问题库列表")
    public ActionResult<PageListVO<IssueLibraryVO>> list(@RequestBody IssueLibraryPagination pagination) {
        try {
            List<IssueLibraryEntity> list = issueLibraryService.getList(pagination);
            List<IssueLibraryVO> listVO = BeanCopierUtils.copyList(list, IssueLibraryVO.class);

            // 对结果进行数据转换和补充
            for (IssueLibraryVO vo : listVO) {
                // 状态名称转换
                if (vo.getStatus() != null) {
                    switch (vo.getStatus()) {
                        case "draft":
                            vo.setStatusName("草稿");
                            break;
                        case "published":
                            vo.setStatusName("已发布");
                            break;
                        case "archived":
                            vo.setStatusName("归档");
                            break;
                        default:
                            vo.setStatusName(vo.getStatus());
                    }
                }

                // TODO: 可以在这里添加其他关联数据的查询和设置
                // 例如：创建用户名称、问题类别名称、优先级名称等
            }

            PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
            return ActionResult.page(listVO, page);
        } catch (Exception e) {
            log.error("获取问题库列表失败", e);
            return ActionResult.fail("获取问题库列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据状态获取问题库列表
     */
    @GetMapping("/getListByStatus/{status}")
    @Operation(summary = "根据状态获取问题库列表")
    public ActionResult<List<IssueLibraryEntity>> getListByStatus(
            @Parameter(description = "状态") @PathVariable String status) {
        try {
            List<IssueLibraryEntity> list = issueLibraryService.getListByStatus(status);
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("根据状态获取问题库列表失败", e);
            return ActionResult.fail("获取问题库列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据问题类别获取问题库列表
     */
    @GetMapping("/getListByIssueCategory/{issueCategoryId}")
    @Operation(summary = "根据问题类别获取问题库列表")
    public ActionResult<List<IssueLibraryEntity>> getListByIssueCategory(
            @Parameter(description = "问题类别ID") @PathVariable String issueCategoryId) {
        try {
            List<IssueLibraryEntity> list = issueLibraryService.getListByIssueCategory(issueCategoryId);
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("根据问题类别获取问题库列表失败", e);
            return ActionResult.fail("获取问题库列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据优先级获取问题库列表
     */
    @GetMapping("/getListByPriority/{priorityId}")
    @Operation(summary = "根据优先级获取问题库列表")
    public ActionResult<List<IssueLibraryEntity>> getListByPriority(
            @Parameter(description = "优先级ID") @PathVariable String priorityId) {
        try {
            List<IssueLibraryEntity> list = issueLibraryService.getListByPriority(priorityId);
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("根据优先级获取问题库列表失败", e);
            return ActionResult.fail("获取问题库列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取问题库详情
     */
    @GetMapping("/getInfo/{id}")
    @Operation(summary = "获取问题库详情")
    public ActionResult<IssueLibraryEntity> getInfo(
            @Parameter(description = "问题库ID") @PathVariable String id) {
        try {
            IssueLibraryEntity entity = issueLibraryService.getInfo(id);
            if (entity == null) {
                return ActionResult.fail("问题库不存在");
            }
            return ActionResult.success(entity);
        } catch (Exception e) {
            log.error("获取问题库详情失败", e);
            return ActionResult.fail("获取问题库详情失败：" + e.getMessage());
        }
    }

    /**
     * 根据编码获取问题库
     */
    @GetMapping("/getByCode/{code}")
    @Operation(summary = "根据编码获取问题库")
    public ActionResult<IssueLibraryEntity> getByCode(
            @Parameter(description = "问题编码") @PathVariable String code) {
        try {
            IssueLibraryEntity entity = issueLibraryService.getByCode(code);
            if (entity == null) {
                return ActionResult.fail("问题库不存在");
            }
            return ActionResult.success(entity);
        } catch (Exception e) {
            log.error("根据编码获取问题库失败", e);
            return ActionResult.fail("获取问题库失败：" + e.getMessage());
        }
    }

    /**
     * 创建问题库
     */
    @PostMapping("/create")
    @Operation(summary = "创建问题库")
    public ActionResult<String> create(@RequestBody @Valid IssueLibraryEntity entity) {
        try {
            String id = issueLibraryService.create(entity);
            return ActionResult.success("创建成功", id);
        } catch (Exception e) {
            log.error("创建问题库失败", e);
            return ActionResult.fail("创建问题库失败：" + e.getMessage());
        }
    }

    /**
     * 更新问题库
     */
    @PutMapping("/update/{id}")
    @Operation(summary = "更新问题库")
    public ActionResult<String> update(
            @Parameter(description = "问题库ID") @PathVariable String id,
            @RequestBody @Valid IssueLibraryEntity entity) {
        try {
            issueLibraryService.update(id, entity);
            return ActionResult.success("更新成功");
        } catch (Exception e) {
            log.error("更新问题库失败", e);
            return ActionResult.fail("更新问题库失败：" + e.getMessage());
        }
    }

    /**
     * 删除问题库
     */
    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除问题库")
    public ActionResult<String> delete(
            @Parameter(description = "问题库ID") @PathVariable String id) {
        try {
            issueLibraryService.delete(id);
            return ActionResult.success("删除成功");
        } catch (Exception e) {
            log.error("删除问题库失败", e);
            return ActionResult.fail("删除问题库失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除问题库
     */
    @DeleteMapping("/batchDelete")
    @Operation(summary = "批量删除问题库")
    public ActionResult<String> batchDelete(@RequestBody List<String> ids) {
        try {
            issueLibraryService.batchDelete(ids);
            return ActionResult.success("批量删除成功");
        } catch (Exception e) {
            log.error("批量删除问题库失败", e);
            return ActionResult.fail("批量删除问题库失败：" + e.getMessage());
        }
    }

    /**
     * 更新问题库状态
     */
    @PutMapping("/updateStatus/{id}")
    @Operation(summary = "更新问题库状态")
    public ActionResult<String> updateStatus(
            @Parameter(description = "问题库ID") @PathVariable String id,
            @RequestParam String status) {
        try {
            issueLibraryService.updateStatus(id, status);
            return ActionResult.success("状态更新成功");
        } catch (Exception e) {
            log.error("更新问题库状态失败", e);
            return ActionResult.fail("更新问题库状态失败：" + e.getMessage());
        }
    }

    /**
     * 批量更新状态
     */
    @PutMapping("/batchUpdateStatus")
    @Operation(summary = "批量更新状态")
    public ActionResult<String> batchUpdateStatus(
            @RequestBody List<String> ids,
            @RequestParam String status) {
        try {
            issueLibraryService.batchUpdateStatus(ids, status);
            return ActionResult.success("批量更新状态成功");
        } catch (Exception e) {
            log.error("批量更新问题库状态失败", e);
            return ActionResult.fail("批量更新状态失败：" + e.getMessage());
        }
    }

    /**
     * 发布问题库
     */
    @PutMapping("/publish/{id}")
    @Operation(summary = "发布问题库")
    public ActionResult<String> publish(
            @Parameter(description = "问题库ID") @PathVariable String id) {
        try {
            issueLibraryService.publish(id);
            return ActionResult.success("发布成功");
        } catch (Exception e) {
            log.error("发布问题库失败", e);
            return ActionResult.fail("发布问题库失败：" + e.getMessage());
        }
    }

    /**
     * 归档问题库
     */
    @PutMapping("/archive/{id}")
    @Operation(summary = "归档问题库")
    public ActionResult<String> archive(
            @Parameter(description = "问题库ID") @PathVariable String id) {
        try {
            issueLibraryService.archive(id);
            return ActionResult.success("归档成功");
        } catch (Exception e) {
            log.error("归档问题库失败", e);
            return ActionResult.fail("归档问题库失败：" + e.getMessage());
        }
    }

    /**
     * 批量发布问题库
     */
    @PutMapping("/batchPublish")
    @Operation(summary = "批量发布问题库")
    public ActionResult<String> batchPublish(@RequestBody List<String> ids) {
        try {
            issueLibraryService.batchPublish(ids);
            return ActionResult.success("批量发布成功");
        } catch (Exception e) {
            log.error("批量发布问题库失败", e);
            return ActionResult.fail("批量发布问题库失败：" + e.getMessage());
        }
    }

    /**
     * 批量归档问题库
     */
    @PutMapping("/batchArchive")
    @Operation(summary = "批量归档问题库")
    public ActionResult<String> batchArchive(@RequestBody List<String> ids) {
        try {
            issueLibraryService.batchArchive(ids);
            return ActionResult.success("批量归档成功");
        } catch (Exception e) {
            log.error("批量归档问题库失败", e);
            return ActionResult.fail("批量归档问题库失败：" + e.getMessage());
        }
    }

    /**
     * 复制问题库
     */
    @PostMapping("/copy/{id}")
    @Operation(summary = "复制问题库")
    public ActionResult<String> copy(
            @Parameter(description = "问题库ID") @PathVariable String id,
            @RequestParam String newTitle) {
        try {
            String newId = issueLibraryService.copy(id, newTitle);
            return ActionResult.success("复制成功", newId);
        } catch (Exception e) {
            log.error("复制问题库失败", e);
            return ActionResult.fail("复制问题库失败：" + e.getMessage());
        }
    }

    /**
     * 检查问题编码是否存在
     */
    @GetMapping("/checkCodeExists")
    @Operation(summary = "检查问题编码是否存在")
    public ActionResult<Boolean> checkCodeExists(
            @RequestParam String code,
            @RequestParam(required = false) String excludeId) {
        try {
            boolean exists = issueLibraryService.isExistByCode(code, excludeId);
            return ActionResult.success(exists);
        } catch (Exception e) {
            log.error("检查问题编码失败", e);
            return ActionResult.fail("检查问题编码失败：" + e.getMessage());
        }
    }

    /**
     * 检查问题标题是否存在
     */
    @GetMapping("/checkTitleExists")
    @Operation(summary = "检查问题标题是否存在")
    public ActionResult<Boolean> checkTitleExists(
            @RequestParam String title,
            @RequestParam(required = false) String excludeId) {
        try {
            boolean exists = issueLibraryService.isExistByTitle(title, excludeId);
            return ActionResult.success(exists);
        } catch (Exception e) {
            log.error("检查问题标题失败", e);
            return ActionResult.fail("检查问题标题失败：" + e.getMessage());
        }
    }

    /**
     * 获取问题库选择列表
     */
    @GetMapping("/getSelectList")
    @Operation(summary = "获取问题库选择列表")
    public ActionResult<List<IssueLibrarySelectVO>> getSelectList(
            @RequestParam(required = false) String keyword) {
        try {
            List<IssueLibraryEntity> list = issueLibraryService.getSelectList(keyword);
            List<IssueLibrarySelectVO> listVO = BeanCopierUtils.copyList(list, IssueLibrarySelectVO.class);

            // 构建fullName字段，格式：[编码] 标题
            for (IssueLibrarySelectVO vo : listVO) {
                if (vo.getCode() != null && vo.getTitle() != null) {
                    vo.setFullName("[" + vo.getCode() + "] " + vo.getTitle());
                } else if (vo.getTitle() != null) {
                    vo.setFullName(vo.getTitle());
                } else {
                    vo.setFullName(vo.getId());
                }
            }

            return ActionResult.success(listVO);
        } catch (Exception e) {
            log.error("获取问题库选择列表失败", e);
            return ActionResult.fail("获取问题库选择列表失败：" + e.getMessage());
        }
    }

    /**
     * 生成问题编码
     */
    @GetMapping("/generateCode")
    @Operation(summary = "生成问题编码")
    public ActionResult<String> generateCode() {
        try {
            String code = issueLibraryService.generateCode();
            return ActionResult.success(code);
        } catch (Exception e) {
            log.error("生成问题编码失败", e);
            return ActionResult.fail("生成问题编码失败：" + e.getMessage());
        }
    }

    /**
     * 获取问题库使用情况
     */
    @GetMapping("/getIssueLibraryUsageInfo/{id}")
    @Operation(summary = "获取问题库使用情况")
    public ActionResult<Map<String, Object>> getIssueLibraryUsageInfo(
            @Parameter(description = "问题库ID") @PathVariable String id) {
        try {
            Map<String, Object> usageInfo = issueLibraryService.getIssueLibraryUsageInfo(id);
            return ActionResult.success(usageInfo);
        } catch (Exception e) {
            log.error("获取问题库使用情况失败", e);
            return ActionResult.fail("获取问题库使用情况失败：" + e.getMessage());
        }
    }
}
