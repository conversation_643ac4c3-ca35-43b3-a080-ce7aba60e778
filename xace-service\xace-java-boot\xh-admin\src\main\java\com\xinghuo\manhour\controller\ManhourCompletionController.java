package com.xinghuo.manhour.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.manhour.model.completion.*;
import com.xinghuo.manhour.service.ManhourCompletionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 工时填写情况分析控制器
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Slf4j
@RestController
@RequestMapping("/api/manhour/completion")
@Tag(name = "工时填写情况分析", description = "工时填写情况分析相关接口")
public class ManhourCompletionController {

    @Autowired
    private ManhourCompletionService manhourCompletionService;

    /**
     * 获取工时填写情况概览数据
     *
     * @param params 查询参数
     * @return 概览数据
     */
    @GetMapping("/overview")
    @Operation(summary = "获取工时填写情况概览数据")
    public ActionResult<WorkhourCompletionOverview> getOverview(WorkhourCompletionParams params) {
        WorkhourCompletionOverview overview = manhourCompletionService.getOverview(params);
        return ActionResult.success(overview);
    }

    /**
     * 获取工时填写情况图表数据
     *
     * @param params 查询参数
     * @return 图表数据
     */
    @GetMapping("/charts")
    @Operation(summary = "获取工时填写情况图表数据")
    public ActionResult<CompletionChartDataModel> getCharts(WorkhourCompletionParams params) {
        CompletionChartDataModel chartData = manhourCompletionService.getCharts(params);
        return ActionResult.success(chartData);
    }

    /**
     * 获取未填写人员列表
     *
     * @param pagination 分页参数
     * @return 未填写人员数据列表
     */
    @PostMapping("/unfilled/list")
    @Operation(summary = "获取未填写人员列表")
    public ActionResult getUnfilledUsersList(@RequestBody WorkhourCompletionPagination pagination) {
        List<UnfilledUserVO> list = manhourCompletionService.getUnfilledUsersList(pagination);

        // 创建PaginationVO对象
        PaginationVO paginationVO = new PaginationVO();
        paginationVO.setCurrentPage(pagination.getCurrentPage());
        paginationVO.setPageSize(pagination.getPageSize());
        paginationVO.setTotal(pagination.getTotal());

        return ActionResult.page(list, paginationVO);
    }

    /**
     * 获取待审批记录列表
     *
     * @param pagination 分页参数
     * @return 待审批记录数据列表
     */
    @PostMapping("/pending/list")
    @Operation(summary = "获取待审批记录列表")
    public ActionResult getPendingApprovalList(@RequestBody WorkhourCompletionPagination pagination) {
        List<PendingApprovalVO> list = manhourCompletionService.getPendingApprovalList(pagination);

        PaginationVO paginationVO = new PaginationVO();
        paginationVO.setCurrentPage(pagination.getCurrentPage());
        paginationVO.setPageSize(pagination.getPageSize());
        paginationVO.setTotal(pagination.getTotal());

        return ActionResult.page(list, paginationVO);
    }

    /**
     * 获取分部完成情况列表
     *
     * @param pagination 分页参数
     * @return 分部完成情况数据列表
     */
    @PostMapping("/department/list")
    @Operation(summary = "获取分部完成情况列表")
    public ActionResult getDepartmentCompletionList(@RequestBody WorkhourCompletionPagination pagination) {
        List<DepartmentCompletionVO> list = manhourCompletionService.getDepartmentCompletionList(pagination);

        PaginationVO paginationVO = new PaginationVO();
        paginationVO.setCurrentPage(pagination.getCurrentPage());
        paginationVO.setPageSize(pagination.getPageSize());
        paginationVO.setTotal(pagination.getTotal());

        return ActionResult.page(list, paginationVO);
    }

    /**
     * 获取负责人统计列表
     *
     * @param pagination 分页参数
     * @return 负责人统计数据列表
     */
    @PostMapping("/leader/list")
    @Operation(summary = "获取负责人统计列表")
    public ActionResult getLeaderStatisticsList(@RequestBody WorkhourCompletionPagination pagination) {
        List<LeaderStatisticsVO> list = manhourCompletionService.getLeaderStatisticsList(pagination);

        PaginationVO paginationVO = new PaginationVO();
        paginationVO.setCurrentPage(pagination.getCurrentPage());
        paginationVO.setPageSize(pagination.getPageSize());
        paginationVO.setTotal(pagination.getTotal());

        return ActionResult.page(list, paginationVO);
    }

    /**
     * 导出工时填写情况报表
     *
     * @param params   查询参数
     * @param response HTTP响应
     */
    @PostMapping("/export")
    @Operation(summary = "导出工时填写情况报表")
    public void exportWorkhourCompletion(@RequestBody WorkhourCompletionParams params, HttpServletResponse response) {
        manhourCompletionService.exportWorkhourCompletion(params, response);
    }

    /**
     * 发送用户提醒
     *
     * @param request 提醒请求
     * @return 提醒结果
     */
    @PostMapping("/notify/user")
    @Operation(summary = "发送用户提醒")
    public ActionResult<String> notifyUser(@RequestBody NotifyUserRequest request) {
        manhourCompletionService.notifyUser(request.getUserId(), request.getMonth());
        return ActionResult.success("提醒发送成功");
    }

    /**
     * 发送负责人提醒
     *
     * @param request 提醒请求
     * @return 提醒结果
     */
    @PostMapping("/notify/leader")
    @Operation(summary = "发送负责人提醒")
    public ActionResult<String> notifyLeader(@RequestBody NotifyLeaderRequest request) {
        manhourCompletionService.notifyLeader(request.getLeaderId());
        return ActionResult.success("提醒发送成功");
    }

    /**
     * 批量发送用户提醒
     *
     * @param request 批量提醒请求
     * @return 批量提醒结果
     */
    @PostMapping("/notify/batch")
    @Operation(summary = "批量发送用户提醒")
    public ActionResult<String> batchNotifyUsers(@RequestBody BatchNotifyRequest request) {
        manhourCompletionService.batchNotifyUsers(request.getUserIds(), request.getMonth());
        return ActionResult.success("批量提醒发送成功");
    }

    /**
     * 获取分部选择器数据
     *
     * @return 分部列表
     */
    @GetMapping("/selector/department")
    @Operation(summary = "获取分部选择器数据")
    public ActionResult<List<Map<String, Object>>> getDepartmentSelector() {
        List<Map<String, Object>> departments = manhourCompletionService.getDepartmentSelector();
        return ActionResult.success(departments);
    }

    /**
     * 获取填写状态统计
     *
     * @param params 查询参数
     * @return 填写状态统计
     */
    @GetMapping("/status/statistics")
    @Operation(summary = "获取填写状态统计")
    public ActionResult<List<Map<String, Object>>> getCompletionStatusStatistics(WorkhourCompletionParams params) {
        List<Map<String, Object>> statistics = manhourCompletionService.getCompletionStatusStatistics(params);
        return ActionResult.success(statistics);
    }

    /**
     * 获取审批效率统计
     *
     * @param params 查询参数
     * @return 审批效率统计
     */
    @GetMapping("/approval/statistics")
    @Operation(summary = "获取审批效率统计")
    public ActionResult<List<Map<String, Object>>> getApprovalEfficiencyStatistics(WorkhourCompletionParams params) {
        List<Map<String, Object>> statistics = manhourCompletionService.getApprovalEfficiencyStatistics(params);
        return ActionResult.success(statistics);
    }

    /**
     * 获取逾期统计
     *
     * @param params 查询参数
     * @return 逾期统计
     */
    @GetMapping("/overdue/statistics")
    @Operation(summary = "获取逾期统计")
    public ActionResult<List<Map<String, Object>>> getOverdueStatistics(WorkhourCompletionParams params) {
        List<Map<String, Object>> statistics = manhourCompletionService.getOverdueStatistics(params);
        return ActionResult.success(statistics);
    }
}
