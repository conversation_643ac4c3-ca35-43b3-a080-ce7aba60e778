<template>
  <div class="customer-management">
    <!-- 测试区域 -->
    <div class="test-area" style="margin-bottom: 16px; padding: 16px; background: var(--section-bg-color); border-radius: 6px">
      <div style="display: flex; align-items: center; gap: 16px">
        <span style="font-weight: 500">组件测试：</span>
        <Button type="primary" @click="handleTestModal"> 测试客户单位选择组件 </Button>
        <Button type="default" @click="handleTestDrawer"> 测试抽屉模式 </Button>
        <Button type="dashed" @click="handleDebugTest"> 调试测试 </Button>
      </div>
    </div>

    <div class="customer-management-container">
      <!-- 左侧客户单位列表 -->
      <div class="customer-list-container">
        <Card :bordered="false" :bodyStyle="{ padding: '0' }" class="customer-card">
          <BasicTable @register="registerCustomerTable">
            <template #tableTitle>
              <div class="card-title">
                <Button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="handleCreateCustomer"> 新增客户单位 </Button>
              </div>
            </template>
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'custType'">
                <Tag :color="getCustomerTypeColor(record.custType)">
                  {{ getCustTypeText(record.custType) }}
                </Tag>
              </template>
              <template v-if="column.key === 'type'">
                {{ getTypeText(record.custLine) }}
              </template>
              <template v-if="column.key === 'action'">
                <TableAction
                  :actions="[
                    {
                      icon: 'clarity:note-edit-line',
                      label: '编辑',
                      onClick: handleEditCustomer.bind(null, record),
                    },
                    {
                      icon: 'ant-design:delete-outlined',
                      color: 'error',
                      label: '删除',
                      popConfirm: {
                        title: '是否确认删除',
                        confirm: handleDeleteCustomer.bind(null, record),
                      },
                    },
                  ]" />
              </template>
            </template>
          </BasicTable>
        </Card>
      </div>

      <!-- 右侧联系人列表 -->
      <div class="linkman-list-container">
        <Card :bordered="false" :bodyStyle="{ padding: '0' }" class="linkman-card">
          <template #title>
            <div class="card-title">
              <span class="customer-name">
                {{ viewMode === 'single' ? (currentCustomer ? `${currentCustomer.name}的联系人` : '请选择客户单位') : '全部联系人' }}
              </span>
            </div>
          </template>
          <div v-if="viewMode === 'single' && !currentCustomer" class="empty-tip">
            <Empty description="请先选择左侧的客户单位" :image="Empty.PRESENTED_IMAGE_SIMPLE" />
            <p class="empty-help-text">从左侧列表中选择一个客户单位，查看其联系人信息</p>
          </div>
          <BasicTable v-else @register="registerLinkmanTable" @row-click="handleLinkmanRowClick">
            <template #tableTitle>
              <div class="title-actions">
                <Button :type="viewMode === 'all' ? 'primary' : 'default'" @click="toggleViewMode" style="margin-right: 8px">
                  {{ viewMode === 'single' ? '查看全部' : '返回单个' }}
                </Button>
                <Button v-if="viewMode === 'single' && currentCustomer" type="primary" preIcon="icon-ym icon-ym-btn-add" @click="handleCreateLinkman">
                  新增联系人
                </Button>
              </div>
            </template>
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'status'">
                <Tag :color="record.status === 1 ? 'success' : 'default'">
                  {{ record.status === 1 ? '有效' : '无效' }}
                </Tag>
              </template>
              <template v-if="column.key === 'action'">
                <TableAction
                  :actions="[
                    {
                      icon: 'clarity:note-edit-line',
                      label: '编辑',
                      onClick: handleEditLinkman.bind(null, record),
                    },
                    {
                      icon: record.status === 1 ? 'ant-design:stop-outlined' : 'ant-design:check-outlined',
                      color: record.status === 1 ? 'warning' : 'success',
                      label: record.status === 1 ? '禁用' : '启用',
                      onClick: handleToggleLinkmanStatus.bind(null, record),
                    },
                    {
                      icon: 'ant-design:delete-outlined',
                      color: 'error',
                      label: '删除',
                      popConfirm: {
                        title: '是否确认删除',
                        confirm: handleDeleteLinkman.bind(null, record),
                      },
                    },
                  ]" />
              </template>
            </template>
          </BasicTable>
        </Card>
      </div>
    </div>

    <!-- 客户单位表单抽屉 -->
    <CustomerDrawer @register="registerCustomerDrawer" @success="handleCustomerSuccess" />

    <!-- 联系人表单抽屉 -->
    <LinkmanDrawer @register="registerLinkmanDrawer" @success="handleLinkmanSuccess" :customerId="currentCustomer?.id" :customerName="currentCustomer?.name" />

    <!-- 测试模态框 -->
    <Modal v-model:visible="showTestModal" title="客户单位选择组件测试" width="800px" :footer="null" destroyOnClose>
      <CustomerSelectTest />
    </Modal>

    <!-- 测试抽屉 -->
    <Drawer
      :visible="showTestDrawer"
      @update:visible="showTestDrawer = $event"
      title="客户单位选择组件测试 - 抽屉模式"
      width="60%"
      :closable="true"
      destroyOnClose>
      <CustomerSelectTest />
    </Drawer>
  </div>
</template>

<script lang="ts" setup>
  import { ref, watch, onMounted, h, nextTick } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useDrawer } from '/@/components/Drawer';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { Empty, Tag, Button, Card, Modal, Drawer } from 'ant-design-vue';
  import CustomerDrawer from './CustomerDrawer.vue';
  import LinkmanDrawer from './LinkmanDrawer.vue';
  import CustomerSelectTest from '../components/CustomerSelectTest.vue';
  import { useBaseStore } from '/@/store/modules/base';
  import { getDictionaryDataSelector } from '/@/api/systemData/dictionary';
  import { getCustomerList, deleteCustomer } from '/@/api/project/customer';
  import { getLinkmanList, deleteCustomerLinkman, updateCustomerLinkmanStatus, ProjCustomerLinkmanModel } from '/@/api/project/customerLinkman/index';

  // 客户单位模型接口
  interface ProjCustomerModel {
    id: string;
    name: string;
    custType: string;
    custLine: string;
    leader: string;
    remark: string;
    creatorUserId: string;
    creatorTime: string;
  }

  const { createMessage } = useMessage();
  const baseStore = useBaseStore();
  const currentCustomer = ref<ProjCustomerModel | null>(null);

  // 查看模式：'single' - 单个客户的联系人，'all' - 全部联系人
  const viewMode = ref<'single' | 'all'>('single');

  // 测试相关状态
  const showTestModal = ref(false);
  const showTestDrawer = ref(false);

  // 单位类型字典数据
  const typeDict = ref<any[]>([]);
  // 客户类型字典数据
  const custTypeDict = ref<any[]>([]);

  // 获取客户单位类型颜色
  function getCustomerTypeColor(type: string) {
    const typeMap: Record<string, string> = {
      SJ: 'blue',
      JZFJX: 'green',
      ZSX: 'orange',
      WDSCX: 'warning',
      ZBF: 'purple',
    };
    return typeMap[type] || 'default';
  }

  // 获取客户类型文本
  function getCustTypeText(custType: string) {
    const item = custTypeDict.value.find(item => item.enCode === custType);
    return item ? item.fullName : custType;
  }

  // 获取单位类型文本
  function getTypeText(type: string) {
    const item = typeDict.value.find(item => item.enCode === type);
    return item ? item.fullName : type;
  }

  // 客户单位表格列定义
  const customerColumns = [
    {
      title: '单位名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
    },
    {
      title: '单位类型',
      dataIndex: 'custType',
      key: 'custType',
      width: 120,
    },
    {
      title: '业务线',
      dataIndex: 'custLine',
      key: 'type',
      width: 120,
    },
    {
      title: '市场负责人',
      dataIndex: 'leaderName',
      key: 'leaderName',
      width: 120,
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right' as const,
    },
  ];

  // 联系人表格列定义
  const linkmanColumns = [
    {
      title: '客户单位',
      dataIndex: 'customerName',
      key: 'customerName',
      width: 180,
      customRender: ({ record }) => {
        if (viewMode.value === 'all' && record.customerName) {
          return h(
            'a',
            {
              style: { color: '#1890ff', cursor: 'pointer' },
              onClick: () => handleLinkmanRowClick(record),
            },
            record.customerName,
          );
        }
        return record.customerName || '-';
      },
    },
    {
      title: '姓名',
      dataIndex: 'linkman',
      key: 'linkman',
      width: 120,
    },
    {
      title: '联系电话',
      dataIndex: 'telephone',
      key: 'telephone',
      width: 150,
    },
    {
      title: '岗位',
      dataIndex: 'topic',
      key: 'topic',
      width: 120,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      customRender: ({ record }) => {
        return record.status === 1 ? '有效' : '无效';
      },
    },
    {
      title: '备注',
      dataIndex: 'note',
      key: 'note',
      width: 200,
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      fixed: 'right' as const,
    },
  ];

  // 客户单位搜索表单配置
  const customerSearchFormSchema = [
    {
      field: 'name',
      label: '单位名称',
      component: 'Input' as const,
      colProps: { span: 8 },
    },
    {
      field: 'custType',
      label: '单位类型',
      component: 'Select' as const,
      componentProps: {
        options: [],
        fieldNames: { label: 'fullName', value: 'enCode' },
      },
      colProps: { span: 8 },
    },
    {
      field: 'custLine',
      label: '业务线',
      component: 'Select' as const,
      componentProps: {
        options: [],
        fieldNames: { label: 'fullName', value: 'enCode' },
      },
      colProps: { span: 8 },
    },
  ];

  // 联系人搜索表单配置
  const linkmanSearchFormSchema = [
    {
      field: 'linkman',
      label: '姓名',
      component: 'Input' as const,
      colProps: { span: 8 },
    },
    {
      field: 'telephone',
      label: '电话',
      component: 'Input' as const,
      colProps: { span: 8 },
    },
    {
      field: 'status',
      label: '状态',
      component: 'Select' as const,
      componentProps: {
        options: [
          { fullName: '全部', id: '' },
          { fullName: '有效', id: 1 },
          { fullName: '无效', id: 0 },
        ],
      },
      colProps: { span: 8 },
    },
  ];

  // 加载数据字典
  async function loadDictionaries() {
    try {
      // 加载业务线字典 (custLine)
      const typeRes = await getDictionaryDataSelector('custLine');
      if (typeRes && typeRes.data && typeRes.data.list) {
        typeDict.value = typeRes.data.list;

        // 更新业务线搜索表单的选项
        const custLineOptions = [{ fullName: '全部', id: '' }].concat(typeRes.data.list);
        const form = getForm();
        if (form) {
          form.updateSchema([
            {
              field: 'custLine',
              componentProps: { options: custLineOptions },
            },
          ]);
        }
      }

      // 加载单位类型字典 (dwlx)
      const custTypeRes = await getDictionaryDataSelector('dwlx');
      if (custTypeRes && custTypeRes.data && custTypeRes.data.list) {
        custTypeDict.value = custTypeRes.data.list;

        // 更新单位类型搜索表单的选项
        const custTypeOptions = [{ fullName: '全部', id: '' }].concat(custTypeRes.data.list);
        const form = getForm();
        if (form) {
          form.updateSchema([
            {
              field: 'custType',
              componentProps: { options: custTypeOptions },
            },
          ]);
        }
      }
    } catch (error) {
      console.error('加载数据字典失败:', error);
    }
  }

  // 初始化加载数据字典
  onMounted(() => {
    loadDictionaries();
  });

  // 注册客户单位表格
  const [registerCustomerTable, { reload: reloadCustomerTable, getForm, getDataSource }] = useTable({
    title: '客户单位列表',
    api: getCustomerList,
    columns: customerColumns,
    rowKey: 'id',
    showIndexColumn: false,
    clickToRowSelect: true,
    useSearchForm: true,
    formConfig: {
      labelWidth: 80,
      schemas: customerSearchFormSchema,
    },
    pagination: {
      pageSize: 10,
    },
    bordered: true,
    canResize: true,
    striped: false,
    showTableSetting: true,
    tableSetting: { fullScreen: true },
    immediate: true,
    rowSelection: {
      type: 'radio',
      onChange: (_, selectedRows) => {
        if (selectedRows && selectedRows.length > 0) {
          currentCustomer.value = selectedRows[0];
        } else {
          currentCustomer.value = null;
        }
      },
    },
  });

  // 注册联系人表格
  const [registerLinkmanTable, { reload: reloadLinkmanTable }] = useTable({
    title: '联系人列表',
    api: getLinkmanList,
    columns: linkmanColumns,
    rowKey: 'id',
    showIndexColumn: false,
    useSearchForm: true,
    formConfig: {
      labelWidth: 80,
      schemas: linkmanSearchFormSchema,
    },
    pagination: {
      pageSize: 10,
    },
    bordered: true,
    canResize: true,
    striped: false,
    showTableSetting: true,
    tableSetting: { fullScreen: true },
    immediate: false,
    beforeFetch: params => {
      // 根据查看模式决定是否传递客户单位ID
      if (viewMode.value === 'single' && currentCustomer.value) {
        params.customerId = currentCustomer.value.id;
      }
      // 如果是查看全部模式，不传递cuId参数
      return params;
    },
  });

  // 注册客户单位抽屉
  const [registerCustomerDrawer, { openDrawer: openCustomerDrawer }] = useDrawer();

  // 注册联系人抽屉
  const [registerLinkmanDrawer, { openDrawer: openLinkmanDrawer }] = useDrawer();

  // 新增客户单位
  function handleCreateCustomer() {
    openCustomerDrawer(true, {
      isUpdate: false,
    });
  }

  // 编辑客户单位
  function handleEditCustomer(record: ProjCustomerModel) {
    openCustomerDrawer(true, {
      record,
      isUpdate: true,
    });
  }

  // 删除客户单位
  async function handleDeleteCustomer(record: ProjCustomerModel) {
    try {
      await deleteCustomer(record.id);
      createMessage.success('删除成功');
      reloadCustomerTable();

      // 如果删除的是当前选中的客户单位，清空当前选中
      if (currentCustomer.value && currentCustomer.value.id === record.id) {
        currentCustomer.value = null;
      }
    } catch (error) {
      console.error('删除客户单位失败:', error);
      createMessage.error('删除失败');
    }
  }

  // 客户单位操作成功回调
  function handleCustomerSuccess() {
    reloadCustomerTable();
  }

  // 新增联系人
  function handleCreateLinkman() {
    if (!currentCustomer.value) {
      createMessage.warning('请先选择客户单位');
      return;
    }

    openLinkmanDrawer(true, {
      isUpdate: false,
      customerId: currentCustomer.value.id,
      customerName: currentCustomer.value.name,
    });
  }

  // 编辑联系人
  function handleEditLinkman(record: ProjCustomerLinkmanModel) {
    openLinkmanDrawer(true, {
      record,
      isUpdate: true,
      customerId: record.cuId,
      customerName: record.customerName || '',
    });
  }

  // 切换联系人状态
  async function handleToggleLinkmanStatus(record: ProjCustomerLinkmanModel) {
    try {
      const newStatus = record.status === 1 ? 0 : 1;
      await updateCustomerLinkmanStatus(record.id, newStatus);
      createMessage.success(newStatus === 1 ? '启用成功' : '禁用成功');
      reloadLinkmanTable();
    } catch (error) {
      console.error('更新联系人状态失败:', error);
      createMessage.error('操作失败');
    }
  }

  // 删除联系人
  async function handleDeleteLinkman(record: ProjCustomerLinkmanModel) {
    try {
      await deleteCustomerLinkman(record.id);
      createMessage.success('删除成功');
      reloadLinkmanTable();
    } catch (error) {
      console.error('删除联系人失败:', error);
      createMessage.error('删除失败');
    }
  }

  // 切换查看模式
  function toggleViewMode() {
    viewMode.value = viewMode.value === 'single' ? 'all' : 'single';
    reloadLinkmanTable();
  }

  // 处理联系人行点击，设置对应的客户单位
  function handleLinkmanRowClick(record: any) {
    if (viewMode.value === 'all' && record.cuId) {
      // 在全部联系人模式下，点击联系人时设置对应的客户单位
      const customerDataSource = getDataSource();
      const customer = customerDataSource.find((c: any) => c.id === record.cuId) as ProjCustomerModel;
      if (customer) {
        currentCustomer.value = customer;
        viewMode.value = 'single';
        createMessage.info(`已切换到客户单位：${customer.name}`);
      }
    }
  }

  // 联系人操作成功回调
  function handleLinkmanSuccess() {
    reloadLinkmanTable();
  }

  // 测试相关处理函数
  function handleTestModal() {
    console.log('点击测试模态框按钮');
    console.log('修改前 showTestModal:', showTestModal.value);
    showTestModal.value = true;
    console.log('修改后 showTestModal:', showTestModal.value);
    createMessage.info('尝试打开测试模态框');
  }

  function handleTestDrawer() {
    console.log('点击测试抽屉按钮');
    console.log('修改前 showTestDrawer:', showTestDrawer.value);
    showTestDrawer.value = true;
    console.log('修改后 showTestDrawer:', showTestDrawer.value);
    createMessage.info('尝试打开测试抽屉');
  }

  function handleDebugTest() {
    console.log('调试测试');
    console.log('showTestModal:', showTestModal.value);
    console.log('showTestDrawer:', showTestDrawer.value);
    createMessage.info('调试信息已输出到控制台');
  }

  // 监听当前选中的客户单位变化，刷新联系人列表
  watch(
    () => currentCustomer.value,
    newVal => {
      if (newVal && viewMode.value === 'single') {
        // 使用 nextTick 确保表格实例已经准备好
        nextTick(() => {
          try {
            reloadLinkmanTable();
          } catch (error) {
            console.warn('表格实例还未准备好，跳过刷新:', error);
          }
        });
      }
    },
    { immediate: false }, // 不立即执行
  );
</script>

<style lang="less" scoped>
  .customer-management {
    height: 100%;
    width: 100%;

    &-container {
      display: flex;
      height: 100%;
      width: 100%;

      .customer-list-container {
        width: 40%;
        padding-right: 16px;
        overflow: auto;
      }

      .linkman-list-container {
        width: 60%;
        padding-left: 16px;
        overflow: auto;
      }
    }
  }

  .customer-card,
  .linkman-card {
    height: 100%;
    box-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.08), 0 3px 6px 0 rgba(0, 0, 0, 0.06), 0 5px 12px 4px rgba(0, 0, 0, 0.04);
    border-radius: 4px;

    :deep(.ant-card-head) {
      padding: 0 16px;
      min-height: 48px;
      border-bottom: 1px solid #f0f0f0;

      .ant-card-head-title {
        padding: 8px 0;
      }
    }

    :deep(.ant-card-body) {
      padding: 0;
    }
  }

  .card-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    .customer-name {
      font-weight: 500;
      font-size: 16px;
      color: #1f1f1f;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 500px;
    }

    .title-actions {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .empty-tip {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 300px;

    .empty-help-text {
      color: #999;
      margin-top: 8px;
    }
  }

  :deep(.ant-table-wrapper) {
    .ant-table-thead > tr > th {
      background-color: #f5f7fa;
      font-weight: 500;
    }

    .ant-table-tbody > tr.ant-table-row:hover > td {
      background-color: #e6f7ff;
    }

    .ant-table-row-selected > td {
      background-color: #e6f7ff;
    }
  }
</style>
