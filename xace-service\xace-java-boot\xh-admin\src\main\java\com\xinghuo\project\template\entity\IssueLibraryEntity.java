package com.xinghuo.project.template.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 标准项目问题库实体类
 * 对应数据库表：zz_proj_issue_library
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("zz_proj_issue_library")
public class IssueLibraryEntity extends BaseEntityV2.CUBaseEntityV2<String> {

    /**
     * 标准问题编码
     */
    @TableField("code")
    private String code;

    /**
     * 标准问题标题/名称
     */
    @TableField("title")
    private String title;

    /**
     * 问题的详细描述模板 (现象, 可能原因, 潜在影响)
     */
    @TableField("description")
    private String description;

    /**
     * 发布状态 (如: draft 草稿, published 已发布, archived 归档)
     */
    @TableField("status")
    private String status;

    /**
     * 问题类别ID (关联字典表, 如: 过程不符合, 客户反馈, 事件)
     */
    @TableField("issue_category_id")
    private String issueCategoryId;

    /**
     * 默认优先级/严重性ID (关联字典表, 如: 严重, 一般, 较低)
     */
    @TableField("default_priority_id")
    private String defaultPriorityId;

    /**
     * 建议的通用解决方案或处理思路
     */
    @TableField("suggested_solution")
    private String suggestedSolution;
}
