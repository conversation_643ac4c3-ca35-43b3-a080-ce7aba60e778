<template>
  <div class="template-phase-page p-4">
    <!-- 页面标题 -->
    <div class="page-header mb-4">
      <h2 class="text-xl font-semibold mb-2">阶段管理</h2>
      <p class="text-gray-600">配置项目模板的阶段信息，设置各阶段的基本属性和依赖关系</p>
    </div>

    <a-spin :spinning="loading">
      <!-- 工具栏 -->
      <div class="toolbar mb-4 flex justify-between items-center">
        <div class="search-area flex items-center space-x-4">
          <a-input-search v-model:value="searchText" placeholder="搜索阶段名称" style="width: 300px" @search="handleSearch" />
          <a-select v-model:value="statusFilter" placeholder="筛选状态" style="width: 150px" allow-clear @change="handleStatusFilter">
            <a-select-option value="active">启用</a-select-option>
            <a-select-option value="inactive">禁用</a-select-option>
          </a-select>
        </div>
        <div class="actions">
          <a-space>
            <a-button type="primary" @click="handleAdd">
              <template #icon><PlusOutlined /></template>
              添加阶段
            </a-button>
            <a-button @click="handleImportFromLibrary">
              <template #icon><ImportOutlined /></template>
              从阶段库导入
            </a-button>
            <a-button @click="handleExport">
              <template #icon><ExportOutlined /></template>
              导出
            </a-button>
            <a-button @click="handleRefresh">
              <template #icon><ReloadOutlined /></template>
              刷新
            </a-button>
          </a-space>
        </div>
      </div>

      <!-- 阶段表格 -->
      <BasicTable @register="registerTable">
        <!-- 自定义列 -->
        <template #stageName="{ record }">
          <div class="flex items-center">
            <div class="stage-icon mr-2">
              <a-avatar :style="{ backgroundColor: getStageColor(record.stageType) }" :size="24">
                <template #icon>
                  <component :is="getStageIcon(record.stageType)" />
                </template>
              </a-avatar>
            </div>
            <div>
              <div class="font-medium">{{ record.stageName }}</div>
              <div class="text-sm text-gray-500">{{ record.stageCode }}</div>
            </div>
          </div>
        </template>

        <template #stageType="{ record }">
          <a-tag :color="getStageTypeColor(record.stageType)">
            {{ getStageTypeText(record.stageType) }}
          </a-tag>
        </template>

        <template #status="{ record }">
          <a-tag :color="record.status === 'active' ? 'green' : 'red'">
            {{ record.status === 'active' ? '启用' : '禁用' }}
          </a-tag>
        </template>

        <template #action="{ record }">
          <TableAction
            :actions="[
              {
                icon: 'ant-design:edit-outlined',
                label: '编辑',
                onClick: handleEdit.bind(null, record),
              },
              {
                icon: 'ant-design:copy-outlined',
                label: '复制',
                onClick: handleCopy.bind(null, record),
              },
              {
                icon: 'ant-design:delete-outlined',
                label: '删除',
                color: 'error',
                popConfirm: {
                  title: '是否确认删除？',
                  placement: 'left',
                  confirm: handleDelete.bind(null, record),
                },
              },
            ]" />
        </template>
      </BasicTable>
    </a-spin>

    <!-- 添加/编辑弹窗 -->
    <BasicDrawer @register="registerDrawer" :title="drawerTitle" width="50%" @ok="handleSubmit">
      <BasicForm @register="registerForm" />
    </BasicDrawer>

    <!-- 从阶段库导入弹窗 -->
    <BasicModal @register="registerImportModal" title="从阶段库导入" width="80%" @ok="handleImportConfirm">
      <div class="import-content">
        <div class="mb-4">
          <a-input-search v-model:value="importSearchText" placeholder="搜索阶段库中的阶段" @search="loadLibraryPhases" />
        </div>
        <BasicTable @register="registerImportTable" :can-resize="false">
          <template #action="{ record }">
            <a-button type="link" size="small" @click="handleSelectPhase(record)" :disabled="isPhaseSelected(record.id)">
              {{ isPhaseSelected(record.id) ? '已选择' : '选择' }}
            </a-button>
          </template>
        </BasicTable>

        <!-- 已选择的阶段 -->
        <div v-if="selectedPhases.length > 0" class="mt-4">
          <h4 class="mb-2">已选择的阶段 ({{ selectedPhases.length }})</h4>
          <div class="selected-phases flex flex-wrap gap-2">
            <a-tag v-for="phase in selectedPhases" :key="phase.id" closable @close="handleRemovePhase(phase.id)">
              {{ phase.stageName }}
            </a-tag>
          </div>
        </div>
      </div>
    </BasicModal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed, inject, onMounted } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { BasicForm, useForm } from '/@/components/Form';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import {
    PlusOutlined,
    ImportOutlined,
    ExportOutlined,
    ReloadOutlined,
    ProjectOutlined,
    PlayCircleOutlined,
    CheckCircleOutlined,
  } from '@ant-design/icons-vue';

  defineOptions({ name: 'ProjectTemplatePhaseConfigPage' });

  // 注入模板ID
  const templateId = inject<any>('templateId');
  const { createMessage } = useMessage();

  const loading = ref(false);
  const searchText = ref('');
  const statusFilter = ref('');
  const drawerTitle = ref('');
  const currentRecord = ref<any>(null);
  const importSearchText = ref('');
  const selectedPhases = ref<any[]>([]);

  // 表格列配置
  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      width: 60,
      customRender: ({ index }) => index + 1,
    },
    {
      title: '阶段名称',
      dataIndex: 'stageName',
      width: 200,
      slots: { customRender: 'stageName' },
    },
    {
      title: '阶段类型',
      dataIndex: 'stageType',
      width: 120,
      slots: { customRender: 'stageType' },
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 80,
      slots: { customRender: 'status' },
    },
    {
      title: '排序',
      dataIndex: 'sort',
      width: 80,
    },
    {
      title: '工期(天)',
      dataIndex: 'duration',
      width: 100,
    },
    {
      title: '前置阶段',
      dataIndex: 'predecessorNames',
      width: 150,
    },
    {
      title: '描述',
      dataIndex: 'description',
      width: 200,
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 150,
      customRender: ({ text }) => (text ? new Date(text).toLocaleDateString() : '-'),
    },
  ];

  // 表单配置
  const formSchemas = [
    {
      field: 'stageName',
      label: '阶段名称',
      component: 'Input',
      required: true,
      colProps: { span: 12 },
    },
    {
      field: 'stageCode',
      label: '阶段编码',
      component: 'Input',
      required: true,
      colProps: { span: 12 },
    },
    {
      field: 'stageType',
      label: '阶段类型',
      component: 'Select',
      required: true,
      componentProps: {
        options: [
          { label: '启动阶段', value: 'start' },
          { label: '执行阶段', value: 'process' },
          { label: '结束阶段', value: 'end' },
        ],
      },
      colProps: { span: 12 },
    },
    {
      field: 'status',
      label: '状态',
      component: 'RadioGroup',
      defaultValue: 'active',
      componentProps: {
        options: [
          { label: '启用', value: 'active' },
          { label: '禁用', value: 'inactive' },
        ],
      },
      colProps: { span: 12 },
    },
    {
      field: 'sort',
      label: '排序',
      component: 'InputNumber',
      required: true,
      componentProps: {
        min: 1,
      },
      colProps: { span: 12 },
    },
    {
      field: 'duration',
      label: '工期(天)',
      component: 'InputNumber',
      componentProps: {
        min: 1,
      },
      colProps: { span: 12 },
    },
    {
      field: 'predecessorIds',
      label: '前置阶段',
      component: 'Select',
      componentProps: {
        mode: 'multiple',
        options: [], // 动态加载
      },
      colProps: { span: 24 },
    },
    {
      field: 'description',
      label: '描述',
      component: 'InputTextArea',
      componentProps: {
        rows: 3,
      },
      colProps: { span: 24 },
    },
  ];

  // 导入表格列配置
  const importColumns = [
    {
      title: '阶段名称',
      dataIndex: 'stageName',
      width: 200,
    },
    {
      title: '阶段类型',
      dataIndex: 'stageType',
      width: 120,
    },
    {
      title: '工期(天)',
      dataIndex: 'duration',
      width: 100,
    },
    {
      title: '描述',
      dataIndex: 'description',
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      slots: { customRender: 'action' },
    },
  ];

  // 表格实例
  const [registerTable, { reload, getDataSource }] = useTable({
    api: loadPhaseData,
    columns,
    useSearchForm: false,
    actionColumn: {
      width: 200,
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
    },
  });

  // 表单实例
  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 100,
    schemas: formSchemas,
    showActionButtonGroup: false,
  });

  // 抽屉实例
  const [registerDrawer, { openDrawer, closeDrawer }] = useDrawerInner();

  // 导入弹窗实例
  const [registerImportModal, { openModal: openImportModal, closeModal: closeImportModal }] = useModalInner();

  // 导入表格实例
  const [registerImportTable] = useTable({
    api: loadLibraryPhases,
    columns: importColumns,
    useSearchForm: false,
    pagination: {
      pageSize: 10,
    },
  });

  // 加载阶段数据
  async function loadPhaseData() {
    if (!templateId?.value) return { list: [], total: 0 };

    loading.value = true;
    try {
      // 这里调用实际的API
      // const response = await getTemplatePhases(templateId.value);
      // if (response.code === 200) {
      //   return response.data;
      // }

      // 模拟数据
      const mockData = [
        {
          id: '1',
          stageName: '项目启动',
          stageCode: 'STAGE_START',
          stageType: 'start',
          status: 'active',
          sort: 1,
          duration: 5,
          predecessorIds: [],
          predecessorNames: '',
          description: '项目启动阶段，包括项目启动会、团队组建等工作',
          createdAt: '2025-01-15T10:00:00Z',
        },
        {
          id: '2',
          stageName: '需求分析',
          stageCode: 'STAGE_ANALYSIS',
          stageType: 'process',
          status: 'active',
          sort: 2,
          duration: 10,
          predecessorIds: ['1'],
          predecessorNames: '项目启动',
          description: '需求调研、分析和确认',
          createdAt: '2025-01-15T10:00:00Z',
        },
        {
          id: '3',
          stageName: '系统设计',
          stageCode: 'STAGE_DESIGN',
          stageType: 'process',
          status: 'active',
          sort: 3,
          duration: 8,
          predecessorIds: ['2'],
          predecessorNames: '需求分析',
          description: '系统架构设计和详细设计',
          createdAt: '2025-01-15T10:00:00Z',
        },
      ];

      return { list: mockData, total: mockData.length };
    } catch (error) {
      console.error('加载阶段数据失败:', error);
      createMessage.error('加载阶段数据失败');
      return { list: [], total: 0 };
    } finally {
      loading.value = false;
    }
  }

  // 加载阶段库数据
  async function loadLibraryPhases() {
    // 模拟阶段库数据
    const mockLibraryData = [
      {
        id: 'lib_1',
        stageName: '立项阶段',
        stageType: 'start',
        duration: 3,
        description: '项目立项和审批',
      },
      {
        id: 'lib_2',
        stageName: '计划阶段',
        stageType: 'process',
        duration: 5,
        description: '制定项目计划',
      },
      {
        id: 'lib_3',
        stageName: '执行阶段',
        stageType: 'process',
        duration: 20,
        description: '项目具体执行',
      },
      {
        id: 'lib_4',
        stageName: '验收阶段',
        stageType: 'end',
        duration: 5,
        description: '项目验收和交付',
      },
    ];

    return { list: mockLibraryData, total: mockLibraryData.length };
  }

  // 工具函数
  const getStageColor = (stageType: string) => {
    const colorMap = {
      start: '#52c41a',
      process: '#1890ff',
      end: '#722ed1',
    };
    return colorMap[stageType] || '#1890ff';
  };

  const getStageIcon = (stageType: string) => {
    const iconMap = {
      start: PlayCircleOutlined,
      process: ProjectOutlined,
      end: CheckCircleOutlined,
    };
    return iconMap[stageType] || ProjectOutlined;
  };

  const getStageTypeColor = (stageType: string) => {
    const colorMap = {
      start: 'green',
      process: 'blue',
      end: 'purple',
    };
    return colorMap[stageType] || 'blue';
  };

  const getStageTypeText = (stageType: string) => {
    const textMap = {
      start: '启动阶段',
      process: '执行阶段',
      end: '结束阶段',
    };
    return textMap[stageType] || '未知类型';
  };

  const isPhaseSelected = (phaseId: string) => {
    return selectedPhases.value.some(phase => phase.id === phaseId);
  };

  // 事件处理
  const handleSearch = () => {
    reload();
  };

  const handleStatusFilter = () => {
    reload();
  };

  const handleAdd = () => {
    currentRecord.value = null;
    drawerTitle.value = '添加阶段';
    resetFields();
    openDrawer();
  };

  const handleEdit = (record: any) => {
    currentRecord.value = record;
    drawerTitle.value = '编辑阶段';
    setFieldsValue(record);
    openDrawer();
  };

  const handleCopy = (record: any) => {
    currentRecord.value = null;
    drawerTitle.value = '复制阶段';
    const copyData = { ...record };
    delete copyData.id;
    copyData.stageName = `${record.stageName} - 副本`;
    copyData.stageCode = `${record.stageCode}_COPY`;
    setFieldsValue(copyData);
    openDrawer();
  };

  const handleDelete = (record: any) => {
    createMessage.success('删除成功');
    reload();
  };

  const handleSubmit = async () => {
    try {
      const values = await validate();
      console.log('提交阶段数据:', values);

      // 这里调用API保存数据
      // if (currentRecord.value) {
      //   await updateTemplatePhase(templateId.value, currentRecord.value.id, values);
      // } else {
      //   await createTemplatePhase(templateId.value, values);
      // }

      createMessage.success('保存成功');
      closeDrawer();
      reload();
    } catch (error) {
      console.error('保存失败:', error);
      createMessage.error('保存失败');
    }
  };

  const handleExport = () => {
    createMessage.info('导出功能开发中...');
  };

  const handleRefresh = () => {
    reload();
  };

  const handleImportFromLibrary = () => {
    selectedPhases.value = [];
    openImportModal();
  };

  const handleSelectPhase = (record: any) => {
    if (!isPhaseSelected(record.id)) {
      selectedPhases.value.push(record);
    }
  };

  const handleRemovePhase = (phaseId: string) => {
    selectedPhases.value = selectedPhases.value.filter(phase => phase.id !== phaseId);
  };

  const handleImportConfirm = () => {
    if (selectedPhases.value.length === 0) {
      createMessage.warning('请选择要导入的阶段');
      return;
    }

    console.log('导入阶段:', selectedPhases.value);
    // 这里调用API导入阶段
    // await importPhasesFromLibrary(templateId.value, selectedPhases.value);

    createMessage.success(`成功导入 ${selectedPhases.value.length} 个阶段`);
    closeImportModal();
    reload();
  };

  onMounted(() => {
    console.log('阶段管理页面挂载，模板ID:', templateId?.value);
  });
</script>

<style lang="less" scoped>
  .template-phase-page {
    .stage-icon {
      display: inline-flex;
      align-items: center;
      justify-content: center;
    }

    .import-content {
      .selected-phases {
        max-height: 200px;
        overflow-y: auto;
        padding: 12px;
        background: #f5f5f5;
        border-radius: 6px;
      }
    }
  }
</style>
