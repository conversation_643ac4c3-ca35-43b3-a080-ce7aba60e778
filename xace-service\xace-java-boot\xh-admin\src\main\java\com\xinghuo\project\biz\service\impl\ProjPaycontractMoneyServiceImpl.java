package com.xinghuo.project.biz.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.biz.dao.PaymentContractMoneyMapper;
import com.xinghuo.project.biz.entity.BizContractEntity;
import com.xinghuo.project.biz.entity.PaymentContractEntity;
import com.xinghuo.project.biz.entity.PaymentContractMoneyEntity;
import com.xinghuo.project.biz.model.paymentContractMoney.PaymentContractMoneyPagination;
import com.xinghuo.project.biz.model.paymentContractMoney.PaymentContractMoneyStatusForm;
import com.xinghuo.project.biz.model.paymentContractMoney.PaymentContractMoneyVO;
import com.xinghuo.project.biz.model.paymentContractMoney.PaymentContractMoneyStats;
import com.xinghuo.project.biz.service.BizContractService;
import com.xinghuo.project.biz.service.PaymentContractMoneyService;
import com.xinghuo.project.biz.service.PaymentContractService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 采购付款计划服务实现类
 */
@Slf4j
@Service
public class ProjPaycontractMoneyServiceImpl extends BaseServiceImpl<PaymentContractMoneyMapper, PaymentContractMoneyEntity> implements PaymentContractMoneyService {

    @Autowired
    private PaymentContractService paycontractService;

    @Autowired
    private BizContractService contractService;

    @Override
    public List<PaymentContractMoneyEntity> getList(PaymentContractMoneyPagination pagination) {
        QueryWrapper<PaymentContractMoneyEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<PaymentContractMoneyEntity> lambda = queryWrapper.lambda();

        // 根据采购合同ID精确查询
        if (StrXhUtil.isNotEmpty(pagination.getPcId())) {
            lambda.eq(PaymentContractMoneyEntity::getPayContractId, pagination.getPcId());
        }

        // 根据付款状态精确查询
        if (StrXhUtil.isNotEmpty(pagination.getPayStatus())) {
            lambda.eq(PaymentContractMoneyEntity::getPayStatus, pagination.getPayStatus());
        }

        if (pagination.getDateType() != null && pagination.getStartTime() != null) {
            queryWrapper.ge(pagination.getDateType(), pagination.getStartTime());
        }
        if (pagination.getDateType() != null && pagination.getEndTime() != null) {
            queryWrapper.le(pagination.getDateType(), pagination.getEndTime());
        }

        // 根据负责人ID精确查询
        if (StrXhUtil.isNotEmpty(pagination.getOwnId())) {
            lambda.eq(PaymentContractMoneyEntity::getOwnId, pagination.getOwnId());
        }

        // 排序
        lambda.orderByAsc(PaymentContractMoneyEntity::getYufuDate);

        // 分页
        return processDataType(queryWrapper,pagination);
    }

    @Override
    public List<PaymentContractMoneyEntity> getListByPaycontractId(String paycontractId) {
        LambdaQueryWrapper<PaymentContractMoneyEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PaymentContractMoneyEntity::getPayContractId, paycontractId);
        queryWrapper.orderByAsc(PaymentContractMoneyEntity::getYufuDate);
        return this.list(queryWrapper);
    }

    @Override
    public PaymentContractMoneyEntity getInfo(String id) {
        return this.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(PaymentContractMoneyEntity entity) {
        entity.setId(RandomUtil.snowId());

        // 设置初始状态
        if (entity.getPayStatus() == null) {
            entity.setPayStatus(0); // 未付款
        }

        this.save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(String id, PaymentContractMoneyEntity entity) {
        PaymentContractMoneyEntity oldEntity = this.getById(id);
        if (oldEntity == null) {
            throw new RuntimeException("采购付款计划不存在");
        }

        // 如果已付款，不允许修改
        if (Integer.valueOf(1).equals(oldEntity.getPayStatus())) {
            throw new RuntimeException("已付款的付款计划不允许修改");
        }

        String oldPaycontractId = oldEntity.getPayContractId();
        entity.setId(id);

        this.updateById(entity);

        // 如果采购合同ID发生变化，需要更新两个采购合同的已付金额
        if (!oldPaycontractId.equals(entity.getPayContractId())) {
            updatePaycontractYfAmount(oldPaycontractId);
        }
        updatePaycontractYfAmount(entity.getPayContractId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        PaymentContractMoneyEntity entity = this.getById(id);
        if (entity == null) {
            throw new RuntimeException("采购付款计划不存在");
        }

        // 如果已付款，不允许删除
        if (Integer.valueOf(1).equals(entity.getPayStatus())) {
            throw new RuntimeException("已付款的付款计划不允许删除");
        }

        String paycontractId = entity.getPayContractId();
        this.removeById(id);

        // 更新采购合同的已付金额
        updatePaycontractYfAmount(paycontractId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(String id, PaymentContractMoneyStatusForm form) {
        PaymentContractMoneyEntity entity = this.getById(id);
        if (entity == null) {
            throw new RuntimeException("采购付款计划不存在");
        }

        // 更新状态 - 将字符串状态转换为数字状态
        Integer payStatus = "1".equals(form.getPayStatus()) ? 1 : 0;
        entity.setPayStatus(payStatus);
        entity.setFukuanDate(form.getFukuanDate());
        entity.setLastNote(form.getLastNote());

        this.updateById(entity);

        // 如果状态为已付款，更新采购合同的已付金额
        if ("1".equals(form.getPayStatus()) && form.getFukuanDate() != null) {
            updatePaycontractYfAmount(entity.getPayContractId());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void registerPayment(String id, Date fukuanDate, String lastNote) {
        PaymentContractMoneyEntity entity = this.getById(id);
        if (entity == null) {
            throw new RuntimeException("采购付款计划不存在");
        }

        // 更新付款日期和状态
        entity.setFukuanDate(fukuanDate);
        entity.setPayStatus(1); // 已付款
        entity.setLastNote(lastNote);

        this.updateById(entity);

        // 更新采购合同的已付金额
        updatePaycontractYfAmount(entity.getPayContractId());
    }

    /**
     * 更新采购合同的已付金额
     *
     * @param paycontractId 采购合同ID
     */
    private void updatePaycontractYfAmount(String paycontractId) {
        PaymentContractEntity paycontract = paycontractService.getById(paycontractId);
        if (paycontract == null) {
            return;
        }

        // 获取该采购合同下的所有已付款的付款计划
        LambdaQueryWrapper<PaymentContractMoneyEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PaymentContractMoneyEntity::getPayContractId, paycontractId);
        queryWrapper.eq(PaymentContractMoneyEntity::getPayStatus, 1);
        List<PaymentContractMoneyEntity> paidList = this.list(queryWrapper);

        // 计算已付金额
        BigDecimal yfAmount = BigDecimal.ZERO;
        BigDecimal outYfYbAmount = BigDecimal.ZERO;
        BigDecimal outYfEbAmount = BigDecimal.ZERO;
        BigDecimal outYfOtherAmount = BigDecimal.ZERO;

        for (PaymentContractMoneyEntity money : paidList) {
            if (money.getCmMoney() != null) {
                yfAmount = yfAmount.add(money.getCmMoney());
            }

            // 计算各部门已付金额
            if (money.getYbAmount() != null) {
                outYfYbAmount = outYfYbAmount.add(money.getYbAmount());
            }
            if (money.getEbAmount() != null) {
                outYfEbAmount = outYfEbAmount.add(money.getEbAmount());
            }
            if (money.getOtherAmount() != null) {
                outYfOtherAmount = outYfOtherAmount.add(money.getOtherAmount());
            }
        }

        // 更新采购合同的已付金额
        paycontract.setYfAmount(yfAmount);

        // 更新采购合同的付款状态
        if (yfAmount.compareTo(paycontract.getAmount()) >= 0) {
            paycontract.setMoneyStatus("paid"); // 已付讫
        } else if (yfAmount.compareTo(BigDecimal.ZERO) > 0) {
            paycontract.setMoneyStatus("partial"); // 部分付款
        } else {
            paycontract.setMoneyStatus("unpaid"); // 未付款
        }

        paycontractService.updateById(paycontract);

        // 更新收款合同的实际外采成本
        updateContractActExternalAmount(paycontract.getContractId(), outYfYbAmount, outYfEbAmount, outYfOtherAmount);
    }

    /**
     * 更新收款合同的实际外采成本
     *
     * @param contractId      收款合同ID
     * @param outYfYbAmount   一部已付金额
     * @param outYfEbAmount   二部已付金额
     * @param outYfOtherAmount 综合已付金额
     */
    private void updateContractActExternalAmount(String contractId, BigDecimal outYfYbAmount, BigDecimal outYfEbAmount, BigDecimal outYfOtherAmount) {
        BizContractEntity contract = contractService.getById(contractId);
        if (contract == null) {
            return;
        }

        // 获取该收款合同下的所有采购合同
        List<PaymentContractEntity> paycontracts = paycontractService.getListByContractId(contractId);

        // 计算实际外采金额
        BigDecimal actExternalAmount = BigDecimal.ZERO;

        for (PaymentContractEntity paycontract : paycontracts) {
            if (paycontract.getYfAmount() != null) {
                actExternalAmount = actExternalAmount.add(paycontract.getYfAmount());
            }
        }

        // 更新收款合同的实际外采成本
        contract.setActExternalAmount(actExternalAmount);
        contract.setOutYfYbAmount(outYfYbAmount);
        contract.setOutYfEbAmount(outYfEbAmount);
        contract.setOutYfOtherAmount(outYfOtherAmount);

        contractService.updateById(contract);
    }

    @Override
    public void fillRelatedInfo(List<PaymentContractMoneyVO> listVOs) {
        for (PaymentContractMoneyVO vo : listVOs) {
            // 填充采购合同信息
            if (StrXhUtil.isNotEmpty(vo.getPcId())) {
                try {
                    PaymentContractEntity paycontract = paycontractService.getInfo(vo.getPcId());
                    if (paycontract != null) {
                        vo.setPaycontractName(paycontract.getName());
                        vo.setPaycontractNo(paycontract.getCno());
                    }
                } catch (Exception e) {
                    log.warn("获取采购合同信息失败，合同ID: {}", vo.getPcId());
                }
            }
        }
    }

    @Override
    public PaymentContractMoneyStats getStats(String paycontractId) {
        PaymentContractMoneyStats stats = new PaymentContractMoneyStats();

        LambdaQueryWrapper<PaymentContractMoneyEntity> queryWrapper = new LambdaQueryWrapper<>();

        // 如果指定了采购合同ID，则只统计该合同的数据
        if (StrXhUtil.isNotEmpty(paycontractId)) {
            queryWrapper.eq(PaymentContractMoneyEntity::getPayContractId, paycontractId);
        }

        List<PaymentContractMoneyEntity> list = this.list(queryWrapper);

        if (list.isEmpty()) {
            return stats;
        }

        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal paidAmount = BigDecimal.ZERO;
        int completedCount = 0;
        int pendingCount = 0;

        for (PaymentContractMoneyEntity entity : list) {
            // 累计总金额
            if (entity.getCmMoney() != null) {
                totalAmount = totalAmount.add(entity.getCmMoney());
            }

            // 根据付款状态统计
            if (Integer.valueOf(1).equals(entity.getPayStatus())) {
                // 已付款
                if (entity.getCmMoney() != null) {
                    paidAmount = paidAmount.add(entity.getCmMoney());
                }
                completedCount++;
            } else {
                // 待付款
                pendingCount++;
            }
        }

        // 计算待付金额
        BigDecimal unpaidAmount = totalAmount.subtract(paidAmount);

        // 计算执行进度（百分比）
        BigDecimal progressRate = BigDecimal.ZERO;
        if (totalAmount.compareTo(BigDecimal.ZERO) > 0) {
            progressRate = paidAmount.divide(totalAmount, 4, BigDecimal.ROUND_HALF_UP)
                    .multiply(new BigDecimal("100"));
        }

        stats.setTotalAmount(totalAmount);
        stats.setPaidAmount(paidAmount);
        stats.setUnpaidAmount(unpaidAmount);
        stats.setCompletedCount(completedCount);
        stats.setPendingCount(pendingCount);
        stats.setProgressRate(progressRate);

        return stats;
    }
}

