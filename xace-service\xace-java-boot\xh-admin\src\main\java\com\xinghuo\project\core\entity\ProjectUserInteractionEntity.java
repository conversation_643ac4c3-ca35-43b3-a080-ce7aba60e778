package com.xinghuo.project.core.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 用户项目交互实体
 * 对应表：zz_proj_user_interaction
 * 记录用户与项目的交互行为（访问、关注、收藏等）
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("zz_proj_user_interaction")
public class ProjectUserInteractionEntity extends BaseEntityV2.CUBaseEntityV2<String> {

    /**
     * 项目ID
     */
    @TableField("project_id")
    private String projectId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 交互类型
     * visit-访问, follow-关注, favorite-收藏, like-点赞, comment-评论
     */
    @TableField("interaction_type")
    private String interactionType;

    /**
     * 交互状态
     * 1-有效, 0-无效（如取消关注、取消收藏）
     */
    @TableField("interaction_status")
    private Integer interactionStatus;

    /**
     * 最后交互时间
     */
    @TableField("last_interaction_time")
    private Date lastInteractionTime;

    /**
     * 交互次数
     */
    @TableField("interaction_count")
    private Integer interactionCount;

    /**
     * 交互内容（如评论内容、备注等）
     */
    @TableField("interaction_content")
    private String interactionContent;

    /**
     * 交互来源
     * web-网页, mobile-移动端, api-接口
     */
    @TableField("interaction_source")
    private String interactionSource;

    /**
     * IP地址
     */
    @TableField("ip_address")
    private String ipAddress;

    /**
     * 用户代理
     */
    @TableField("user_agent")
    private String userAgent;

    /**
     * 扩展信息（JSON格式）
     */
    @TableField("extend_info")
    private String extendInfo;

    /**
     * 备注
     */
    @TableField("remarks")
    private String remarks;
}
