package com.xinghuo.project.execution.model;

import com.xinghuo.common.base.vo.PaginationVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 项目分页查询参数
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectPagination extends PaginationVO {

    /**
     * 项目名称
     */
    private String name;

    /**
     * 项目编码
     */
    private String code;

    /**
     * 项目状态
     */
    private Integer status;

    /**
     * 工作流状态
     */
    private Integer wfStatus;

    /**
     * 备选状态
     */
    private Integer alternativeStatus;

    /**
     * 项目经理用户ID
     */
    private String managerUserId;

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 项目组合ID
     */
    private String portfolioId;

    /**
     * 项目模板ID
     */
    private String schemaId;

    /**
     * 项目类型
     */
    private Integer projectType;

    /**
     * 项目优先级
     */
    private Integer priority;

    /**
     * 项目风险等级
     */
    private Integer riskLevel;

    /**
     * 项目健康度
     */
    private Integer healthStatus;

    /**
     * 关键字搜索（名称、编码、描述）
     */
    private String keyword;

    /**
     * 创建时间开始
     */
    private Date createTimeStart;

    /**
     * 创建时间结束
     */
    private Date createTimeEnd;

    /**
     * 计划开始时间开始
     */
    private Date planStartTimeStart;

    /**
     * 计划开始时间结束
     */
    private Date planStartTimeEnd;

    /**
     * 计划结束时间开始
     */
    private Date planEndTimeStart;

    /**
     * 计划结束时间结束
     */
    private Date planEndTimeEnd;

    /**
     * 预期结束时间开始
     */
    private Date expectEndTimeStart;

    /**
     * 预期结束时间结束
     */
    private Date expectEndTimeEnd;

    /**
     * 创建用户ID
     */
    private String creatorUserId;

    /**
     * 父项目ID
     */
    private String parentProjectId;

    /**
     * 项目标签列表
     */
    private List<String> tagIds;

    /**
     * 是否包含子项目
     */
    private Boolean includeSubProjects;

    /**
     * 是否我参与的项目
     */
    private Boolean myParticipated;

    /**
     * 是否我管理的项目
     */
    private Boolean myManaged;

    /**
     * 是否我关注的项目
     */
    private Boolean myFollowed;

    /**
     * 是否我收藏的项目
     */
    private Boolean myFavorited;

    /**
     * 是否最近访问的项目
     */
    private Boolean recentlyVisited;

    /**
     * 最近访问天数
     */
    private Integer recentDays;

    /**
     * 预算范围最小值
     */
    private Double budgetMin;

    /**
     * 预算范围最大值
     */
    private Double budgetMax;

    /**
     * 进度范围最小值
     */
    private Double progressMin;

    /**
     * 进度范围最大值
     */
    private Double progressMax;

    /**
     * 项目团队成员用户ID列表
     */
    private List<String> teamMemberIds;

    /**
     * 项目角色ID列表
     */
    private List<String> roleIds;

    /**
     * 是否包含项目统计信息
     */
    private Boolean includeStatistics;

    /**
     * 是否包含团队信息
     */
    private Boolean includeTeamInfo;

    /**
     * 是否包含权限信息
     */
    private Boolean includePermissions;

    /**
     * 自定义字段搜索条件
     */
    private java.util.Map<String, Object> customFieldConditions;

    /**
     * 数据权限过滤
     */
    private Boolean dataPermissionFilter;

    /**
     * 搜索范围
     * all-全部, department-部门, portfolio-组合, personal-个人
     */
    private String searchScope;
}
