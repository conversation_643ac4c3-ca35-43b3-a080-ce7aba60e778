package com.xinghuo.manhour.model.analysis;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 项目分析数据VO
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@Schema(description = "项目分析数据")
public class ProjectAnalysisVO {

    @Schema(description = "项目ID")
    private String projectId;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "项目类型")
    private String projType;

    @Schema(description = "项目类型名称")
    private String projTypeName;

    @Schema(description = "总工时")
    private BigDecimal totalWorkMonth;

    @Schema(description = "参与人数")
    private Integer userCount;

    @Schema(description = "平均工时")
    private BigDecimal avgWorkMonth;

    @Schema(description = "项目健康度")
    private BigDecimal healthScore;

    @Schema(description = "主要工时类型")
    private String mainWorkType;

    @Schema(description = "项目负责人")
    private String projectLeader;

    @Schema(description = "工时集中度")
    private BigDecimal workConcentration;

    @Schema(description = "团队稳定性")
    private BigDecimal teamStability;

    @Schema(description = "进度完成率")
    private BigDecimal progressRate;

    @Schema(description = "资源利用率")
    private BigDecimal resourceUtilization;
}
