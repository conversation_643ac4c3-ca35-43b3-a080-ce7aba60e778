package com.xinghuo.project.schema.model.vo;

import lombok.Data;

/**
 * 项目模板选择VO类
 * 用于下拉选择等场景
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
public class ProjectTemplateSelectVO {

    /**
     * 模板ID
     */
    private String id;

    /**
     * 模板编码
     */
    private String code;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 模板描述
     */
    private String description;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 模板类型ID
     */
    private String typeId;

    /**
     * 模板类型名称
     */
    private String typeName;

    /**
     * 模板图标
     */
    private String icon;

    /**
     * 完整名称（用于显示：[编码] 名称）
     */
    private String fullName;
}
