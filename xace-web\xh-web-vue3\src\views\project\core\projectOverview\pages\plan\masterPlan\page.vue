<template>
  <div class="master-plan-page p-4">
    <!-- 页面标题 -->
    <div class="page-header mb-4">
      <h2 class="text-xl font-semibold mb-2">主计划管理</h2>
      <p class="text-gray-600">查看和管理项目的整体计划，包含WBS结构、任务分解和进度安排</p>
    </div>

    <a-spin :spinning="loading">
      <!-- 工具栏 -->
      <div class="toolbar mb-4 flex justify-between items-center">
        <div class="view-controls flex items-center space-x-4">
          <a-radio-group v-model:value="viewMode" button-style="solid">
            <a-radio-button value="tree">树形视图</a-radio-button>
            <a-radio-button value="gantt">甘特图</a-radio-button>
            <a-radio-button value="list">列表视图</a-radio-button>
          </a-radio-group>
          <a-select v-model:value="statusFilter" placeholder="筛选状态" style="width: 150px" allow-clear @change="handleStatusFilter">
            <a-select-option value="completed">已完成</a-select-option>
            <a-select-option value="in_progress">进行中</a-select-option>
            <a-select-option value="pending">未开始</a-select-option>
            <a-select-option value="delayed">延期</a-select-option>
          </a-select>
          <a-checkbox v-model:checked="showCriticalPath"> 显示关键路径 </a-checkbox>
        </div>
        <div class="actions">
          <a-space>
            <a-button type="primary" @click="handleAddTask">
              <template #icon><PlusOutlined /></template>
              添加任务
            </a-button>
            <a-button @click="handleImportTemplate">
              <template #icon><ImportOutlined /></template>
              导入模板
            </a-button>
            <a-button @click="handleExport">
              <template #icon><ExportOutlined /></template>
              导出计划
            </a-button>
            <a-button @click="handleAutoSchedule">
              <template #icon><ClockCircleOutlined /></template>
              自动排期
            </a-button>
            <a-button @click="handleRefresh">
              <template #icon><ReloadOutlined /></template>
              刷新
            </a-button>
          </a-space>
        </div>
      </div>

      <!-- 项目概览卡片 -->
      <div class="project-overview grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="overview-card bg-blue-50 rounded-lg p-4">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-sm text-blue-600">总任务数</div>
              <div class="text-2xl font-bold text-blue-600">{{ projectOverview.totalTasks }}</div>
            </div>
            <ProjectOutlined class="text-3xl text-blue-600" />
          </div>
        </div>
        <div class="overview-card bg-green-50 rounded-lg p-4">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-sm text-green-600">整体进度</div>
              <div class="text-2xl font-bold text-green-600">{{ projectOverview.overallProgress }}%</div>
            </div>
            <CheckCircleOutlined class="text-3xl text-green-600" />
          </div>
        </div>
        <div class="overview-card bg-orange-50 rounded-lg p-4">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-sm text-orange-600">剩余天数</div>
              <div class="text-2xl font-bold text-orange-600">{{ projectOverview.remainingDays }}</div>
            </div>
            <CalendarOutlined class="text-3xl text-orange-600" />
          </div>
        </div>
        <div class="overview-card bg-purple-50 rounded-lg p-4">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-sm text-purple-600">关键任务</div>
              <div class="text-2xl font-bold text-purple-600">{{ projectOverview.criticalTasks }}</div>
            </div>
            <WarningOutlined class="text-3xl text-purple-600" />
          </div>
        </div>
      </div>

      <!-- 主计划表格 -->
      <div class="master-plan-table bg-white rounded-lg shadow-sm border">
        <a-table
          :columns="planColumns"
          :data-source="filteredPlanData"
          :pagination="false"
          :scroll="{ x: 1800, y: 600 }"
          row-key="id"
          :default-expand-all-rows="false"
          :expand-row-by-click="false"
          :tree-data="true"
          :row-selection="{
            selectedRowKeys: selectedRowKeys,
            onChange: onSelectChange,
          }">
          <!-- 任务名称列 -->
          <template #taskName="{ record }">
            <div class="flex items-center">
              <div class="task-icon mr-2">
                <a-avatar :style="{ backgroundColor: getTaskTypeColor(record.taskType) }" :size="20">
                  <template #icon>
                    <component :is="getTaskTypeIcon(record.taskType)" />
                  </template>
                </a-avatar>
              </div>
              <div>
                <div class="font-medium">{{ record.taskName }}</div>
                <div v-if="record.isCritical && showCriticalPath" class="text-xs text-red-500"> 关键路径 </div>
              </div>
            </div>
          </template>

          <!-- 状态列 -->
          <template #status="{ record }">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>

          <!-- 进度列 -->
          <template #progress="{ record }">
            <div class="flex items-center">
              <a-progress
                :percent="record.percentComplete"
                :show-info="false"
                size="small"
                class="flex-1 mr-2"
                :stroke-color="getProgressColor(record.percentComplete)" />
              <span class="text-sm">{{ record.percentComplete }}%</span>
            </div>
          </template>

          <!-- 甘特图列 -->
          <template #gantt="{ record }">
            <div class="gantt-bar-container" style="width: 200px; height: 20px; position: relative">
              <div
                class="gantt-bar"
                :style="{
                  left: getGanttPosition(record.planBeginTime) + '%',
                  width: getGanttWidth(record.planBeginTime, record.planEndTime) + '%',
                  backgroundColor: getTaskTypeColor(record.taskType),
                  height: '16px',
                  borderRadius: '2px',
                  position: 'absolute',
                  top: '2px',
                }"></div>
            </div>
          </template>

          <!-- 工时列 -->
          <template #workHours="{ record }">
            <div class="text-sm"> {{ record.totalHour || 0 }}h </div>
          </template>

          <!-- 负责人列 -->
          <template #responsible="{ record }">
            <div v-if="record.responserList && record.responserList.length > 0" class="flex items-center">
              <a-avatar-group :max-count="2" :size="24">
                <a-avatar v-for="person in record.responserList" :key="person.id" :size="24">
                  {{ person.name.charAt(0) }}
                </a-avatar>
              </a-avatar-group>
              <span v-if="record.responserList.length > 2" class="text-sm text-gray-500 ml-1"> +{{ record.responserList.length - 2 }} </span>
            </div>
            <span v-else class="text-gray-400">未分配</span>
          </template>

          <!-- 操作列 -->
          <template #action="{ record }">
            <a-space>
              <a-button type="link" size="small" @click="handleViewTask(record)"> 查看 </a-button>
              <a-button type="link" size="small" @click="handleEditTask(record)"> 编辑 </a-button>
              <a-dropdown>
                <a-button type="link" size="small"> 更多 <DownOutlined /> </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="handleAddSubTask(record)">
                      <PlusOutlined />
                      添加子任务
                    </a-menu-item>
                    <a-menu-item @click="handleSetDependency(record)">
                      <NodeIndexOutlined />
                      设置依赖
                    </a-menu-item>
                    <a-menu-item @click="handleAssignResource(record)">
                      <UserOutlined />
                      分配资源
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item @click="handleMarkComplete(record)" :disabled="record.status === 'completed'">
                      <CheckCircleOutlined />
                      标记完成
                    </a-menu-item>
                    <a-menu-item @click="handleDeleteTask(record)" danger>
                      <DeleteOutlined />
                      删除
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </a-table>
      </div>
    </a-spin>

    <!-- 任务详情抽屉 -->
    <a-modal v-model:open="taskDrawerVisible" title="任务详情" width="80%" :footer="null" :mask-closable="false">
      <div v-if="selectedTask" class="task-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="任务名称">
            {{ selectedTask.taskName }}
          </a-descriptions-item>
          <a-descriptions-item label="任务类型">
            {{ getTaskTypeText(selectedTask.taskType) }}
          </a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getStatusColor(selectedTask.status)">
              {{ getStatusText(selectedTask.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="优先级">
            <a-tag :color="getPriorityColor(selectedTask.priority)">
              {{ getPriorityText(selectedTask.priority) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="计划开始">
            {{ formatDate(selectedTask.planBeginTime) }}
          </a-descriptions-item>
          <a-descriptions-item label="计划结束">
            {{ formatDate(selectedTask.planEndTime) }}
          </a-descriptions-item>
          <a-descriptions-item label="实际开始">
            {{ formatDate(selectedTask.actualBeginTime) }}
          </a-descriptions-item>
          <a-descriptions-item label="实际结束">
            {{ formatDate(selectedTask.actualEndTime) }}
          </a-descriptions-item>
          <a-descriptions-item label="计划工期"> {{ selectedTask.duration || 0 }} 天 </a-descriptions-item>
          <a-descriptions-item label="计划工时"> {{ selectedTask.totalHour || 0 }} 小时 </a-descriptions-item>
          <a-descriptions-item label="完成率">
            <div class="flex items-center">
              <a-progress :percent="selectedTask.percentComplete" :show-info="false" size="small" class="flex-1 mr-2" />
              <span>{{ selectedTask.percentComplete }}%</span>
            </div>
          </a-descriptions-item>
          <a-descriptions-item label="是否关键路径">
            <a-tag :color="selectedTask.isCritical ? 'red' : 'default'">
              {{ selectedTask.isCritical ? '是' : '否' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="前置任务" :span="2">
            <a-tag v-for="dep in selectedTask.dependencies" :key="dep.id" class="mb-1">
              {{ dep.name }}
            </a-tag>
            <span v-if="!selectedTask.dependencies || selectedTask.dependencies.length === 0" class="text-gray-400"> 无前置任务 </span>
          </a-descriptions-item>
          <a-descriptions-item label="任务描述" :span="2">
            {{ selectedTask.description || '暂无描述' }}
          </a-descriptions-item>
        </a-descriptions>

        <!-- 负责人信息 -->
        <div class="mt-6">
          <h4 class="text-lg font-medium mb-4">负责人信息</h4>
          <div v-if="selectedTask.responserList && selectedTask.responserList.length > 0">
            <a-list item-layout="horizontal" :data-source="selectedTask.responserList" size="small">
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta>
                    <template #avatar>
                      <a-avatar>{{ item.name.charAt(0) }}</a-avatar>
                    </template>
                    <template #title>{{ item.name }}</template>
                    <template #description>{{ item.role || '项目成员' }} | 工作负载: {{ item.workload || 0 }}%</template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
          </div>
          <a-empty v-else description="暂无负责人" />
        </div>

        <!-- 子任务列表 -->
        <div class="mt-6" v-if="selectedTask.children && selectedTask.children.length > 0">
          <h4 class="text-lg font-medium mb-4">子任务</h4>
          <a-list item-layout="horizontal" :data-source="selectedTask.children" size="small">
            <template #renderItem="{ item }">
              <a-list-item>
                <template #actions>
                  <a @click="handleViewTask(item)">查看</a>
                </template>
                <a-list-item-meta>
                  <template #title>
                    <div class="flex items-center">
                      <component :is="getTaskTypeIcon(item.taskType)" class="mr-2" />
                      {{ item.taskName }}
                    </div>
                  </template>
                  <template #description>
                    状态: {{ getStatusText(item.status) }} | 进度: {{ item.percentComplete }}% | 计划完成: {{ formatDate(item.planEndTime) }}
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import dayjs from 'dayjs';
  import {
    PlusOutlined,
    ImportOutlined,
    ExportOutlined,
    ClockCircleOutlined,
    ReloadOutlined,
    DownOutlined,
    ProjectOutlined,
    CheckCircleOutlined,
    CalendarOutlined,
    WarningOutlined,
    NodeIndexOutlined,
    UserOutlined,
    DeleteOutlined,
    FolderOutlined,
    FileOutlined,
    SettingOutlined,
  } from '@ant-design/icons-vue';

  const { createMessage } = useMessage();

  // 响应式数据
  const loading = ref(false);
  const viewMode = ref('tree');
  const statusFilter = ref('');
  const showCriticalPath = ref(false);
  const selectedRowKeys = ref([]);
  const taskDrawerVisible = ref(false);
  const selectedTask = ref(null);

  // 项目概览数据
  const projectOverview = ref({
    totalTasks: 25,
    overallProgress: 35,
    remainingDays: 87,
    criticalTasks: 8,
  });

  // 主计划数据（模拟WBS结构）
  const planData = ref([
    {
      id: '1',
      taskName: '内部控制审计项目 ( 2025000054 )',
      taskType: 'project',
      status: 'in_progress',
      priority: 'high',
      percentComplete: 35,
      planBeginTime: '2025-03-03',
      planEndTime: '2025-09-02',
      actualBeginTime: '2025-03-03',
      actualEndTime: null,
      duration: 132,
      totalHour: 2320,
      isCritical: true,
      responserList: [],
      dependencies: [],
      description: '内部控制审计项目的总体计划',
      children: [
        {
          id: '2',
          taskName: '项目启动',
          taskType: 'package',
          status: 'delayed',
          priority: 'high',
          percentComplete: 60,
          planBeginTime: '2025-03-03',
          planEndTime: '2025-04-14',
          actualBeginTime: '2025-03-03',
          actualEndTime: null,
          duration: 31,
          totalHour: 744,
          isCritical: true,
          responserList: [
            { id: '1', name: '张三', role: '项目经理', workload: 80 },
            { id: '2', name: '李四', role: '业务分析师', workload: 60 },
          ],
          dependencies: [],
          description: '项目启动阶段的各项工作',
          children: [
            {
              id: '3',
              taskName: '1.1确定项目目标和范围',
              taskType: 'task',
              status: 'completed',
              priority: 'high',
              percentComplete: 100,
              planBeginTime: '2025-03-03',
              planEndTime: '2025-03-21',
              actualBeginTime: '2025-03-03',
              actualEndTime: '2025-03-20',
              duration: 15,
              totalHour: 360,
              isCritical: true,
              responserList: [{ id: '1', name: '张三', role: '项目经理', workload: 100 }],
              dependencies: [],
              description: '明确项目的目标和范围边界',
            },
            {
              id: '4',
              taskName: '1.2资源分配',
              taskType: 'task',
              status: 'in_progress',
              priority: 'medium',
              percentComplete: 75,
              planBeginTime: '2025-03-24',
              planEndTime: '2025-04-14',
              actualBeginTime: '2025-03-24',
              actualEndTime: null,
              duration: 16,
              totalHour: 384,
              isCritical: false,
              responserList: [
                { id: '2', name: '李四', role: '业务分析师', workload: 70 },
                { id: '3', name: '王五', role: '技术顾问', workload: 50 },
              ],
              dependencies: [{ id: '3', name: '1.1确定项目目标和范围' }],
              description: '进行人力和物力资源的分配',
            },
          ],
        },
        {
          id: '5',
          taskName: '审计计划',
          taskType: 'package',
          status: 'pending',
          priority: 'high',
          percentComplete: 0,
          planBeginTime: '2025-04-15',
          planEndTime: '2025-04-25',
          actualBeginTime: null,
          actualEndTime: null,
          duration: 9,
          totalHour: 144,
          isCritical: true,
          responserList: [{ id: '4', name: '赵六', role: '审计经理', workload: 90 }],
          dependencies: [{ id: '2', name: '项目启动' }],
          description: '制定详细的审计计划',
          children: [
            {
              id: '6',
              taskName: '2.1制定审计计划',
              taskType: 'task',
              status: 'pending',
              priority: 'high',
              percentComplete: 0,
              planBeginTime: '2025-04-15',
              planEndTime: '2025-04-21',
              actualBeginTime: null,
              actualEndTime: null,
              duration: 5,
              totalHour: 80,
              isCritical: true,
              responserList: [{ id: '4', name: '赵六', role: '审计经理', workload: 100 }],
              dependencies: [{ id: '2', name: '项目启动' }],
              description: '编制详细的审计实施计划',
            },
            {
              id: '7',
              taskName: '2.2风险评估',
              taskType: 'task',
              status: 'pending',
              priority: 'medium',
              percentComplete: 0,
              planBeginTime: '2025-04-22',
              planEndTime: '2025-04-25',
              actualBeginTime: null,
              actualEndTime: null,
              duration: 4,
              totalHour: 64,
              isCritical: false,
              responserList: [{ id: '5', name: '钱七', role: '风险专家', workload: 80 }],
              dependencies: [{ id: '6', name: '2.1制定审计计划' }],
              description: '识别和评估项目风险',
            },
          ],
        },
        {
          id: '8',
          taskName: '现场审计',
          taskType: 'package',
          status: 'pending',
          priority: 'high',
          percentComplete: 0,
          planBeginTime: '2025-04-28',
          planEndTime: '2025-05-21',
          actualBeginTime: null,
          actualEndTime: null,
          duration: 18,
          totalHour: 240,
          isCritical: true,
          responserList: [
            { id: '6', name: '孙八', role: '现场审计员', workload: 100 },
            { id: '7', name: '周九', role: '审计助理', workload: 90 },
          ],
          dependencies: [{ id: '5', name: '审计计划' }],
          description: '实施现场审计工作',
        },
        {
          id: '9',
          taskName: '报告编制',
          taskType: 'package',
          status: 'pending',
          priority: 'high',
          percentComplete: 0,
          planBeginTime: '2025-05-22',
          planEndTime: '2025-06-19',
          actualBeginTime: null,
          actualEndTime: null,
          duration: 21,
          totalHour: 312,
          isCritical: true,
          responserList: [{ id: '8', name: '吴十', role: '报告编写员', workload: 80 }],
          dependencies: [{ id: '8', name: '现场审计' }],
          description: '编制审计报告',
        },
        {
          id: '10',
          taskName: '质量控制',
          taskType: 'package',
          status: 'pending',
          priority: 'medium',
          percentComplete: 0,
          planBeginTime: '2025-06-20',
          planEndTime: '2025-07-17',
          actualBeginTime: null,
          actualEndTime: null,
          duration: 20,
          totalHour: 320,
          isCritical: false,
          responserList: [{ id: '9', name: '郑十一', role: '质量控制员', workload: 70 }],
          dependencies: [{ id: '9', name: '报告编制' }],
          description: '进行质量控制检查',
        },
        {
          id: '11',
          taskName: '项目收尾',
          taskType: 'package',
          status: 'pending',
          priority: 'medium',
          percentComplete: 0,
          planBeginTime: '2025-07-18',
          planEndTime: '2025-09-02',
          actualBeginTime: null,
          actualEndTime: null,
          duration: 33,
          totalHour: 560,
          isCritical: true,
          responserList: [{ id: '1', name: '张三', role: '项目经理', workload: 60 }],
          dependencies: [{ id: '10', name: '质量控制' }],
          description: '项目收尾和交付工作',
        },
      ],
    },
  ]);

  // 表格列配置
  const planColumns = [
    {
      title: '序号',
      dataIndex: 'sequence',
      key: 'sequence',
      width: 60,
      customRender: ({ record, index }) => {
        // 根据层级显示不同的序号
        if (record.taskType === 'project') return '';
        if (record.taskType === 'package') return index;
        return record.sequence || index + 1;
      },
    },
    {
      title: '任务名称',
      dataIndex: 'taskName',
      key: 'taskName',
      width: 300,
      slots: { customRender: 'taskName' },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      slots: { customRender: 'status' },
    },
    {
      title: '进度',
      dataIndex: 'percentComplete',
      key: 'percentComplete',
      width: 120,
      slots: { customRender: 'progress' },
    },
    {
      title: '计划开始',
      dataIndex: 'planBeginTime',
      key: 'planBeginTime',
      width: 110,
      customRender: ({ text }) => formatDate(text),
    },
    {
      title: '计划完成',
      dataIndex: 'planEndTime',
      key: 'planEndTime',
      width: 110,
      customRender: ({ text }) => formatDate(text),
    },
    {
      title: '工期(天)',
      dataIndex: 'duration',
      key: 'duration',
      width: 80,
      customRender: ({ text }) => text || 0,
    },
    {
      title: '计划工时',
      dataIndex: 'totalHour',
      key: 'totalHour',
      width: 100,
      slots: { customRender: 'workHours' },
    },
    {
      title: '负责人',
      dataIndex: 'responserList',
      key: 'responserList',
      width: 120,
      slots: { customRender: 'responsible' },
    },
    {
      title: '甘特图',
      dataIndex: 'gantt',
      key: 'gantt',
      width: 200,
      slots: { customRender: 'gantt' },
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      fixed: 'right',
      slots: { customRender: 'action' },
    },
  ];

  // 计算属性
  const filteredPlanData = computed(() => {
    let result = planData.value;

    // 状态过滤
    if (statusFilter.value) {
      result = result
        .map(item => ({
          ...item,
          children: filterChildrenByStatus(item.children, statusFilter.value),
        }))
        .filter(item => item.status === statusFilter.value || (item.children && item.children.length > 0));
    }

    return result;
  });

  // 递归过滤子节点
  const filterChildrenByStatus = (children: any[], status: string): any[] => {
    if (!children) return [];

    return children
      .map(child => ({
        ...child,
        children: filterChildrenByStatus(child.children, status),
      }))
      .filter(child => child.status === status || (child.children && child.children.length > 0));
  };

  onMounted(() => {
    loadPlanData();
  });

  // 加载计划数据
  const loadPlanData = async () => {
    loading.value = true;
    try {
      // 这里调用实际的API
      // const result = await getMasterPlan();
      // planData.value = result.data;
    } catch (error) {
      console.error('加载主计划数据失败:', error);
      createMessage.error('加载主计划数据失败');
    } finally {
      loading.value = false;
    }
  };

  // 工具函数
  const getTaskTypeColor = (taskType: string) => {
    const colorMap = {
      project: '#722ed1',
      package: '#1890ff',
      task: '#52c41a',
      milestone: '#faad14',
    };
    return colorMap[taskType] || '#1890ff';
  };

  const getTaskTypeIcon = (taskType: string) => {
    const iconMap = {
      project: ProjectOutlined,
      package: FolderOutlined,
      task: FileOutlined,
      milestone: SettingOutlined,
    };
    return iconMap[taskType] || FileOutlined;
  };

  const getTaskTypeText = (taskType: string) => {
    const textMap = {
      project: '项目',
      package: '工作包',
      task: '任务',
      milestone: '里程碑',
    };
    return textMap[taskType] || '未知';
  };

  const getStatusColor = (status: string) => {
    const colorMap = {
      completed: 'green',
      in_progress: 'blue',
      pending: 'default',
      delayed: 'red',
    };
    return colorMap[status] || 'default';
  };

  const getStatusText = (status: string) => {
    const textMap = {
      completed: '已完成',
      in_progress: '进行中',
      pending: '未开始',
      delayed: '延期',
    };
    return textMap[status] || '未知';
  };

  const getPriorityColor = (priority: string) => {
    const colorMap = {
      high: 'red',
      medium: 'orange',
      low: 'green',
    };
    return colorMap[priority] || 'default';
  };

  const getPriorityText = (priority: string) => {
    const textMap = {
      high: '高',
      medium: '中',
      low: '低',
    };
    return textMap[priority] || '未知';
  };

  const getProgressColor = (progress: number) => {
    if (progress === 100) return '#52c41a';
    if (progress >= 80) return '#1890ff';
    if (progress >= 60) return '#faad14';
    if (progress >= 40) return '#fa8c16';
    return '#ff4d4f';
  };

  const formatDate = (date: string) => {
    return date ? dayjs(date).format('MM-DD') : '-';
  };

  // 甘特图相关函数
  const getGanttPosition = (startDate: string) => {
    if (!startDate) return 0;
    const projectStart = dayjs('2025-03-01');
    const taskStart = dayjs(startDate);
    const totalDays = dayjs('2025-09-30').diff(projectStart, 'day');
    const taskStartDays = taskStart.diff(projectStart, 'day');
    return Math.max(0, (taskStartDays / totalDays) * 100);
  };

  const getGanttWidth = (startDate: string, endDate: string) => {
    if (!startDate || !endDate) return 0;
    const taskStart = dayjs(startDate);
    const taskEnd = dayjs(endDate);
    const taskDuration = taskEnd.diff(taskStart, 'day');
    const totalDays = dayjs('2025-09-30').diff(dayjs('2025-03-01'), 'day');
    return Math.max(1, (taskDuration / totalDays) * 100);
  };

  // 事件处理函数
  const handleStatusFilter = () => {
    // 触发状态过滤
  };

  const handleAddTask = () => {
    createMessage.info('添加任务功能开发中...');
  };

  const handleImportTemplate = () => {
    createMessage.info('导入模板功能开发中...');
  };

  const handleExport = () => {
    createMessage.info('导出计划功能开发中...');
  };

  const handleAutoSchedule = () => {
    createMessage.info('自动排期功能开发中...');
  };

  const handleRefresh = () => {
    loadPlanData();
  };

  const handleViewTask = (record: any) => {
    selectedTask.value = record;
    taskDrawerVisible.value = true;
  };

  const handleEditTask = (record: any) => {
    createMessage.info('编辑任务功能开发中...');
  };

  const handleAddSubTask = (record: any) => {
    createMessage.info('添加子任务功能开发中...');
  };

  const handleSetDependency = (record: any) => {
    createMessage.info('设置依赖功能开发中...');
  };

  const handleAssignResource = (record: any) => {
    createMessage.info('分配资源功能开发中...');
  };

  const handleMarkComplete = (record: any) => {
    createMessage.info('标记完成功能开发中...');
  };

  const handleDeleteTask = (record: any) => {
    createMessage.info('删除任务功能开发中...');
  };

  const onSelectChange = (selectedKeys: string[]) => {
    selectedRowKeys.value = selectedKeys;
  };
</script>

<style scoped>
  .master-plan-page {
    background: var(--section-bg-color);
  }

  .overview-card {
    transition: all 0.3s ease;
  }

  .overview-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .master-plan-table {
    min-height: 600px;
  }

  .gantt-bar-container {
    border: 1px solid #f0f0f0;
    background: #fafafa;
    border-radius: 4px;
  }

  .gantt-bar {
    opacity: 0.8;
  }

  .gantt-bar:hover {
    opacity: 1;
  }

  /* 表格行样式 */
  :deep(.ant-table-row) {
    transition: all 0.3s ease;
  }

  :deep(.ant-table-row:hover) {
    background-color: var(--section-bg-color);
  }

  /* 树形表格缩进 */
  :deep(.ant-table-tbody > tr.ant-table-row-level-1 > td) {
    padding-left: 28px;
  }

  :deep(.ant-table-tbody > tr.ant-table-row-level-2 > td) {
    padding-left: 56px;
  }
</style>
