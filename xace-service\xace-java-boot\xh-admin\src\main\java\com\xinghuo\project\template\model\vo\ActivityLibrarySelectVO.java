package com.xinghuo.project.template.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 标准项目活动库选择列表视图对象
 * 用于下拉选择框等场景，只包含必要的id和fullName字段
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
@Schema(description = "标准项目活动库选择列表视图对象")
public class ActivityLibrarySelectVO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private String id;

    /**
     * 完整名称 (格式: [编码] 名称)
     * 用于option显示，符合fullName和id的规范要求
     */
    @Schema(description = "完整名称")
    private String fullName;

    /**
     * 活动编码 (用于构建fullName)
     */
    @Schema(description = "活动编码")
    private String code;

    /**
     * 活动名称 (用于构建fullName)
     */
    @Schema(description = "活动名称")
    private String name;

    /**
     * 状态 (0:启用, 1:禁用)
     * 用于过滤，通常只显示启用状态的选项
     */
    @Schema(description = "状态")
    private Integer status;

    /**
     * 是否是里程碑 (1:是, 0:否)
     */
    @Schema(description = "是否是里程碑")
    private Integer isMilestone;

    /**
     * 标准工期 (天)
     */
    @Schema(description = "标准工期(天)")
    private BigDecimal duration;

    /**
     * 标准工时 (小时)
     */
    @Schema(description = "标准工时(小时)")
    private BigDecimal standardHour;

    /**
     * 活动大类ID
     */
    @Schema(description = "活动大类ID")
    private String activityTypeId;

    /**
     * 活动子类ID
     */
    @Schema(description = "活动子类ID")
    private String activitySubTypeId;
}
