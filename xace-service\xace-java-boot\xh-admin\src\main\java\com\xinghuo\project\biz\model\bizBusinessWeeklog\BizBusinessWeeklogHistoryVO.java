package com.xinghuo.project.biz.model.bizBusinessWeeklog;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 商机周报历史记录视图对象
 * 用于查询项目的历史商机周报记录
 * 
 * <AUTHOR>
 * @date 2025-07-08
 */
@Data
@Schema(description = "商机周报历史记录视图对象")
public class BizBusinessWeeklogHistoryVO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private String id;

    /**
     * 项目ID
     */
    @Schema(description = "项目ID")
    private String projId;

    /**
     * 项目名称
     */
    @Schema(description = "项目名称")
    private String projName;

    /**
     * 开始日期
     */
    @Schema(description = "开始日期")
    private Date startDate;

    /**
     * 结束日期
     */
    @Schema(description = "结束日期")
    private Date endDate;

    /**
     * 录入日期
     */
    @Schema(description = "录入日期")
    private Date inputDate;

    /**
     * 负责人ID
     */
    @Schema(description = "负责人ID")
    private String ownId;

    /**
     * 负责人名称
     */
    @Schema(description = "负责人名称")
    private String ownName;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 计划
     */
    @Schema(description = "计划")
    private String plan;

    /**
     * 风险
     */
    @Schema(description = "风险")
    private String risk;

    /**
     * 状态 (1-表示已填写，0-未填写，2-提交审核，3-已发布，-1-已驳回)
     */
    @Schema(description = "状态")
    private Integer status;

    /**
     * 状态名称
     */
    @Schema(description = "状态名称")
    private String statusName;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createdAt;

    /**
     * 创建人名称
     */
    @Schema(description = "创建人名称")
    private String createdByName;
}
