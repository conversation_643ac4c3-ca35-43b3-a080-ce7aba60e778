# 项目指引目录

## 📝 目录说明

此目录用于存放具体项目的业务模块开发指引，区别于框架级的通用规范。

## 🎯 适用场景

当基于XACE框架开发具体项目时，可在此目录添加：

### 业务模块指引
- 具体业务领域的开发规范
- 项目特有的数据结构设计
- 业务流程实现指南
- 项目定制化的组件规范

### 项目配置指引  
- 环境配置说明
- 部署流程指南
- 数据迁移脚本
- 项目特有的集成方案

### 团队协作规范
- 项目分工规范
- 代码review流程
- 发布流程管理
- 问题处理流程

## 📁 建议结构

```
project-guides/
├── business-modules/      # 业务模块指引
│   ├── order-management/  # 订单管理模块
│   ├── product-catalog/   # 产品目录模块
│   └── user-center/       # 用户中心模块
├── deployment/           # 部署指引
│   ├── environment-setup.md
│   └── deployment-guide.md
├── integration/          # 集成指引
│   ├── third-party-apis.md
│   └── external-systems.md
└── team-workflow/        # 团队流程
    ├── development-workflow.md
    └── release-process.md
```

## 🔗 与框架规范的关系

- **框架规范**（[framework-standards](../framework-standards/)）：通用的技术规范和最佳实践
- **项目指引**（本目录）：基于框架规范的具体项目实施指南

## 💡 使用建议

1. 先熟悉框架规范，再添加项目特有的指引
2. 保持与框架规范的一致性，避免冲突
3. 定期更新和维护项目指引文档
4. 新成员加入时，优先学习框架规范，再了解项目指引

---

**注意**：此目录当前为空，等待具体项目团队根据实际需求添加相关文档。