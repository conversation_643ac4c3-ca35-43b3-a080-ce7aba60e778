<template>
  <div class="template-phase-page p-6">
    <div class="max-w-7xl mx-auto">
      <!-- 页面标题 -->
      <div class="header-section mb-6">
        <div class="flex justify-between items-center">
          <div>
            <h1 class="text-2xl font-bold mb-2">项目阶段配置</h1>
            <p class="text-gray-600">配置项目模板的阶段划分和进度管理</p>
          </div>
          <a-space>
            <a-button @click="handleImportPhases">
              <template #icon><ImportOutlined /></template>
              导入阶段
            </a-button>
            <a-button type="primary" @click="handleAddPhase">
              <template #icon><PlusOutlined /></template>
              添加阶段
            </a-button>
          </a-space>
        </div>
      </div>

      <!-- 阶段配置 -->
      <a-card title="阶段管理" :bordered="false" class="mb-6">
        <a-table
          :columns="phaseColumns"
          :data-source="phaseList"
          :loading="loading"
          :pagination="false"
          row-key="id"
          :row-selection="{
            selectedRowKeys: selectedRows,
            onChange: onSelectionChange,
            type: 'checkbox',
          }"
          size="small">
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.key === 'seqNo'">
              {{ index + 1 }}
            </template>
            <template v-else-if="column.key === 'canCut'">
              <a-tag :color="record.canCut === 1 ? 'green' : 'red'">
                {{ record.canCut === 1 ? '是' : '否' }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'status'">
              <a-tag :color="record.status === 1 ? 'green' : 'default'">
                {{ record.status === 1 ? '启用' : '禁用' }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'progress'">
              <a-progress :percent="record.completionWeight || 0" size="small" :show-info="false" />
              <span class="ml-2 text-xs">{{ record.completionWeight || 0 }}%</span>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="handleEditPhase(record)"> 编辑 </a-button>
                <a-button type="link" size="small" @click="handleConfigPhase(record)"> 配置 </a-button>
                <a-button type="link" size="small" danger @click="handleDeletePhase(record)"> 删除 </a-button>
              </a-space>
            </template>
          </template>
        </a-table>

        <div class="mt-4" v-if="selectedRows.length > 0">
          <a-space>
            <a-button type="primary" danger @click="handleBatchDelete"> 批量删除 ({{ selectedRows.length }}) </a-button>
            <a-button @click="handleBatchEnable"> 批量启用 </a-button>
            <a-button @click="handleBatchDisable"> 批量禁用 </a-button>
          </a-space>
        </div>
      </a-card>

      <!-- 阶段流程图 -->
      <a-card title="阶段流程图" :bordered="false">
        <div class="phase-flow-chart p-4">
          <div class="flex items-center justify-center space-x-4 overflow-x-auto">
            <template v-for="(phase, index) in phaseList" :key="phase.id">
              <div class="phase-node flex flex-col items-center">
                <div
                  class="phase-box w-24 h-16 border-2 rounded-lg flex items-center justify-center cursor-pointer transition-all"
                  :class="{
                    'border-blue-500 bg-blue-50': phase.status === 1,
                    'border-gray-300 bg-gray-50': phase.status !== 1,
                  }"
                  @click="handleConfigPhase(phase)">
                  <div class="text-center">
                    <div class="text-sm font-medium">{{ phase.phaseName }}</div>
                    <div class="text-xs text-gray-500">{{ phase.duration }}天</div>
                  </div>
                </div>
                <div class="mt-2 text-xs text-center">
                  <div>权重: {{ phase.completionWeight }}%</div>
                  <div :class="phase.canCut === 1 ? 'text-green-600' : 'text-red-600'">
                    {{ phase.canCut === 1 ? '可裁剪' : '不可裁剪' }}
                  </div>
                </div>
              </div>
              <div v-if="index < phaseList.length - 1" class="phase-arrow">
                <RightOutlined class="text-gray-400" />
              </div>
            </template>
          </div>

          <div v-if="phaseList.length === 0" class="text-center py-8 text-gray-500">
            <Empty description="暂无阶段配置">
              <a-button type="primary" @click="handleAddPhase"> 添加第一个阶段 </a-button>
            </Empty>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 阶段编辑弹窗 -->
    <a-modal
      v-model:open="phaseModalVisible"
      :title="phaseEditMode === 'add' ? '添加项目阶段' : '编辑项目阶段'"
      width="600px"
      @ok="handleSavePhase"
      @cancel="handleCancelPhase">
      <a-form :model="phaseForm" :rules="phaseRules" layout="vertical">
        <a-form-item label="阶段名称" name="phaseName" required>
          <a-input v-model:value="phaseForm.phaseName" placeholder="请输入阶段名称" />
        </a-form-item>
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="工期(天)" name="duration">
              <a-input-number v-model:value="phaseForm.duration" :min="0" class="w-full" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="完成权重(%)" name="completionWeight">
              <a-input-number v-model:value="phaseForm.completionWeight" :min="0" :max="100" class="w-full" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="排序" name="sortOrder">
              <a-input-number v-model:value="phaseForm.sortOrder" :min="1" class="w-full" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="可裁剪" name="canCut">
              <a-radio-group v-model:value="phaseForm.canCut">
                <a-radio :value="0">否</a-radio>
                <a-radio :value="1">是</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="状态" name="status">
              <a-radio-group v-model:value="phaseForm.status">
                <a-radio :value="1">启用</a-radio>
                <a-radio :value="0">禁用</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item label="阶段描述" name="description">
          <a-textarea v-model:value="phaseForm.description" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import {
    Card as ACard,
    Table as ATable,
    Button as AButton,
    Space as ASpace,
    Tag as ATag,
    Progress as AProgress,
    Modal as AModal,
    Form as AForm,
    FormItem as AFormItem,
    Input as AInput,
    InputNumber as AInputNumber,
    Textarea as ATextarea,
    Radio as ARadio,
    RadioGroup as ARadioGroup,
    Row as ARow,
    Col as ACol,
    Empty,
  } from 'ant-design-vue';
  import { PlusOutlined, ImportOutlined, RightOutlined } from '@ant-design/icons-vue';
  import { useMessage } from '/@/hooks/web/useMessage';

  interface Props {
    templateId: string;
  }

  const props = defineProps<Props>();
  const { createMessage, createConfirm } = useMessage();

  // 响应式数据
  const phaseList = ref([]);
  const loading = ref(false);
  const selectedRows = ref([]);
  const phaseModalVisible = ref(false);
  const phaseEditMode = ref('add');
  const phaseForm = reactive({
    id: '',
    phaseName: '',
    duration: 0,
    completionWeight: 0,
    canCut: 0,
    status: 1,
    sortOrder: 1,
    description: '',
  });

  // 表格列定义
  const phaseColumns = [
    { title: '序号', key: 'seqNo', width: 60, align: 'center' },
    { title: '阶段名称', dataIndex: 'phaseName', width: 150 },
    { title: '工期(天)', dataIndex: 'duration', width: 100, align: 'center' },
    { title: '完成权重', key: 'progress', width: 150, align: 'center' },
    { title: '可裁剪', key: 'canCut', width: 100, align: 'center' },
    { title: '状态', key: 'status', width: 100, align: 'center' },
    { title: '描述', dataIndex: 'description' },
    { title: '操作', key: 'action', width: 180, align: 'center', fixed: 'right' },
  ];

  // 表单验证规则
  const phaseRules = {
    phaseName: [{ required: true, message: '请输入阶段名称', trigger: 'blur' }],
  };

  // 方法
  function onSelectionChange(selectedRowKeys: string[]) {
    selectedRows.value = selectedRowKeys;
  }

  function handleAddPhase() {
    resetPhaseForm();
    phaseEditMode.value = 'add';
    phaseModalVisible.value = true;
  }

  function handleEditPhase(record: any) {
    Object.assign(phaseForm, record);
    phaseEditMode.value = 'edit';
    phaseModalVisible.value = true;
  }

  function handleConfigPhase(record: any) {
    createMessage.info(`配置阶段：${record.phaseName}（功能开发中...）`);
  }

  function handleDeletePhase(record: any) {
    createConfirm({
      title: '确认删除',
      content: `确定要删除阶段"${record.phaseName}"吗？`,
      onOk: () => {
        createMessage.info('删除功能开发中...');
      },
    });
  }

  function handleBatchDelete() {
    createConfirm({
      title: '确认删除',
      content: `确定要删除选中的${selectedRows.value.length}个阶段吗？`,
      onOk: () => {
        createMessage.info('批量删除功能开发中...');
      },
    });
  }

  function handleBatchEnable() {
    createMessage.info('批量启用功能开发中...');
  }

  function handleBatchDisable() {
    createMessage.info('批量禁用功能开发中...');
  }

  function handleImportPhases() {
    createMessage.info('导入阶段功能开发中...');
  }

  function handleSavePhase() {
    createMessage.info('保存功能开发中...');
    phaseModalVisible.value = false;
  }

  function handleCancelPhase() {
    phaseModalVisible.value = false;
    resetPhaseForm();
  }

  function resetPhaseForm() {
    Object.assign(phaseForm, {
      id: '',
      phaseName: '',
      duration: 0,
      completionWeight: 0,
      canCut: 0,
      status: 1,
      sortOrder: 1,
      description: '',
    });
  }

  // 加载阶段配置
  async function loadPhases() {
    if (!props.templateId) return;

    try {
      loading.value = true;
      // TODO: 调用API加载阶段数据
      // 模拟数据
      phaseList.value = [
        {
          id: '1',
          phaseName: '项目启动',
          duration: 5,
          completionWeight: 10,
          canCut: 0,
          status: 1,
          description: '项目启动阶段',
        },
        {
          id: '2',
          phaseName: '需求分析',
          duration: 10,
          completionWeight: 20,
          canCut: 1,
          status: 1,
          description: '需求分析阶段',
        },
        {
          id: '3',
          phaseName: '系统设计',
          duration: 15,
          completionWeight: 25,
          canCut: 1,
          status: 1,
          description: '系统设计阶段',
        },
        {
          id: '4',
          phaseName: '开发实现',
          duration: 30,
          completionWeight: 35,
          canCut: 0,
          status: 1,
          description: '开发实现阶段',
        },
        {
          id: '5',
          phaseName: '测试验收',
          duration: 10,
          completionWeight: 10,
          canCut: 0,
          status: 1,
          description: '测试验收阶段',
        },
      ];
    } catch (error) {
      console.error('加载阶段配置失败:', error);
      createMessage.error('加载阶段配置失败');
    } finally {
      loading.value = false;
    }
  }

  // 初始化
  onMounted(() => {
    loadPhases();
  });
</script>

<style scoped>
  .template-phase-page {
    background: #f5f5f5;
    min-height: 100vh;
  }

  .phase-flow-chart {
    background: #fafafa;
    border-radius: 8px;
    min-height: 200px;
  }

  .phase-box:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .phase-arrow {
    display: flex;
    align-items: center;
    margin: 0 8px;
  }

  :deep(.ant-card-head) {
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
  }

  :deep(.ant-table-thead > tr > th) {
    background: #fafafa;
    font-weight: 600;
  }
</style>
