<template>
  <Layout v-if="templateInfo" :menu-data="menuData" @onChangeMain="data => onMenuChange(data, 'mainMenu')" @onChangeSub="data => onMenuChange(data, 'subMenu')">
    <template #header>
      <div class="px-[8px] relative flex items-center">
        <div class="flex items-center space-x-4">
          <TemplateSelector v-model:value="selectedTemplateId" @change="onTemplateChange" />
        </div>
        <div class="ml-auto flex items-center">
          <Tooltip title="保存配置">
            <a-button class="ml-[8px]" type="primary" @click="handleSave">
              <template #icon><SaveOutlined /></template>
              保存
            </a-button>
          </Tooltip>
          <Tooltip title="刷新数据">
            <a-button class="ml-[8px]" type="text" @click="reload">
              <template #icon><ReloadOutlined /></template>
            </a-button>
          </Tooltip>
          <Tooltip title="返回列表">
            <a-button class="ml-[8px]" type="text" @click="goBack">
              <template #icon><ArrowLeftOutlined /></template>
            </a-button>
          </Tooltip>
        </div>
      </div>
    </template>

    <!-- 主要内容区域 -->
    <div class="template-config-content">
      <!-- 如果没有选择模板，显示选择提示 -->
      <div v-if="!selectedTemplateId" class="template-select-hint">
        <TemplateSelector v-model:value="selectedTemplateId" @change="onTemplateChange" placeholder="请选择要配置的项目模板" />
      </div>

      <!-- 如果选择了模板但没有选择菜单，显示模板基本信息 -->
      <div v-else-if="!activeMenuKey" class="template-dashboard">
        <BasicInfo :template-id="selectedTemplateId" @success="reload" />
      </div>

      <!-- 如果选择了模板和菜单，显示对应组件 -->
      <div v-else>
        <Spin :spinning="loading">
          <keep-alive :key="`${activeMenuKey}-${selectedTemplateId}`">
            <component :template-id="selectedTemplateId" :is="activeComponent" @success="reload" />
          </keep-alive>
        </Spin>
      </div>
    </div>
  </Layout>
</template>

<script setup lang="ts">
  import { ref, onMounted, markRaw, provide, computed } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { Spin, Tooltip } from 'ant-design-vue';
  import { ReloadOutlined, SaveOutlined, ArrowLeftOutlined } from '@ant-design/icons-vue';
  import Layout from '/@/views/project/portfolio/layout.vue';
  import Exception from '/@/views/basic/exception/Exception.vue';
  import TemplateSelector from '../components/TemplateSelector.vue';
  import BasicInfo from './BasicInfo.vue';
  import { getProjectTemplateInfo } from '/@/api/project/projectTemplate';
  import { useMessage } from '/@/hooks/web/useMessage';

  const { createMessage } = useMessage();
  const route = useRoute();
  const router = useRouter();

  const menuData = ref([]);
  const activeComponent = ref();
  const activeMenuKey = ref('');
  const renderKey = ref(0);
  const loading = ref(false);
  const componentCache = new Map();

  // 模板相关
  const selectedTemplateId = ref('');
  const templateInfo = ref<any>(null);

  // 向子组件提供模板ID
  provide('templateId', selectedTemplateId);

  // 每个路由组件需要按照系统菜单严格匹配，否则开启缓存后不生效
  defineOptions({ name: 'project-schema-projectTemplate-config' });

  // 计算当前模板信息
  const currentTemplate = computed(() => {
    return templateInfo.value;
  });

  const reload = () => {
    console.log('重新加载模板配置数据', selectedTemplateId.value);
    renderKey.value++;
    // 清除组件缓存，强制重新加载
    componentCache.clear();
    // 重新加载当前组件
    if (activeMenuKey.value) {
      loadComponent(activeMenuKey.value);
    }
    // 重新加载模板信息
    if (selectedTemplateId.value) {
      loadTemplateInfo();
    }
  };

  const handleSave = () => {
    createMessage.success('配置保存成功');
  };

  const goBack = () => {
    router.push('/project/schema/projectTemplate');
  };

  // 模板选择相关方法
  const onTemplateChange = (template: any) => {
    const oldTemplateId = selectedTemplateId.value;
    selectedTemplateId.value = template.id;
    templateInfo.value = template;

    // 模板切换后重新加载菜单和组件
    loadTemplateMenu(oldTemplateId !== template.id);
    console.log('模板已切换:', template);
  };

  // 加载模板信息
  const loadTemplateInfo = async () => {
    if (!selectedTemplateId.value) return;

    try {
      const response = await getProjectTemplateInfo(selectedTemplateId.value);
      if (response.code === 200) {
        templateInfo.value = response.data;
      }
    } catch (error) {
      console.error('加载模板信息失败:', error);
    }
  };

  // 加载模板配置菜单
  const loadTemplateMenu = (isTemplateChanged = false) => {
    fetch(import.meta.env.VITE_PUBLIC_PATH + 'data/projectTemplateConfig.json')
      .then(response => response.json())
      .then(data => {
        menuData.value = data || [];
        console.log('项目模板配置菜单数据加载完成:', data);

        if (data && data.length > 0) {
          if (isTemplateChanged && activeMenuKey.value) {
            // 模板切换时，检查当前菜单是否在新模板中也存在
            const currentMenuItem = findMenuItemByKey(data, activeMenuKey.value);

            if (currentMenuItem) {
              // 当前菜单在新模板中也存在，保持当前菜单，只刷新数据
              console.log('保持当前菜单:', currentMenuItem.fullName);
              // 清除组件缓存，强制重新加载当前组件以获取新模板数据
              componentCache.delete(activeMenuKey.value);
              loadComponent(activeMenuKey.value);
            } else {
              // 当前菜单在新模板中不存在，跳转到第一个菜单项
              console.log('当前菜单不存在，跳转到默认菜单');
              onMenuChange(data[0], 'mainMenu');
            }
          } else if (!activeMenuKey.value) {
            // 没有当前活动菜单，加载第一个菜单项
            onMenuChange(data[0], 'mainMenu');
          } else {
            // 模板没有切换，保持当前页面但刷新组件（传入新的模板ID）
            if (activeComponent.value && activeMenuKey.value) {
              // 清除组件缓存，强制重新加载当前组件
              componentCache.delete(activeMenuKey.value);
              loadComponent(activeMenuKey.value);
            }
          }
        }
      })
      .catch(error => {
        console.error('加载项目模板配置菜单数据失败:', error);
      });
  };

  // 检查菜单项是否存在于菜单数据中
  const findMenuItemByKey = (menuData: any[], menuKey: string): any => {
    for (const menu of menuData) {
      if (menu.enCode === menuKey) {
        return menu;
      }
      if (menu.children) {
        for (const child of menu.children) {
          if (child.enCode === menuKey) {
            return child;
          }
        }
      }
    }
    return null;
  };

  onMounted(async () => {
    // 从路由参数获取模板ID
    const templateId = route.params.id as string;
    if (templateId) {
      selectedTemplateId.value = templateId;
      await loadTemplateInfo();
      loadTemplateMenu();
    }
  });

  const loadComponent = async (enCode: string) => {
    loading.value = true;

    if (componentCache.has(enCode)) {
      activeComponent.value = componentCache.get(enCode);
      loading.value = false;
      return;
    }

    try {
      let module: any;

      // 处理包含斜杠的路径（如 plan/phase）
      if (enCode.includes('/')) {
        const [folder, subpage] = enCode.split('/');
        // 使用更具体的导入模式，让 Vite 能够正确解析
        const modules = import.meta.glob('./pages/*/*/page.vue');
        const modulePath = `./pages/${folder}/${subpage}/page.vue`;

        if (modules[modulePath]) {
          module = await modules[modulePath]();
        } else {
          throw new Error(`Component not found: ${modulePath}`);
        }
      } else {
        // 处理简单路径（如 basic）
        const modules = import.meta.glob('./pages/*/page.vue');
        const modulePath = `./pages/${enCode}/page.vue`;

        if (modules[modulePath]) {
          module = await modules[modulePath]();
        } else {
          throw new Error(`Component not found: ${modulePath}`);
        }
      }

      activeComponent.value = markRaw(module.default);
      componentCache.set(enCode, markRaw(module.default));
      loading.value = false;
    } catch (error) {
      console.error(`加载组件失败: ${enCode}`, error);
      activeComponent.value = markRaw(Exception);
      loading.value = false;
    }
  };

  const onMenuChange = (data: any, mainOrSub: string) => {
    console.log(`菜单切换 (${mainOrSub}):`, data);

    if (data.enCode) {
      activeMenuKey.value = data.enCode;
      loadComponent(data.enCode);
    } else {
      activeComponent.value = markRaw(Exception);
      loading.value = false;
    }
  };
</script>

<style lang="less" scoped>
  .template-config-content {
    .template-select-hint {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 400px;
      background: white;
      border-radius: 8px;
      padding: 24px;
      margin: 16px;
    }

    .template-dashboard {
      padding: 16px;

      :deep(.ant-card) {
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      }
    }
  }

  .icon-ym {
    font-size: 16px;
  }
</style>
