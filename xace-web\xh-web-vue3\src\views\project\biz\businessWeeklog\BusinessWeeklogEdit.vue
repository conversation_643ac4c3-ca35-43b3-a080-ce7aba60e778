<template>
  <BasicDrawer
    v-bind="$attrs"
    @register="registerDrawer"
    title="商机周报编辑"
    width="1200px"
    @ok="handleSubmit"
    showDetailBack
    :keyboard="false"
    :maskClosable="false">
    <div class="p-4">
      <!-- 主要内容区域：使用Tabs分隔 -->
      <a-tabs v-model:activeKey="activeTab" type="card">
        <!-- 编辑表单Tab -->
        <a-tab-pane key="edit" tab="编辑周报">
          <BasicForm @register="registerForm" />
        </a-tab-pane>

        <!-- 历史记录Tab -->
        <a-tab-pane key="history" tab="历史记录" :disabled="!formState.projId">
          <div v-if="activeTab === 'history'">
            <BusinessWeeklogHistory :projId="formState.projId" @select="handleHistorySelect" />
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 自定义底部按钮 -->
    <template #footer>
      <div class="flex justify-between items-center w-full">
        <!-- 左侧导航按钮 -->
        <div class="flex gap-2">
          <a-button :disabled="!hasPrevious" @click="handlePrevious">上一条</a-button>
          <a-button :disabled="!hasNext" @click="handleNext">下一条</a-button>
        </div>

        <!-- 右侧操作按钮 -->
        <div class="flex gap-2">
          <a-button @click="handleCancel">取消</a-button>
          <a-button v-if="formState.status === 1" type="primary" @click="handleSubmitForAudit"> 提交审核 </a-button>
          <a-button type="primary" @click="handleSubmit">
            {{ isUpdate ? '保存' : '创建' }}
          </a-button>
        </div>
      </div>
    </template>
  </BasicDrawer>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed, unref, nextTick } from 'vue';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { BasicForm, useForm } from '/@/components/Form';
  import type { FormSchema } from '/@/components/Form/src/types/form';
  import { getBusinessWeeklogInfo, createBusinessWeeklog, updateBusinessWeeklog, submitBusinessWeeklogForAudit } from '/@/api/project/biz/businessWeeklog';
  import { getOpportunityInfo } from '/@/api/project/biz/opportunity';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useUserStore } from '/@/store/modules/user';
  import BusinessWeeklogHistory from './components/BusinessWeeklogHistory.vue';

  const emit = defineEmits(['success', 'register']);
  const { createMessage } = useMessage();
  const userStore = useUserStore();

  // 状态管理
  const activeTab = ref('edit');
  const isUpdate = ref(false);
  const recordId = ref('');
  const formState = reactive({
    projId: '',
    status: 0,
  });

  // 导航状态
  const hasPrevious = ref(false);
  const hasNext = ref(false);
  const currentIndex = ref(-1);
  const recordList = ref([]);

  // 表单Schema
  const formSchemas: FormSchema[] = [
    // 商机信息区域
    {
      field: 'opportunityInfo',
      component: 'Divider',
      label: '商机信息',
      colProps: { span: 24 },
    },
    {
      field: 'fbId',
      component: 'OrganizeSelect',
      label: '所属分部',
      colProps: { span: 12 },
      componentProps: {
        disabled: true,
      },
    },
    {
      field: 'projName',
      component: 'Input',
      label: '项目名称',
      required: true,
      colProps: { span: 24 },
      componentProps: {
        readonly: true,
      },
    },
    {
      field: 'projNote',
      component: 'Textarea',
      label: '项目信息',
      colProps: { span: 24 },
      componentProps: {
        rows: 3,
        readonly: true,
        showCount: true,
      },
    },

    // 进度跟踪区域
    {
      field: 'progressInfo',
      component: 'Divider',
      label: '进度跟踪',
      colProps: { span: 24 },
    },
    {
      field: 'progressTip',
      component: 'Alert',
      label: '',
      colProps: { span: 24 },
      componentProps: {
        type: 'success',
        showIcon: false,
        description: 'A类项目 - 半年内落地的，每周汇报一下进度和风险情况。B类项目/A类半年后落地、三个月落地的维护项目，每两周汇报一下进度和风险情况。',
      },
    },
    {
      field: 'ownId',
      component: 'UserSelect',
      label: '项目经理',
      required: true,
      colProps: { span: 12 },
      componentProps: {
        disabled: true,
      },
    },
    {
      field: 'startDate',
      component: 'DatePicker',
      label: '开始日期',
      required: true,
      colProps: { span: 12 },
      componentProps: {
        disabled: true,
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
    },
    {
      field: 'projectLevel',
      component: 'Input',
      label: '项目等级',
      required: true,
      colProps: { span: 12 },
      componentProps: {
        disabled: true,
      },
    },
    {
      field: 'endDate',
      component: 'DatePicker',
      label: '结束日期',
      required: true,
      colProps: { span: 12 },
      componentProps: {
        disabled: true,
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
    },
    {
      field: 'note',
      component: 'Textarea',
      label: '本次进度',
      required: true,
      colProps: { span: 24 },
      componentProps: {
        rows: 4,
        placeholder: '要体现项目的总体进度阶段，当前进度。',
        showCount: true,
        maxlength: 1000,
      },
    },
    {
      field: 'plan',
      component: 'Textarea',
      label: '下一步计划',
      required: true,
      colProps: { span: 24 },
      componentProps: {
        rows: 4,
        placeholder: '请输入下一步计划',
        showCount: true,
        maxlength: 1000,
      },
    },
    {
      field: 'risk',
      component: 'Textarea',
      label: '存在风险/问题',
      colProps: { span: 24 },
      componentProps: {
        rows: 4,
        placeholder: '请输入存在的风险或问题',
        showCount: true,
        maxlength: 1000,
      },
    },
  ];

  // 注册表单
  const [registerForm, { setFieldsValue, getFieldsValue, validate, resetFields }] = useForm({
    labelWidth: 120,
    schemas: formSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });

  // 注册抽屉
  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async data => {
    await resetFields();
    setDrawerProps({ confirmLoading: false });

    isUpdate.value = !!data?.isUpdate;
    recordId.value = data?.record?.id || '';

    if (data?.record) {
      // 设置导航信息
      recordList.value = data.recordList || [];
      currentIndex.value = data.currentIndex || -1;
      updateNavigationState();

      // 加载数据
      await loadFormData(data.record);
    } else {
      // 新建模式
      await loadDefaultData();
    }
  });

  // 加载表单数据
  async function loadFormData(record: any) {
    try {
      let formData = { ...record };

      // 如果有projId，加载商机信息
      if (record.projId) {
        formState.projId = record.projId;
        const opportunityRes = await getOpportunityInfo(record.projId);
        if (opportunityRes.code === 200) {
          const opportunity = opportunityRes.data;
          formData = {
            ...formData,
            projName: opportunity.projectName,
            projNote: opportunity.projectContent,
            projectLevel: opportunity.projectLevel,
            fbId: opportunity.deptId,
          };
        }
      }

      formState.status = record.status || 0;
      await setFieldsValue(formData);
    } catch (error) {
      console.error('加载表单数据失败:', error);
      createMessage.error('加载数据失败');
    }
  }

  // 加载默认数据
  async function loadDefaultData() {
    const userInfo = userStore.getUserInfo;
    await setFieldsValue({
      ownId: userInfo.userId,
      startDate: new Date().toISOString().split('T')[0],
      endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      status: 0,
    });
  }

  // 更新导航状态
  function updateNavigationState() {
    hasPrevious.value = currentIndex.value > 0;
    hasNext.value = currentIndex.value < recordList.value.length - 1;
  }

  // 历史记录选择处理
  function handleHistorySelect(historyRecord: any) {
    const currentValues = getFieldsValue();

    // 只覆盖可编辑的字段
    setFieldsValue({
      ...currentValues,
      note: historyRecord.note,
      plan: historyRecord.plan,
      risk: historyRecord.risk,
    });

    // 切换回编辑Tab
    activeTab.value = 'edit';
    createMessage.success('已引用历史记录内容');
  }

  // 处理保存
  async function handleSubmit() {
    try {
      const values = await validate();
      setDrawerProps({ confirmLoading: true });

      if (isUpdate.value) {
        await updateBusinessWeeklog(recordId.value, values);
        createMessage.success('更新成功');
      } else {
        const result = await createBusinessWeeklog(values);
        recordId.value = result.data;
        createMessage.success('创建成功');
      }

      emit('success');
      closeDrawer();
    } catch (error) {
      console.error('保存失败:', error);
      createMessage.error('保存失败');
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }

  // 处理提交审核
  async function handleSubmitForAudit() {
    try {
      // 先保存
      await handleSubmit();

      // 提交审核
      await submitBusinessWeeklogForAudit(recordId.value);
      createMessage.success('提交审核成功');

      emit('success');
      closeDrawer();
    } catch (error) {
      console.error('提交审核失败:', error);
      createMessage.error('提交审核失败');
    }
  }

  // 处理取消
  function handleCancel() {
    closeDrawer();
  }

  // 处理上一条
  function handlePrevious() {
    if (hasPrevious.value) {
      currentIndex.value--;
      const record = recordList.value[currentIndex.value];
      loadFormData(record);
      updateNavigationState();
    }
  }

  // 处理下一条
  function handleNext() {
    if (hasNext.value) {
      currentIndex.value++;
      const record = recordList.value[currentIndex.value];
      loadFormData(record);
      updateNavigationState();
    }
  }
</script>

<style lang="less" scoped>
  .ant-tabs {
    .ant-tabs-content-holder {
      padding: 0;
    }
  }

  :deep(.ant-form-item-control-input-content) {
    .ant-alert {
      margin-bottom: 0;
    }
  }
</style>
