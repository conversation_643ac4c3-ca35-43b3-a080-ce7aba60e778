package com.xinghuo.project.template.dao;

import com.xinghuo.common.base.dao.XHBaseMapper;
import com.xinghuo.project.template.entity.TemplateRelationEntity;
import org.apache.ibatis.annotations.Mapper;

/**
 * 通用模板关联Mapper接口
 *
 * 注意：此Mapper接口仅继承XHBaseMapper，不再定义自定义SQL方法。
 * 所有数据操作都通过Service层使用MyBatis Plus的QueryWrapper实现。
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Mapper
public interface TemplateRelationMapper extends XHBaseMapper<TemplateRelationEntity> {
    // 不再定义自定义SQL方法，所有操作通过QueryWrapper实现
}
