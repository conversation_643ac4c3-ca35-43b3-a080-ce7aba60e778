package com.xinghuo.performance.model.analysis;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 个人绩效详情VO
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class PersonalPerformanceDetailVO {

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 分部名称
     */
    private String fbName;

    /**
     * 绩效总分
     */
    private BigDecimal totalScore;

    /**
     * 详细维度得分
     */
    private String detailedScores;

    /**
     * 评价内容
     */
    private String evaluation;
}
