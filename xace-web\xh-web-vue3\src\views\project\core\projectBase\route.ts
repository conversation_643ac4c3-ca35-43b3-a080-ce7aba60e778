import type { AppRouteModule } from '/@/router/types';

import { LAYOUT } from '/@/router/constant';
import { t } from '/@/hooks/web/useI18n';

const projectBase: AppRouteModule = {
  path: '/project/core',
  name: 'ProjectCore',
  component: LAYOUT,
  redirect: '/project/core/projectQueryIndex',
  meta: {
    orderNo: 1000,
    icon: 'ant-design:project-outlined',
    title: t('routes.project.core.moduleName'),
    hideChildrenInMenu: false,
  },
  children: [
    {
      path: 'projectQueryIndex',
      name: 'ProjectQueryIndex',
      component: () => import('./ProjectQueryIndex.vue'),
      meta: {
        title: '项目查询',
        icon: 'ant-design:search-outlined',
        hideMenu: false,
      },
    },
    {
      path: 'projectBase',
      name: 'ProjectBase',
      component: () => import('./index.vue'),
      meta: {
        title: t('routes.project.core.projectBase'),
        icon: 'ant-design:project-outlined',
        hideMenu: false,
      },
    },
    {
      path: 'projectQuery',
      name: 'ProjectQuery',
      component: () => import('./ProjectQuery.vue'),
      meta: {
        title: '项目查询详情',
        icon: 'ant-design:search-outlined',
        hideMenu: true, // 隐藏菜单，作为内部页面
      },
    },
  ],
};

export default projectBase;
