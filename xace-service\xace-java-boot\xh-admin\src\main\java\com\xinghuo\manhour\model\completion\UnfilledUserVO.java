package com.xinghuo.manhour.model.completion;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 未填写人员数据VO
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@Schema(description = "未填写人员数据")
public class UnfilledUserVO {

    @Schema(description = "用户ID")
    private String userId;

    @Schema(description = "员工姓名")
    private String userName;

    @Schema(description = "分部ID")
    private String fbId;

    @Schema(description = "所属分部")
    private String fbName;

    @Schema(description = "填写负责人ID")
    private String chargeId;

    @Schema(description = "填写负责人")
    private String chargeName;

    @Schema(description = "应填写月份")
    private String month;

    @Schema(description = "逾期天数")
    private Integer overdueDays;

    @Schema(description = "最后提醒时间")
    private String lastNotifyTime;

    @Schema(description = "提醒次数")
    private Integer notifyCount;

    @Schema(description = "员工状态")
    private String userStatus;

    @Schema(description = "入职时间")
    private String joinDate;

    @Schema(description = "联系电话")
    private String phone;

    @Schema(description = "邮箱")
    private String email;
}
