package com.xinghuo.project.biz.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目采购合同付款实体类
 */
@Data
@EqualsAndHashCode
@TableName("zz_proj_paycontract_money_v2")
public class PaymentContractMoneyEntity extends BaseEntityV2.CUBaseEntityV2<PaymentContractMoneyEntity> {

    /**
     * 关联采购合同ID
     */
    @TableField("pay_contract_id")
    private String payContractId;

    /**
     * 付款条件
     */
    @TableField("fktj")
    private String fktj;

    /**
     * 比例
     */
    @TableField("ratio")
    private String ratio;

    /**
     * 付款金额
     */
    @TableField("cm_money")
    private BigDecimal cmMoney;

    /**
     * 付款负责人
     */
    @TableField("own_id")
    private String ownId;

    /**
     * 支付状态  1- 已付款， 0-未付款
     */
    @TableField("pay_status")
    private Integer payStatus;

    /**
     * 预付日期
     */
    @TableField("yufu_date")
    private Date yufuDate;

    /**
     * 付款日期
     */
    @TableField("fukuan_date")
    private Date fukuanDate;

    /**
     * 最后备注
     */
    @TableField("last_note")
    private String lastNote;

    /**
     * 备注
     */
    @TableField("note")
    private String note;

    /**
     * 部门ID
     */
    @TableField("dept_id")
    private String deptId;

       /**
     * 一部金额
     */
    @TableField("yb_amount")
    private BigDecimal ybAmount;

    /**
     * 二部金额
     */
    @TableField("eb_amount")
    private BigDecimal ebAmount;

    /**
     * 其他金额
     */
    @TableField("other_amount")
    private BigDecimal otherAmount;
}

