package com.xinghuo.project.core.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 用户行为日志实体类
 * 对应数据库表：zz_proj_behavior_log
 * 用于记录用户对项目相关对象的行为（访问、关注等）
 * 
 * <AUTHOR>
 * @date 2025-01-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("zz_proj_behavior_log")
public class ProjBehaviorLogEntity extends BaseEntityV2.IBaseEntityV2<String> {

    /**
     * 执行操作的用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 关联对象的主键ID (可以是项目ID, 项目群ID, 项目组合ID等)
     */
    @TableField("target_id")
    private String targetId;

    /**
     * 关联对象的类型 (如: PROJECT, PROGRAM, PORTFOLIO)
     */
    @TableField("target_type")
    private String targetType;

    /**
     * 行为类型 (VISIT: 访问, FAVORITE: 关注)
     */
    @TableField("behavior_type")
    private String behaviorType;

    /**
     * 最后一次行为发生的时间 (对于访问, 每次都更新; 对于关注, 是关注/取消关注的时间)
     */
    @TableField("last_action_time")
    private Date lastActionTime;

    /**
     * 记录是否有效 (1:有效, 0:无效, 用于实现"取消关注")
     */
    @TableField("is_valid")
    private Integer isValid;
}
