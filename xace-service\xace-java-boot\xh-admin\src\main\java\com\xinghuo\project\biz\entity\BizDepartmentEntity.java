package com.xinghuo.project.biz.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 业务部门配置实体
 * 用于统一管理业务部门信息，替代硬编码的部门字段
 * 
 * <AUTHOR>
 * @version V2.0
 * @copyright 易趋集团
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_department")
public class BizDepartmentEntity extends BaseEntityV2.CUDBaseEntityV2<String> {

    /**
     * 部门编码（唯一标识）
     */
    @TableField("F_DEPARTMENT_CODE")
    private String departmentCode;

    /**
     * 部门名称
     */
    @TableField("F_DEPARTMENT_NAME")
    private String departmentName;

    /**
     * 部门类型（1-内部部门 2-外采部门）
     */
    @TableField("F_DEPARTMENT_TYPE")
    private Integer departmentType;

    /**
     * 排序号
     */
    @TableField("F_SORT_CODE")
    private Integer sortCode;

    /**
     * 状态（0-禁用 1-启用）
     */
    @TableField("F_ENABLED_MARK")
    private Integer enabledMark;

    /**
     * 描述
     */
    @TableField("F_DESCRIPTION")
    private String description;

    /**
     * 上级部门ID
     */
    @TableField("F_PARENT_ID")
    private String parentId;
}