# XACE 后端开发规范

2025-07-21 确认

基于 Spring Boot 3 + MyBatis-Plus 的企业级后端开发规范。

## 🚨 核心要点

### ⚠️ 重要：Jakarta EE 规范（JDK 17+）

由于项目使用 **JDK 17+ 和 Spring Boot 3.x**，**必须使用 Jakarta EE 规范**，不能使用旧的 Java EE 规范。

**✅ 正确的导入（Jakarta EE）：**
```java
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotBlank;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.persistence.Entity;
```

**❌ 错误的导入（Java EE）：**
```java
import javax.validation.constraints.NotNull;  // 错误！会导致编译失败
import javax.servlet.http.HttpServletRequest;  // 错误！会导致编译失败
import javax.persistence.Entity;               // 错误！会导致编译失败
```

**包名对照表：**
| 功能 | 旧包名 (Java EE) | 新包名 (Jakarta EE) |
|------|------------------|---------------------|
| 参数校验 | `javax.validation.*` | `jakarta.validation.*` |
| Servlet API | `javax.servlet.*` | `jakarta.servlet.*` |
| Bean Validation | `javax.validation.constraints.*` | `jakarta.validation.constraints.*` |

### 其他核心规范
- **BaseEntityV2**：实体类继承 `BaseEntityV2.CUBaseEntityV2<String>`  
- **XHBaseMapper**：Mapper接口继承 `XHBaseMapper<T>`
- **ActionResult**：Controller统一返回 `ActionResult<T>` 格式

## 📋 规范文档

按照开发层次组织的规范文档：

### 1. [架构规范](./01_ARCHITECTURE.md)
- 技术栈选择：Java 17+, Spring Boot 3.x, Jakarta EE
- 项目结构：多模块Maven架构和包组织规范
- 包导入路径：项目内部组件正确导入方式

### 2. [编码标准](./02_CODING_STANDARDS.md)
- 命名规范：类名、方法名、变量名等命名约定
- 代码格式：缩进、换行、注释等格式规范
- 基本约定：异常处理、日志记录等通用规范

### 3. [实体层规范](./03_ENTITY_LAYER.md)
- BaseEntityV2：实体基类继承体系和使用方法
- 字段设计：数据库字段映射和类型选择规范
- 最佳实践：实体类开发的完整指南

### 4. [数据访问层](./04_DATA_ACCESS.md)
- Mapper接口：XHBaseMapper继承和方法定义
- SQL编写：MyBatis XML和注解SQL规范
- 事务处理：数据库事务管理最佳实践

### 5. [服务层规范](./05_SERVICE_LAYER.md)
- 服务设计：业务逻辑组织和接口设计
- 事务管理：@Transactional使用规范
- 异常处理：业务异常的定义和处理方式

### 6. [控制器层](./06_CONTROLLER_LAYER.md)
- REST API：RESTful接口设计和路径规范
- 参数验证：请求参数校验和错误处理
- 响应格式：ActionResult统一响应结构

### 7. [模型类规范](./07_MODEL_CLASSES.md)
- VO对象：视图对象设计和使用场景
- Form对象：表单对象设计和验证规范
- Pagination：分页查询对象规范

### 开发工具
- [AI提示词模板](../../../ai-assistants/backend/09_PROMPTS_TEMPLATES.md) - 辅助开发的AI提示词

## 🔧 开发命令

### 构建和运行
```bash
# 构建整个项目
mvn clean package -DskipTests

# 运行主应用
cd xace-service/xace-java-boot/xh-admin
mvn spring-boot:run

# 检查导入规范（项目根目录）
.\.github\scripts\check-imports.ps1
```

## ⚠️ 常见错误

1. **包导入错误**：使用 `jakarta.*` 不是 `javax.*`
2. **实体继承错误**：继承 `BaseEntityV2.CUBaseEntityV2<String>`
3. **字段映射错误**：使用 `createdAt` 不是 `createTime`
4. **Mapper继承错误**：继承 `XHBaseMapper<T>` 不是 `BaseMapper<T>`
5. **响应格式错误**：返回 `ActionResult<T>` 格式