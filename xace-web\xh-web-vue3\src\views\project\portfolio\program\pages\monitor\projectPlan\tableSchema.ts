import { BasicColumn } from '/@/components/Table';
import { Tag, Progress } from 'ant-design-vue';
import { h } from 'vue';

export function getTableSchemas(): BasicColumn[] {
  return [
    {
      title: '项目名称',
      dataIndex: 'projectName',
      width: 280,
      ellipsis: true,
    },
    {
      title: '项目编号',
      dataIndex: 'projectCode',
      width: 120,
    },
    {
      title: '项目经理',
      dataIndex: 'projectManager',
      width: 150,
      ellipsis: true,
    },
    {
      title: '所属部门',
      dataIndex: 'department',
      width: 150,
      ellipsis: true,
    },
    {
      title: '计划状态',
      dataIndex: 'planStatus',
      key: 'planStatus',
      width: 100,
      customRender: ({ record }) => {
        const colorMap = {
          按计划: 'success',
          轻微延期: 'warning',
          严重延期: 'error',
          提前完成: 'processing',
        };
        return h(Tag, { color: colorMap[record.planStatus] || 'default' }, () => record.planStatus);
      },
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      width: 100,
      customRender: ({ record }) => {
        return h(Progress, {
          percent: record.progress,
          size: 'small',
          status: record.progress === 100 ? 'success' : record.planStatus === '严重延期' ? 'exception' : 'active',
        });
      },
    },
    {
      title: '计划开始日期',
      dataIndex: 'plannedStartDate',
      width: 120,
    },
    {
      title: '计划结束日期',
      dataIndex: 'plannedEndDate',
      width: 120,
    },
    {
      title: '实际开始日期',
      dataIndex: 'actualStartDate',
      width: 120,
      customRender: ({ record }) => {
        return record.actualStartDate || '未开始';
      },
    },
    {
      title: '预计结束日期',
      dataIndex: 'estimatedEndDate',
      width: 120,
    },
    {
      title: '偏差天数',
      dataIndex: 'daysDeviation',
      width: 100,
      customRender: ({ record }) => {
        const days = record.daysDeviation;
        const color = days === 0 ? '#666' : days > 0 ? '#f5222d' : '#52c41a';
        const prefix = days > 0 ? '+' : '';
        return h('span', { style: { color } }, `${prefix}${days}天`);
      },
    },
    {
      title: '里程碑进度',
      dataIndex: 'milestoneProgress',
      width: 120,
      customRender: ({ record }) => {
        const completedRate = ((record.completedMilestones / record.milestoneCount) * 100).toFixed(0);
        return h('div', [
          h('div', `${record.completedMilestones}/${record.milestoneCount}`),
          h('div', { style: { fontSize: '12px', color: '#666' } }, `${completedRate}%`),
        ]);
      },
    },
    {
      title: '预算使用率(%)',
      dataIndex: 'budgetUsageRate',
      width: 120,
      customRender: ({ record }) => {
        const rate = record.budgetUsageRate;
        const color = rate > 90 ? '#f5222d' : rate > 75 ? '#faad14' : '#52c41a';
        return h('span', { style: { color } }, `${rate}%`);
      },
    },
    {
      title: '风险等级',
      dataIndex: 'riskLevel',
      width: 100,
      customRender: ({ record }) => {
        const colorMap = {
          低: 'success',
          中: 'warning',
          高: 'error',
        };
        return h(Tag, { color: colorMap[record.riskLevel] || 'default' }, () => record.riskLevel);
      },
    },
  ];
}
