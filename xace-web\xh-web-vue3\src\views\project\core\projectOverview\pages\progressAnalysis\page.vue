<template>
  <div class="progress-analysis p-4">
    <!-- 页面标题 -->
    <div class="page-header mb-4">
      <h2 class="text-xl font-semibold mb-2">进度分析</h2>
      <p class="text-gray-600">深入分析项目进度，识别瓶颈和优化机会</p>
    </div>

    <!-- 筛选工具栏 -->
    <div class="filter-toolbar bg-white rounded-lg shadow-sm border p-4 mb-6">
      <div class="flex flex-wrap items-center gap-4">
        <div class="filter-item">
          <label class="text-sm text-gray-600 mr-2">时间范围:</label>
          <a-range-picker v-model:value="dateRange" @change="onDateRangeChange" />
        </div>

        <div class="filter-item">
          <label class="text-sm text-gray-600 mr-2">分析维度:</label>
          <a-select v-model:value="analysisDimension" style="width: 150px" @change="onDimensionChange">
            <a-select-option value="overall">整体进度</a-select-option>
            <a-select-option value="module">模块进度</a-select-option>
            <a-select-option value="member">成员进度</a-select-option>
            <a-select-option value="milestone">里程碑进度</a-select-option>
          </a-select>
        </div>

        <div class="filter-item">
          <a-button type="primary" @click="refreshAnalysis" :loading="loading">
            <template #icon><ReloadOutlined /></template>
            刷新分析
          </a-button>
        </div>
      </div>
    </div>

    <!-- 进度概览卡片 -->
    <div class="progress-overview grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
      <div class="overview-card bg-white rounded-lg shadow-sm border p-4">
        <div class="card-header mb-3">
          <h3 class="text-base font-medium text-gray-800">整体进度</h3>
        </div>
        <div class="progress-circle-container text-center">
          <a-progress type="circle" :percent="overallProgress.percent" :width="80" :stroke-color="getProgressColor(overallProgress.percent)" />
          <div class="mt-3">
            <p class="text-sm text-gray-600">预期进度: {{ overallProgress.expected }}%</p>
            <p
              class="text-xs"
              :class="overallProgress.status === 'ahead' ? 'text-green-600' : overallProgress.status === 'behind' ? 'text-red-600' : 'text-blue-600'">
              {{ getProgressStatusText(overallProgress.status) }}
            </p>
          </div>
        </div>
      </div>

      <div class="overview-card bg-white rounded-lg shadow-sm border p-4">
        <div class="card-header mb-3">
          <h3 class="text-base font-medium text-gray-800">任务完成率</h3>
        </div>
        <div class="completion-stats">
          <div class="stat-item flex justify-between items-center mb-2">
            <span class="text-sm text-gray-600">已完成</span>
            <span class="text-sm font-medium text-green-600">{{ taskStats.completed }}</span>
          </div>
          <div class="stat-item flex justify-between items-center mb-2">
            <span class="text-sm text-gray-600">进行中</span>
            <span class="text-sm font-medium text-blue-600">{{ taskStats.inProgress }}</span>
          </div>
          <div class="stat-item flex justify-between items-center mb-2">
            <span class="text-sm text-gray-600">待开始</span>
            <span class="text-sm font-medium text-gray-600">{{ taskStats.pending }}</span>
          </div>
          <div class="stat-item flex justify-between items-center">
            <span class="text-sm text-gray-600">已延期</span>
            <span class="text-sm font-medium text-red-600">{{ taskStats.overdue }}</span>
          </div>
        </div>
      </div>

      <div class="overview-card bg-white rounded-lg shadow-sm border p-4">
        <div class="card-header mb-3">
          <h3 class="text-base font-medium text-gray-800">关键指标</h3>
        </div>
        <div class="key-metrics">
          <div class="metric-item mb-3">
            <div class="flex justify-between items-center mb-1">
              <span class="text-sm text-gray-600">平均完成速度</span>
              <span class="text-sm font-medium">{{ keyMetrics.avgCompletionRate }}/天</span>
            </div>
            <a-progress :percent="75" :show-info="false" size="small" />
          </div>

          <div class="metric-item mb-3">
            <div class="flex justify-between items-center mb-1">
              <span class="text-sm text-gray-600">质量评分</span>
              <span class="text-sm font-medium">{{ keyMetrics.qualityScore }}/100</span>
            </div>
            <a-progress :percent="keyMetrics.qualityScore" :show-info="false" size="small" stroke-color="#52c41a" />
          </div>

          <div class="metric-item">
            <div class="flex justify-between items-center mb-1">
              <span class="text-sm text-gray-600">团队效率</span>
              <span class="text-sm font-medium">{{ keyMetrics.teamEfficiency }}%</span>
            </div>
            <a-progress :percent="keyMetrics.teamEfficiency" :show-info="false" size="small" stroke-color="#722ed1" />
          </div>
        </div>
      </div>
    </div>

    <!-- 详细分析图表 -->
    <div class="analysis-charts grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
      <!-- 进度趋势分析 -->
      <div class="chart-card bg-white rounded-lg shadow-sm border p-4">
        <div class="chart-header mb-4">
          <h3 class="text-lg font-medium">进度趋势分析</h3>
          <p class="text-sm text-gray-500">实际进度与计划进度对比</p>
        </div>
        <div class="chart-content" style="height: 300px">
          <div class="mock-trend-chart bg-gradient-to-br from-blue-50 to-indigo-100 h-full rounded flex items-center justify-center">
            <div class="text-center">
              <LineChartOutlined class="text-4xl text-blue-400 mb-2" />
              <p class="text-gray-600">进度趋势图</p>
              <div class="mt-4 text-sm text-gray-500">
                <div class="flex items-center justify-center mb-1">
                  <div class="w-3 h-0.5 bg-blue-500 mr-2"></div>
                  <span>实际进度</span>
                </div>
                <div class="flex items-center justify-center">
                  <div class="w-3 h-0.5 bg-gray-400 border-dashed mr-2"></div>
                  <span>计划进度</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 模块进度分析 -->
      <div class="chart-card bg-white rounded-lg shadow-sm border p-4">
        <div class="chart-header mb-4">
          <h3 class="text-lg font-medium">模块进度分析</h3>
          <p class="text-sm text-gray-500">各功能模块的完成情况</p>
        </div>
        <div class="chart-content" style="height: 300px">
          <div class="module-progress-list">
            <div v-for="(module, index) in moduleProgress" :key="index" class="module-item mb-4">
              <div class="flex justify-between items-center mb-2">
                <span class="text-sm font-medium">{{ module.name }}</span>
                <span class="text-sm text-gray-600">{{ module.progress }}%</span>
              </div>
              <a-progress :percent="module.progress" :show-info="false" :stroke-color="getModuleProgressColor(module.progress, module.expected)" />
              <div class="flex justify-between items-center mt-1">
                <span class="text-xs text-gray-500">预期: {{ module.expected }}%</span>
                <span class="text-xs" :class="getModuleStatusClass(module.progress, module.expected)">
                  {{ getModuleStatusText(module.progress, module.expected) }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 瓶颈分析和建议 -->
    <div class="bottleneck-analysis grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 瓶颈识别 -->
      <div class="bottleneck-card bg-white rounded-lg shadow-sm border p-4">
        <div class="card-header mb-4">
          <h3 class="text-lg font-medium">瓶颈识别</h3>
          <p class="text-sm text-gray-500">影响项目进度的关键因素</p>
        </div>
        <div class="bottleneck-list">
          <div v-for="(bottleneck, index) in bottlenecks" :key="index" class="bottleneck-item mb-4 last:mb-0">
            <div class="flex items-start">
              <div class="bottleneck-severity w-3 h-3 rounded-full mr-3 mt-1" :class="getSeverityClass(bottleneck.severity)"></div>
              <div class="bottleneck-content flex-1">
                <h4 class="text-sm font-medium mb-1">{{ bottleneck.title }}</h4>
                <p class="text-xs text-gray-600 mb-2">{{ bottleneck.description }}</p>
                <div class="flex items-center">
                  <span class="text-xs px-2 py-1 rounded" :class="getSeverityBadgeClass(bottleneck.severity)">
                    {{ bottleneck.severity }}
                  </span>
                  <span class="text-xs text-gray-500 ml-2">影响: {{ bottleneck.impact }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 优化建议 -->
      <div class="suggestions-card bg-white rounded-lg shadow-sm border p-4">
        <div class="card-header mb-4">
          <h3 class="text-lg font-medium">优化建议</h3>
          <p class="text-sm text-gray-500">提升项目进度的改进措施</p>
        </div>
        <div class="suggestions-list">
          <div v-for="(suggestion, index) in suggestions" :key="index" class="suggestion-item mb-4 last:mb-0">
            <div class="flex items-start">
              <div class="suggestion-priority w-3 h-3 rounded-full mr-3 mt-1" :class="getPriorityClass(suggestion.priority)"></div>
              <div class="suggestion-content flex-1">
                <h4 class="text-sm font-medium mb-1">{{ suggestion.title }}</h4>
                <p class="text-xs text-gray-600 mb-2">{{ suggestion.description }}</p>
                <div class="flex items-center">
                  <span class="text-xs px-2 py-1 rounded" :class="getPriorityBadgeClass(suggestion.priority)">
                    {{ suggestion.priority }}
                  </span>
                  <span class="text-xs text-gray-500 ml-2">预期效果: {{ suggestion.expectedEffect }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { RangePicker, Select, Button, Progress } from 'ant-design-vue';
  import { ReloadOutlined, LineChartOutlined } from '@ant-design/icons-vue';
  import { getProgressAnalysis } from '../../api';

  const props = defineProps({
    projectId: {
      type: String,
      default: '',
    },
  });

  const loading = ref(false);
  const dateRange = ref([]);
  const analysisDimension = ref('overall');

  // 整体进度数据
  const overallProgress = ref({
    percent: 68,
    expected: 75,
    status: 'behind', // ahead, onTrack, behind
  });

  // 任务统计数据
  const taskStats = ref({
    completed: 45,
    inProgress: 18,
    pending: 7,
    overdue: 2,
  });

  // 关键指标数据
  const keyMetrics = ref({
    avgCompletionRate: 3.2,
    qualityScore: 85,
    teamEfficiency: 78,
  });

  // 模块进度数据
  const moduleProgress = ref([
    { name: '用户管理模块', progress: 85, expected: 80 },
    { name: '权限控制模块', progress: 72, expected: 75 },
    { name: '数据分析模块', progress: 45, expected: 60 },
    { name: '报表生成模块', progress: 30, expected: 40 },
    { name: '系统配置模块', progress: 90, expected: 85 },
  ]);

  // 瓶颈数据
  const bottlenecks = ref([
    {
      title: '第三方API集成延迟',
      description: '外部支付接口响应时间过长，影响测试进度',
      severity: '高',
      impact: '延期3天',
    },
    {
      title: '数据库性能问题',
      description: '大数据量查询响应缓慢，需要优化索引',
      severity: '中',
      impact: '影响用户体验',
    },
    {
      title: '团队成员技能差异',
      description: '部分成员对新技术栈不熟悉，学习成本较高',
      severity: '中',
      impact: '开发效率降低',
    },
  ]);

  // 优化建议数据
  const suggestions = ref([
    {
      title: '增加API缓存机制',
      description: '对频繁调用的第三方API增加缓存，减少响应时间',
      priority: '高',
      expectedEffect: '提升30%响应速度',
    },
    {
      title: '数据库查询优化',
      description: '优化慢查询SQL，添加必要的索引',
      priority: '高',
      expectedEffect: '查询速度提升50%',
    },
    {
      title: '技术培训计划',
      description: '组织内部技术分享，提升团队整体技能水平',
      priority: '中',
      expectedEffect: '团队效率提升20%',
    },
    {
      title: '代码审查流程优化',
      description: '建立更高效的代码审查机制，提升代码质量',
      priority: '中',
      expectedEffect: '减少bug数量',
    },
  ]);

  onMounted(async () => {
    await loadProgressAnalysis();
  });

  const loadProgressAnalysis = async () => {
    loading.value = true;
    try {
      // 这里可以调用实际的API获取数据
      // const result = await getProgressAnalysis({
      //   projectId: props.projectId,
      //   dimension: analysisDimension.value,
      //   dateRange: dateRange.value
      // });

      console.log('进度分析数据加载完成');
    } catch (error) {
      console.error('加载进度分析数据失败:', error);
    } finally {
      loading.value = false;
    }
  };

  const onDateRangeChange = dates => {
    dateRange.value = dates;
    loadProgressAnalysis();
  };

  const onDimensionChange = value => {
    analysisDimension.value = value;
    loadProgressAnalysis();
  };

  const refreshAnalysis = () => {
    loadProgressAnalysis();
  };

  const getProgressColor = (percent: number) => {
    if (percent >= 80) return '#52c41a';
    if (percent >= 60) return '#1890ff';
    if (percent >= 40) return '#faad14';
    return '#ff4d4f';
  };

  const getProgressStatusText = (status: string) => {
    const texts = {
      ahead: '进度超前',
      onTrack: '进度正常',
      behind: '进度滞后',
    };
    return texts[status] || '状态未知';
  };

  const getModuleProgressColor = (actual: number, expected: number) => {
    if (actual >= expected) return '#52c41a';
    if (actual >= expected * 0.8) return '#1890ff';
    return '#ff4d4f';
  };

  const getModuleStatusClass = (actual: number, expected: number) => {
    if (actual >= expected) return 'text-green-600';
    if (actual >= expected * 0.8) return 'text-blue-600';
    return 'text-red-600';
  };

  const getModuleStatusText = (actual: number, expected: number) => {
    if (actual >= expected) return '超前';
    if (actual >= expected * 0.8) return '正常';
    return '滞后';
  };

  const getSeverityClass = (severity: string) => {
    const classes = {
      高: 'bg-red-500',
      中: 'bg-yellow-500',
      低: 'bg-green-500',
    };
    return classes[severity] || 'bg-gray-500';
  };

  const getSeverityBadgeClass = (severity: string) => {
    const classes = {
      高: 'bg-red-100 text-red-800',
      中: 'bg-yellow-100 text-yellow-800',
      低: 'bg-green-100 text-green-800',
    };
    return classes[severity] || 'bg-gray-100 text-gray-800';
  };

  const getPriorityClass = (priority: string) => {
    const classes = {
      高: 'bg-red-500',
      中: 'bg-blue-500',
      低: 'bg-gray-500',
    };
    return classes[priority] || 'bg-gray-500';
  };

  const getPriorityBadgeClass = (priority: string) => {
    const classes = {
      高: 'bg-red-100 text-red-800',
      中: 'bg-blue-100 text-blue-800',
      低: 'bg-gray-100 text-gray-800',
    };
    return classes[priority] || 'bg-gray-100 text-gray-800';
  };
</script>
