<template>
  <a-modal v-model:visible="modalVisible" title="更新合同日期" :confirm-loading="loading" :mask-closable="false" @ok="handleOk" @cancel="handleCancel">
    <div class="date-update-modal">
      <a-form ref="formRef" :model="formData" :rules="formRules" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <a-form-item label="合同名称">
          <span class="font-medium">{{ contractData?.name || '-' }}</span>
        </a-form-item>

        <a-form-item label="合同编号">
          <a-tag color="blue">{{ contractData?.cno || '-' }}</a-tag>
        </a-form-item>

        <a-form-item label="日期类型" name="dateType" required>
          <a-select v-model:value="formData.dateType" placeholder="请选择要更新的日期类型" @change="handleDateTypeChange">
            <a-select-option value="signDate">签订日期</a-select-option>
            <a-select-option value="bidDate">招标日期</a-select-option>
            <a-select-option value="commencementDate">开工日期</a-select-option>
            <a-select-option value="initialCheckDate">初检日期</a-select-option>
            <a-select-option value="finalCheckDate">终检日期</a-select-option>
            <a-select-option value="auditDate">审计日期</a-select-option>
            <a-select-option value="cstartDate">合同开始日期</a-select-option>
            <a-select-option value="cendDate">合同结束日期</a-select-option>
            <a-select-option value="mstartDate">监理开始日期</a-select-option>
            <a-select-option value="mendDate">监理结束日期</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item v-if="formData.dateType" label="当前日期">
          <span class="text-gray-600">
            {{ getCurrentDateText() }}
          </span>
        </a-form-item>

        <a-form-item label="新日期" name="newDate" required>
          <a-date-picker v-model:value="formData.newDate" placeholder="请选择新日期" style="width: 100%" format="YYYY-MM-DD" />
        </a-form-item>

        <a-form-item label="备注" name="note">
          <a-textarea v-model:value="formData.note" placeholder="请输入日期变更备注" :rows="3" :maxlength="200" show-count />
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed, watch } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import type { FormInstance } from 'ant-design-vue';
  import type { ContractVO } from '/@/api/project/contract';
  import { formatToDate } from '/@/utils/dateUtil';
  import dayjs, { type Dayjs } from 'dayjs';

  // Props & Emits
  const props = defineProps<{
    visible: boolean;
    contractData: ContractVO | null;
  }>();

  const emit = defineEmits<{
    'update:visible': [value: boolean];
    ok: [data: { dateType: string; newDate: string; note?: string }];
    cancel: [];
  }>();

  const { createMessage } = useMessage();

  // Reactive state
  const formRef = ref<FormInstance>();
  const loading = ref(false);

  const modalVisible = computed({
    get: () => props.visible,
    set: value => emit('update:visible', value),
  });

  // Form data
  const formData = reactive({
    dateType: '',
    newDate: null as Dayjs | null,
    note: '',
  });

  // Form validation rules
  const formRules = {
    dateType: [{ required: true, message: '请选择日期类型', trigger: 'change' }],
    newDate: [{ required: true, message: '请选择新日期', trigger: 'change' }],
  };

  // Date type mapping
  const dateTypeMap = {
    signDate: '签订日期',
    bidDate: '招标日期',
    commencementDate: '开工日期',
    initialCheckDate: '初检日期',
    finalCheckDate: '终检日期',
    auditDate: '审计日期',
    cstartDate: '合同开始日期',
    cendDate: '合同结束日期',
    mstartDate: '监理开始日期',
    mendDate: '监理结束日期',
  };

  // Methods
  const getCurrentDateText = () => {
    if (!formData.dateType || !props.contractData) return '-';

    const currentDate = props.contractData[formData.dateType as keyof ContractVO];
    return currentDate ? formatToDate(currentDate as string) : '未设置';
  };

  // Watch for props changes
  watch(
    () => props.visible,
    visible => {
      if (visible) {
        formData.dateType = '';
        formData.newDate = null;
        formData.note = '';
      }
    },
  );

  // Event handlers
  const handleDateTypeChange = (value: string) => {
    // 清空日期选择
    formData.newDate = null;

    // 可以在这里添加一些日期类型相关的逻辑，比如设置默认日期
    if (value === 'signDate' && !props.contractData?.signDate) {
      formData.newDate = dayjs(); // 设置为今天
    }
  };

  const handleOk = async () => {
    try {
      await formRef.value?.validate();

      if (!formData.newDate) {
        createMessage.error('请选择新日期');
        return;
      }

      // 检查日期是否有实际变更
      const currentDate = props.contractData?.[formData.dateType as keyof ContractVO] as string;
      const newDateStr = formData.newDate.format('YYYY-MM-DD');

      if (currentDate && formatToDate(currentDate) === newDateStr) {
        createMessage.warning('新日期与当前日期相同');
        return;
      }

      loading.value = true;
      emit('ok', {
        dateType: formData.dateType,
        newDate: newDateStr,
        note: formData.note || undefined,
      });
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const handleCancel = () => {
    formRef.value?.resetFields();
    emit('cancel');
  };
</script>

<style lang="less" scoped>
  .date-update-modal {
    .ant-form-item {
      margin-bottom: 16px;
    }

    .font-medium {
      font-weight: 500;
    }

    .text-gray-600 {
      color: #666;
    }

    // 标签样式
    :deep(.ant-tag) {
      border-radius: 4px;
      font-size: 12px;
    }

    // 选择器样式
    .ant-select {
      &.ant-select-focused {
        .ant-select-selector {
          border-color: #40a9ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
      }
    }

    // 日期选择器样式
    .ant-picker {
      &.ant-picker-focused {
        border-color: #40a9ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }
    }
  }
</style>
