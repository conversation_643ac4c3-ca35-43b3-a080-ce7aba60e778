/**
 * 批量更新项目页面的辅助脚本
 * 用于帮助开发者快速为现有页面添加项目切换响应功能
 */

// 使用说明和示例代码

export const migrationGuide = {
  // 1. 对于完全没有项目ID处理的页面
  noProjectIdPages: `
// 在 <script setup> 中添加:
import { useProjectContext } from '/@/hooks/web/useProjectContext';

// 使用Hook
const { projectId } = useProjectContext({
  onProjectChange: async (newProjectId) => {
    console.log('🔄 [页面名称] 项目切换，重新加载数据:', newProjectId);
    await loadPageData(); // 替换为页面的数据加载函数
  }
});

// 确保有数据加载函数
const loadPageData = async () => {
  if (!projectId.value) {
    console.warn('⚠️ [页面名称] 项目ID为空，跳过数据加载');
    return;
  }
  
  loading.value = true;
  try {
    // TODO: 根据项目ID加载页面数据
    // const response = await getPageData(projectId.value);
    console.log('✅ [页面名称] 数据加载完成');
  } catch (error) {
    console.error('❌ [页面名称] 数据加载失败:', error);
  } finally {
    loading.value = false;
  }
};
  `,

  // 2. 对于使用inject方式的页面
  injectBasedPages: `
// 替换现有的inject代码:
// const projectId = inject('projectId', ref(''));

// 改为使用Hook:
import { useProjectContext } from '/@/hooks/web/useProjectContext';

const { projectId } = useProjectContext({
  onProjectChange: async (newProjectId, oldProjectId) => {
    console.log('🔄 [页面名称] 项目切换:', { 旧项目: oldProjectId, 新项目: newProjectId });
    // 添加项目切换时的逻辑
    await loadPageData();
  }
});
  `,

  // 3. 对于使用props方式的页面
  propsBasedPages: `
// 保留现有props定义，在hook中处理fallback:
const props = defineProps({
  projectId: {
    type: String,
    default: '',
  },
});

// 使用Hook作为fallback
import { useProjectContext } from '/@/hooks/web/useProjectContext';

const { projectId: contextProjectId } = useProjectContext({
  onProjectChange: async (newProjectId) => {
    console.log('🔄 [页面名称] 项目切换:', newProjectId);
    // 如果没有props.projectId，则使用context中的项目ID
    if (!props.projectId) {
      projectId.value = newProjectId;
      await loadPageData();
    }
  }
});

// 优先使用props，fallback到context
const projectId = ref(props.projectId || contextProjectId.value);
  `,

  // 4. 需要特殊处理的合同管理类页面
  contractManagementPages: `
// 合同管理页面需要同时处理项目ID和合同ID
import { useProjectContext } from '/@/hooks/web/useProjectContext';

const { projectId } = useProjectContext({
  onProjectChange: (newProjectId, oldProjectId) => {
    console.log('🔄 [合同管理] 项目切换:', { 旧项目: oldProjectId, 新项目: newProjectId });
    // 项目切换时清空编辑状态
    editMode.value = false;
    // 合同数据会通过父组件重新注入
  }
});

// 继续使用inject获取合同相关数据
const contractId = inject('contractId', ref(''));
const contractInfo = inject('contractInfo', ref(null));
const hasContract = inject('hasContract', ref(false));
  `,
};

// 页面类型检测函数
export function detectPageType(filePath: string): keyof typeof migrationGuide {
  if (filePath.includes('contractManagement')) {
    return 'contractManagementPages';
  }
  // 可以添加更多检测逻辑
  return 'noProjectIdPages';
}

// 自动化迁移建议
export const automationTips = `
推荐的迁移步骤：

1. 批量搜索和替换：
   - 搜索: inject('projectId'
   - 替换为使用 useProjectContext Hook

2. 添加项目切换回调：
   - 为每个页面添加 onProjectChange 回调
   - 在回调中重新加载页面数据

3. 统一日志格式：
   - 使用 console.log('🔄 [页面名称] 项目切换:', newProjectId)
   - 使用 console.warn('⚠️ [页面名称] 项目ID为空')

4. 测试验证：
   - 确保项目切换时页面数据正确更新
   - 检查控制台日志是否正常输出
`;
