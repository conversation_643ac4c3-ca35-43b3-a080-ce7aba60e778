# 实体层开发规范

2025-07-21 已确认

## 概述

实体层是对象关系映射(ORM)的基础，代表数据库表结构在应用程序中的映射。XACE项目使用 BaseEntityV2 作为统一的实体基类，提供完整的系统字段和多租户支持。

### 核心职责
- 映射数据库表结构
- 提供属性访问方法
- 支持ORM框架特性（关系映射、懒加载等）
- 不包含业务逻辑

## BaseEntityV2 基类体系

XACE项目统一使用BaseEntityV2作为实体基类（现有代码中的AbstractBaseEntity将逐步迁移）。

### 继承体系
```
BaseEntityV2
├── IBaseEntityV2<T>        # 只包含 ID
├── TBaseEntityV2<T>        # ID + 租户ID
├── CBaseEntityV2<T>        # ID + 租户ID + 创建信息
├── CUBaseEntityV2<T>       # ID + 租户ID + 创建/更新信息 (推荐)
└── CUDBaseEntityV2<T>      # ID + 租户ID + 创建/更新/删除信息 (需要逻辑删除时)
```

### 字段映射

| 字段层级 | Java 字段名 | 数据库字段名 | 类型 | 包含该字段的基类 |
|----------|------------|-------------|------|----------------|
| **基础字段** | id | F_ID | String | 所有基类 |
| | tenantId | F_TenantId | String | TBaseEntityV2+ |
| **创建字段** | createdAt | f_created_at | Date | CBaseEntityV2+ |
| | createdBy | f_created_by | String | CBaseEntityV2+ |
| **更新字段** | lastUpdatedAt | f_last_updated_at | Date | CUBaseEntityV2+ |
| | lastUpdatedBy | f_last_updated_by | String | CUBaseEntityV2+ |
| **删除字段** | deleteMark | F_DeleteMark | Integer | CUDBaseEntityV2 |
| | deletedAt | f_deleted_at | Date | CUDBaseEntityV2 |
| | deletedBy | f_deleted_by | String | CUDBaseEntityV2 |

> **说明**：`+` 表示该基类及其子类都包含此字段

### 字段命名约定

**系统字段（F_前缀）**：
- 由框架基类自动提供，使用 `F_` 前缀
- 如：`F_ID`, `F_CREATED_AT`, `F_CREATED_BY`
- 开发者无需手动定义，继承基类即可

**业务字段（无前缀）**：
- 业务相关字段使用大写下划线命名
- 如：`FULLNAME`, `ACCOUNT`, `EMAIL`, `MOBILE`
- 开发者需要明确使用 `@TableField` 注解指定映射关系

### 基类选择指南

| 基类 | 适用场景 | 包含字段 |
|------|----------|----------|
| `BaseEntityV2.CUBaseEntityV2<String>` | **大部分业务实体（推荐）** | id, tenantId, createdAt, createdBy, lastUpdatedAt, lastUpdatedBy |
| `BaseEntityV2.CUDBaseEntityV2<String>` | 需要逻辑删除的实体 | 以上字段 + deleteMark, deletedAt, deletedBy |
| `BaseEntityV2.TBaseEntityV2<String>` | 简单配置表、字典表 | id, tenantId |
| `BaseEntityV2.CBaseEntityV2<String>` | 只需要创建信息的实体 | id, tenantId, createdAt, createdBy |

## 基本规范

### 包路径和命名
```java
// 包路径
com.xinghuo.[模块名].entity

// 类命名
[业务名]Entity  // 如：UserEntity, OrderEntity
```

### 必需注解
```java
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.xinghuo.common.base.entity.BaseEntityV2;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_user")
public class UserEntity extends BaseEntityV2.CUBaseEntityV2<String> {
    // 业务字段...
}
```

### 注解说明
| 注解 | 作用 | 必要性 |
|------|------|--------|
| `@Data` | 自动生成getter/setter等方法 | 必须 |
| `@EqualsAndHashCode(callSuper = true)` | 继承时包含父类字段比较 | 必须 |
| `@TableName` | 指定数据库表名 | 必须 |
| `@TableField` | 指定字段映射 | 必须 |

## 字段设计规范

### 命名规范
```java
// Java字段：驼峰命名
private String fullName;

// 系统字段：F_前缀 + 大写下划线（由基类提供，无需手动定义）
// F_ID, F_CREATED_AT, F_CREATED_BY 等系统字段由基类自动处理

// 业务字段：大写下划线命名
@TableField("FULLNAME")
private String fullName;
```

### 类型选择指南
| 数据用途 | Java类型 | 数据库类型 | 说明 |
|---------|---------|-----------|------|
| ID/编码 | String | varchar | 主键统一使用String |
| 布尔值 | Integer | int | 0/1表示false/true |
| 日期时间 | Date | datetime | 统一使用Date类型 |
| 金额 | BigDecimal | decimal | 避免精度丢失 |
| 文本 | String | varchar/text | 根据长度选择 |
| 枚举值 | Integer | int | 使用数字代码 |

### 字段注释规范
```java
/**
 * 用户姓名
 */
@TableField("FULLNAME")
private String fullName;

/**
 * 用户状态（0=禁用，1=启用）
 */
@TableField("ENABLED_MARK")
private Integer enabledMark;
```

## 完整示例

### 标准实体示例
```java
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;

/**
 * 用户实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_user")
public class UserEntity extends BaseEntityV2.CUBaseEntityV2<String> {
    
    /**
     * 用户账号
     */
    @TableField("ACCOUNT")
    private String account;
    
    /**
     * 用户姓名
     */
    @TableField("FULLNAME")
    private String fullName;
    
    /**
     * 用户状态（0=禁用，1=启用）
     */
    @TableField("ENABLED_MARK")
    private Integer enabledMark;
    
    // 系统字段由基类提供：
    // id, tenantId, createdAt, createdBy, lastUpdatedAt, lastUpdatedBy
}
```

### 业务实体示例
```java
/**
 * 订单实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_order")
public class OrderEntity extends BaseEntityV2.CUBaseEntityV2<String> {
    
    /**
     * 订单编号
     */
    @TableField("ORDER_NO")
    private String orderNo;
    
    /**
     * 客户ID
     */
    @TableField("CUSTOMER_ID")
    private String customerId;
    
    /**
     * 订单金额
     */
    @TableField("TOTAL_AMOUNT")
    private BigDecimal totalAmount;
    
    /**
     * 订单状态（1=待付款，2=已付款，3=已发货，4=已完成）
     */
    @TableField("ORDER_STATUS")
    private Integer orderStatus;
}
```

## 开发使用指南

### 系统字段自动处理
```java
// ✅ 正确：系统字段由框架自动填充，无需手动设置
OrderEntity order = new OrderEntity();
order.setOrderNo("ORD001");
order.setCustomerId("CUST001");
// createdAt, createdBy, lastUpdatedAt, lastUpdatedBy 会在保存时自动填充

// ❌ 错误：不要手动设置系统字段
order.setCreatedAt(new Date());  // 错误！
order.setCreatedBy(userId);      // 错误！
```

### 查询条件构造
```java
// ✅ 正确：使用正确的字段名进行查询
LambdaQueryWrapper<OrderEntity> lambda = new LambdaQueryWrapper<>();
lambda.ge(OrderEntity::getCreatedAt, startDate);        // 正确
lambda.le(OrderEntity::getLastUpdatedAt, endDate);      // 正确
lambda.eq(OrderEntity::getDeleteMark, 0);               // 正确：查询未删除记录

// ❌ 错误：使用不存在的字段名
lambda.ge(OrderEntity::getCreateTime, startDate);       // 错误！字段名不对
lambda.le(OrderEntity::getUpdateTime, endDate);         // 错误！字段名不对
lambda.isNull(OrderEntity::getDeleteMark);              // 错误！应该用eq(0)
```

### 逻辑删除操作
```java
@Service
public class OrderServiceImpl extends BaseServiceImpl<OrderMapper, OrderEntity> {
    
    @Override
    @Transactional
    public void delete(String id) {
        OrderEntity entity = this.getById(id);
        if (entity != null) {
            // 逻辑删除：设置 deleteMark = 1
            entity.setDeleteMark(1);
            this.updateById(entity);
        }
    }
    
    // 查询未删除的记录
    public List<OrderEntity> listActive() {
        LambdaQueryWrapper<OrderEntity> lambda = new LambdaQueryWrapper<>();
        lambda.eq(OrderEntity::getDeleteMark, 0);
        return this.list(lambda);
    }
}
```

## 最佳实践

### 核心原则
1. **继承推荐基类**：优先使用 `CUBaseEntityV2<String>`
2. **系统字段自动化**：不手动设置createdAt、updatedAt等系统字段
3. **显式字段映射**：所有字段都使用@TableField明确指定数据库列名
4. **统一命名规范**：Java驼峰，数据库大写下划线，系统字段F_前缀
5. **类型选择合理**：根据业务需求选择合适的Java和数据库类型

### 常见错误避免
```java
// ❌ 错误：使用不存在的基类或字段名
public class UserEntity extends AbstractCUDBaseEntity {              // 错误的基类
public class UserEntity extends BaseEntity {                         // 错误的基类

// ❌ 错误：使用错误的字段名
lambda.ge(UserEntity::getCreateTime, startDate);                     // 不存在此字段
lambda.ge(UserEntity::getUpdateTime, endDate);                       // 不存在此字段
lambda.ge(UserEntity::getCreatorTime, startDate);                    // BaseEntityV2中不存在

// ✅ 正确：使用BaseEntityV2
public class UserEntity extends BaseEntityV2.CUBaseEntityV2<String> {
lambda.ge(UserEntity::getCreatedAt, startDate);                      // 正确
lambda.ge(UserEntity::getLastUpdatedAt, endDate);                    // 正确
```

### 开发检查清单

- [ ] 继承了正确的基类（推荐 `BaseEntityV2.CUBaseEntityV2<String>`）
- [ ] 使用了 `@Data` 和 `@EqualsAndHashCode(callSuper = true)`
- [ ] 明确指定了 `@TableName`
- [ ] 所有业务字段都有 `@TableField` 注解
- [ ] 字段命名遵循规范（Java驼峰，数据库大写下划线，系统字段F_前缀）
- [ ] 每个字段都有清晰的注释
- [ ] 字段类型选择合理
- [ ] 没有手动定义系统字段（createdAt、lastUpdatedAt等）
- [ ] 查询时使用正确的字段名（getCreatedAt而不是getCreateTime）
- [ ] 逻辑删除查询使用 eq(deleteMark, 0) 而不是 isNull