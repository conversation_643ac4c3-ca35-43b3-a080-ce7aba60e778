<template>
  <div class="xh-content-wrapper xh-basic-bg">
    <div class="xh-content-wrapper-center">
      <div class="xh-content-wrapper-content">
        <ScrollContainer>
          <div class="pl-2 pr-2 mt-2">
            <a-alert type="warning">
              <template #description>
                <p class="font-bold">
                  <i class="icon-ym icon-ym-sysNotice mr-1"></i>
                  <span>{{ reviewMonthAndStatusRef }}</span>
                </p>
                <p v-for="(t, i) in noPlatScoreRef" :key="i">
                  {{ t.user_name }}部门可用剩余分：<span :class="t.score > 0 ? 'text-green-500' : 'text-red-500'">{{ t.score }} </span>分
                </p>
              </template>
            </a-alert>

            <!-- 员工名单差异提示 -->
            <a-alert v-if="showUserDifferenceRef" type="error" class="mt-2">
              <template #description>
                <div class="font-bold mb-2">
                  <i class="icon-ym icon-ym-warning mr-1"></i>
                  <span>绩效发布失败：员工名单不一致</span>
                </div>
                <div class="mb-2">
                  <span class="text-gray-600">当前有效用户数：</span>
                  <span class="font-bold text-blue-600">{{ currentUserCountRef }}</span>
                  <span class="text-gray-600 ml-4">配置表用户数：</span>
                  <span class="font-bold text-blue-600">{{ totalConfigUserCountRef }}</span>
                </div>

                <div v-if="extraUserNamesRef && extraUserNamesRef.length > 0" class="mb-2">
                  <p class="font-bold text-red-600 mb-1">
                    多余的用户（在当前查询中存在但配置表中缺失）：
                    <span class="text-sm font-normal">({{ extraUserNamesRef.length }}人)</span>
                  </p>
                  <div class="pl-4 max-h-32 overflow-y-auto bg-red-50 rounded p-2">
                    <p v-for="(userName, index) in extraUserNamesRef" :key="index" class="text-red-600 text-sm mb-1"> {{ index + 1 }}. {{ userName }} </p>
                  </div>
                  <p class="text-sm text-gray-500 mt-1">💡 建议：检查这些用户是否应该参与绩效考核，如需要请在考核配置中添加相关设置。</p>
                </div>

                <div v-if="missingUserNamesRef && missingUserNamesRef.length > 0" class="mb-2">
                  <p class="font-bold text-orange-600 mb-1">
                    缺失的用户（在配置表中存在但当前查询中缺失）：
                    <span class="text-sm font-normal">({{ missingUserNamesRef.length }}人)</span>
                  </p>
                  <div class="pl-4 max-h-32 overflow-y-auto bg-orange-50 rounded p-2">
                    <p v-for="(userName, index) in missingUserNamesRef" :key="index" class="text-orange-600 text-sm mb-1"> {{ index + 1 }}. {{ userName }} </p>
                  </div>
                  <p class="text-sm text-gray-500 mt-1">💡 建议：检查这些用户的状态（是否离职、禁用等），或更新考核配置。</p>
                </div>
              </template>
            </a-alert>
          </div>
          <div class="pl-2 pr-2">
            <BasicTable @register="registerTable" ref="tableRef">
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'noteList'">
                  <div class="leading-relaxed" v-for="t in record.noteList" :key="t.checker">
                    <span v-if="!isNull(t.personActScore)"
                      >{{ t.startDate }}~{{ t.endDate }} 考核人：{{ t.checker }}{{ t.ratio == 100 ? '' : `[${t.ratio}%]`
                      }}<span class="text-green-500 font-bold"
                        >[团体分:{{ t.checkDeptAvgScore }},个人分:{{ t.checkPersonScore }},折:{{ t.personActScore }}]</span
                      >
                      评语：{{ t.checkComment }}</span
                    >
                    <span v-else
                      >{{ t.startDate }}~{{ t.endDate }} 考核人：{{ t.checker }}{{ t.ratio == 100 ? '' : `[${t.ratio}%]`
                      }}<span class="text-red-500 font-bold">[未打分]</span></span
                    >
                  </div>
                </template>
              </template>
              <template #summary v-if="publishPermission">
                <a-table-summary fixed>
                  <a-table-summary-row>
                    <a-table-summary-cell class="text-center" :index="0" :col-span="24">
                      <a-space>
                        <a-button
                          :loading="btnLoadingRef"
                          :disabled="publishBtnDisabledRef"
                          type="primary"
                          preIcon="ym-custom ym-custom-cursor-pointer"
                          @click="handlePublish"
                          >绩效成绩分布</a-button
                        >
                      </a-space>
                    </a-table-summary-cell>
                  </a-table-summary-row>
                </a-table-summary>
              </template>
            </BasicTable>
          </div>
        </ScrollContainer>
      </div>
    </div>
  </div>
</template>
<script lang="tsx" setup>
  import { ref } from 'vue';
  import { BasicTable, useTable, BasicColumn } from '/@/components/Table';
  import { performancePreviewList, performancePublish } from './api';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useBtnLoading } from '../hooks/useBtnLoading';
  import { isNull } from '/@/utils/is';
  import { ScrollContainer } from '/@/components/Container/index';
  import { sortChineseStr } from '/@/utils/xh';
  import { useComponentRegister } from '/@/components/Form/index';
  import FbSelect from '../common/fbSelect.vue';
  import { usePermission } from '/@/hooks/web/usePermission';

  defineOptions({ name: 'performance-publish' });
  // @ts-expect-error
  useComponentRegister('FbSelect', FbSelect);
  const { hasBtnP } = usePermission();
  const publishPermission = hasBtnP('PERFORMANCE_PUBLISH');
  const [btnLoadingRef, { openLoading, closeLoading }] = useBtnLoading();
  const { createMessage, createConfirm } = useMessage();
  const publishBtnDisabledRef = ref<boolean>(true);
  const reviewMonthAndStatusRef = ref<string>('');
  const noPlatScoreRef = ref<Recordable[]>([]);
  const showUserDifferenceRef = ref<boolean>(false);
  const extraUserNamesRef = ref<string[]>([]);
  const missingUserNamesRef = ref<string[]>([]);
  const currentUserCountRef = ref<number>(0);
  const totalConfigUserCountRef = ref<number>(0);
  const columns: BasicColumn[] = [
    { title: '所属分部', dataIndex: 'fbName', width: 150, align: 'center', sorter: (a, b) => sortChineseStr(a.fbName, b.fbName) },
    { title: '员工', dataIndex: 'userName', width: 150, align: 'center', sorter: (a, b) => sortChineseStr(a.userName, b.userName) },
    { title: '绩效实际分', dataIndex: 'actScore', width: 150, align: 'center', sorter: (a, b) => a.actScore - b.actScore },
    { title: '考核信息', dataIndex: 'noteList' },
  ];
  const [registerTable, { reload }] = useTable({
    api: performancePreviewList,
    columns,
    immediate: true,
    useSearchForm: true,
    clickToRowSelect: false,
    showTableSetting: false,
    pagination: false,
    bordered: true,
    formConfig: {
      schemas: [
        {
          field: 'fbId',
          label: '所属分部',
          // @ts-expect-error
          component: 'FbSelect',
          colProps: {
            span: 4,
          },
          componentProps: {
            placeholder: '请选择',
          },
        },
        // {
        //   field: 'userId',
        //   label: '员工',
        //   component: 'UserSelectDropdown',
        //   colProps: {
        //     span: 4,
        //   },
        //   componentProps: {
        //     placeholder: '请输入',
        //   },
        // },
      ],
    },
    afterFetch: (_, { extra }) => {
      publishBtnDisabledRef.value = !extra?.canPublic;
      reviewMonthAndStatusRef.value = extra?.reviewMonthAndStatus || '';
      noPlatScoreRef.value = extra?.noPlatScore || [];

      // 处理员工名单差异信息
      extraUserNamesRef.value = extra?.extraUserNames || [];
      missingUserNamesRef.value = extra?.missingUserNames || [];
      currentUserCountRef.value = extra?.currentUserCount || 0;
      totalConfigUserCountRef.value = extra?.totalConfigUserCount || 0;

      // 判断是否显示员工差异提示
      showUserDifferenceRef.value =
        ((extraUserNamesRef.value && extraUserNamesRef.value.length > 0) || (missingUserNamesRef.value && missingUserNamesRef.value.length > 0)) &&
        !extra?.canPublic;
    },
  });

  function handlePublish() {
    createConfirm({
      iconType: 'warning',
      title: '提示',
      content: '是否确认发布绩效成绩？',
      onOk: () => {
        openLoading();
        performancePublish()
          .then(() => {
            createMessage.success('发布成功！');
            reload();
            closeLoading();
          })
          .catch(() => {
            closeLoading();
          });
      },
      onCancel: () => {},
    });
  }
</script>
