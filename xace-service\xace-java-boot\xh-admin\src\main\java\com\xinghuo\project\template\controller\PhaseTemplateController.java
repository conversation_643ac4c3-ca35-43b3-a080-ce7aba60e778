package com.xinghuo.project.template.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.project.template.entity.PhaseTemplateEntity;
import com.xinghuo.project.template.model.PhaseTemplatePagination;
import com.xinghuo.project.template.model.vo.PhaseTemplateVO;
import com.xinghuo.project.template.model.dto.PhaseTemplateSelectVO;
import com.xinghuo.project.template.model.form.PhaseTemplateForm;
import com.xinghuo.project.template.service.PhaseTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 阶段模板管理控制器
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Tag(name = "阶段模板管理", description = "阶段模板管理相关接口")
@RestController
@RequestMapping("/api/project/template/phaseTemplate")
public class PhaseTemplateController {

    @Resource
    private PhaseTemplateService phaseTemplateService;

    /**
     * 获取阶段模板列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取阶段模板列表")
    public ActionResult<PageListVO<PhaseTemplateVO>> list(@RequestBody PhaseTemplatePagination pagination) {

            List<PhaseTemplateEntity> list = phaseTemplateService.getList(pagination);
            List<PhaseTemplateVO> listVO = BeanCopierUtils.copyList(list, PhaseTemplateVO.class);

            // 对结果进行数据转换和补充
            for (PhaseTemplateVO vo : listVO) {
                // 状态名称转换
                if (vo.getStatus() != null) {
                    vo.setStatusName(vo.getStatus() == 1 ? "启用" : "禁用");
                }
            }

            PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
            return ActionResult.page(listVO, page);

    }

    /**
     * 获取阶段模板选择器列表（用于下拉选择等场景）
     */
    @GetMapping("/getSelectList")
    @Operation(summary = "获取阶段模板选择器列表")
    public ActionResult<List<PhaseTemplateSelectVO>> getSelectList(
            @Parameter(description = "关键字") @RequestParam(required = false) String keyword,
            @Parameter(description = "状态") @RequestParam(required = false) Integer status) {
        // 复用现有的分页查询方法，设置大分页获取所有数据
        PhaseTemplatePagination pagination = new PhaseTemplatePagination();
        pagination.setKeyword(keyword);
        pagination.setStatus(status);
        pagination.setPageSize(9999); // 设置大分页获取所有数据
        pagination.setCurrentPage(1);

        // 复用现有的查询逻辑
        List<PhaseTemplateEntity> entities = phaseTemplateService.getList(pagination);

        // 转换为选择器VO
        List<PhaseTemplateSelectVO> selectList = BeanCopierUtils.copyList(entities, PhaseTemplateSelectVO.class);

        // 构建fullName字段和状态名称
        for (PhaseTemplateSelectVO vo : selectList) {
            // 构建fullName字段，格式：[编码] 名称
            if (vo.getCode() != null && vo.getName() != null) {
                vo.setFullName("[" + vo.getCode() + "] " + vo.getName());
            } else if (vo.getName() != null) {
                vo.setFullName(vo.getName());
            } else {
                vo.setFullName(vo.getId());
            }
            
            // 状态名称转换
            if (vo.getStatus() != null) {
                vo.setStatusName(vo.getStatus() == 1 ? "启用" : "禁用");
            }
        }

        return ActionResult.success(selectList);
    }

    /**
     * 获取阶段模板详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取阶段模板详情")
    public ActionResult<PhaseTemplateEntity> getInfo(
            @Parameter(description = "阶段模板ID") @PathVariable String id) {

            PhaseTemplateEntity entity = phaseTemplateService.getInfo(id);
            if (entity == null) {
                return ActionResult.fail("阶段模板不存在");
            }
            return ActionResult.success(entity);

    }

    /**
     * 根据编码获取阶段模板
     */
    @GetMapping("/getByCode/{code}")
    @Operation(summary = "根据编码获取阶段模板")
    public ActionResult<PhaseTemplateEntity> getByCode(
            @Parameter(description = "阶段编码") @PathVariable String code) {

            PhaseTemplateEntity entity = phaseTemplateService.getByCode(code);
            if (entity == null) {
                return ActionResult.fail("阶段模板不存在");
            }
            return ActionResult.success(entity);

    }

    /**
     * 创建阶段模板
     */
    @PostMapping("")
    @Operation(summary = "创建阶段模板")
    public ActionResult<String> create(@RequestBody @Valid PhaseTemplateForm form) {
        // 检查阶段编码是否已存在
        if (phaseTemplateService.isExistByCode(form.getCode(), null)) {
            return ActionResult.fail("阶段编码已存在");
        }

        PhaseTemplateEntity entity = BeanCopierUtils.copy(form, PhaseTemplateEntity.class);
        String id = phaseTemplateService.create(entity);
        return ActionResult.success("创建成功", id);
    }

    /**
     * 更新阶段模板
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新阶段模板")
    public ActionResult<String> update(
            @Parameter(description = "阶段模板ID") @PathVariable String id,
            @RequestBody @Valid PhaseTemplateForm form) {
        // 检查阶段编码是否已存在
        if (phaseTemplateService.isExistByCode(form.getCode(), id)) {
            return ActionResult.fail("阶段编码已存在");
        }

        PhaseTemplateEntity entity = BeanCopierUtils.copy(form, PhaseTemplateEntity.class);
        phaseTemplateService.update(id, entity);
        return ActionResult.success("更新成功");
    }

    /**
     * 删除阶段模板
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除阶段模板")
    public ActionResult<String> delete(
            @Parameter(description = "阶段模板ID") @PathVariable String id) {
        phaseTemplateService.delete(id);
        return ActionResult.success("删除成功");
    }

    /**
     * 批量删除阶段模板
     */
    @DeleteMapping("/batchDelete")
    @Operation(summary = "批量删除阶段模板")
    public ActionResult<String> batchDelete(@RequestBody List<String> ids) {
        phaseTemplateService.batchDelete(ids);
        return ActionResult.success("批量删除成功");
    }

    /**
     * 更新阶段模板状态
     */
    @PutMapping("/updateStatus/{id}")
    @Operation(summary = "更新阶段模板状态")
    public ActionResult<String> updateStatus(
            @Parameter(description = "阶段模板ID") @PathVariable String id,
            @RequestParam Integer status) {
        phaseTemplateService.updateStatus(id, status);
        return ActionResult.success("状态更新成功");
    }

    /**
     * 批量更新状态
     */
    @PutMapping("/batchUpdateStatus")
    @Operation(summary = "批量更新状态")
    public ActionResult<String> batchUpdateStatus(
            @RequestBody List<String> ids,
            @RequestParam Integer status) {
        phaseTemplateService.batchUpdateStatus(ids, status);
        return ActionResult.success("批量更新状态成功");
    }

    /**
     * 启用阶段模板
     */
    @PutMapping("/enable/{id}")
    @Operation(summary = "启用阶段模板")
    public ActionResult<String> enable(
            @Parameter(description = "阶段模板ID") @PathVariable String id) {
        phaseTemplateService.enable(id);
        return ActionResult.success("启用成功");
    }

    /**
     * 禁用阶段模板
     */
    @PutMapping("/disable/{id}")
    @Operation(summary = "禁用阶段模板")
    public ActionResult<String> disable(
            @Parameter(description = "阶段模板ID") @PathVariable String id) {
        phaseTemplateService.disable(id);
        return ActionResult.success("禁用成功");
    }

    /**
     * 复制阶段模板
     */
    @PostMapping("/copy/{id}")
    @Operation(summary = "复制阶段模板")
    public ActionResult<String> copy(
            @Parameter(description = "阶段模板ID") @PathVariable String id,
            @RequestParam String newName) {
        String newId = phaseTemplateService.copy(id, newName);
        return ActionResult.success("复制成功", newId);
    }

    /**
     * 检查阶段编码是否存在
     */
    @GetMapping("/checkCodeExists")
    @Operation(summary = "检查阶段编码是否存在")
    public ActionResult<Boolean> checkCodeExists(
            @RequestParam String code,
            @RequestParam(required = false) String excludeId) {
        boolean exists = phaseTemplateService.isExistByCode(code, excludeId);
        return ActionResult.success(exists);
    }


    /**
     * 生成阶段编码
     */
    @GetMapping("/generateCode")
    @Operation(summary = "生成阶段编码")
    public ActionResult<String> generateCode() {
        String code = phaseTemplateService.generateCode();
        return ActionResult.success(code);
    }

    /**
     * 获取阶段模板使用情况
     */
    @GetMapping("/getPhaseTemplateUsageInfo/{id}")
    @Operation(summary = "获取阶段模板使用情况")
    public ActionResult<Map<String, Object>> getPhaseTemplateUsageInfo(
            @Parameter(description = "阶段模板ID") @PathVariable String id) {
        Map<String, Object> usageInfo = phaseTemplateService.getPhaseTemplateUsageInfo(id);
        return ActionResult.success(usageInfo);
    }
}
