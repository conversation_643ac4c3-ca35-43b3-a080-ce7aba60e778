package com.xinghuo.performance.model.analysis;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 绩效分析概览数据模型
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class PerformanceAnalysisOverviewModel {

    /**
     * 总参与人数
     */
    private Integer totalUsers;

    /**
     * 平均绩效得分
     */
    private BigDecimal avgScore;

    /**
     * 优秀率（90分以上）
     */
    private BigDecimal excellentRate;

    /**
     * 完成评分人数
     */
    private Integer completedUsers;

    /**
     * 良好率（80-89分）
     */
    private BigDecimal goodRate;

    /**
     * 合格率（60-79分）
     */
    private BigDecimal passRate;

    /**
     * 不合格率（60分以下）
     */
    private BigDecimal failRate;

    /**
     * 最高分
     */
    private BigDecimal maxScore;

    /**
     * 最低分
     */
    private BigDecimal minScore;

    /**
     * 分数标准差
     */
    private BigDecimal scoreStdDev;

    /**
     * 参与分部数量
     */
    private Integer departmentCount;

    /**
     * 平均完成时间（天）
     */
    private BigDecimal avgCompletionDays;
}
