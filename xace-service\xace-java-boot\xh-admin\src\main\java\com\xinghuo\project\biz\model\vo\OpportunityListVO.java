package com.xinghuo.project.biz.model.vo;

import com.xinghuo.project.biz.entity.OpportunityEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商机列表VO
 * 用于商机列表显示，包含关联数据的名称字段
 * 
 * <AUTHOR>
 * @date 2025-07-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OpportunityListVO extends OpportunityEntity {
    
    /**
     * 客户单位名称
     */
    private String custName;
    
    /**
     * 项目负责人名称
     */
    private String projectLeaderName;
    
    /**
     * 市场负责人名称
     */
    private String marketLinkmanName;
    
    /**
     * 售前负责人名称
     */
    private String presaleLinkmanName;
    
    /**
     * 所属分部名称
     */
    private String deptName;
    
    /**
     * 研发分部名称
     */
    private String yfDeptName;
    
    /**
     * 创建人名称
     */
    private String createUserName;
    
    /**
     * 最后修改人名称
     */
    private String lastModifiedUserName;
    
    /**
     * 最后更新时间（格式化后的字符串）
     */
    private String lastModifyTime;
    
    /**
     * 一部金额
     */
    private String oneDeptMoney;
    
    /**
     * 一部外采
     */
    private String oneDeptPurchase;
    
    /**
     * 二部金额
     */
    private String twoDeptMoney;
    
    /**
     * 二部外采
     */
    private String twoDeptPurchase;
    
    /**
     * 综合金额
     */
    private String comprehensiveMoney;
    
    /**
     * 综合外采
     */
    private String comprehensivePurchase;
}