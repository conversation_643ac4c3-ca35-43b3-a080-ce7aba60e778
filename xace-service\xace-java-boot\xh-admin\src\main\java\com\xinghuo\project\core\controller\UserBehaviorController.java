package com.xinghuo.project.core.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.project.core.entity.ProjBehaviorLogEntity;
import com.xinghuo.project.core.model.dto.FavoriteStatusUpdateDTO;
import com.xinghuo.project.core.service.UserBehaviorService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户行为通用控制器
 * 
 * 提供通用的用户行为管理接口，支持项目、项目群、项目组合等多种对象类型的关注和访问记录功能。
 * 
 * 支持的对象类型：
 * - PROJECT: 项目
 * - PROGRAM: 项目群  
 * - PORTFOLIO: 项目组合
 * 
 * <AUTHOR>
 * @date 2025-01-08
 */
@Slf4j
@Tag(name = "用户行为管理", description = "通用的用户行为管理接口，支持关注、访问记录等功能")
@RestController
@RequestMapping("/api/project/core/user-behavior")
public class UserBehaviorController {

    @Resource
    private UserBehaviorService userBehaviorService;

    /**
     * 通用关注状态切换接口
     * 
     * 支持项目、项目群、项目组合的关注和取消关注操作
     */
    @PostMapping("/{targetType}/{targetId}/favorite")
    @Operation(summary = "切换关注状态", description = "通用的关注状态切换接口，支持多种对象类型")
    public ActionResult<String> toggleFavoriteStatus(
            @Parameter(description = "对象类型 (PROJECT/PROGRAM/PORTFOLIO)", example = "PROJECT") 
            @PathVariable String targetType,
            @Parameter(description = "对象ID") 
            @PathVariable String targetId,
            @RequestBody @Valid FavoriteStatusUpdateDTO request) {
       
            // 验证对象类型
            if (!isValidTargetType(targetType)) {
                return ActionResult.fail("不支持的对象类型: " + targetType);
            }

            // 获取当前登录用户ID
            String currentUserId = UserProvider.getUser().getUserId();
            if (currentUserId == null) {
                return ActionResult.fail("用户未登录");
            }

            // 调用服务切换关注状态
            userBehaviorService.toggleFavorite(currentUserId, targetId, targetType.toUpperCase(), request.isFavorite());
            
            String message = request.isFavorite() ? "关注成功" : "取消关注成功";
            log.info("用户 {} {} 对象 {}({})", currentUserId, message, targetId, targetType);
            
            return ActionResult.success(message);
        
    }

    /**
     * 通用关注状态查询接口
     */
    @GetMapping("/{targetType}/{targetId}/favorite/status")
    @Operation(summary = "查询关注状态", description = "查询当前用户对指定对象的关注状态")
    public ActionResult<Boolean> getFavoriteStatus(
            @Parameter(description = "对象类型 (PROJECT/PROGRAM/PORTFOLIO)", example = "PROJECT") 
            @PathVariable String targetType,
            @Parameter(description = "对象ID") 
            @PathVariable String targetId) {
       
            // 验证对象类型
            if (!isValidTargetType(targetType)) {
                return ActionResult.fail("不支持的对象类型: " + targetType);
            }

            // 获取当前登录用户ID
            String currentUserId = UserProvider.getUser().getUserId();
            if (currentUserId == null) {
                return ActionResult.fail("用户未登录");
            }

            // 查询关注状态
            boolean isFavorite = userBehaviorService.isFavorite(currentUserId, targetId, targetType.toUpperCase());
            return ActionResult.success(isFavorite);
        
    }

    /**
     * 批量查询关注状态
     */
    @PostMapping("/batch/favorite/status")
    @Operation(summary = "批量查询关注状态", description = "批量查询当前用户对多个对象的关注状态")
    public ActionResult<Map<String, Boolean>> batchGetFavoriteStatus(
            @RequestBody List<Map<String, String>> targets) {
     
            // 获取当前登录用户ID
            String currentUserId = UserProvider.getUser().getUserId();
            if (currentUserId == null) {
                return ActionResult.fail("用户未登录");
            }

            // 验证请求参数
            if (targets == null || targets.isEmpty()) {
                return ActionResult.success(new HashMap<>());
            }

            // 验证所有对象类型
            for (Map<String, String> target : targets) {
                String targetType = target.get("targetType");
                if (targetType != null && !isValidTargetType(targetType)) {
                    return ActionResult.fail("不支持的对象类型: " + targetType);
                }
                // 转换为大写
                if (targetType != null) {
                    target.put("targetType", targetType.toUpperCase());
                }
            }

            // 批量查询关注状态
            Map<String, Boolean> results = userBehaviorService.batchCheckFavoriteStatus(currentUserId, targets);
            return ActionResult.success(results);
        
    }
   
  
    /**
     * 验证对象类型是否有效
     */
    private boolean isValidTargetType(String targetType) {
        if (targetType == null) {
            return false;
        }
        String upperType = targetType.toUpperCase();
        return "PROJECT".equals(upperType) || "PROGRAM".equals(upperType) || "PORTFOLIO".equals(upperType);
    }
}
