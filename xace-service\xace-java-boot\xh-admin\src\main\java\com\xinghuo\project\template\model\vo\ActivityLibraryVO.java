package com.xinghuo.project.template.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 标准项目活动库视图对象
 * 用于返回活动库列表和详情信息
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
@Schema(description = "标准项目活动库视图对象")
public class ActivityLibraryVO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private String id;

    /**
     * 标准活动名称 (如: 项目章程编写)
     */
    @Schema(description = "标准活动名称")
    private String name;

    /**
     * 标准活动编码 (可用于WBS编码)
     */
    @Schema(description = "标准活动编码")
    private String code;

    /**
     * 活动描述与指南
     */
    @Schema(description = "活动描述与指南")
    private String description;

    /**
     * 状态 (0:启用, 1:禁用)
     */
    @Schema(description = "状态")
    private Integer status;

    /**
     * 状态名称 (启用/禁用)
     */
    @Schema(description = "状态名称")
    private String statusName;

    /**
     * 是否是里程碑 (1:是, 0:否)
     */
    @Schema(description = "是否是里程碑")
    private Integer isMilestone;

    /**
     * 是否是里程碑名称 (是/否)
     */
    @Schema(description = "是否是里程碑名称")
    private String isMilestoneName;

    /**
     * 标准工期 (天)
     */
    @Schema(description = "标准工期(天)")
    private BigDecimal duration;

    /**
     * 标准工时 (小时)
     */
    @Schema(description = "标准工时(小时)")
    private BigDecimal standardHour;

    /**
     * 默认责任角色ID (关联角色字典表)
     */
    @Schema(description = "默认责任角色ID")
    private String responseRoleId;

    /**
     * 默认责任角色名称 (冗余字段，便于显示)
     */
    @Schema(description = "默认责任角色名称")
    private String responseRoleName;

    /**
     * 默认确认角色ID (当完成方式为人工确认时)
     */
    @Schema(description = "默认确认角色ID")
    private String confirmRoleId;

    /**
     * 默认确认角色名称 (冗余字段，便于显示)
     */
    @Schema(description = "默认确认角色名称")
    private String confirmRoleName;

    /**
     * 默认完成方式ID (关联字典表, 如: 0:无需确认, 2:审批确认, 3:人工确认)
     */
    @Schema(description = "默认完成方式ID")
    private String completeTypeId;

    /**
     * 默认完成方式名称 (冗余字段，便于显示)
     */
    @Schema(description = "默认完成方式名称")
    private String completeTypeName;

    /**
     * 关联的审批流程模板ID (当完成方式为审批确认时)
     */
    @Schema(description = "关联的审批流程模板ID")
    private String approveSchemaId;

    /**
     * 审批流程模板名称 (冗余字段，便于显示)
     */
    @Schema(description = "审批流程模板名称")
    private String approveSchemaName;

    /**
     * 活动大类ID (关联字典表, 如: 项目管理, 质量管理)
     */
    @Schema(description = "活动大类ID")
    private String activityTypeId;

    /**
     * 活动大类名称 (冗余字段，便于显示)
     */
    @Schema(description = "活动大类名称")
    private String activityTypeName;

    /**
     * 活动子类ID (关联字典表)
     */
    @Schema(description = "活动子类ID")
    private String activitySubTypeId;

    /**
     * 活动子类名称 (冗余字段，便于显示)
     */
    @Schema(description = "活动子类名称")
    private String activitySubTypeName;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private String tenantId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    /**
     * 创建用户ID
     */
    @Schema(description = "创建用户ID")
    private String createdBy;

    /**
     * 创建用户名称 (冗余字段，便于显示)
     */
    @Schema(description = "创建用户名称")
    private String createdByName;

    /**
     * 最后修改时间
     */
    @Schema(description = "最后修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdatedAt;

    /**
     * 最后修改用户ID
     */
    @Schema(description = "最后修改用户ID")
    private String lastUpdatedBy;

    /**
     * 最后修改用户名称 (冗余字段，便于显示)
     */
    @Schema(description = "最后修改用户名称")
    private String lastUpdatedByName;
}
