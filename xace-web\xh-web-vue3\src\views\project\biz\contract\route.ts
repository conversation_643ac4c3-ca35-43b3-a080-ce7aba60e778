import { LAYOUT } from '/@/router/constant';
import { t } from '/@/hooks/web/useI18n';

// 合同管理路由配置
export default {
  path: '/project/contract',
  name: 'ProjectContract',
  component: LAYOUT,
  redirect: '/project/contract/index',
  meta: {
    orderNo: 20,
    icon: 'icon-ym icon-ym-contract',
    title: t('合同管理'),
    defaultTitle: '合同管理',
  },
  children: [
    {
      path: 'index',
      name: 'ProjectContractIndex',
      component: () => import('/@/views/project/contract/index.vue'),
      meta: {
        title: t('合同列表'),
        defaultTitle: '合同列表',
        icon: 'icon-ym icon-ym-contract',
      },
    },
    {
      path: 'money',
      name: 'ProjectContractMoney',
      component: () => import('/@/views/project/contract/money/index.vue'),
      meta: {
        title: t('收款管理'),
        defaultTitle: '收款管理',
        icon: 'icon-ym icon-ym-money',
      },
    },
    // 移除详情页路由，改用抽屉组件
  ],
};
