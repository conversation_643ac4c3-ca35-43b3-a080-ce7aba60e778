<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" title="商机周报历史记录" width="1000px" :showFooter="false">
    <div class="p-4">
      <BasicTable @register="registerTable" />
    </div>
  </BasicDrawer>
</template>

<script lang="ts" setup>
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { BasicTable, useTable } from '/@/components/Table';
  import { historyColumns } from './businessWeeklog.data';
  import { getBusinessWeeklogHistoryByProjId } from '/@/api/project/biz/businessWeeklog';

  // 注册表格
  const [registerTable, { setTableData }] = useTable({
    title: '历史记录列表',
    columns: historyColumns,
    dataSource: [],
    pagination: false,
    striped: true,
    showIndexColumn: true,
    expandRowByClick: true,
    canResize: false,
    rowKey: 'id',
  });

  // 注册抽屉
  const [registerDrawer] = useDrawerInner(async data => {
    if (data.record?.projId) {
      try {
        const response = await getBusinessWeeklogHistoryByProjId(data.record.projId);
        if (response.code === 200) {
          setTableData(response.data || []);
        }
      } catch (error) {
        console.error('加载历史记录失败:', error);
        setTableData([]);
      }
    }
  });
</script>
