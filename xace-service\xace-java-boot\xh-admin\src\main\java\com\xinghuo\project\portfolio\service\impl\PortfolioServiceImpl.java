package com.xinghuo.project.portfolio.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.portfolio.dao.PortfolioMapper;
import com.xinghuo.project.portfolio.entity.PortfolioEntity;
import com.xinghuo.project.portfolio.model.PortfolioPagination;
import com.xinghuo.project.portfolio.service.PortfolioService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 项目组合服务实现类
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Service
public class PortfolioServiceImpl extends BaseServiceImpl<PortfolioMapper, PortfolioEntity> implements PortfolioService {

    @Resource
    private PortfolioMapper portfolioMapper;

    @Override
    public List<PortfolioEntity> getList(PortfolioPagination pagination) {
        QueryWrapper<PortfolioEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<PortfolioEntity> lambda = queryWrapper.lambda();

        // 根据组合编码模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getCode())) {
            lambda.like(PortfolioEntity::getCode, pagination.getCode());
        }

        // 根据组合名称模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getName())) {
            lambda.like(PortfolioEntity::getName, pagination.getName());
        }

        // 根据组合类型ID精确查询
        if (StrXhUtil.isNotEmpty(pagination.getTypeId())) {
            lambda.eq(PortfolioEntity::getTypeId, pagination.getTypeId());
        }

        // 根据组合负责人ID精确查询
        if (StrXhUtil.isNotEmpty(pagination.getOwnerId())) {
            lambda.eq(PortfolioEntity::getOwnerId, pagination.getOwnerId());
        }

        // 根据状态精确查询
        if (StrXhUtil.isNotEmpty(pagination.getStatus())) {
            lambda.eq(PortfolioEntity::getStatus, pagination.getStatus());
        }

        // 根据关键字搜索名称或编码
        String keyword = pagination.getKeyword();
        if (StrXhUtil.isNotEmpty(keyword)) {
            lambda.and(wrapper -> wrapper
                    .like(PortfolioEntity::getName, keyword)
                    .or()
                    .like(PortfolioEntity::getCode, keyword)
            );
        }

        // 排序
        lambda.orderByDesc(PortfolioEntity::getCreatedAt);
        
        return processDataType(queryWrapper, pagination);
    }

    @Override
    public PortfolioEntity getInfo(String id) {
        return this.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveInfo(PortfolioEntity entity) {
        if (StrXhUtil.isEmpty(entity.getId())) {
            // 新增
            String id = RandomUtil.snowId();
            entity.setId(id);
        }
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateInfo(PortfolioEntity entity) {
        return this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteById(String id) {
        return this.removeById(id);
    }

    @Override
    public List<PortfolioEntity> getListByOwnerId(String ownerId) {
        QueryWrapper<PortfolioEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(PortfolioEntity::getOwnerId, ownerId);
        queryWrapper.lambda().orderByDesc(PortfolioEntity::getCreatedAt);
        // 使用自定义方法查询
        return this.list(queryWrapper);
    }

    @Override
    public List<PortfolioEntity> getListByTypeId(String typeId) {
        QueryWrapper<PortfolioEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(PortfolioEntity::getTypeId, typeId);
        queryWrapper.lambda().orderByDesc(PortfolioEntity::getCreatedAt);
        return this.list(queryWrapper);

    }

    @Override
    public List<PortfolioEntity> getListByStatus(String status) {
       QueryWrapper<PortfolioEntity>  queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(PortfolioEntity::getStatus, status);
        queryWrapper.lambda().orderByDesc(PortfolioEntity::getCreatedAt);
        return this.list(queryWrapper);
    }

    @Override
    public boolean isExistByCode(String code, String excludeId) {
//        int count = portfolioMapper.checkCodeExists(code, excludeId);
        QueryWrapper<PortfolioEntity>  queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(PortfolioEntity::getCode, code);
        if (StrXhUtil.isNotEmpty(excludeId)) {
            queryWrapper.lambda().ne(PortfolioEntity::getId, excludeId);
        }
        long count = this.count(queryWrapper);



        return count > 0;
    }
}
