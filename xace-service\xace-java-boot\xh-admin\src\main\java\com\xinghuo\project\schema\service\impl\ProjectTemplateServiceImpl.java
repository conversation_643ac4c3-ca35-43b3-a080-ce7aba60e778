package com.xinghuo.project.schema.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.common.exception.DataException;
import com.xinghuo.project.schema.dao.ProjectTemplateMapper;
import com.xinghuo.project.schema.dao.ProjectSchemaWbsMapper;
import com.xinghuo.project.schema.dao.ProjectSchemaPhaseMapper;
import com.xinghuo.project.schema.entity.ProjectTemplateEntity;
import com.xinghuo.project.schema.entity.ProjectSchemaWbsEntity;
import com.xinghuo.project.schema.entity.ProjectSchemaPhaseEntity;
import com.xinghuo.project.schema.model.ProjectTemplatePagination;
import com.xinghuo.project.schema.model.vo.ProjectTemplateVO;
import com.xinghuo.project.schema.service.ProjectTemplateService;
import com.xinghuo.project.template.service.WbsTemplateMasterService;
import com.xinghuo.project.template.service.PhasePlanTemplateService;
import com.xinghuo.project.template.service.PhaseTemplateService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目模板服务实现类
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Slf4j
@Service
public class ProjectTemplateServiceImpl extends BaseServiceImpl<ProjectTemplateMapper, ProjectTemplateEntity> implements ProjectTemplateService {

    @Resource
    private ProjectSchemaWbsMapper projectSchemaWbsMapper;

    @Resource
    private ProjectSchemaPhaseMapper projectSchemaPhaseMapper;

    @Resource
    private WbsTemplateMasterService wbsTemplateMasterService;

    @Resource
    private PhasePlanTemplateService phasePlanTemplateService;

    @Resource
    private PhaseTemplateService phaseTemplateService;

    @Override
    public List<ProjectTemplateVO> getList(ProjectTemplatePagination pagination) {
        QueryWrapper<ProjectTemplateEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<ProjectTemplateEntity> lambda = queryWrapper.lambda();

        // 根据模板编码模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getCode())) {
            lambda.like(ProjectTemplateEntity::getCode, pagination.getCode());
        }

        // 根据模板名称模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getName())) {
            lambda.like(ProjectTemplateEntity::getName, pagination.getName());
        }

        // 根据状态精确查询
        if (pagination.getStatus() != null) {
            lambda.eq(ProjectTemplateEntity::getStatus, pagination.getStatus());
        }

        // 根据模板类型查询
        if (StrXhUtil.isNotEmpty(pagination.getTypeId())) {
            lambda.eq(ProjectTemplateEntity::getTypeId, pagination.getTypeId());
        }

        // 创建时间范围查询
        if (pagination.getCreateTimeStart() != null) {
            lambda.ge(ProjectTemplateEntity::getCreatedAt, pagination.getCreateTimeStart());
        }
        if (pagination.getCreateTimeEnd() != null) {
            lambda.le(ProjectTemplateEntity::getCreatedAt, pagination.getCreateTimeEnd());
        }

        // 根据创建用户查询
        if (StrXhUtil.isNotEmpty(pagination.getCreatedBy())) {
            lambda.eq(ProjectTemplateEntity::getCreatedBy, pagination.getCreatedBy());
        }

        // 根据描述关键字查询
        if (StrXhUtil.isNotEmpty(pagination.getDescriptionKeyword())) {
            lambda.like(ProjectTemplateEntity::getDescription, pagination.getDescriptionKeyword());
        }

        // 根据关键字搜索编码或名称
        String keyword = pagination.getKeyword();
        if (StrXhUtil.isNotEmpty(keyword)) {
            lambda.and(wrapper -> wrapper
                    .like(ProjectTemplateEntity::getCode, keyword)
                    .or()
                    .like(ProjectTemplateEntity::getName, keyword)
            );
        }

        // 排序：按创建时间倒序
        lambda.orderByDesc(ProjectTemplateEntity::getCreatedAt);
        
        List<ProjectTemplateEntity> masterList = processDataType(queryWrapper, pagination);
        List<ProjectTemplateVO> voList = BeanCopierUtils.copyList(masterList, ProjectTemplateVO.class);

        // 补充统计信息
        for (ProjectTemplateVO vo : voList) {
            enrichTemplateStatistics(vo);
        }

        return voList;
    }

    @Override
    public List<ProjectTemplateEntity> getListByStatus(Integer status) {
        if (status == null) {
            return new ArrayList<>();
        }

        QueryWrapper<ProjectTemplateEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ProjectTemplateEntity::getStatus, status)
                .orderByDesc(ProjectTemplateEntity::getCreatedAt);
        
        return list(queryWrapper);
    }

    @Override
    public List<ProjectTemplateEntity> getListByType(String typeId) {
        if (StrXhUtil.isEmpty(typeId)) {
            return new ArrayList<>();
        }

        QueryWrapper<ProjectTemplateEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(ProjectTemplateEntity::getTypeId, typeId)
                .eq(ProjectTemplateEntity::getStatus, 0) // 只查询启用状态（0为启用，1为禁用）
                .orderByDesc(ProjectTemplateEntity::getCreatedAt);
        
        return list(queryWrapper);
    }

    @Override
    public ProjectTemplateVO getDetailInfo(String id) {
        if (StrXhUtil.isEmpty(id)) {
            return null;
        }

        ProjectTemplateEntity masterEntity = getById(id);
        if (masterEntity == null) {
            return null;
        }

        ProjectTemplateVO vo = BeanCopierUtils.copy(masterEntity, ProjectTemplateVO.class);
        
        // 获取WBS配置列表
        List<ProjectSchemaWbsEntity> wbsList = getWbsConfigs(id);
        vo.setWbsList(wbsList);

        // 获取阶段配置列表
        List<ProjectSchemaPhaseEntity> phaseList = getPhaseConfigs(id);
        vo.setPhaseList(phaseList);

        // 补充统计信息
        enrichTemplateStatistics(vo);

        return vo;
    }

    @Override
    public ProjectTemplateEntity getInfo(String id) {
        if (StrXhUtil.isEmpty(id)) {
            return null;
        }
        return getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String create(ProjectTemplateVO templateVO) {
        if (templateVO == null) {
            throw new DataException("项目模板信息不能为空");
        }

        // 验证必填字段
        if (StrXhUtil.isEmpty(templateVO.getName())) {
            throw new DataException("模板名称不能为空");
        }

        // 检查名称是否重复
        if (isExistByName(templateVO.getName(), null)) {
            throw new DataException("模板名称已存在");
        }

        // 检查编码是否重复
        if (StrXhUtil.isNotEmpty(templateVO.getCode()) && isExistByCode(templateVO.getCode(), null)) {
            throw new DataException("模板编码已存在");
        }

        // 如果没有提供编码，自动生成
        if (StrXhUtil.isEmpty(templateVO.getCode())) {
            templateVO.setCode(generateCode());
        }

        // 设置默认状态（参照ActivityLibraryEntity，0为启用，1为禁用）
        if (templateVO.getStatus() == null) {
            templateVO.setStatus(0); // 默认启用状态
        }

        // 保存主表
        ProjectTemplateEntity masterEntity = BeanCopierUtils.copy(templateVO, ProjectTemplateEntity.class);
        save(masterEntity);

        // 保存WBS配置
        if (templateVO.getWbsList() != null && !templateVO.getWbsList().isEmpty()) {
            saveWbsConfigs(masterEntity.getId(), templateVO.getWbsList());
        }

        // 保存阶段配置
        if (templateVO.getPhaseList() != null && !templateVO.getPhaseList().isEmpty()) {
            savePhaseConfigs(masterEntity.getId(), templateVO.getPhaseList());
        }

        return masterEntity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(String id, ProjectTemplateVO templateVO) {
        if (StrXhUtil.isEmpty(id) || templateVO == null) {
            throw new DataException("参数不能为空");
        }

        ProjectTemplateEntity existEntity = getById(id);
        if (existEntity == null) {
            throw new DataException("项目模板不存在");
        }

        // 验证必填字段
        if (StrXhUtil.isEmpty(templateVO.getName())) {
            throw new DataException("模板名称不能为空");
        }

        // 检查名称是否重复
        if (isExistByName(templateVO.getName(), id)) {
            throw new DataException("模板名称已存在");
        }

        // 检查编码是否重复
        if (StrXhUtil.isNotEmpty(templateVO.getCode()) && isExistByCode(templateVO.getCode(), id)) {
            throw new DataException("模板编码已存在");
        }

        // 更新主表
        ProjectTemplateEntity masterEntity = BeanCopierUtils.copy(templateVO, ProjectTemplateEntity.class);
        masterEntity.setId(id);
        updateById(masterEntity);

        // 删除原有WBS配置
        QueryWrapper<ProjectSchemaWbsEntity> deleteWbsWrapper = new QueryWrapper<>();
        deleteWbsWrapper.lambda().eq(ProjectSchemaWbsEntity::getProjectTemplateId, id);
        projectSchemaWbsMapper.delete(deleteWbsWrapper);

        // 删除原有阶段配置
        QueryWrapper<ProjectSchemaPhaseEntity> deletePhaseWrapper = new QueryWrapper<>();
        deletePhaseWrapper.lambda().eq(ProjectSchemaPhaseEntity::getProjectTemplateId, id);
        projectSchemaPhaseMapper.delete(deletePhaseWrapper);

        // 保存新的WBS配置
        if (templateVO.getWbsList() != null && !templateVO.getWbsList().isEmpty()) {
            saveWbsConfigs(id, templateVO.getWbsList());
        }

        // 保存新的阶段配置
        if (templateVO.getPhaseList() != null && !templateVO.getPhaseList().isEmpty()) {
            savePhaseConfigs(id, templateVO.getPhaseList());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        if (StrXhUtil.isEmpty(id)) {
            throw new DataException("模板ID不能为空");
        }

        ProjectTemplateEntity entity = getById(id);
        if (entity == null) {
            throw new DataException("项目模板不存在");
        }

        // TODO: 检查是否被其他模块引用，如果被引用则不允许删除
        // 例如：检查是否被项目等引用

        // 删除WBS配置
        QueryWrapper<ProjectSchemaWbsEntity> deleteWbsWrapper = new QueryWrapper<>();
        deleteWbsWrapper.lambda().eq(ProjectSchemaWbsEntity::getProjectTemplateId, id);
        projectSchemaWbsMapper.delete(deleteWbsWrapper);

        // 删除阶段配置
        QueryWrapper<ProjectSchemaPhaseEntity> deletePhaseWrapper = new QueryWrapper<>();
        deletePhaseWrapper.lambda().eq(ProjectSchemaPhaseEntity::getProjectTemplateId, id);
        projectSchemaPhaseMapper.delete(deletePhaseWrapper);

        // 删除主表
        removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new DataException("模板ID列表不能为空");
        }

        for (String id : ids) {
            delete(id);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(String id, Integer status) {
        if (StrXhUtil.isEmpty(id) || status == null) {
            throw new DataException("参数不能为空");
        }

        ProjectTemplateEntity entity = getById(id);
        if (entity == null) {
            throw new DataException("项目模板不存在");
        }

        UpdateWrapper<ProjectTemplateEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .eq(ProjectTemplateEntity::getId, id)
                .set(ProjectTemplateEntity::getStatus, status);
        
        update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateStatus(List<String> ids, Integer status) {
        if (ids == null || ids.isEmpty() || status == null) {
            throw new DataException("参数不能为空");
        }

        UpdateWrapper<ProjectTemplateEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .in(ProjectTemplateEntity::getId, ids)
                .set(ProjectTemplateEntity::getStatus, status);
        
        update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enable(String id) {
        updateStatus(id, 0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void disable(String id) {
        updateStatus(id, 1);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchEnable(List<String> ids) {
        batchUpdateStatus(ids, 0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDisable(List<String> ids) {
        batchUpdateStatus(ids, 1);
    }

    @Override
    public boolean isExistByName(String name, String excludeId) {
        if (StrXhUtil.isEmpty(name)) {
            return false;
        }

        QueryWrapper<ProjectTemplateEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ProjectTemplateEntity::getName, name);
        
        if (StrXhUtil.isNotEmpty(excludeId)) {
            queryWrapper.lambda().ne(ProjectTemplateEntity::getId, excludeId);
        }

        return count(queryWrapper) > 0;
    }

    @Override
    public boolean isExistByCode(String code, String excludeId) {
        if (StrXhUtil.isEmpty(code)) {
            return false;
        }

        QueryWrapper<ProjectTemplateEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ProjectTemplateEntity::getCode, code);
        
        if (StrXhUtil.isNotEmpty(excludeId)) {
            queryWrapper.lambda().ne(ProjectTemplateEntity::getId, excludeId);
        }

        return count(queryWrapper) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String copy(String id, String newName) {
        if (StrXhUtil.isEmpty(id) || StrXhUtil.isEmpty(newName)) {
            throw new DataException("参数不能为空");
        }

        ProjectTemplateVO sourceTemplate = getDetailInfo(id);
        if (sourceTemplate == null) {
            throw new DataException("源项目模板不存在");
        }

        // 检查新名称是否重复
        if (isExistByName(newName, null)) {
            throw new DataException("模板名称已存在");
        }

        // 创建新的模板
        ProjectTemplateVO newTemplate = BeanCopierUtils.copy(sourceTemplate, ProjectTemplateVO.class);
        newTemplate.setId(null);
        newTemplate.setName(newName);
        newTemplate.setCode(generateCode()); // 生成新的编码
        newTemplate.setStatus(0); // 复制的模板默认为启用状态

        // 复制WBS配置
        if (newTemplate.getWbsList() != null) {
            for (ProjectSchemaWbsEntity wbs : newTemplate.getWbsList()) {
                wbs.setId(null);
                wbs.setProjectTemplateId(null); // 将在保存时设置
            }
        }

        // 复制阶段配置
        if (newTemplate.getPhaseList() != null) {
            for (ProjectSchemaPhaseEntity phase : newTemplate.getPhaseList()) {
                phase.setId(null);
                phase.setProjectTemplateId(null); // 将在保存时设置
            }
        }

        return create(newTemplate);
    }

    @Override
    public List<ProjectTemplateEntity> getSelectList(String keyword) {
        QueryWrapper<ProjectTemplateEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<ProjectTemplateEntity> lambda = queryWrapper.lambda();

        // 只查询启用状态的记录
        lambda.eq(ProjectTemplateEntity::getStatus, 0);

        // 根据关键字搜索
        if (StrXhUtil.isNotEmpty(keyword)) {
            lambda.and(wrapper -> wrapper
                    .like(ProjectTemplateEntity::getCode, keyword)
                    .or()
                    .like(ProjectTemplateEntity::getName, keyword)
            );
        }

        // 排序：按名称升序
        lambda.orderByAsc(ProjectTemplateEntity::getName);
        
        return list(queryWrapper);
    }

    @Override
    public String generateCode() {
        String prefix = "PT";
        String randomStr = RandomUtil.enUuId().substring(0, 8).toUpperCase();
        String code = prefix + randomStr;

        // 确保编码不重复
        while (isExistByCode(code, null)) {
            randomStr = RandomUtil.enUuId().substring(0, 8).toUpperCase();
            code = prefix + randomStr;
        }

        return code;
    }

    @Override
    public Map<String, Object> getTemplateUsageInfo(String id) {
        Map<String, Object> result = new HashMap<>();
        
        if (StrXhUtil.isEmpty(id)) {
            result.put("total", 0);
            result.put("details", new ArrayList<>());
            return result;
        }

        // TODO: 实现模板使用情况统计
        // 统计在以下模块中的使用情况：
        // 1. 项目
        // 2. 其他相关模块

        result.put("total", 0);
        result.put("details", new ArrayList<>());
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importFromWbsTemplate(String templateId, String wbsTemplateId) {
        if (StrXhUtil.isEmpty(templateId) || StrXhUtil.isEmpty(wbsTemplateId)) {
            throw new DataException("参数不能为空");
        }

        ProjectTemplateEntity template = getById(templateId);
        if (template == null) {
            throw new DataException("项目模板不存在");
        }

        try {
            // 获取WBS模板详情
            var wbsTemplateDetail = wbsTemplateMasterService.getDetailInfo(wbsTemplateId);
            if (wbsTemplateDetail == null) {
                throw new DataException("WBS模板不存在");
            }

            // 删除原有WBS配置
            QueryWrapper<ProjectSchemaWbsEntity> deleteWrapper = new QueryWrapper<>();
            deleteWrapper.lambda().eq(ProjectSchemaWbsEntity::getProjectTemplateId, templateId);
            projectSchemaWbsMapper.delete(deleteWrapper);

            // 导入WBS配置
            if (wbsTemplateDetail.getWbsDetails() != null && !wbsTemplateDetail.getWbsDetails().isEmpty()) {
                List<ProjectSchemaWbsEntity> wbsConfigs = new ArrayList<>();
                for (var wbsDetail : wbsTemplateDetail.getWbsDetails()) {
                    ProjectSchemaWbsEntity wbsConfig = new ProjectSchemaWbsEntity();
                    wbsConfig.setProjectTemplateId(templateId);
                    wbsConfig.setSourceWbsDetailId(wbsDetail.getId());
                    wbsConfig.setSourceLibraryActivityId(wbsDetail.getLibraryActivityId());
                    wbsConfig.setParentId(wbsDetail.getParentId());
                    wbsConfig.setWbsCode(wbsDetail.getWbsCode());
                    wbsConfig.setName(wbsDetail.getName());
                    wbsConfig.setSeqNo(wbsDetail.getSeqNo());
                    wbsConfig.setLevel(wbsDetail.getLevel());
                    wbsConfig.setNodeType(wbsDetail.getNodeType());
                    wbsConfig.setIsMilestone(wbsDetail.getIsMilestone());
                    wbsConfig.setDuration(wbsDetail.getDuration());
                    wbsConfig.setResponseRoleId(wbsDetail.getResponseRoleId());
                    wbsConfig.setPredecessors(wbsDetail.getPredecessors());
                    wbsConfigs.add(wbsConfig);
                }
                saveWbsConfigs(templateId, wbsConfigs);
            }

            log.info("从WBS模板 {} 导入配置到项目模板 {} 成功", wbsTemplateId, templateId);
        } catch (Exception e) {
            log.error("从WBS模板导入配置失败", e);
            throw new DataException("从WBS模板导入配置失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importFromPhaseTemplate(String templateId, String phaseTemplateId) {
        if (StrXhUtil.isEmpty(templateId) || StrXhUtil.isEmpty(phaseTemplateId)) {
            throw new DataException("参数不能为空");
        }

        ProjectTemplateEntity template = getById(templateId);
        if (template == null) {
            throw new DataException("项目模板不存在");
        }

        try {
            // 获取阶段模板详情
            var phaseTemplateDetail = phasePlanTemplateService.getDetailInfo(phaseTemplateId);
            if (phaseTemplateDetail == null) {
                throw new DataException("阶段模板不存在");
            }

            // 删除原有阶段配置
            QueryWrapper<ProjectSchemaPhaseEntity> deleteWrapper = new QueryWrapper<>();
            deleteWrapper.lambda().eq(ProjectSchemaPhaseEntity::getProjectTemplateId, templateId);
            projectSchemaPhaseMapper.delete(deleteWrapper);

            // 导入阶段配置
            if (phaseTemplateDetail.getPhaseDetails() != null && !phaseTemplateDetail.getPhaseDetails().isEmpty()) {
                List<ProjectSchemaPhaseEntity> phaseConfigs = new ArrayList<>();
                for (var phaseDetail : phaseTemplateDetail.getPhaseDetails()) {
                    ProjectSchemaPhaseEntity phaseConfig = new ProjectSchemaPhaseEntity();
                    phaseConfig.setProjectTemplateId(templateId);
                    phaseConfig.setPhaseLibraryId(phaseDetail.getId()); // 使用阶段明细ID作为关联
                    phaseConfig.setSeqNo(phaseDetail.getSeqNo());
                    phaseConfig.setDuration(phaseDetail.getDuration());
                    phaseConfig.setCompletionWeight(100); // 默认权重
                    phaseConfig.setCanCut(phaseDetail.getCanCut());
                    phaseConfig.setApprovalId(phaseDetail.getApprovalSchemaId());
                    phaseConfig.setChecklistId(phaseDetail.getCheckTemplateId());
                    phaseConfig.setWorkproductTplId(null); // 阶段计划模板中没有交付物模板字段
                    phaseConfigs.add(phaseConfig);
                }
                savePhaseConfigs(templateId, phaseConfigs);
            }

            log.info("从阶段模板 {} 导入配置到项目模板 {} 成功", phaseTemplateId, templateId);
        } catch (Exception e) {
            log.error("从阶段模板导入配置失败", e);
            throw new DataException("从阶段模板导入配置失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importFromPhaseLibrary(String templateId, List<String> phaseIds) {
        if (StrXhUtil.isEmpty(templateId) || phaseIds == null || phaseIds.isEmpty()) {
            throw new DataException("参数不能为空");
        }

        ProjectTemplateEntity template = getById(templateId);
        if (template == null) {
            throw new DataException("项目模板不存在");
        }

        try {
            // 删除原有阶段配置
            QueryWrapper<ProjectSchemaPhaseEntity> deleteWrapper = new QueryWrapper<>();
            deleteWrapper.lambda().eq(ProjectSchemaPhaseEntity::getProjectTemplateId, templateId);
            projectSchemaPhaseMapper.delete(deleteWrapper);

            // 导入标准阶段
            List<ProjectSchemaPhaseEntity> phaseConfigs = new ArrayList<>();
            for (int i = 0; i < phaseIds.size(); i++) {
                String phaseId = phaseIds.get(i);
                // 获取标准阶段信息
                var phaseInfo = phaseTemplateService.getById(phaseId);
                if (phaseInfo != null) {
                    ProjectSchemaPhaseEntity phaseConfig = new ProjectSchemaPhaseEntity();
                    phaseConfig.setProjectTemplateId(templateId);
                    phaseConfig.setPhaseLibraryId(phaseId);
                    phaseConfig.setSeqNo(i + 1);
                    phaseConfig.setDuration(phaseInfo.getStdDuration());
//                    phaseConfig.setCompletionWeight(phaseInfo.getCompletionWeight());
                    phaseConfig.setCanCut(0); // 默认不可裁剪
                    phaseConfig.setApprovalId(phaseInfo.getDefaultApprovalId());
                    phaseConfigs.add(phaseConfig);
                }
            }

            if (!phaseConfigs.isEmpty()) {
                savePhaseConfigs(templateId, phaseConfigs);
            }

            log.info("从标准阶段库导入阶段 {} 到项目模板 {} 成功", phaseIds, templateId);
        } catch (Exception e) {
            log.error("从标准阶段库导入阶段失败", e);
            throw new DataException("从标准阶段库导入阶段失败：" + e.getMessage());
        }
    }

    @Override
    public List<ProjectSchemaWbsEntity> getWbsConfigs(String templateId) {
        if (StrXhUtil.isEmpty(templateId)) {
            return new ArrayList<>();
        }

        QueryWrapper<ProjectSchemaWbsEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(ProjectSchemaWbsEntity::getProjectTemplateId, templateId)
                .orderByAsc(ProjectSchemaWbsEntity::getSeqNo);
        
        return projectSchemaWbsMapper.selectList(queryWrapper);
    }

    @Override
    public List<ProjectSchemaPhaseEntity> getPhaseConfigs(String templateId) {
        if (StrXhUtil.isEmpty(templateId)) {
            return new ArrayList<>();
        }

        QueryWrapper<ProjectSchemaPhaseEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(ProjectSchemaPhaseEntity::getProjectTemplateId, templateId)
                .orderByAsc(ProjectSchemaPhaseEntity::getSeqNo);
        
        return projectSchemaPhaseMapper.selectList(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveWbsConfigs(String templateId, List<ProjectSchemaWbsEntity> wbsConfigs) {
        if (StrXhUtil.isEmpty(templateId) || wbsConfigs == null) {
            return;
        }

        for (ProjectSchemaWbsEntity wbs : wbsConfigs) {
            wbs.setProjectTemplateId(templateId);
            if (StrXhUtil.isEmpty(wbs.getId())) {
                projectSchemaWbsMapper.insert(wbs);
            } else {
                projectSchemaWbsMapper.updateById(wbs);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePhaseConfigs(String templateId, List<ProjectSchemaPhaseEntity> phaseConfigs) {
        if (StrXhUtil.isEmpty(templateId) || phaseConfigs == null) {
            return;
        }

        for (ProjectSchemaPhaseEntity phase : phaseConfigs) {
            phase.setProjectTemplateId(templateId);
            if (StrXhUtil.isEmpty(phase.getId())) {
                projectSchemaPhaseMapper.insert(phase);
            } else {
                projectSchemaPhaseMapper.updateById(phase);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteWbsConfigs(List<String> wbsIds) {
        if (wbsIds == null || wbsIds.isEmpty()) {
            return;
        }

        projectSchemaWbsMapper.deleteBatchIds(wbsIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePhaseConfigs(List<String> phaseIds) {
        if (phaseIds == null || phaseIds.isEmpty()) {
            return;
        }

        projectSchemaPhaseMapper.deleteBatchIds(phaseIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void applyToProjects(String templateId, List<String> projectIds) {
        // TODO: 实现应用到项目的逻辑
        // 将项目模板应用到指定的项目中
        log.info("应用项目模板 {} 到项目 {}", templateId, projectIds);
    }

    /**
     * 补充模板统计信息
     */
    private void enrichTemplateStatistics(ProjectTemplateVO vo) {
        if (vo == null) {
            return;
        }

        List<ProjectSchemaWbsEntity> wbsList = getWbsConfigs(vo.getId());
        List<ProjectSchemaPhaseEntity> phaseList = getPhaseConfigs(vo.getId());
        
        // WBS活动总数
        vo.setWbsCount(wbsList.size());
        
        // 阶段总数
        vo.setPhaseCount(phaseList.size());
        
        // 里程碑数量
        long milestoneCount = wbsList.stream()
                .filter(wbs -> wbs.getIsMilestone() != null && wbs.getIsMilestone() == 1)
                .count();
        vo.setMilestoneCount((int) milestoneCount);
        
        // 总工期（所有阶段工期之和）
        int totalDuration = phaseList.stream()
                .filter(phase -> phase.getDuration() != null)
                .mapToInt(ProjectSchemaPhaseEntity::getDuration)
                .sum();
        vo.setTotalDuration(totalDuration);
        
        // 最大WBS层级深度
        int maxWbsLevel = wbsList.stream()
                .filter(wbs -> wbs.getLevel() != null)
                .mapToInt(ProjectSchemaWbsEntity::getLevel)
                .max()
                .orElse(0);
        vo.setMaxWbsLevel(maxWbsLevel);
        
        // 是否包含审批流程
        boolean hasApproval = phaseList.stream()
                .anyMatch(phase -> StrXhUtil.isNotEmpty(phase.getApprovalId()));
        vo.setHasApproval(hasApproval);
        
        // 是否包含检查清单
        boolean hasChecklist = phaseList.stream()
                .anyMatch(phase -> StrXhUtil.isNotEmpty(phase.getChecklistId()));
        vo.setHasChecklist(hasChecklist);
        
        // 是否包含交付物模板
        boolean hasWorkProduct = phaseList.stream()
                .anyMatch(phase -> StrXhUtil.isNotEmpty(phase.getWorkproductTplId()));
        vo.setHasWorkProduct(hasWorkProduct);
    }
}
