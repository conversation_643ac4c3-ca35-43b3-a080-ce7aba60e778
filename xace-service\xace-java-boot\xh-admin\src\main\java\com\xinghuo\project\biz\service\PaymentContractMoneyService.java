package com.xinghuo.project.biz.service;

import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.project.biz.entity.PaymentContractMoneyEntity;
import com.xinghuo.project.biz.model.paymentContractMoney.PaymentContractMoneyPagination;
import com.xinghuo.project.biz.model.paymentContractMoney.PaymentContractMoneyStatusForm;
import com.xinghuo.project.biz.model.paymentContractMoney.PaymentContractMoneyVO;
import com.xinghuo.project.biz.model.paymentContractMoney.PaymentContractMoneyStats;

import java.util.Date;
import java.util.List;

public interface PaymentContractMoneyService extends BaseService<PaymentContractMoneyEntity> {

    /**
     * 获取采购付款计划列表
     *
     * @param pagination 分页查询条件
     * @return 采购付款计划列表
     */
    List<PaymentContractMoneyEntity> getList(PaymentContractMoneyPagination pagination);

    /**
     * 根据采购合同ID获取付款计划列表
     *
     * @param paycontractId 采购合同ID
     * @return 付款计划列表
     */
    List<PaymentContractMoneyEntity> getListByPaycontractId(String paycontractId);

    /**
     * 获取采购付款计划详情
     *
     * @param id 采购付款计划ID
     * @return 采购付款计划详情
     */
    PaymentContractMoneyEntity getInfo(String id);

    /**
     * 创建采购付款计划
     *
     * @param entity 采购付款计划实体
     */
    void create(PaymentContractMoneyEntity entity);

    /**
     * 更新采购付款计划
     *
     * @param id     采购付款计划ID
     * @param entity 采购付款计划实体
     */
    void update(String id, PaymentContractMoneyEntity entity);

    /**
     * 删除采购付款计划
     *
     * @param id 采购付款计划ID
     */
    void delete(String id);

    /**
     * 更新付款状态
     *
     * @param id     采购付款计划ID
     * @param form   状态更新表单
     */
    void updateStatus(String id, PaymentContractMoneyStatusForm form);

    /**
     * 登记付款
     *
     * @param id          采购付款计划ID
     * @param fukuanDate  付款日期
     * @param lastNote    备注
     */
    void registerPayment(String id, Date fukuanDate, String lastNote);

    /**
     * 填充关联信息
     *
     * @param listVOs 采购付款计划视图对象列表
     */
    void fillRelatedInfo(List<PaymentContractMoneyVO> listVOs);

    /**
     * 获取付款计划统计信息
     *
     * @param paycontractId 采购合同ID（可选）
     * @return 统计信息
     */
    PaymentContractMoneyStats getStats(String paycontractId);
}
