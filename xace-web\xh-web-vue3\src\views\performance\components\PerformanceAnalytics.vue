<template>
  <div class="performance-analytics">
    <!-- 分析控制面板 -->
    <a-card size="small" class="control-panel">
      <template #title>
        <div class="panel-title">
          <Icon icon="ant-design:bar-chart-outlined" class="title-icon" />
          <span>绩效分析控制台</span>
        </div>
      </template>

      <a-row :gutter="16">
        <a-col :span="6">
          <a-select v-model:value="analysisParams.timeRange" placeholder="选择时间范围" @change="handleTimeRangeChange">
            <a-select-option value="3months">近3个月</a-select-option>
            <a-select-option value="6months">近6个月</a-select-option>
            <a-select-option value="12months">近12个月</a-select-option>
            <a-select-option value="custom">自定义</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="6">
          <a-select v-model:value="analysisParams.departmentId" placeholder="选择部门" allow-clear @change="handleDepartmentChange">
            <a-select-option v-for="dept in departments" :key="dept.id" :value="dept.id">
              {{ dept.name }}
            </a-select-option>
          </a-select>
        </a-col>
        <a-col :span="6">
          <a-select v-model:value="analysisParams.analysisType" placeholder="分析类型" @change="handleAnalysisTypeChange">
            <a-select-option value="trend">趋势分析</a-select-option>
            <a-select-option value="comparison">对比分析</a-select-option>
            <a-select-option value="distribution">分布分析</a-select-option>
            <a-select-option value="correlation">关联分析</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="6">
          <a-button type="primary" @click="handleRefreshAnalysis" :loading="loading">
            <template #icon><Icon icon="ant-design:reload-outlined" /></template>
            刷新分析
          </a-button>
        </a-col>
      </a-row>

      <!-- 自定义时间范围 -->
      <div v-if="analysisParams.timeRange === 'custom'" class="custom-time-range">
        <a-range-picker v-model:value="customTimeRange" picker="month" format="YYYY-MM" @change="handleCustomTimeChange" />
      </div>
    </a-card>

    <!-- 绩效趋势图表 -->
    <a-row :gutter="16" class="charts-row">
      <a-col :span="12">
        <a-card title="个人绩效趋势" size="small" class="chart-card">
          <template #extra>
            <a-dropdown>
              <template #overlay>
                <a-menu @click="handlePersonalTrendAction">
                  <a-menu-item key="export">导出图表</a-menu-item>
                  <a-menu-item key="fullscreen">全屏查看</a-menu-item>
                  <a-menu-item key="settings">图表设置</a-menu-item>
                </a-menu>
              </template>
              <a-button size="small">
                <Icon icon="ant-design:more-outlined" />
              </a-button>
            </a-dropdown>
          </template>

          <div class="chart-container">
            <div ref="personalTrendChart" class="chart" style="height: 300px"></div>
            <div v-if="!personalTrendData.length" class="chart-empty">
              <a-empty description="暂无个人趋势数据" />
            </div>
          </div>

          <!-- 趋势指标 -->
          <div class="trend-indicators">
            <a-row :gutter="8">
              <a-col :span="8">
                <a-statistic title="平均分数" :value="personalTrendStats.average" :precision="1" suffix="分" />
              </a-col>
              <a-col :span="8">
                <a-statistic title="最高分数" :value="personalTrendStats.max" suffix="分" />
              </a-col>
              <a-col :span="8">
                <a-statistic
                  title="增长趋势"
                  :value="personalTrendStats.growth"
                  :precision="1"
                  suffix="%"
                  :value-style="{ color: personalTrendStats.growth >= 0 ? '#3f8600' : '#cf1322' }" />
              </a-col>
            </a-row>
          </div>
        </a-card>
      </a-col>

      <a-col :span="12">
        <a-card title="部门绩效对比" size="small" class="chart-card">
          <template #extra>
            <a-dropdown>
              <template #overlay>
                <a-menu @click="handleDepartmentCompareAction">
                  <a-menu-item key="export">导出图表</a-menu-item>
                  <a-menu-item key="fullscreen">全屏查看</a-menu-item>
                  <a-menu-item key="settings">图表设置</a-menu-item>
                </a-menu>
              </template>
              <a-button size="small">
                <Icon icon="ant-design:more-outlined" />
              </a-button>
            </a-dropdown>
          </template>

          <div class="chart-container">
            <div ref="departmentCompareChart" class="chart" style="height: 300px"></div>
            <div v-if="!departmentCompareData.length" class="chart-empty">
              <a-empty description="暂无部门对比数据" />
            </div>
          </div>

          <!-- 对比指标 -->
          <div class="compare-indicators">
            <a-row :gutter="8">
              <a-col :span="8">
                <a-statistic title="最佳部门" :value="departmentCompareStats.bestDept" />
              </a-col>
              <a-col :span="8">
                <a-statistic title="平均差异" :value="departmentCompareStats.avgDifference" :precision="1" suffix="分" />
              </a-col>
              <a-col :span="8">
                <a-statistic title="标准差" :value="departmentCompareStats.standardDeviation" :precision="2" />
              </a-col>
            </a-row>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 绩效分布统计 -->
    <a-card title="绩效分布分析" size="small" class="distribution-card">
      <template #extra>
        <a-space>
          <a-select v-model:value="distributionType" size="small" @change="handleDistributionTypeChange">
            <a-select-option value="score">分数分布</a-select-option>
            <a-select-option value="rank">排名分布</a-select-option>
            <a-select-option value="grade">等级分布</a-select-option>
          </a-select>
          <a-button size="small" @click="handleExportDistribution">
            <template #icon><Icon icon="ant-design:download-outlined" /></template>
            导出
          </a-button>
        </a-space>
      </template>

      <div class="distribution-content">
        <div ref="distributionChart" class="chart" style="height: 400px"></div>

        <!-- 分布统计表 -->
        <div class="distribution-table">
          <a-table :columns="distributionColumns" :data-source="distributionTableData" :pagination="false" size="small">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'percentage'">
                <a-progress :percent="record.percentage" size="small" :show-info="true" />
              </template>
            </template>
          </a-table>
        </div>
      </div>
    </a-card>

    <!-- 关联分析 -->
    <a-card title="绩效关联分析" size="small" class="correlation-card" v-if="analysisParams.analysisType === 'correlation'">
      <div class="correlation-content">
        <a-row :gutter="16">
          <a-col :span="12">
            <div class="correlation-chart">
              <div ref="correlationChart" style="height: 300px"></div>
            </div>
          </a-col>
          <a-col :span="12">
            <div class="correlation-matrix">
              <h4>相关性矩阵</h4>
              <a-table :columns="correlationColumns" :data-source="correlationData" :pagination="false" size="small">
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key !== 'factor'">
                    <span :class="getCorrelationClass(record[column.key])">
                      {{ record[column.key] }}
                    </span>
                  </template>
                </template>
              </a-table>
            </div>
          </a-col>
        </a-row>
      </div>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted, nextTick, watch } from 'vue';
  import { Icon } from '/@/components/Icon';
  import { useMessage } from '/@/hooks/web/useMessage';
  import * as echarts from 'echarts';

  interface AnalysisParams {
    timeRange: string;
    departmentId?: string;
    analysisType: string;
  }

  interface Department {
    id: string;
    name: string;
  }

  interface TrendStats {
    average: number;
    max: number;
    growth: number;
  }

  interface CompareStats {
    bestDept: string;
    avgDifference: number;
    standardDeviation: number;
  }

  const { createMessage } = useMessage();

  // 响应式数据
  const loading = ref(false);
  const analysisParams = reactive<AnalysisParams>({
    timeRange: '6months',
    departmentId: undefined,
    analysisType: 'trend',
  });

  const customTimeRange = ref();
  const distributionType = ref('score');

  // 图表引用
  const personalTrendChart = ref();
  const departmentCompareChart = ref();
  const distributionChart = ref();
  const correlationChart = ref();

  // 图表实例
  let personalTrendChartInstance: echarts.ECharts | null = null;
  let departmentCompareChartInstance: echarts.ECharts | null = null;
  let distributionChartInstance: echarts.ECharts | null = null;
  let correlationChartInstance: echarts.ECharts | null = null;

  // 数据
  const departments = ref<Department[]>([
    { id: '1', name: '技术部' },
    { id: '2', name: '产品部' },
    { id: '3', name: '运营部' },
    { id: '4', name: '市场部' },
  ]);

  const personalTrendData = ref([]);
  const departmentCompareData = ref([]);

  const personalTrendStats = reactive<TrendStats>({
    average: 0,
    max: 0,
    growth: 0,
  });

  const departmentCompareStats = reactive<CompareStats>({
    bestDept: '',
    avgDifference: 0,
    standardDeviation: 0,
  });

  // 分布分析表格列
  const distributionColumns = [
    { title: '分数区间', dataIndex: 'range', key: 'range', width: 120 },
    { title: '人数', dataIndex: 'count', key: 'count', width: 80, align: 'center' },
    { title: '占比', dataIndex: 'percentage', key: 'percentage', width: 150 },
    { title: '等级', dataIndex: 'grade', key: 'grade', width: 80, align: 'center' },
  ];

  const distributionTableData = ref([
    { range: '90-100', count: 15, percentage: 25, grade: 'A' },
    { range: '80-89', count: 25, percentage: 42, grade: 'B' },
    { range: '70-79', count: 18, percentage: 30, grade: 'C' },
    { range: '60-69', count: 2, percentage: 3, grade: 'D' },
  ]);

  // 关联分析数据
  const correlationColumns = [
    { title: '因子', dataIndex: 'factor', key: 'factor', width: 100 },
    { title: '绩效分数', dataIndex: 'performance', key: 'performance', width: 100, align: 'center' },
    { title: '工时完成率', dataIndex: 'workhour', key: 'workhour', width: 100, align: 'center' },
    { title: '考勤率', dataIndex: 'attendance', key: 'attendance', width: 100, align: 'center' },
  ];

  const correlationData = ref([
    { factor: '绩效分数', performance: '1.00', workhour: '0.75', attendance: '0.68' },
    { factor: '工时完成率', performance: '0.75', workhour: '1.00', attendance: '0.82' },
    { factor: '考勤率', performance: '0.68', workhour: '0.82', attendance: '1.00' },
  ]);

  // 生命周期
  onMounted(() => {
    initCharts();
    loadAnalysisData();
  });

  // 监听器
  watch(
    () => analysisParams.analysisType,
    () => {
      loadAnalysisData();
    },
  );

  // 方法
  const initCharts = async () => {
    await nextTick();

    // 初始化个人趋势图表
    if (personalTrendChart.value) {
      personalTrendChartInstance = echarts.init(personalTrendChart.value);
      initPersonalTrendChart();
    }

    // 初始化部门对比图表
    if (departmentCompareChart.value) {
      departmentCompareChartInstance = echarts.init(departmentCompareChart.value);
      initDepartmentCompareChart();
    }

    // 初始化分布图表
    if (distributionChart.value) {
      distributionChartInstance = echarts.init(distributionChart.value);
      initDistributionChart();
    }

    // 初始化关联分析图表
    if (correlationChart.value) {
      correlationChartInstance = echarts.init(correlationChart.value);
      initCorrelationChart();
    }

    // 监听窗口大小变化
    window.addEventListener('resize', handleResize);
  };

  const initPersonalTrendChart = () => {
    const option = {
      title: {
        text: '个人绩效趋势',
        left: 'center',
        textStyle: { fontSize: 14 },
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'cross' },
      },
      legend: {
        data: ['绩效分数', '目标分数'],
        bottom: 0,
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: ['2024-01', '2024-02', '2024-03', '2024-04', '2024-05', '2024-06'],
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 100,
      },
      series: [
        {
          name: '绩效分数',
          type: 'line',
          data: [75, 78, 82, 85, 88, 90],
          smooth: true,
          itemStyle: { color: '#1890ff' },
          areaStyle: { opacity: 0.3 },
        },
        {
          name: '目标分数',
          type: 'line',
          data: [80, 80, 80, 80, 80, 80],
          lineStyle: { type: 'dashed' },
          itemStyle: { color: '#52c41a' },
        },
      ],
    };

    personalTrendChartInstance?.setOption(option);
  };

  const initDepartmentCompareChart = () => {
    const option = {
      title: {
        text: '部门绩效对比',
        left: 'center',
        textStyle: { fontSize: 14 },
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'shadow' },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: ['技术部', '产品部', '运营部', '市场部'],
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 100,
      },
      series: [
        {
          name: '平均绩效',
          type: 'bar',
          data: [85, 78, 82, 75],
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#83bff6' },
              { offset: 0.5, color: '#188df0' },
              { offset: 1, color: '#188df0' },
            ]),
          },
        },
      ],
    };

    departmentCompareChartInstance?.setOption(option);
  };

  const initDistributionChart = () => {
    const option = {
      title: {
        text: '绩效分布统计',
        left: 'center',
        textStyle: { fontSize: 14 },
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)',
      },
      legend: {
        orient: 'vertical',
        left: 'left',
      },
      series: [
        {
          name: '绩效分布',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['60%', '50%'],
          data: [
            { value: 15, name: '优秀(90-100)', itemStyle: { color: '#52c41a' } },
            { value: 25, name: '良好(80-89)', itemStyle: { color: '#1890ff' } },
            { value: 18, name: '一般(70-79)', itemStyle: { color: '#faad14' } },
            { value: 2, name: '待改进(60-69)', itemStyle: { color: '#ff4d4f' } },
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
        },
      ],
    };

    distributionChartInstance?.setOption(option);
  };

  const initCorrelationChart = () => {
    const option = {
      title: {
        text: '绩效关联分析',
        left: 'center',
        textStyle: { fontSize: 14 },
      },
      tooltip: {
        trigger: 'axis',
      },
      legend: {
        data: ['绩效分数', '工时完成率', '考勤率'],
        bottom: 0,
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: ['张三', '李四', '王五', '赵六', '钱七', '孙八'],
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 100,
      },
      series: [
        {
          name: '绩效分数',
          type: 'line',
          data: [85, 78, 92, 75, 88, 82],
          itemStyle: { color: '#1890ff' },
        },
        {
          name: '工时完成率',
          type: 'line',
          data: [90, 85, 95, 80, 92, 88],
          itemStyle: { color: '#52c41a' },
        },
        {
          name: '考勤率',
          type: 'line',
          data: [95, 92, 98, 88, 96, 94],
          itemStyle: { color: '#faad14' },
        },
      ],
    };

    correlationChartInstance?.setOption(option);
  };

  const loadAnalysisData = async () => {
    loading.value = true;

    try {
      // 模拟数据加载
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 更新统计数据
      personalTrendStats.average = 82.5;
      personalTrendStats.max = 90;
      personalTrendStats.growth = 12.5;

      departmentCompareStats.bestDept = '技术部';
      departmentCompareStats.avgDifference = 7.5;
      departmentCompareStats.standardDeviation = 4.2;

      // 更新图表数据
      personalTrendData.value = [75, 78, 82, 85, 88, 90];
      departmentCompareData.value = [85, 78, 82, 75];
    } catch (error) {
      createMessage.error('加载分析数据失败');
    } finally {
      loading.value = false;
    }
  };

  const handleResize = () => {
    personalTrendChartInstance?.resize();
    departmentCompareChartInstance?.resize();
    distributionChartInstance?.resize();
    correlationChartInstance?.resize();
  };

  // 事件处理
  const handleTimeRangeChange = () => {
    loadAnalysisData();
  };

  const handleDepartmentChange = () => {
    loadAnalysisData();
  };

  const handleAnalysisTypeChange = () => {
    loadAnalysisData();
  };

  const handleCustomTimeChange = () => {
    loadAnalysisData();
  };

  const handleRefreshAnalysis = () => {
    loadAnalysisData();
  };

  const handleDistributionTypeChange = () => {
    initDistributionChart();
  };

  const handlePersonalTrendAction = ({ key }) => {
    switch (key) {
      case 'export':
        exportChart(personalTrendChartInstance, '个人绩效趋势');
        break;
      case 'fullscreen':
        // 全屏显示逻辑
        break;
      case 'settings':
        // 图表设置逻辑
        break;
    }
  };

  const handleDepartmentCompareAction = ({ key }) => {
    switch (key) {
      case 'export':
        exportChart(departmentCompareChartInstance, '部门绩效对比');
        break;
      case 'fullscreen':
        // 全屏显示逻辑
        break;
      case 'settings':
        // 图表设置逻辑
        break;
    }
  };

  const handleExportDistribution = () => {
    exportChart(distributionChartInstance, '绩效分布分析');
  };

  const exportChart = (chartInstance: echarts.ECharts | null, filename: string) => {
    if (!chartInstance) return;

    const url = chartInstance.getDataURL({
      type: 'png',
      pixelRatio: 2,
      backgroundColor: '#fff',
    });

    const link = document.createElement('a');
    link.href = url;
    link.download = `${filename}.png`;
    link.click();

    createMessage.success('图表导出成功');
  };

  const getCorrelationClass = (value: string) => {
    const num = parseFloat(value);
    if (num >= 0.7) return 'correlation-high';
    if (num >= 0.4) return 'correlation-medium';
    return 'correlation-low';
  };
</script>

<style lang="less" scoped>
  // 响应式设计
  @media (max-width: 1200px) {
    .performance-analytics {
      .charts-row {
        .chart-card {
          margin-bottom: 16px;
        }
      }
    }
  }

  @media (max-width: 768px) {
    .performance-analytics {
      .control-panel {
        :deep(.ant-row) {
          .ant-col {
            margin-bottom: 8px;
          }
        }
      }

      .charts-row {
        .ant-col {
          margin-bottom: 16px;
        }
      }

      .chart-card {
        .chart {
          height: 250px !important;
        }
      }

      .distribution-card {
        .chart {
          height: 300px !important;
        }
      }
    }
  }
  .performance-analytics {
    .control-panel {
      margin-bottom: 16px;

      .panel-title {
        display: flex;
        align-items: center;
        gap: 8px;

        .title-icon {
          color: #1890ff;
          font-size: 16px;
        }
      }

      .custom-time-range {
        margin-top: 16px;
        padding-top: 16px;
        border-top: 1px solid #f0f0f0;
      }
    }

    .charts-row {
      margin-bottom: 16px;
    }

    .chart-card,
    .distribution-card,
    .correlation-card {
      margin-bottom: 16px;

      .chart-container {
        position: relative;

        .chart {
          width: 100%;
        }

        .chart-empty {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          z-index: 10;
        }
      }

      .trend-indicators,
      .compare-indicators {
        margin-top: 16px;
        padding-top: 16px;
        border-top: 1px solid #f0f0f0;
      }
    }

    .distribution-content {
      .distribution-table {
        margin-top: 20px;
      }
    }

    .correlation-content {
      .correlation-matrix {
        h4 {
          margin-bottom: 16px;
          font-size: 14px;
          color: #333;
        }

        .correlation-high {
          color: #52c41a;
          font-weight: bold;
        }

        .correlation-medium {
          color: #faad14;
          font-weight: bold;
        }

        .correlation-low {
          color: #ff4d4f;
          font-weight: bold;
        }
      }
    }
  }
</style>
