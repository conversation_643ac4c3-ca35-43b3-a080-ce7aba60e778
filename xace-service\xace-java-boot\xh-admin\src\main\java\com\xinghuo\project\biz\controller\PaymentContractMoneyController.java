package com.xinghuo.project.biz.controller;


import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.controller.BaseController;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.constant.MsgCode;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.project.biz.entity.PaymentContractMoneyEntity;
import com.xinghuo.project.biz.model.paymentContractMoney.PaymentContractMoneyForm;
import com.xinghuo.project.biz.model.paymentContractMoney.PaymentContractMoneyPagination;
import com.xinghuo.project.biz.model.paymentContractMoney.PaymentContractMoneyStatusForm;
import com.xinghuo.project.biz.model.paymentContractMoney.PaymentContractMoneyVO;
import com.xinghuo.project.biz.model.paymentContractMoney.PaymentContractMoneyStats;
import com.xinghuo.project.biz.service.PaymentContractMoneyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 采购付款计划管理
 */
@Slf4j
@RestController
@Tag(name = "采购付款计划管理", description = "PaymentContractMoney")
@RequestMapping("/api/project/biz/paymentContract/money")
public class PaymentContractMoneyController extends BaseController<PaymentContractMoneyService, PaymentContractMoneyEntity> {

    @Autowired
    private PaymentContractMoneyService paycontractMoneyService;

    /**
     * 获取采购付款计划列表
     *
     * @param pagination 分页查询参数
     * @return 采购付款计划列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取采购付款计划列表")
    public ActionResult list(@RequestBody PaymentContractMoneyPagination pagination) {
        List<PaymentContractMoneyEntity> list = paycontractMoneyService.getList(pagination);
        List<PaymentContractMoneyVO> listVOs = BeanCopierUtils.copyList(list, PaymentContractMoneyVO.class);

        // 填充关联的采购合同信息
        paycontractMoneyService.fillRelatedInfo(listVOs);

        PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
        return ActionResult.page(listVOs,page);
    }

    /**
     * 根据采购合同ID获取付款计划列表
     *
     * @param paycontractId 采购合同ID
     * @return 付款计划列表
     */
    @GetMapping("/paycontract/{paycontractId}")
    @Operation(summary = "根据采购合同ID获取付款计划列表")
    @Parameters({
            @Parameter(name = "paycontractId", description = "采购合同ID", required = true),
    })
    public ActionResult<List<PaymentContractMoneyVO>> listByPaycontractId(@PathVariable("paycontractId") String paycontractId) {
        List<PaymentContractMoneyEntity> list = paycontractMoneyService.getListByPaycontractId(paycontractId);
        List<PaymentContractMoneyVO> listVOs = BeanCopierUtils.copyList(list, PaymentContractMoneyVO.class);
        return ActionResult.success(listVOs);
    }

    /**
     * 获取采购付款计划详情
     *
     * @param id 采购付款计划ID
     * @return 采购付款计划详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取采购付款计划详情")
    @Parameters({
            @Parameter(name = "id", description = "采购付款计划ID", required = true),
    })
    public ActionResult<PaymentContractMoneyVO> info(@PathVariable("id") String id) {
        PaymentContractMoneyEntity entity = paycontractMoneyService.getInfo(id);
        PaymentContractMoneyVO vo = BeanCopierUtils.copy(entity, PaymentContractMoneyVO.class);

        // 填充关联信息
        List<PaymentContractMoneyVO> voList = new ArrayList<>();
        voList.add(vo);
        paycontractMoneyService.fillRelatedInfo(voList);

        return ActionResult.success(vo);
    }

    /**
     * 创建采购付款计划
     *
     * @param moneyForm 采购付款计划表单
     * @return 操作结果
     */
    @PostMapping
    @Operation(summary = "创建采购付款计划")
    @Parameters({
            @Parameter(name = "moneyForm", description = "采购付款计划表单", required = true),
    })
    public ActionResult create(@RequestBody @Valid PaymentContractMoneyForm moneyForm) {
        PaymentContractMoneyEntity entity = BeanCopierUtils.copy(moneyForm, PaymentContractMoneyEntity.class);
        paycontractMoneyService.create(entity);
        return ActionResult.success(MsgCode.SU001.get());
    }

    /**
     * 更新采购付款计划
     *
     * @param id        采购付款计划ID
     * @param moneyForm 采购付款计划表单
     * @return 操作结果
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新采购付款计划")
    @Parameters({
            @Parameter(name = "id", description = "采购付款计划ID", required = true),
            @Parameter(name = "moneyForm", description = "采购付款计划表单", required = true),
    })
    public ActionResult update(@PathVariable("id") String id, @RequestBody @Valid PaymentContractMoneyForm moneyForm) {
        try {
            PaymentContractMoneyEntity entity = BeanCopierUtils.copy(moneyForm, PaymentContractMoneyEntity.class);
            paycontractMoneyService.update(id, entity);
            return ActionResult.success(MsgCode.SU002.get());
        } catch (Exception e) {
            return ActionResult.fail(e.getMessage());
        }
    }

    /**
     * 删除采购付款计划
     *
     * @param id 采购付款计划ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除采购付款计划")
    @Parameters({
            @Parameter(name = "id", description = "采购付款计划ID", required = true),
    })
    public ActionResult delete(@PathVariable("id") String id) {
        try {
            paycontractMoneyService.delete(id);
            return ActionResult.success(MsgCode.SU003.get());
        } catch (Exception e) {
            return ActionResult.fail(e.getMessage());
        }
    }

    /**
     * 更新付款状态
     *
     * @param id         采购付款计划ID
     * @param statusForm 状态更新表单
     * @return 操作结果
     */
    @PutMapping("/{id}/status")
    @Operation(summary = "更新付款状态")
    @Parameters({
            @Parameter(name = "id", description = "采购付款计划ID", required = true),
            @Parameter(name = "statusForm", description = "状态更新表单", required = true),
    })
    public ActionResult updateStatus(@PathVariable("id") String id, @RequestBody @Valid PaymentContractMoneyStatusForm statusForm) {
        paycontractMoneyService.updateStatus(id, statusForm);
        return ActionResult.success(MsgCode.SU002.get());
    }

    /**
     * 登记付款
     *
     * @param id           采购付款计划ID
     * @param fukuanDate   付款日期
     * @param lastNote     备注
     * @return 操作结果
     */
    @PutMapping("/{id}/payment")
    @Operation(summary = "登记付款")
    @Parameters({
            @Parameter(name = "id", description = "采购付款计划ID", required = true),
            @Parameter(name = "fukuanDate", description = "付款日期", required = true),
            @Parameter(name = "lastNote", description = "备注"),
    })
    public ActionResult registerPayment(
            @PathVariable("id") String id,
            @RequestParam("fukuanDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date fukuanDate,
            @RequestParam(value = "lastNote", required = false) String lastNote) {
        paycontractMoneyService.registerPayment(id, fukuanDate, lastNote);
        return ActionResult.success(MsgCode.SU002.get());
    }

    /**
     * 获取付款计划统计信息
     *
     * @param paycontractId 采购合同ID（可选）
     * @return 统计信息
     */
    @GetMapping("/stats")
    @Operation(summary = "获取付款计划统计信息")
    @Parameters({
            @Parameter(name = "paycontractId", description = "采购合同ID", required = false),
    })
    public ActionResult<PaymentContractMoneyStats> getStats(
            @RequestParam(value = "paycontractId", required = false) String paycontractId) {
        PaymentContractMoneyStats stats = paycontractMoneyService.getStats(paycontractId);
        return ActionResult.success(stats);
    }
}
