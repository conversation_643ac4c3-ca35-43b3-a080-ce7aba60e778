package com.xinghuo.project.biz.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 项目商机周报实体类
 * 对应数据库表：zz_proj_business_weeklog_v2
 * 
 * <AUTHOR>
 * @date 2025-07-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("zz_proj_business_weeklog_v2")
public class BizBusinessWeeklogEntity extends BaseEntityV2.CUBaseEntityV2<String> {

    /**
     * 所属分部
     */
    @TableField("fb_id")
    private String fbId;

    /**
     * 项目类型
     */
    @TableField("proj_type")
    private String projType;

    /**
     * 项目ID
     */
    @TableField("proj_id")
    private String projId;

    /**
     * 项目名称
     */
    @TableField("proj_name")
    private String projName;

    /**
     * 项目备注
     */
    @TableField("proj_note")
    private String projNote;

    /**
     * 项目级别
     */
    @TableField("project_level")
    private String projectLevel;

    /**
     * 开始日期
     */
    @TableField("start_date")
    private Date startDate;

    /**
     * 结束日期
     */
    @TableField("end_date")
    private Date endDate;

    /**
     * 录入日期
     */
    @TableField("input_date")
    private Date inputDate;

    /**
     * 负责人ID
     */
    @TableField("own_id")
    private String ownId;

    /**
     * 备注
     */
    @TableField("note")
    private String note;

    /**
     * 计划
     */
    @TableField("plan")
    private String plan;

    /**
     * 风险
     */
    @TableField("risk")
    private String risk;

    /**
     * 状态 (1-表示已填写，0-未填写，2-提交审核，3-已发布，-1 -已驳回)
     */
    @TableField("status")
    private Integer status;

    /**
     * 显示状态 (0-表示未显示，1-表示显示)
     */
    @TableField("show_status")
    private Integer showStatus;
}
