package com.xinghuo.manhour.service;

import com.xinghuo.manhour.model.completion.*;

import jakarta.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 工时填写情况分析服务接口
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
public interface ManhourCompletionService {

    /**
     * 获取工时填写情况概览数据
     *
     * @param params 查询参数
     * @return 概览数据
     */
    WorkhourCompletionOverview getOverview(WorkhourCompletionParams params);

    /**
     * 获取工时填写情况图表数据
     *
     * @param params 查询参数
     * @return 图表数据
     */
    CompletionChartDataModel getCharts(WorkhourCompletionParams params);

    /**
     * 获取未填写人员列表
     *
     * @param pagination 分页参数
     * @return 未填写人员数据列表
     */
    List<UnfilledUserVO> getUnfilledUsersList(WorkhourCompletionPagination pagination);

    /**
     * 获取待审批记录列表
     *
     * @param pagination 分页参数
     * @return 待审批记录数据列表
     */
    List<PendingApprovalVO> getPendingApprovalList(WorkhourCompletionPagination pagination);

    /**
     * 获取分部完成情况列表
     *
     * @param pagination 分页参数
     * @return 分部完成情况数据列表
     */
    List<DepartmentCompletionVO> getDepartmentCompletionList(WorkhourCompletionPagination pagination);

    /**
     * 获取负责人统计列表
     *
     * @param pagination 分页参数
     * @return 负责人统计数据列表
     */
    List<LeaderStatisticsVO> getLeaderStatisticsList(WorkhourCompletionPagination pagination);

    /**
     * 导出工时填写情况报表
     *
     * @param params   查询参数
     * @param response HTTP响应
     */
    void exportWorkhourCompletion(WorkhourCompletionParams params, HttpServletResponse response);

    /**
     * 发送用户提醒
     *
     * @param userId 用户ID
     * @param month  月份
     */
    void notifyUser(String userId, String month);

    /**
     * 发送负责人提醒
     *
     * @param leaderId 负责人ID
     */
    void notifyLeader(String leaderId);

    /**
     * 批量发送用户提醒
     *
     * @param userIds 用户ID列表
     * @param month   月份
     */
    void batchNotifyUsers(List<String> userIds, String month);

    /**
     * 获取分部选择器数据
     *
     * @return 分部列表
     */
    List<Map<String, Object>> getDepartmentSelector();

    /**
     * 获取填写状态统计
     *
     * @param params 查询参数
     * @return 填写状态统计
     */
    List<Map<String, Object>> getCompletionStatusStatistics(WorkhourCompletionParams params);

    /**
     * 获取审批效率统计
     *
     * @param params 查询参数
     * @return 审批效率统计
     */
    List<Map<String, Object>> getApprovalEfficiencyStatistics(WorkhourCompletionParams params);

    /**
     * 获取逾期统计
     *
     * @param params 查询参数
     * @return 逾期统计
     */
    List<Map<String, Object>> getOverdueStatistics(WorkhourCompletionParams params);
}
