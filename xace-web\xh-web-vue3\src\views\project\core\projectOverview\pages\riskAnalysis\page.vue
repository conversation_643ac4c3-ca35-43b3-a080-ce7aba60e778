<template>
  <div class="risk-analysis p-4">
    <!-- 页面标题 -->
    <div class="page-header mb-4">
      <h2 class="text-xl font-semibold mb-2">风险分析</h2>
      <p class="text-gray-600">识别、评估和监控项目执行过程中的各类风险</p>
    </div>

    <!-- 风险概览仪表盘 -->
    <div class="risk-dashboard grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <div class="dashboard-card bg-white rounded-lg shadow-sm border p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 mb-1">风险总数</p>
            <p class="text-2xl font-bold text-blue-600">{{ riskDashboard.totalRisks }}</p>
          </div>
          <div class="dashboard-icon bg-blue-100 p-3 rounded-full">
            <ExclamationCircleOutlined class="text-blue-600 text-xl" />
          </div>
        </div>
        <div class="mt-3">
          <span class="text-sm text-green-600">↓ {{ riskDashboard.totalChange }}%</span>
          <span class="text-sm text-gray-500 ml-1">较上月</span>
        </div>
      </div>

      <div class="dashboard-card bg-white rounded-lg shadow-sm border p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 mb-1">高风险项</p>
            <p class="text-2xl font-bold text-red-600">{{ riskDashboard.highRisks }}</p>
          </div>
          <div class="dashboard-icon bg-red-100 p-3 rounded-full">
            <WarningOutlined class="text-red-600 text-xl" />
          </div>
        </div>
        <div class="mt-3">
          <span class="text-sm text-red-600">{{ Math.round((riskDashboard.highRisks / riskDashboard.totalRisks) * 100) }}%</span>
          <span class="text-sm text-gray-500 ml-1">占比</span>
        </div>
      </div>

      <div class="dashboard-card bg-white rounded-lg shadow-sm border p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 mb-1">已处理</p>
            <p class="text-2xl font-bold text-green-600">{{ riskDashboard.resolvedRisks }}</p>
          </div>
          <div class="dashboard-icon bg-green-100 p-3 rounded-full">
            <CheckCircleOutlined class="text-green-600 text-xl" />
          </div>
        </div>
        <div class="mt-3">
          <span class="text-sm text-green-600">{{ Math.round((riskDashboard.resolvedRisks / riskDashboard.totalRisks) * 100) }}%</span>
          <span class="text-sm text-gray-500 ml-1">处理率</span>
        </div>
      </div>

      <div class="dashboard-card bg-white rounded-lg shadow-sm border p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 mb-1">风险评分</p>
            <p class="text-2xl font-bold text-orange-600">{{ riskDashboard.riskScore }}</p>
          </div>
          <div class="dashboard-icon bg-orange-100 p-3 rounded-full">
            <DashboardOutlined class="text-orange-600 text-xl" />
          </div>
        </div>
        <div class="mt-3">
          <span class="text-sm" :class="riskDashboard.riskScore <= 60 ? 'text-green-600' : riskDashboard.riskScore <= 80 ? 'text-orange-600' : 'text-red-600'">
            {{ getRiskLevelText(riskDashboard.riskScore) }}
          </span>
        </div>
      </div>
    </div>

    <!-- 风险分布和趋势 -->
    <div class="risk-charts grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
      <!-- 风险等级分布 -->
      <div class="chart-card bg-white rounded-lg shadow-sm border p-4">
        <div class="chart-header mb-4">
          <h3 class="text-lg font-medium">风险等级分布</h3>
          <p class="text-sm text-gray-500">各等级风险的数量和占比</p>
        </div>
        <div class="chart-content" style="height: 300px">
          <div class="risk-distribution">
            <div class="distribution-center mb-6 text-center">
              <div class="total-risks text-3xl font-bold text-gray-800 mb-1">{{ riskDashboard.totalRisks }}</div>
              <div class="text-sm text-gray-500">风险总数</div>
            </div>

            <div class="distribution-items">
              <div v-for="(item, index) in riskDistribution" :key="index" class="distribution-item flex items-center justify-between mb-4">
                <div class="flex items-center">
                  <div class="risk-dot w-4 h-4 rounded-full mr-3" :class="getRiskDotClass(item.level)"></div>
                  <span class="font-medium">{{ item.label }}</span>
                </div>
                <div class="flex items-center">
                  <span class="text-lg font-bold mr-3" :class="getRiskTextClass(item.level)">{{ item.count }}</span>
                  <div class="progress-bar bg-gray-200 w-20 h-3 rounded-full overflow-hidden">
                    <div class="h-full rounded-full" :class="getRiskBarClass(item.level)" :style="{ width: item.percentage + '%' }"></div>
                  </div>
                  <span class="text-sm text-gray-500 ml-2">{{ item.percentage }}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 风险趋势分析 -->
      <div class="chart-card bg-white rounded-lg shadow-sm border p-4">
        <div class="chart-header mb-4">
          <h3 class="text-lg font-medium">风险趋势分析</h3>
          <p class="text-sm text-gray-500">最近6个月的风险变化趋势</p>
        </div>
        <div class="chart-content" style="height: 300px">
          <div class="trend-chart">
            <div class="chart-legend mb-4 flex justify-center">
              <div class="legend-item flex items-center mr-6">
                <div class="w-3 h-0.5 bg-red-500 mr-2"></div>
                <span class="text-sm">高风险</span>
              </div>
              <div class="legend-item flex items-center mr-6">
                <div class="w-3 h-0.5 bg-yellow-500 mr-2"></div>
                <span class="text-sm">中风险</span>
              </div>
              <div class="legend-item flex items-center">
                <div class="w-3 h-0.5 bg-green-500 mr-2"></div>
                <span class="text-sm">低风险</span>
              </div>
            </div>

            <div class="trend-data">
              <div v-for="(month, index) in riskTrend" :key="index" class="trend-month mb-4">
                <div class="month-header flex justify-between items-center mb-2">
                  <span class="text-sm font-medium">{{ month.month }}</span>
                  <span class="text-xs text-gray-500">总计: {{ month.total }}</span>
                </div>

                <div class="trend-bars">
                  <div class="bar-container flex items-center mb-1">
                    <span class="bar-label text-xs text-gray-600 w-12">高</span>
                    <div class="bar-bg bg-gray-200 flex-1 h-2 rounded-full mr-2">
                      <div class="bar-fill bg-red-500 h-full rounded-full" :style="{ width: (month.high / 10) * 100 + '%' }"></div>
                    </div>
                    <span class="bar-value text-xs w-6">{{ month.high }}</span>
                  </div>

                  <div class="bar-container flex items-center mb-1">
                    <span class="bar-label text-xs text-gray-600 w-12">中</span>
                    <div class="bar-bg bg-gray-200 flex-1 h-2 rounded-full mr-2">
                      <div class="bar-fill bg-yellow-500 h-full rounded-full" :style="{ width: (month.medium / 10) * 100 + '%' }"></div>
                    </div>
                    <span class="bar-value text-xs w-6">{{ month.medium }}</span>
                  </div>

                  <div class="bar-container flex items-center">
                    <span class="bar-label text-xs text-gray-600 w-12">低</span>
                    <div class="bar-bg bg-gray-200 flex-1 h-2 rounded-full mr-2">
                      <div class="bar-fill bg-green-500 h-full rounded-full" :style="{ width: (month.low / 10) * 100 + '%' }"></div>
                    </div>
                    <span class="bar-value text-xs w-6">{{ month.low }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 风险类别分析和关键风险 -->
    <div class="risk-details grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
      <!-- 风险类别分析 -->
      <div class="category-card bg-white rounded-lg shadow-sm border p-4">
        <div class="card-header mb-4">
          <h3 class="text-lg font-medium">风险类别分析</h3>
          <p class="text-sm text-gray-500">不同类别风险的分布情况</p>
        </div>
        <div class="category-content">
          <div v-for="(category, index) in riskCategories" :key="index" class="category-item mb-4">
            <div class="category-header flex items-center justify-between mb-2">
              <div class="flex items-center">
                <div class="category-icon w-8 h-8 rounded-full flex items-center justify-center mr-3" :class="getCategoryIconClass(category.type)">
                  <component :is="getCategoryIcon(category.type)" class="text-sm" />
                </div>
                <span class="font-medium">{{ category.name }}</span>
              </div>
              <span class="text-sm font-bold">{{ category.count }}</span>
            </div>

            <div class="category-progress mb-2">
              <a-progress :percent="category.percentage" :show-info="false" size="small" :stroke-color="getCategoryColor(category.type)" />
            </div>

            <div class="category-details flex justify-between text-xs text-gray-500">
              <span>高风险: {{ category.high }}</span>
              <span>中风险: {{ category.medium }}</span>
              <span>低风险: {{ category.low }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 关键风险监控 -->
      <div class="key-risks-card bg-white rounded-lg shadow-sm border p-4">
        <div class="card-header mb-4">
          <h3 class="text-lg font-medium">关键风险监控</h3>
          <p class="text-sm text-gray-500">需要重点关注的高风险项目</p>
        </div>
        <div class="key-risks-content">
          <div v-for="(risk, index) in keyRisks" :key="index" class="risk-item mb-4 last:mb-0">
            <div class="risk-header flex items-start justify-between mb-2">
              <div class="flex items-start">
                <div class="risk-level w-3 h-3 rounded-full mr-3 mt-1" :class="getRiskLevelClass(risk.level)"></div>
                <div class="risk-info flex-1">
                  <h4 class="text-sm font-medium">{{ risk.title }}</h4>
                  <p class="text-xs text-gray-600 mt-1">{{ risk.description }}</p>
                </div>
              </div>
              <div class="risk-status">
                <a-tag :color="getRiskStatusColor(risk.status)" size="small">
                  {{ risk.status }}
                </a-tag>
              </div>
            </div>

            <div class="risk-metrics grid grid-cols-3 gap-2 text-xs">
              <div class="metric-item text-center p-2 bg-gray-50 rounded">
                <div class="metric-value font-bold text-red-600">{{ risk.impact }}</div>
                <div class="metric-label text-gray-500">影响度</div>
              </div>
              <div class="metric-item text-center p-2 bg-gray-50 rounded">
                <div class="metric-value font-bold text-orange-600">{{ risk.probability }}%</div>
                <div class="metric-label text-gray-500">发生概率</div>
              </div>
              <div class="metric-item text-center p-2 bg-gray-50 rounded">
                <div class="metric-value font-bold text-blue-600">{{ risk.daysLeft }}</div>
                <div class="metric-label text-gray-500">剩余天数</div>
              </div>
            </div>

            <div class="risk-actions mt-3 flex justify-end">
              <a-space size="small">
                <a-button type="link" size="small" @click="handleViewRisk(risk)"> 查看详情 </a-button>
                <a-button type="link" size="small" @click="handleUpdateRisk(risk)"> 更新状态 </a-button>
              </a-space>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 风险应对措施 -->
    <div class="risk-response bg-white rounded-lg shadow-sm border p-4">
      <div class="response-header mb-4">
        <h3 class="text-lg font-medium">风险应对措施</h3>
        <p class="text-sm text-gray-500">针对识别出的风险制定的应对策略和措施</p>
      </div>

      <div class="response-content">
        <div class="response-tabs mb-4">
          <a-tabs v-model:activeKey="activeResponseTab" type="card">
            <a-tab-pane key="prevention" tab="预防措施">
              <div class="prevention-measures">
                <div v-for="(measure, index) in preventionMeasures" :key="index" class="measure-item mb-4">
                  <div class="measure-header flex items-center justify-between mb-2">
                    <h4 class="text-sm font-medium">{{ measure.title }}</h4>
                    <a-tag :color="getMeasureStatusColor(measure.status)" size="small">
                      {{ measure.status }}
                    </a-tag>
                  </div>
                  <p class="text-xs text-gray-600 mb-2">{{ measure.description }}</p>
                  <div class="measure-progress">
                    <div class="flex justify-between items-center mb-1">
                      <span class="text-xs text-gray-500">执行进度</span>
                      <span class="text-xs">{{ measure.progress }}%</span>
                    </div>
                    <a-progress :percent="measure.progress" :show-info="false" size="small" />
                  </div>
                </div>
              </div>
            </a-tab-pane>

            <a-tab-pane key="mitigation" tab="缓解措施">
              <div class="mitigation-measures">
                <div v-for="(measure, index) in mitigationMeasures" :key="index" class="measure-item mb-4">
                  <div class="measure-header flex items-center justify-between mb-2">
                    <h4 class="text-sm font-medium">{{ measure.title }}</h4>
                    <a-tag :color="getMeasureStatusColor(measure.status)" size="small">
                      {{ measure.status }}
                    </a-tag>
                  </div>
                  <p class="text-xs text-gray-600 mb-2">{{ measure.description }}</p>
                  <div class="measure-metrics grid grid-cols-2 gap-4 text-xs">
                    <div>
                      <span class="text-gray-500">负责人: </span>
                      <span>{{ measure.owner }}</span>
                    </div>
                    <div>
                      <span class="text-gray-500">截止日期: </span>
                      <span>{{ measure.deadline }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </a-tab-pane>

            <a-tab-pane key="contingency" tab="应急预案">
              <div class="contingency-plans">
                <div v-for="(plan, index) in contingencyPlans" :key="index" class="plan-item mb-4">
                  <div class="plan-header flex items-center justify-between mb-2">
                    <h4 class="text-sm font-medium">{{ plan.title }}</h4>
                    <a-tag :color="getPlanStatusColor(plan.status)" size="small">
                      {{ plan.status }}
                    </a-tag>
                  </div>
                  <p class="text-xs text-gray-600 mb-2">{{ plan.description }}</p>
                  <div class="plan-details text-xs text-gray-500">
                    <div>触发条件: {{ plan.trigger }}</div>
                    <div>执行时间: {{ plan.executionTime }}</div>
                  </div>
                </div>
              </div>
            </a-tab-pane>
          </a-tabs>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { Progress, Tag, Button, Space, Tabs } from 'ant-design-vue';
  import {
    ExclamationCircleOutlined,
    WarningOutlined,
    CheckCircleOutlined,
    DashboardOutlined,
    TeamOutlined,
    DollarOutlined,
    ClockCircleOutlined,
    SettingOutlined,
    BugOutlined,
  } from '@ant-design/icons-vue';
  import { useMessage } from '/@/hooks/web/useMessage';

  const props = defineProps({
    projectId: {
      type: String,
      default: '',
    },
  });

  const { createMessage } = useMessage();

  const activeResponseTab = ref('prevention');

  // 风险仪表盘数据
  const riskDashboard = ref({
    totalRisks: 28,
    highRisks: 5,
    resolvedRisks: 18,
    riskScore: 72,
    totalChange: -8.5,
  });

  // 风险分布数据
  const riskDistribution = ref([
    { level: 'high', label: '高风险', count: 5, percentage: 18 },
    { level: 'medium', label: '中风险', count: 12, percentage: 43 },
    { level: 'low', label: '低风险', count: 11, percentage: 39 },
  ]);

  // 风险趋势数据
  const riskTrend = ref([
    { month: '2023-12', high: 3, medium: 8, low: 6, total: 17 },
    { month: '2024-01', high: 4, medium: 9, low: 8, total: 21 },
    { month: '2024-02', high: 6, medium: 10, low: 9, total: 25 },
    { month: '2024-03', high: 5, medium: 11, low: 10, total: 26 },
    { month: '2024-04', high: 4, medium: 12, low: 11, total: 27 },
    { month: '2024-05', high: 5, medium: 12, low: 11, total: 28 },
  ]);

  // 风险类别数据
  const riskCategories = ref([
    { type: 'technical', name: '技术风险', count: 8, percentage: 29, high: 2, medium: 4, low: 2 },
    { type: 'schedule', name: '进度风险', count: 7, percentage: 25, high: 1, medium: 3, low: 3 },
    { type: 'resource', name: '资源风险', count: 6, percentage: 21, high: 1, medium: 2, low: 3 },
    { type: 'financial', name: '财务风险', count: 4, percentage: 14, high: 1, medium: 2, low: 1 },
    { type: 'external', name: '外部风险', count: 3, percentage: 11, high: 0, medium: 1, low: 2 },
  ]);

  // 关键风险数据
  const keyRisks = ref([
    {
      title: '核心开发人员离职风险',
      description: '项目核心开发人员可能在关键阶段离职，影响项目进度',
      level: 'high',
      status: '监控中',
      impact: '高',
      probability: 35,
      daysLeft: 15,
    },
    {
      title: '第三方接口延期风险',
      description: '关键第三方接口开发进度滞后，可能影响系统集成',
      level: 'high',
      status: '处理中',
      impact: '中',
      probability: 60,
      daysLeft: 8,
    },
    {
      title: '客户需求变更风险',
      description: '客户可能在开发后期提出重大需求变更',
      level: 'medium',
      status: '已识别',
      impact: '中',
      probability: 45,
      daysLeft: 30,
    },
  ]);

  // 预防措施数据
  const preventionMeasures = ref([
    {
      title: '建立人员备份机制',
      description: '为核心岗位培养备份人员，降低人员流失风险',
      status: '执行中',
      progress: 75,
    },
    {
      title: '加强供应商管理',
      description: '建立供应商评估和监控机制，确保交付质量',
      status: '已完成',
      progress: 100,
    },
  ]);

  // 缓解措施数据
  const mitigationMeasures = ref([
    {
      title: '增加开发资源投入',
      description: '临时增加开发人员，加快项目进度',
      status: '执行中',
      owner: '张三',
      deadline: '2024-06-15',
    },
    {
      title: '优化项目计划',
      description: '调整项目里程碑，合理分配资源',
      status: '计划中',
      owner: '李四',
      deadline: '2024-06-10',
    },
  ]);

  // 应急预案数据
  const contingencyPlans = ref([
    {
      title: '人员紧急替换预案',
      description: '核心人员离职时的紧急替换和知识转移方案',
      status: '已准备',
      trigger: '核心人员提出离职',
      executionTime: '24小时内',
    },
    {
      title: '进度延期应对预案',
      description: '项目进度严重滞后时的应对措施',
      status: '已准备',
      trigger: '进度延期超过2周',
      executionTime: '48小时内',
    },
  ]);

  onMounted(async () => {
    await loadRiskAnalysisData();
  });

  const loadRiskAnalysisData = async () => {
    try {
      // 这里可以调用实际的API获取数据
      console.log('风险分析数据加载完成');
    } catch (error) {
      console.error('加载风险分析数据失败:', error);
    }
  };

  // 工具函数
  const getRiskLevelText = (score: number) => {
    if (score <= 60) return '低风险';
    if (score <= 80) return '中风险';
    return '高风险';
  };

  const getRiskDotClass = (level: string) => {
    const classes = {
      high: 'bg-red-500',
      medium: 'bg-yellow-500',
      low: 'bg-green-500',
    };
    return classes[level] || 'bg-gray-500';
  };

  const getRiskTextClass = (level: string) => {
    const classes = {
      high: 'text-red-600',
      medium: 'text-yellow-600',
      low: 'text-green-600',
    };
    return classes[level] || 'text-gray-600';
  };

  const getRiskBarClass = (level: string) => {
    const classes = {
      high: 'bg-red-500',
      medium: 'bg-yellow-500',
      low: 'bg-green-500',
    };
    return classes[level] || 'bg-gray-500';
  };

  const getCategoryIconClass = (type: string) => {
    const classes = {
      technical: 'bg-blue-100 text-blue-600',
      schedule: 'bg-orange-100 text-orange-600',
      resource: 'bg-purple-100 text-purple-600',
      financial: 'bg-green-100 text-green-600',
      external: 'bg-gray-100 text-gray-600',
    };
    return classes[type] || 'bg-gray-100 text-gray-600';
  };

  const getCategoryIcon = (type: string) => {
    const icons = {
      technical: BugOutlined,
      schedule: ClockCircleOutlined,
      resource: TeamOutlined,
      financial: DollarOutlined,
      external: SettingOutlined,
    };
    return icons[type] || SettingOutlined;
  };

  const getCategoryColor = (type: string) => {
    const colors = {
      technical: '#1890ff',
      schedule: '#fa8c16',
      resource: '#722ed1',
      financial: '#52c41a',
      external: '#8c8c8c',
    };
    return colors[type] || '#8c8c8c';
  };

  const getRiskLevelClass = (level: string) => {
    const classes = {
      high: 'bg-red-500',
      medium: 'bg-yellow-500',
      low: 'bg-green-500',
    };
    return classes[level] || 'bg-gray-500';
  };

  const getRiskStatusColor = (status: string) => {
    const colors = {
      监控中: 'blue',
      处理中: 'orange',
      已识别: 'default',
      已解决: 'green',
    };
    return colors[status] || 'default';
  };

  const getMeasureStatusColor = (status: string) => {
    const colors = {
      执行中: 'processing',
      已完成: 'success',
      计划中: 'default',
    };
    return colors[status] || 'default';
  };

  const getPlanStatusColor = (status: string) => {
    const colors = {
      已准备: 'success',
      准备中: 'processing',
      未准备: 'error',
    };
    return colors[status] || 'default';
  };

  // 事件处理函数
  const handleViewRisk = (risk: any) => {
    createMessage.info('查看风险详情功能开发中...');
  };

  const handleUpdateRisk = (risk: any) => {
    createMessage.info('更新风险状态功能开发中...');
  };
</script>
