<template>
  <div class="template-other-page p-4">
    <!-- 页面标题 -->
    <div class="page-header mb-4">
      <h2 class="text-xl font-semibold mb-2">其他设置</h2>
      <p class="text-gray-600">配置项目模板的其他功能设置和扩展选项</p>
    </div>

    <a-spin :spinning="loading">
      <!-- 设置卡片网格 -->
      <div class="settings-grid grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 通知设置 -->
        <a-card title="通知设置" :bordered="false" class="setting-card">
          <template #extra>
            <a-switch v-model:checked="notificationSettings.enabled" />
          </template>

          <div class="setting-content">
            <a-form layout="vertical" :disabled="!notificationSettings.enabled">
              <a-form-item label="邮件通知">
                <a-checkbox-group v-model:value="notificationSettings.email">
                  <a-checkbox value="task_assigned">任务分配时</a-checkbox>
                  <a-checkbox value="task_completed">任务完成时</a-checkbox>
                  <a-checkbox value="deadline_approaching">截止日期临近</a-checkbox>
                  <a-checkbox value="milestone_reached">里程碑达成</a-checkbox>
                </a-checkbox-group>
              </a-form-item>

              <a-form-item label="系统通知">
                <a-checkbox-group v-model:value="notificationSettings.system">
                  <a-checkbox value="status_change">状态变更</a-checkbox>
                  <a-checkbox value="comment_added">评论添加</a-checkbox>
                  <a-checkbox value="file_uploaded">文件上传</a-checkbox>
                </a-checkbox-group>
              </a-form-item>
            </a-form>
          </div>
        </a-card>

        <!-- 自动化设置 -->
        <a-card title="自动化设置" :bordered="false" class="setting-card">
          <template #extra>
            <a-switch v-model:checked="automationSettings.enabled" />
          </template>

          <div class="setting-content">
            <a-form layout="vertical" :disabled="!automationSettings.enabled">
              <a-form-item label="自动任务分配">
                <a-switch v-model:checked="automationSettings.autoAssign" />
                <div class="text-sm text-gray-500 mt-1"> 根据团队成员工作负载自动分配任务 </div>
              </a-form-item>

              <a-form-item label="自动状态更新">
                <a-switch v-model:checked="automationSettings.autoStatusUpdate" />
                <div class="text-sm text-gray-500 mt-1"> 根据子任务完成情况自动更新父任务状态 </div>
              </a-form-item>

              <a-form-item label="自动时间跟踪">
                <a-switch v-model:checked="automationSettings.autoTimeTracking" />
                <div class="text-sm text-gray-500 mt-1"> 自动记录任务执行时间 </div>
              </a-form-item>
            </a-form>
          </div>
        </a-card>

        <!-- 质量标准 -->
        <a-card title="质量标准" :bordered="false" class="setting-card">
          <template #extra>
            <a-button type="link" @click="handleEditQualityStandards">
              <EditOutlined />
              编辑
            </a-button>
          </template>

          <div class="setting-content">
            <div class="quality-standards">
              <div v-for="standard in qualityStandards" :key="standard.id" class="standard-item mb-3">
                <div class="flex justify-between items-start">
                  <div class="flex-1">
                    <div class="font-medium">{{ standard.name }}</div>
                    <div class="text-sm text-gray-500">{{ standard.description }}</div>
                  </div>
                  <a-tag :color="standard.level === 'high' ? 'red' : standard.level === 'medium' ? 'orange' : 'green'">
                    {{ getLevelText(standard.level) }}
                  </a-tag>
                </div>
              </div>

              <div v-if="!qualityStandards.length" class="text-center text-gray-400 py-4"> 暂无质量标准，点击编辑添加 </div>
            </div>
          </div>
        </a-card>

        <!-- 风险管理 -->
        <a-card title="风险管理" :bordered="false" class="setting-card">
          <template #extra>
            <a-button type="link" @click="handleEditRiskManagement">
              <EditOutlined />
              编辑
            </a-button>
          </template>

          <div class="setting-content">
            <div class="risk-categories">
              <div v-for="category in riskCategories" :key="category.id" class="risk-item mb-3">
                <div class="flex justify-between items-start">
                  <div class="flex-1">
                    <div class="font-medium">{{ category.name }}</div>
                    <div class="text-sm text-gray-500">{{ category.description }}</div>
                  </div>
                  <a-tag :color="getRiskLevelColor(category.level)">
                    {{ getRiskLevelText(category.level) }}
                  </a-tag>
                </div>
              </div>

              <div v-if="!riskCategories.length" class="text-center text-gray-400 py-4"> 暂无风险分类，点击编辑添加 </div>
            </div>
          </div>
        </a-card>

        <!-- 集成配置 -->
        <a-card title="集成配置" :bordered="false" class="setting-card">
          <template #extra>
            <a-button type="link" @click="handleEditIntegrations">
              <EditOutlined />
              配置
            </a-button>
          </template>

          <div class="setting-content">
            <div class="integrations">
              <div v-for="integration in integrations" :key="integration.id" class="integration-item mb-3">
                <div class="flex justify-between items-center">
                  <div class="flex items-center">
                    <div class="integration-icon mr-3">
                      <a-avatar :src="integration.icon" :size="32" />
                    </div>
                    <div>
                      <div class="font-medium">{{ integration.name }}</div>
                      <div class="text-sm text-gray-500">{{ integration.description }}</div>
                    </div>
                  </div>
                  <a-switch v-model:checked="integration.enabled" />
                </div>
              </div>
            </div>
          </div>
        </a-card>

        <!-- 模板设置 -->
        <a-card title="模板设置" :bordered="false" class="setting-card">
          <div class="setting-content">
            <a-form layout="vertical">
              <a-form-item label="模板版本">
                <a-input v-model:value="templateSettings.version" placeholder="1.0.0" />
              </a-form-item>

              <a-form-item label="模板描述">
                <a-textarea v-model:value="templateSettings.description" :rows="3" placeholder="请输入模板的详细描述..." />
              </a-form-item>

              <a-form-item label="标签">
                <a-select v-model:value="templateSettings.tags" mode="tags" placeholder="添加标签" style="width: 100%" />
              </a-form-item>

              <a-form-item label="可见性">
                <a-radio-group v-model:value="templateSettings.visibility">
                  <a-radio value="private">私有</a-radio>
                  <a-radio value="team">团队</a-radio>
                  <a-radio value="public">公开</a-radio>
                </a-radio-group>
              </a-form-item>
            </a-form>
          </div>
        </a-card>
      </div>

      <!-- 操作按钮 -->
      <div class="actions mt-6 flex justify-center">
        <a-space size="large">
          <a-button size="large" @click="handleReset">
            <ReloadOutlined />
            重置设置
          </a-button>
          <a-button type="primary" size="large" @click="handleSave">
            <SaveOutlined />
            保存设置
          </a-button>
        </a-space>
      </div>
    </a-spin>

    <!-- 质量标准编辑弹窗 -->
    <BasicModal @register="registerQualityModal" title="质量标准设置" width="60%" @ok="handleQualitySubmit">
      <div class="quality-editor">
        <div class="mb-4">
          <a-button type="dashed" @click="handleAddQualityStandard">
            <PlusOutlined />
            添加质量标准
          </a-button>
        </div>

        <BasicTable @register="registerQualityTable" :can-resize="false">
          <template #level="{ record }">
            <a-select v-model:value="record.level" style="width: 100%">
              <a-select-option value="high">高</a-select-option>
              <a-select-option value="medium">中</a-select-option>
              <a-select-option value="low">低</a-select-option>
            </a-select>
          </template>

          <template #action="{ record, index }">
            <a-button type="link" size="small" danger @click="handleRemoveQualityStandard(index)"> 移除 </a-button>
          </template>
        </BasicTable>
      </div>
    </BasicModal>

    <!-- 风险管理编辑弹窗 -->
    <BasicModal @register="registerRiskModal" title="风险管理设置" width="60%" @ok="handleRiskSubmit">
      <div class="risk-editor">
        <div class="mb-4">
          <a-button type="dashed" @click="handleAddRiskCategory">
            <PlusOutlined />
            添加风险分类
          </a-button>
        </div>

        <BasicTable @register="registerRiskTable" :can-resize="false">
          <template #level="{ record }">
            <a-select v-model:value="record.level" style="width: 100%">
              <a-select-option value="critical">严重</a-select-option>
              <a-select-option value="high">高</a-select-option>
              <a-select-option value="medium">中</a-select-option>
              <a-select-option value="low">低</a-select-option>
            </a-select>
          </template>

          <template #action="{ record, index }">
            <a-button type="link" size="small" danger @click="handleRemoveRiskCategory(index)"> 移除 </a-button>
          </template>
        </BasicTable>
      </div>
    </BasicModal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed, inject, onMounted } from 'vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { EditOutlined, PlusOutlined, SaveOutlined, ReloadOutlined } from '@ant-design/icons-vue';

  defineOptions({ name: 'ProjectTemplateOtherConfigPage' });

  // 注入模板ID
  const templateId = inject<any>('templateId');
  const { createMessage } = useMessage();

  const loading = ref(false);

  // 通知设置
  const notificationSettings = ref({
    enabled: true,
    email: ['task_assigned', 'deadline_approaching'],
    system: ['status_change', 'comment_added'],
  });

  // 自动化设置
  const automationSettings = ref({
    enabled: true,
    autoAssign: false,
    autoStatusUpdate: true,
    autoTimeTracking: false,
  });

  // 质量标准
  const qualityStandards = ref([
    {
      id: '1',
      name: '代码审查',
      description: '所有代码必须经过同行评审',
      level: 'high',
    },
    {
      id: '2',
      name: '单元测试',
      description: '代码覆盖率不低于80%',
      level: 'medium',
    },
  ]);

  // 风险分类
  const riskCategories = ref([
    {
      id: '1',
      name: '技术风险',
      description: '技术选型、架构设计等相关风险',
      level: 'medium',
    },
    {
      id: '2',
      name: '进度风险',
      description: '项目进度延期风险',
      level: 'high',
    },
  ]);

  // 集成配置
  const integrations = ref([
    {
      id: '1',
      name: 'Git',
      description: '代码版本管理',
      icon: '/icons/git.png',
      enabled: true,
    },
    {
      id: '2',
      name: 'Jenkins',
      description: '持续集成/持续部署',
      icon: '/icons/jenkins.png',
      enabled: false,
    },
    {
      id: '3',
      name: 'JIRA',
      description: '问题跟踪和项目管理',
      icon: '/icons/jira.png',
      enabled: true,
    },
  ]);

  // 模板设置
  const templateSettings = ref({
    version: '1.0.0',
    description: '',
    tags: [],
    visibility: 'team',
  });

  // 质量标准编辑数据
  const qualityTableData = ref([...qualityStandards.value]);
  const riskTableData = ref([...riskCategories.value]);

  // 质量标准表格列
  const qualityColumns = [
    {
      title: '名称',
      dataIndex: 'name',
      width: 150,
    },
    {
      title: '描述',
      dataIndex: 'description',
      width: 200,
    },
    {
      title: '级别',
      dataIndex: 'level',
      width: 100,
      slots: { customRender: 'level' },
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      slots: { customRender: 'action' },
    },
  ];

  // 风险表格列
  const riskColumns = [
    {
      title: '名称',
      dataIndex: 'name',
      width: 150,
    },
    {
      title: '描述',
      dataIndex: 'description',
      width: 200,
    },
    {
      title: '风险级别',
      dataIndex: 'level',
      width: 100,
      slots: { customRender: 'level' },
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      slots: { customRender: 'action' },
    },
  ];

  // 弹窗实例
  const [registerQualityModal, { openModal: openQualityModal, closeModal: closeQualityModal }] = useModalInner();
  const [registerRiskModal, { openModal: openRiskModal, closeModal: closeRiskModal }] = useModalInner();

  // 表格实例
  const [registerQualityTable] = useTable({
    dataSource: qualityTableData,
    columns: qualityColumns,
    useSearchForm: false,
    pagination: false,
  });

  const [registerRiskTable] = useTable({
    dataSource: riskTableData,
    columns: riskColumns,
    useSearchForm: false,
    pagination: false,
  });

  // 工具函数
  const getLevelText = (level: string) => {
    const textMap = {
      high: '高',
      medium: '中',
      low: '低',
    };
    return textMap[level] || level;
  };

  const getRiskLevelColor = (level: string) => {
    const colorMap = {
      critical: 'red',
      high: 'orange',
      medium: 'gold',
      low: 'green',
    };
    return colorMap[level] || 'default';
  };

  const getRiskLevelText = (level: string) => {
    const textMap = {
      critical: '严重',
      high: '高',
      medium: '中',
      low: '低',
    };
    return textMap[level] || level;
  };

  // 事件处理
  const handleEditQualityStandards = () => {
    qualityTableData.value = [...qualityStandards.value];
    openQualityModal();
  };

  const handleEditRiskManagement = () => {
    riskTableData.value = [...riskCategories.value];
    openRiskModal();
  };

  const handleEditIntegrations = () => {
    createMessage.info('集成配置功能开发中...');
  };

  const handleAddQualityStandard = () => {
    qualityTableData.value.push({
      id: Date.now().toString(),
      name: '',
      description: '',
      level: 'medium',
    });
  };

  const handleRemoveQualityStandard = (index: number) => {
    qualityTableData.value.splice(index, 1);
  };

  const handleQualitySubmit = () => {
    qualityStandards.value = [...qualityTableData.value];
    createMessage.success('质量标准保存成功');
    closeQualityModal();
  };

  const handleAddRiskCategory = () => {
    riskTableData.value.push({
      id: Date.now().toString(),
      name: '',
      description: '',
      level: 'medium',
    });
  };

  const handleRemoveRiskCategory = (index: number) => {
    riskTableData.value.splice(index, 1);
  };

  const handleRiskSubmit = () => {
    riskCategories.value = [...riskTableData.value];
    createMessage.success('风险管理设置保存成功');
    closeRiskModal();
  };

  const handleSave = () => {
    console.log('保存其他设置:', {
      templateId: templateId?.value,
      notificationSettings: notificationSettings.value,
      automationSettings: automationSettings.value,
      qualityStandards: qualityStandards.value,
      riskCategories: riskCategories.value,
      integrations: integrations.value,
      templateSettings: templateSettings.value,
    });

    createMessage.success('设置保存成功');
  };

  const handleReset = () => {
    // 重置所有设置到默认值
    notificationSettings.value = {
      enabled: true,
      email: ['task_assigned', 'deadline_approaching'],
      system: ['status_change', 'comment_added'],
    };

    automationSettings.value = {
      enabled: true,
      autoAssign: false,
      autoStatusUpdate: true,
      autoTimeTracking: false,
    };

    templateSettings.value = {
      version: '1.0.0',
      description: '',
      tags: [],
      visibility: 'team',
    };

    createMessage.success('设置已重置');
  };

  onMounted(() => {
    console.log('其他设置页面挂载，模板ID:', templateId?.value);
  });
</script>

<style lang="less" scoped>
  .template-other-page {
    .settings-grid {
      .setting-card {
        height: fit-content;

        :deep(.ant-card-head) {
          border-bottom: 1px solid #f0f0f0;
        }

        .setting-content {
          padding-top: 16px;

          .ant-form-item {
            margin-bottom: 16px;
          }
        }
      }
    }

    .standard-item,
    .risk-item {
      padding: 12px;
      border: 1px solid #f0f0f0;
      border-radius: 6px;
      transition: all 0.3s ease;

      &:hover {
        border-color: #d9d9d9;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      }
    }

    .integration-item {
      padding: 12px;
      border: 1px solid #f0f0f0;
      border-radius: 6px;
      transition: all 0.3s ease;

      &:hover {
        border-color: #d9d9d9;
      }

      .integration-icon {
        display: flex;
        align-items: center;
      }
    }

    .actions {
      border-top: 1px solid #f0f0f0;
      padding-top: 24px;
    }
  }
</style>
