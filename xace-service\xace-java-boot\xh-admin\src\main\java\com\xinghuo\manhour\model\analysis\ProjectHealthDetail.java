package com.xinghuo.manhour.model.analysis;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 项目健康度详情
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@Schema(description = "项目健康度详情")
public class ProjectHealthDetail {

    @Schema(description = "项目ID")
    private String projectId;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "总体健康度")
    private BigDecimal overallHealth;

    @Schema(description = "工时健康度")
    private BigDecimal workhourHealth;

    @Schema(description = "团队健康度")
    private BigDecimal teamHealth;

    @Schema(description = "进度健康度")
    private BigDecimal progressHealth;

    @Schema(description = "资源健康度")
    private BigDecimal resourceHealth;

    @Schema(description = "月度健康度趋势")
    private List<MonthlyHealthData> monthlyTrend;

    @Schema(description = "团队成员分布")
    private List<TeamMemberData> teamMembers;

    @Schema(description = "工时分布")
    private List<WorkhourDistributionData> workhourDistribution;

    @Schema(description = "风险指标")
    private List<RiskIndicatorData> riskIndicators;

    /**
     * 月度健康度数据
     */
    @Data
    @Schema(description = "月度健康度数据")
    public static class MonthlyHealthData {
        @Schema(description = "月份")
        private String month;

        @Schema(description = "健康度")
        private BigDecimal health;

        @Schema(description = "工时")
        private BigDecimal workMonth;
    }

    /**
     * 团队成员数据
     */
    @Data
    @Schema(description = "团队成员数据")
    public static class TeamMemberData {
        @Schema(description = "员工姓名")
        private String userName;

        @Schema(description = "投入工时")
        private BigDecimal workMonth;

        @Schema(description = "参与度")
        private BigDecimal participation;
    }

    /**
     * 工时分布数据
     */
    @Data
    @Schema(description = "工时分布数据")
    public static class WorkhourDistributionData {
        @Schema(description = "工时类型")
        private String workType;

        @Schema(description = "工时")
        private BigDecimal workMonth;

        @Schema(description = "占比")
        private BigDecimal percentage;
    }

    /**
     * 风险指标数据
     */
    @Data
    @Schema(description = "风险指标数据")
    public static class RiskIndicatorData {
        @Schema(description = "风险类型")
        private String riskType;

        @Schema(description = "风险等级")
        private String riskLevel;

        @Schema(description = "风险描述")
        private String description;
    }
}
