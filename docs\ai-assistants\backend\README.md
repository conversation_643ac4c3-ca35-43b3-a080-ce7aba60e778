# XACE 后端 AI 助手工具指南

本目录提供用于XACE后端开发的AI助手工具配置和快速指令，帮助开发者快速生成符合项目规范的高质量代码。

## 📁 文件说明

### [QUICK_REFERENCE.md](./QUICK_REFERENCE.md) ⭐
**最常用** - 快速参考卡片，包含最常用的一键生成指令
- 核心组件生成指令
- 关键规范速记表
- 3分钟完成标准业务模块

### [CLAUDE_QUICK_COMMANDS.md](./CLAUDE_QUICK_COMMANDS.md)
**详细说明** - Claude快速指令完整指南
- 10+ 类型代码生成指令
- 详细使用说明和示例
- 高级功能和使用技巧

### [09_PROMPTS_TEMPLATES.md](./09_PROMPTS_TEMPLATES.md)
**底层模板** - AI代码生成提示词模板库
- 13种代码结构生成模板
- 符合XACE规范的详细模板
- 供其他AI工具参考使用

## 🚀 快速开始

### 新手入门（推荐）
1. 阅读 [QUICK_REFERENCE.md](./QUICK_REFERENCE.md)
2. 复制快速指令到Claude对话框
3. 替换参数，一键生成代码

### 示例：5分钟创建用户管理模块
```
# 第1步：生成完整CRUD
生成XACE完整CRUD：
表名：sys_user
业务名：User
字段：account(String,账号), fullName(String,姓名), email(String,邮箱), mobile(String,手机), status(Integer,状态)
模块：com.xinghuo.system

# 第2步：添加特殊业务方法
在现有UserService基础上添加方法：resetPassword, batchUpdateStatus, exportUsers

# 完成！获得完整的8个文件：
# Entity + Form + VO + Pagination + Mapper + Service + ServiceImpl + Controller
```

## 🎯 核心优势

### ✅ 严格遵循XACE规范
- Jakarta EE包导入（`jakarta.*`）
- BaseEntityV2继承体系
- LambdaQueryWrapper类型安全查询
- ActionResult统一返回格式

### ✅ 提高开发效率
- 一条指令生成完整模块
- 自动处理样板代码
- 包含完整的验证和注解

### ✅ 降低错误率
- 自动遵循命名规范
- 正确的包结构和导入
- 统一的代码风格

## 🔧 支持的生成类型

| 类型 | 功能 | 指令前缀 |
|------|------|----------|
| **Entity** | 数据库实体类 | `生成XACE Entity` |
| **Form** | 表单对象（创建/更新） | `生成XACE Form` |
| **VO** | 视图对象（数据展示） | `生成XACE VO` |
| **Pagination** | 分页查询对象 | `生成XACE Pagination` |
| **Mapper** | 数据访问接口 | `生成XACE Mapper` |
| **Service** | 业务逻辑接口+实现 | `生成XACE Service` |
| **Controller** | REST API控制器 | `生成XACE Controller` |
| **完整CRUD** | 全套业务模块 | `生成XACE完整CRUD` |
| **单元测试** | JUnit5测试类 | `生成XACE单元测试` |

## 📚 相关规范文档

**必读规范**：
- [后端架构规范](../../framework-standards/backend/01_ARCHITECTURE.md)
- [实体层规范](../../framework-standards/backend/03_ENTITY_LAYER.md)
- [服务层规范](../../framework-standards/backend/05_SERVICE_LAYER.md)
- [Model类规范](../../framework-standards/backend/07_MODEL_CLASSES.md)

## 💡 使用建议

### 开发流程建议
1. **设计阶段**：确定业务实体和表结构
2. **生成阶段**：使用快速指令生成基础代码
3. **定制阶段**：根据具体业务需求调整和扩展
4. **测试阶段**：生成并完善单元测试

### 最佳实践
- 优先使用完整CRUD生成，一次性获得全套代码
- 复杂业务逻辑单独生成和优化
- 定期检查生成代码是否符合最新规范
- 保持AI工具配置的同步更新

## 🔄 更新日志

- **2025-01-01**：创建AI助手工具指南
- 基于XACE后端规范v2.0制作
- 支持BaseEntityV2和Jakarta EE规范
- 包含完整的快速指令体系

---

**开始使用**：打开 [QUICK_REFERENCE.md](./QUICK_REFERENCE.md)，复制指令，开始高效编码！