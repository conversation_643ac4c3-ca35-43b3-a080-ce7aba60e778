<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" :title="getTitle" width="50%" @ok="handleSubmit" showFooter>
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>

<script lang="ts" setup>
  import { ref, computed, unref } from 'vue';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { BasicForm, useForm } from '/@/components/Form';
  import { formSchema } from './businessWeeklog.data';
  import { createBusinessWeeklog, updateBusinessWeeklog } from '/@/api/project/biz/businessWeeklog';
  import { useMessage } from '/@/hooks/web/useMessage';

  defineOptions({ name: 'BusinessWeeklogDrawer' });

  const emit = defineEmits(['success', 'register']);

  const { createMessage } = useMessage();
  const isUpdate = ref(true);
  const rowId = ref('');

  const [registerForm, { setFieldsValue, updateSchema, resetFields, validate }] = useForm({
    labelWidth: 100,
    baseColProps: { span: 24 },
    schemas: formSchema,
    showActionButtonGroup: false,
    autoSubmitOnEnter: true,
  });

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async data => {
    resetFields();
    setDrawerProps({ confirmLoading: false });
    isUpdate.value = !!data?.isUpdate;

    if (unref(isUpdate)) {
      rowId.value = data.record.id;
      // 处理日期范围
      const formData = { ...data.record };
      if (formData.startDate && formData.endDate) {
        formData.dateRange = [formData.startDate, formData.endDate];
      }
      setFieldsValue(formData);
    } else {
      // 新增时设置默认值
      setFieldsValue({
        status: 0, // 默认未填写
        showStatus: 0, // 默认未显示
        inputDate: new Date().toISOString().split('T')[0], // 默认当前日期
      });
    }

    // 根据状态控制字段是否可编辑
    const record = data?.record;
    if (record && record.status === 3) {
      // 已发布状态，所有字段只读
      updateSchema([
        {
          field: 'projId',
          componentProps: { disabled: true },
        },
        {
          field: 'projName',
          componentProps: { disabled: true },
        },
        // 可以继续添加其他字段的只读设置
      ]);
    }
  });

  const getTitle = computed(() => (!unref(isUpdate) ? '新增商机周报' : '编辑商机周报'));

  async function handleSubmit() {
    try {
      const values = await validate();
      setDrawerProps({ confirmLoading: true });

      // 处理日期范围
      if (values.dateRange && values.dateRange.length === 2) {
        values.startDate = values.dateRange[0];
        values.endDate = values.dateRange[1];
        delete values.dateRange;
      }

      if (unref(isUpdate)) {
        await updateBusinessWeeklog(rowId.value, values);
        createMessage.success('更新成功');
      } else {
        await createBusinessWeeklog(values);
        createMessage.success('创建成功');
      }

      closeDrawer();
      emit('success');
    } catch (error) {
      createMessage.error(unref(isUpdate) ? '更新失败' : '创建失败');
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
