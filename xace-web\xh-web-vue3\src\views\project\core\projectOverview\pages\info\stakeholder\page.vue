<template>
  <div class="stakeholder-page p-4">
    <!-- 页面标题 -->
    <div class="page-header mb-4">
      <h2 class="text-xl font-semibold mb-2">干系人</h2>
      <p class="text-gray-600">管理项目相关的利益相关者和干系人信息</p>
    </div>

    <a-spin :spinning="loading">
      <!-- 工具栏 -->
      <div class="toolbar mb-4 flex justify-between items-center">
        <div class="filters flex items-center space-x-4">
          <a-input-search v-model:value="searchText" placeholder="搜索干系人姓名或公司" style="width: 300px" @search="handleSearch" />
          <a-select v-model:value="companyFilter" placeholder="筛选公司" style="width: 180px" allow-clear @change="handleCompanyFilter">
            <a-select-option v-for="company in companies" :key="company.value" :value="company.value">
              {{ company.label }}
            </a-select-option>
          </a-select>
          <a-select v-model:value="participateDegreeFilter" placeholder="筛选参与程度" style="width: 150px" allow-clear @change="handleParticipateFilter">
            <a-select-option v-for="degree in participateDegrees" :key="degree.value" :value="degree.value">
              {{ degree.label }}
            </a-select-option>
          </a-select>
        </div>
        <div class="actions">
          <a-space>
            <a-button type="primary" @click="handleAdd">
              <template #icon><PlusOutlined /></template>
              添加干系人
            </a-button>
            <a-button @click="handleExport">
              <template #icon><ExportOutlined /></template>
              导出列表
            </a-button>
            <a-button @click="handleRefresh">
              <template #icon><ReloadOutlined /></template>
              刷新
            </a-button>
          </a-space>
        </div>
      </div>

      <!-- 干系人统计卡片 -->
      <div class="stakeholder-stats grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="stat-card bg-blue-50 rounded-lg p-4">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-sm text-blue-600">总干系人数</div>
              <div class="text-2xl font-bold text-blue-600">{{ stakeholderStats.total }}</div>
            </div>
            <UserOutlined class="text-3xl text-blue-600" />
          </div>
        </div>
        <div class="stat-card bg-green-50 rounded-lg p-4">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-sm text-green-600">高参与度</div>
              <div class="text-2xl font-bold text-green-600">{{ stakeholderStats.highParticipation }}</div>
            </div>
            <StarOutlined class="text-3xl text-green-600" />
          </div>
        </div>
        <div class="stat-card bg-orange-50 rounded-lg p-4">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-sm text-orange-600">高影响力</div>
              <div class="text-2xl font-bold text-orange-600">{{ stakeholderStats.highInfluence }}</div>
            </div>
            <CrownOutlined class="text-3xl text-orange-600" />
          </div>
        </div>
        <div class="stat-card bg-purple-50 rounded-lg p-4">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-sm text-purple-600">沟通记录</div>
              <div class="text-2xl font-bold text-purple-600">{{ stakeholderStats.communications }}</div>
            </div>
            <MessageOutlined class="text-3xl text-purple-600" />
          </div>
        </div>
      </div>

      <!-- 干系人表格 -->
      <div class="stakeholder-table bg-white rounded-lg shadow-sm border">
        <a-table
          :columns="stakeholderColumns"
          :data-source="filteredStakeholders"
          row-key="id"
          :pagination="pagination"
          :scroll="{ x: 1600 }"
          @change="handleTableChange">
          <template #headerCell="{ column }">
            <template v-if="column.key === 'name'">
              <UserOutlined class="mr-1" />
              姓名
            </template>
            <template v-else-if="column.key === 'company'">
              <BankOutlined class="mr-1" />
              公司
            </template>
            <template v-else-if="column.key === 'job'">
              <IdcardOutlined class="mr-1" />
              职务
            </template>
            <template v-else-if="column.key === 'participateDegree'">
              <RiseOutlined class="mr-1" />
              参与程度
            </template>
            <template v-else-if="column.key === 'influenceMode'">
              <ThunderboltOutlined class="mr-1" />
              影响方式
            </template>
            <template v-else-if="column.key === 'influenceDegree'">
              <FireOutlined class="mr-1" />
              影响程度
            </template>
            <template v-else-if="column.key === 'managementStrategy'">
              <AimOutlined class="mr-1" />
              管理策略
            </template>
            <template v-else-if="column.key === 'communicateCount'">
              <MessageOutlined class="mr-1" />
              沟通记录
            </template>
          </template>

          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'name'">
              <div class="flex items-center">
                <a-avatar class="mr-2" :src="record.avatar" :style="{ backgroundColor: getUserColor(record.name) }">
                  {{ record.name.charAt(0) }}
                </a-avatar>
                <div>
                  <div class="font-medium">{{ record.name }}</div>
                  <div class="text-sm text-gray-500">{{ record.email }}</div>
                </div>
              </div>
            </template>
            <template v-else-if="column.key === 'company'">
              <div>
                <div class="font-medium">{{ record.company }}</div>
                <div class="text-sm text-gray-500">{{ record.job }}</div>
              </div>
            </template>
            <template v-else-if="column.key === 'telephone'">
              <div class="space-y-1">
                <div v-if="record.telephone" class="flex items-center">
                  <PhoneOutlined class="mr-1 text-gray-400" />
                  {{ record.telephone }}
                </div>
                <div v-if="record.mobile" class="flex items-center">
                  <MobileOutlined class="mr-1 text-gray-400" />
                  {{ record.mobile }}
                </div>
              </div>
            </template>
            <template v-else-if="column.key === 'participateDegree'">
              <a-select
                v-if="editingRecord === record.id"
                v-model:value="record.participateDegree"
                size="small"
                style="width: 100%"
                @change="handleFieldChange(record, 'participateDegree')">
                <a-select-option v-for="degree in participateDegrees" :key="degree.value" :value="degree.value">
                  {{ degree.label }}
                </a-select-option>
              </a-select>
              <a-tag v-else :color="getParticipationColor(record.participateDegree)">
                {{ record.participateDegree }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'influenceMode'">
              <a-select
                v-if="editingRecord === record.id"
                v-model:value="record.influenceMode"
                size="small"
                style="width: 100%"
                @change="handleFieldChange(record, 'influenceMode')">
                <a-select-option v-for="mode in influenceModes" :key="mode.value" :value="mode.value">
                  {{ mode.label }}
                </a-select-option>
              </a-select>
              <a-tag v-else :color="getInfluenceColor(record.influenceMode)">
                {{ record.influenceMode }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'influenceDegree'">
              <a-select
                v-if="editingRecord === record.id"
                v-model:value="record.influenceDegree"
                size="small"
                style="width: 100%"
                @change="handleFieldChange(record, 'influenceDegree')">
                <a-select-option v-for="degree in influenceDegrees" :key="degree.value" :value="degree.value">
                  {{ degree.label }}
                </a-select-option>
              </a-select>
              <a-tag v-else :color="getInfluenceColor(record.influenceDegree)">
                {{ record.influenceDegree }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'managementStrategy'">
              <a-input
                v-if="editingRecord === record.id"
                v-model:value="record.managementStrategy"
                size="small"
                @change="handleFieldChange(record, 'managementStrategy')" />
              <div v-else class="max-w-xs">
                <a-tooltip :title="record.managementStrategy">
                  <div class="truncate">{{ record.managementStrategy }}</div>
                </a-tooltip>
              </div>
            </template>
            <template v-else-if="column.key === 'remark'">
              <a-tooltip :title="record.remark">
                <InfoCircleOutlined class="text-gray-400 cursor-help" />
              </a-tooltip>
            </template>
            <template v-else-if="column.key === 'communicateCount'">
              <a-button type="link" size="small" @click="handleViewCommunications(record)">
                <a-badge :count="record.communicateCount" :overflow-count="99">
                  <MessageOutlined />
                </a-badge>
              </a-button>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button v-if="editingRecord !== record.id" type="link" size="small" @click="handleEdit(record)">
                  <template #icon><EditOutlined /></template>
                  编辑
                </a-button>
                <a-button v-else type="link" size="small" @click="handleSave(record)">
                  <template #icon><SaveOutlined /></template>
                  保存
                </a-button>
                <a-button v-if="editingRecord === record.id" type="link" size="small" @click="handleCancel(record)">
                  <template #icon><CloseOutlined /></template>
                  取消
                </a-button>
                <a-popconfirm v-if="editingRecord !== record.id" title="确定要删除这个干系人吗？" @confirm="handleDelete(record)">
                  <a-button type="link" size="small" danger>
                    <template #icon><DeleteOutlined /></template>
                    删除
                  </a-button>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </a-spin>

    <!-- 添加干系人模态框 -->
    <a-modal v-model:open="modalVisible" title="添加干系人" :confirm-loading="modalLoading" @ok="handleModalOk" @cancel="handleModalCancel" width="800px">
      <a-form ref="modalFormRef" :model="modalFormData" :rules="modalRules" layout="vertical">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <a-form-item label="姓名" name="name">
            <a-input v-model:value="modalFormData.name" placeholder="请输入姓名" />
          </a-form-item>
          <a-form-item label="公司" name="company">
            <a-input v-model:value="modalFormData.company" placeholder="请输入公司名称" />
          </a-form-item>
          <a-form-item label="职务" name="job">
            <a-input v-model:value="modalFormData.job" placeholder="请输入职务" />
          </a-form-item>
          <a-form-item label="电子邮箱" name="email">
            <a-input v-model:value="modalFormData.email" placeholder="请输入邮箱" />
          </a-form-item>
          <a-form-item label="办公电话" name="telephone">
            <a-input v-model:value="modalFormData.telephone" placeholder="请输入办公电话" />
          </a-form-item>
          <a-form-item label="手机号码" name="mobile">
            <a-input v-model:value="modalFormData.mobile" placeholder="请输入手机号码" />
          </a-form-item>
          <a-form-item label="参与程度" name="participateDegree">
            <a-select v-model:value="modalFormData.participateDegree" placeholder="请选择参与程度">
              <a-select-option v-for="degree in participateDegrees" :key="degree.value" :value="degree.value">
                {{ degree.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="影响方式" name="influenceMode">
            <a-select v-model:value="modalFormData.influenceMode" placeholder="请选择影响方式">
              <a-select-option v-for="mode in influenceModes" :key="mode.value" :value="mode.value">
                {{ mode.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="影响程度" name="influenceDegree">
            <a-select v-model:value="modalFormData.influenceDegree" placeholder="请选择影响程度">
              <a-select-option v-for="degree in influenceDegrees" :key="degree.value" :value="degree.value">
                {{ degree.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="管理策略" name="managementStrategy" class="md:col-span-2">
            <a-textarea v-model:value="modalFormData.managementStrategy" placeholder="请输入管理策略" :rows="2" />
          </a-form-item>
          <a-form-item label="备注" name="remark" class="md:col-span-2">
            <a-textarea v-model:value="modalFormData.remark" placeholder="请输入备注信息" :rows="2" />
          </a-form-item>
        </div>
      </a-form>
    </a-modal>

    <!-- 沟通记录模态框 -->
    <a-modal v-model:open="communicationModalVisible" title="沟通记录" :footer="null" width="800px">
      <div v-if="selectedStakeholder" class="communication-records">
        <div class="record-header mb-4">
          <h4 class="text-lg font-semibold">{{ selectedStakeholder.name }} 的沟通记录</h4>
          <p class="text-gray-600">{{ selectedStakeholder.company }} - {{ selectedStakeholder.job }}</p>
        </div>

        <a-timeline>
          <a-timeline-item v-for="record in communicationRecords" :key="record.id" :color="getCommunicationColor(record.type)">
            <template #dot>
              <MessageOutlined v-if="record.type === '邮件'" />
              <PhoneOutlined v-else-if="record.type === '电话'" />
              <TeamOutlined v-else-if="record.type === '会议'" />
              <FileTextOutlined v-else />
            </template>
            <div class="communication-item">
              <div class="flex justify-between items-start mb-2">
                <span class="font-medium">{{ record.title }}</span>
                <span class="text-sm text-gray-500">{{ record.date }}</span>
              </div>
              <div class="text-gray-600 mb-2">{{ record.content }}</div>
              <div class="flex items-center space-x-2">
                <a-tag size="small" :color="getCommunicationColor(record.type)">
                  {{ record.type }}
                </a-tag>
                <span class="text-xs text-gray-500">by {{ record.creator }}</span>
              </div>
            </div>
          </a-timeline-item>
        </a-timeline>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted } from 'vue';
  import {
    PlusOutlined,
    ExportOutlined,
    ReloadOutlined,
    UserOutlined,
    StarOutlined,
    CrownOutlined,
    MessageOutlined,
    BankOutlined,
    IdcardOutlined,
    RiseOutlined,
    ThunderboltOutlined,
    FireOutlined,
    AimOutlined,
    PhoneOutlined,
    MobileOutlined,
    InfoCircleOutlined,
    EditOutlined,
    SaveOutlined,
    CloseOutlined,
    DeleteOutlined,
    TeamOutlined,
    FileTextOutlined,
  } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';
  import type { TableColumnsType } from 'ant-design-vue';

  // 干系人接口
  interface Stakeholder {
    id: string;
    name: string;
    company: string;
    job: string;
    telephone: string;
    mobile: string;
    email: string;
    participateDegree: string;
    influenceMode: string;
    influenceDegree: string;
    managementStrategy: string;
    remark: string;
    communicateCount: number;
    avatar?: string;
  }

  // 沟通记录接口
  interface CommunicationRecord {
    id: string;
    title: string;
    content: string;
    type: string;
    date: string;
    creator: string;
  }

  // 响应式数据
  const loading = ref(false);
  const searchText = ref('');
  const companyFilter = ref('');
  const participateDegreeFilter = ref('');
  const modalVisible = ref(false);
  const modalLoading = ref(false);
  const communicationModalVisible = ref(false);
  const modalFormRef = ref();
  const editingRecord = ref<string | null>(null);
  const selectedStakeholder = ref<Stakeholder | null>(null);
  const backupData = ref<Stakeholder | null>(null);

  // 分页配置
  const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number) => `共 ${total} 条记录`,
  });

  // 干系人统计
  const stakeholderStats = reactive({
    total: 8,
    highParticipation: 3,
    highInfluence: 4,
    communications: 25,
  });

  // 模态框表单数据
  const modalFormData = reactive({
    name: '',
    company: '',
    job: '',
    telephone: '',
    mobile: '',
    email: '',
    participateDegree: '',
    influenceMode: '',
    influenceDegree: '',
    managementStrategy: '',
    remark: '',
  });

  // 模态框验证规则
  const modalRules = {
    name: [{ required: true, message: '请输入姓名' }],
    company: [{ required: true, message: '请输入公司名称' }],
    job: [{ required: true, message: '请输入职务' }],
    email: [{ type: 'email', message: '请输入正确的邮箱格式' }],
  };

  // 选项数据
  const companies = [
    { label: '易趋科技', value: '易趋科技' },
    { label: '华为技术', value: '华为技术' },
    { label: '阿里巴巴', value: '阿里巴巴' },
    { label: '腾讯科技', value: '腾讯科技' },
  ];

  const participateDegrees = [
    { label: '高', value: '高' },
    { label: '中', value: '中' },
    { label: '低', value: '低' },
  ];

  const influenceModes = [
    { label: '积极', value: '积极' },
    { label: '消极', value: '消极' },
    { label: '中性', value: '中性' },
  ];

  const influenceDegrees = [
    { label: '高', value: '高' },
    { label: '中', value: '中' },
    { label: '低', value: '低' },
  ];

  // 干系人数据
  const stakeholders = ref<Stakeholder[]>([
    {
      id: '1',
      name: 'DM',
      company: '易趋科技',
      job: '开发经理',
      telephone: '021-12345678',
      mobile: '13812345678',
      email: '<EMAIL>',
      participateDegree: '高',
      influenceMode: '积极',
      influenceDegree: '高',
      managementStrategy: '定期沟通，及时反馈项目进展',
      remark: '项目核心干系人，决策权重较高',
      communicateCount: 8,
    },
    {
      id: '2',
      name: 'PM',
      company: '易趋科技',
      job: '项目经理',
      telephone: '021-12345679',
      mobile: '13812345679',
      email: '<EMAIL>',
      participateDegree: '高',
      influenceMode: '积极',
      influenceDegree: '高',
      managementStrategy: '密切配合，协调各方资源',
      remark: '项目负责人，全程参与',
      communicateCount: 12,
    },
    {
      id: '3',
      name: '曹静',
      company: '华为技术',
      job: '技术总监',
      telephone: '010-87654321',
      mobile: '13987654321',
      email: '<EMAIL>',
      participateDegree: '中',
      influenceMode: '积极',
      influenceDegree: '中',
      managementStrategy: '定期技术评审，提供专业建议',
      remark: '技术专家，提供技术支持',
      communicateCount: 5,
    },
    {
      id: '4',
      name: '曹静国',
      company: '阿里巴巴',
      job: '产品经理',
      telephone: '0571-28345678',
      mobile: '13798765432',
      email: '<EMAIL>',
      participateDegree: '中',
      influenceMode: '中性',
      influenceDegree: '中',
      managementStrategy: '需求澄清，产品验收',
      remark: '产品负责人，关注用户体验',
      communicateCount: 3,
    },
    {
      id: '5',
      name: '张三',
      company: '腾讯科技',
      job: '架构师',
      telephone: '0755-86543210',
      mobile: '13876543210',
      email: '<EMAIL>',
      participateDegree: '低',
      influenceMode: '积极',
      influenceDegree: '高',
      managementStrategy: '架构评审，技术指导',
      remark: '资深架构师，技术权威',
      communicateCount: 2,
    },
    {
      id: '6',
      name: '李四',
      company: '易趋科技',
      job: '测试经理',
      telephone: '021-12345680',
      mobile: '13812345680',
      email: '<EMAIL>',
      participateDegree: '中',
      influenceMode: '积极',
      influenceDegree: '中',
      managementStrategy: '质量保证，测试计划制定',
      remark: '质量负责人，严格把控',
      communicateCount: 6,
    },
    {
      id: '7',
      name: '王五',
      company: '华为技术',
      job: '运维经理',
      telephone: '010-87654322',
      mobile: '13987654322',
      email: '<EMAIL>',
      participateDegree: '低',
      influenceMode: '中性',
      influenceDegree: '低',
      managementStrategy: '运维支持，系统监控',
      remark: '运维负责人，保障系统稳定',
      communicateCount: 4,
    },
    {
      id: '8',
      name: '赵六',
      company: '阿里巴巴',
      job: '业务分析师',
      telephone: '0571-28345679',
      mobile: '13798765433',
      email: '<EMAIL>',
      participateDegree: '高',
      influenceMode: '积极',
      influenceDegree: '中',
      managementStrategy: '需求分析，业务流程梳理',
      remark: '业务专家，深度参与',
      communicateCount: 7,
    },
  ]);

  // 沟通记录数据
  const communicationRecords = ref<CommunicationRecord[]>([
    {
      id: '1',
      title: '项目启动会议',
      content: '讨论项目目标、范围和时间计划，确定各方职责',
      type: '会议',
      date: '2024-03-15',
      creator: '项目经理',
    },
    {
      id: '2',
      title: '技术方案评审',
      content: '对技术架构和实现方案进行评审，提出优化建议',
      type: '邮件',
      date: '2024-03-10',
      creator: '技术总监',
    },
    {
      id: '3',
      title: '需求澄清电话',
      content: '针对模糊需求进行详细澄清，确保理解一致',
      type: '电话',
      date: '2024-03-08',
      creator: '业务分析师',
    },
    {
      id: '4',
      title: '进度汇报',
      content: '汇报项目当前进展情况，讨论存在的问题和解决方案',
      type: '会议',
      date: '2024-03-05',
      creator: '项目经理',
    },
  ]);

  // 表格列配置
  const stakeholderColumns: TableColumnsType = [
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      fixed: 'left',
    },
    {
      title: '公司',
      dataIndex: 'company',
      key: 'company',
      width: 150,
    },
    {
      title: '联系方式',
      dataIndex: 'telephone',
      key: 'telephone',
      width: 150,
    },
    {
      title: '参与程度',
      dataIndex: 'participateDegree',
      key: 'participateDegree',
      width: 120,
    },
    {
      title: '影响方式',
      dataIndex: 'influenceMode',
      key: 'influenceMode',
      width: 120,
    },
    {
      title: '影响程度',
      dataIndex: 'influenceDegree',
      key: 'influenceDegree',
      width: 120,
    },
    {
      title: '管理策略',
      dataIndex: 'managementStrategy',
      key: 'managementStrategy',
      width: 200,
      ellipsis: true,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      width: 40,
    },
    {
      title: '沟通记录',
      dataIndex: 'communicateCount',
      key: 'communicateCount',
      width: 80,
      align: 'center',
    },
    {
      title: '操作',
      key: 'action',
      width: 160,
      fixed: 'right',
    },
  ];

  // 计算过滤后的干系人列表
  const filteredStakeholders = computed(() => {
    let filtered = stakeholders.value;

    if (searchText.value) {
      filtered = filtered.filter(
        stakeholder =>
          stakeholder.name.toLowerCase().includes(searchText.value.toLowerCase()) || stakeholder.company.toLowerCase().includes(searchText.value.toLowerCase()),
      );
    }

    if (companyFilter.value) {
      filtered = filtered.filter(stakeholder => stakeholder.company === companyFilter.value);
    }

    if (participateDegreeFilter.value) {
      filtered = filtered.filter(stakeholder => stakeholder.participateDegree === participateDegreeFilter.value);
    }

    return filtered;
  });

  // 获取用户颜色
  const getUserColor = (name: string) => {
    const colors = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1', '#13c2c2'];
    const index = name.charCodeAt(0) % colors.length;
    return colors[index];
  };

  // 获取参与度颜色
  const getParticipationColor = (degree: string) => {
    const colorMap: Record<string, string> = {
      高: 'red',
      中: 'orange',
      低: 'green',
    };
    return colorMap[degree] || 'default';
  };

  // 获取影响力颜色
  const getInfluenceColor = (influence: string) => {
    const colorMap: Record<string, string> = {
      积极: 'green',
      消极: 'red',
      中性: 'blue',
      高: 'red',
      中: 'orange',
      低: 'green',
    };
    return colorMap[influence] || 'default';
  };

  // 获取沟通记录颜色
  const getCommunicationColor = (type: string) => {
    const colorMap: Record<string, string> = {
      邮件: 'blue',
      电话: 'green',
      会议: 'orange',
      其他: 'default',
    };
    return colorMap[type] || 'default';
  };

  // 搜索处理
  const handleSearch = () => {
    console.log('搜索:', searchText.value);
  };

  // 公司筛选
  const handleCompanyFilter = () => {
    console.log('公司筛选:', companyFilter.value);
  };

  // 参与程度筛选
  const handleParticipateFilter = () => {
    console.log('参与程度筛选:', participateDegreeFilter.value);
  };

  // 刷新处理
  const handleRefresh = () => {
    loadData();
    message.success('刷新成功');
  };

  // 导出处理
  const handleExport = () => {
    message.info('导出功能开发中...');
  };

  // 添加干系人
  const handleAdd = () => {
    resetModalForm();
    modalVisible.value = true;
  };

  // 编辑干系人
  const handleEdit = (record: Stakeholder) => {
    editingRecord.value = record.id;
    backupData.value = { ...record };
  };

  // 保存编辑
  const handleSave = async (record: Stakeholder) => {
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));

      editingRecord.value = null;
      backupData.value = null;
      message.success('保存成功');
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败');
    }
  };

  // 取消编辑
  const handleCancel = (record: Stakeholder) => {
    if (backupData.value) {
      Object.assign(record, backupData.value);
    }
    editingRecord.value = null;
    backupData.value = null;
  };

  // 字段变更处理
  const handleFieldChange = (record: Stakeholder, field: string) => {
    console.log('字段变更:', field, record[field as keyof Stakeholder]);
  };

  // 删除干系人
  const handleDelete = (record: Stakeholder) => {
    const index = stakeholders.value.findIndex(s => s.id === record.id);
    if (index > -1) {
      stakeholders.value.splice(index, 1);
      updateStats();
      message.success('删除成功');
    }
  };

  // 查看沟通记录
  const handleViewCommunications = (record: Stakeholder) => {
    selectedStakeholder.value = record;
    communicationModalVisible.value = true;
  };

  // 表格变化处理
  const handleTableChange = (pag: any) => {
    pagination.current = pag.current;
    pagination.pageSize = pag.pageSize;
  };

  // 重置模态框表单
  const resetModalForm = () => {
    Object.assign(modalFormData, {
      name: '',
      company: '',
      job: '',
      telephone: '',
      mobile: '',
      email: '',
      participateDegree: '',
      influenceMode: '',
      influenceDegree: '',
      managementStrategy: '',
      remark: '',
    });
  };

  // 模态框确认
  const handleModalOk = async () => {
    try {
      await modalFormRef.value.validate();
      modalLoading.value = true;

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));

      const newStakeholder: Stakeholder = {
        id: Date.now().toString(),
        name: modalFormData.name,
        company: modalFormData.company,
        job: modalFormData.job,
        telephone: modalFormData.telephone,
        mobile: modalFormData.mobile,
        email: modalFormData.email,
        participateDegree: modalFormData.participateDegree,
        influenceMode: modalFormData.influenceMode,
        influenceDegree: modalFormData.influenceDegree,
        managementStrategy: modalFormData.managementStrategy,
        remark: modalFormData.remark,
        communicateCount: 0,
      };

      stakeholders.value.push(newStakeholder);
      updateStats();
      modalVisible.value = false;
      resetModalForm();
      message.success('添加成功');
    } catch (error) {
      console.error('添加失败:', error);
      message.error('添加失败，请检查输入信息');
    } finally {
      modalLoading.value = false;
    }
  };

  // 模态框取消
  const handleModalCancel = () => {
    resetModalForm();
    modalVisible.value = false;
  };

  // 更新统计数据
  const updateStats = () => {
    stakeholderStats.total = stakeholders.value.length;
    stakeholderStats.highParticipation = stakeholders.value.filter(s => s.participateDegree === '高').length;
    stakeholderStats.highInfluence = stakeholders.value.filter(s => s.influenceDegree === '高').length;
    stakeholderStats.communications = stakeholders.value.reduce((sum, s) => sum + s.communicateCount, 0);
    pagination.total = stakeholders.value.length;
  };

  // 加载数据
  const loadData = async () => {
    loading.value = true;
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));

      updateStats();
      console.log('干系人数据加载完成');
    } catch (error) {
      console.error('加载失败:', error);
      message.error('加载失败，请刷新页面重试');
    } finally {
      loading.value = false;
    }
  };

  // 页面初始化
  onMounted(() => {
    loadData();
  });
</script>

<style scoped>
  .stakeholder-page {
    min-height: 100vh;
    background-color: var(--section-bg-color);
  }

  .stat-card {
    transition: all 0.3s ease;
  }

  .stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .communication-item {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 8px;
  }

  :deep(.ant-table-thead > tr > th) {
    background-color: #fafafa;
    font-weight: 600;
  }

  :deep(.ant-table-tbody > tr > td) {
    padding: 12px 16px;
  }

  :deep(.ant-timeline-item-content) {
    margin-left: 8px;
  }
</style>
