package com.xinghuo.performance.model.analysis;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 绩效分析图表数据模型
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class PerformanceAnalysisChartModel {

    /**
     * 绩效得分分布
     */
    private List<ScoreDistributionData> scoreDistribution;

    /**
     * 分部绩效对比
     */
    private List<DepartmentComparisonData> departmentComparison;

    /**
     * 绩效趋势分析
     */
    private List<TrendData> trend;

    /**
     * 评分维度分析
     */
    private List<DimensionData> dimension;

    /**
     * 得分分布数据
     */
    @Data
    public static class ScoreDistributionData {
        /**
         * 得分范围
         */
        private String scoreRange;

        /**
         * 人数
         */
        private Integer count;

        /**
         * 占比
         */
        private BigDecimal percentage;
    }

    /**
     * 分部对比数据
     */
    @Data
    public static class DepartmentComparisonData {
        /**
         * 分部ID
         */
        private String fbId;

        /**
         * 分部名称
         */
        private String fbName;

        /**
         * 平均得分
         */
        private BigDecimal avgScore;

        /**
         * 参与人数
         */
        private Integer userCount;

        /**
         * 优秀率
         */
        private BigDecimal excellentRate;
    }

    /**
     * 趋势数据
     */
    @Data
    public static class TrendData {
        /**
         * 时间周期
         */
        private String period;

        /**
         * 平均得分
         */
        private BigDecimal avgScore;

        /**
         * 参与人数
         */
        private Integer userCount;

        /**
         * 完成率
         */
        private BigDecimal completionRate;
    }

    /**
     * 维度数据
     */
    @Data
    public static class DimensionData {
        /**
         * 维度名称
         */
        private String dimensionName;

        /**
         * 平均得分
         */
        private BigDecimal avgScore;

        /**
         * 权重
         */
        private BigDecimal weight;

        /**
         * 满分
         */
        private BigDecimal fullScore;
    }
}
