/**
 * 项目模板相关TypeScript类型定义
 *
 * <AUTHOR> Code
 * @date 2025-07-31
 */

// ============================
// 基础接口定义
// ============================

/**
 * API响应基础接口
 */
export interface ApiResponse<T = any> {
  code: number;
  msg: string;
  data: T;
}

/**
 * 分页查询响应接口
 */
export interface PageResponse<T = any> {
  list: T[];
  total: number;
  current: number;
  pageSize: number;
}

/**
 * 选择器选项接口（使用标准格式）
 */
export interface SelectOption {
  id: string;
  fullName: string;
}

// ============================
// 项目模板相关接口
// ============================

/**
 * 项目模板实体接口
 */
export interface ProjectTemplateEntity {
  /** 模板ID */
  id: string;
  /** 模板名称 */
  name: string;
  /** 模板编码 */
  code?: string;
  /** 模板描述 */
  description?: string;
  /** 状态 (0:启用, 1:禁用) */
  status: number;
  /** 模板类型ID */
  typeId?: string;
  /** 模板图标 */
  icon?: string;
  /** 配置预留字段1 */
  configStr01?: string;
  /** 配置预留字段2 */
  configStr02?: string;
  /** 配置预留字段3 */
  configStr03?: string;
  /** 创建时间 */
  createdAt: string;
  /** 最后更新时间 */
  lastUpdatedAt: string;
  /** 创建人ID */
  createdBy: string;
  /** 最后更新人ID */
  lastUpdatedBy: string;
  /** 租户ID */
  tenantId: string;
  /** 删除标记 */
  deleteMark?: number;
}

/**
 * 项目模板视图对象接口（包含显示字段）
 */
export interface ProjectTemplateVO extends ProjectTemplateEntity {
  /** 状态名称 */
  statusName?: string;
  /** 模板类型名称 */
  typeName?: string;
  /** 创建人姓名 */
  createdByName?: string;
  /** 创建人用户名 */
  creatorUserName?: string;
  /** 最后更新人姓名 */
  lastUpdatedByName?: string;
  /** 完整名称（用于选择器：[编码] 名称） */
  fullName: string;

  // 统计信息字段
  /** WBS活动数量 */
  wbsCount?: number;
  /** 阶段数量 */
  phaseCount?: number;
  /** 里程碑数量 */
  milestoneCount?: number;
  /** 总工期（天） */
  totalDuration?: number;
  /** 最大WBS层级深度 */
  maxWbsLevel?: number;
  /** 是否包含审批流程 */
  hasApproval?: boolean;
  /** 是否包含检查清单 */
  hasChecklist?: boolean;
  /** 是否包含交付物模板 */
  hasWorkProduct?: boolean;

  // 关联配置数据
  /** WBS配置列表 */
  wbsList?: ProjectSchemaWbsEntity[];
  /** 阶段配置列表 */
  phaseList?: ProjectSchemaPhaseEntity[];
}

/**
 * 项目模板选择器VO接口
 */
export interface ProjectTemplateSelectVO extends SelectOption {
  /** 模板编码 */
  code?: string;
  /** 模板名称 */
  name: string;
  /** 状态 */
  status: number;
}

/**
 * 项目模板分页查询参数接口
 */
export interface ProjectTemplatePagination {
  /** 当前页 */
  current: number;
  /** 页大小 */
  pageSize: number;
  /** 模板名称（模糊查询） */
  name?: string;
  /** 模板编码（模糊查询） */
  code?: string;
  /** 状态 */
  status?: number;
  /** 模板类型ID */
  typeId?: string;
  /** 创建时间开始 */
  createTimeStart?: string;
  /** 创建时间结束 */
  createTimeEnd?: string;
  /** 创建人ID */
  createdBy?: string;
  /** 描述关键字 */
  descriptionKeyword?: string;
  /** 关键字（搜索编码或名称） */
  keyword?: string;
}

/**
 * 项目模板表单数据接口
 */
export interface ProjectTemplateForm {
  /** 模板ID（编辑时使用） */
  id?: string;
  /** 模板名称 */
  name: string;
  /** 模板编码 */
  code?: string;
  /** 模板描述 */
  description?: string;
  /** 状态 */
  status: number;
  /** 模板类型ID */
  typeId?: string;
  /** 模板图标 */
  icon?: string;
}

// ============================
// WBS配置相关接口
// ============================

/**
 * 项目模板WBS配置实体接口
 */
export interface ProjectSchemaWbsEntity {
  /** 配置ID */
  id: string;
  /** 项目模板ID */
  projectTemplateId: string;
  /** 源WBS明细ID */
  sourceWbsDetailId?: string;
  /** 源活动库ID */
  sourceLibraryActivityId?: string;
  /** 父级ID */
  parentId?: string;
  /** WBS编码 */
  wbsCode?: string;
  /** 活动名称 */
  name: string;
  /** 序号 */
  seqNo: number;
  /** 层级 */
  level: number;
  /** 节点类型 (1:活动, 3:工作包) */
  nodeType: number;
  /** 是否里程碑 (0:否, 1:是) */
  isMilestone?: number;
  /** 工期（天） */
  duration?: number;
  /** 责任角色ID */
  responseRoleId?: string;
  /** 责任角色名称 */
  responseRoleName?: string;
  /** 前置任务 */
  predecessors?: string;
  /** 创建时间 */
  createdAt: string;
  /** 最后更新时间 */
  lastUpdatedAt: string;
  /** 创建人ID */
  createdBy: string;
  /** 最后更新人ID */
  lastUpdatedBy: string;
  /** 租户ID */
  tenantId: string;
}

// ============================
// 阶段配置相关接口
// ============================

/**
 * 项目模板阶段配置实体接口
 */
export interface ProjectSchemaPhaseEntity {
  /** 配置ID */
  id: string;
  /** 项目模板ID */
  projectTemplateId: string;
  /** 阶段库ID */
  phaseLibraryId?: string;
  /** 序号 */
  seqNo: number;
  /** 阶段名称 */
  phaseName?: string;
  /** 工期（天） */
  duration?: number;
  /** 完成权重 */
  completionWeight?: number;
  /** 可裁剪 (0:否, 1:是) */
  canCut?: number;
  /** 审批流程ID */
  approvalId?: string;
  /** 审批流程名称 */
  approvalName?: string;
  /** 检查清单ID */
  checklistId?: string;
  /** 检查清单名称 */
  checklistName?: string;
  /** 交付物模板ID */
  workproductTplId?: string;
  /** 交付物模板名称 */
  workproductTplName?: string;
  /** 创建时间 */
  createdAt: string;
  /** 最后更新时间 */
  lastUpdatedAt: string;
  /** 创建人ID */
  createdBy: string;
  /** 最后更新人ID */
  lastUpdatedBy: string;
  /** 租户ID */
  tenantId: string;
}

// ============================
// 使用情况统计相关接口
// ============================

/**
 * 模板使用情况统计接口
 */
export interface TemplateUsageInfo {
  /** 总使用次数 */
  total: number;
  /** 使用详情列表 */
  details: TemplateUsageDetail[];
  /** 最近使用时间 */
  lastUsedAt?: string;
  /** 使用频率趋势 */
  usageTrend?: UsageTrendData[];
}

/**
 * 模板使用详情接口
 */
export interface TemplateUsageDetail {
  /** 项目ID */
  projectId: string;
  /** 项目名称 */
  projectName: string;
  /** 使用时间 */
  usedAt: string;
  /** 使用人ID */
  usedBy: string;
  /** 使用人姓名 */
  usedByName: string;
  /** 使用状态 (success:成功, failed:失败) */
  status: 'success' | 'failed';
  /** 备注信息 */
  remark?: string;
}

/**
 * 使用频率趋势数据接口
 */
export interface UsageTrendData {
  /** 日期 */
  date: string;
  /** 使用次数 */
  count: number;
}

// ============================
// 操作相关接口
// ============================

/**
 * 复制模板参数接口
 */
export interface CopyTemplateParams {
  /** 源模板ID */
  sourceId: string;
  /** 新模板名称 */
  newName: string;
}

/**
 * 应用到项目参数接口
 */
export interface ApplyToProjectsParams {
  /** 模板ID */
  templateId: string;
  /** 目标项目ID列表 */
  projectIds: string[];
  /** 应用策略 (merge:合并, replace:覆盖) */
  strategy: 'merge' | 'replace';
}

/**
 * 导入配置参数接口
 */
export interface ImportConfigParams {
  /** 项目模板ID */
  templateId: string;
  /** 导入类型 (wbs:WBS模板, phase:阶段模板, library:标准阶段库) */
  importType: 'wbs' | 'phase' | 'library';
  /** 源模板ID或阶段ID列表 */
  sourceIds: string | string[];
}

// ============================
// 组件Props接口
// ============================

/**
 * FormDrawer组件Props接口
 */
export interface FormDrawerProps {
  /** 是否为更新模式 */
  isUpdate: boolean;
  /** 编辑的记录数据 */
  record?: ProjectTemplateVO;
}

/**
 * DetailDrawer组件Props接口
 */
export interface DetailDrawerProps {
  /** 查看的记录数据 */
  record: ProjectTemplateVO;
}

/**
 * CopyModal组件Props接口
 */
export interface CopyModalProps {
  /** 源记录数据 */
  record: ProjectTemplateVO;
}

/**
 * 配置抽屉组件Props接口
 */
export interface ConfigDrawerProps {
  /** 配置的模板记录 */
  record: ProjectTemplateVO;
  /** 配置类型 (wbs:WBS配置, phase:阶段配置) */
  configType: 'wbs' | 'phase';
}
