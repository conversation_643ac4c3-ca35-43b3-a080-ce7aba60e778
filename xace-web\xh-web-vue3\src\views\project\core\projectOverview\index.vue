<template>
  <Layout
    v-if="menuData.length || !selectedProjectId"
    :menu-data="menuData"
    @onChangeMain="data => onMenuChange(data, 'mainMenu')"
    @onChangeSub="data => onMenuChange(data, 'subMenu')">
    <template #header>
      <div class="px-[8px] relative flex items-center">
        <div class="flex items-center space-x-4">
          <ProjectSelector v-model:value="selectedProjectId" :query-type="currentQueryType" @change="onProjectChange" />
        </div>
        <div class="ml-auto flex items-center">
          <Tooltip title="刷新数据">
            <a-button class="ml-[8px]" type="text" @click="reload">
              <template #icon><ReloadOutlined /></template>
            </a-button>
          </Tooltip>
          <Tooltip title="全屏显示">
            <a-button class="ml-[8px]" type="text" @click="toggleFullscreen">
              <template #icon><FullscreenOutlined /></template>
            </a-button>
          </Tooltip>
        </div>
      </div>
    </template>

    <!-- 主要内容区域 -->
    <div class="project-overview-content h-full overflow-auto">
      <!-- 如果没有选择项目，显示快速访问卡片 -->
      <div v-if="!selectedProjectId" class="welcome-section">
        <QuickAccessCards @query-type-change="onQueryTypeChange" @project-select="onProjectSelect" @enter-project="onEnterProject" />
      </div>

      <!-- 如果选择了项目但没有选择菜单，显示项目概况 -->
      <div v-else-if="!activeMenuKey" class="project-dashboard">
        <ProjectInfoCard :project-id="selectedProjectId" @enter-project="onEnterProject" />
      </div>

      <!-- 如果选择了项目和菜单，显示对应组件 -->
      <div v-else>
        <Spin :spinning="loading">
          <keep-alive :key="`${activeMenuKey}-${selectedProjectId}`">
            <component :project-id="selectedProjectId" :is="activeComponent" @success="reload" />
          </keep-alive>
        </Spin>
      </div>
    </div>
  </Layout>
</template>

<script setup lang="ts">
  import { ref, onMounted, markRaw, provide, watch } from 'vue';
  import { Spin, Tooltip } from 'ant-design-vue';
  import { ReloadOutlined, FullscreenOutlined } from '@ant-design/icons-vue';
  import Layout from '/@/views/project/portfolio/layout.vue';
  import Exception from '/@/views/basic/exception/Exception.vue';
  import ProjectSelector from './components/ProjectSelector.vue';
  import ProjectInfoCard from './components/ProjectInfoCard.vue';
  import QuickAccessCards from './components/QuickAccessCards.vue';
  import { getProjectListByType } from '/@/api/project/projectBase';
  import { getContractList, type ContractModel } from '/@/api/project/contract';
  import { to } from '/@/utils/xh';
  import { useMessage } from '/@/hooks/web/useMessage';

  const { createMessage } = useMessage();

  const menuData = ref<any[]>([]);
  const activeComponent = ref();
  const activeMenuKey = ref('');
  const renderKey = ref(0);
  const loading = ref(false);
  const componentCache = new Map();

  // 项目选择相关
  const selectedProjectId = ref('');
  const currentQueryType = ref('RECENT_VISITED');
  const selectedProject = ref<any>(null);

  // 合同相关状态
  const contractInfo = ref<ContractModel | null>(null);
  const contractId = ref('');
  const hasContract = ref(false);

  // 向子组件提供项目ID和合同信息
  provide('projectId', selectedProjectId);
  provide('contractId', contractId);
  provide('contractInfo', contractInfo);
  provide('hasContract', hasContract);

  // 每个路由组件需要按照系统菜单严格匹配，否则开启缓存后不生效
  defineOptions({ name: 'project-core-projectOverview' });

  const reload = () => {
    console.log('重新加载项目概览数据', selectedProjectId.value);
    renderKey.value++;
    // 清除组件缓存，强制重新加载
    componentCache.clear();
    // 重新加载当前组件
    if (activeMenuKey.value) {
      loadComponent(activeMenuKey.value);
    }
  };

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
    } else {
      document.exitFullscreen();
    }
  };

  // 项目选择相关方法
  const onProjectChange = async (project: any) => {
    const oldProjectId = selectedProjectId.value;
    const newProjectId = project.id;

    console.log('🔄 [onProjectChange] 项目切换请求:', {
      旧项目: oldProjectId,
      新项目: newProjectId,
      项目名称: project.fullName,
      是否真正切换: oldProjectId !== newProjectId,
    });

    // 如果是相同项目，跳过处理
    if (oldProjectId === newProjectId) {
      console.log('⚠️ [onProjectChange] 相同项目，跳过切换');
      return;
    }

    selectedProject.value = project;

    // 🚨 关键修复：先更新合同数据，再更新项目ID
    console.log('⏳ [onProjectChange] 开始检查新项目的合同信息...');
    await checkProjectContract(newProjectId);
    console.log('✅ [onProjectChange] 合同信息检查完成，现在更新项目ID');

    // 现在再更新项目ID，这时provide的数据已经是最新的
    selectedProjectId.value = newProjectId;

    // 项目切换后加载菜单
    loadProjectMenu(true); // 强制重新加载菜单
    console.log('✅ [onProjectChange] 项目切换完成:', project);
  };

  const onQueryTypeChange = (queryType: string) => {
    currentQueryType.value = queryType;
    console.log('查询类型已切换:', queryType);
  };

  const onProjectSelect = async (project: any) => {
    const oldProjectId = selectedProjectId.value;
    const newProjectId = project.id;

    console.log('🔄 [onProjectSelect] 项目选择请求:', {
      旧项目: oldProjectId,
      新项目: newProjectId,
      是否真正切换: oldProjectId !== newProjectId,
    });

    // 如果是相同项目，跳过处理
    if (oldProjectId === newProjectId) {
      console.log('⚠️ [onProjectSelect] 相同项目，跳过切换');
      return;
    }

    // 先更新合同数据，再更新项目ID
    await checkProjectContract(newProjectId);
    selectedProjectId.value = newProjectId;
    selectedProject.value = project;

    // 项目选择后加载菜单
    loadProjectMenu(true);
    console.log('✅ [onProjectSelect] 项目选择完成:', project);
  };

  const onEnterProject = async (project: any) => {
    const newProjectId = typeof project === 'string' ? project : project.id;
    const oldProjectId = selectedProjectId.value;

    console.log('🔄 [onEnterProject] 进入项目请求:', {
      旧项目: oldProjectId,
      新项目: newProjectId,
      是否真正切换: oldProjectId !== newProjectId,
    });

    // 如果是相同项目，跳过处理
    if (oldProjectId === newProjectId) {
      console.log('⚠️ [onEnterProject] 相同项目，跳过切换');
      return;
    }

    // 先更新合同数据，再更新项目ID
    await checkProjectContract(newProjectId);
    selectedProjectId.value = newProjectId;

    // 加载项目菜单
    loadProjectMenu(true);
    console.log('✅ [onEnterProject] 进入项目完成:', newProjectId);
  };

  // 检查菜单项是否存在于菜单数据中
  const findMenuItemByKey = (menuData: any[], menuKey: string): any => {
    for (const menu of menuData) {
      if (menu.children) {
        for (const child of menu.children) {
          if (child.enCode === menuKey) {
            return child;
          }
        }
      }
    }
    return null;
  };

  // 检查项目是否有合同
  const checkProjectContract = async (projectId: string) => {
    console.log('📋 [checkProjectContract] 开始检查项目合同:', projectId);

    if (!projectId) {
      console.log('⚠️ [checkProjectContract] 项目ID为空，清空合同信息');
      hasContract.value = false;
      contractInfo.value = null;
      contractId.value = '';
      return false;
    }

    try {
      const params = {
        currentPage: 1,
        pageSize: 1,
        projBaseId: projectId,
        projectId: projectId,
      };

      console.log('🔍 [checkProjectContract] 调用API查询合同:', params);
      const response = await getContractList(params);
      console.log('📄 [checkProjectContract] API响应:', response);

      // 详细分析API响应数据
      if (response && response.data && response.data.list) {
        console.log('🔍 [checkProjectContract] 合同列表详情:', {
          查询项目ID: projectId,
          返回合同数量: response.data.list.length,
          合同列表: response.data.list.map(contract => ({
            合同ID: contract.id,
            合同名称: contract.name,
            关联项目ID: contract.projectId || contract.projBaseId,
            合同编号: contract.cno,
            完整合同对象: contract,
          })),
        });
      }

      // 详细分析API响应数据
      if (response && response.data && response.data.list) {
        console.log('🔍 [checkProjectContract] 合同列表详情:', {
          查询项目ID: projectId,
          返回合同数量: response.data.list.length,
          合同列表: response.data.list.map(contract => ({
            合同ID: contract.id,
            合同名称: contract.name,
            关联项目ID: contract.projectId || contract.projBaseId,
            合同编号: contract.cno,
          })),
        });
      }

      if (!response) {
        console.error('❌ [checkProjectContract] API响应为空');
        hasContract.value = false;
        contractInfo.value = null;
        contractId.value = '';
        return false;
      }

      if (response && response.data && response.data.list && response.data.list.length > 0) {
        // 项目有合同
        const contract = response.data.list[0];
        hasContract.value = true;
        contractInfo.value = contract;
        contractId.value = contract.id || '';
        console.log('✅ [checkProjectContract] 项目有合同，更新provide数据:', {
          合同名称: contract.name,
          合同ID: contract.id,
          hasContract: hasContract.value,
          contractInfo: contractInfo.value,
        });
        return true;
      } else {
        // 项目没有合同
        hasContract.value = false;
        contractInfo.value = null;
        contractId.value = '';
        console.log('⚠️ [checkProjectContract] 项目没有合同，清空provide数据');
        return false;
      }
    } catch (error) {
      console.error('❌ [checkProjectContract] 检查项目合同失败:', error);
      hasContract.value = false;
      contractInfo.value = null;
      contractId.value = '';
      return false;
    }
  };

  // 过滤菜单数据，根据项目是否有合同决定是否显示合同管理菜单
  const filterMenuData = (menuData: any[], hasContract: boolean) => {
    console.log('🔍 [filterMenuData] 菜单过滤开始:', {
      原始菜单数量: menuData.length,
      项目是否有合同: hasContract,
      原始菜单: menuData.map(m => ({ id: m.id, fullName: m.fullName })),
    });

    if (!hasContract) {
      // 如果没有合同，过滤掉合同管理菜单（id为"2"）
      const filtered = menuData.filter(menu => menu.id !== '2');
      console.log(
        '⚠️ [filterMenuData] 项目无合同，过滤后菜单:',
        filtered.map(m => ({ id: m.id, fullName: m.fullName })),
      );
      return filtered;
    }

    console.log(
      '✅ [filterMenuData] 项目有合同，保留所有菜单:',
      menuData.map(m => ({ id: m.id, fullName: m.fullName })),
    );
    return menuData;
  };

  // 加载项目菜单
  const loadProjectMenu = async (isProjectChanged = false) => {
    try {
      // 1. 加载静态菜单数据
      const response = await fetch(import.meta.env.VITE_PUBLIC_PATH + 'data/projectOverview.json');
      const data = await response.json();

      // 2. 检查项目是否有合同
      const projectHasContract = await checkProjectContract(selectedProjectId.value);

      // 3. 根据合同情况过滤菜单
      const filteredMenuData = filterMenuData(data || [], projectHasContract);
      menuData.value = filteredMenuData;

      console.log('📋 [loadProjectMenu] 菜单加载完成:', {
        项目ID: selectedProjectId.value,
        项目是否有合同: projectHasContract,
        原始菜单数量: data?.length || 0,
        过滤后菜单数量: filteredMenuData.length,
        过滤后菜单: filteredMenuData.map(m => ({ id: m.id, fullName: m.fullName })),
      });

      if (filteredMenuData && filteredMenuData.length > 0) {
        if (isProjectChanged && activeMenuKey.value) {
          // 项目切换时，检查当前菜单是否在新项目中也存在
          const currentMenuItem = findMenuItemByKey(filteredMenuData, activeMenuKey.value);

          if (currentMenuItem) {
            // 当前菜单在新项目中也存在，保持当前菜单，只刷新数据
            console.log('保持当前菜单:', currentMenuItem.fullName);
            // 清除组件缓存，强制重新加载当前组件以获取新项目数据
            componentCache.delete(activeMenuKey.value);
            loadComponent(activeMenuKey.value);
          } else {
            // 当前菜单在新项目中不存在，跳转到第一个菜单项
            console.log('当前菜单不存在，跳转到默认菜单');
            onMenuChange(filteredMenuData[0], 'mainMenu');
          }
        } else if (!activeMenuKey.value) {
          // 没有当前活动菜单，加载第一个菜单项
          onMenuChange(filteredMenuData[0], 'mainMenu');
        } else {
          // 项目没有切换，保持当前页面但刷新组件（传入新的项目ID）
          if (activeComponent.value && activeMenuKey.value) {
            // 清除组件缓存，强制重新加载当前组件
            componentCache.delete(activeMenuKey.value);
            loadComponent(activeMenuKey.value);
          }
        }
      }
    } catch (error) {
      console.error('加载项目概览菜单数据失败:', error);
      menuData.value = [];
    }
  };

  onMounted(async () => {
    // 尝试获取最近访问的项目
    try {
      const [err, res] = await to(
        getProjectListByType({
          queryType: 'RECENT_VISITED',
          pageSize: 1,
        }),
      );

      if (!err && res?.data?.list && res.data.list.length > 0) {
        const recentProject = res.data.list[0];
        selectedProjectId.value = recentProject.id;
        selectedProject.value = recentProject;
        loadProjectMenu();
        console.log('已自动选择最近访问的项目:', recentProject);
      }
    } catch (error) {
      console.warn('获取最近访问项目失败:', error);
    }
  });

  // 监听项目ID变化，重新检查合同信息
  // 注意：在onProjectChange中已经提前调用了checkProjectContract，这里主要处理其他触发场景
  watch(
    selectedProjectId,
    async (newProjectId, oldProjectId) => {
      console.log('🔄 [父组件Watch] selectedProjectId变化:', { 旧项目: oldProjectId, 新项目: newProjectId });
      // 只处理非onProjectChange触发的场景（例如初始化、程序设置等）
      if (newProjectId && newProjectId !== oldProjectId) {
        console.log('📋 [父组件Watch] 检查是否需要重新加载合同信息:', newProjectId);
        // 确保项目切换时合同信息正确更新
        await checkProjectContract(newProjectId);
        // 🔧 关键修复：重新加载菜单以应用合同状态变化
        await loadProjectMenu(true);
        console.log('✅ [父组件Watch] 项目ID变化处理完成');
      } else {
        console.log('⚠️ [父组件Watch] 跳过检查 - 项目ID无效或相同');
      }
    },
    { immediate: false },
  );

  const loadComponent = async (enCode: string) => {
    loading.value = true;

    if (componentCache.has(enCode)) {
      activeComponent.value = componentCache.get(enCode);
      loading.value = false;
      return;
    }

    try {
      let module: any;

      // 处理包含斜杠的路径（如 opportunity/dashboard）
      if (enCode.includes('/')) {
        const [folder, subpage] = enCode.split('/');
        // 使用更具体的导入模式，让 Vite 能够正确解析
        const modules = import.meta.glob('./pages/*/*/page.vue');
        const modulePath = `./pages/${folder}/${subpage}/page.vue`;

        if (modules[modulePath]) {
          module = await modules[modulePath]();
        } else {
          throw new Error(`Component not found: ${modulePath}`);
        }
      } else {
        // 处理简单路径（如 dashboard）
        const modules = import.meta.glob('./pages/*/page.vue');
        const modulePath = `./pages/${enCode}/page.vue`;

        if (modules[modulePath]) {
          module = await modules[modulePath]();
        } else {
          throw new Error(`Component not found: ${modulePath}`);
        }
      }

      activeComponent.value = markRaw(module.default);
      componentCache.set(enCode, markRaw(module.default));
      loading.value = false;
    } catch (error) {
      console.error(`加载组件失败: ${enCode}`, error);
      activeComponent.value = markRaw(Exception);
      loading.value = false;
    }
  };

  const onMenuChange = (data: any, mainOrSub: string) => {
    console.log(`菜单切换 (${mainOrSub}):`, data);

    if (data.enCode) {
      activeMenuKey.value = data.enCode;
      loadComponent(data.enCode);
    } else {
      activeComponent.value = markRaw(Exception);
      loading.value = false;
    }
  };
</script>

<style lang="less" scoped>
  .project-overview-content {
    .welcome-section {
      background: var(--content-bg-color);
      border-radius: 8px;
      padding: 24px;
      margin: 16px;
    }

    .project-dashboard {
      padding: 16px;

      :deep(.ant-card) {
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      }
    }
  }

  .icon-ym {
    font-size: 16px;
  }
</style>
