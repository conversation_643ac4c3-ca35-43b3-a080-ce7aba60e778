import { defHttp } from '/@/utils/http/axios';
import { ListResult } from '/@/api/model/baseModel';

/**
 * 项目组合管理API
 */

// API URL前缀
const API_PREFIX = '/api/project/portfolio/portfolio';

/**
 * 项目组合对象接口
 */
export interface PortfolioModel {
  id: string;
  code: string;
  name: string;
  description?: string;
  typeId?: string;
  ownerId?: string;
  status?: string;
  startDate?: string;
  endDate?: string;
  budget?: number;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * 项目组合表单接口
 */
export interface PortfolioFormModel {
  code: string;
  name: string;
  description?: string;
  typeId?: string;
  ownerId?: string;
  status?: string;
  startDate?: string;
  endDate?: string;
  budget?: number;
}

/**
 * 项目组合查询参数接口
 */
export interface PortfolioQueryParams {
  code?: string;
  name?: string;
  typeId?: string;
  ownerId?: string;
  status?: string;
  keyword?: string;
  pageSize?: number;
  currentPage?: number;
  sort?: string;
  sidx?: string;
}

/**
 * 获取项目组合列表
 * @param params 查询参数
 * @returns 项目组合列表
 */
export const getPortfolioList = (params?: PortfolioQueryParams) => {
  return defHttp.post<ListResult<PortfolioModel>>({
    url: `${API_PREFIX}/getList`,
    data: params,
  });
};

/**
 * 根据负责人ID获取组合列表
 * @param ownerId 负责人ID
 * @returns 组合列表
 */
export const getPortfolioListByOwnerId = (ownerId: string) => {
  return defHttp.get<PortfolioModel[]>({
    url: `${API_PREFIX}/getListByOwnerId/${ownerId}`,
  });
};

/**
 * 根据组合类型ID获取组合列表
 * @param typeId 组合类型ID
 * @returns 组合列表
 */
export const getPortfolioListByTypeId = (typeId: string) => {
  return defHttp.get<PortfolioModel[]>({
    url: `${API_PREFIX}/getListByTypeId/${typeId}`,
  });
};

/**
 * 根据状态获取组合列表
 * @param status 状态
 * @returns 组合列表
 */
export const getPortfolioListByStatus = (status: string) => {
  return defHttp.get<PortfolioModel[]>({
    url: `${API_PREFIX}/getListByStatus/${status}`,
  });
};

/**
 * 获取项目组合详情
 * @param id 组合ID
 * @returns 组合详情
 */
export const getPortfolioInfo = (id: string) => {
  return defHttp.get<PortfolioModel>({
    url: `${API_PREFIX}/getInfo/${id}`,
  });
};

/**
 * 创建项目组合
 * @param params 组合创建参数
 * @returns 操作结果
 */
export const createPortfolio = (params: PortfolioFormModel) => {
  return defHttp.post<string>({
    url: `${API_PREFIX}/create`,
    data: params,
  });
};

/**
 * 更新项目组合
 * @param id 组合ID
 * @param params 组合更新参数
 * @returns 操作结果
 */
export const updatePortfolio = (id: string, params: PortfolioFormModel) => {
  return defHttp.put<void>({
    url: `${API_PREFIX}/update/${id}`,
    data: params,
  });
};

/**
 * 删除项目组合
 * @param id 组合ID
 * @returns 操作结果
 */
export const deletePortfolio = (id: string) => {
  return defHttp.delete<void>({
    url: `${API_PREFIX}/delete/${id}`,
  });
};

/**
 * 检查组合编码是否存在
 * @param code 组合编码
 * @param excludeId 排除的ID
 * @returns 是否存在
 */
export const checkPortfolioCodeExists = (code: string, excludeId?: string) => {
  return defHttp.get<boolean>({
    url: `${API_PREFIX}/checkCodeExists`,
    params: {
      code,
      excludeId,
    },
  });
};
