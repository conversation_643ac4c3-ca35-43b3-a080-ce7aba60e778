<template>
  <div class="template-team-page p-4">
    <!-- 页面标题 -->
    <div class="page-header mb-4">
      <h2 class="text-xl font-semibold mb-2">团队配置</h2>
      <p class="text-gray-600">配置项目模板的团队角色和权限设置</p>
    </div>

    <a-spin :spinning="loading">
      <!-- 工具栏 -->
      <div class="toolbar mb-4 flex justify-between items-center">
        <div class="search-area flex items-center space-x-4">
          <a-input-search v-model:value="searchText" placeholder="搜索角色名称" style="width: 300px" @search="handleSearch" />
          <a-select v-model:value="roleTypeFilter" placeholder="筛选角色类型" style="width: 150px" allow-clear @change="handleRoleTypeFilter">
            <a-select-option value="manager">项目经理</a-select-option>
            <a-select-option value="developer">开发人员</a-select-option>
            <a-select-option value="tester">测试人员</a-select-option>
            <a-select-option value="analyst">需求分析师</a-select-option>
            <a-select-option value="designer">设计师</a-select-option>
          </a-select>
        </div>
        <div class="actions">
          <a-space>
            <a-button type="primary" @click="handleAdd">
              <template #icon><PlusOutlined /></template>
              添加团队角色
            </a-button>
            <a-button @click="handleImportFromTemplate">
              <template #icon><ImportOutlined /></template>
              从模板导入
            </a-button>
            <a-button @click="handleRefresh">
              <template #icon><ReloadOutlined /></template>
              刷新
            </a-button>
          </a-space>
        </div>
      </div>

      <!-- 团队角色表格 -->
      <BasicTable @register="registerTable">
        <!-- 自定义列 -->
        <template #roleName="{ record }">
          <div class="flex items-center">
            <div class="role-icon mr-2">
              <a-avatar :style="{ backgroundColor: getRoleColor(record.roleType) }" :size="24">
                <template #icon>
                  <component :is="getRoleIcon(record.roleType)" />
                </template>
              </a-avatar>
            </div>
            <div>
              <div class="font-medium">{{ record.roleName }}</div>
              <div class="text-sm text-gray-500">{{ record.roleCode }}</div>
            </div>
          </div>
        </template>

        <template #roleType="{ record }">
          <a-tag :color="getRoleTypeColor(record.roleType)">
            {{ getRoleTypeText(record.roleType) }}
          </a-tag>
        </template>

        <template #permissions="{ record }">
          <div class="permissions">
            <a-tag v-for="permission in record.permissions" :key="permission" size="small" class="mb-1">
              {{ permission }}
            </a-tag>
            <span v-if="!record.permissions?.length" class="text-gray-400">-</span>
          </div>
        </template>

        <template #status="{ record }">
          <a-tag :color="record.status === 'active' ? 'green' : 'red'">
            {{ record.status === 'active' ? '启用' : '禁用' }}
          </a-tag>
        </template>

        <template #action="{ record }">
          <TableAction
            :actions="[
              {
                icon: 'ant-design:edit-outlined',
                label: '编辑',
                onClick: handleEdit.bind(null, record),
              },
              {
                icon: 'ant-design:copy-outlined',
                label: '复制',
                onClick: handleCopy.bind(null, record),
              },
              {
                icon: 'ant-design:setting-outlined',
                label: '权限设置',
                onClick: handlePermissions.bind(null, record),
              },
              {
                icon: 'ant-design:delete-outlined',
                label: '删除',
                color: 'error',
                popConfirm: {
                  title: '是否确认删除？',
                  placement: 'left',
                  confirm: handleDelete.bind(null, record),
                },
              },
            ]" />
        </template>
      </BasicTable>
    </a-spin>

    <!-- 添加/编辑弹窗 -->
    <BasicDrawer @register="registerDrawer" :title="drawerTitle" width="50%" @ok="handleSubmit">
      <BasicForm @register="registerForm" />
    </BasicDrawer>

    <!-- 权限设置弹窗 -->
    <BasicModal @register="registerPermissionModal" title="权限设置" width="60%" @ok="handlePermissionSubmit">
      <div class="permission-content">
        <div class="current-role mb-4 p-3 bg-gray-50 rounded">
          <h4 class="font-medium">当前角色：{{ currentPermissionRecord?.roleName }}</h4>
        </div>

        <div class="permission-groups">
          <div v-for="group in permissionGroups" :key="group.key" class="mb-6">
            <h5 class="font-medium mb-3 text-gray-700">{{ group.label }}</h5>
            <a-checkbox-group v-model:value="selectedPermissions[group.key]">
              <a-row :gutter="[16, 8]">
                <a-col :span="8" v-for="permission in group.permissions" :key="permission.value">
                  <a-checkbox :value="permission.value">{{ permission.label }}</a-checkbox>
                </a-col>
              </a-row>
            </a-checkbox-group>
          </div>
        </div>
      </div>
    </BasicModal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed, inject, onMounted } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { BasicForm, useForm } from '/@/components/Form';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import {
    PlusOutlined,
    ImportOutlined,
    ReloadOutlined,
    UserOutlined,
    CodeOutlined,
    BugOutlined,
    FileTextOutlined,
    BgColorsOutlined,
  } from '@ant-design/icons-vue';

  defineOptions({ name: 'ProjectTemplateTeamConfigPage' });

  // 注入模板ID
  const templateId = inject<any>('templateId');
  const { createMessage } = useMessage();

  const loading = ref(false);
  const searchText = ref('');
  const roleTypeFilter = ref('');
  const drawerTitle = ref('');
  const currentRecord = ref<any>(null);
  const currentPermissionRecord = ref<any>(null);

  // 权限分组数据
  const permissionGroups = ref([
    {
      key: 'project',
      label: '项目管理',
      permissions: [
        { label: '查看项目', value: 'project_view' },
        { label: '编辑项目', value: 'project_edit' },
        { label: '删除项目', value: 'project_delete' },
        { label: '项目设置', value: 'project_setting' },
      ],
    },
    {
      key: 'task',
      label: '任务管理',
      permissions: [
        { label: '查看任务', value: 'task_view' },
        { label: '创建任务', value: 'task_create' },
        { label: '编辑任务', value: 'task_edit' },
        { label: '删除任务', value: 'task_delete' },
        { label: '分配任务', value: 'task_assign' },
      ],
    },
    {
      key: 'document',
      label: '文档管理',
      permissions: [
        { label: '查看文档', value: 'doc_view' },
        { label: '创建文档', value: 'doc_create' },
        { label: '编辑文档', value: 'doc_edit' },
        { label: '删除文档', value: 'doc_delete' },
      ],
    },
    {
      key: 'report',
      label: '报告管理',
      permissions: [
        { label: '查看报告', value: 'report_view' },
        { label: '生成报告', value: 'report_generate' },
        { label: '导出报告', value: 'report_export' },
      ],
    },
  ]);

  const selectedPermissions = ref<Record<string, string[]>>({
    project: [],
    task: [],
    document: [],
    report: [],
  });

  // 表格列配置
  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      width: 60,
      customRender: ({ index }) => index + 1,
    },
    {
      title: '角色名称',
      dataIndex: 'roleName',
      width: 200,
      slots: { customRender: 'roleName' },
    },
    {
      title: '角色类型',
      dataIndex: 'roleType',
      width: 120,
      slots: { customRender: 'roleType' },
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 80,
      slots: { customRender: 'status' },
    },
    {
      title: '人员数量',
      dataIndex: 'memberCount',
      width: 100,
      customRender: ({ text }) => text || 0,
    },
    {
      title: '权限',
      dataIndex: 'permissions',
      width: 200,
      slots: { customRender: 'permissions' },
    },
    {
      title: '描述',
      dataIndex: 'description',
      width: 200,
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 150,
      customRender: ({ text }) => (text ? new Date(text).toLocaleDateString() : '-'),
    },
  ];

  // 表单配置
  const formSchemas = [
    {
      field: 'roleName',
      label: '角色名称',
      component: 'Input',
      required: true,
      colProps: { span: 12 },
    },
    {
      field: 'roleCode',
      label: '角色编码',
      component: 'Input',
      required: true,
      colProps: { span: 12 },
    },
    {
      field: 'roleType',
      label: '角色类型',
      component: 'Select',
      required: true,
      componentProps: {
        options: [
          { label: '项目经理', value: 'manager' },
          { label: '开发人员', value: 'developer' },
          { label: '测试人员', value: 'tester' },
          { label: '需求分析师', value: 'analyst' },
          { label: '设计师', value: 'designer' },
        ],
      },
      colProps: { span: 12 },
    },
    {
      field: 'status',
      label: '状态',
      component: 'RadioGroup',
      defaultValue: 'active',
      componentProps: {
        options: [
          { label: '启用', value: 'active' },
          { label: '禁用', value: 'inactive' },
        ],
      },
      colProps: { span: 12 },
    },
    {
      field: 'description',
      label: '描述',
      component: 'InputTextArea',
      componentProps: {
        rows: 3,
      },
      colProps: { span: 24 },
    },
  ];

  // 表格实例
  const [registerTable, { reload, getDataSource }] = useTable({
    api: loadTeamData,
    columns,
    useSearchForm: false,
    actionColumn: {
      width: 200,
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
    },
  });

  // 表单实例
  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 100,
    schemas: formSchemas,
    showActionButtonGroup: false,
  });

  // 抽屉实例
  const [registerDrawer, { openDrawer, closeDrawer }] = useDrawerInner();

  // 权限设置弹窗实例
  const [registerPermissionModal, { openModal: openPermissionModal, closeModal: closePermissionModal }] = useModalInner();

  // 加载团队数据
  async function loadTeamData() {
    if (!templateId?.value) return { list: [], total: 0 };

    loading.value = true;
    try {
      // 模拟数据
      const mockData = [
        {
          id: '1',
          roleName: '项目经理',
          roleCode: 'ROLE_MANAGER',
          roleType: 'manager',
          status: 'active',
          memberCount: 1,
          permissions: ['项目管理', '任务分配', '进度监控'],
          description: '负责项目整体管理和协调',
          createdAt: '2025-01-15T10:00:00Z',
        },
        {
          id: '2',
          roleName: '高级开发工程师',
          roleCode: 'ROLE_SENIOR_DEV',
          roleType: 'developer',
          status: 'active',
          memberCount: 3,
          permissions: ['任务执行', '代码审查', '技术决策'],
          description: '负责核心功能开发和技术架构',
          createdAt: '2025-01-15T10:00:00Z',
        },
        {
          id: '3',
          roleName: '测试工程师',
          roleCode: 'ROLE_TESTER',
          roleType: 'tester',
          status: 'active',
          memberCount: 2,
          permissions: ['测试计划', '缺陷管理', '质量评估'],
          description: '负责系统测试和质量保证',
          createdAt: '2025-01-15T10:00:00Z',
        },
      ];

      return { list: mockData, total: mockData.length };
    } catch (error) {
      console.error('加载团队数据失败:', error);
      createMessage.error('加载团队数据失败');
      return { list: [], total: 0 };
    } finally {
      loading.value = false;
    }
  }

  // 工具函数
  const getRoleColor = (roleType: string) => {
    const colorMap = {
      manager: '#722ed1',
      developer: '#1890ff',
      tester: '#52c41a',
      analyst: '#fa8c16',
      designer: '#eb2f96',
    };
    return colorMap[roleType] || '#1890ff';
  };

  const getRoleIcon = (roleType: string) => {
    const iconMap = {
      manager: UserOutlined,
      developer: CodeOutlined,
      tester: BugOutlined,
      analyst: FileTextOutlined,
      designer: BgColorsOutlined,
    };
    return iconMap[roleType] || UserOutlined;
  };

  const getRoleTypeColor = (roleType: string) => {
    const colorMap = {
      manager: 'purple',
      developer: 'blue',
      tester: 'green',
      analyst: 'orange',
      designer: 'pink',
    };
    return colorMap[roleType] || 'blue';
  };

  const getRoleTypeText = (roleType: string) => {
    const textMap = {
      manager: '项目经理',
      developer: '开发人员',
      tester: '测试人员',
      analyst: '需求分析师',
      designer: '设计师',
    };
    return textMap[roleType] || '未知类型';
  };

  // 事件处理
  const handleSearch = () => {
    reload();
  };

  const handleRoleTypeFilter = () => {
    reload();
  };

  const handleAdd = () => {
    currentRecord.value = null;
    drawerTitle.value = '添加团队角色';
    resetFields();
    openDrawer();
  };

  const handleEdit = (record: any) => {
    currentRecord.value = record;
    drawerTitle.value = '编辑团队角色';
    setFieldsValue(record);
    openDrawer();
  };

  const handleCopy = (record: any) => {
    currentRecord.value = null;
    drawerTitle.value = '复制团队角色';
    const copyData = { ...record };
    delete copyData.id;
    copyData.roleName = `${record.roleName} - 副本`;
    copyData.roleCode = `${record.roleCode}_COPY`;
    setFieldsValue(copyData);
    openDrawer();
  };

  const handlePermissions = (record: any) => {
    currentPermissionRecord.value = record;
    // 初始化已选择的权限
    selectedPermissions.value = {
      project: [],
      task: [],
      document: [],
      report: [],
    };
    openPermissionModal();
  };

  const handleDelete = (record: any) => {
    createMessage.success('删除成功');
    reload();
  };

  const handleSubmit = async () => {
    try {
      const values = await validate();
      console.log('提交团队角色数据:', values);

      createMessage.success('保存成功');
      closeDrawer();
      reload();
    } catch (error) {
      console.error('保存失败:', error);
      createMessage.error('保存失败');
    }
  };

  const handleImportFromTemplate = () => {
    createMessage.info('从模板导入功能开发中...');
  };

  const handleRefresh = () => {
    reload();
  };

  const handlePermissionSubmit = () => {
    console.log('保存权限设置:', {
      roleId: currentPermissionRecord.value?.id,
      permissions: selectedPermissions.value,
    });

    createMessage.success('权限设置保存成功');
    closePermissionModal();
    reload();
  };

  onMounted(() => {
    console.log('团队配置页面挂载，模板ID:', templateId?.value);
  });
</script>

<style lang="less" scoped>
  .template-team-page {
    .role-icon {
      display: inline-flex;
      align-items: center;
      justify-content: center;
    }

    .permissions {
      .ant-tag {
        margin-bottom: 4px;
      }
    }

    .permission-content {
      .current-role {
        border-left: 4px solid #1890ff;
      }

      .permission-groups {
        max-height: 400px;
        overflow-y: auto;
      }
    }
  }
</style>
