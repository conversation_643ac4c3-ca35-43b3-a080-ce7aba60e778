import { defHttp } from '/@/utils/http/axios';
import type {
  BizBusinessWeeklogVO,
  BizBusinessWeeklogForm,
  BizBusinessWeeklogPagination,
  BizBusinessWeeklogAuditForm,
  BizBusinessWeeklogHistoryVO,
} from './model/businessWeeklogModel';

enum Api {
  GetList = '/api/project/biz/businessWeeklog/getList',
  GetInfo = '/api/project/biz/businessWeeklog',
  Create = '/api/project/biz/businessWeeklog',
  Update = '/api/project/biz/businessWeeklog',
  Delete = '/api/project/biz/businessWeeklog',
  GetListByDateRange = '/api/project/biz/businessWeeklog/getListByDateRange',
  GetListByOwnId = '/api/project/biz/businessWeeklog/getListByOwnId',
  GetHistoryByProjId = '/api/project/biz/businessWeeklog/getHistoryByProjId',
  SubmitForAudit = '/api/project/biz/businessWeeklog',
  Audit = '/api/project/biz/businessWeeklog/audit',
  GetPendingAuditList = '/api/project/biz/businessWeeklog/getPendingAuditList',
  BatchUpdateShowStatus = '/api/project/biz/businessWeeklog/batchUpdateShowStatus',
}

/**
 * 获取商机周报列表
 */
export function getBusinessWeeklogList(params: BizBusinessWeeklogPagination) {
  return defHttp.post<{
    list: BizBusinessWeeklogVO[];
    pagination: {
      currentPage: number;
      pageSize: number;
      total: number;
    };
  }>({
    url: Api.GetList,
    data: params,
  });
}

/**
 * 获取商机周报详情
 */
export function getBusinessWeeklogInfo(id: string) {
  return defHttp.get<BizBusinessWeeklogVO>({
    url: `${Api.GetInfo}/${id}`,
  });
}

/**
 * 创建商机周报
 */
export function createBusinessWeeklog(data: BizBusinessWeeklogForm) {
  return defHttp.post<string>({
    url: Api.Create,
    data,
  });
}

/**
 * 更新商机周报
 */
export function updateBusinessWeeklog(id: string, data: BizBusinessWeeklogForm) {
  return defHttp.put<string>({
    url: `${Api.Update}/${id}`,
    data,
  });
}

/**
 * 删除商机周报
 */
export function deleteBusinessWeeklog(id: string) {
  return defHttp.delete<string>({
    url: `${Api.Delete}/${id}`,
  });
}

/**
 * 根据时间段查询商机周报
 */
export function getBusinessWeeklogListByDateRange(startDate?: string, endDate?: string) {
  return defHttp.post<BizBusinessWeeklogVO[]>({
    url: Api.GetListByDateRange,
    params: {
      startDate,
      endDate,
    },
  });
}

/**
 * 根据负责人查询商机周报
 */
export function getBusinessWeeklogListByOwnId(ownId: string) {
  return defHttp.get<BizBusinessWeeklogVO[]>({
    url: `${Api.GetListByOwnId}/${ownId}`,
  });
}

/**
 * 根据项目ID查询历史商机周报记录
 */
export function getBusinessWeeklogHistoryByProjId(projId: string) {
  return defHttp.get<BizBusinessWeeklogHistoryVO[]>({
    url: `${Api.GetHistoryByProjId}/${projId}`,
  });
}

/**
 * 提交审核
 */
export function submitBusinessWeeklogForAudit(id: string) {
  return defHttp.post<string>({
    url: `${Api.SubmitForAudit}/${id}/submitForAudit`,
  });
}

/**
 * 审核商机周报
 */
export function auditBusinessWeeklog(data: BizBusinessWeeklogAuditForm) {
  return defHttp.post<string>({
    url: Api.Audit,
    data,
  });
}

/**
 * 获取待审核的商机周报列表
 */
export function getPendingAuditBusinessWeeklogList() {
  return defHttp.post<BizBusinessWeeklogVO[]>({
    url: Api.GetPendingAuditList,
  });
}

/**
 * 批量更新显示状态
 */
export function batchUpdateBusinessWeeklogShowStatus(ids: string[], showStatus: number) {
  return defHttp.post<string>({
    url: Api.BatchUpdateShowStatus,
    data: ids,
    params: {
      showStatus,
    },
  });
}

/**
 * 获取商机周报选择器列表（用于下拉选择）
 */
export function getBusinessWeeklogSelectList(params?: Partial<BizBusinessWeeklogPagination>) {
  return defHttp
    .post<Array<{ id: string; fullName: string; projName: string }>>({
      url: Api.GetList,
      data: {
        ...params,
        pageSize: 1000, // 获取更多数据用于选择
      },
    })
    .then(res => {
      return (
        res.list?.map(item => ({
          id: item.id,
          fullName: `${item.projName} (${item.startDate} - ${item.endDate})`,
          projName: item.projName,
        })) || []
      );
    });
}
