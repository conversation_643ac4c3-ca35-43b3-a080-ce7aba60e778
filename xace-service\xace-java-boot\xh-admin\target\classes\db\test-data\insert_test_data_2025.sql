-- 插入2025年测试数据用于验证报表功能
-- 注意：这是测试数据，生产环境请谨慎使用

-- 插入测试合同数据
INSERT INTO zz_proj_contract (
    c_id, name, c_no, cust_id, final_user_id, own_id, 
    amount, ys_amount, year_ys_amount, external_amount,
    money_status, contract_status, note, dept_id, linkman, link_telephone,
    sign_date, bid_date, commencement_date, initial_check_date, final_check_date, audit_date,
    sign_year, cstart_date, cend_date, mstart_date, mend_date,
    create_user_id, create_time, last_modified_user_id, update_time, del_mark
) VALUES 
-- 2025年合同1
('test_contract_2025_001', '哈尔滨市政府采购服务类合同【基础警务】', 'HT2025001', 'cust_001', 'final_user_001', 'user_001',
 1330000.00, 0.00, 0.00, 0.00,
 'unpaid', 'executing', '2025年新签合同', 'dept_002', '张三', '13800138001',
 '2025-01-15', '2025-01-10', '2025-02-01', NULL, NULL, NULL,
 2025, '2025-02-01', '2025-12-31', '2026-01-01', '2026-12-31',
 'admin', NOW(), 'admin', NOW(), 0),

-- 2025年合同2
('test_contract_2025_002', '智慧城市管理平台建设项目', 'HT2025002', 'cust_002', 'final_user_002', 'user_002',
 2500000.00, 500000.00, 500000.00, 0.00,
 'partial', 'executing', '2025年重点项目', 'dept_001', '李四', '13800138002',
 '2025-02-01', '2025-01-25', '2025-03-01', NULL, NULL, NULL,
 2025, '2025-03-01', '2025-11-30', '2025-12-01', '2026-11-30',
 'admin', NOW(), 'admin', NOW(), 0),

-- 2025年合同3
('test_contract_2025_003', '数据中心安全监控系统', 'HT2025003', 'cust_003', 'final_user_003', 'user_003',
 1800000.00, 900000.00, 900000.00, 0.00,
 'partial', 'executing', '2025年安全项目', 'dept_002', '王五', '13800138003',
 '2025-03-01', '2025-02-20', '2025-04-01', NULL, NULL, NULL,
 2025, '2025-04-01', '2025-10-31', '2025-11-01', '2026-10-31',
 'admin', NOW(), 'admin', NOW(), 0);

-- 插入测试收款计划数据
INSERT INTO zz_proj_contract_money_v2 (
    cm_id, proj_base_id, contract_id, fktj, ratio, cm_money, own_id, pay_status,
    kaipiao_date, yingshou_date, yushou_date, shoukuan_date, tmp_date,
    last_note, note, dept_id, create_user_id, create_time, last_modified_user_id, update_time,
    yb_amount, eb_amount, other_amount
) VALUES 
-- 合同1的收款计划
('test_money_2025_001', 'test_proj_base_2025_001', 'test_contract_2025_001', '合同签订后完成部署付70%', '70', 931000.00, 'user_001', '0',
 '2025-02-13', '2025-04-30', '2025-04-30', NULL, NULL,
 '已开票未收款', '1', 'dept_002', 'admin', NOW(), 'admin', NOW(),
 0.00, 931000.00, 0.00),

('test_money_2025_002', 'test_proj_base_2025_001', 'test_contract_2025_001', '验收合格后付30%', '30', 399000.00, 'user_001', '0',
 NULL, '2025-12-31', '2025-12-31', NULL, NULL,
 '未开票', '2', 'dept_002', 'admin', NOW(), 'admin', NOW(),
 0.00, 399000.00, 0.00),

-- 合同2的收款计划
('test_money_2025_003', 'test_proj_base_2025_002', 'test_contract_2025_002', '首付款20%', '20', 500000.00, 'user_002', '1',
 '2025-02-05', '2025-02-28', '2025-02-28', '2025-02-28', NULL,
 '已收款', '首付款已到账', 'dept_001', 'admin', NOW(), 'admin', NOW(),
 500000.00, 0.00, 0.00),

('test_money_2025_004', 'test_proj_base_2025_002', 'test_contract_2025_002', '进度款50%', '50', 1250000.00, 'user_002', '0',
 '2025-03-15', '2025-06-30', '2025-06-30', NULL, NULL,
 '已开票未收款', '进度款', 'dept_001', 'admin', NOW(), 'admin', NOW(),
 1250000.00, 0.00, 0.00),

('test_money_2025_005', 'test_proj_base_2025_002', 'test_contract_2025_002', '尾款30%', '30', 750000.00, 'user_002', '0',
 NULL, '2025-11-30', '2025-11-30', NULL, NULL,
 '未开票', '尾款', 'dept_001', 'admin', NOW(), 'admin', NOW(),
 750000.00, 0.00, 0.00),

-- 合同3的收款计划
('test_money_2025_006', 'test_proj_base_2025_003', 'test_contract_2025_003', '首付款50%', '50', 900000.00, 'user_003', '1',
 '2025-03-10', '2025-03-31', '2025-03-31', '2025-03-31', NULL,
 '已收款', '首付款已到账', 'dept_002', 'admin', NOW(), 'admin', NOW(),
 0.00, 900000.00, 0.00),

('test_money_2025_007', 'test_proj_base_2025_003', 'test_contract_2025_003', '尾款50%', '50', 900000.00, 'user_003', '0',
 '2025-04-15', '2025-10-31', '2025-10-31', NULL, NULL,
 '已开票未收款', '尾款', 'dept_002', 'admin', NOW(), 'admin', NOW(),
 0.00, 900000.00, 0.00);

-- 插入一些历史收款数据（用于趋势分析）
INSERT INTO zz_proj_contract_money_v2 (
    cm_id, proj_base_id, contract_id, fktj, ratio, cm_money, own_id, pay_status,
    kaipiao_date, yingshou_date, yushou_date, shoukuan_date, tmp_date,
    last_note, note, dept_id, create_user_id, create_time, last_modified_user_id, update_time,
    yb_amount, eb_amount, other_amount
) VALUES 
-- 1月收款
('test_money_2025_history_001', 'test_proj_base_2025_002', 'test_contract_2025_002', '历史收款1月', '100', 300000.00, 'user_002', '1',
 '2025-01-10', '2025-01-31', '2025-01-31', '2025-01-31', NULL,
 '已收款', '1月收款', 'dept_001', 'admin', '2025-01-31 10:00:00', 'admin', '2025-01-31 10:00:00',
 300000.00, 0.00, 0.00),

-- 2月收款
('test_money_2025_history_002', 'test_proj_base_2025_003', 'test_contract_2025_003', '历史收款2月', '100', 450000.00, 'user_003', '1',
 '2025-02-05', '2025-02-28', '2025-02-28', '2025-02-28', NULL,
 '已收款', '2月收款', 'dept_002', 'admin', '2025-02-28 10:00:00', 'admin', '2025-02-28 10:00:00',
 0.00, 450000.00, 0.00),

-- 3月收款
('test_money_2025_history_003', 'test_proj_base_2025_001', 'test_contract_2025_001', '历史收款3月', '100', 200000.00, 'user_001', '1',
 '2025-03-05', '2025-03-31', '2025-03-31', '2025-03-31', NULL,
 '已收款', '3月收款', 'dept_002', 'admin', '2025-03-31 10:00:00', 'admin', '2025-03-31 10:00:00',
 0.00, 200000.00, 0.00);

-- 更新合同的已收金额
UPDATE zz_proj_contract SET 
    ys_amount = 500000.00, 
    year_ys_amount = 500000.00,
    money_status = 'partial'
WHERE c_id = 'test_contract_2025_002';

UPDATE zz_proj_contract SET 
    ys_amount = 900000.00, 
    year_ys_amount = 900000.00,
    money_status = 'partial'
WHERE c_id = 'test_contract_2025_003';

-- 插入测试用户数据（如果不存在）
INSERT IGNORE INTO base_user (F_Id, F_Account, F_RealName, F_QuickQuery, F_NickName, F_EnabledMark, F_DeleteMark)
VALUES 
('user_001', 'tangsj', '唐生继', 'tangsj', '唐生继', 1, 0),
('user_002', 'zhangsan', '张三', 'zhangsan', '张三', 1, 0),
('user_003', 'lisi', '李四', 'lisi', '李四', 1, 0);

-- 插入测试部门数据（如果不存在）
INSERT IGNORE INTO base_organize (F_Id, F_FullName, F_EnCode, F_Category, F_EnabledMark, F_DeleteMark)
VALUES 
('dept_001', '软件一部', 'YB', 1, 1, 0),
('dept_002', '软件二部', 'EB', 1, 1, 0),
('dept_003', '综合部', 'ZHB', 1, 1, 0);

-- 插入测试客户数据（如果不存在）
INSERT IGNORE INTO zz_proj_customer (id, name, code, cust_type, cust_line, del_mark)
VALUES 
('cust_001', '哈尔滨市政府', 'HEBSF001', 'JIAFANG', 'A', 0),
('cust_002', '智慧城市科技有限公司', 'ZHCS001', 'JIAFANG', 'A', 0),
('cust_003', '数据中心运营公司', 'SJZX001', 'JIAFANG', 'B', 0),
('final_user_001', '哈尔滨市公安局', 'HEBGAJ001', 'JIAFANG', 'A', 0),
('final_user_002', '智慧城市管理委员会', 'ZHCSGLWYH001', 'JIAFANG', 'A', 0),
('final_user_003', '数据中心管理局', 'SJZXGLJ001', 'JIAFANG', 'B', 0);

-- 提交事务
COMMIT;
