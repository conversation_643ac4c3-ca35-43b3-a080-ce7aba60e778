package com.xinghuo.manhour.model.analysis;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 分部资源利用率详情
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@Schema(description = "分部资源利用率详情")
public class DepartmentUtilizationDetail {

    @Schema(description = "分部ID")
    private String fbId;

    @Schema(description = "分部名称")
    private String fbName;

    @Schema(description = "总体利用率")
    private BigDecimal overallUtilization;

    @Schema(description = "人员利用率")
    private BigDecimal personnelUtilization;

    @Schema(description = "项目利用率")
    private BigDecimal projectUtilization;

    @Schema(description = "时间利用率")
    private BigDecimal timeUtilization;

    @Schema(description = "月度利用率趋势")
    private List<MonthlyUtilizationData> monthlyTrend;

    @Schema(description = "人员利用率分布")
    private List<PersonnelUtilizationData> personnelDistribution;

    @Schema(description = "项目利用率分布")
    private List<ProjectUtilizationData> projectDistribution;

    @Schema(description = "资源配置建议")
    private List<ResourceRecommendationData> recommendations;

    /**
     * 月度利用率数据
     */
    @Data
    @Schema(description = "月度利用率数据")
    public static class MonthlyUtilizationData {
        @Schema(description = "月份")
        private String month;

        @Schema(description = "利用率")
        private BigDecimal utilization;

        @Schema(description = "工时")
        private BigDecimal workMonth;
    }

    /**
     * 人员利用率数据
     */
    @Data
    @Schema(description = "人员利用率数据")
    public static class PersonnelUtilizationData {
        @Schema(description = "员工姓名")
        private String userName;

        @Schema(description = "利用率")
        private BigDecimal utilization;

        @Schema(description = "工时")
        private BigDecimal workMonth;

        @Schema(description = "状态")
        private String status;
    }

    /**
     * 项目利用率数据
     */
    @Data
    @Schema(description = "项目利用率数据")
    public static class ProjectUtilizationData {
        @Schema(description = "项目名称")
        private String projectName;

        @Schema(description = "利用率")
        private BigDecimal utilization;

        @Schema(description = "投入人数")
        private Integer userCount;

        @Schema(description = "总工时")
        private BigDecimal totalWorkMonth;
    }

    /**
     * 资源配置建议数据
     */
    @Data
    @Schema(description = "资源配置建议数据")
    public static class ResourceRecommendationData {
        @Schema(description = "建议类型")
        private String type;

        @Schema(description = "建议内容")
        private String content;

        @Schema(description = "优先级")
        private String priority;
    }
}
