# 商机周报管理前端模块

## 📁 目录结构

```
businessWeeklog/
├── index.vue                      # 商机周报管理主页面
├── BusinessWeeklogDrawer.vue      # 新增/编辑商机周报抽屉
├── DetailDrawer.vue               # 商机周报详情抽屉
├── AuditDrawer.vue                # 商机周报审核抽屉
├── HistoryDrawer.vue              # 商机周报历史记录抽屉
├── businessWeeklog.data.ts        # 数据配置文件
├── route.ts                       # 路由配置
└── README.md                      # 本文件
```

## 🎯 功能概述

商机周报管理模块是项目管理系统的重要组成部分，主要用于管理项目的商机周报信息，支持完整的审核流程。

### 核心功能
- ✅ 商机周报的增删改查
- ✅ 商机周报列表的搜索和筛选
- ✅ 商机周报详情查看
- ✅ 商机周报审核流程（提交审核、分部经理审核）
- ✅ 历史记录查询和追踪
- ✅ 批量操作（显示/隐藏状态）
- ✅ 权限控制和操作日志

### 技术特性
- 🚀 基于 Vue 3 + TypeScript 开发
- 🎨 使用 Ant Design Vue 组件库
- 📱 响应式设计，支持多设备访问
- ⚡ 高性能表格组件，支持虚拟滚动
- 🔍 强大的搜索和筛选功能
- 📊 实时数据统计和状态展示
- 🎭 抽屉式表单，提升用户体验

## 🚀 快速开始

### 路由配置
在主路由文件中引入商机周报管理路由：

```typescript
// src/router/routes/modules/project.ts
import businessWeeklogRoute from '/@/views/project/biz/businessWeeklog/route';

export default {
  path: '/project',
  name: 'Project',
  children: [
    businessWeeklogRoute,
    // 其他路由...
  ],
};
```

### 菜单配置
在后端管理系统中配置菜单：

```sql
-- 商机周报管理菜单
INSERT INTO base_menu (id, parent_id, en_code, full_name, icon, url_address, type, sort_code, enabled_mark) 
VALUES ('business_weeklog', 'project_biz', 'project.business.weeklog', '商机周报管理', 'ant-design:file-text-outlined', 'project/biz/businessWeeklog', 1, 30, 1);
```

### 权限配置
需要配置以下权限：

```sql
-- 权限配置
INSERT INTO base_permission (id, en_code, full_name, type) VALUES 
('project.business.weeklog.view', 'project.business.weeklog.view', '查看商机周报', 1),
('project.business.weeklog.create', 'project.business.weeklog.create', '创建商机周报', 1),
('project.business.weeklog.edit', 'project.business.weeklog.edit', '编辑商机周报', 1),
('project.business.weeklog.delete', 'project.business.weeklog.delete', '删除商机周报', 1),
('project.business.weeklog.submit', 'project.business.weeklog.submit', '提交审核', 1),
('project.business.weeklog.audit', 'project.business.weeklog.audit', '审核商机周报', 1);
```

## 📋 页面组件说明

### index.vue - 主页面
**功能**：商机周报列表展示和管理
**组件**：
- BasicTable：数据表格组件
- TableAction：操作按钮组件
- 搜索表单：支持多条件搜索

**主要功能**：
- 商机周报列表展示
- 搜索和筛选（项目名称、类型、级别、负责人、状态、日期范围）
- 新增、编辑、删除、查看操作
- 提交审核、审核操作
- 查看历史记录
- 批量显示/隐藏操作
- 分页和排序

### BusinessWeeklogDrawer.vue - 表单抽屉
**功能**：新增和编辑商机周报信息
**组件**：
- BasicDrawer：抽屉组件（右侧弹出，宽度50%）
- BasicForm：表单组件

**表单字段**：
- 项目ID（必填）
- 项目名称（必填）
- 项目类型
- 项目级别
- 所属分部
- 负责人（必填）
- 时间范围（必填）
- 录入日期
- 项目备注
- 备注
- 计划
- 风险
- 状态
- 显示状态

### DetailDrawer.vue - 详情抽屉
**功能**：查看商机周报详细信息
**组件**：
- BasicDrawer：抽屉组件（右侧弹出，宽度60%）
- Description：描述列表组件
- Tabs：标签页组件

**标签页**：
- 基本信息：商机周报基本信息展示
- 关联商机：关联商机信息（如果有）
- 内容详情：项目备注、备注、计划、风险等详细内容

### AuditDrawer.vue - 审核抽屉
**功能**：分部经理审核商机周报
**组件**：
- BasicDrawer：抽屉组件（右侧弹出，宽度50%）
- BasicForm：审核表单
- Descriptions：商机周报信息展示

**审核功能**：
- 显示商机周报基本信息
- 显示内容详情（备注、计划、风险）
- 审核操作（通过/驳回）
- 审核意见填写

### HistoryDrawer.vue - 历史记录抽屉
**功能**：查看项目的历史商机周报记录
**组件**：
- BasicDrawer：抽屉组件（右侧弹出，宽度70%）
- BasicTable：历史记录表格
- Descriptions：项目信息展示

**历史记录功能**：
- 显示项目基本信息
- 按时间倒序显示历史记录
- 支持查看每条记录的详细信息

## 🔧 开发说明

### 技术栈
- **前端框架**：Vue 3.x
- **开发语言**：TypeScript
- **UI组件库**：Ant Design Vue
- **状态管理**：Pinia
- **路由管理**：Vue Router
- **HTTP客户端**：Axios
- **构建工具**：Vite

### 数据流
```
用户操作 → 组件事件 → API调用 → 后端处理 → 数据返回 → 界面更新
```

### 状态管理
商机周报状态流转：
```
未填写(0) → 已填写(1) → 提交审核(2) → 已发布(3)
                                    ↓
                              已驳回(-1) → 已填写(1)
```

### 权限控制
- **查看权限**：`project.business.weeklog.view`
- **创建权限**：`project.business.weeklog.create`
- **编辑权限**：`project.business.weeklog.edit`
- **删除权限**：`project.business.weeklog.delete`
- **提交审核权限**：`project.business.weeklog.submit`
- **审核权限**：`project.business.weeklog.audit`

### 审核权限规则
1. **管理员**：拥有所有权限，可以审核任何商机周报
2. **分部经理**：可以审核本分部及下级分部的商机周报
3. **普通用户**：只能操作自己创建的商机周报

## 🎨 样式规范

### 抽屉宽度
- 表单抽屉：50%
- 详情抽屉：60%
- 审核抽屉：50%
- 历史记录抽屉：70%

### 颜色规范
- 未填写：`default`（灰色）
- 已填写：`processing`（蓝色）
- 提交审核：`warning`（橙色）
- 已发布：`success`（绿色）
- 已驳回：`error`（红色）

## 📝 使用示例

### 基本操作
```bash
# 新增商机周报
点击"新增商机周报" → 填写表单 → 保存

# 查看详情
选择商机周报 → 点击"查看详情" → 查看基本信息和内容详情

# 编辑信息
选择商机周报 → 点击"编辑" → 修改信息 → 保存

# 提交审核
选择已填写状态的商机周报 → 点击"提交审核" → 确认提交

# 审核操作
选择提交审核状态的商机周报 → 点击"审核" → 选择审核结果 → 填写审核意见 → 确认

# 查看历史
选择商机周报 → 点击"查看历史" → 查看项目的历史商机周报记录

# 批量操作
选择多条记录 → 点击"批量显示"或"批量隐藏" → 确认操作
```

### 搜索筛选
```bash
# 按项目名称搜索
在搜索框输入项目名称 → 点击搜索

# 按状态筛选
选择状态下拉框 → 选择对应状态 → 点击搜索

# 按时间范围筛选
选择开始日期范围 → 点击搜索

# 组合条件搜索
同时设置多个搜索条件 → 点击搜索
```

## 🚨 注意事项

1. **数据一致性**：表单提交前会进行数据验证
2. **权限安全**：所有操作都有相应的权限控制
3. **状态限制**：已发布状态的商机周报不能编辑和删除
4. **审核流程**：只有提交审核状态的商机周报才能进行审核
5. **历史记录**：删除商机周报会影响历史记录的完整性
6. **批量操作**：批量操作前请确认选择的数据正确

## 🔄 更新日志

### v1.0.0 (2025-07-08)
- ✅ 完成商机周报管理基础功能
- ✅ 实现审核流程
- ✅ 添加历史记录查询
- ✅ 优化为抽屉式表单
- ✅ 完善权限控制
- ✅ 添加批量操作功能
