<template>
  <div class="organization-page p-4">
    <!-- 页面标题 -->
    <div class="page-header mb-4">
      <h2 class="text-xl font-semibold mb-2">项目组织</h2>
      <p class="text-gray-600">管理项目的组织架构，配置团队角色和人员分配</p>
    </div>

    <a-spin :spinning="loading">
      <!-- 工具栏 -->
      <div class="toolbar mb-4 flex justify-between items-center">
        <div class="actions-left">
          <a-space>
            <a-button type="primary" @click="handleAddTeam">
              <template #icon><PlusOutlined /></template>
              添加团队
            </a-button>
            <a-button @click="handleAddPendingUser">
              <template #icon><UserAddOutlined /></template>
              添加待定人员
            </a-button>
            <a-button @click="handleImportTemplate">
              <template #icon><ImportOutlined /></template>
              导入模板
            </a-button>
          </a-space>
        </div>
        <div class="actions-right">
          <a-space>
            <a-switch v-model:checked="showEmptyRoles" checked-children="显示空角色" un-checked-children="隐藏空角色" />
            <a-button @click="handleExport">
              <template #icon><ExportOutlined /></template>
              导出
            </a-button>
            <a-button @click="handleRefresh">
              <template #icon><ReloadOutlined /></template>
              刷新
            </a-button>
          </a-space>
        </div>
      </div>

      <!-- 组织统计卡片 -->
      <div class="org-stats grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="stat-card bg-blue-50 rounded-lg p-4">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-sm text-blue-600">团队总数</div>
              <div class="text-2xl font-bold text-blue-600">{{ orgStats.totalTeams }}</div>
            </div>
            <TeamOutlined class="text-3xl text-blue-600" />
          </div>
        </div>
        <div class="stat-card bg-green-50 rounded-lg p-4">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-sm text-green-600">项目成员</div>
              <div class="text-2xl font-bold text-green-600">{{ orgStats.totalMembers }}</div>
            </div>
            <UserOutlined class="text-3xl text-green-600" />
          </div>
        </div>
        <div class="stat-card bg-orange-50 rounded-lg p-4">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-sm text-orange-600">角色类型</div>
              <div class="text-2xl font-bold text-orange-600">{{ orgStats.totalRoles }}</div>
            </div>
            <CrownOutlined class="text-3xl text-orange-600" />
          </div>
        </div>
        <div class="stat-card bg-purple-50 rounded-lg p-4">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-sm text-purple-600">待定人员</div>
              <div class="text-2xl font-bold text-purple-600">{{ orgStats.pendingUsers }}</div>
            </div>
            <ClockCircleOutlined class="text-3xl text-purple-600" />
          </div>
        </div>
      </div>

      <!-- 组织架构表格 -->
      <div class="organization-table bg-white rounded-lg shadow-sm border">
        <a-table
          :columns="orgColumns"
          :data-source="filteredOrgData"
          :pagination="false"
          :scroll="{ y: 600 }"
          row-key="id"
          :default-expand-all-rows="true"
          :tree-data="true"
          :show-header="true">
          <!-- 团队/角色名称列 -->
          <template #teamName="{ record }">
            <div class="flex items-center">
              <div class="team-icon mr-2">
                <a-avatar :style="{ backgroundColor: getTeamTypeColor(record.type) }" :size="24">
                  <template #icon>
                    <component :is="getTeamTypeIcon(record.type)" />
                  </template>
                </a-avatar>
              </div>
              <div>
                <div class="font-medium">{{ record.name }}</div>
                <div v-if="record.description" class="text-sm text-gray-500">
                  {{ record.description }}
                </div>
              </div>
              <div class="ml-auto">
                <a-dropdown trigger="click" v-if="record.type !== 'project'">
                  <a-button type="text" size="small">
                    <MoreOutlined />
                  </a-button>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="handleEditTeam(record)">
                        <EditOutlined />
                        编辑
                      </a-menu-item>
                      <a-menu-item @click="handleDeleteTeam(record)" danger>
                        <DeleteOutlined />
                        删除
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </div>
            </div>
          </template>

          <!-- 人员列 -->
          <template #userList="{ record }">
            <div class="user-list">
              <!-- 添加角色按钮 -->
              <div v-if="record.type === 'project'" class="add-role-btn mb-2">
                <a-button type="dashed" size="small" @click="handleAddRole(record)">
                  <template #icon><PlusOutlined /></template>
                  添加角色
                </a-button>
              </div>

              <!-- 现有人员列表 -->
              <div v-if="record.userList && record.userList.length > 0" class="user-cards">
                <div
                  v-for="user in record.userList"
                  :key="user.id"
                  class="user-card flex items-center p-3 mb-2 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                  <div class="user-avatar mr-3">
                    <a-avatar :size="40" :src="user.avatar">
                      {{ user.name?.charAt(0) || '?' }}
                    </a-avatar>
                  </div>
                  <div class="user-info flex-1">
                    <div class="font-medium">{{ user.name }}</div>
                    <div class="text-sm text-gray-500">{{ user.department }}</div>
                    <div v-if="user.position" class="text-xs text-gray-400">{{ user.position }}</div>
                  </div>
                  <div class="user-actions">
                    <a-dropdown trigger="click">
                      <a-button type="text" size="small">
                        <MoreOutlined />
                      </a-button>
                      <template #overlay>
                        <a-menu>
                          <a-menu-item @click="handleViewUser(user)">
                            <EyeOutlined />
                            查看详情
                          </a-menu-item>
                          <a-menu-item @click="handleEditUser(user)">
                            <EditOutlined />
                            编辑
                          </a-menu-item>
                          <a-menu-item @click="handleRemoveUser(record, user)" danger>
                            <DeleteOutlined />
                            移除
                          </a-menu-item>
                        </a-menu>
                      </template>
                    </a-dropdown>
                  </div>
                </div>
              </div>

              <!-- 添加人员按钮 -->
              <div v-if="record.type === 'role'" class="add-user-btn">
                <a-button type="dashed" size="small" @click="handleAddUser(record)">
                  <template #icon><UserAddOutlined /></template>
                  添加人员
                </a-button>
              </div>
            </div>
          </template>
        </a-table>
      </div>
    </a-spin>

    <!-- 用户详情模态框 -->
    <a-modal v-model:open="userDetailVisible" title="用户详情" width="60%" :footer="null" :mask-closable="false">
      <div v-if="selectedUser" class="user-detail">
        <div class="user-header flex items-center mb-6">
          <a-avatar :size="80" :src="selectedUser.avatar" class="mr-4">
            {{ selectedUser.name?.charAt(0) || '?' }}
          </a-avatar>
          <div class="user-basic-info">
            <h3 class="text-xl font-semibold">{{ selectedUser.name }}</h3>
            <p class="text-gray-600">{{ selectedUser.department }}</p>
            <p class="text-sm text-gray-500">{{ selectedUser.position }}</p>
          </div>
        </div>

        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="工号">
            {{ selectedUser.employeeId || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="邮箱">
            {{ selectedUser.email || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="手机">
            {{ selectedUser.phone || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="办公电话">
            {{ selectedUser.officePhone || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="直属上级">
            {{ selectedUser.supervisor || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="入职日期">
            {{ formatDate(selectedUser.joinDate) }}
          </a-descriptions-item>
          <a-descriptions-item label="项目角色">
            {{ selectedUser.projectRole || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="参与时间">
            {{ formatDate(selectedUser.joinProjectDate) }}
          </a-descriptions-item>
          <a-descriptions-item label="工作负载">
            <a-progress :percent="selectedUser.workload || 0" size="small" />
          </a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getUserStatusColor(selectedUser.status)">
              {{ getUserStatusText(selectedUser.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="技能标签" :span="2">
            <a-tag v-for="skill in selectedUser.skills" :key="skill" class="mb-1">
              {{ skill }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="备注" :span="2">
            {{ selectedUser.remark || '无' }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>

    <!-- 添加人员模态框 -->
    <a-modal v-model:open="addUserVisible" title="添加人员" width="60%" :footer="null" :mask-closable="false">
      <div class="add-user-form">
        <a-form ref="addUserFormRef" :model="addUserForm" :rules="addUserRules" layout="vertical">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="姓名" name="name">
                <a-input v-model:value="addUserForm.name" placeholder="请输入姓名" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="工号" name="employeeId">
                <a-input v-model:value="addUserForm.employeeId" placeholder="请输入工号" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="部门" name="department">
                <a-input v-model:value="addUserForm.department" placeholder="请输入部门" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="职位" name="position">
                <a-input v-model:value="addUserForm.position" placeholder="请输入职位" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="邮箱" name="email">
                <a-input v-model:value="addUserForm.email" placeholder="请输入邮箱" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="手机" name="phone">
                <a-input v-model:value="addUserForm.phone" placeholder="请输入手机号" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="项目角色" name="projectRole">
                <a-select v-model:value="addUserForm.projectRole" placeholder="请选择项目角色">
                  <a-select-option value="项目经理">项目经理</a-select-option>
                  <a-select-option value="技术负责人">技术负责人</a-select-option>
                  <a-select-option value="业务分析师">业务分析师</a-select-option>
                  <a-select-option value="开发工程师">开发工程师</a-select-option>
                  <a-select-option value="测试工程师">测试工程师</a-select-option>
                  <a-select-option value="运维工程师">运维工程师</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="工作负载" name="workload">
                <a-slider v-model:value="addUserForm.workload" :min="0" :max="100" :step="5" :tooltip-formatter="value => `${value}%`" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-form-item label="技能标签" name="skills">
            <a-select v-model:value="addUserForm.skills" mode="tags" placeholder="请输入技能标签" :token-separators="[',']" />
          </a-form-item>
          <a-form-item label="备注" name="remark">
            <a-textarea v-model:value="addUserForm.remark" placeholder="请输入备注" :rows="3" />
          </a-form-item>
        </a-form>
        <div class="form-actions text-right mt-4">
          <a-space>
            <a-button @click="addUserVisible = false">取消</a-button>
            <a-button type="primary" @click="handleSaveUser" :loading="saveUserLoading"> 保存 </a-button>
          </a-space>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted } from 'vue';
  import { useProjectContext } from '/@/hooks/web/useProjectContext';
  import { useMessage } from '/@/hooks/web/useMessage';
  import dayjs from 'dayjs';
  import {
    PlusOutlined,
    UserAddOutlined,
    ImportOutlined,
    ExportOutlined,
    ReloadOutlined,
    TeamOutlined,
    UserOutlined,
    CrownOutlined,
    ClockCircleOutlined,
    MoreOutlined,
    EditOutlined,
    DeleteOutlined,
    EyeOutlined,
    ProjectOutlined,
    UsergroupAddOutlined,
  } from '@ant-design/icons-vue';

  const { createMessage } = useMessage();

  // 响应式数据
  const loading = ref(false);
  const showEmptyRoles = ref(true);
  const userDetailVisible = ref(false);
  const addUserVisible = ref(false);
  const saveUserLoading = ref(false);
  const selectedUser = ref(null);
  const addUserFormRef = ref();

  // 组织统计数据
  const orgStats = ref({
    totalTeams: 8,
    totalMembers: 25,
    totalRoles: 12,
    pendingUsers: 3,
  });

  // 添加用户表单
  const addUserForm = reactive({
    name: '',
    employeeId: '',
    department: '',
    position: '',
    email: '',
    phone: '',
    projectRole: '',
    workload: 80,
    skills: [],
    remark: '',
  });

  // 表单验证规则
  const addUserRules = {
    name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
    employeeId: [{ required: true, message: '请输入工号', trigger: 'blur' }],
    department: [{ required: true, message: '请输入部门', trigger: 'blur' }],
    email: [
      { required: true, message: '请输入邮箱', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' },
    ],
    phone: [
      { required: true, message: '请输入手机号', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
    ],
    projectRole: [{ required: true, message: '请选择项目角色', trigger: 'change' }],
  };

  // 组织架构数据
  const orgData = ref([
    {
      id: '1',
      name: '内部控制审计项目 ( 2025000054 )',
      type: 'project',
      description: '内部控制审计项目组织架构',
      userList: [],
      children: [
        {
          id: '2',
          name: '项目经理',
          type: 'role',
          description: '负责项目整体管理和协调',
          userList: [
            {
              id: '1',
              name: '张强',
              department: '专业服务事业部',
              position: '高级项目经理',
              email: '<EMAIL>',
              phone: '13800138001',
              employeeId: 'E001',
              avatar: '',
              supervisor: '李总',
              joinDate: '2020-01-15',
              joinProjectDate: '2025-01-01',
              projectRole: '项目经理',
              workload: 100,
              status: 'active',
              skills: ['项目管理', 'PMP', '敏捷开发'],
              remark: '资深项目经理，具有丰富的项目管理经验',
            },
            {
              id: '2',
              name: '陈涛中',
              department: '专业服务事业部',
              position: '项目经理',
              email: '<EMAIL>',
              phone: '13800138002',
              employeeId: 'E002',
              avatar: '',
              supervisor: '张强',
              joinDate: '2021-03-10',
              joinProjectDate: '2025-01-01',
              projectRole: '副项目经理',
              workload: 80,
              status: 'active',
              skills: ['项目管理', 'Scrum Master'],
              remark: '负责项目日常管理工作',
            },
          ],
        },
        {
          id: '3',
          name: '技术团队',
          type: 'team',
          description: '负责技术实施和开发工作',
          userList: [],
          children: [
            {
              id: '4',
              name: '技术负责人',
              type: 'role',
              description: '负责技术方案设计和技术团队管理',
              userList: [
                {
                  id: '3',
                  name: '王建华',
                  department: '技术部',
                  position: '技术总监',
                  email: '<EMAIL>',
                  phone: '13800138003',
                  employeeId: 'E003',
                  avatar: '',
                  supervisor: '张强',
                  joinDate: '2019-06-20',
                  joinProjectDate: '2025-01-05',
                  projectRole: '技术负责人',
                  workload: 90,
                  status: 'active',
                  skills: ['系统架构', 'Java', 'Spring Boot', 'Vue.js'],
                  remark: '负责整体技术架构设计',
                },
              ],
            },
            {
              id: '5',
              name: '前端开发',
              type: 'role',
              description: '负责前端界面开发',
              userList: [
                {
                  id: '4',
                  name: '李晓明',
                  department: '技术部',
                  position: '前端开发工程师',
                  email: '<EMAIL>',
                  phone: '13800138004',
                  employeeId: 'E004',
                  avatar: '',
                  supervisor: '王建华',
                  joinDate: '2022-04-12',
                  joinProjectDate: '2025-01-10',
                  projectRole: '前端开发工程师',
                  workload: 85,
                  status: 'active',
                  skills: ['Vue.js', 'React', 'TypeScript', 'CSS'],
                  remark: '专注于前端用户界面开发',
                },
                {
                  id: '5',
                  name: '刘芳',
                  department: '技术部',
                  position: '前端开发工程师',
                  email: '<EMAIL>',
                  phone: '13800138005',
                  employeeId: 'E005',
                  avatar: '',
                  supervisor: '王建华',
                  joinDate: '2023-02-08',
                  joinProjectDate: '2025-01-15',
                  projectRole: '前端开发工程师',
                  workload: 80,
                  status: 'active',
                  skills: ['Vue.js', 'JavaScript', 'HTML5', 'CSS3'],
                  remark: '负责前端组件开发',
                },
              ],
            },
            {
              id: '6',
              name: '后端开发',
              type: 'role',
              description: '负责后端服务开发',
              userList: [
                {
                  id: '6',
                  name: '赵云峰',
                  department: '技术部',
                  position: '后端开发工程师',
                  email: '<EMAIL>',
                  phone: '13800138006',
                  employeeId: 'E006',
                  avatar: '',
                  supervisor: '王建华',
                  joinDate: '2021-07-15',
                  joinProjectDate: '2025-01-08',
                  projectRole: '后端开发工程师',
                  workload: 90,
                  status: 'active',
                  skills: ['Java', 'Spring Boot', 'MySQL', 'Redis'],
                  remark: '负责核心业务逻辑开发',
                },
              ],
            },
          ],
        },
        {
          id: '7',
          name: '测试团队',
          type: 'team',
          description: '负责质量保障和测试工作',
          userList: [],
          children: [
            {
              id: '8',
              name: '测试负责人',
              type: 'role',
              description: '负责测试计划和测试管理',
              userList: [
                {
                  id: '7',
                  name: '孙丽娟',
                  department: '质量部',
                  position: '测试经理',
                  email: '<EMAIL>',
                  phone: '13800138007',
                  employeeId: 'E007',
                  avatar: '',
                  supervisor: '张强',
                  joinDate: '2020-09-12',
                  joinProjectDate: '2025-01-12',
                  projectRole: '测试负责人',
                  workload: 85,
                  status: 'active',
                  skills: ['测试管理', '自动化测试', 'Selenium'],
                  remark: '负责测试计划制定和执行',
                },
              ],
            },
            {
              id: '9',
              name: '测试工程师',
              type: 'role',
              description: '负责具体测试执行',
              userList: [
                {
                  id: '8',
                  name: '周志强',
                  department: '质量部',
                  position: '测试工程师',
                  email: '<EMAIL>',
                  phone: '13800138008',
                  employeeId: 'E008',
                  avatar: '',
                  supervisor: '孙丽娟',
                  joinDate: '2022-11-05',
                  joinProjectDate: '2025-01-20',
                  projectRole: '测试工程师',
                  workload: 75,
                  status: 'active',
                  skills: ['功能测试', '性能测试', 'JMeter'],
                  remark: '负责功能测试和性能测试',
                },
              ],
            },
          ],
        },
        {
          id: '10',
          name: '业务团队',
          type: 'team',
          description: '负责业务需求和用户支持',
          userList: [],
          children: [
            {
              id: '11',
              name: '业务分析师',
              type: 'role',
              description: '负责业务需求分析',
              userList: [
                {
                  id: '9',
                  name: '吴秀英',
                  department: '业务部',
                  position: '业务分析师',
                  email: '<EMAIL>',
                  phone: '13800138009',
                  employeeId: 'E009',
                  avatar: '',
                  supervisor: '张强',
                  joinDate: '2021-01-18',
                  joinProjectDate: '2025-01-03',
                  projectRole: '业务分析师',
                  workload: 70,
                  status: 'active',
                  skills: ['需求分析', '业务建模', 'UML'],
                  remark: '负责业务需求调研和分析',
                },
              ],
            },
          ],
        },
      ],
    },
  ]);

  // 表格列配置
  const orgColumns = [
    {
      title: '团队/角色名称',
      dataIndex: 'name',
      key: 'name',
      width: 300,
      slots: { customRender: 'teamName' },
    },
    {
      title: '人员',
      dataIndex: 'userList',
      key: 'userList',
      slots: { customRender: 'userList' },
    },
  ];

  // 计算属性
  const filteredOrgData = computed(() => {
    if (showEmptyRoles.value) {
      return orgData.value;
    }
    // 过滤掉空角色的逻辑
    return orgData.value.map(item => ({
      ...item,
      children: filterEmptyRoles(item.children),
    }));
  });

  // 递归过滤空角色
  const filterEmptyRoles = (nodes: any[]): any[] => {
    if (!nodes) return [];

    return nodes.filter(node => {
      if (node.type === 'role' && (!node.userList || node.userList.length === 0)) {
        return false;
      }
      if (node.children) {
        node.children = filterEmptyRoles(node.children);
      }
      return true;
    });
  };

  onMounted(() => {
    loadOrganizationData();
  });

  // 加载组织数据
  const loadOrgData = async () => {
    loading.value = true;
    try {
      // 这里调用实际的API
      // const result = await getOrgData();
      // orgData.value = result.data;
    } catch (error) {
      console.error('加载组织数据失败:', error);
      createMessage.error('加载组织数据失败');
    } finally {
      loading.value = false;
    }
  };

  // 工具函数
  const getTeamTypeColor = (type: string) => {
    const colorMap = {
      project: '#722ed1',
      team: '#1890ff',
      role: '#52c41a',
    };
    return colorMap[type] || '#1890ff';
  };

  const getTeamTypeIcon = (type: string) => {
    const iconMap = {
      project: ProjectOutlined,
      team: TeamOutlined,
      role: UserOutlined,
    };
    return iconMap[type] || UserOutlined;
  };

  const getUserStatusColor = (status: string) => {
    const colorMap = {
      active: 'green',
      inactive: 'red',
      pending: 'orange',
    };
    return colorMap[status] || 'default';
  };

  const getUserStatusText = (status: string) => {
    const textMap = {
      active: '在职',
      inactive: '离职',
      pending: '待入职',
    };
    return textMap[status] || '未知';
  };

  const formatDate = (date: string) => {
    return date ? dayjs(date).format('YYYY-MM-DD') : '-';
  };

  // 加载组织数据
  const loadOrganizationData = async () => {
    if (!projectId.value) {
      console.warn('⚠️ [项目组织] 项目ID为空，跳过数据加载');
      return;
    }

    loading.value = true;
    try {
      console.log('📋 [项目组织] 开始加载项目组织数据:', projectId.value);
      // TODO: 根据项目ID加载组织架构数据
      // const response = await getProjectOrganization(projectId.value);
      // orgStats.value = response.stats;
      // orgData.value = response.data;

      // 模拟数据加载
      await new Promise(resolve => setTimeout(resolve, 500));
      console.log('✅ [项目组织] 组织数据加载完成');
    } catch (error) {
      console.error('❌ [项目组织] 加载组织数据失败:', error);
      createMessage.error('加载组织数据失败');
    } finally {
      loading.value = false;
    }
  };

  // 使用项目上下文Hook（在函数定义之后）
  const { projectId } = useProjectContext({
    onProjectChange: async newProjectId => {
      console.log('🔄 [项目组织] 项目切换，重新加载组织数据:', newProjectId);
      await loadOrganizationData();
    },
  });

  // 事件处理函数
  const handleAddTeam = () => {
    createMessage.info('添加团队功能开发中...');
  };

  const handleAddPendingUser = () => {
    createMessage.info('添加待定人员功能开发中...');
  };

  const handleImportTemplate = () => {
    createMessage.info('导入模板功能开发中...');
  };

  const handleExport = () => {
    createMessage.info('导出功能开发中...');
  };

  const handleRefresh = () => {
    loadOrganizationData();
  };

  const handleEditTeam = (record: any) => {
    createMessage.info('编辑团队功能开发中...');
  };

  const handleDeleteTeam = (record: any) => {
    createMessage.info('删除团队功能开发中...');
  };

  const handleAddRole = (record: any) => {
    createMessage.info('添加角色功能开发中...');
  };

  const handleAddUser = (record: any) => {
    // 重置表单
    Object.assign(addUserForm, {
      name: '',
      employeeId: '',
      department: '',
      position: '',
      email: '',
      phone: '',
      projectRole: '',
      workload: 80,
      skills: [],
      remark: '',
    });
    addUserVisible.value = true;
  };

  const handleViewUser = (user: any) => {
    selectedUser.value = user;
    userDetailVisible.value = true;
  };

  const handleEditUser = (user: any) => {
    createMessage.info('编辑用户功能开发中...');
  };

  const handleRemoveUser = (role: any, user: any) => {
    createMessage.info('移除用户功能开发中...');
  };

  const handleSaveUser = async () => {
    try {
      await addUserFormRef.value.validate();
      saveUserLoading.value = true;

      // 这里调用保存用户的API
      // await saveUser(addUserForm);

      createMessage.success('用户添加成功');
      addUserVisible.value = false;

      // 刷新数据
      await loadOrganizationData();
    } catch (error) {
      console.error('保存用户失败:', error);
    } finally {
      saveUserLoading.value = false;
    }
  };
</script>

<style scoped>
  .organization-page {
    background: var(--section-bg-color);
  }

  .stat-card {
    transition: all 0.3s ease;
  }

  .stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .organization-table {
    min-height: 600px;
  }

  .team-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  .user-card {
    transition: all 0.3s ease;
  }

  .user-card:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .add-role-btn,
  .add-user-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 8px;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    background: #fafafa;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .add-role-btn:hover,
  .add-user-btn:hover {
    border-color: #40a9ff;
    background: #f0f8ff;
  }

  .user-detail {
    max-height: 70vh;
    overflow-y: auto;
  }

  .user-header {
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 20px;
  }

  /* 表格样式 */
  :deep(.ant-table-tbody > tr > td) {
    vertical-align: top;
  }

  :deep(.ant-table-tbody > tr.ant-table-row-level-1 > td:first-child) {
    padding-left: 28px;
  }

  :deep(.ant-table-tbody > tr.ant-table-row-level-2 > td:first-child) {
    padding-left: 56px;
  }

  :deep(.ant-table-tbody > tr.ant-table-row-level-3 > td:first-child) {
    padding-left: 84px;
  }
</style>
