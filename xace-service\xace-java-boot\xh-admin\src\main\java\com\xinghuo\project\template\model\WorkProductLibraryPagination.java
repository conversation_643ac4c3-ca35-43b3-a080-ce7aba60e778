package com.xinghuo.project.template.model;

import com.xinghuo.common.base.model.Pagination;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 标准交付物库分页查询参数
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WorkProductLibraryPagination extends Pagination {

    /**
     * 交付物名称
     */
    private String name;

    /**
     * 交付物编码
     */
    private String code;

    /**
     * 关键字搜索（名称或编码）
     */
    private String keyword;

    /**
     * 交付物类型ID
     */
    private String typeId;

    /**
     * 交付物子类型ID
     */
    private String subTypeId;

    /**
     * 状态ID
     */
    private String statusId;

    /**
     * 默认责任角色ID
     */
    private String defaultRoleId;

    /**
     * 是否需要评审
     */
    private Integer needReview;

    /**
     * 是否是项目最终交付成果
     */
    private Integer isDeliverable;

    /**
     * 是否可裁剪
     */
    private Integer canCut;

    /**
     * 创建时间开始
     */
    private Date createTimeStart;

    /**
     * 创建时间结束
     */
    private Date createTimeEnd;

    /**
     * 创建用户ID
     */
    private String creatorUserId;

    /**
     * 关联的项目模板ID
     */
    private String projectTemplateId;

    /**
     * 交付物类型ID列表（用于多选筛选）
     */
    private List<String> typeIds;

    /**
     * 状态ID列表（用于多选筛选）
     */
    private List<String> statusIds;
}
