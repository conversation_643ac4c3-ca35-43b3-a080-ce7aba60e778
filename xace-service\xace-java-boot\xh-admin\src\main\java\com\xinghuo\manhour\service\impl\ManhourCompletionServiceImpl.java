package com.xinghuo.manhour.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xinghuo.manhour.entity.ManhourTaskEntity;
import com.xinghuo.manhour.dao.ManhourTaskMapper;
import com.xinghuo.manhour.model.completion.*;
import com.xinghuo.manhour.service.ManhourCompletionService;
import com.xinghuo.permission.service.OrganizeService;
import com.xinghuo.permission.entity.OrganizeEntity;
import com.xinghuo.permission.service.UserService;
import com.xinghuo.permission.entity.UserEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import jakarta.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 工时填写情况分析服务实现类
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Slf4j
@Service
public class ManhourCompletionServiceImpl implements ManhourCompletionService {

    @Autowired
    private ManhourTaskMapper manhourTaskMapper;

    @Autowired
    private OrganizeService organizeService;

    @Autowired
    private UserService userService;

    @Override
    public WorkhourCompletionOverview getOverview(WorkhourCompletionParams params) {
        WorkhourCompletionOverview overview = new WorkhourCompletionOverview();

        // 构建查询条件
        LambdaQueryWrapper<ManhourTaskEntity> queryWrapper = buildQueryWrapper(params);

        // 查询所有符合条件的工时任务记录
        List<ManhourTaskEntity> manhourTaskList = manhourTaskMapper.selectList(queryWrapper);

        if (manhourTaskList.isEmpty()) {
            // 返回空数据
            overview.setTotalUsers(0);
            overview.setCompletedUsers(0);
            overview.setCompletionRate(BigDecimal.ZERO);
            overview.setPendingApproval(0);
            overview.setUnfilledUsers(0);
            overview.setOverdueUsers(0);
            overview.setApprovedCount(0);
            overview.setApprovalRate(BigDecimal.ZERO);
            overview.setAvgApprovalDays(BigDecimal.ZERO);
            overview.setMaxWaitingDays(0);
            return overview;
        }
        
        // 统计应填写人数（去重）
        Set<String> allUsers = manhourTaskList.stream()
                .map(ManhourTaskEntity::getUserId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        int totalUsers = allUsers.size();

        // 统计已填写人数（状态大于0的用户）
        Set<String> completedUsers = manhourTaskList.stream()
                .filter(m -> m.getStatus() != null && m.getStatus() > 0)
                .map(ManhourTaskEntity::getUserId)
                .collect(Collectors.toSet());
        int completedUserCount = completedUsers.size();

        // 计算填写完成率
        BigDecimal completionRate = totalUsers > 0 ?
                BigDecimal.valueOf(completedUserCount * 100.0 / totalUsers).setScale(1, RoundingMode.HALF_UP) :
                BigDecimal.ZERO;
        
        // 统计待审批数量
        long pendingApproval = manhourTaskList.stream()
                .filter(m -> m.getStatus() != null && m.getStatus() == 2) // 状态2为待审批
                .count();

        // 统计未填写人数
        int unfilledUsers = totalUsers - completedUserCount;

        // 统计逾期人数（这里简化处理，实际需要根据业务规则计算）
        int overdueUsers = (int) manhourTaskList.stream()
                .filter(m -> isOverdue(m))
                .map(ManhourTaskEntity::getUserId)
                .distinct()
                .count();
        
        // 统计已审批数量
        long approvedCount = manhourTaskList.stream()
                .filter(m -> m.getStatus() != null && m.getStatus() >= 3) // 状态3以上为已审批
                .count();

        // 计算审批完成率
        long totalSubmitted = manhourTaskList.stream()
                .filter(m -> m.getStatus() != null && m.getStatus() >= 2) // 状态2以上为已提交
                .count();
        BigDecimal approvalRate = totalSubmitted > 0 ? 
                BigDecimal.valueOf(approvedCount * 100.0 / totalSubmitted).setScale(1, RoundingMode.HALF_UP) : 
                BigDecimal.ZERO;
        
        // 计算平均审批时长（简化处理）
        BigDecimal avgApprovalDays = BigDecimal.valueOf(3.5); // 示例值
        
        // 计算最长等待天数（简化处理）
        int maxWaitingDays = 15; // 示例值
        
        // 设置概览数据
        overview.setTotalUsers(totalUsers);
        overview.setCompletedUsers(completedUserCount);
        overview.setCompletionRate(completionRate);
        overview.setPendingApproval((int) pendingApproval);
        overview.setUnfilledUsers(unfilledUsers);
        overview.setOverdueUsers(overdueUsers);
        overview.setApprovedCount((int) approvedCount);
        overview.setApprovalRate(approvalRate);
        overview.setAvgApprovalDays(avgApprovalDays);
        overview.setMaxWaitingDays(maxWaitingDays);
        
        return overview;
    }

    @Override
    public CompletionChartDataModel getCharts(WorkhourCompletionParams params) {
        CompletionChartDataModel chartData = new CompletionChartDataModel();

        // 构建查询条件
        LambdaQueryWrapper<ManhourTaskEntity> queryWrapper = buildQueryWrapper(params);
        List<ManhourTaskEntity> manhourTaskList = manhourTaskMapper.selectList(queryWrapper);

        if (manhourTaskList.isEmpty()) {
            return chartData;
        }
        
        // 填写状态分布
        Map<String, Long> statusCount = manhourTaskList.stream()
                .collect(Collectors.groupingBy(
                        m -> getStatusName(m.getStatus()),
                        Collectors.counting()
                ));

        List<CompletionChartDataModel.StatusDistributionData> statusDistribution = statusCount.entrySet().stream()
                .map(entry -> {
                    CompletionChartDataModel.StatusDistributionData data = new CompletionChartDataModel.StatusDistributionData();
                    data.setStatusName(entry.getKey());
                    data.setStatus(getStatusCode(entry.getKey()));
                    data.setUserCount(entry.getValue().intValue());
                    return data;
                })
                .collect(Collectors.toList());

        chartData.setStatusDistribution(statusDistribution);
        
        // 审批状态分布
        Map<String, Long> approvalStatusCount = manhourTaskList.stream()
                .filter(m -> m.getStatus() != null && m.getStatus() >= 2)
                .collect(Collectors.groupingBy(
                        m -> getApprovalStatusName(m.getStatus()),
                        Collectors.counting()
                ));

        List<CompletionChartDataModel.ApprovalStatusData> approvalStatus = approvalStatusCount.entrySet().stream()
                .map(entry -> {
                    CompletionChartDataModel.ApprovalStatusData data = new CompletionChartDataModel.ApprovalStatusData();
                    data.setStatusName(entry.getKey());
                    data.setStatus(getApprovalStatusCode(entry.getKey()));
                    data.setRecordCount(entry.getValue().intValue());
                    return data;
                })
                .collect(Collectors.toList());

        chartData.setApprovalStatus(approvalStatus);
        
        // 分部完成率对比 - 按分部ID分组，并关联查询分部名称
        Map<String, List<ManhourTaskEntity>> departmentGroup = manhourTaskList.stream()
                .filter(m -> StringUtils.hasText(m.getFbId()))
                .collect(Collectors.groupingBy(ManhourTaskEntity::getFbId));

        List<CompletionChartDataModel.DepartmentCompletionData> departmentCompletion = departmentGroup.entrySet().stream()
                .map(entry -> {
                    List<ManhourTaskEntity> deptTasks = entry.getValue();
                    Set<String> totalUsers = deptTasks.stream()
                            .map(ManhourTaskEntity::getUserId)
                            .collect(Collectors.toSet());
                    Set<String> completedUsers = deptTasks.stream()
                            .filter(m -> m.getStatus() != null && m.getStatus() > 0)
                            .map(ManhourTaskEntity::getUserId)
                            .collect(Collectors.toSet());

                    BigDecimal completionRate = totalUsers.size() > 0 ?
                            BigDecimal.valueOf(completedUsers.size() * 100.0 / totalUsers.size()).setScale(1, RoundingMode.HALF_UP) :
                            BigDecimal.ZERO;

                    CompletionChartDataModel.DepartmentCompletionData data = new CompletionChartDataModel.DepartmentCompletionData();

                    // 关联查询分部名称
                    String fbName = "分部" + entry.getKey(); // 默认值
                    try {
                        OrganizeEntity organizeEntity = organizeService.getInfo(entry.getKey());
                        if (organizeEntity != null) {
                            fbName = organizeEntity.getFullName();
                        }
                    } catch (Exception e) {
                        log.warn("查询分部信息失败，分部ID: {}", entry.getKey(), e);
                    }

                    data.setFbName(fbName);
                    data.setCompletionRate(completionRate);
                    return data;
                })
                .sorted((a, b) -> b.getCompletionRate().compareTo(a.getCompletionRate()))
                .collect(Collectors.toList());

        chartData.setDepartmentCompletion(departmentCompletion);
        
        // 填写完成趋势（按月统计）
        Map<String, List<ManhourTaskEntity>> monthlyGroup = manhourTaskList.stream()
                .filter(m -> StringUtils.hasText(m.getMonth()))
                .collect(Collectors.groupingBy(ManhourTaskEntity::getMonth));

        List<CompletionChartDataModel.CompletionTrendData> completionTrend = monthlyGroup.entrySet().stream()
                .map(entry -> {
                    List<ManhourTaskEntity> monthTasks = entry.getValue();
                    Set<String> totalUsers = monthTasks.stream()
                            .map(ManhourTaskEntity::getUserId)
                            .collect(Collectors.toSet());
                    Set<String> completedUsers = monthTasks.stream()
                            .filter(m -> m.getStatus() != null && m.getStatus() > 0)
                            .map(ManhourTaskEntity::getUserId)
                            .collect(Collectors.toSet());

                    BigDecimal completionRate = totalUsers.size() > 0 ?
                            BigDecimal.valueOf(completedUsers.size() * 100.0 / totalUsers.size()).setScale(1, RoundingMode.HALF_UP) :
                            BigDecimal.ZERO;

                    CompletionChartDataModel.CompletionTrendData data = new CompletionChartDataModel.CompletionTrendData();
                    data.setPeriod(entry.getKey());
                    data.setTotalUsers(totalUsers.size());
                    data.setCompletedUsers(completedUsers.size());
                    data.setCompletionRate(completionRate);
                    return data;
                })
                .sorted(Comparator.comparing(CompletionChartDataModel.CompletionTrendData::getPeriod))
                .collect(Collectors.toList());

        chartData.setCompletionTrend(completionTrend);
        
        // 审批效率趋势（示例数据）
        List<CompletionChartDataModel.ApprovalEfficiencyData> approvalEfficiency = new ArrayList<>();
        for (CompletionChartDataModel.CompletionTrendData trend : completionTrend) {
            CompletionChartDataModel.ApprovalEfficiencyData data = new CompletionChartDataModel.ApprovalEfficiencyData();
            data.setPeriod(trend.getPeriod());
            data.setApprovalEfficiency(BigDecimal.valueOf(85.0 + Math.random() * 10)); // 示例数据
            data.setAvgApprovalDays(BigDecimal.valueOf(2.0 + Math.random() * 3)); // 示例数据
            approvalEfficiency.add(data);
        }
        
        chartData.setApprovalEfficiency(approvalEfficiency);
        
        return chartData;
    }

    @Override
    public List<UnfilledUserVO> getUnfilledUsersList(WorkhourCompletionPagination pagination) {
        // 构建查询条件
        LambdaQueryWrapper<ManhourTaskEntity> queryWrapper = buildQueryWrapper(pagination);

        // 查询所有工时任务记录
        List<ManhourTaskEntity> allTasks = manhourTaskMapper.selectList(queryWrapper);

        // 找出所有应该填写但未填写的用户
        Set<String> allUsers = allTasks.stream()
                .map(ManhourTaskEntity::getUserId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        Set<String> filledUsers = allTasks.stream()
                .filter(m -> m.getStatus() != null && m.getStatus() > 0)
                .map(ManhourTaskEntity::getUserId)
                .collect(Collectors.toSet());

        Set<String> unfilledUserIds = allUsers.stream()
                .filter(userId -> !filledUsers.contains(userId))
                .collect(Collectors.toSet());
        
        // 构建未填写用户列表
        List<UnfilledUserVO> unfilledUsers = new ArrayList<>();
        for (String userId : unfilledUserIds) {
            ManhourTaskEntity userRecord = allTasks.stream()
                    .filter(m -> userId.equals(m.getUserId()))
                    .findFirst()
                    .orElse(null);

            if (userRecord != null) {
                UnfilledUserVO vo = new UnfilledUserVO();
                vo.setUserId(userId);

                // 关联查询用户信息
                String userName = "用户" + userId; // 默认值
                String fbId = userRecord.getFbId();
                String fbName = "分部" + fbId; // 默认值
                String chargeName = ""; // 默认值

                try {
                    UserEntity userEntity = userService.getInfo(userId);
                    if (userEntity != null) {
                        userName = userEntity.getRealName();
                        fbId = userEntity.getOrganizeId();

                        // 查询分部名称
                        if (StringUtils.hasText(fbId)) {
                            OrganizeEntity organizeEntity = organizeService.getInfo(fbId);
                            if (organizeEntity != null) {
                                fbName = organizeEntity.getFullName();
                            }
                        }

                        // 查询主管信息
                        if (StringUtils.hasText(userEntity.getManagerId())) {
                            UserEntity managerEntity = userService.getInfo(userEntity.getManagerId());
                            if (managerEntity != null) {
                                chargeName = managerEntity.getRealName();
                            }
                        }
                    }
                } catch (Exception e) {
                    log.warn("查询用户信息失败，用户ID: {}", userId, e);
                }

                vo.setUserName(userName);
                vo.setFbId(fbId);
                vo.setFbName(fbName);
                vo.setMonth(getCurrentMonth());
                vo.setOverdueDays(5); // 示例值
                vo.setLastNotifyTime(""); // 需要从通知记录表查询
                vo.setNotifyCount(0); // 需要从通知记录表查询
                vo.setUserStatus("在职");
                vo.setChargeName(chargeName);

                unfilledUsers.add(vo);
            }
        }
        
        // 设置分页信息
        pagination.setTotal((long) unfilledUsers.size());

        // 分页处理
        int start = (pagination.getCurrentPage() - 1) * pagination.getPageSize();
        int end = Math.min(start + pagination.getPageSize(), unfilledUsers.size());
        
        return unfilledUsers.subList(start, end);
    }

    @Override
    public List<PendingApprovalVO> getPendingApprovalList(WorkhourCompletionPagination pagination) {
        // 构建查询条件
        LambdaQueryWrapper<ManhourTaskEntity> queryWrapper = buildQueryWrapper(pagination);
        queryWrapper.eq(ManhourTaskEntity::getStatus, 2); // 只查询待审批状态

        // 分页查询
        IPage<ManhourTaskEntity> page = new Page<>(pagination.getCurrentPage(), pagination.getPageSize());
        IPage<ManhourTaskEntity> result = manhourTaskMapper.selectPage(page, queryWrapper);

        List<PendingApprovalVO> pendingList = result.getRecords().stream()
                .map(this::convertToPendingApprovalVO)
                .collect(Collectors.toList());

        // 设置分页信息
        pagination.setTotal(result.getTotal());

        return pendingList;
    }

    @Override
    public List<DepartmentCompletionVO> getDepartmentCompletionList(WorkhourCompletionPagination pagination) {
        // 构建查询条件
        LambdaQueryWrapper<ManhourTaskEntity> queryWrapper = buildQueryWrapper(pagination);

        // 查询所有工时任务记录
        List<ManhourTaskEntity> allTasks = manhourTaskMapper.selectList(queryWrapper);

        // 按分部分组统计
        Map<String, List<ManhourTaskEntity>> departmentGroup = allTasks.stream()
                .filter(m -> StringUtils.hasText(m.getFbId()))
                .collect(Collectors.groupingBy(ManhourTaskEntity::getFbId));

        List<DepartmentCompletionVO> departmentList = departmentGroup.entrySet().stream()
                .map(entry -> {
                    List<ManhourTaskEntity> deptTasks = entry.getValue();
                    return convertToDepartmentCompletionVO(entry.getKey(), deptTasks);
                })
                .sorted((a, b) -> b.getCompletionRate().compareTo(a.getCompletionRate()))
                .collect(Collectors.toList());
        
        // 设置排名
        for (int i = 0; i < departmentList.size(); i++) {
            departmentList.get(i).setRanking(i + 1);
        }
        
        // 设置分页信息
        pagination.setTotal((long) departmentList.size());

        // 分页处理
        int start = (pagination.getCurrentPage() - 1) * pagination.getPageSize();
        int end = Math.min(start + pagination.getPageSize(), departmentList.size());
        
        return departmentList.subList(start, end);
    }

    @Override
    public List<LeaderStatisticsVO> getLeaderStatisticsList(WorkhourCompletionPagination pagination) {
        // TODO: 实现负责人统计列表
        // 这里需要根据实际的负责人关系表来查询
        List<LeaderStatisticsVO> leaderList = new ArrayList<>();
        
        // 示例数据
        LeaderStatisticsVO leader1 = new LeaderStatisticsVO();
        leader1.setLeaderId("leader1");
        leader1.setLeaderName("张三");
        leader1.setLeaderType("分部负责人");
        leader1.setManagedUsers(15);
        leader1.setPendingCount(3);
        leader1.setApprovedCount(12);
        leader1.setApprovalEfficiency(BigDecimal.valueOf(80.0));
        leader1.setAvgApprovalDays(BigDecimal.valueOf(2.5));
        leader1.setMaxWaitingDays(5);
        leader1.setMonthlyApprovalCount(8);
        leader1.setOverdueApprovalCount(1);
        
        leaderList.add(leader1);
        
        // 设置分页信息
        pagination.setTotal((long) leaderList.size());
        
        return leaderList;
    }

    @Override
    public void exportWorkhourCompletion(WorkhourCompletionParams params, HttpServletResponse response) {
        // TODO: 实现导出功能
        log.info("导出工时填写情况报表，参数：{}", params);
    }

    @Override
    public void notifyUser(String userId, String month) {
        // TODO: 实现用户提醒功能
        log.info("发送用户提醒，用户ID：{}，月份：{}", userId, month);
    }

    @Override
    public void notifyLeader(String leaderId) {
        // TODO: 实现负责人提醒功能
        log.info("发送负责人提醒，负责人ID：{}", leaderId);
    }

    @Override
    public void batchNotifyUsers(List<String> userIds, String month) {
        // TODO: 实现批量用户提醒功能
        log.info("批量发送用户提醒，用户数量：{}，月份：{}", userIds.size(), month);
    }

    @Override
    public List<Map<String, Object>> getDepartmentSelector() {
        // TODO: 实现分部选择器
        List<Map<String, Object>> departments = new ArrayList<>();
        Map<String, Object> dept1 = new HashMap<>();
        dept1.put("fbId", "dept1");
        dept1.put("fbName", "技术部");
        departments.add(dept1);
        
        Map<String, Object> dept2 = new HashMap<>();
        dept2.put("fbId", "dept2");
        dept2.put("fbName", "产品部");
        departments.add(dept2);
        
        return departments;
    }

    @Override
    public List<Map<String, Object>> getCompletionStatusStatistics(WorkhourCompletionParams params) {
        // TODO: 实现填写状态统计
        return new ArrayList<>();
    }

    @Override
    public List<Map<String, Object>> getApprovalEfficiencyStatistics(WorkhourCompletionParams params) {
        // TODO: 实现审批效率统计
        return new ArrayList<>();
    }

    @Override
    public List<Map<String, Object>> getOverdueStatistics(WorkhourCompletionParams params) {
        // TODO: 实现逾期统计
        return new ArrayList<>();
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<ManhourTaskEntity> buildQueryWrapper(WorkhourCompletionParams params) {
        LambdaQueryWrapper<ManhourTaskEntity> queryWrapper = new LambdaQueryWrapper<>();

        if (params == null) {
            return queryWrapper;
        }

        // 时间范围条件
        if (StringUtils.hasText(params.getStartMonth())) {
            queryWrapper.ge(ManhourTaskEntity::getMonth, params.getStartMonth());
        }
        if (StringUtils.hasText(params.getEndMonth())) {
            queryWrapper.le(ManhourTaskEntity::getMonth, params.getEndMonth());
        }

        // 分部条件
        if (StringUtils.hasText(params.getFbId())) {
            queryWrapper.eq(ManhourTaskEntity::getFbId, params.getFbId());
        }

        // 状态条件
        if (StringUtils.hasText(params.getStatus())) {
            queryWrapper.eq(ManhourTaskEntity::getStatus, Integer.parseInt(params.getStatus()));
        }

        // 用户条件
        if (StringUtils.hasText(params.getUserId())) {
            queryWrapper.eq(ManhourTaskEntity::getUserId, params.getUserId());
        }

        return queryWrapper;
    }

    /**
     * 构建查询条件（分页版本）
     */
    private LambdaQueryWrapper<ManhourTaskEntity> buildQueryWrapper(WorkhourCompletionPagination pagination) {
        WorkhourCompletionParams params = new WorkhourCompletionParams();
        params.setTimeType(pagination.getTimeType());
        params.setStartMonth(pagination.getStartMonth());
        params.setEndMonth(pagination.getEndMonth());
        params.setQuarter(pagination.getQuarter());
        params.setYear(pagination.getYear());
        params.setFbId(pagination.getFbId());
        params.setStatus(pagination.getStatus());
        params.setUserId(pagination.getUserId());
        params.setLeaderId(pagination.getLeaderId());

        return buildQueryWrapper(params);
    }

    /**
     * 判断是否逾期
     */
    private boolean isOverdue(ManhourTaskEntity manhourTask) {
        // 简化处理，实际需要根据业务规则判断
        return manhourTask.getStatus() != null && manhourTask.getStatus() < 2;
    }

    /**
     * 获取状态名称
     */
    private String getStatusName(Integer status) {
        if (status == null) return "未知";
        switch (status) {
            case 0: return "未填写";
            case 1: return "已填写";
            case 2: return "待审批";
            case 3: return "已审批";
            default: return "未知";
        }
    }

    /**
     * 获取状态代码
     */
    private String getStatusCode(String statusName) {
        switch (statusName) {
            case "未填写": return "0";
            case "已填写": return "1";
            case "待审批": return "2";
            case "已审批": return "3";
            default: return "0";
        }
    }

    /**
     * 获取审批状态名称
     */
    private String getApprovalStatusName(Integer status) {
        if (status == null) return "未知";
        switch (status) {
            case 2: return "待审批";
            case 3: return "已审批";
            default: return "未知";
        }
    }

    /**
     * 获取审批状态代码
     */
    private String getApprovalStatusCode(String statusName) {
        switch (statusName) {
            case "待审批": return "2";
            case "已审批": return "3";
            default: return "2";
        }
    }



    /**
     * 转换为待审批VO
     */
    private PendingApprovalVO convertToPendingApprovalVO(ManhourTaskEntity entity) {
        PendingApprovalVO vo = new PendingApprovalVO();
        vo.setId(entity.getId());
        vo.setUserId(entity.getUserId());

        // 关联查询用户信息
        String userName = "用户" + entity.getUserId(); // 默认值
        String fbId = entity.getFbId();
        String fbName = "分部" + fbId; // 默认值

        try {
            UserEntity userEntity = userService.getInfo(entity.getUserId());
            if (userEntity != null) {
                userName = userEntity.getRealName();
                fbId = userEntity.getOrganizeId();

                // 查询分部名称
                if (StringUtils.hasText(fbId)) {
                    OrganizeEntity organizeEntity = organizeService.getInfo(fbId);
                    if (organizeEntity != null) {
                        fbName = organizeEntity.getFullName();
                    }
                }
            }
        } catch (Exception e) {
            log.warn("查询用户信息失败，用户ID: {}", entity.getUserId(), e);
        }

        vo.setUserName(userName);
        vo.setFbId(fbId);
        vo.setFbName(fbName);
        vo.setProjectName("项目名称"); // 临时处理，需要关联项目表查询项目名
        vo.setWorkMonth(entity.getWorkMonth() != null ? BigDecimal.valueOf(entity.getWorkMonth()) : BigDecimal.ZERO);
        vo.setMonth(entity.getMonth());
        vo.setStatus(entity.getStatus());
        vo.setStatusName(getStatusName(entity.getStatus()));
        vo.setSubmitTime(entity.getCreateTime() != null ? entity.getCreateTime().toString() : "");
        vo.setWaitingDays(calculateWaitingDays(entity.getCreateTime()));
        vo.setWorkNote("工作内容"); // 临时处理，需要关联工时记录表查询工作内容
        vo.setPriority("普通"); // 示例值
        return vo;
    }

    /**
     * 转换为分部完成率VO
     */
    private DepartmentCompletionVO convertToDepartmentCompletionVO(String fbId, List<ManhourTaskEntity> deptTasks) {
        DepartmentCompletionVO vo = new DepartmentCompletionVO();

        vo.setFbId(fbId);

        // 关联查询分部名称
        String fbName = "分部" + fbId; // 默认值
        try {
            OrganizeEntity organizeEntity = organizeService.getInfo(fbId);
            if (organizeEntity != null) {
                fbName = organizeEntity.getFullName();
            }
        } catch (Exception e) {
            log.warn("查询分部信息失败，分部ID: {}", fbId, e);
        }

        vo.setFbName(fbName);

        // 计算总用户数
        Set<String> totalUsers = deptTasks.stream()
                .map(ManhourTaskEntity::getUserId)
                .collect(Collectors.toSet());
        vo.setTotalUsers(totalUsers.size());

        // 计算已完成用户数（状态大于0的用户）
        Set<String> completedUsers = deptTasks.stream()
                .filter(m -> m.getStatus() != null && m.getStatus() > 0)
                .map(ManhourTaskEntity::getUserId)
                .collect(Collectors.toSet());
        vo.setCompletedUsers(completedUsers.size());

        // 计算未完成用户数
        vo.setUnfilledUsers(totalUsers.size() - completedUsers.size());

        // 计算完成率
        BigDecimal completionRate = totalUsers.size() > 0 ?
                BigDecimal.valueOf(completedUsers.size() * 100.0 / totalUsers.size()).setScale(1, RoundingMode.HALF_UP) :
                BigDecimal.ZERO;
        vo.setCompletionRate(completionRate);

        // 计算待审批数量
        long pendingApproval = deptTasks.stream()
                .filter(m -> m.getStatus() != null && m.getStatus() == 2)
                .count();
        vo.setPendingCount((int) pendingApproval);

        // 计算逾期用户数
        long overdueUsers = deptTasks.stream()
                .filter(this::isOverdue)
                .map(ManhourTaskEntity::getUserId)
                .distinct()
                .count();
        vo.setOverdueUsers((int) overdueUsers);

        // 计算平均完成时间（示例值）
        vo.setAvgCompletionDays(BigDecimal.valueOf(5.5));

        // 设置负责人信息（需要从其他表查询）
        vo.setFbLeaderId(""); // 需要实现
        vo.setFbLeaderName(""); // 需要实现

        return vo;
    }

    /**
     * 计算等待天数（重载方法）
     */
    private int calculateWaitingDays(java.util.Date submitTime) {
        if (submitTime == null) {
            return 0;
        }
        long daysDiff = (System.currentTimeMillis() - submitTime.getTime()) / (1000 * 60 * 60 * 24);
        return (int) daysDiff;
    }

    /**
     * 获取当前月份
     */
    private String getCurrentMonth() {
        return LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMM"));
    }
}
