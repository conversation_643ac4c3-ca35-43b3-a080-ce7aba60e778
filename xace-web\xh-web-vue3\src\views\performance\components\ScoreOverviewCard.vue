<template>
  <a-card class="score-overview-card" size="small">
    <template #title>
      <div class="card-title">
        <Icon icon="ant-design:dashboard-outlined" class="title-icon" />
        <span>分数分配概览</span>
      </div>
    </template>

    <div class="score-stats">
      <div class="stat-item">
        <div class="stat-content">
          <span class="label">总可分配分数</span>
          <span class="value primary">{{ totalScore }}分</span>
        </div>
        <div class="stat-icon primary">
          <Icon icon="ant-design:pie-chart-outlined" />
        </div>
      </div>

      <div class="stat-item">
        <div class="stat-content">
          <span class="label">已分配分数</span>
          <span class="value success">{{ usedScore }}分</span>
        </div>
        <div class="stat-icon success">
          <Icon icon="ant-design:check-circle-outlined" />
        </div>
      </div>

      <div class="stat-item">
        <div class="stat-content">
          <span class="label">剩余分数</span>
          <span class="value" :class="remainingScoreClass">{{ remainingScore }}分</span>
        </div>
        <div class="stat-icon" :class="remainingScoreClass">
          <Icon :icon="remainingScore > 0 ? 'ant-design:exclamation-circle-outlined' : 'ant-design:check-circle-outlined'" />
        </div>
      </div>
    </div>

    <!-- 进度条 -->
    <div class="progress-section">
      <div class="progress-header">
        <span class="progress-label">分配进度</span>
        <span class="progress-percentage">{{ progressPercentage }}%</span>
      </div>
      <a-progress :percent="progressPercentage" :status="progressStatus" :stroke-color="progressColor" :show-info="false" stroke-width="8" />
    </div>

    <!-- 快速操作按钮 -->
    <div class="quick-actions">
      <a-button-group size="small">
        <a-button @click="handleQuickFill" :disabled="!canQuickFill">
          <template #icon><Icon icon="ant-design:thunderbolt-outlined" /></template>
          智能分配
        </a-button>
        <a-button @click="handleResetScores" :disabled="!canReset">
          <template #icon><Icon icon="ant-design:reload-outlined" /></template>
          重置分数
        </a-button>
        <a-button @click="handleAverageDistribute" :disabled="!canDistribute">
          <template #icon><Icon icon="ant-design:appstore-outlined" /></template>
          平均分配
        </a-button>
      </a-button-group>
    </div>

    <!-- 分配建议 -->
    <div class="allocation-tips" v-if="allocationTips.length > 0">
      <a-divider orientation="left" orientation-margin="0">
        <span class="tips-title">分配建议</span>
      </a-divider>
      <div class="tips-list">
        <div v-for="tip in allocationTips" :key="tip.id" class="tip-item" :class="tip.type">
          <Icon :icon="getTipIcon(tip.type)" class="tip-icon" />
          <span class="tip-text">{{ tip.message }}</span>
        </div>
      </div>
    </div>
  </a-card>
</template>

<script lang="ts" setup>
  import { computed, PropType } from 'vue';
  import { Icon } from '/@/components/Icon';

  interface AllocationTip {
    id: string;
    type: 'info' | 'warning' | 'error' | 'success';
    message: string;
  }

  const props = defineProps({
    totalScore: {
      type: Number,
      default: 0,
    },
    usedScore: {
      type: Number,
      default: 0,
    },
    canQuickFill: {
      type: Boolean,
      default: true,
    },
    canReset: {
      type: Boolean,
      default: true,
    },
    canDistribute: {
      type: Boolean,
      default: true,
    },
    allocationTips: {
      type: Array as PropType<AllocationTip[]>,
      default: () => [],
    },
  });

  const emit = defineEmits(['quickFill', 'resetScores', 'averageDistribute']);

  // 计算属性
  const remainingScore = computed(() => props.totalScore - props.usedScore);

  const remainingScoreClass = computed(() => {
    if (remainingScore.value === 0) return 'success';
    if (remainingScore.value > 0) return 'warning';
    return 'error';
  });

  const progressPercentage = computed(() => {
    if (props.totalScore === 0) return 0;
    return Math.round((props.usedScore / props.totalScore) * 100);
  });

  const progressStatus = computed(() => {
    if (remainingScore.value === 0) return 'success';
    if (remainingScore.value < 0) return 'exception';
    return 'active';
  });

  const progressColor = computed(() => {
    if (remainingScore.value === 0) return '#52c41a';
    if (remainingScore.value < 0) return '#ff4d4f';
    return '#1890ff';
  });

  // 方法
  const handleQuickFill = () => {
    emit('quickFill');
  };

  const handleResetScores = () => {
    emit('resetScores');
  };

  const handleAverageDistribute = () => {
    emit('averageDistribute');
  };

  const getTipIcon = (type: string) => {
    const iconMap = {
      info: 'ant-design:info-circle-outlined',
      warning: 'ant-design:warning-outlined',
      error: 'ant-design:close-circle-outlined',
      success: 'ant-design:check-circle-outlined',
    };
    return iconMap[type] || 'ant-design:info-circle-outlined';
  };
</script>

<style lang="less" scoped>
  // 响应式设计
  @media (max-width: 768px) {
    .score-overview-card {
      .score-stats {
        grid-template-columns: 1fr;
        gap: 12px;
      }

      .quick-actions {
        :deep(.ant-btn-group) {
          .ant-btn {
            font-size: 12px;
            padding: 4px 8px;
          }
        }
      }
    }
  }
  .score-overview-card {
    margin-bottom: 16px;

    .card-title {
      display: flex;
      align-items: center;
      gap: 8px;

      .title-icon {
        color: #1890ff;
        font-size: 16px;
      }
    }

    .score-stats {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 16px;
      margin-bottom: 20px;

      .stat-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px;
        background: #fafafa;
        border-radius: 6px;
        transition: all 0.3s ease;

        &:hover {
          background: #f0f0f0;
          transform: translateY(-2px);
        }

        .stat-content {
          display: flex;
          flex-direction: column;
          gap: 4px;

          .label {
            font-size: 12px;
            color: #666;
            font-weight: 500;
          }

          .value {
            font-size: 18px;
            font-weight: bold;

            &.primary {
              color: #1890ff;
            }
            &.success {
              color: #52c41a;
            }
            &.warning {
              color: #faad14;
            }
            &.error {
              color: #ff4d4f;
            }
          }
        }

        .stat-icon {
          font-size: 24px;
          opacity: 0.6;

          &.primary {
            color: #1890ff;
          }
          &.success {
            color: #52c41a;
          }
          &.warning {
            color: #faad14;
          }
          &.error {
            color: #ff4d4f;
          }
        }
      }
    }

    .progress-section {
      margin-bottom: 20px;

      .progress-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .progress-label {
          font-size: 14px;
          color: #666;
          font-weight: 500;
        }

        .progress-percentage {
          font-size: 14px;
          font-weight: bold;
          color: #1890ff;
        }
      }
    }

    .quick-actions {
      margin-bottom: 16px;

      :deep(.ant-btn-group) {
        width: 100%;

        .ant-btn {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 4px;
        }
      }
    }

    .allocation-tips {
      .tips-title {
        font-size: 12px;
        color: #666;
        font-weight: 500;
      }

      .tips-list {
        .tip-item {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 6px 0;
          font-size: 12px;

          .tip-icon {
            font-size: 14px;
          }

          &.info {
            color: #1890ff;
          }

          &.warning {
            color: #faad14;
          }

          &.error {
            color: #ff4d4f;
          }

          &.success {
            color: #52c41a;
          }
        }
      }
    }
  }
</style>
