# Claude 快速开发命令

专为Claude优化的XACE项目开发提示词。

## 🎯 核心生成命令

### 后端开发

**⚡ 最新快速指令（推荐）**：
> 详细指令请参考 [backend/QUICK_REFERENCE.md](../backend/QUICK_REFERENCE.md)

```
# 完整模块（一键生成8个文件）
生成XACE完整CRUD：
表名：[table_name]
业务名：[Business]
字段：[field1(type,desc), field2(type,desc)]
模块：com.xinghuo.[module]

# 单独组件生成
生成XACE Entity：表名：[table] 业务名：[Business] 字段：[字段列表]
生成XACE Form：业务名：[Business] 字段：[字段和验证规则]
生成XACE VO：业务名：[Business] 展示字段：[列表] 关联字段：[关联列表]
生成XACE Service：业务名：[Business] 特殊功能：[业务方法列表]
生成XACE Controller：业务名：[Business] API路径：/api/[path] 功能：[CRUD功能]
```

**传统详细模板（兼容保留）**：
```
请基于XACE框架生成 [模块名] 的完整后端代码，包括：
1. Entity实体类（继承BaseEntityV2.CUBaseEntityV2）
2. Mapper接口（继承XHBaseMapper）
3. Service接口和实现类
4. Controller控制器（返回ActionResult格式）
5. 模型类：VO、Form、Pagination

要求：
- 使用Jakarta EE导入 (jakarta.* 不是 javax.*)
- 遵循XACE命名规范
- 包含标准CRUD操作
- 添加必要的验证注解
- 包结构：com.xinghuo.[模块名].model.[业务功能]
- 包含完整的import路径
- 推荐使用Form合并设计，仅在验证差异大时分离为CrForm/UpForm
- 只使用VO/Form/Pagination，不使用DTO/Model/Query
```

### 前端开发  
```
请基于XACE框架生成 [页面名] 的Vue 3页面，包括：
1. 使用BasicTable表格组件
2. 使用BasicForm表单组件  
3. API调用处理（检查ActionResult格式）
4. 分页数据处理
5. TypeScript类型定义

要求：
- 使用Composition API
- 组件数据格式为fullName/id
- 正确处理response.code检查
- 包含错误处理逻辑
```

### API接口设计
```
请设计 [业务模块] 的RESTful API接口，要求：
1. 遵循XACE Controller规范
2. 返回ActionResult<T>格式
3. 支持分页查询
4. 包含参数验证
5. 生成对应的前端TypeScript接口

参考XACE标准的增删改查接口设计。
```

## 🔧 调试和优化命令

### 代码审查
```
请审查以下XACE项目代码，检查：
1. 是否使用Jakarta EE导入
2. 实体类是否正确继承BaseEntityV2
3. API响应是否使用ActionResult格式
4. 前端是否正确处理response.code
5. 组件数据是否使用fullName/id格式

[粘贴代码]
```

### 错误修复
```
XACE项目中遇到以下错误，请帮助修复：
[错误信息]

请按照XACE框架规范提供解决方案，重点检查：
- Jakarta EE导入问题
- BaseEntityV2字段映射
- ActionResult响应处理
- 分页数据结构
```

### 性能优化
```
请优化以下XACE项目代码的性能：
[代码片段]

重点关注：
- 数据库查询优化
- 前端组件渲染优化  
- API接口响应优化
- 内存使用优化
```

## 📋 开发规范检查

### 快速检查命令
```
请检查以下代码是否符合XACE开发规范：

后端检查项：
- [ ] Jakarta EE导入
- [ ] BaseEntityV2继承
- [ ] XHBaseMapper继承
- [ ] ActionResult返回格式
- [ ] 标准包结构

前端检查项：
- [ ] response.code检查
- [ ] fullName/id数据格式
- [ ] BasicTable/BasicForm使用
- [ ] TypeScript类型定义
- [ ] Composition API规范

[代码内容]
```

## 🚀 一键生成模板

### 完整模块生成
```
请生成一个完整的XACE业务模块：[模块名]

包含功能：
- 数据管理（增删改查）
- 列表展示和搜索
- 表单验证
- 权限控制

技术要求：
- 后端：Spring Boot 3 + MyBatis-Plus + Jakarta EE
- 前端：Vue 3 + TypeScript + Ant Design Vue
- 严格遵循XACE框架规范
```

## 💡 智能提示

使用这些命令时，Claude会：
1. 自动应用XACE核心规则
2. 生成符合规范的代码
3. 包含必要的错误处理
4. 提供代码说明和注意事项