<template>
  <div class="resource-analysis p-4">
    <!-- 页面标题 -->
    <div class="page-header mb-4">
      <h2 class="text-xl font-semibold mb-2">资源分析</h2>
      <p class="text-gray-600">分析项目资源配置、利用率和优化建议</p>
    </div>

    <!-- 资源概览卡片 -->
    <div class="resource-overview grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <div class="overview-card bg-white rounded-lg shadow-sm border p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 mb-1">人力资源</p>
            <p class="text-2xl font-bold text-blue-600">{{ resourceOverview.humanResource.total }}</p>
          </div>
          <div class="resource-icon bg-blue-100 p-3 rounded-full">
            <TeamOutlined class="text-blue-600 text-xl" />
          </div>
        </div>
        <div class="mt-3">
          <div class="flex justify-between text-sm">
            <span class="text-gray-500">利用率</span>
            <span class="font-medium">{{ resourceOverview.humanResource.utilization }}%</span>
          </div>
          <a-progress :percent="resourceOverview.humanResource.utilization" :show-info="false" size="small" />
        </div>
      </div>

      <div class="overview-card bg-white rounded-lg shadow-sm border p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 mb-1">技术资源</p>
            <p class="text-2xl font-bold text-green-600">{{ resourceOverview.techResource.total }}</p>
          </div>
          <div class="resource-icon bg-green-100 p-3 rounded-full">
            <DatabaseOutlined class="text-green-600 text-xl" />
          </div>
        </div>
        <div class="mt-3">
          <div class="flex justify-between text-sm">
            <span class="text-gray-500">使用率</span>
            <span class="font-medium">{{ resourceOverview.techResource.usage }}%</span>
          </div>
          <a-progress :percent="resourceOverview.techResource.usage" :show-info="false" size="small" stroke-color="#52c41a" />
        </div>
      </div>

      <div class="overview-card bg-white rounded-lg shadow-sm border p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 mb-1">预算资源</p>
            <p class="text-2xl font-bold text-purple-600">¥{{ resourceOverview.budget.used }}万</p>
          </div>
          <div class="resource-icon bg-purple-100 p-3 rounded-full">
            <DollarOutlined class="text-purple-600 text-xl" />
          </div>
        </div>
        <div class="mt-3">
          <div class="flex justify-between text-sm">
            <span class="text-gray-500">使用率</span>
            <span class="font-medium">{{ Math.round((resourceOverview.budget.used / resourceOverview.budget.total) * 100) }}%</span>
          </div>
          <a-progress
            :percent="Math.round((resourceOverview.budget.used / resourceOverview.budget.total) * 100)"
            :show-info="false"
            size="small"
            stroke-color="#722ed1" />
        </div>
      </div>

      <div class="overview-card bg-white rounded-lg shadow-sm border p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 mb-1">时间资源</p>
            <p class="text-2xl font-bold text-orange-600">{{ resourceOverview.timeResource.used }}天</p>
          </div>
          <div class="resource-icon bg-orange-100 p-3 rounded-full">
            <ClockCircleOutlined class="text-orange-600 text-xl" />
          </div>
        </div>
        <div class="mt-3">
          <div class="flex justify-between text-sm">
            <span class="text-gray-500">进度</span>
            <span class="font-medium">{{ Math.round((resourceOverview.timeResource.used / resourceOverview.timeResource.total) * 100) }}%</span>
          </div>
          <a-progress
            :percent="Math.round((resourceOverview.timeResource.used / resourceOverview.timeResource.total) * 100)"
            :show-info="false"
            size="small"
            stroke-color="#fa8c16" />
        </div>
      </div>
    </div>

    <!-- 详细资源分析 -->
    <div class="detailed-analysis grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
      <!-- 人力资源分析 -->
      <div class="analysis-card bg-white rounded-lg shadow-sm border p-4">
        <div class="card-header mb-4">
          <h3 class="text-lg font-medium">人力资源分析</h3>
          <p class="text-sm text-gray-500">团队成员工作负载和技能分布</p>
        </div>
        <div class="human-resource-content">
          <!-- 工作负载分布 -->
          <div class="workload-section mb-6">
            <h4 class="text-sm font-medium mb-3">工作负载分布</h4>
            <div class="workload-list">
              <div v-for="(member, index) in teamMembers" :key="index" class="workload-item mb-3">
                <div class="flex justify-between items-center mb-1">
                  <div class="flex items-center">
                    <div class="member-avatar bg-blue-100 w-6 h-6 rounded-full flex items-center justify-center mr-2">
                      <span class="text-xs text-blue-600">{{ member.name.charAt(0) }}</span>
                    </div>
                    <span class="text-sm">{{ member.name }}</span>
                    <span class="text-xs text-gray-500 ml-2">{{ member.role }}</span>
                  </div>
                  <span class="text-sm font-medium">{{ member.workload }}%</span>
                </div>
                <a-progress :percent="member.workload" :show-info="false" size="small" :stroke-color="getWorkloadColor(member.workload)" />
              </div>
            </div>
          </div>

          <!-- 技能分布 -->
          <div class="skills-section">
            <h4 class="text-sm font-medium mb-3">技能分布</h4>
            <div class="skills-chart">
              <div v-for="(skill, index) in skillDistribution" :key="index" class="skill-item flex items-center justify-between mb-2">
                <span class="text-sm">{{ skill.name }}</span>
                <div class="flex items-center">
                  <div class="skill-bar bg-gray-200 w-20 h-2 rounded-full overflow-hidden mr-2">
                    <div class="bg-blue-500 h-full" :style="{ width: skill.level + '%' }"></div>
                  </div>
                  <span class="text-xs text-gray-600 w-8">{{ skill.count }}人</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 技术资源分析 -->
      <div class="analysis-card bg-white rounded-lg shadow-sm border p-4">
        <div class="card-header mb-4">
          <h3 class="text-lg font-medium">技术资源分析</h3>
          <p class="text-sm text-gray-500">服务器、数据库等技术资源使用情况</p>
        </div>
        <div class="tech-resource-content">
          <!-- 服务器资源 -->
          <div class="server-section mb-6">
            <h4 class="text-sm font-medium mb-3">服务器资源</h4>
            <div class="server-list">
              <div v-for="(server, index) in serverResources" :key="index" class="server-item mb-3">
                <div class="flex justify-between items-center mb-2">
                  <span class="text-sm font-medium">{{ server.name }}</span>
                  <span class="text-xs px-2 py-1 rounded" :class="getServerStatusClass(server.status)">
                    {{ server.status }}
                  </span>
                </div>
                <div class="server-metrics grid grid-cols-3 gap-2 text-xs">
                  <div class="metric-item">
                    <div class="text-gray-500 mb-1">CPU</div>
                    <a-progress :percent="server.cpu" :show-info="false" size="small" />
                    <div class="text-center">{{ server.cpu }}%</div>
                  </div>
                  <div class="metric-item">
                    <div class="text-gray-500 mb-1">内存</div>
                    <a-progress :percent="server.memory" :show-info="false" size="small" stroke-color="#52c41a" />
                    <div class="text-center">{{ server.memory }}%</div>
                  </div>
                  <div class="metric-item">
                    <div class="text-gray-500 mb-1">磁盘</div>
                    <a-progress :percent="server.disk" :show-info="false" size="small" stroke-color="#faad14" />
                    <div class="text-center">{{ server.disk }}%</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 数据库资源 -->
          <div class="database-section">
            <h4 class="text-sm font-medium mb-3">数据库资源</h4>
            <div class="database-list">
              <div v-for="(db, index) in databaseResources" :key="index" class="database-item flex justify-between items-center mb-2">
                <div class="flex items-center">
                  <div class="db-icon bg-green-100 w-6 h-6 rounded flex items-center justify-center mr-2">
                    <DatabaseOutlined class="text-green-600 text-xs" />
                  </div>
                  <span class="text-sm">{{ db.name }}</span>
                </div>
                <div class="flex items-center">
                  <span class="text-xs text-gray-500 mr-2">{{ db.size }}GB</span>
                  <span class="text-xs px-2 py-1 rounded" :class="getDbStatusClass(db.performance)">
                    {{ db.performance }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 资源优化建议 -->
    <div class="optimization-suggestions grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 资源瓶颈 -->
      <div class="bottleneck-card bg-white rounded-lg shadow-sm border p-4">
        <div class="card-header mb-4">
          <h3 class="text-lg font-medium">资源瓶颈</h3>
          <p class="text-sm text-gray-500">当前资源配置中的瓶颈问题</p>
        </div>
        <div class="bottleneck-list">
          <div v-for="(bottleneck, index) in resourceBottlenecks" :key="index" class="bottleneck-item mb-4 last:mb-0">
            <div class="flex items-start">
              <div class="bottleneck-type w-8 h-8 rounded-full flex items-center justify-center mr-3" :class="getBottleneckTypeClass(bottleneck.type)">
                <component :is="getBottleneckIcon(bottleneck.type)" class="text-sm" />
              </div>
              <div class="bottleneck-content flex-1">
                <h4 class="text-sm font-medium mb-1">{{ bottleneck.title }}</h4>
                <p class="text-xs text-gray-600 mb-2">{{ bottleneck.description }}</p>
                <div class="flex items-center">
                  <span class="text-xs px-2 py-1 rounded" :class="getSeverityBadgeClass(bottleneck.severity)">
                    {{ bottleneck.severity }}
                  </span>
                  <span class="text-xs text-gray-500 ml-2">影响: {{ bottleneck.impact }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 优化建议 -->
      <div class="suggestions-card bg-white rounded-lg shadow-sm border p-4">
        <div class="card-header mb-4">
          <h3 class="text-lg font-medium">优化建议</h3>
          <p class="text-sm text-gray-500">提升资源利用效率的建议措施</p>
        </div>
        <div class="suggestions-list">
          <div v-for="(suggestion, index) in optimizationSuggestions" :key="index" class="suggestion-item mb-4 last:mb-0">
            <div class="flex items-start">
              <div class="suggestion-priority w-3 h-3 rounded-full mr-3 mt-1" :class="getPriorityClass(suggestion.priority)"></div>
              <div class="suggestion-content flex-1">
                <h4 class="text-sm font-medium mb-1">{{ suggestion.title }}</h4>
                <p class="text-xs text-gray-600 mb-2">{{ suggestion.description }}</p>
                <div class="flex items-center justify-between">
                  <span class="text-xs px-2 py-1 rounded" :class="getPriorityBadgeClass(suggestion.priority)">
                    {{ suggestion.priority }}
                  </span>
                  <div class="flex items-center text-xs text-gray-500">
                    <span class="mr-2">预期节省: {{ suggestion.expectedSaving }}</span>
                    <span>实施难度: {{ suggestion.difficulty }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { Progress } from 'ant-design-vue';
  import { TeamOutlined, DatabaseOutlined, DollarOutlined, ClockCircleOutlined, WarningOutlined, BulbOutlined } from '@ant-design/icons-vue';
  import { getResourceAnalysis } from '../../api';

  const props = defineProps({
    projectId: {
      type: String,
      default: '',
    },
  });

  // 资源概览数据
  const resourceOverview = ref({
    humanResource: {
      total: 12,
      utilization: 85,
    },
    techResource: {
      total: 8,
      usage: 72,
    },
    budget: {
      used: 45.6,
      total: 80,
    },
    timeResource: {
      used: 45,
      total: 90,
    },
  });

  // 团队成员数据
  const teamMembers = ref([
    { name: '张三', role: '项目经理', workload: 95 },
    { name: '李四', role: '技术负责人', workload: 88 },
    { name: '王五', role: '前端开发', workload: 75 },
    { name: '赵六', role: '后端开发', workload: 82 },
    { name: '钱七', role: '测试工程师', workload: 65 },
    { name: '孙八', role: 'UI设计师', workload: 70 },
  ]);

  // 技能分布数据
  const skillDistribution = ref([
    { name: 'Vue.js', level: 80, count: 4 },
    { name: 'Java', level: 90, count: 5 },
    { name: 'Python', level: 60, count: 3 },
    { name: 'MySQL', level: 85, count: 6 },
    { name: 'Redis', level: 70, count: 4 },
    { name: 'Docker', level: 65, count: 3 },
  ]);

  // 服务器资源数据
  const serverResources = ref([
    { name: '生产服务器-01', status: '正常', cpu: 65, memory: 78, disk: 45 },
    { name: '测试服务器-01', status: '正常', cpu: 45, memory: 55, disk: 32 },
    { name: '开发服务器-01', status: '警告', cpu: 85, memory: 92, disk: 67 },
  ]);

  // 数据库资源数据
  const databaseResources = ref([
    { name: '主数据库', size: 125.6, performance: '良好' },
    { name: '缓存数据库', size: 45.2, performance: '优秀' },
    { name: '日志数据库', size: 89.3, performance: '一般' },
    { name: '测试数据库', size: 23.7, performance: '良好' },
  ]);

  // 资源瓶颈数据
  const resourceBottlenecks = ref([
    {
      type: 'human',
      title: '核心开发人员工作负载过高',
      description: '技术负责人和项目经理工作负载超过85%，存在过劳风险',
      severity: '高',
      impact: '可能影响项目质量和进度',
    },
    {
      type: 'server',
      title: '开发服务器资源紧张',
      description: '开发服务器CPU和内存使用率过高，影响开发效率',
      severity: '中',
      impact: '开发环境响应缓慢',
    },
    {
      type: 'skill',
      title: '新技术栈人员不足',
      description: 'Docker和微服务相关技能人员较少，可能影响部署',
      severity: '中',
      impact: '部署和运维风险增加',
    },
  ]);

  // 优化建议数据
  const optimizationSuggestions = ref([
    {
      title: '合理分配工作负载',
      description: '将部分非核心任务分配给其他团队成员，减轻核心人员压力',
      priority: '高',
      expectedSaving: '20%工作量',
      difficulty: '低',
    },
    {
      title: '升级开发服务器配置',
      description: '增加开发服务器的CPU和内存配置，提升开发环境性能',
      priority: '高',
      expectedSaving: '30%响应时间',
      difficulty: '中',
    },
    {
      title: '组织技术培训',
      description: '针对Docker和微服务技术组织内部培训，提升团队技能',
      priority: '中',
      expectedSaving: '减少外包成本',
      difficulty: '中',
    },
    {
      title: '引入自动化工具',
      description: '使用CI/CD工具自动化部署流程，减少人工操作',
      priority: '中',
      expectedSaving: '50%部署时间',
      difficulty: '高',
    },
  ]);

  onMounted(async () => {
    await loadResourceAnalysis();
  });

  const loadResourceAnalysis = async () => {
    try {
      // 这里可以调用实际的API获取数据
      // const result = await getResourceAnalysis({ projectId: props.projectId });

      console.log('资源分析数据加载完成');
    } catch (error) {
      console.error('加载资源分析数据失败:', error);
    }
  };

  const getWorkloadColor = (workload: number) => {
    if (workload >= 90) return '#ff4d4f';
    if (workload >= 80) return '#faad14';
    if (workload >= 60) return '#1890ff';
    return '#52c41a';
  };

  const getServerStatusClass = (status: string) => {
    const classes = {
      正常: 'bg-green-100 text-green-800',
      警告: 'bg-yellow-100 text-yellow-800',
      异常: 'bg-red-100 text-red-800',
    };
    return classes[status] || 'bg-gray-100 text-gray-800';
  };

  const getDbStatusClass = (performance: string) => {
    const classes = {
      优秀: 'bg-green-100 text-green-800',
      良好: 'bg-blue-100 text-blue-800',
      一般: 'bg-yellow-100 text-yellow-800',
      较差: 'bg-red-100 text-red-800',
    };
    return classes[performance] || 'bg-gray-100 text-gray-800';
  };

  const getBottleneckTypeClass = (type: string) => {
    const classes = {
      human: 'bg-blue-100 text-blue-600',
      server: 'bg-orange-100 text-orange-600',
      skill: 'bg-purple-100 text-purple-600',
      budget: 'bg-green-100 text-green-600',
    };
    return classes[type] || 'bg-gray-100 text-gray-600';
  };

  const getBottleneckIcon = (type: string) => {
    const icons = {
      human: TeamOutlined,
      server: ClockCircleOutlined,
      skill: BulbOutlined,
      budget: DollarOutlined,
    };
    return icons[type] || WarningOutlined;
  };

  const getSeverityBadgeClass = (severity: string) => {
    const classes = {
      高: 'bg-red-100 text-red-800',
      中: 'bg-yellow-100 text-yellow-800',
      低: 'bg-green-100 text-green-800',
    };
    return classes[severity] || 'bg-gray-100 text-gray-800';
  };

  const getPriorityClass = (priority: string) => {
    const classes = {
      高: 'bg-red-500',
      中: 'bg-blue-500',
      低: 'bg-gray-500',
    };
    return classes[priority] || 'bg-gray-500';
  };

  const getPriorityBadgeClass = (priority: string) => {
    const classes = {
      高: 'bg-red-100 text-red-800',
      中: 'bg-blue-100 text-blue-800',
      低: 'bg-gray-100 text-gray-800',
    };
    return classes[priority] || 'bg-gray-100 text-gray-800';
  };
</script>
