package com.xinghuo.project.biz.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业务类型枚举
 * 
 * <AUTHOR>
 * @version V2.0
 */
@Getter
@AllArgsConstructor
public enum BusinessTypeEnum {
    
    /**
     * 商机
     */
    OPPORTUNITY(1, "商机", "opportunity"),
    
    /**
     * 合同
     */
    CONTRACT(2, "合同", "contract"),
    
    /**
     * 收款
     */
    RECEIVABLE(3, "收款", "receivable"),
    
    /**
     * 付款
     */
    PAYMENT(4, "付款", "payment");

    private final Integer code;
    private final String message;
    private final String value;

    public static BusinessTypeEnum getByCode(Integer code) {
        for (BusinessTypeEnum type : BusinessTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}