<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" :title="getTitle" :width="800" :maskClosable="false" showFooter @ok="handleSubmit">
    <BasicForm @register="registerForm">
      <template #contractSelect="{ model, field }">
        <ContractSelect v-model:value="model[field]" placeholder="请选择合同" />
      </template>
    </BasicForm>
  </BasicDrawer>
</template>

<script lang="ts" setup>
  import { ref, computed, unref } from 'vue';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { createContractMoney, updateContractMoney } from '/@/api/project/contractMoney';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { formatToDate } from '/@/utils/dateUtil';
  import ContractSelect from '/@/views/project/components/ContractSelect.vue';

  import type { FormSchema } from '/@/components/Table';
  import type { ContractMoneyModel } from '/@/api/project/contractMoney';

  const emit = defineEmits(['success', 'register']);
  const { createMessage } = useMessage();

  const isUpdate = ref(true);
  const rowId = ref('');
  const readonly = ref(false);

  const [registerForm, { setFieldsValue, resetFields, validate, updateSchema }] = useForm({
    labelWidth: 120,
    baseColProps: { span: 24 },
    schemas: getFormSchema(),
    showActionButtonGroup: false,
  });

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async data => {
    resetFields();
    setDrawerProps({ confirmLoading: false });

    isUpdate.value = !!data?.isUpdate;
    readonly.value = !!data?.readonly;

    if (unref(isUpdate) || readonly.value) {
      rowId.value = data.record.cmId;

      console.log('处理收款记录数据，原始数据:', data.record);
      console.log('合同ID:', data.record.contractId);

      // 设置表单值，确保所有字段正确传递
      const formValues = {
        ...data.record,
        // 确保合同ID字段名正确
        contractId: data.record.contractId,
        // 格式化日期字段
        yingshouDate: data.record.yingshouDate ? formatToDate(data.record.yingshouDate) : undefined,
        yushouDate: data.record.yushouDate ? formatToDate(data.record.yushouDate) : undefined,
        kaipiaoDate: data.record.kaipiaoDate ? formatToDate(data.record.kaipiaoDate) : undefined,
        shoukuanDate: data.record.shoukuanDate ? formatToDate(data.record.shoukuanDate) : undefined,
        // 确保状态字段为字符串
        payStatus: String(data.record.payStatus || '0'),
      };

      console.log('设置表单值:', formValues);
      await setFieldsValue(formValues);
    }

    // 设置表单只读状态
    const schemas = getFormSchema();
    schemas.forEach(schema => {
      updateSchema({
        field: schema.field,
        componentProps: {
          ...schema.componentProps,
          disabled: readonly.value,
        },
      });
    });

    // 设置抽屉按钮显示状态
    setDrawerProps({
      showOkBtn: !readonly.value,
      showCancelBtn: true,
      okText: readonly.value ? undefined : unref(isUpdate) ? '保存' : '确认',
      cancelText: readonly.value ? '关闭' : '取消',
    });
  });

  const getTitle = computed(() => {
    if (readonly.value) {
      return '查看收款详情';
    }
    return !unref(isUpdate) ? '新增收款' : '编辑收款';
  });

  function getFormSchema(): FormSchema[] {
    return [
      {
        field: 'contractId',
        label: '合同',
        component: 'Input',
        slot: 'contractSelect',
        required: true,
        colProps: { span: 24 },
      },
      {
        field: 'fktj',
        label: '支付条件',
        component: 'Input',
        componentProps: {
          placeholder: '请输入支付条件',
        },
        colProps: { span: 24 },
      },
      {
        field: 'ratio',
        label: '收款比例(%)',
        component: 'Input',
        componentProps: {
          placeholder: '请输入收款比例',
        },
        colProps: { span: 12 },
      },
      {
        field: 'cmMoney',
        label: '收款金额',
        component: 'InputNumber',
        componentProps: {
          placeholder: '请输入收款金额',
          min: 0,
          precision: 2,
          formatter: (value: string) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
          parser: (value: string) => value.replace(/¥\s?|(,*)/g, ''),
        },
        required: true,
        colProps: { span: 12 },
      },
      {
        field: 'payStatus',
        label: '收款状态',
        component: 'Select',
        componentProps: {
          placeholder: '请选择收款状态',
          options: [
            { fullName: '未收款', id: '0' },
            { fullName: '已收款', id: '1' },
          ],
        },
        colProps: { span: 12 },
      },
      {
        field: 'yingshouDate',
        label: '应收日期',
        component: 'DatePicker',
        componentProps: {
          placeholder: '请选择应收日期',
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
        },
        colProps: { span: 12 },
      },
      {
        field: 'yushouDate',
        label: '预收日期',
        component: 'DatePicker',
        componentProps: {
          placeholder: '请选择预收日期',
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
        },
        colProps: { span: 12 },
      },
      {
        field: 'kaipiaoDate',
        label: '开票日期',
        component: 'DatePicker',
        componentProps: {
          placeholder: '请选择开票日期',
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
        },
        colProps: { span: 12 },
      },
      {
        field: 'shoukuanDate',
        label: '收款日期',
        component: 'DatePicker',
        componentProps: {
          placeholder: '请选择收款日期',
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
        },
        colProps: { span: 12 },
      },
      {
        field: 'note',
        label: '备注',
        component: 'Textarea',
        componentProps: {
          type: 'textarea',
          placeholder: '请输入备注',
          rows: 3,
        },
        colProps: { span: 24 },
      },
    ];
  }

  async function handleSubmit() {
    try {
      const values = await validate();
      setDrawerProps({ confirmLoading: true });

      if (unref(isUpdate)) {
        await updateContractMoney(rowId.value, values);
        createMessage.success('编辑成功');
      } else {
        await createContractMoney(values);
        createMessage.success('新增成功');
      }

      closeDrawer();
      emit('success');
    } catch (error) {
      createMessage.error('操作失败');
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
