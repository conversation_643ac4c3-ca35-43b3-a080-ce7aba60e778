package com.xinghuo.project.biz.model.paymentContractMoney;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 采购付款计划视图对象
 */
@Data
@Schema(description = "采购付款计划视图对象")
public class PaymentContractMoneyVO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private String id;

    /**
     * 采购合同ID
     */
    @Schema(description = "采购合同ID")
    private String pcId;

    /**
     * 采购合同名称（非数据库字段，需要关联查询）
     */
    @Schema(description = "采购合同名称")
    private String paycontractName;

    /**
     * 采购合同编号（非数据库字段，需要关联查询）
     */
    @Schema(description = "采购合同编号")
    private String paycontractNo;

    /**
     * 付款条件
     */
    @Schema(description = "付款条件")
    private String fktj;

    /**
     * 付款比例
     */
    @Schema(description = "付款比例")
    private BigDecimal ratio;

    /**
     * 付款金额
     */
    @Schema(description = "付款金额")
    private BigDecimal cmMoney;

    /**
     * 预付日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "预付日期")
    private Date yufuDate;

    /**
     * 付款日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "付款日期")
    private Date fukuanDate;

    /**
     * 付款状态 1-已付款，0-未付款
     */
    @Schema(description = "付款状态 1-已付款，0-未付款")
    private Integer payStatus;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 最后备注
     */
    @Schema(description = "最后备注")
    private String lastNote;

    /**
     * 负责人ID
     */
    @Schema(description = "负责人ID")
    private String ownId;

    /**
     * 负责人名称（非数据库字段，需要关联查询）
     */
    @Schema(description = "负责人名称")
    private String ownName;

    /**
     * 部门ID
     */
    @Schema(description = "部门ID")
    private String deptId;

    /**
     * 部门名称（非数据库字段，需要关联查询）
     */
    @Schema(description = "部门名称")
    private String deptName;

    /**
     * 一部金额
     */
    @Schema(description = "一部金额")
    private BigDecimal ybAmount;

    /**
     * 二部金额
     */
    @Schema(description = "二部金额")
    private BigDecimal ebAmount;

    /**
     * 其他金额
     */
    @Schema(description = "其他金额")
    private BigDecimal otherAmount;

    /**
     * 创建用户
     */
    @Schema(description = "创建用户")
    private String createUserId;

    /**
     * 创建用户名称（非数据库字段，需要关联查询）
     */
    @Schema(description = "创建用户名称")
    private String createUserName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 最后修改人
     */
    @Schema(description = "最后修改人")
    private String lastModifiedUserId;

    /**
     * 最后修改人名称（非数据库字段，需要关联查询）
     */
    @Schema(description = "最后修改人名称")
    private String lastModifiedUserName;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "更新时间")
    private Date updateTime;
}
