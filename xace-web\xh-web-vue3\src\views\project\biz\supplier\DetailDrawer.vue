<template>
  <BasicDrawer
    v-bind="$attrs"
    @register="registerDrawer"
    :title="getTitle"
    width="90%"
    showFooter
    :canFullscreen="true"
    :maskClosable="false"
    :keyboard="false"
    class="supplier-detail-drawer"
    @ok="handleClose">
    <template #footer>
      <a-space>
        <a-button @click="handleClose">
          <template #icon><CloseOutlined /></template>
          关闭
        </a-button>
      </a-space>
    </template>

    <div class="supplier-detail-container">
      <a-spin :spinning="loading" tip="加载中...">
        <!-- 供应商概览卡片 -->
        <a-card :bordered="false" class="overview-card mb-4">
          <template #title>
            <div class="card-title">
              <ShopOutlined class="title-icon" />
              供应商概览 【{{ supplierInfo?.name || '-' }}】
            </div>
          </template>
          <template #extra>
            <a-space>
              <a-tag color="blue" v-if="supplierInfo?.name">
                <ShopOutlined class="tag-icon" />
                供应商
              </a-tag>
            </a-space>
          </template>
        </a-card>

        <a-tabs v-model:activeKey="activeTab" type="card" class="supplier-detail-tabs" :tabBarGutter="8" :key="`tabs-${supplierId}`">
          <!-- 基本信息标签页 -->
          <a-tab-pane key="basic" class="tab-pane-content">
            <template #tab>
              <span class="tab-title">
                <InfoCircleOutlined class="tab-icon" />
                基本信息
              </span>
            </template>
            <a-card :bordered="false" class="detail-card">
              <template #title>
                <div class="card-title">
                  <FileTextOutlined class="title-icon" />
                  详细信息
                </div>
              </template>
              <Description :bordered="true" :schema="basicSchema" :data="supplierInfo" :column="2" size="middle" class="enhanced-description" />

              <!-- 创建和修改信息单独区域 -->
              <div class="audit-section">
                <div class="audit-header">
                  <ClockCircleOutlined class="audit-icon" />
                  <span class="audit-title">创建和修改信息</span>
                </div>
                <div class="audit-content">
                  <div class="audit-grid">
                    <div class="audit-item">
                      <div class="audit-label">
                        <UserAddOutlined class="item-icon" />
                        创建人
                      </div>
                      <div class="audit-value">{{ supplierInfo?.createdBy || '-' }}</div>
                    </div>
                    <div class="audit-item">
                      <div class="audit-label">
                        <CalendarOutlined class="item-icon" />
                        创建时间
                      </div>
                      <div class="audit-value">{{ formatDateTime(supplierInfo?.createdAt) }}</div>
                    </div>
                    <div class="audit-item">
                      <div class="audit-label">
                        <EditOutlined class="item-icon" />
                        最后修改人
                      </div>
                      <div class="audit-value">{{ supplierInfo?.lastUpdatedBy || '-' }}</div>
                    </div>
                    <div class="audit-item">
                      <div class="audit-label">
                        <CalendarOutlined class="item-icon" />
                        最后修改时间
                      </div>
                      <div class="audit-value">{{ formatDateTime(supplierInfo?.lastUpdatedAt) }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </a-card>
          </a-tab-pane>

          <!-- 采购合同标签页 -->
          <a-tab-pane key="contracts" class="tab-pane-content">
            <template #tab>
              <span class="tab-title">
                <FileTextOutlined class="tab-icon" />
                采购合同
                <a-badge :count="totalCount" :offset="[10, -5]" v-if="totalCount > 0" />
              </span>
            </template>

            <!-- 紧凑统计信息 -->
            <div class="compact-stats mb-4">
              <div class="stats-row">
                <div class="stat-compact">
                  <span class="stat-label">合同总数:</span>
                  <span class="stat-value total">{{ totalCount }}</span>
                </div>
                <div class="stat-compact">
                  <span class="stat-label">已签合同:</span>
                  <span class="stat-value signed">{{ signedCount }}</span>
                </div>
                <div class="stat-compact">
                  <span class="stat-label">合同总金额:</span>
                  <span class="stat-value amount">¥{{ totalAmount.toLocaleString() }}</span>
                </div>
                <div class="stat-compact">
                  <span class="stat-label">已签金额:</span>
                  <span class="stat-value amount-signed">¥{{ signedAmount.toLocaleString() }}</span>
                </div>
              </div>
            </div>

            <!-- 采购合同列表 -->
            <a-card :bordered="false" class="table-card">
              <template #title>
                <div class="card-title">
                  <TableOutlined class="title-icon" />
                  合同列表
                </div>
              </template>
              <BasicTable @register="registerTable" :canResize="false" class="enhanced-table">
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'status'">
                    <a-tag :color="getStatusColor(record.status)">
                      {{ record.status }}
                    </a-tag>
                  </template>
                  <template v-if="column.key === 'amount'">
                    <span class="amount-text">
                      {{ record.amount ? `¥${record.amount.toLocaleString()}` : '-' }}
                    </span>
                  </template>
                </template>
                <template #summary>
                  <a-table-summary>
                    <a-table-summary-row class="summary-row">
                      <a-table-summary-cell :index="0" :col-span="2">
                        <strong>合计</strong>
                      </a-table-summary-cell>
                      <a-table-summary-cell :index="2">
                        <strong class="total-amount">¥{{ totalAmount.toLocaleString() }}</strong>
                      </a-table-summary-cell>
                      <a-table-summary-cell :index="3" :col-span="4">
                        <strong class="summary-text">
                          总数: {{ totalCount }} | 已签: {{ signedCount }} | 已签金额: ¥{{ signedAmount.toLocaleString() }}
                        </strong>
                      </a-table-summary-cell>
                    </a-table-summary-row>
                  </a-table-summary>
                </template>
              </BasicTable>
            </a-card>
          </a-tab-pane>
        </a-tabs>
      </a-spin>
    </div>
  </BasicDrawer>
</template>

<script lang="ts" setup>
  import { computed, ref, unref } from 'vue';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { BasicTable, useTable } from '/@/components/Table';
  import { Description } from '/@/components/Description';
  import { getSupplierInfo, getSupplierPaycontracts, SupplierModel } from '/@/api/project/supplier';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { formatToDateTime } from '/@/utils/dateUtil';
  import {
    ShopOutlined,
    UserOutlined,
    PhoneOutlined,
    SortAscendingOutlined,
    UserAddOutlined,
    CalendarOutlined,
    FileTextOutlined,
    CloseOutlined,
    InfoCircleOutlined,
    BarChartOutlined,
    TableOutlined,
    ClockCircleOutlined,
    EditOutlined,
  } from '@ant-design/icons-vue';

  const { createMessage } = useMessage();
  const emit = defineEmits(['register', 'reload']);
  const supplierId = ref('');
  const supplierInfo = ref<Partial<SupplierModel>>({});
  const loading = ref(false);
  const activeTab = ref('basic');
  const contractData = ref<any[]>([]);

  // 格式化日期时间
  function formatDateTime(dateTime: any) {
    return dateTime ? formatToDateTime(dateTime) : '-';
  }

  // 计算统计数据
  const totalCount = computed(() => contractData.value.length);

  const totalAmount = computed(() => {
    return contractData.value.reduce((sum, contract) => {
      return sum + (contract.amount || 0);
    }, 0);
  });

  const signedCount = computed(() => {
    return contractData.value.filter(contract => contract.status === '已签').length;
  });

  const signedAmount = computed(() => {
    return contractData.value
      .filter(contract => contract.status === '已签')
      .reduce((sum, contract) => {
        return sum + (contract.amount || 0);
      }, 0);
  });

  // 基本信息字段配置
  const basicSchema = computed(() => {
    console.log('DetailDrawer supplierInfo:', supplierInfo.value);
    return [
      {
        field: 'name',
        label: '供应商名称',
      },
      {
        field: 'linkman',
        label: '联系人',
      },
      {
        field: 'telephone',
        label: '联系电话',
      },
      {
        field: 'remark',
        label: '备注',
      },
      {
        field: 'sortCode',
        label: '排序码',
      },
    ];
  });

  // 审计信息字段配置
  const auditSchema = computed(() => {
    return [
      {
        field: 'createdBy',
        label: '创建人',
      },
      {
        field: 'createdAt',
        label: '创建时间',
        render: (val: any) => {
          return val ? formatToDateTime(val) : '-';
        },
      },
      {
        field: 'lastUpdatedBy',
        label: '最后修改人',
      },
      {
        field: 'lastUpdatedAt',
        label: '最后修改时间',
        render: (val: any) => {
          return val ? formatToDateTime(val) : '-';
        },
      },
    ];
  });

  // 采购合同状态颜色映射
  function getStatusColor(status: string) {
    const colorMap = {
      草稿: 'default',
      待签: 'processing',
      已签: 'success',
      已废弃: 'error',
      已完成: 'success',
    };
    return colorMap[status] || 'default';
  }

  // 采购合同表格列配置
  const columns = [
    {
      title: '合同名称',
      dataIndex: 'name',
      width: 200,
    },
    {
      title: '采购合同编号',
      dataIndex: 'cno',
      width: 150,
    },
    {
      title: '合同金额',
      dataIndex: 'amount',
      key: 'amount',
      width: 120,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
    },
    {
      title: '项目经理',
      dataIndex: 'ownName',
      width: 120,
    },
    {
      title: '签订日期',
      dataIndex: 'signDate',
      width: 120,
      customRender: ({ text }) => {
        return text ? formatToDateTime(text).split(' ')[0] : '-';
      },
    },
    {
      title: '预计签订日期',
      dataIndex: 'estSignDate',
      width: 120,
      customRender: ({ text }) => {
        return text ? formatToDateTime(text).split(' ')[0] : '-';
      },
    },
  ];

  // 注册采购合同表格
  const [registerTable, { reload: reloadTable }] = useTable({
    title: '采购合同列表',
    api: params => getSupplierPaycontracts(supplierId.value, params),
    columns,
    formConfig: {
      labelWidth: 80,
      showAdvancedButton: false,
      schemas: [
        {
          field: 'name',
          label: '合同名称',
          component: 'Input',
          colProps: { span: 4 },
        },
        {
          field: 'cNo',
          label: '合同编号',
          component: 'Input',
          colProps: { span: 4 },
        },
        {
          field: 'status',
          label: '状态',
          component: 'Select',
          componentProps: {
            options: [
              { fullName: '全部', id: '' },
              { fullName: '草稿', id: '草稿' },
              { fullName: '待签', id: '待签' },
              { fullName: '已签', id: '已签' },
              { fullName: '已废弃', id: '已废弃' },
              { fullName: '已完成', id: '已完成' },
            ],
          },
          colProps: { span: 4 },
        },
      ],
    },
    useSearchForm: true,
    showTableSetting: false,
    bordered: true,
    showIndexColumn: false,
    canResize: false,
    pagination: {
      pageSize: 10,
    },
    afterFetch: data => {
      // 更新合同数据用于统计计算
      contractData.value = data || [];
      console.log('更新合同数据:', contractData.value);
      return data;
    },
  });

  // 注册抽屉
  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async data => {
    setDrawerProps({ confirmLoading: false });
    supplierId.value = data.id;

    if (unref(supplierId)) {
      await loadSupplierInfo();
      // 表格数据会在 afterFetch 回调中自动更新 contractData
    }
  });

  // 获取标题
  const getTitle = computed(() => {
    return '供应商详情';
  });

  // 加载供应商信息
  async function loadSupplierInfo() {
    try {
      loading.value = true;
      const result: any = await getSupplierInfo(supplierId.value);
      console.log('loadSupplierInfo result:', result);

      // 根据API返回结构处理数据
      if (result && result.data) {
        supplierInfo.value = result.data;
      } else {
        supplierInfo.value = result;
      }

      console.log('设置后的 supplierInfo:', supplierInfo.value);
    } catch (error) {
      console.error('获取供应商信息失败:', error);
      createMessage.error('获取供应商信息失败');
    } finally {
      loading.value = false;
    }
  }

  // 关闭抽屉
  function handleClose() {
    closeDrawer();
  }
</script>

<style lang="less" scoped>
  // 响应式设计
  @media (max-width: 1200px) {
    .supplier-detail-drawer {
      :deep(.ant-drawer) {
        width: 95% !important;
      }
    }

    .supplier-detail-container {
      padding: 16px;
    }

    .overview-content {
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 16px;
    }

    .compact-stats {
      .stats-row {
        gap: 12px;

        .stat-compact {
          min-width: 100px;

          .stat-label {
            font-size: 13px;
          }

          .stat-value {
            font-size: 15px;
          }
        }
      }
    }

    .tab-pane-content {
      padding: 16px;
    }
  }

  @media (max-width: 768px) {
    .supplier-detail-tabs {
      :deep(.ant-tabs-nav) {
        padding: 12px 16px 0;

        .ant-tabs-tab {
          padding: 8px 12px;
          margin-right: 4px;

          .tab-title {
            font-size: 12px;

            .tab-icon {
              font-size: 14px;
            }
          }
        }
      }
    }

    .tab-pane-content {
      padding: 12px;
    }

    .overview-content {
      grid-template-columns: 1fr;
      gap: 12px;
    }

    .compact-stats {
      padding: 12px 16px;

      .stats-row {
        flex-direction: column;
        gap: 8px;

        .stat-compact {
          justify-content: space-between;
          min-width: auto;
          width: 100%;
          padding: 8px 0;
          border-bottom: 1px solid #e8eaec;

          &:last-child {
            border-bottom: none;
          }

          .stat-label {
            font-size: 12px;
          }

          .stat-value {
            font-size: 14px;
          }
        }
      }
    }

    .card-title {
      .title-icon {
        font-size: 16px;
      }
    }

    .audit-section {
      .audit-content {
        padding: 16px;

        .audit-grid {
          grid-template-columns: 1fr;
          gap: 12px;

          .audit-item {
            padding: 10px 12px;

            .audit-label {
              font-size: 11px;

              .item-icon {
                font-size: 12px;
              }
            }

            .audit-value {
              font-size: 13px;
            }
          }
        }
      }
    }
  }
  .supplier-detail-drawer {
    :deep(.ant-drawer-body) {
      padding: 0;
      background: #f5f7fa;
    }

    :deep(.ant-drawer-header) {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-bottom: none;

      .ant-drawer-title {
        color: #fff;
        font-weight: 600;
        font-size: 16px;
      }

      .ant-drawer-close {
        color: rgba(255, 255, 255, 0.8);

        &:hover {
          color: #fff;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 4px;
        }
      }
    }

    :deep(.ant-drawer-footer) {
      background: #fff;
      border-top: 1px solid #e8eaec;
      padding: 16px 24px;
    }
  }

  .supplier-detail-container {
    height: 100%;
    padding: 24px;
    overflow-y: auto;
  }

  .overview-card,
  .detail-card,
  .stats-card,
  .table-card {
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid #e8eaec;

    :deep(.ant-card-head) {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-bottom: 1px solid #e8eaec;
      border-radius: 12px 12px 0 0;
      padding: 16px 24px;

      .ant-card-head-title {
        padding: 0;
        font-size: 18px;
        font-weight: 600;
      }
    }

    :deep(.ant-card-body) {
      padding: 24px;
    }
  }

  .overview-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;

    .overview-item {
      &.full-width {
        grid-column: 1 / -1;
      }

      .overview-label {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
        font-weight: 500;

        .label-icon {
          margin-right: 6px;
          font-size: 16px;
          color: #1890ff;
        }
      }

      .overview-value {
        font-size: 16px;
        font-weight: 600;
        color: #2c3e50;
        word-break: break-all;

        &.description {
          font-size: 14px;
          line-height: 1.6;
          color: #595959;
          background: #fafafa;
          padding: 12px;
          border-radius: 6px;
          border-left: 3px solid #1890ff;
          font-weight: 400;
        }
      }
    }
  }

  .card-title {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #2c3e50;

    .title-icon {
      font-size: 18px;
      color: #1890ff;
    }
  }

  .tag-icon {
    margin-right: 4px;
  }

  .supplier-detail-tabs {
    height: calc(100% - 200px);

    :deep(.ant-tabs-nav) {
      background: #fff;
      margin: 0;
      padding: 16px 24px 0;
      border-bottom: 1px solid #e8eaec;
      border-radius: 12px 12px 0 0;

      .ant-tabs-tab {
        border: 1px solid #e8eaec;
        border-radius: 8px 8px 0 0;
        margin-right: 8px;
        padding: 12px 20px;
        background: #fafbfc;
        transition: all 0.3s ease;

        &:hover {
          background: #f0f2f5;
          border-color: #d9d9d9;
        }

        &.ant-tabs-tab-active {
          background: #fff;
          border-color: #1890ff;
          border-bottom-color: #fff;

          .tab-title {
            color: #1890ff;
            font-weight: 600;
          }
        }
      }
    }

    :deep(.ant-tabs-content-holder) {
      background: #f5f7fa;
      padding: 0;
      height: calc(100% - 60px);
      overflow-y: auto;
    }

    :deep(.ant-tabs-tabpane) {
      height: 100%;
      padding: 0;
    }
  }

  .tab-pane-content {
    padding: 24px;
    height: 100%;
    overflow-y: auto;
  }

  .tab-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #666;
    transition: color 0.3s ease;

    .tab-icon {
      font-size: 16px;
    }
  }

  .compact-stats {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    border: 1px solid #e8eaec;
    padding: 16px 20px;

    .stats-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: 16px;

      .stat-compact {
        display: flex;
        align-items: center;
        gap: 8px;
        min-width: 120px;

        .stat-label {
          font-size: 14px;
          color: #666;
          font-weight: 500;
        }

        .stat-value {
          font-size: 16px;
          font-weight: 700;

          &.total {
            color: #1890ff;
          }

          &.signed {
            color: #52c41a;
          }

          &.amount {
            color: #fa8c16;
          }

          &.amount-signed {
            color: #52c41a;
          }
        }
      }
    }
  }

  .enhanced-description {
    :deep(.ant-descriptions-item-label) {
      font-weight: 600;
      color: #2c3e50;
      background: #f8f9fa;
    }

    :deep(.ant-descriptions-item-content) {
      color: #595959;
    }
  }

  .audit-section {
    margin-top: 24px;
    background: #fff;
    border-radius: 12px;
    border: 1px solid #e8eaec;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    .audit-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      padding: 12px 20px;
      display: flex;
      align-items: center;
      gap: 8px;

      .audit-icon {
        color: #fff;
        font-size: 16px;
      }

      .audit-title {
        color: #fff;
        font-weight: 600;
        font-size: 14px;
      }
    }

    .audit-content {
      padding: 20px;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);

      .audit-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;

        .audit-item {
          background: #fff;
          border-radius: 8px;
          padding: 12px 16px;
          border: 1px solid #e8eaec;
          transition: all 0.3s ease;

          &:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transform: translateY(-1px);
          }

          .audit-label {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
            color: #666;
            margin-bottom: 6px;
            font-weight: 500;

            .item-icon {
              font-size: 14px;
              color: #1890ff;
            }
          }

          .audit-value {
            font-size: 14px;
            color: #2c3e50;
            font-weight: 600;
            word-break: break-all;
          }
        }
      }
    }
  }

  .enhanced-table {
    :deep(.ant-table-thead > tr > th) {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      font-weight: 600;
      color: #2c3e50;
    }

    .amount-text {
      font-weight: 600;
      color: #fa8c16;
    }

    .summary-row {
      background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);

      .total-amount {
        color: #fa8c16;
        font-size: 16px;
      }

      .summary-text {
        color: #1890ff;
      }
    }
  }

  .mb-4 {
    margin-bottom: 24px;
  }

  // 滚动条美化
  .supplier-detail-container,
  .tab-pane-content {
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }

  // 标签样式
  :deep(.ant-tag) {
    border-radius: 6px;
    padding: 4px 12px;
    font-weight: 500;
    border: none;
  }

  // 徽章样式
  :deep(.ant-badge) {
    .ant-badge-count {
      background: #52c41a;
      border-radius: 10px;
      font-size: 12px;
      min-width: 20px;
      height: 20px;
      line-height: 20px;
    }
  }
</style>
