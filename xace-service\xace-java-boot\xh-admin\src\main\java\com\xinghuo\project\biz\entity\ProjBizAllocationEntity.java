package com.xinghuo.project.biz.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 项目业务分部分配实体
 * 支持项目/商机/合同/收付款的统一分部分配管理
 * 
 * <AUTHOR>
 * @version V3.0
 * @copyright 易趋集团
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("proj_biz_allocation")
public class ProjBizAllocationEntity extends BaseEntityV2.CUDBaseEntityV2<String> {

    // ==================== 业务关联字段 ====================
    
    /**
     * 项目基础ID（如果有的话）
     */
    @TableField("PROJ_BASE_ID")
    private String projBaseId;
    
    /**
     * 商机ID
     */
    @TableField("OPPORTUNITY_ID")
    private String opportunityId;

    /**
     * 合同ID
     */
    @TableField("CONTRACT_ID")
    private String contractId;

    /**
     * 主业务ID（冗余字段，便于查询）
     */
    @TableField("BUSINESS_ID")
    private String businessId;

    /**
     * 业务类型（1-商机 2-合同 3-收款 4-付款）
     */
    @TableField("BUSINESS_TYPE")
    private Integer businessType;

    // ==================== 4个分部的营收分配 ====================
    
    /**
     * 一部营收分配
     */
    @TableField("YB_AMOUNT")
    private BigDecimal ybAmount;

    /**
     * 二部营收分配
     */
    @TableField("EB_AMOUNT")
    private BigDecimal ebAmount;

    /**
     * 交付营收分配
     */
    @TableField("JF_AMOUNT")
    private BigDecimal jfAmount;

    /**
     * 综合营收分配
     */
    @TableField("OTHER_AMOUNT")
    private BigDecimal otherAmount;

    // ==================== 4个分部的外采分配 ====================
    
    /**
     * 一部外采分配
     */
    @TableField("OUT_YB_AMOUNT")
    private BigDecimal outYbAmount;

    /**
     * 二部外采分配
     */
    @TableField("OUT_EB_AMOUNT")
    private BigDecimal outEbAmount;

    /**
     * 交付外采分配
     */
    @TableField("OUT_JF_AMOUNT")
    private BigDecimal outJfAmount;

    /**
     * 综合外采分配
     */
    @TableField("OUT_OTHER_AMOUNT")
    private BigDecimal outOtherAmount;

    /**
     * 分配备注
     */
    @TableField("F_REMARKS")
    private String remarks;

    // ==================== 业务方法 ====================

    /**
     * 获取分部营收分配总额
     */
    public BigDecimal getTotalAmount() {
        return (ybAmount != null ? ybAmount : BigDecimal.ZERO)
            .add(ebAmount != null ? ebAmount : BigDecimal.ZERO)
            .add(jfAmount != null ? jfAmount : BigDecimal.ZERO)
            .add(otherAmount != null ? otherAmount : BigDecimal.ZERO);
    }

    /**
     * 获取分部外采分配总额
     */
    public BigDecimal getTotalOutAmount() {
        return (outYbAmount != null ? outYbAmount : BigDecimal.ZERO)
            .add(outEbAmount != null ? outEbAmount : BigDecimal.ZERO)
            .add(outJfAmount != null ? outJfAmount : BigDecimal.ZERO)
            .add(outOtherAmount != null ? outOtherAmount : BigDecimal.ZERO);
    }

    /**
     * 获取指定分部的营收分配
     */
    public BigDecimal getAmountByDept(String deptCode) {
        switch (deptCode.toUpperCase()) {
            case "YB": return ybAmount != null ? ybAmount : BigDecimal.ZERO;
            case "EB": return ebAmount != null ? ebAmount : BigDecimal.ZERO;
            case "JF": return jfAmount != null ? jfAmount : BigDecimal.ZERO;
            case "OTHER": case "ZH": return otherAmount != null ? otherAmount : BigDecimal.ZERO;
            default: return BigDecimal.ZERO;
        }
    }

    /**
     * 获取指定分部的外采分配
     */
    public BigDecimal getOutAmountByDept(String deptCode) {
        switch (deptCode.toUpperCase()) {
            case "YB": return outYbAmount != null ? outYbAmount : BigDecimal.ZERO;
            case "EB": return outEbAmount != null ? outEbAmount : BigDecimal.ZERO;
            case "JF": return outJfAmount != null ? outJfAmount : BigDecimal.ZERO;
            case "OTHER": case "ZH": return outOtherAmount != null ? outOtherAmount : BigDecimal.ZERO;
            default: return BigDecimal.ZERO;
        }
    }

    /**
     * 设置指定分部的营收分配
     */
    public void setAmountByDept(String deptCode, BigDecimal amount) {
        switch (deptCode.toUpperCase()) {
            case "YB": this.ybAmount = amount; break;
            case "EB": this.ebAmount = amount; break;
            case "JF": this.jfAmount = amount; break;
            case "OTHER": case "ZH": this.otherAmount = amount; break;
        }
    }

    /**
     * 设置指定分部的外采分配
     */
    public void setOutAmountByDept(String deptCode, BigDecimal amount) {
        switch (deptCode.toUpperCase()) {
            case "YB": this.outYbAmount = amount; break;
            case "EB": this.outEbAmount = amount; break;
            case "JF": this.outJfAmount = amount; break;
            case "OTHER": case "ZH": this.outOtherAmount = amount; break;
        }
    }

    /**
     * 获取交付分部的完整信息
     */
    public DeptAllocationInfo getJfDeptInfo() {
        DeptAllocationInfo info = new DeptAllocationInfo();
        info.setDeptCode("JF");
        info.setDeptName("交付");
        info.setAmount(this.jfAmount);
        info.setOutAmount(this.outJfAmount);
        info.setBusinessType(this.getBusinessTypeName());
        return info;
    }

    /**
     * 获取所有分部的分配信息
     */
    public java.util.List<DeptAllocationInfo> getAllDeptInfo() {
        java.util.List<DeptAllocationInfo> list = new java.util.ArrayList<>();
        
        list.add(createDeptInfo("YB", "一部", ybAmount, outYbAmount));
        list.add(createDeptInfo("EB", "二部", ebAmount, outEbAmount));
        list.add(createDeptInfo("JF", "交付", jfAmount, outJfAmount));
        list.add(createDeptInfo("OTHER", "综合", otherAmount, outOtherAmount));
        
        return list;
    }

    private DeptAllocationInfo createDeptInfo(String code, String name, BigDecimal amount, BigDecimal outAmount) {
        DeptAllocationInfo info = new DeptAllocationInfo();
        info.setDeptCode(code);
        info.setDeptName(name);
        info.setAmount(amount != null ? amount : BigDecimal.ZERO);
        info.setOutAmount(outAmount != null ? outAmount : BigDecimal.ZERO);
        info.setBusinessType(this.getBusinessTypeName());
        return info;
    }

    /**
     * 获取业务类型名称
     */
    public String getBusinessTypeName() {
        switch (businessType) {
            case 1: return "商机";
            case 2: return "合同";
            case 3: return "收款";
            case 4: return "付款";
            default: return "未知";
        }
    }

    /**
     * 获取主要业务关联ID
     */
    public String getPrimaryBusinessId() {
        if (opportunityId != null) return opportunityId;
        if (contractId != null) return contractId;
        return businessId;
    }

    /**
     * 判断是否为商机分配
     */
    public boolean isOpportunityAllocation() {
        return businessType != null && businessType == 1;
    }

    /**
     * 判断是否为合同分配
     */
    public boolean isContractAllocation() {
        return businessType != null && businessType == 2;
    }

    /**
     * 判断是否为收款分配
     */
    public boolean isReceivableAllocation() {
        return businessType != null && businessType == 3;
    }

    /**
     * 判断是否为付款分配
     */
    public boolean isPaymentAllocation() {
        return businessType != null && businessType == 4;
    }

    /**
     * 验证分配数据有效性
     */
    public boolean validate() {
        // 至少要有一个业务关联ID
        boolean hasBusinessId = opportunityId != null || contractId != null || businessId != null;
        
        // 业务类型必须有效
        boolean validBusinessType = businessType != null && businessType >= 1 && businessType <= 4;
        
        // 至少要有一个分配金额大于0
        boolean hasAllocation = getTotalAmount().compareTo(BigDecimal.ZERO) > 0 ||
                               getTotalOutAmount().compareTo(BigDecimal.ZERO) > 0;
        
        return hasBusinessId && validBusinessType && hasAllocation;
    }

    /**
     * 分部分配信息DTO
     */
    @Data
    public static class DeptAllocationInfo {
        private String deptCode;         // 分部编码
        private String deptName;         // 分部名称
        private BigDecimal amount;       // 营收分配
        private BigDecimal outAmount;    // 外采分配
        private String businessType;     // 业务类型
        
        public BigDecimal getTotalAmount() {
            return (amount != null ? amount : BigDecimal.ZERO)
                .add(outAmount != null ? outAmount : BigDecimal.ZERO);
        }
    }
}