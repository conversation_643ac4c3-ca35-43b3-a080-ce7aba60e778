package com.xinghuo.project.biz.service.impl;

import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.project.biz.entity.ProjBizAllocationEntity;
import com.xinghuo.project.biz.mapper.ProjBizAllocationMapper;
import com.xinghuo.project.biz.service.ProjBizAllocationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 项目业务分部分配 服务实现类
 * 
 * <AUTHOR>
 * @version V3.0
 * @copyright 易趋集团
 * @date 2024-01-01
 */
@Slf4j
@Service
public class ProjBizAllocationServiceImpl extends BaseServiceImpl<ProjBizAllocationMapper, ProjBizAllocationEntity> 
        implements ProjBizAllocationService {

    @Override
    public ProjBizAllocationEntity getByBusinessIdAndType(String businessId, Integer businessType) {
        return baseMapper.selectByBusinessIdAndType(businessId, businessType);
    }

    @Override
    public ProjBizAllocationEntity getByOpportunityId(String opportunityId) {
        return baseMapper.selectByOpportunityId(opportunityId);
    }

    @Override
    public ProjBizAllocationEntity getByContractId(String contractId) {
        return baseMapper.selectByContractId(contractId);
    }

    @Override
    public List<ProjBizAllocationEntity> getByProjBaseId(String projBaseId) {
        return baseMapper.selectByProjBaseId(projBaseId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateAllocation(ProjBizAllocationEntity allocation) {
        if (allocation == null || !allocation.validate()) {
            log.warn("分配信息验证失败: {}", allocation);
            return false;
        }

        try {
            // 检查是否已存在
            ProjBizAllocationEntity existing = getByBusinessIdAndType(
                allocation.getBusinessId(), allocation.getBusinessType());
            
            if (existing != null) {
                // 更新现有记录
                allocation.setId(existing.getId());
                return updateById(allocation);
            } else {
                // 创建新记录
                if (allocation.getId() == null) {
                    allocation.setId(RandomUtil.snowId());
                }
                return save(allocation);
            }
        } catch (Exception e) {
            log.error("保存分配信息失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createOpportunityAllocation(String opportunityId, String projBaseId,
                                             BigDecimal ybAmount, BigDecimal ebAmount,
                                             BigDecimal jfAmount, BigDecimal otherAmount,
                                             BigDecimal outYbAmount, BigDecimal outEbAmount,
                                             BigDecimal outJfAmount, BigDecimal outOtherAmount) {
        ProjBizAllocationEntity allocation = new ProjBizAllocationEntity();
        allocation.setId(RandomUtil.snowId());
        allocation.setBusinessId(opportunityId);
        allocation.setBusinessType(1);
        allocation.setOpportunityId(opportunityId);
        allocation.setProjBaseId(projBaseId);
        
        allocation.setYbAmount(ybAmount);
        allocation.setEbAmount(ebAmount);
        allocation.setJfAmount(jfAmount);
        allocation.setOtherAmount(otherAmount);
        
        allocation.setOutYbAmount(outYbAmount);
        allocation.setOutEbAmount(outEbAmount);
        allocation.setOutJfAmount(outJfAmount);
        allocation.setOutOtherAmount(outOtherAmount);
        
        return save(allocation);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createContractAllocation(String contractId, String opportunityId, String projBaseId,
                                          BigDecimal ybAmount, BigDecimal ebAmount,
                                          BigDecimal jfAmount, BigDecimal otherAmount,
                                          BigDecimal outYbAmount, BigDecimal outEbAmount,
                                          BigDecimal outJfAmount, BigDecimal outOtherAmount) {
        ProjBizAllocationEntity allocation = new ProjBizAllocationEntity();
        allocation.setId(RandomUtil.snowId());
        allocation.setBusinessId(contractId);
        allocation.setBusinessType(2);
        allocation.setContractId(contractId);
        allocation.setOpportunityId(opportunityId);
        allocation.setProjBaseId(projBaseId);
        
        allocation.setYbAmount(ybAmount);
        allocation.setEbAmount(ebAmount);
        allocation.setJfAmount(jfAmount);
        allocation.setOtherAmount(otherAmount);
        
        allocation.setOutYbAmount(outYbAmount);
        allocation.setOutEbAmount(outEbAmount);
        allocation.setOutJfAmount(outJfAmount);
        allocation.setOutOtherAmount(outOtherAmount);
        
        return save(allocation);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createReceivableAllocation(String receivableId, String contractId,
                                            BigDecimal ybAmount, BigDecimal ebAmount,
                                            BigDecimal jfAmount, BigDecimal otherAmount) {
        ProjBizAllocationEntity allocation = new ProjBizAllocationEntity();
        allocation.setId(RandomUtil.snowId());
        allocation.setBusinessId(receivableId);
        allocation.setBusinessType(3);
        allocation.setContractId(contractId);
        
        allocation.setYbAmount(ybAmount);
        allocation.setEbAmount(ebAmount);
        allocation.setJfAmount(jfAmount);
        allocation.setOtherAmount(otherAmount);
        
        return save(allocation);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createPaymentAllocation(String paymentId, String contractId,
                                         BigDecimal outYbAmount, BigDecimal outEbAmount,
                                         BigDecimal outJfAmount, BigDecimal outOtherAmount) {
        ProjBizAllocationEntity allocation = new ProjBizAllocationEntity();
        allocation.setId(RandomUtil.snowId());
        allocation.setBusinessId(paymentId);
        allocation.setBusinessType(4);
        allocation.setContractId(contractId);
        
        allocation.setOutYbAmount(outYbAmount);
        allocation.setOutEbAmount(outEbAmount);
        allocation.setOutJfAmount(outJfAmount);
        allocation.setOutOtherAmount(outOtherAmount);
        
        return save(allocation);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDeptAllocation(String allocationId, String deptCode,
                                      BigDecimal amount, BigDecimal outAmount) {
        ProjBizAllocationEntity allocation = getById(allocationId);
        if (allocation == null) {
            return false;
        }
        
        allocation.setAmountByDept(deptCode, amount);
        allocation.setOutAmountByDept(deptCode, outAmount);
        
        return updateById(allocation);
    }

    @Override
    public List<Map<String, Object>> getAllocationSummaryByProjId(String projBaseId) {
        return baseMapper.selectAllocationSummaryByProjId(projBaseId);
    }

    @Override
    public List<Map<String, Object>> getDeptSummaryByBusinessType(Integer businessType) {
        return baseMapper.selectDeptSummaryByBusinessType(businessType);
    }

    @Override
    public List<Map<String, Object>> getJfDeptSummary() {
        return baseMapper.selectJfDeptSummary();
    }

    @Override
    public Map<String, BigDecimal> getDeptTotalAmount(String deptCode, Integer businessType) {
        return baseMapper.selectDeptTotalAmount(deptCode, businessType);
    }

    @Override
    public boolean existsByBusinessIdAndType(String businessId, Integer businessType) {
        return baseMapper.countByBusinessIdAndType(businessId, businessType) > 0;
    }

    @Override
    public List<ProjBizAllocationEntity> getByBusinessIds(List<String> businessIds, Integer businessType) {
        return baseMapper.selectByBusinessIds(businessIds, businessType);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean copyOpportunityToContract(String opportunityId, String contractId) {
        try {
            ProjBizAllocationEntity oppAllocation = getByOpportunityId(opportunityId);
            if (oppAllocation == null) {
                log.warn("商机分配信息不存在: {}", opportunityId);
                return false;
            }
            
            // 检查合同分配是否已存在
            if (existsByBusinessIdAndType(contractId, 2)) {
                log.warn("合同分配信息已存在: {}", contractId);
                return false;
            }
            
            // 复制商机分配到合同
            ProjBizAllocationEntity contractAllocation = BeanCopierUtils.copy(oppAllocation, ProjBizAllocationEntity.class);
            contractAllocation.setId(RandomUtil.snowId());
            contractAllocation.setBusinessId(contractId);
            contractAllocation.setBusinessType(2);
            contractAllocation.setContractId(contractId);
            
            return save(contractAllocation);
        } catch (Exception e) {
            log.error("复制商机分配到合同失败", e);
            return false;
        }
    }

    @Override
    public boolean validateAllocationConsistency(String businessId, Integer businessType) {
        ProjBizAllocationEntity allocation = getByBusinessIdAndType(businessId, businessType);
        return allocation != null && allocation.validate();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByBusinessIdAndType(String businessId, Integer businessType) {
        return baseMapper.deleteByBusinessIdAndType(businessId, businessType) > 0;
    }

    @Override
    public BigDecimal sumAmountByDeptAndType(String deptCode, Integer businessType, boolean isOutAmount) {
        Map<String, BigDecimal> result = getDeptTotalAmount(deptCode, businessType);
        if (result == null) {
            return BigDecimal.ZERO;
        }
        
        String key = isOutAmount ? "total_out_amount" : "total_amount";
        return result.getOrDefault(key, BigDecimal.ZERO);
    }

    @Override
    public List<ProjBizAllocationEntity.DeptAllocationInfo> getJfAllocationAcrossAllTypes() {
        List<Map<String, Object>> summaryList = getJfDeptSummary();
        List<ProjBizAllocationEntity.DeptAllocationInfo> result = new ArrayList<>();
        
        for (Map<String, Object> summary : summaryList) {
            ProjBizAllocationEntity.DeptAllocationInfo info = new ProjBizAllocationEntity.DeptAllocationInfo();
            info.setDeptCode("JF");
            info.setDeptName("交付");
            
            Integer businessType = (Integer) summary.get("BUSINESS_TYPE");
            String businessTypeName = getBusinessTypeName(businessType);
            info.setBusinessType(businessTypeName);
            
            BigDecimal amount = (BigDecimal) summary.get("jf_total_amount");
            BigDecimal outAmount = (BigDecimal) summary.get("jf_total_out_amount");
            info.setAmount(amount != null ? amount : BigDecimal.ZERO);
            info.setOutAmount(outAmount != null ? outAmount : BigDecimal.ZERO);
            
            result.add(info);
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateAllocations(List<ProjBizAllocationEntity> allocations) {
        if (allocations == null || allocations.isEmpty()) {
            return true;
        }
        
        try {
            return updateBatchById(allocations);
        } catch (Exception e) {
            log.error("批量更新分配记录失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean syncAllocationToMainBusiness(String businessId, Integer businessType) {
        // 这里需要根据具体业务表的结构来实现
        // 比如将分配表的汇总数据同步到商机表、合同表等主业务表中
        log.info("同步分配金额到主业务表: businessId={}, businessType={}", businessId, businessType);
        
        ProjBizAllocationEntity allocation = getByBusinessIdAndType(businessId, businessType);
        if (allocation == null) {
            return false;
        }
        
        // TODO: 根据实际业务需求实现同步逻辑
        // 例如：更新商机表的总金额、分部分配金额等字段
        
        return true;
    }

    /**
     * 获取业务类型名称
     */
    private String getBusinessTypeName(Integer businessType) {
        switch (businessType) {
            case 1: return "商机";
            case 2: return "合同";
            case 3: return "收款";
            case 4: return "付款";
            default: return "未知";
        }
    }
}