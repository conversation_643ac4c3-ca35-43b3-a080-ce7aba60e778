<template>
  <div class="template-schedule-page p-4">
    <!-- 页面标题 -->
    <div class="page-header mb-4">
      <h2 class="text-xl font-semibold mb-2">主计划管理</h2>
      <p class="text-gray-600">配置项目模板的主计划信息，设置项目整体计划和里程碑</p>
    </div>

    <a-spin :spinning="loading">
      <!-- 工具栏 -->
      <div class="toolbar mb-4 flex justify-between items-center">
        <div class="search-area flex items-center space-x-4">
          <a-input-search v-model:value="searchText" placeholder="搜索计划名称" style="width: 300px" @search="handleSearch" />
          <a-select v-model:value="typeFilter" placeholder="筛选类型" style="width: 150px" allow-clear @change="handleTypeFilter">
            <a-select-option value="milestone">里程碑</a-select-option>
            <a-select-option value="task">任务</a-select-option>
            <a-select-option value="phase">阶段</a-select-option>
          </a-select>
          <a-select v-model:value="statusFilter" placeholder="筛选状态" style="width: 150px" allow-clear @change="handleStatusFilter">
            <a-select-option value="active">启用</a-select-option>
            <a-select-option value="inactive">禁用</a-select-option>
          </a-select>
        </div>
        <div class="actions">
          <a-space>
            <a-button type="primary" @click="handleAdd">
              <template #icon><PlusOutlined /></template>
              添加计划项
            </a-button>
            <a-button @click="handleGenerateFromPhases">
              <template #icon><BranchesOutlined /></template>
              从阶段生成
            </a-button>
            <a-button @click="handleGanttView">
              <template #icon><BarChartOutlined /></template>
              甘特图视图
            </a-button>
            <a-button @click="handleExport">
              <template #icon><ExportOutlined /></template>
              导出
            </a-button>
            <a-button @click="handleRefresh">
              <template #icon><ReloadOutlined /></template>
              刷新
            </a-button>
          </a-space>
        </div>
      </div>

      <!-- 计划概览卡片 -->
      <div class="schedule-overview grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="stat-card bg-blue-50 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-blue-600">{{ scheduleStats.totalItems }}</div>
          <div class="text-sm text-blue-600">总计划项</div>
        </div>
        <div class="stat-card bg-green-50 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-green-600">{{ scheduleStats.milestones }}</div>
          <div class="text-sm text-green-600">里程碑</div>
        </div>
        <div class="stat-card bg-orange-50 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-orange-600">{{ scheduleStats.totalDuration }}</div>
          <div class="text-sm text-orange-600">总工期(天)</div>
        </div>
        <div class="stat-card bg-purple-50 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-purple-600">{{ scheduleStats.criticalPath }}</div>
          <div class="text-sm text-purple-600">关键路径</div>
        </div>
      </div>

      <!-- 主计划表格 -->
      <BasicTable @register="registerTable">
        <!-- 自定义列 -->
        <template #itemName="{ record }">
          <div class="flex items-center">
            <div class="item-icon mr-2">
              <a-avatar :style="{ backgroundColor: getTypeColor(record.itemType) }" :size="24">
                <template #icon>
                  <component :is="getTypeIcon(record.itemType)" />
                </template>
              </a-avatar>
            </div>
            <div>
              <div class="font-medium">{{ record.itemName }}</div>
              <div class="text-sm text-gray-500">{{ record.itemCode }}</div>
            </div>
          </div>
        </template>

        <template #itemType="{ record }">
          <a-tag :color="getTypeColor(record.itemType)">
            {{ getTypeText(record.itemType) }}
          </a-tag>
        </template>

        <template #status="{ record }">
          <a-tag :color="record.status === 'active' ? 'green' : 'red'">
            {{ record.status === 'active' ? '启用' : '禁用' }}
          </a-tag>
        </template>

        <template #timeRange="{ record }">
          <div class="text-sm">
            <div>开始：{{ formatDate(record.planStartDate) }}</div>
            <div>结束：{{ formatDate(record.planEndDate) }}</div>
          </div>
        </template>

        <template #dependencies="{ record }">
          <div class="dependencies">
            <a-tag v-for="dep in record.dependencies" :key="dep.id" size="small" class="mb-1">
              {{ dep.name }}
            </a-tag>
            <span v-if="!record.dependencies?.length" class="text-gray-400">-</span>
          </div>
        </template>

        <template #progress="{ record }">
          <div class="flex items-center" v-if="record.itemType !== 'milestone'">
            <a-progress
              :percent="record.progress || 0"
              :show-info="false"
              size="small"
              class="flex-1 mr-2"
              :stroke-color="getProgressColor(record.progress || 0)" />
            <span class="text-sm">{{ record.progress || 0 }}%</span>
          </div>
          <span v-else class="text-gray-400">-</span>
        </template>

        <template #action="{ record }">
          <TableAction
            :actions="[
              {
                icon: 'ant-design:edit-outlined',
                label: '编辑',
                onClick: handleEdit.bind(null, record),
              },
              {
                icon: 'ant-design:copy-outlined',
                label: '复制',
                onClick: handleCopy.bind(null, record),
              },
              {
                icon: 'ant-design:apartment-outlined',
                label: '依赖关系',
                onClick: handleDependencies.bind(null, record),
              },
              {
                icon: 'ant-design:delete-outlined',
                label: '删除',
                color: 'error',
                popConfirm: {
                  title: '是否确认删除？',
                  placement: 'left',
                  confirm: handleDelete.bind(null, record),
                },
              },
            ]" />
        </template>
      </BasicTable>
    </a-spin>

    <!-- 添加/编辑弹窗 -->
    <BasicDrawer @register="registerDrawer" :title="drawerTitle" width="50%" @ok="handleSubmit">
      <BasicForm @register="registerForm" />
    </BasicDrawer>

    <!-- 依赖关系设置弹窗 -->
    <BasicModal @register="registerDependencyModal" title="依赖关系设置" width="70%" @ok="handleDependencySubmit">
      <div class="dependency-content">
        <div class="current-item mb-4 p-3 bg-gray-50 rounded">
          <h4 class="font-medium">当前项目：{{ currentDependencyRecord?.itemName }}</h4>
        </div>

        <a-tabs v-model:activeKey="dependencyTabKey">
          <a-tab-pane key="predecessors" tab="前置依赖">
            <div class="mb-4">
              <a-button type="dashed" @click="handleAddPredecessor">
                <PlusOutlined />
                添加前置依赖
              </a-button>
            </div>
            <BasicTable @register="registerPredecessorTable" :can-resize="false">
              <template #action="{ record, index }">
                <a-button type="link" size="small" danger @click="handleRemovePredecessor(index)"> 移除 </a-button>
              </template>
            </BasicTable>
          </a-tab-pane>

          <a-tab-pane key="successors" tab="后置依赖">
            <div class="mb-4">
              <a-button type="dashed" @click="handleAddSuccessor">
                <PlusOutlined />
                添加后置依赖
              </a-button>
            </div>
            <BasicTable @register="registerSuccessorTable" :can-resize="false">
              <template #action="{ record, index }">
                <a-button type="link" size="small" danger @click="handleRemoveSuccessor(index)"> 移除 </a-button>
              </template>
            </BasicTable>
          </a-tab-pane>
        </a-tabs>
      </div>
    </BasicModal>

    <!-- 甘特图弹窗 -->
    <BasicModal @register="registerGanttModal" title="甘特图视图" width="90%" :footer="null">
      <div class="gantt-content">
        <div class="gantt-toolbar mb-4 flex justify-between items-center">
          <div class="view-controls">
            <a-radio-group v-model:value="ganttViewMode" button-style="solid">
              <a-radio-button value="day">日视图</a-radio-button>
              <a-radio-button value="week">周视图</a-radio-button>
              <a-radio-button value="month">月视图</a-radio-button>
            </a-radio-group>
          </div>
          <div class="gantt-actions">
            <a-button @click="handleGanttExport">
              <ExportOutlined />
              导出甘特图
            </a-button>
          </div>
        </div>

        <!-- 甘特图占位区域 -->
        <div class="gantt-chart-container" style="height: 500px; border: 1px dashed #d9d9d9; display: flex; align-items: center; justify-content: center">
          <div class="text-center text-gray-500">
            <BarChartOutlined style="font-size: 48px; margin-bottom: 16px" />
            <div>甘特图组件开发中...</div>
            <div class="text-sm mt-2">将在此显示项目计划的甘特图视图</div>
          </div>
        </div>
      </div>
    </BasicModal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed, inject, onMounted } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { BasicForm, useForm } from '/@/components/Form';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import {
    PlusOutlined,
    BranchesOutlined,
    BarChartOutlined,
    ExportOutlined,
    ReloadOutlined,
    CalendarOutlined,
    FlagOutlined,
    ProjectOutlined,
    ClockCircleOutlined,
  } from '@ant-design/icons-vue';
  import dayjs from 'dayjs';

  defineOptions({ name: 'ProjectTemplateScheduleConfigPage' });

  // 注入模板ID
  const templateId = inject<any>('templateId');
  const { createMessage } = useMessage();

  const loading = ref(false);
  const searchText = ref('');
  const typeFilter = ref('');
  const statusFilter = ref('');
  const drawerTitle = ref('');
  const currentRecord = ref<any>(null);
  const currentDependencyRecord = ref<any>(null);
  const dependencyTabKey = ref('predecessors');
  const ganttViewMode = ref('week');

  // 计划统计数据
  const scheduleStats = ref({
    totalItems: 8,
    milestones: 3,
    totalDuration: 60,
    criticalPath: 5,
  });

  // 依赖关系数据
  const predecessorData = ref<any[]>([]);
  const successorData = ref<any[]>([]);

  // 表格列配置
  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      width: 60,
      customRender: ({ index }) => index + 1,
    },
    {
      title: '计划项名称',
      dataIndex: 'itemName',
      width: 200,
      slots: { customRender: 'itemName' },
    },
    {
      title: '类型',
      dataIndex: 'itemType',
      width: 100,
      slots: { customRender: 'itemType' },
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 80,
      slots: { customRender: 'status' },
    },
    {
      title: '计划时间',
      dataIndex: 'timeRange',
      width: 180,
      slots: { customRender: 'timeRange' },
    },
    {
      title: '工期(天)',
      dataIndex: 'duration',
      width: 100,
    },
    {
      title: '进度',
      dataIndex: 'progress',
      width: 120,
      slots: { customRender: 'progress' },
    },
    {
      title: '依赖项',
      dataIndex: 'dependencies',
      width: 150,
      slots: { customRender: 'dependencies' },
    },
    {
      title: '负责人',
      dataIndex: 'assignee',
      width: 100,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 150,
      customRender: ({ text }) => (text ? new Date(text).toLocaleDateString() : '-'),
    },
  ];

  // 表单配置
  const formSchemas = [
    {
      field: 'itemName',
      label: '计划项名称',
      component: 'Input',
      required: true,
      colProps: { span: 12 },
    },
    {
      field: 'itemCode',
      label: '计划项编码',
      component: 'Input',
      required: true,
      colProps: { span: 12 },
    },
    {
      field: 'itemType',
      label: '类型',
      component: 'Select',
      required: true,
      componentProps: {
        options: [
          { label: '里程碑', value: 'milestone' },
          { label: '任务', value: 'task' },
          { label: '阶段', value: 'phase' },
        ],
      },
      colProps: { span: 12 },
    },
    {
      field: 'status',
      label: '状态',
      component: 'RadioGroup',
      defaultValue: 'active',
      componentProps: {
        options: [
          { label: '启用', value: 'active' },
          { label: '禁用', value: 'inactive' },
        ],
      },
      colProps: { span: 12 },
    },
    {
      field: 'planStartDate',
      label: '计划开始日期',
      component: 'DatePicker',
      required: true,
      componentProps: {
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
      colProps: { span: 12 },
    },
    {
      field: 'planEndDate',
      label: '计划结束日期',
      component: 'DatePicker',
      required: true,
      componentProps: {
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
      colProps: { span: 12 },
    },
    {
      field: 'duration',
      label: '工期(天)',
      component: 'InputNumber',
      componentProps: {
        min: 1,
      },
      colProps: { span: 12 },
    },
    {
      field: 'assignee',
      label: '负责人',
      component: 'Input',
      colProps: { span: 12 },
    },
    {
      field: 'description',
      label: '描述',
      component: 'InputTextArea',
      componentProps: {
        rows: 3,
      },
      colProps: { span: 24 },
    },
  ];

  // 依赖关系表格列
  const dependencyColumns = [
    {
      title: '计划项名称',
      dataIndex: 'itemName',
      width: 200,
    },
    {
      title: '类型',
      dataIndex: 'itemType',
      width: 100,
    },
    {
      title: '依赖类型',
      dataIndex: 'dependencyType',
      width: 120,
      customRender: ({ text }) => {
        const typeMap = {
          fs: 'FS (完成-开始)',
          ss: 'SS (开始-开始)',
          ff: 'FF (完成-完成)',
          sf: 'SF (开始-完成)',
        };
        return typeMap[text] || text;
      },
    },
    {
      title: '延迟天数',
      dataIndex: 'lag',
      width: 100,
      customRender: ({ text }) => (text ? `${text}天` : '0天'),
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      slots: { customRender: 'action' },
    },
  ];

  // 表格实例
  const [registerTable, { reload, getDataSource }] = useTable({
    api: loadScheduleData,
    columns,
    useSearchForm: false,
    actionColumn: {
      width: 200,
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
    },
  });

  // 表单实例
  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 120,
    schemas: formSchemas,
    showActionButtonGroup: false,
  });

  // 抽屉实例
  const [registerDrawer, { openDrawer, closeDrawer }] = useDrawerInner();

  // 依赖关系弹窗实例
  const [registerDependencyModal, { openModal: openDependencyModal, closeModal: closeDependencyModal }] = useModalInner();

  // 甘特图弹窗实例
  const [registerGanttModal, { openModal: openGanttModal, closeModal: closeGanttModal }] = useModalInner();

  // 前置依赖表格
  const [registerPredecessorTable] = useTable({
    dataSource: predecessorData,
    columns: dependencyColumns,
    useSearchForm: false,
    pagination: false,
  });

  // 后置依赖表格
  const [registerSuccessorTable] = useTable({
    dataSource: successorData,
    columns: dependencyColumns,
    useSearchForm: false,
    pagination: false,
  });

  // 加载主计划数据
  async function loadScheduleData() {
    if (!templateId?.value) return { list: [], total: 0 };

    loading.value = true;
    try {
      // 模拟数据
      const mockData = [
        {
          id: '1',
          itemName: '项目启动里程碑',
          itemCode: 'MS_START',
          itemType: 'milestone',
          status: 'active',
          planStartDate: '2025-03-01',
          planEndDate: '2025-03-01',
          duration: 0,
          progress: 0,
          dependencies: [],
          assignee: '项目经理',
          description: '项目正式启动的里程碑',
          createdAt: '2025-01-15T10:00:00Z',
        },
        {
          id: '2',
          itemName: '需求分析阶段',
          itemCode: 'PHASE_REQ',
          itemType: 'phase',
          status: 'active',
          planStartDate: '2025-03-02',
          planEndDate: '2025-03-15',
          duration: 14,
          progress: 30,
          dependencies: [{ id: '1', name: '项目启动里程碑' }],
          assignee: '需求分析师',
          description: '完成系统需求的收集和分析',
          createdAt: '2025-01-15T10:00:00Z',
        },
        {
          id: '3',
          itemName: '需求评审里程碑',
          itemCode: 'MS_REQ_REVIEW',
          itemType: 'milestone',
          status: 'active',
          planStartDate: '2025-03-16',
          planEndDate: '2025-03-16',
          duration: 0,
          progress: 0,
          dependencies: [{ id: '2', name: '需求分析阶段' }],
          assignee: '技术总监',
          description: '需求文档评审通过',
          createdAt: '2025-01-15T10:00:00Z',
        },
      ];

      return { list: mockData, total: mockData.length };
    } catch (error) {
      console.error('加载主计划数据失败:', error);
      createMessage.error('加载主计划数据失败');
      return { list: [], total: 0 };
    } finally {
      loading.value = false;
    }
  }

  // 工具函数
  const getTypeColor = (itemType: string) => {
    const colorMap = {
      milestone: '#fa8c16',
      task: '#1890ff',
      phase: '#52c41a',
    };
    return colorMap[itemType] || '#1890ff';
  };

  const getTypeIcon = (itemType: string) => {
    const iconMap = {
      milestone: FlagOutlined,
      task: ClockCircleOutlined,
      phase: ProjectOutlined,
    };
    return iconMap[itemType] || ProjectOutlined;
  };

  const getTypeText = (itemType: string) => {
    const textMap = {
      milestone: '里程碑',
      task: '任务',
      phase: '阶段',
    };
    return textMap[itemType] || '未知类型';
  };

  const getProgressColor = (progress: number) => {
    if (progress === 100) return '#52c41a';
    if (progress >= 80) return '#1890ff';
    if (progress >= 60) return '#faad14';
    if (progress >= 40) return '#fa8c16';
    return '#ff4d4f';
  };

  const formatDate = (date: string) => {
    return date ? dayjs(date).format('YYYY-MM-DD') : '-';
  };

  // 事件处理
  const handleSearch = () => {
    reload();
  };

  const handleTypeFilter = () => {
    reload();
  };

  const handleStatusFilter = () => {
    reload();
  };

  const handleAdd = () => {
    currentRecord.value = null;
    drawerTitle.value = '添加计划项';
    resetFields();
    openDrawer();
  };

  const handleEdit = (record: any) => {
    currentRecord.value = record;
    drawerTitle.value = '编辑计划项';
    setFieldsValue(record);
    openDrawer();
  };

  const handleCopy = (record: any) => {
    currentRecord.value = null;
    drawerTitle.value = '复制计划项';
    const copyData = { ...record };
    delete copyData.id;
    copyData.itemName = `${record.itemName} - 副本`;
    copyData.itemCode = `${record.itemCode}_COPY`;
    setFieldsValue(copyData);
    openDrawer();
  };

  const handleDependencies = (record: any) => {
    currentDependencyRecord.value = record;
    // 加载依赖关系数据
    loadDependencyData(record.id);
    openDependencyModal();
  };

  const handleDelete = (record: any) => {
    createMessage.success('删除成功');
    reload();
  };

  const handleSubmit = async () => {
    try {
      const values = await validate();
      console.log('提交计划数据:', values);

      createMessage.success('保存成功');
      closeDrawer();
      reload();
    } catch (error) {
      console.error('保存失败:', error);
      createMessage.error('保存失败');
    }
  };

  const handleGenerateFromPhases = () => {
    createMessage.info('从阶段生成计划功能开发中...');
  };

  const handleGanttView = () => {
    openGanttModal();
  };

  const handleExport = () => {
    createMessage.info('导出功能开发中...');
  };

  const handleRefresh = () => {
    reload();
  };

  const handleGanttExport = () => {
    createMessage.info('甘特图导出功能开发中...');
  };

  // 依赖关系处理
  const loadDependencyData = (itemId: string) => {
    // 模拟加载依赖关系数据
    predecessorData.value = [
      {
        id: '1',
        itemName: '项目启动里程碑',
        itemType: 'milestone',
        dependencyType: 'fs',
        lag: 0,
      },
    ];

    successorData.value = [
      {
        id: '3',
        itemName: '需求评审里程碑',
        itemType: 'milestone',
        dependencyType: 'fs',
        lag: 1,
      },
    ];
  };

  const handleAddPredecessor = () => {
    createMessage.info('添加前置依赖功能开发中...');
  };

  const handleAddSuccessor = () => {
    createMessage.info('添加后置依赖功能开发中...');
  };

  const handleRemovePredecessor = (index: number) => {
    predecessorData.value.splice(index, 1);
  };

  const handleRemoveSuccessor = (index: number) => {
    successorData.value.splice(index, 1);
  };

  const handleDependencySubmit = () => {
    console.log('保存依赖关系:', {
      itemId: currentDependencyRecord.value?.id,
      predecessors: predecessorData.value,
      successors: successorData.value,
    });

    createMessage.success('依赖关系保存成功');
    closeDependencyModal();
    reload();
  };

  onMounted(() => {
    console.log('主计划管理页面挂载，模板ID:', templateId?.value);
  });
</script>

<style lang="less" scoped>
  .template-schedule-page {
    .stat-card {
      transition: all 0.3s ease;
    }

    .stat-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .dependencies {
      .ant-tag {
        margin-bottom: 4px;
      }
    }

    .dependency-content {
      .current-item {
        border-left: 4px solid #1890ff;
      }
    }

    .gantt-content {
      .gantt-chart-container {
        background: #fafafa;
      }
    }
  }
</style>
