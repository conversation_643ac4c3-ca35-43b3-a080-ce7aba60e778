<template>
  <div class="xh-content-wrapper">
    <div class="xh-content-wrapper-center">
      <div class="xh-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="handleCreate">新增采购合同</a-button>
          </template>
          <template #form-supplierSelect="{ model, field }">
            <SupplierSelect v-model:value="model[field]" placeholder="请选择供应商" />
          </template>
          <template #form-userSelect="{ model, field }">
            <UserSelect v-model:value="model[field]" placeholder="请选择项目经理" />
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'contractInfo'">
              <div>
                <div v-if="record.contractNo && record.contractName"> {{ record.contractNo }} - {{ record.contractName }} </div>
                <div v-else-if="record.contractName">
                  {{ record.contractName }}
                </div>
                <div v-else-if="record.contractNo">
                  {{ record.contractNo }}
                </div>
                <div v-else>-</div>
              </div>
            </template>
            <template v-if="column.key === 'amount'">
              {{ formatAmount(record.amount) }}
            </template>
            <template v-if="column.key === 'yfAmount'">
              <div>
                <div>{{ formatAmount(record.yfAmount) }}</div>
                <div v-if="record.amount > 0 && record.yfAmount > 0" class="payment-ratio">
                  <a-progress :percent="Math.round((record.yfAmount / record.amount) * 100)" size="small" :show-info="false" />
                  <span class="ratio-text">{{ Math.round((record.yfAmount / record.amount) * 100) }}%</span>
                </div>
              </div>
            </template>
            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'moneyStatus'">
              <a-tag :color="getMoneyStatusColor(record.moneyStatus)">
                {{ getMoneyStatusText(record.moneyStatus) }}
              </a-tag>
            </template>
            <template v-if="column.dataIndex === 'action'">
              <TableAction :actions="getActions(record)" />
            </template>
          </template>

          <!-- 数据统计汇总 -->
          <template #summary>
            <a-table-summary>
              <a-table-summary-row>
                <a-table-summary-cell :col-span="6" class="summary-label">
                  <strong>合计</strong>
                </a-table-summary-cell>
                <a-table-summary-cell class="summary-amount">
                  <strong>{{ formatAmount(summaryData.totalAmount) }}</strong>
                </a-table-summary-cell>
                <a-table-summary-cell class="summary-amount">
                  <strong>{{ formatAmount(summaryData.totalPaidAmount) }}</strong>
                </a-table-summary-cell>
                <a-table-summary-cell :col-span="4" class="summary-unpaid">
                  <strong>未付款: {{ formatAmount(summaryData.totalUnpaidAmount) }}</strong>
                </a-table-summary-cell>
              </a-table-summary-row>
            </a-table-summary>
          </template>
        </BasicTable>
      </div>
    </div>
    <PaycontractDrawer @register="registerDrawer" @success="handleSuccess" />
    <SignForm @register="registerSignForm" @reload="reload" />
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, watch } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { getPaycontractList, deletePaycontract, updatePaycontractStatus, PaycontractModel } from '/@/api/project/paycontract';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useDrawer } from '/@/components/Drawer';
  import { useModal } from '/@/components/Modal';
  import PaycontractDrawer from './PaycontractDrawer.vue';
  import SignForm from './SignForm.vue';
  import SupplierSelect from '/@/views/project/components/SupplierSelect.vue';
  import UserSelect from '/@/components/Xh/Organize/src/UserSelect.vue';
  import { useRouter } from 'vue-router';
  import { formatToDate } from '/@/utils/dateUtil';
  import type { FormSchema } from '/@/components/Table';

  const { createMessage } = useMessage();
  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerSignForm, { openModal: openSignModal }] = useModal();
  const router = useRouter();
  defineOptions({ name: 'project-biz-payContract' });

  // 数据统计
  const summaryData = ref({
    totalAmount: 0,
    totalPaidAmount: 0,
    totalUnpaidAmount: 0,
  });

  // 采购合同状态映射
  const statusMap = {
    draft: { text: '草稿', color: 'default' },
    pending: { text: '待审批', color: 'warning' },
    approved: { text: '待签订', color: 'processing' },
    executing: { text: '执行中', color: 'processing' },
    completed: { text: '已完成', color: 'success' },
    terminated: { text: '已中止', color: 'error' },
  };

  // 付款状态映射
  const moneyStatusMap = {
    unpaid: { text: '未付款', color: 'default' },
    partial: { text: '部分付款', color: 'processing' },
    paid: { text: '已付讫', color: 'success' },
  };

  // 获取采购合同状态文本
  function getStatusText(status: string) {
    return statusMap[status]?.text || status;
  }

  // 获取采购合同状态颜色
  function getStatusColor(status: string) {
    return statusMap[status]?.color || 'default';
  }

  // 获取付款状态文本
  function getMoneyStatusText(status: string) {
    return moneyStatusMap[status]?.text || status;
  }

  // 获取付款状态颜色
  function getMoneyStatusColor(status: string) {
    return moneyStatusMap[status]?.color || 'default';
  }

  // 格式化金额
  function formatAmount(amount: number) {
    return amount ? amount.toLocaleString('zh-CN', { style: 'currency', currency: 'CNY' }) : '¥0.00';
  }

  // 根据记录状态获取操作按钮
  function getActions(record: PaycontractModel) {
    const actions = [
      {
        icon: 'clarity:info-standard-line',
        label: '查看详情',
        onClick: handleView.bind(null, record),
      },
      {
        icon: 'clarity:note-edit-line',
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        ifShow: record.status === 'draft' || record.status === 'pending', // 只有草稿和待审批状态可以编辑
      },
    ];

    // 根据状态添加不同的操作按钮
    if (record.status === 'approved') {
      actions.push({
        icon: 'ant-design:file-done-outlined',
        label: '签订合同',
        onClick: handleSign.bind(null, record),
      });
    }

    if (record.status === 'executing') {
      actions.push({
        icon: 'ant-design:check-circle-outlined',
        label: '标记为已完成',
        onClick: handleComplete.bind(null, record),
      });
    }

    actions.push({
      icon: 'ant-design:delete-outlined',
      label: '删除',
      popConfirm: {
        title: '是否确认删除',
        confirm: handleDelete.bind(null, record),
      },
      ifShow: record.status === 'draft' || record.status === 'pending', // 只有草稿和待审批状态可以删除
    });

    return actions;
  }

  // 表格列定义
  const columns = [
    {
      title: '采购合同名称',
      dataIndex: 'name',
      width: 200,
    },
    {
      title: '采购合同编号',
      dataIndex: 'cno',
      width: 150,
    },
    {
      title: '关联收款合同',
      key: 'contractInfo',
      dataIndex: 'contractInfo',
      width: 200,
    },
    {
      title: '供应商',
      dataIndex: 'supplierName',
      width: 150,
    },
    {
      title: '合同金额',
      key: 'amount',
      dataIndex: 'amount',
      width: 120,
    },
    {
      title: '已付金额',
      key: 'yfAmount',
      dataIndex: 'yfAmount',
      width: 150,
    },
    {
      title: '项目经理',
      dataIndex: 'ownName',
      width: 100,
    },
    {
      title: '开发一部金额',
      dataIndex: 'kfybAmount',
      width: 120,
      customRender: ({ record }) => formatAmount(record.kfybAmount),
    },
    {
      title: '开发二部金额',
      dataIndex: 'kfebAmount',
      width: 120,
      customRender: ({ record }) => formatAmount(record.kfebAmount),
    },
    {
      title: '综合金额',
      dataIndex: 'otherAmount',
      width: 120,
      customRender: ({ record }) => formatAmount(record.otherAmount),
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      width: 150,
    },
    {
      title: '备注',
      dataIndex: 'note',
      width: 150,
      ellipsis: true,
    },
  ];

  // 搜索表单配置
  const searchFormSchema: FormSchema[] = [
    {
      field: 'name',
      label: '采购合同名称',
      component: 'Input',
      colProps: { span: 6 },
    },
    {
      field: 'cno',
      label: '采购合同编号',
      component: 'Input',
      colProps: { span: 6 },
    },
    {
      field: 'suppilerId',
      label: '供应商',
      component: 'Input',
      slot: 'supplierSelect',
      colProps: { span: 6 },
    },
    {
      field: 'ownId',
      label: '项目经理',
      component: 'Input',
      slot: 'userSelect',
      colProps: { span: 6 },
    },
    {
      field: 'status',
      label: '采购合同状态',
      component: 'Select',
      componentProps: {
        options: [
          { fullName: '全部', id: '' },
          { fullName: '草稿', id: 'draft' },
          { fullName: '待审批', id: 'pending' },
          { fullName: '待签订', id: 'approved' },
          { fullName: '执行中', id: 'executing' },
          { fullName: '已完成', id: 'completed' },
          { fullName: '已中止', id: 'terminated' },
        ],
      },
      colProps: { span: 6 },
    },
    {
      field: 'moneyStatus',
      label: '付款状态',
      component: 'Select',
      componentProps: {
        options: [
          { fullName: '全部', id: '' },
          { fullName: '未付款', id: 'unpaid' },
          { fullName: '部分付款', id: 'partial' },
          { fullName: '已付讫', id: 'paid' },
        ],
      },
      colProps: { span: 6 },
    },
    {
      field: 'signYear',
      label: '签订年份',
      component: 'InputNumber',
      colProps: { span: 6 },
    },
  ];

  // 注册表格
  const [registerTable, { reload, getDataSource }] = useTable({
    title: '采购合同列表',
    api: getPaycontractList,
    columns,
    formConfig: {
      labelWidth: 80,
      alwaysShowLines: 3,
      showAdvancedButton: false,
      schemas: searchFormSchema,
    },
    useSearchForm: true,
    showTableSetting: true,
    bordered: true,
    showIndexColumn: true,
    canResize: true,
    actionColumn: {
      width: 200,
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
    },
    afterFetch: data => {
      // 计算统计数据
      calculateSummary(data);
      return data;
    },
  });

  // 计算统计数据
  function calculateSummary(data: PaycontractModel[]) {
    const totalAmount = data.reduce((sum, item) => sum + (item.amount || 0), 0);
    const totalPaidAmount = data.reduce((sum, item) => sum + (item.yfAmount || 0), 0);
    const totalUnpaidAmount = totalAmount - totalPaidAmount;

    summaryData.value = {
      totalAmount,
      totalPaidAmount,
      totalUnpaidAmount,
    };
  }

  // 新增采购合同
  function handleCreate() {
    const drawerData = {
      isUpdate: false,
      readonly: false,
    };
    console.log('新增采购合同，传递给抽屉的数据:', drawerData);
    openDrawer(true, drawerData);
  }

  // 编辑采购合同
  function handleEdit(record: PaycontractModel) {
    console.log('编辑采购合同:', record);
    const drawerData = {
      record: { pcId: record.pcId }, // 只传递ID，让抽屉组件自己获取详情
      isUpdate: true,
      readonly: false,
    };
    console.log('传递给抽屉的数据:', drawerData);
    openDrawer(true, drawerData);
  }

  // 查看采购合同详情
  function handleView(record: PaycontractModel) {
    console.log('查看采购合同:', record);
    const drawerData = {
      record: { pcId: record.pcId }, // 只传递ID，让抽屉组件自己获取详情
      isUpdate: false,
      readonly: true,
    };
    console.log('传递给抽屉的数据:', drawerData);
    openDrawer(true, drawerData);
  }

  // 操作成功回调
  function handleSuccess() {
    reload();
  }

  // 签订采购合同
  function handleSign(record: PaycontractModel) {
    openSignModal(true, {
      paycontractId: record.pcId,
      record,
    });
  }

  // 标记为已完成
  async function handleComplete(record: PaycontractModel) {
    try {
      await updatePaycontractStatus(record.pcId, 'completed');
      createMessage.success('操作成功');
      reload();
    } catch (error) {
      console.error('操作失败:', error);
      createMessage.error('操作失败');
    }
  }

  // 删除采购合同
  async function handleDelete(record: PaycontractModel) {
    try {
      await deletePaycontract(record.pcId);
      createMessage.success('删除成功');
      reload();
    } catch (error: any) {
      if (error.message && error.message.includes('已有付款记录')) {
        createMessage.error('该采购合同已有付款记录，无法删除');
      } else {
        createMessage.error('删除失败');
      }
    }
  }
</script>

<style scoped>
  .payment-ratio {
    margin-top: 4px;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .ratio-text {
    font-size: 12px;
    color: #666;
    min-width: 35px;
  }

  .summary-label {
    text-align: center;
    background-color: #fafafa;
  }

  .summary-amount {
    text-align: right;
    background-color: #fafafa;
  }

  .summary-unpaid {
    text-align: right;
    background-color: #fafafa;
    color: #ff4d4f;
  }

  :deep(.ant-table-summary) {
    background-color: #fafafa;
  }

  :deep(.ant-table-summary td) {
    border-top: 2px solid #d9d9d9;
  }
</style>
