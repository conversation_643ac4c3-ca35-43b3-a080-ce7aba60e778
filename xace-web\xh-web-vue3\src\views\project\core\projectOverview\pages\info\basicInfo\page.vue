<template>
  <div class="basic-info-page p-4">
    <!-- 页面标题 -->
    <div class="page-header mb-4">
      <div class="flex justify-between items-center">
        <div>
          <h2 class="text-xl font-semibold mb-2">基本信息</h2>
          <p class="text-gray-600">查看和编辑项目的基本信息</p>
        </div>
        <a-space>
          <a-button v-if="!isEditing" type="primary" @click="handleEdit">
            <template #icon><EditOutlined /></template>
            编辑
          </a-button>
          <a-button v-if="isEditing" type="primary" @click="handleSave" :loading="saving">
            <template #icon><SaveOutlined /></template>
            保存
          </a-button>
          <a-button v-if="isEditing" @click="handleCancel">
            <template #icon><CloseOutlined /></template>
            取消
          </a-button>
        </a-space>
      </div>
    </div>

    <a-spin :spinning="loading">
      <!-- 基本信息表单 -->
      <div class="basic-info-form bg-white rounded-lg shadow-sm border p-6">
        <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ style: { width: '150px' } }">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- 项目编码 -->
            <a-form-item label="项目编码" name="code">
              <a-input v-model:value="formData.code" disabled />
            </a-form-item>

            <!-- 状态 -->
            <a-form-item label="状态" name="status">
              <a-tag :color="getStatusColor(formData.status)">
                {{ formData.status }}
              </a-tag>
            </a-form-item>

            <!-- 项目名称 -->
            <a-form-item label="项目名称" name="title" class="md:col-span-2">
              <a-input v-model:value="formData.title" :disabled="!isEditing" placeholder="请输入项目名称" />
            </a-form-item>

            <!-- 项目目标 -->
            <a-form-item label="项目目标" name="text01" class="md:col-span-2">
              <a-textarea v-model:value="formData.text01" :disabled="!isEditing" placeholder="请输入项目目标" :rows="3" />
            </a-form-item>

            <!-- 项目经理 -->
            <a-form-item label="项目经理" name="sys01">
              <a-select v-model:value="formData.sys01" :disabled="!isEditing" placeholder="请选择项目经理" show-search :options="managerOptions" />
            </a-form-item>

            <!-- 项目发起人 -->
            <a-form-item label="项目发起人" name="sys02">
              <a-select v-model:value="formData.sys02" :disabled="!isEditing" placeholder="请选择项目发起人" show-search :options="sponsorOptions" />
            </a-form-item>

            <!-- 日历开始时间 -->
            <a-form-item label="日历开始时间" name="date01">
              <a-date-picker v-model:value="formData.date01" :disabled="!isEditing" placeholder="请选择开始时间" style="width: 100%" />
            </a-form-item>

            <!-- 日历结束时间 -->
            <a-form-item label="日历结束时间" name="date02">
              <a-date-picker v-model:value="formData.date02" :disabled="!isEditing" placeholder="请选择结束时间" style="width: 100%" />
            </a-form-item>

            <!-- 投资概算 -->
            <a-form-item label="投资概算" name="num12">
              <a-input-number
                v-model:value="formData.num12"
                :disabled="!isEditing"
                placeholder="请输入投资概算"
                :min="0"
                :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                :parser="value => value.replace(/¥\s?|(,*)/g, '')"
                style="width: 100%" />
            </a-form-item>

            <!-- 战略目标 -->
            <a-form-item label="战略目标" name="enum01">
              <a-select v-model:value="formData.enum01" :disabled="!isEditing" placeholder="请选择战略目标" :options="strategicGoalOptions" />
            </a-form-item>

            <!-- 优先级 -->
            <a-form-item label="优先级" name="enum02">
              <a-select v-model:value="formData.enum02" :disabled="!isEditing" placeholder="请选择优先级" :options="priorityOptions" />
            </a-form-item>

            <!-- 工作量估算 -->
            <a-form-item label="工作量估算（人天）" name="num05">
              <a-input-number v-model:value="formData.num05" :disabled="!isEditing" placeholder="请输入工作量估算" :min="0" style="width: 100%" />
            </a-form-item>

            <!-- 风险等级 -->
            <a-form-item label="风险等级" name="enum04">
              <a-select v-model:value="formData.enum04" :disabled="!isEditing" placeholder="请选择风险等级" :options="riskLevelOptions" />
            </a-form-item>

            <!-- 所属部门 -->
            <a-form-item label="所属部门" name="departmentID">
              <a-select v-model:value="formData.departmentID" :disabled="!isEditing" placeholder="请选择所属部门" show-search :options="departmentOptions" />
            </a-form-item>

            <!-- 所属组合/项目群 -->
            <a-form-item label="所属组合/项目群" name="sys03">
              <a-select v-model:value="formData.sys03" :disabled="!isEditing" placeholder="请选择所属组合/项目群" show-search :options="portfolioOptions" />
            </a-form-item>

            <!-- 项目范围 -->
            <a-form-item label="项目范围" name="text02" class="md:col-span-2">
              <a-textarea v-model:value="formData.text02" :disabled="!isEditing" placeholder="请输入项目范围" :rows="3" />
            </a-form-item>

            <!-- 创建日期 -->
            <a-form-item label="创建日期" name="createTime">
              <a-date-picker v-model:value="formData.createTime" disabled style="width: 100%" />
            </a-form-item>

            <!-- 创建人 -->
            <a-form-item label="创建人" name="createBy">
              <a-input v-model:value="formData.createBy" disabled />
            </a-form-item>

            <!-- 修改日期 -->
            <a-form-item label="修改日期" name="lastUpdateTime">
              <a-date-picker v-model:value="formData.lastUpdateTime" disabled style="width: 100%" />
            </a-form-item>

            <!-- 修改人 -->
            <a-form-item label="修改人" name="lastUpdateBy">
              <a-input v-model:value="formData.lastUpdateBy" disabled />
            </a-form-item>
          </div>
        </a-form>
      </div>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import { EditOutlined, SaveOutlined, CloseOutlined } from '@ant-design/icons-vue';
  import { message, DatePicker as ADatePicker } from 'ant-design-vue';
  import dayjs from 'dayjs';
  import { getProjectInfo, updateProjectBasicInfo } from '/@/api/project/projectBase';

  // 响应式数据
  const loading = ref(false);
  const saving = ref(false);
  const isEditing = ref(false);
  const formRef = ref();

  // 表单数据
  const formData = reactive({
    code: 'PROJ-2024-001',
    status: '进行中',
    title: '企业级项目管理系统',
    text01: '构建一个高效、可扩展的项目管理平台，提升项目交付效率和质量管理水平。',
    sys01: '1',
    sys02: '2',
    date01: dayjs('2024-01-01'),
    date02: dayjs('2024-12-31'),
    num12: 5000000,
    enum01: '1',
    enum02: '1',
    num05: 500,
    enum04: '2',
    departmentID: '1',
    sys03: '1',
    text02: '包括项目计划管理、任务分配、进度跟踪、资源管理、风险控制、质量保证等核心功能模块。',
    createTime: dayjs('2024-01-01'),
    createBy: '系统管理员',
    lastUpdateTime: dayjs(),
    lastUpdateBy: '张三',
  });

  // 备份数据
  const backupData = reactive({});

  // 表单验证规则
  const rules = {
    title: [{ required: true, message: '请输入项目名称' }],
    departmentID: [{ required: true, message: '请选择所属部门' }],
    sys03: [{ required: true, message: '请选择所属组合/项目群' }],
  };

  // 选项数据
  const managerOptions = [
    { label: '张三', value: '1' },
    { label: '李四', value: '2' },
    { label: '王五', value: '3' },
  ];

  const sponsorOptions = [
    { label: '赵六', value: '1' },
    { label: '钱七', value: '2' },
    { label: '孙八', value: '3' },
  ];

  const strategicGoalOptions = [
    { label: '提升效率', value: '1' },
    { label: '降低成本', value: '2' },
    { label: '增加收入', value: '3' },
  ];

  const priorityOptions = [
    { label: '高', value: '1' },
    { label: '中', value: '2' },
    { label: '低', value: '3' },
  ];

  const riskLevelOptions = [
    { label: '低', value: '1' },
    { label: '中', value: '2' },
    { label: '高', value: '3' },
  ];

  const departmentOptions = [
    { label: '软件科技事业部', value: '1' },
    { label: '数字化解决方案部', value: '2' },
    { label: '研发部', value: '3' },
  ];

  const portfolioOptions = [
    { label: '数字化转型项目群', value: '1' },
    { label: '产品研发项目群', value: '2' },
    { label: '基础设施项目群', value: '3' },
  ];

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      进行中: 'processing',
      已完成: 'success',
      已暂停: 'warning',
      已取消: 'error',
    };
    return colorMap[status] || 'default';
  };

  // 备份数据
  const backupFormData = () => {
    Object.assign(backupData, formData);
  };

  // 恢复数据
  const restoreFormData = () => {
    Object.assign(formData, backupData);
  };

  // 编辑
  const handleEdit = () => {
    backupFormData();
    isEditing.value = true;
    message.info('已进入编辑模式');
  };

  // 保存
  const handleSave = async () => {
    try {
      await formRef.value.validate();
      saving.value = true;

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));

      formData.lastUpdateTime = dayjs();
      formData.lastUpdateBy = '当前用户';

      isEditing.value = false;
      saving.value = false;
      message.success('保存成功');
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败，请检查输入信息');
      saving.value = false;
    }
  };

  // 取消
  const handleCancel = () => {
    restoreFormData();
    isEditing.value = false;
    message.info('已取消编辑');
  };

  // 加载数据
  const loadData = async () => {
    loading.value = true;
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));
      // await updateProjectBasicInfo()

      // 这里可以从API加载实际数据
      console.log('项目基本信息加载完成');
    } catch (error) {
      console.error('加载失败:', error);
      message.error('加载失败，请刷新页面重试');
    } finally {
      loading.value = false;
    }
  };

  // 页面初始化
  onMounted(() => {
    loadData();
  });
</script>

<style scoped>
  .basic-info-page {
    min-height: 100vh;
    background-color: var(--section-bg-color);
  }

  .form-header {
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 12px;
    margin-bottom: 24px;
  }

  :deep(.ant-form-item-label) {
    font-weight: 500;
  }

  :deep(.ant-input:disabled),
  :deep(.ant-select-disabled .ant-select-selector),
  :deep(.ant-picker-disabled) {
    color: #666;
    background-color: var(--section-bg-color);
  }
</style>
