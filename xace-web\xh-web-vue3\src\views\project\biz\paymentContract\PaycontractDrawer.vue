<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" :title="getTitle" :width="800" :maskClosable="false" @ok="handleSubmit">
    <BasicForm @register="registerForm">
      <template #contractSelect="{ model, field }">
        <ContractSelect v-model:value="model[field]" placeholder="请选择关联收款合同" />
      </template>
      <template #supplierSelect="{ model, field }">
        <SupplierSelect v-model:value="model[field]" placeholder="请选择供应商" />
      </template>
      <template #userSelect="{ model, field }">
        <UserSelect v-model:value="model[field]" placeholder="请选择项目经理" />
      </template>
    </BasicForm>
  </BasicDrawer>
</template>

<script lang="ts" setup>
  import { ref, computed, unref } from 'vue';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { createPaycontract, updatePaycontract, getPaycontractInfo } from '/@/api/project/paycontract';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { formatToDate } from '/@/utils/dateUtil';
  import ContractSelect from '/@/views/project/components/ContractSelect.vue';
  import SupplierSelect from '/@/views/project/components/SupplierSelect.vue';
  import UserSelect from '/@/components/Xh/Organize/src/UserSelect.vue';

  import type { FormSchema } from '/@/components/Table';
  import type { PaycontractModel } from '/@/api/project/paycontract';

  const emit = defineEmits(['success', 'register']);
  const { createMessage } = useMessage();

  const isUpdate = ref(true);
  const rowId = ref('');
  const readonly = ref(false);

  const [registerForm, { setFieldsValue, resetFields, validate, updateSchema }] = useForm({
    labelWidth: 120,
    baseColProps: { span: 24 },
    schemas: getFormSchema(),
    showActionButtonGroup: false,
  });

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async data => {
    resetFields();
    setDrawerProps({ confirmLoading: false, loading: true });

    isUpdate.value = !!data?.isUpdate;
    readonly.value = !!data?.readonly;

    try {
      if (unref(isUpdate) || readonly.value) {
        // 编辑或查看时，从后台获取最新数据
        rowId.value = data.record.pcId;

        console.log('获取采购合同详情，ID:', rowId.value);

        // 调用接口获取详情数据
        const detailDataRes = await getPaycontractInfo(rowId.value);
        const detailData = detailDataRes.data;

        console.log('从后台获取的详情数据:', detailData);

        // 设置表单值
        const formValues = {
          ...detailData,
          estSignDate: detailData.estSignDate ? formatToDate(detailData.estSignDate) : undefined,
          signDate: detailData.signDate ? formatToDate(detailData.signDate) : undefined,
        };

        console.log('设置表单值:', formValues);
        await setFieldsValue(formValues);
      } else {
        // 新增时，清空 rowId
        rowId.value = '';
        console.log('新增采购合同模式');
      }

      // 设置表单只读状态
      const schemas = getFormSchema();
      schemas.forEach(schema => {
        updateSchema({
          field: schema.field,
          componentProps: {
            ...schema.componentProps,
            disabled: readonly.value,
          },
        });
      });

      console.log('设置抽屉按钮状态:', {
        isUpdate: isUpdate.value,
        readonly: readonly.value,
        showOkBtn: !readonly.value,
        showCancelBtn: true,
      });

      setDrawerProps({
        showOkBtn: !readonly.value,
        showCancelBtn: true,
        loading: false,
      });
    } catch (error) {
      console.error('获取采购合同详情失败:', error);
      createMessage.error('获取数据失败');
      setDrawerProps({
        loading: false,
        showOkBtn: !readonly.value,
        showCancelBtn: true,
      });
      closeDrawer();
    }
  });

  const getTitle = computed(() => {
    if (readonly.value) {
      return '查看采购合同详情';
    }
    return !unref(isUpdate) ? '新增采购合同' : '编辑采购合同';
  });

  function getFormSchema(): FormSchema[] {
    return [
      {
        field: 'name',
        label: '采购合同名称',
        component: 'Input',
        componentProps: {
          placeholder: '请输入采购合同名称',
        },
        required: true,
        colProps: { span: 24 },
      },
      {
        field: 'cno',
        label: '采购合同编号',
        component: 'Input',
        componentProps: {
          placeholder: '请输入采购合同编号',
        },
        colProps: { span: 12 },
      },
      {
        field: 'cid',
        label: '关联收款合同',
        component: 'Input',
        slot: 'contractSelect',
        required: true,
        colProps: { span: 12 },
      },
      {
        field: 'suppilerId',
        label: '供应商',
        component: 'Input',
        slot: 'supplierSelect',
        required: true,
        colProps: { span: 12 },
      },
      {
        field: 'ownId',
        label: '项目经理',
        component: 'Input',
        slot: 'userSelect',
        colProps: { span: 12 },
      },
      {
        field: 'amount',
        label: '合同金额',
        component: 'InputNumber',
        componentProps: {
          placeholder: '请输入合同金额',
          min: 0,
          precision: 2,
          formatter: (value: string) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
          parser: (value: string) => value.replace(/¥\s?|(,*)/g, ''),
        },
        required: true,
        colProps: { span: 12 },
      },
      {
        field: 'kfybAmount',
        label: '开发一部金额',
        component: 'InputNumber',
        componentProps: {
          placeholder: '请输入开发一部金额',
          min: 0,
          precision: 2,
          formatter: (value: string) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
          parser: (value: string) => value.replace(/¥\s?|(,*)/g, ''),
        },
        colProps: { span: 8 },
      },
      {
        field: 'kfebAmount',
        label: '开发二部金额',
        component: 'InputNumber',
        componentProps: {
          placeholder: '请输入开发二部金额',
          min: 0,
          precision: 2,
          formatter: (value: string) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
          parser: (value: string) => value.replace(/¥\s?|(,*)/g, ''),
        },
        colProps: { span: 8 },
      },
      {
        field: 'otherAmount',
        label: '综合金额',
        component: 'InputNumber',
        componentProps: {
          placeholder: '请输入综合金额',
          min: 0,
          precision: 2,
          formatter: (value: string) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
          parser: (value: string) => value.replace(/¥\s?|(,*)/g, ''),
        },
        colProps: { span: 8 },
      },
      {
        field: 'estSignDate',
        label: '预计签订日期',
        component: 'DatePicker',
        componentProps: {
          placeholder: '请选择预计签订日期',
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
        },
        colProps: { span: 12 },
      },
      {
        field: 'signDate',
        label: '实际签订日期',
        component: 'DatePicker',
        componentProps: {
          placeholder: '请选择实际签订日期',
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
        },
        colProps: { span: 12 },
      },
      {
        field: 'status',
        label: '合同状态',
        component: 'Select',
        componentProps: {
          placeholder: '请选择合同状态',
          options: [
            { fullName: '草稿', id: 'draft' },
            { fullName: '待审批', id: 'pending' },
            { fullName: '待签订', id: 'approved' },
            { fullName: '执行中', id: 'executing' },
            { fullName: '已完成', id: 'completed' },
            { fullName: '已中止', id: 'terminated' },
          ],
        },
        colProps: { span: 12 },
      },
      {
        field: 'moneyStatus',
        label: '付款状态',
        component: 'Select',
        componentProps: {
          placeholder: '请选择付款状态',
          options: [
            { fullName: '未付款', id: 'unpaid' },
            { fullName: '部分付款', id: 'partial' },
            { fullName: '已付讫', id: 'paid' },
          ],
        },
        colProps: { span: 12 },
      },
      {
        field: 'note',
        label: '备注',
        component: 'Input',
        componentProps: {
          type: 'textarea',
          placeholder: '请输入备注',
          rows: 3,
        },
        colProps: { span: 24 },
      },
    ];
  }

  async function handleSubmit() {
    try {
      const values = await validate();
      setDrawerProps({ confirmLoading: true });

      if (unref(isUpdate)) {
        await updatePaycontract(rowId.value, values);
        createMessage.success('编辑成功');
      } else {
        await createPaycontract(values);
        createMessage.success('新增成功');
      }

      closeDrawer();
      emit('success');
    } catch (error) {
      createMessage.error('操作失败');
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
