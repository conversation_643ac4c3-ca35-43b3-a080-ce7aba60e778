package com.xinghuo.project.schema.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.schema.dao.ProjectSchemaWbsMapper;
import com.xinghuo.project.schema.entity.ProjectSchemaWbsEntity;
import com.xinghuo.project.schema.model.ProjectSchemaWbsPagination;
import com.xinghuo.project.schema.service.ProjectSchemaWbsService;
import com.xinghuo.project.template.entity.WbsTemplateDetailEntity;
import com.xinghuo.project.template.service.WbsTemplateMasterService;
import com.xinghuo.project.template.service.ActivityLibraryService;
import com.xinghuo.project.template.entity.ActivityLibraryEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 项目模板WBS计划服务实现类
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
@Slf4j
@Service
public class ProjectSchemaWbsServiceImpl extends BaseServiceImpl<ProjectSchemaWbsMapper, ProjectSchemaWbsEntity> implements ProjectSchemaWbsService {

    @Resource
    private WbsTemplateMasterService wbsTemplateMasterService;

    @Resource
    private ActivityLibraryService activityLibraryService;

    @Override
    public List<ProjectSchemaWbsEntity> getList(ProjectSchemaWbsPagination pagination) {
        if (pagination == null) {
            return new ArrayList<>();
        }

        QueryWrapper<ProjectSchemaWbsEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<ProjectSchemaWbsEntity> lambda = queryWrapper.lambda();

        // 项目模板ID条件
        if (StrXhUtil.isNotEmpty(pagination.getProjectTemplateId())) {
            lambda.eq(ProjectSchemaWbsEntity::getProjectTemplateId, pagination.getProjectTemplateId());
        }

        // 父级ID条件
        if (StrXhUtil.isNotEmpty(pagination.getParentId())) {
            lambda.eq(ProjectSchemaWbsEntity::getParentId, pagination.getParentId());
        }

        // 名称模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getName())) {
            lambda.like(ProjectSchemaWbsEntity::getName, pagination.getName());
        }

        // WBS编码模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getWbsCode())) {
            lambda.like(ProjectSchemaWbsEntity::getWbsCode, pagination.getWbsCode());
        }

        // 节点类型条件
        if (pagination.getNodeType() != null) {
            lambda.eq(ProjectSchemaWbsEntity::getNodeType, pagination.getNodeType());
        }

        // 是否里程碑条件
        if (pagination.getIsMilestone() != null) {
            lambda.eq(ProjectSchemaWbsEntity::getIsMilestone, pagination.getIsMilestone());
        }

        // 层级条件
        if (pagination.getLevel() != null) {
            lambda.eq(ProjectSchemaWbsEntity::getLevel, pagination.getLevel());
        }

        // 工期范围条件
        if (pagination.getDurationMin() != null) {
            lambda.ge(ProjectSchemaWbsEntity::getDuration, pagination.getDurationMin());
        }
        if (pagination.getDurationMax() != null) {
            lambda.le(ProjectSchemaWbsEntity::getDuration, pagination.getDurationMax());
        }

        // 创建时间范围
        if (pagination.getCreateTimeStart() != null) {
            lambda.ge(ProjectSchemaWbsEntity::getCreatedAt, pagination.getCreateTimeStart());
        }
        if (pagination.getCreateTimeEnd() != null) {
            lambda.le(ProjectSchemaWbsEntity::getCreatedAt, pagination.getCreateTimeEnd());
        }

        // 排序
        lambda.orderByAsc(ProjectSchemaWbsEntity::getSeqNo)
              .orderByAsc(ProjectSchemaWbsEntity::getLevel)
              .orderByAsc(ProjectSchemaWbsEntity::getCreatedAt);

        return list(queryWrapper);
    }

    @Override
    public List<ProjectSchemaWbsEntity> getListByTemplateId(String projectTemplateId) {
        if (StrXhUtil.isEmpty(projectTemplateId)) {
            return new ArrayList<>();
        }

        QueryWrapper<ProjectSchemaWbsEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(ProjectSchemaWbsEntity::getProjectTemplateId, projectTemplateId)
                .orderByAsc(ProjectSchemaWbsEntity::getSeqNo)
                .orderByAsc(ProjectSchemaWbsEntity::getLevel);

        return list(queryWrapper);
    }

    @Override
    public ProjectSchemaWbsEntity getInfo(String id) {
        if (StrXhUtil.isEmpty(id)) {
            return null;
        }
        return getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String create(ProjectSchemaWbsEntity entity) {
        if (entity == null) {
            throw new RuntimeException("WBS计划信息不能为空");
        }

        // 验证必填字段
        if (StrXhUtil.isEmpty(entity.getProjectTemplateId())) {
            throw new RuntimeException("项目模板ID不能为空");
        }
        if (StrXhUtil.isEmpty(entity.getName())) {
            throw new RuntimeException("WBS名称不能为空");
        }

        // 检查名称是否重复
        if (isExistByName(entity.getProjectTemplateId(), entity.getName(), null)) {
            throw new RuntimeException("WBS名称在当前项目模板中已存在");
        }

        // 设置默认值
        if (entity.getSeqNo() == null) {
            entity.setSeqNo(getNextSeqNo(entity.getProjectTemplateId(), entity.getParentId()));
        }

        // 设置层级
        if (entity.getLevel() == null) {
            entity.setLevel(calculateLevel(entity.getParentId()));
        }

        // 生成WBS编码
        if (StrXhUtil.isEmpty(entity.getWbsCode())) {
            entity.setWbsCode(generateWbsCode(entity.getProjectTemplateId(), entity.getParentId()));
        }

        // 设置默认节点类型
        if (entity.getNodeType() == null) {
            entity.setNodeType(1); // 默认为活动
        }

        // 设置默认里程碑标识
        if (entity.getIsMilestone() == null) {
            entity.setIsMilestone(0); // 默认不是里程碑
        }

        // 生成ID并保存
        String id = RandomUtil.snowId();
        entity.setId(id);
        save(entity);

        log.info("创建WBS计划成功，ID: {}, 名称: {}", id, entity.getName());
        return id;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(String id, ProjectSchemaWbsEntity entity) {
        if (StrXhUtil.isEmpty(id) || entity == null) {
            throw new RuntimeException("参数不能为空");
        }

        ProjectSchemaWbsEntity existingEntity = getById(id);
        if (existingEntity == null) {
            throw new RuntimeException("WBS计划不存在");
        }

        // 验证必填字段
        if (StrXhUtil.isEmpty(entity.getName())) {
            throw new RuntimeException("WBS名称不能为空");
        }

        // 检查名称是否重复（排除自己）
        if (isExistByName(existingEntity.getProjectTemplateId(), entity.getName(), id)) {
            throw new RuntimeException("WBS名称在当前项目模板中已存在");
        }

        // 更新字段
        existingEntity.setName(entity.getName());
        existingEntity.setNodeType(entity.getNodeType());
        existingEntity.setIsMilestone(entity.getIsMilestone());
        existingEntity.setDuration(entity.getDuration());
        existingEntity.setPlanStartOffset(entity.getPlanStartOffset());
        existingEntity.setConstraintTypeId(entity.getConstraintTypeId());
        existingEntity.setConstraintDate(entity.getConstraintDate());
        existingEntity.setResponseRoleId(entity.getResponseRoleId());
        existingEntity.setPredecessors(entity.getPredecessors());

        updateById(existingEntity);
        log.info("更新WBS计划成功，ID: {}, 名称: {}", id, entity.getName());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        if (StrXhUtil.isEmpty(id)) {
            throw new RuntimeException("WBS计划ID不能为空");
        }

        ProjectSchemaWbsEntity entity = getById(id);
        if (entity == null) {
            throw new RuntimeException("WBS计划不存在");
        }

        // 检查是否有子节点
        if (hasChildren(id)) {
            throw new RuntimeException("存在子节点，无法删除");
        }

        removeById(id);
        log.info("删除WBS计划成功，ID: {}, 名称: {}", id, entity.getName());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            return;
        }

        for (String id : ids) {
            delete(id);
        }
        log.info("批量删除WBS计划成功，数量: {}", ids.size());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByTemplateId(String projectTemplateId) {
        if (StrXhUtil.isEmpty(projectTemplateId)) {
            return;
        }

        QueryWrapper<ProjectSchemaWbsEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ProjectSchemaWbsEntity::getProjectTemplateId, projectTemplateId);
        
        List<ProjectSchemaWbsEntity> entities = list(queryWrapper);
        if (!entities.isEmpty()) {
            remove(queryWrapper);
            log.info("根据项目模板ID删除WBS计划成功，模板ID: {}, 数量: {}", projectTemplateId, entities.size());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importFromWbsTemplate(String projectTemplateId, String wbsTemplateId, String parentId) {
        if (StrXhUtil.isEmpty(projectTemplateId) || StrXhUtil.isEmpty(wbsTemplateId)) {
            throw new RuntimeException("项目模板ID和WBS模板ID不能为空");
        }

        // 获取WBS模板明细列表
        List<WbsTemplateDetailEntity> templateDetails = wbsTemplateMasterService.getWbsDetails(wbsTemplateId);
        if (templateDetails.isEmpty()) {
            throw new RuntimeException("WBS模板中没有明细数据");
        }

        // 创建ID映射关系（原ID -> 新ID）
        Map<String, String> idMapping = new HashMap<>();

        // 按层级排序，确保父节点先创建
        templateDetails.sort(Comparator.comparing(WbsTemplateDetailEntity::getLevel)
                .thenComparing(WbsTemplateDetailEntity::getSeqNo));

        for (WbsTemplateDetailEntity templateDetail : templateDetails) {
            ProjectSchemaWbsEntity wbsEntity = new ProjectSchemaWbsEntity();

            // 复制基本信息
            wbsEntity.setProjectTemplateId(projectTemplateId);
            wbsEntity.setSourceWbsDetailId(templateDetail.getId());
            wbsEntity.setSourceLibraryActivityId(templateDetail.getLibraryActivityId());
            wbsEntity.setName(templateDetail.getName());
            wbsEntity.setNodeType(templateDetail.getNodeType());
            wbsEntity.setIsMilestone(templateDetail.getIsMilestone());
            wbsEntity.setDuration(templateDetail.getDuration());
            wbsEntity.setPlanStartOffset(templateDetail.getPlanStartOffset());
            wbsEntity.setConstraintTypeId(templateDetail.getConstraintTypeId());
            wbsEntity.setConstraintDate(templateDetail.getConstraintDate());
            wbsEntity.setResponseRoleId(templateDetail.getResponseRoleId());
            wbsEntity.setPredecessors(templateDetail.getPredecessors());

            // 处理父级关系
            if (StrXhUtil.isNotEmpty(templateDetail.getParentId())) {
                String newParentId = idMapping.get(templateDetail.getParentId());
                wbsEntity.setParentId(newParentId);
            } else {
                wbsEntity.setParentId(parentId);
            }

            // 设置层级和序号
            wbsEntity.setLevel(calculateLevel(wbsEntity.getParentId()));
            wbsEntity.setSeqNo(getNextSeqNo(projectTemplateId, wbsEntity.getParentId()));

            // 生成WBS编码
            wbsEntity.setWbsCode(generateWbsCode(projectTemplateId, wbsEntity.getParentId()));

            // 创建并记录ID映射
            String newId = create(wbsEntity);
            idMapping.put(templateDetail.getId(), newId);
        }

        log.info("从WBS模板导入成功，模板ID: {}, 导入数量: {}", wbsTemplateId, templateDetails.size());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addActivitiesFromLibrary(String projectTemplateId, List<String> activityIds, String parentId) {
        if (StrXhUtil.isEmpty(projectTemplateId) || activityIds == null || activityIds.isEmpty()) {
            return;
        }

        for (String activityId : activityIds) {
            ActivityLibraryEntity activity = activityLibraryService.getById(activityId);
            if (activity != null) {
                ProjectSchemaWbsEntity wbsEntity = new ProjectSchemaWbsEntity();
                wbsEntity.setProjectTemplateId(projectTemplateId);
                wbsEntity.setSourceLibraryActivityId(activityId);
                wbsEntity.setParentId(parentId);
                wbsEntity.setName(activity.getName());
                wbsEntity.setDuration(activity.getDuration());
                wbsEntity.setIsMilestone(activity.getIsMilestone());
                wbsEntity.setResponseRoleId(activity.getResponseRoleId());
                wbsEntity.setNodeType(1); // 活动类型

                // 设置层级和序号
                wbsEntity.setLevel(calculateLevel(parentId));
                wbsEntity.setSeqNo(getNextSeqNo(projectTemplateId, parentId));
                wbsEntity.setWbsCode(generateWbsCode(projectTemplateId, parentId));

                create(wbsEntity);
            }
        }

        log.info("从活动库添加活动成功，数量: {}", activityIds.size());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void adjustSeqNo(String id, String direction) {
        if (StrXhUtil.isEmpty(id) || StrXhUtil.isEmpty(direction)) {
            throw new RuntimeException("参数不能为空");
        }

        ProjectSchemaWbsEntity entity = getById(id);
        if (entity == null) {
            throw new RuntimeException("WBS计划不存在");
        }

        // 获取同级节点列表
        QueryWrapper<ProjectSchemaWbsEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<ProjectSchemaWbsEntity> lambda = queryWrapper.lambda()
                .eq(ProjectSchemaWbsEntity::getProjectTemplateId, entity.getProjectTemplateId());

        if (StrXhUtil.isNotEmpty(entity.getParentId())) {
            lambda.eq(ProjectSchemaWbsEntity::getParentId, entity.getParentId());
        } else {
            lambda.isNull(ProjectSchemaWbsEntity::getParentId);
        }

        lambda.orderByAsc(ProjectSchemaWbsEntity::getSeqNo);
        List<ProjectSchemaWbsEntity> siblings = list(queryWrapper);

        // 找到当前节点在列表中的位置
        int currentIndex = -1;
        for (int i = 0; i < siblings.size(); i++) {
            if (siblings.get(i).getId().equals(id)) {
                currentIndex = i;
                break;
            }
        }

        if (currentIndex == -1) {
            throw new RuntimeException("未找到当前节点");
        }

        // 执行移动
        if ("up".equals(direction) && currentIndex > 0) {
            // 上移：与前一个节点交换序号
            ProjectSchemaWbsEntity prevEntity = siblings.get(currentIndex - 1);
            Integer tempSeqNo = entity.getSeqNo();
            entity.setSeqNo(prevEntity.getSeqNo());
            prevEntity.setSeqNo(tempSeqNo);

            updateById(entity);
            updateById(prevEntity);

        } else if ("down".equals(direction) && currentIndex < siblings.size() - 1) {
            // 下移：与后一个节点交换序号
            ProjectSchemaWbsEntity nextEntity = siblings.get(currentIndex + 1);
            Integer tempSeqNo = entity.getSeqNo();
            entity.setSeqNo(nextEntity.getSeqNo());
            nextEntity.setSeqNo(tempSeqNo);

            updateById(entity);
            updateById(nextEntity);
        }

        log.info("调整WBS序号成功，ID: {}, 方向: {}", id, direction);
    }

    @Override
    public Integer getNextSeqNo(String projectTemplateId, String parentId) {
        if (StrXhUtil.isEmpty(projectTemplateId)) {
            return 1;
        }

        QueryWrapper<ProjectSchemaWbsEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<ProjectSchemaWbsEntity> lambda = queryWrapper.lambda()
                .eq(ProjectSchemaWbsEntity::getProjectTemplateId, projectTemplateId);

        if (StrXhUtil.isNotEmpty(parentId)) {
            lambda.eq(ProjectSchemaWbsEntity::getParentId, parentId);
        } else {
            lambda.isNull(ProjectSchemaWbsEntity::getParentId);
        }

        lambda.orderByDesc(ProjectSchemaWbsEntity::getSeqNo).last("LIMIT 1");

        ProjectSchemaWbsEntity lastEntity = getOne(queryWrapper);
        return lastEntity != null ? lastEntity.getSeqNo() + 1 : 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recalculateWbsCode(String projectTemplateId) {
        if (StrXhUtil.isEmpty(projectTemplateId)) {
            return;
        }

        List<ProjectSchemaWbsEntity> wbsList = getWbsTree(projectTemplateId);
        recalculateWbsCodeRecursive(wbsList, null, "");

        log.info("重新计算WBS编码完成，项目模板ID: {}", projectTemplateId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recalculateLevel(String projectTemplateId) {
        if (StrXhUtil.isEmpty(projectTemplateId)) {
            return;
        }

        List<ProjectSchemaWbsEntity> wbsList = getWbsTree(projectTemplateId);
        recalculateLevelRecursive(wbsList, 1);

        log.info("重新计算层级深度完成，项目模板ID: {}", projectTemplateId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void moveNode(String id, String newParentId, Integer newSeqNo) {
        if (StrXhUtil.isEmpty(id)) {
            throw new RuntimeException("WBS计划ID不能为空");
        }

        ProjectSchemaWbsEntity entity = getById(id);
        if (entity == null) {
            throw new RuntimeException("WBS计划不存在");
        }

        // 检查是否移动到自己的子节点下（避免循环引用）
        if (StrXhUtil.isNotEmpty(newParentId) && isDescendant(newParentId, id)) {
            throw new RuntimeException("不能移动到自己的子节点下");
        }

        // 更新父级和层级
        entity.setParentId(newParentId);
        entity.setLevel(calculateLevel(newParentId));

        // 更新序号
        if (newSeqNo != null) {
            entity.setSeqNo(newSeqNo);
        } else {
            entity.setSeqNo(getNextSeqNo(entity.getProjectTemplateId(), newParentId));
        }

        // 重新生成WBS编码
        entity.setWbsCode(generateWbsCode(entity.getProjectTemplateId(), newParentId));

        updateById(entity);

        // 递归更新所有子节点的层级和编码
        updateChildrenLevelAndCode(id);

        log.info("移动WBS节点成功，ID: {}, 新父级: {}", id, newParentId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String copyNode(String id, String newParentId) {
        if (StrXhUtil.isEmpty(id)) {
            throw new RuntimeException("WBS计划ID不能为空");
        }

        ProjectSchemaWbsEntity sourceEntity = getById(id);
        if (sourceEntity == null) {
            throw new RuntimeException("源WBS计划不存在");
        }

        // 创建副本
        ProjectSchemaWbsEntity copyEntity = new ProjectSchemaWbsEntity();
        copyEntity.setProjectTemplateId(sourceEntity.getProjectTemplateId());
        copyEntity.setSourceWbsDetailId(sourceEntity.getSourceWbsDetailId());
        copyEntity.setSourceLibraryActivityId(sourceEntity.getSourceLibraryActivityId());
        copyEntity.setParentId(newParentId);
        copyEntity.setName(sourceEntity.getName() + "_副本");
        copyEntity.setNodeType(sourceEntity.getNodeType());
        copyEntity.setIsMilestone(sourceEntity.getIsMilestone());
        copyEntity.setDuration(sourceEntity.getDuration());
        copyEntity.setPlanStartOffset(sourceEntity.getPlanStartOffset());
        copyEntity.setConstraintTypeId(sourceEntity.getConstraintTypeId());
        copyEntity.setConstraintDate(sourceEntity.getConstraintDate());
        copyEntity.setResponseRoleId(sourceEntity.getResponseRoleId());
        copyEntity.setPredecessors(sourceEntity.getPredecessors());

        String newId = create(copyEntity);

        // 递归复制子节点
        List<ProjectSchemaWbsEntity> children = getChildren(id);
        for (ProjectSchemaWbsEntity child : children) {
            copyNode(child.getId(), newId);
        }

        log.info("复制WBS节点成功，源ID: {}, 新ID: {}", id, newId);
        return newId;
    }

    @Override
    public List<ProjectSchemaWbsEntity> getWbsTree(String projectTemplateId) {
        if (StrXhUtil.isEmpty(projectTemplateId)) {
            return new ArrayList<>();
        }

        List<ProjectSchemaWbsEntity> allNodes = getListByTemplateId(projectTemplateId);
        return buildTree(allNodes, null);
    }

    @Override
    public List<ProjectSchemaWbsEntity> getChildren(String parentId) {
        if (StrXhUtil.isEmpty(parentId)) {
            return new ArrayList<>();
        }

        QueryWrapper<ProjectSchemaWbsEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(ProjectSchemaWbsEntity::getParentId, parentId)
                .orderByAsc(ProjectSchemaWbsEntity::getSeqNo);

        return list(queryWrapper);
    }

    @Override
    public boolean hasChildren(String parentId) {
        if (StrXhUtil.isEmpty(parentId)) {
            return false;
        }

        QueryWrapper<ProjectSchemaWbsEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(ProjectSchemaWbsEntity::getParentId, parentId)
                .last("LIMIT 1");

        return count(queryWrapper) > 0;
    }

    @Override
    public Map<String, Object> getWbsStatistics(String projectTemplateId) {
        Map<String, Object> statistics = new HashMap<>();

        if (StrXhUtil.isEmpty(projectTemplateId)) {
            return statistics;
        }

        List<ProjectSchemaWbsEntity> wbsList = getListByTemplateId(projectTemplateId);

        // 总数量
        statistics.put("totalCount", wbsList.size());

        // 里程碑数量
        long milestoneCount = wbsList.stream()
                .filter(wbs -> wbs.getIsMilestone() != null && wbs.getIsMilestone() == 1)
                .count();
        statistics.put("milestoneCount", milestoneCount);

        // 总工期
        BigDecimal totalDuration = wbsList.stream()
                .filter(wbs -> wbs.getDuration() != null)
                .map(ProjectSchemaWbsEntity::getDuration)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        statistics.put("totalDuration", totalDuration);

        // 最大层级
        int maxLevel = wbsList.stream()
                .filter(wbs -> wbs.getLevel() != null)
                .mapToInt(ProjectSchemaWbsEntity::getLevel)
                .max()
                .orElse(0);
        statistics.put("maxLevel", maxLevel);

        // 节点类型统计
        Map<Integer, Long> nodeTypeCount = wbsList.stream()
                .filter(wbs -> wbs.getNodeType() != null)
                .collect(Collectors.groupingBy(ProjectSchemaWbsEntity::getNodeType, Collectors.counting()));
        statistics.put("nodeTypeCount", nodeTypeCount);

        return statistics;
    }

    /**
     * 递归重新计算WBS编码
     */
    private void recalculateWbsCodeRecursive(List<ProjectSchemaWbsEntity> nodes, String parentCode, String prefix) {
        if (nodes == null || nodes.isEmpty()) {
            return;
        }

        int index = 1;
        for (ProjectSchemaWbsEntity node : nodes) {
            String newCode;
            if (StrXhUtil.isEmpty(parentCode)) {
                newCode = String.valueOf(index);
            } else {
                newCode = parentCode + "." + index;
            }

            node.setWbsCode(newCode);
            updateById(node);

            // 递归处理子节点
            List<ProjectSchemaWbsEntity> children = getChildren(node.getId());
            if (!children.isEmpty()) {
                recalculateWbsCodeRecursive(children, newCode, prefix);
            }

            index++;
        }
    }

    /**
     * 递归重新计算层级深度
     */
    private void recalculateLevelRecursive(List<ProjectSchemaWbsEntity> nodes, int level) {
        if (nodes == null || nodes.isEmpty()) {
            return;
        }

        for (ProjectSchemaWbsEntity node : nodes) {
            node.setLevel(level);
            updateById(node);

            // 递归处理子节点
            List<ProjectSchemaWbsEntity> children = getChildren(node.getId());
            if (!children.isEmpty()) {
                recalculateLevelRecursive(children, level + 1);
            }
        }
    }

    /**
     * 检查是否为子孙节点
     */
    private boolean isDescendant(String ancestorId, String descendantId) {
        if (StrXhUtil.isEmpty(ancestorId) || StrXhUtil.isEmpty(descendantId)) {
            return false;
        }

        ProjectSchemaWbsEntity ancestor = getById(ancestorId);
        if (ancestor == null) {
            return false;
        }

        // 向上查找父节点
        String currentParentId = ancestor.getParentId();
        while (StrXhUtil.isNotEmpty(currentParentId)) {
            if (currentParentId.equals(descendantId)) {
                return true;
            }

            ProjectSchemaWbsEntity parent = getById(currentParentId);
            if (parent == null) {
                break;
            }
            currentParentId = parent.getParentId();
        }

        return false;
    }

    /**
     * 递归更新子节点的层级和编码
     */
    private void updateChildrenLevelAndCode(String parentId) {
        List<ProjectSchemaWbsEntity> children = getChildren(parentId);
        for (ProjectSchemaWbsEntity child : children) {
            // 更新层级
            child.setLevel(calculateLevel(child.getParentId()));

            // 重新生成WBS编码
            child.setWbsCode(generateWbsCode(child.getProjectTemplateId(), child.getParentId()));

            updateById(child);

            // 递归更新子节点
            updateChildrenLevelAndCode(child.getId());
        }
    }

    /**
     * 构建树形结构
     */
    private List<ProjectSchemaWbsEntity> buildTree(List<ProjectSchemaWbsEntity> allNodes, String parentId) {
        List<ProjectSchemaWbsEntity> result = new ArrayList<>();

        for (ProjectSchemaWbsEntity node : allNodes) {
            if (Objects.equals(node.getParentId(), parentId)) {
                // 递归查找子节点
                List<ProjectSchemaWbsEntity> children = buildTree(allNodes, node.getId());
                // 这里可以设置children属性，如果Entity有的话
                result.add(node);
            }
        }

        // 按序号排序
        result.sort(Comparator.comparing(ProjectSchemaWbsEntity::getSeqNo, Comparator.nullsLast(Comparator.naturalOrder())));

        return result;
    }

    @Override
    public Map<String, Object> validateWbsStructure(String projectTemplateId) {
        Map<String, Object> result = new HashMap<>();
        List<String> errors = new ArrayList<>();
        List<String> warnings = new ArrayList<>();

        if (StrXhUtil.isEmpty(projectTemplateId)) {
            errors.add("项目模板ID不能为空");
            result.put("valid", false);
            result.put("errors", errors);
            return result;
        }

        List<ProjectSchemaWbsEntity> wbsList = getListByTemplateId(projectTemplateId);

        // 检查是否有重复的WBS编码
        Map<String, Long> codeCount = wbsList.stream()
                .filter(wbs -> StrXhUtil.isNotEmpty(wbs.getWbsCode()))
                .collect(Collectors.groupingBy(ProjectSchemaWbsEntity::getWbsCode, Collectors.counting()));

        codeCount.entrySet().stream()
                .filter(entry -> entry.getValue() > 1)
                .forEach(entry -> errors.add("WBS编码重复: " + entry.getKey()));

        // 检查是否有重复的名称
        Map<String, Long> nameCount = wbsList.stream()
                .filter(wbs -> StrXhUtil.isNotEmpty(wbs.getName()))
                .collect(Collectors.groupingBy(ProjectSchemaWbsEntity::getName, Collectors.counting()));

        nameCount.entrySet().stream()
                .filter(entry -> entry.getValue() > 1)
                .forEach(entry -> warnings.add("WBS名称重复: " + entry.getKey()));

        // 检查父子关系完整性
        for (ProjectSchemaWbsEntity wbs : wbsList) {
            if (StrXhUtil.isNotEmpty(wbs.getParentId())) {
                boolean parentExists = wbsList.stream()
                        .anyMatch(parent -> parent.getId().equals(wbs.getParentId()));
                if (!parentExists) {
                    errors.add("WBS节点 " + wbs.getName() + " 的父节点不存在");
                }
            }
        }

        result.put("valid", errors.isEmpty());
        result.put("errors", errors);
        result.put("warnings", warnings);
        result.put("totalCount", wbsList.size());

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSave(String projectTemplateId, List<ProjectSchemaWbsEntity> wbsList) {
        if (StrXhUtil.isEmpty(projectTemplateId) || wbsList == null || wbsList.isEmpty()) {
            return;
        }

        // 先删除现有的WBS配置
        deleteByTemplateId(projectTemplateId);

        // 批量保存新的配置
        for (ProjectSchemaWbsEntity wbs : wbsList) {
            wbs.setProjectTemplateId(projectTemplateId);
            if (StrXhUtil.isEmpty(wbs.getId())) {
                create(wbs);
            } else {
                save(wbs);
            }
        }

        log.info("批量保存WBS计划成功，项目模板ID: {}, 数量: {}", projectTemplateId, wbsList.size());
    }

    @Override
    public List<ProjectSchemaWbsEntity> getWbsPath(String id) {
        List<ProjectSchemaWbsEntity> path = new ArrayList<>();

        if (StrXhUtil.isEmpty(id)) {
            return path;
        }

        ProjectSchemaWbsEntity current = getById(id);
        while (current != null) {
            path.add(0, current); // 插入到开头，保持从根到叶的顺序

            if (StrXhUtil.isEmpty(current.getParentId())) {
                break;
            }

            current = getById(current.getParentId());
        }

        return path;
    }

    @Override
    public boolean isExistByName(String projectTemplateId, String name, String excludeId) {
        if (StrXhUtil.isEmpty(projectTemplateId) || StrXhUtil.isEmpty(name)) {
            return false;
        }

        QueryWrapper<ProjectSchemaWbsEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<ProjectSchemaWbsEntity> lambda = queryWrapper.lambda()
                .eq(ProjectSchemaWbsEntity::getProjectTemplateId, projectTemplateId)
                .eq(ProjectSchemaWbsEntity::getName, name);

        if (StrXhUtil.isNotEmpty(excludeId)) {
            lambda.ne(ProjectSchemaWbsEntity::getId, excludeId);
        }

        return count(queryWrapper) > 0;
    }

    @Override
    public boolean isExistByWbsCode(String projectTemplateId, String wbsCode, String excludeId) {
        if (StrXhUtil.isEmpty(projectTemplateId) || StrXhUtil.isEmpty(wbsCode)) {
            return false;
        }

        QueryWrapper<ProjectSchemaWbsEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<ProjectSchemaWbsEntity> lambda = queryWrapper.lambda()
                .eq(ProjectSchemaWbsEntity::getProjectTemplateId, projectTemplateId)
                .eq(ProjectSchemaWbsEntity::getWbsCode, wbsCode);

        if (StrXhUtil.isNotEmpty(excludeId)) {
            lambda.ne(ProjectSchemaWbsEntity::getId, excludeId);
        }

        return count(queryWrapper) > 0;
    }

    @Override
    public String generateWbsCode(String projectTemplateId, String parentId) {
        if (StrXhUtil.isEmpty(projectTemplateId)) {
            return "1";
        }

        if (StrXhUtil.isEmpty(parentId)) {
            // 根节点，查找同级最大编码
            QueryWrapper<ProjectSchemaWbsEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda()
                    .eq(ProjectSchemaWbsEntity::getProjectTemplateId, projectTemplateId)
                    .isNull(ProjectSchemaWbsEntity::getParentId)
                    .orderByDesc(ProjectSchemaWbsEntity::getWbsCode)
                    .last("LIMIT 1");

            ProjectSchemaWbsEntity lastEntity = getOne(queryWrapper);
            if (lastEntity != null && StrXhUtil.isNotEmpty(lastEntity.getWbsCode())) {
                try {
                    int lastCode = Integer.parseInt(lastEntity.getWbsCode());
                    return String.valueOf(lastCode + 1);
                } catch (NumberFormatException e) {
                    // 如果解析失败，返回默认值
                    return "1";
                }
            }
            return "1";
        } else {
            // 子节点，基于父节点编码生成
            ProjectSchemaWbsEntity parent = getById(parentId);
            if (parent == null || StrXhUtil.isEmpty(parent.getWbsCode())) {
                return "1.1";
            }

            // 查找同级最大编码
            QueryWrapper<ProjectSchemaWbsEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda()
                    .eq(ProjectSchemaWbsEntity::getProjectTemplateId, projectTemplateId)
                    .eq(ProjectSchemaWbsEntity::getParentId, parentId)
                    .orderByDesc(ProjectSchemaWbsEntity::getWbsCode)
                    .last("LIMIT 1");

            ProjectSchemaWbsEntity lastSibling = getOne(queryWrapper);
            if (lastSibling != null && StrXhUtil.isNotEmpty(lastSibling.getWbsCode())) {
                String[] parts = lastSibling.getWbsCode().split("\\.");
                if (parts.length > 0) {
                    try {
                        int lastSubCode = Integer.parseInt(parts[parts.length - 1]);
                        return parent.getWbsCode() + "." + (lastSubCode + 1);
                    } catch (NumberFormatException e) {
                        return parent.getWbsCode() + ".1";
                    }
                }
            }
            return parent.getWbsCode() + ".1";
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePredecessors(String id, String predecessors) {
        if (StrXhUtil.isEmpty(id)) {
            throw new RuntimeException("WBS计划ID不能为空");
        }

        ProjectSchemaWbsEntity entity = getById(id);
        if (entity == null) {
            throw new RuntimeException("WBS计划不存在");
        }

        // 验证前置任务是否存在循环依赖
        if (StrXhUtil.isNotEmpty(predecessors)) {
            // 这里可以解析predecessors JSON并检查循环依赖
            // 简化处理，直接更新
        }

        entity.setPredecessors(predecessors);
        updateById(entity);

        log.info("更新前置任务关系成功，WBS ID: {}", id);
    }

    @Override
    public List<Map<String, Object>> getPredecessors(String id) {
        List<Map<String, Object>> result = new ArrayList<>();

        if (StrXhUtil.isEmpty(id)) {
            return result;
        }

        ProjectSchemaWbsEntity entity = getById(id);
        if (entity == null || StrXhUtil.isEmpty(entity.getPredecessors())) {
            return result;
        }

        // 这里应该解析predecessors JSON字符串
        // 简化处理，返回空列表
        // 实际实现中需要根据JSON格式解析前置任务信息

        return result;
    }

    @Override
    public boolean checkCircularDependency(String id, String predecessorId) {
        if (StrXhUtil.isEmpty(id) || StrXhUtil.isEmpty(predecessorId)) {
            return false;
        }

        // 检查是否直接循环
        if (id.equals(predecessorId)) {
            return true;
        }

        // 递归检查间接循环
        ProjectSchemaWbsEntity predecessor = getById(predecessorId);
        if (predecessor == null || StrXhUtil.isEmpty(predecessor.getPredecessors())) {
            return false;
        }

        // 这里应该解析predecessors JSON并递归检查
        // 简化处理，返回false
        // 实际实现中需要根据JSON格式递归检查循环依赖

        return false;
    }

    /**
     * 计算层级深度
     */
    private Integer calculateLevel(String parentId) {
        if (StrXhUtil.isEmpty(parentId)) {
            return 1; // 根节点层级为1
        }

        ProjectSchemaWbsEntity parent = getById(parentId);
        if (parent == null) {
            return 1;
        }

        return parent.getLevel() + 1;
    }
}
