<template>
  <div class="opportunity-detail">
    <!-- 加载状态 -->
    <a-spin :spinning="loading">
      <!-- 商机不存在提示 -->
      <a-result v-if="!hasOpportunity && !loading" status="info" title="该项目暂无关联商机" sub-title="只有商机类型的项目才会有关联的商机详情信息">
        <template #extra>
          <a-button type="primary" @click="goBack"> 返回概览 </a-button>
        </template>
      </a-result>

      <!-- 商机详情内容 -->
      <div v-else-if="hasOpportunity">
        <!-- 页面头部 -->
        <div class="page-header">
          <div class="header-content">
            <div class="header-left">
              <h1>{{ opportunityInfo.projectName }}</h1>
              <a-space size="large">
                <span>商机编号：{{ opportunityInfo.businessNo }}</span>
                <a-tag :color="getStatusColor(opportunityInfo.status)">
                  {{ getStatusName(opportunityInfo.status) }}
                </a-tag>
              </a-space>
            </div>
            <div class="header-right">
              <a-space>
                <a-button type="primary" @click="handleEdit">
                  <edit-outlined />
                  编辑商机
                </a-button>
                <a-button @click="handlePrint">
                  <printer-outlined />
                  打印
                </a-button>
              </a-space>
            </div>
          </div>
        </div>

        <!-- 标签页内容 -->
        <a-tabs v-model:activeKey="activeTab" type="card">
          <!-- 基本信息 -->
          <a-tab-pane key="basic" tab="基本信息">
            <div class="tab-content">
              <a-row :gutter="24">
                <a-col :span="12">
                  <a-card title="项目信息" size="small">
                    <a-descriptions :column="1" size="small">
                      <a-descriptions-item label="项目名称">
                        {{ opportunityInfo.projectName }}
                      </a-descriptions-item>
                      <a-descriptions-item label="商机编号">
                        {{ opportunityInfo.businessNo }}
                      </a-descriptions-item>
                      <a-descriptions-item label="项目类型">
                        {{ getProjectTypeName(opportunityInfo.projType) }}
                      </a-descriptions-item>
                      <a-descriptions-item label="项目等级">
                        <a-tag :color="getProjectLevelColor(opportunityInfo.projectLevel)">
                          {{ opportunityInfo.projectLevel }}
                        </a-tag>
                      </a-descriptions-item>
                      <a-descriptions-item label="项目状态">
                        <a-tag :color="getStatusColor(opportunityInfo.status)">
                          {{ getStatusName(opportunityInfo.status) }}
                        </a-tag>
                      </a-descriptions-item>
                      <a-descriptions-item label="工时状态">
                        <a-tag :color="opportunityInfo.workStatus === 1 ? 'success' : 'default'">
                          {{ opportunityInfo.workStatus === 1 ? '可填写' : '已结束' }}
                        </a-tag>
                      </a-descriptions-item>
                    </a-descriptions>
                  </a-card>
                </a-col>
                <a-col :span="12">
                  <a-card title="客户信息" size="small">
                    <a-descriptions :column="1" size="small">
                      <a-descriptions-item label="客户单位">
                        {{ opportunityInfo.custName }}
                      </a-descriptions-item>
                      <a-descriptions-item label="客户地址">
                        {{ opportunityInfo.custAddress || '-' }}
                      </a-descriptions-item>
                      <a-descriptions-item label="客户联系人">
                        {{ opportunityInfo.custContact || '-' }}
                      </a-descriptions-item>
                      <a-descriptions-item label="联系电话">
                        {{ opportunityInfo.custPhone || '-' }}
                      </a-descriptions-item>
                    </a-descriptions>
                  </a-card>
                </a-col>
              </a-row>

              <a-row :gutter="24" style="margin-top: 16px">
                <a-col :span="12">
                  <a-card title="负责人信息" size="small">
                    <a-descriptions :column="1" size="small">
                      <a-descriptions-item label="市场负责人">
                        {{ opportunityInfo.marketLinkmanName }}
                      </a-descriptions-item>
                      <a-descriptions-item label="售前负责人">
                        {{ opportunityInfo.presaleLinkmanName }}
                      </a-descriptions-item>
                      <a-descriptions-item label="项目负责人">
                        {{ opportunityInfo.projectLeaderName }}
                      </a-descriptions-item>
                      <a-descriptions-item label="所属分部">
                        {{ opportunityInfo.deptName }}
                      </a-descriptions-item>
                      <a-descriptions-item label="研发分部">
                        {{ opportunityInfo.yfDeptName }}
                      </a-descriptions-item>
                    </a-descriptions>
                  </a-card>
                </a-col>
                <a-col :span="12">
                  <a-card title="关键时间" size="small">
                    <a-descriptions :column="1" size="small">
                      <a-descriptions-item label="启动日期">
                        {{ formatDate(opportunityInfo.startDate) }}
                      </a-descriptions-item>
                      <a-descriptions-item label="预计落地日期">
                        {{ formatDate(opportunityInfo.evaSignMonth) }}
                      </a-descriptions-item>
                      <a-descriptions-item label="合同审核日期">
                        {{ formatDate(opportunityInfo.checkDate) }}
                      </a-descriptions-item>
                      <a-descriptions-item label="交底日期">
                        {{ formatDate(opportunityInfo.transDate) }}
                      </a-descriptions-item>
                    </a-descriptions>
                  </a-card>
                </a-col>
              </a-row>
            </div>
          </a-tab-pane>

          <!-- 财务信息 -->
          <a-tab-pane key="finance" tab="财务信息">
            <div class="tab-content">
              <a-row :gutter="24">
                <a-col :span="12">
                  <a-card title="收款计划" size="small">
                    <a-descriptions :column="1" size="small">
                      <a-descriptions-item label="首笔回款时间">
                        {{ formatDate(opportunityInfo.evaFirstMonth) }}
                      </a-descriptions-item>
                      <a-descriptions-item label="首笔回款金额">
                        {{ formatMoney(opportunityInfo.evaFirstAmount) }}
                      </a-descriptions-item>
                      <a-descriptions-item label="二笔回款时间">
                        {{ formatDate(opportunityInfo.evaSecondMonth) }}
                      </a-descriptions-item>
                      <a-descriptions-item label="二笔回款金额">
                        {{ formatMoney(opportunityInfo.evaSecondAmount) }}
                      </a-descriptions-item>
                      <a-descriptions-item label="今年收款比例">
                        {{ opportunityInfo.yearMoneyRatio }}
                      </a-descriptions-item>
                      <a-descriptions-item label="今年收款金额">
                        {{ formatMoney(opportunityInfo.yearMoney) }}
                      </a-descriptions-item>
                    </a-descriptions>
                  </a-card>
                </a-col>
                <a-col :span="12">
                  <a-card title="外采计划" size="small">
                    <a-descriptions :column="1" size="small">
                      <a-descriptions-item label="首次外采时间">
                        {{ formatDate(opportunityInfo.evaFirstexternalMonth) }}
                      </a-descriptions-item>
                      <a-descriptions-item label="首次外采金额">
                        {{ formatMoney(opportunityInfo.evaFirstexternalAmount) }}
                      </a-descriptions-item>
                      <a-descriptions-item label="二次外采时间">
                        {{ formatDate(opportunityInfo.evaSecondexternalMonth) }}
                      </a-descriptions-item>
                      <a-descriptions-item label="二次外采金额">
                        {{ formatMoney(opportunityInfo.evaSecondexternalAmount) }}
                      </a-descriptions-item>
                      <a-descriptions-item label="外采总金额">
                        {{ formatMoney(opportunityInfo.purchaseMoney) }}
                      </a-descriptions-item>
                      <a-descriptions-item label="预计毛利">
                        {{ formatMoney(opportunityInfo.profitMoney) }}
                      </a-descriptions-item>
                    </a-descriptions>
                  </a-card>
                </a-col>
              </a-row>
            </div>
          </a-tab-pane>

          <!-- 合同分配 -->
          <a-tab-pane key="allocation" tab="合同分配">
            <div class="tab-content">
              <a-row :gutter="24">
                <a-col :span="12">
                  <a-card title="分部金额分配" size="small">
                    <a-descriptions :column="1" size="small">
                      <a-descriptions-item label="软件部总金额">
                        {{ formatMoney(opportunityInfo.deptMoney) }}
                      </a-descriptions-item>
                      <a-descriptions-item label="一部金额">
                        {{ formatMoney(opportunityInfo.yfYbAmount) }}
                      </a-descriptions-item>
                      <a-descriptions-item label="二部金额">
                        {{ formatMoney(opportunityInfo.yfEbAmount) }}
                      </a-descriptions-item>
                      <a-descriptions-item label="交付金额">
                        {{ formatMoney(opportunityInfo.yfJfAmount) }}
                      </a-descriptions-item>
                      <a-descriptions-item label="综合金额">
                        {{ formatMoney(opportunityInfo.yfOtherAmount) }}
                      </a-descriptions-item>
                    </a-descriptions>
                  </a-card>
                </a-col>
                <a-col :span="12">
                  <a-card title="外采分配" size="small">
                    <a-descriptions :column="1" size="small">
                      <a-descriptions-item label="一部外采">
                        {{ formatMoney(opportunityInfo.outYbAmount) }}
                      </a-descriptions-item>
                      <a-descriptions-item label="二部外采">
                        {{ formatMoney(opportunityInfo.outEbAmount) }}
                      </a-descriptions-item>
                      <a-descriptions-item label="交付外采">
                        {{ formatMoney(opportunityInfo.outJfAmount) }}
                      </a-descriptions-item>
                      <a-descriptions-item label="综合外采">
                        {{ formatMoney(opportunityInfo.outOtherAmount) }}
                      </a-descriptions-item>
                    </a-descriptions>
                  </a-card>
                </a-col>
              </a-row>
            </div>
          </a-tab-pane>

          <!-- 项目描述 -->
          <a-tab-pane key="description" tab="项目描述">
            <div class="tab-content">
              <a-card title="项目简介" size="small">
                <div class="project-description">
                  <p>{{ opportunityInfo.projectContent }}</p>
                </div>
              </a-card>

              <a-card title="商机标签" size="small" style="margin-top: 16px">
                <a-space>
                  <a-tag v-for="tag in getBusinessTags()" :key="tag" :color="getBusinessTagColor(tag)" size="large">
                    {{ tag }}
                  </a-tag>
                </a-space>
              </a-card>

              <a-card title="备注信息" size="small" style="margin-top: 16px" v-if="opportunityInfo.note">
                <div class="note-content">
                  <p>{{ opportunityInfo.note }}</p>
                </div>
              </a-card>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
    </a-spin>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, watch, inject } from 'vue';
  import { EditOutlined, PrinterOutlined } from '@ant-design/icons-vue';
  import { formatToDate } from '/@/utils/dateUtil';
  import { message } from 'ant-design-vue';
  import { getBusinessInfoByProjectId, type BusinessModel } from '/@/api/project/business';
  import { useMessage } from '/@/hooks/web/useMessage';

  const props = defineProps<{
    projectId?: string;
  }>();

  const { createMessage } = useMessage();

  // 从父组件注入项目ID
  const projectId = inject('projectId', ref(''));

  const hasOpportunity = ref(false);
  const opportunityInfo = ref<BusinessModel>({} as BusinessModel);
  const activeTab = ref('basic');
  const loading = ref(false);

  // 格式化日期
  const formatDate = (date: string) => {
    return date ? formatToDate(date) : '-';
  };

  // 格式化金额
  const formatMoney = (amount: number) => {
    if (!amount) return '0万元';
    return `${amount.toFixed(1)}万元`;
  };

  // 获取项目类型名称
  const getProjectTypeName = (type: number) => {
    const typeMap = {
      1: '建设商机',
      2: '维护商机',
      3: '产品销售商机',
    };
    return typeMap[type] || '未知类型';
  };

  // 获取项目等级颜色
  const getProjectLevelColor = (level: string) => {
    const colorMap = {
      'A+': 'red',
      A: 'orange',
      B: 'blue',
      C: 'green',
      D: 'gray',
    };
    return colorMap[level] || 'default';
  };

  // 获取状态名称
  const getStatusName = (status: string) => {
    const statusMap = {
      '1': '跟踪中',
      '2': '方案报价中',
      '3': '商务谈判中',
      '4': '已签',
      '5': '已废弃',
      '6': '明年跟踪',
    };
    return statusMap[status] || '未知状态';
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    const colorMap = {
      '1': 'processing',
      '2': 'warning',
      '3': 'cyan',
      '4': 'success',
      '5': 'error',
      '6': 'purple',
    };
    return colorMap[status] || 'default';
  };

  // 获取商机标签
  const getBusinessTags = () => {
    if (!opportunityInfo.value.businessTag) return [];
    return opportunityInfo.value.businessTag.split(',').filter(tag => tag.trim());
  };

  // 获取商机标签颜色
  const getBusinessTagColor = (tag: string) => {
    const colorMap = {
      可靠项目: 'green',
      竞争项目: 'orange',
      暂无经费: 'red',
      智慧园区: 'blue',
      物联网: 'purple',
      大数据: 'cyan',
    };
    return colorMap[tag] || 'default';
  };

  // 操作函数
  const goBack = () => {
    console.log('返回概览');
  };

  const handleEdit = () => {
    message.info('编辑商机功能');
  };

  const handlePrint = () => {
    window.print();
  };

  // 加载商机信息
  const loadOpportunityInfo = async () => {
    if (!projectId.value) {
      console.log('⚠️ [商机详情页面] 没有项目ID');
      hasOpportunity.value = false;
      return;
    }

    loading.value = true;
    try {
      console.log('📋 [商机详情页面] 开始加载商机信息, 项目ID:', projectId.value);
      const response = await getBusinessInfoByProjectId(projectId.value);

      if (response && response.code === 200 && response.data && response.data.list && response.data.list.length > 0) {
        // 找到了商机信息
        opportunityInfo.value = response.data.list[0];
        hasOpportunity.value = true;
        console.log('✅ [商机详情页面] 商机信息加载成功:', opportunityInfo.value);
      } else {
        // 没有找到商机信息
        hasOpportunity.value = false;
        console.log('⚠️ [商机详情页面] 该项目没有关联的商机信息');
      }
    } catch (error) {
      console.error('❌ [商机详情页面] 加载商机信息失败:', error);
      createMessage.error('加载商机信息失败');
      hasOpportunity.value = false;
    } finally {
      loading.value = false;
    }
  };

  // 监听项目ID变化
  watch(
    projectId,
    (newProjectId, oldProjectId) => {
      console.log('🔄 [商机详情页面] 项目ID变化:', { 旧项目: oldProjectId, 新项目: newProjectId });
      if (newProjectId && newProjectId !== oldProjectId) {
        loadOpportunityInfo();
      }
    },
    { immediate: true },
  );

  onMounted(() => {
    if (projectId.value) {
      loadOpportunityInfo();
    }
  });
</script>

<style lang="less" scoped>
  .opportunity-detail {
    padding: 20px;

    .page-header {
      margin-bottom: 24px;

      .header-content {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 20px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        .header-left {
          h1 {
            margin: 0 0 8px 0;
            font-size: 24px;
            font-weight: 500;
          }
        }
      }
    }

    .tab-content {
      padding: 20px;
      background: #fff;
      border-radius: 8px;
    }

    .project-description {
      p {
        line-height: 1.8;
        color: #333;
        margin-bottom: 16px;
      }
    }

    .note-content {
      p {
        line-height: 1.6;
        color: #666;
      }
    }
  }
</style>
