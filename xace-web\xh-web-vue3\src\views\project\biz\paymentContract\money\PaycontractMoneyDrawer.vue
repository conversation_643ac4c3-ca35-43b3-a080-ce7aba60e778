<template>
  <BasicDrawer
    v-bind="$attrs"
    @register="registerDrawer"
    :title="getTitle"
    width="90%"
    showFooter
    :canFullscreen="true"
    :maskClosable="false"
    :keyboard="false"
    class="paycontract-money-drawer"
    @ok="handleSubmit">
    <template #footer>
      <a-space>
        <a-button @click="closeDrawer">
          <template #icon><CloseOutlined /></template>
          {{ readonly ? '关闭' : '取消' }}
        </a-button>
        <a-button v-if="!readonly" type="primary" @click="handleSubmit" :loading="loading">
          <template #icon><SaveOutlined /></template>
          {{ isUpdate ? '更新' : '保存' }}
        </a-button>
      </a-space>
    </template>

    <div class="paycontract-money-container">
      <a-spin :spinning="drawerLoading" tip="加载中...">
        <!-- 顶部概览卡片 -->
        <a-card :bordered="false" class="overview-card mb-4" v-if="!readonly">
          <template #title>
            <div class="card-title">
              <PlusOutlined v-if="!isUpdate" class="title-icon" />
              <EditOutlined v-else class="title-icon" />
              {{ isUpdate ? '编辑付款记录' : '新增付款记录' }}
            </div>
          </template>
          <template #extra>
            <a-space>
              <a-tag color="blue">
                <MoneyCollectOutlined class="tag-icon" />
                付款管理
              </a-tag>
            </a-space>
          </template>

          <div class="overview-content">
            <div class="overview-text">
              <p>{{ isUpdate ? '正在编辑付款记录信息，请在下方表单中修改相关字段。' : '请填写完整的付款记录信息，系统将自动进行数据验证。' }}</p>
            </div>
          </div>
        </a-card>

        <!-- 查看模式的详情卡片 -->
        <a-card :bordered="false" class="detail-card mb-4" v-if="readonly && paymentInfo">
          <template #title>
            <div class="card-title">
              <EyeOutlined class="title-icon" />
              付款详情 【{{ paymentInfo?.pcName || '-' }}】
            </div>
          </template>
          <template #extra>
            <a-space>
              <a-tag :color="getStatusColor(paymentInfo?.payStatus)">
                <MoneyCollectOutlined class="tag-icon" />
                {{ getStatusText(paymentInfo?.payStatus) }}
              </a-tag>
            </a-space>
          </template>

          <div class="detail-overview">
            <div class="detail-item">
              <div class="detail-label">
                <DollarCircleOutlined class="label-icon" />
                付款金额
              </div>
              <div class="detail-value amount">{{ formatAmount(paymentInfo?.cmMoney) }}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">
                <PercentageOutlined class="label-icon" />
                付款比例
              </div>
              <div class="detail-value">{{ paymentInfo?.ratio || 0 }}%</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">
                <UserOutlined class="label-icon" />
                负责人
              </div>
              <div class="detail-value">{{ paymentInfo?.ownName || '-' }}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">
                <CalendarOutlined class="label-icon" />
                付款日期
              </div>
              <div class="detail-value">{{ formatDate(paymentInfo?.fukuanDate) }}</div>
            </div>
          </div>
        </a-card>

        <!-- 标签页 -->
        <a-tabs v-model:activeKey="activeTab" type="card" class="paycontract-money-tabs" :tabBarGutter="8">
          <!-- 基本信息标签页 -->
          <a-tab-pane key="basic" class="tab-pane-content">
            <template #tab>
              <span class="tab-title">
                <InfoCircleOutlined class="tab-icon" />
                基本信息
              </span>
            </template>

            <a-card :bordered="false" class="form-card">
              <template #title>
                <div class="card-title">
                  <FormOutlined class="title-icon" />
                  付款信息
                </div>
              </template>
              <div class="form-section">
                <BasicForm @register="registerForm">
                  <template #paycontractSelect="{ model, field }">
                    <PaycontractSelect v-model:value="model[field]" placeholder="请选择采购合同" :disabled="readonly" class="enhanced-select" />
                  </template>
                  <template #userSelect="{ model, field }">
                    <UserSelect v-model:value="model[field]" placeholder="请选择负责人" :disabled="readonly" class="enhanced-select" />
                  </template>
                  <template #note="{ model, field }">
                    <a-textarea
                      v-model:value="model[field]"
                      placeholder="请输入备注信息"
                      :rows="4"
                      :maxlength="500"
                      show-count
                      class="enhanced-textarea"
                      :disabled="readonly" />
                  </template>
                </BasicForm>
              </div>
            </a-card>
          </a-tab-pane>

          <!-- 金额分配标签页 -->
          <a-tab-pane key="amount" class="tab-pane-content" v-if="!readonly">
            <template #tab>
              <span class="tab-title">
                <DollarOutlined class="tab-icon" />
                金额分配
              </span>
            </template>

            <a-card :bordered="false" class="form-card">
              <template #title>
                <div class="card-title">
                  <CalculatorOutlined class="title-icon" />
                  部门金额分配说明
                </div>
              </template>
              <div class="amount-distribution">
                <a-alert message="请合理分配各部门的付款金额，确保总金额与付款金额一致。" type="info" show-icon class="mb-4" />
                <div class="distribution-note">
                  <p>金额分配字段已包含在基本信息中，请在基本信息标签页中填写各部门的分配金额。</p>
                </div>
              </div>
            </a-card>
          </a-tab-pane>
        </a-tabs>
      </a-spin>
    </div>
  </BasicDrawer>
</template>

<script lang="ts" setup>
  import { ref, computed, unref } from 'vue';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { createPaycontractMoney, updatePaycontractMoney, getPaycontractMoneyInfo } from '/@/api/project/paycontractMoney';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { formatToDate } from '/@/utils/dateUtil';
  import PaycontractSelect from '/@/views/project/components/PaycontractSelect.vue';
  import UserSelect from '/@/components/Xh/Organize/src/UserSelect.vue';
  import type { FormSchema } from '/@/components/Table';
  import {
    PlusOutlined,
    EditOutlined,
    SaveOutlined,
    CloseOutlined,
    MoneyCollectOutlined,
    EyeOutlined,
    DollarCircleOutlined,
    PercentageOutlined,
    UserOutlined,
    CalendarOutlined,
    InfoCircleOutlined,
    FormOutlined,
    DollarOutlined,
    CalculatorOutlined,
  } from '@ant-design/icons-vue';

  const emit = defineEmits(['success', 'register']);
  const { createMessage } = useMessage();

  const isUpdate = ref(true);
  const rowId = ref('');
  const readonly = ref(false);
  const activeTab = ref('basic');
  const paymentInfo = ref<any>({});
  const loading = ref(false);
  const drawerLoading = ref(false);

  const [registerForm, { setFieldsValue, resetFields, validate, updateSchema }] = useForm({
    labelWidth: 120,
    baseColProps: { span: 24 },
    schemas: getFormSchema(),
    showActionButtonGroup: false,
  });

  // 辅助函数
  function getStatusColor(status: string | number) {
    const statusStr = String(status);
    return statusStr === '1' ? 'success' : 'warning';
  }

  function getStatusText(status: string | number) {
    const statusStr = String(status);
    return statusStr === '1' ? '已付款' : '未付款';
  }

  function formatAmount(amount: number | undefined) {
    return amount ? `¥${amount.toLocaleString('zh-CN')}` : '¥0.00';
  }

  function formatDate(date: string | undefined) {
    return date ? formatToDate(date) : '-';
  }

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async data => {
    try {
      drawerLoading.value = true;
      resetFields();
      setDrawerProps({ confirmLoading: false });

      isUpdate.value = !!data?.isUpdate;
      readonly.value = !!data?.readonly;

      if (unref(isUpdate) || readonly.value) {
        // 编辑或查看时，从后台获取最新数据
        rowId.value = data.record.id;

        console.log('获取付款记录详情，ID:', rowId.value);

        // 调用接口获取详情数据
        const detailDataRes = await getPaycontractMoneyInfo(rowId.value);

        console.log('从后台获取的详情数据:', detailDataRes);
        const detailData = detailDataRes.data;

        // 保存详情数据用于查看模式
        paymentInfo.value = detailData;

        // 设置表单值
        const formValues = {
          ...detailData,
          yufuDate: detailData.yufuDate ? formatToDate(detailData.yufuDate) : undefined,
          fukuanDate: detailData.fukuanDate ? formatToDate(detailData.fukuanDate) : undefined,
        };

        console.log('设置表单值:', formValues);
        await setFieldsValue(formValues);
      } else {
        // 新增时，清空数据
        rowId.value = '';
        paymentInfo.value = {};
        console.log('新增付款记录模式');
      }

      // 设置表单只读状态
      const schemas = getFormSchema();
      schemas.forEach(schema => {
        updateSchema({
          field: schema.field,
          componentProps: {
            ...schema.componentProps,
            disabled: readonly.value,
          },
        });
      });

      console.log('设置抽屉按钮状态:', {
        isUpdate: isUpdate.value,
        readonly: readonly.value,
        showOkBtn: !readonly.value,
        showCancelBtn: true,
      });

      setDrawerProps({
        showOkBtn: !readonly.value,
        showCancelBtn: true,
        loading: false,
      });
    } catch (error) {
      console.error('获取付款记录详情失败:', error);
      createMessage.error('获取数据失败');
      setDrawerProps({
        showOkBtn: !readonly.value,
        showCancelBtn: true,
      });
      closeDrawer();
    } finally {
      drawerLoading.value = false;
    }
  });

  const getTitle = computed(() => {
    if (readonly.value) {
      return '查看付款详情';
    }
    return !unref(isUpdate) ? '新增付款' : '编辑付款';
  });

  function getFormSchema(): FormSchema[] {
    return [
      {
        field: 'pcId',
        label: '采购合同',
        component: 'Input',
        slot: 'paycontractSelect',
        required: true,
        colProps: { span: 24 },
      },
      {
        field: 'fktj',
        label: '付款条件',
        component: 'Input',
        componentProps: {
          placeholder: '请输入付款条件',
        },
        required: true,
        colProps: { span: 24 },
      },
      {
        field: 'ratio',
        label: '付款比例(%)',
        component: 'InputNumber',
        componentProps: {
          placeholder: '请输入付款比例',
          min: 0,
          max: 100,
          precision: 2,
        },
        colProps: { span: 12 },
      },
      {
        field: 'cmMoney',
        label: '付款金额',
        component: 'InputNumber',
        componentProps: {
          placeholder: '请输入付款金额',
          min: 0,
          precision: 2,
          formatter: (value: string) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
          parser: (value: string) => value.replace(/¥\s?|(,*)/g, ''),
        },
        required: true,
        colProps: { span: 12 },
      },
      {
        field: 'ybAmount',
        label: '开发一部金额',
        component: 'InputNumber',
        componentProps: {
          placeholder: '请输入开发一部金额',
          min: 0,
          precision: 2,
          formatter: (value: string) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
          parser: (value: string) => value.replace(/¥\s?|(,*)/g, ''),
        },
        colProps: { span: 8 },
      },
      {
        field: 'ebAmount',
        label: '开发二部金额',
        component: 'InputNumber',
        componentProps: {
          placeholder: '请输入开发二部金额',
          min: 0,
          precision: 2,
          formatter: (value: string) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
          parser: (value: string) => value.replace(/¥\s?|(,*)/g, ''),
        },
        colProps: { span: 8 },
      },
      {
        field: 'otherAmount',
        label: '综合金额',
        component: 'InputNumber',
        componentProps: {
          placeholder: '请输入综合金额',
          min: 0,
          precision: 2,
          formatter: (value: string) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
          parser: (value: string) => value.replace(/¥\s?|(,*)/g, ''),
        },
        colProps: { span: 8 },
      },
      {
        field: 'payStatus',
        label: '付款状态',
        component: 'Select',
        componentProps: {
          placeholder: '请选择付款状态',
          options: [
            { fullName: '未付款', id: '0' },
            { fullName: '已付款', id: '1' },
          ],
        },
        colProps: { span: 12 },
      },
      {
        field: 'ownId',
        label: '负责人',
        component: 'Input',
        slot: 'userSelect',
        colProps: { span: 12 },
      },
      {
        field: 'yufuDate',
        label: '预付日期',
        component: 'DatePicker',
        componentProps: {
          placeholder: '请选择预付日期',
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
        },
        colProps: { span: 12 },
      },
      {
        field: 'fukuanDate',
        label: '付款日期',
        component: 'DatePicker',
        componentProps: {
          placeholder: '请选择付款日期',
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
        },
        colProps: { span: 12 },
      },
      {
        field: 'note',
        label: '备注',
        component: 'Input',
        componentProps: {
          type: 'textarea',
          placeholder: '请输入备注',
          rows: 3,
        },
        colProps: { span: 24 },
      },
    ];
  }

  async function handleSubmit() {
    try {
      const values = await validate();
      loading.value = true;
      setDrawerProps({ confirmLoading: true });

      console.log('提交的表单数据:', values);

      if (unref(isUpdate)) {
        await updatePaycontractMoney(rowId.value, values);
        createMessage.success('付款记录更新成功');
      } else {
        await createPaycontractMoney(values);
        createMessage.success('付款记录创建成功');
      }

      closeDrawer();
      emit('success');
    } catch (error) {
      console.error('提交失败:', error);
      createMessage.error(`操作失败: ${(error as any)?.message || '未知错误'}`);
    } finally {
      loading.value = false;
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>

<style lang="less" scoped>
  // 响应式设计
  @media (max-width: 1200px) {
    .paycontract-money-drawer {
      :deep(.ant-drawer) {
        width: 95% !important;
      }
    }

    .paycontract-money-container {
      padding: 16px;
    }

    .detail-overview {
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 16px;
    }

    .tab-pane-content {
      padding: 16px;
    }
  }

  @media (max-width: 768px) {
    .paycontract-money-tabs {
      :deep(.ant-tabs-nav) {
        padding: 12px 16px 0;

        .ant-tabs-tab {
          padding: 8px 12px;
          margin-right: 4px;

          .tab-title {
            font-size: 12px;

            .tab-icon {
              font-size: 14px;
            }
          }
        }
      }
    }

    .tab-pane-content {
      padding: 12px;
    }

    .detail-overview {
      grid-template-columns: 1fr;
      gap: 12px;
    }

    .card-title {
      .title-icon {
        font-size: 16px;
      }
    }

    :deep(.ant-form) {
      .ant-form-item {
        margin-bottom: 16px;
      }
    }
  }
  .paycontract-money-drawer {
    :deep(.ant-drawer-body) {
      padding: 0;
      background: #f5f7fa;
    }

    :deep(.ant-drawer-header) {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-bottom: none;

      .ant-drawer-title {
        color: #fff;
        font-weight: 600;
        font-size: 16px;
      }

      .ant-drawer-close {
        color: rgba(255, 255, 255, 0.8);

        &:hover {
          color: #fff;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 4px;
        }
      }
    }

    :deep(.ant-drawer-footer) {
      background: #fff;
      border-top: 1px solid #e8eaec;
      padding: 16px 24px;
    }
  }

  .paycontract-money-container {
    height: 100%;
    padding: 24px;
    overflow-y: auto;
  }

  .overview-card,
  .detail-card,
  .form-card {
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid #e8eaec;

    :deep(.ant-card-head) {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-bottom: 1px solid #e8eaec;
      border-radius: 12px 12px 0 0;
      padding: 16px 24px;

      .ant-card-head-title {
        padding: 0;
        font-size: 18px;
        font-weight: 600;
      }
    }

    :deep(.ant-card-body) {
      padding: 24px;
    }
  }

  .overview-content,
  .detail-overview {
    .overview-text {
      text-align: center;
      padding: 20px 0;

      p {
        color: #666;
        font-size: 14px;
        line-height: 1.6;
        margin: 0;
      }
    }
  }

  .detail-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;

    .detail-item {
      .detail-label {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
        font-weight: 500;

        .label-icon {
          margin-right: 6px;
          font-size: 16px;
          color: #1890ff;
        }
      }

      .detail-value {
        font-size: 16px;
        font-weight: 600;
        color: #2c3e50;

        &.amount {
          font-size: 18px;
          color: #fa8c16;
          font-weight: 700;
        }
      }
    }
  }

  .card-title {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #2c3e50;

    .title-icon {
      font-size: 18px;
      color: #1890ff;
    }
  }

  .tag-icon {
    margin-right: 4px;
  }

  .paycontract-money-tabs {
    height: calc(100% - 200px);

    :deep(.ant-tabs-nav) {
      background: #fff;
      margin: 0;
      padding: 16px 24px 0;
      border-bottom: 1px solid #e8eaec;
      border-radius: 12px 12px 0 0;

      .ant-tabs-tab {
        border: 1px solid #e8eaec;
        border-radius: 8px 8px 0 0;
        margin-right: 8px;
        padding: 12px 20px;
        background: #fafbfc;
        transition: all 0.3s ease;

        &:hover {
          background: #f0f2f5;
          border-color: #d9d9d9;
        }

        &.ant-tabs-tab-active {
          background: #fff;
          border-color: #1890ff;
          border-bottom-color: #fff;

          .tab-title {
            color: #1890ff;
            font-weight: 600;
          }
        }
      }
    }

    :deep(.ant-tabs-content-holder) {
      background: #f5f7fa;
      padding: 0;
      height: calc(100% - 60px);
      overflow-y: auto;
    }

    :deep(.ant-tabs-tabpane) {
      height: 100%;
      padding: 0;
    }
  }

  .tab-pane-content {
    padding: 24px;
    height: 100%;
    overflow-y: auto;
  }

  .tab-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #666;
    transition: color 0.3s ease;

    .tab-icon {
      font-size: 16px;
    }
  }

  .form-section {
    background: #fff;
    border-radius: 8px;
    padding: 0;
  }

  .enhanced-select,
  .enhanced-textarea {
    border-radius: 8px !important;
    transition: all 0.3s ease;

    &:hover {
      border-color: #40a9ff !important;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1) !important;
    }

    &:focus {
      border-color: #1890ff !important;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
    }
  }

  .amount-distribution {
    .distribution-note {
      background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
      border-radius: 8px;
      padding: 16px;
      border: 1px solid #b7eb8f;
      text-align: center;

      p {
        color: #666;
        font-size: 14px;
        line-height: 1.6;
        margin: 0;
      }
    }
  }

  .mb-4 {
    margin-bottom: 24px;
  }

  :deep(.ant-form) {
    .ant-form-item-label > label {
      font-weight: 500;
      color: #2c3e50;
    }

    .ant-input,
    .ant-input-number,
    .ant-select-selector,
    .ant-picker {
      border-radius: 6px;
      border: 1px solid #d9d9d9;
      transition: all 0.3s ease;

      &:hover {
        border-color: #40a9ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
      }

      &:focus,
      &.ant-input-focused,
      &.ant-select-focused .ant-select-selector,
      &.ant-picker-focused {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }
    }

    .ant-input-number {
      width: 100%;
    }

    .ant-select {
      .ant-select-selection-item {
        color: #2c3e50;
        font-weight: 500;
      }
    }
  }

  // 滚动条美化
  .paycontract-money-container,
  .tab-pane-content {
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }

  // 标签样式
  :deep(.ant-tag) {
    border-radius: 6px;
    padding: 4px 12px;
    font-weight: 500;
    border: none;
  }

  // Alert样式
  :deep(.ant-alert) {
    border-radius: 8px;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

    &.ant-alert-info {
      background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
      border-left: 4px solid #1890ff;
    }
  }
</style>
