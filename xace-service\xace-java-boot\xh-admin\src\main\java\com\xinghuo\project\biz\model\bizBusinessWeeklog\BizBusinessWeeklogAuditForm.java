package com.xinghuo.project.biz.model.bizBusinessWeeklog;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 商机周报审核表单对象
 * 
 * <AUTHOR>
 * @date 2025-07-08
 */
@Data
@Schema(description = "商机周报审核表单对象")
public class BizBusinessWeeklogAuditForm {

    /**
     * 商机周报ID
     */
    @NotBlank(message = "商机周报ID不能为空")
    @Schema(description = "商机周报ID", required = true)
    private String id;

    /**
     * 审核状态 (3-已发布，-1-已驳回)
     */
    @NotNull(message = "审核状态不能为空")
    @Schema(description = "审核状态 (3-已发布，-1-已驳回)", required = true)
    private Integer status;

    /**
     * 审核意见
     */
    @Schema(description = "审核意见")
    private String auditNote;
}
