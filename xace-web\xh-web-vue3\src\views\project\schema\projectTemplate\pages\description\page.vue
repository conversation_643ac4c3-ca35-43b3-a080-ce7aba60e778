<template>
  <div class="template-description-page p-6">
    <!-- 头部信息 -->
    <div class="header-section mb-8">
      <div class="flex items-center justify-between mb-4">
        <h1 class="text-2xl font-bold">{{ templateInfo.name || '项目模板说明' }}</h1>
        <div class="flex items-center space-x-2">
          <a-tag :color="getStatusColor(templateInfo.status)">
            {{ getStatusText(templateInfo.status) }}
          </a-tag>
          <a-button type="primary" @click="handleEdit" v-if="hasEditPermission">
            <template #icon><EditOutlined /></template>
            编辑说明
          </a-button>
        </div>
      </div>

      <div class="template-meta grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg">
        <div class="meta-item">
          <div class="label text-sm text-gray-500">模板编码</div>
          <div class="value font-medium">{{ templateInfo.code || '未设置' }}</div>
        </div>
        <div class="meta-item">
          <div class="label text-sm text-gray-500">模板类型</div>
          <div class="value font-medium">{{ templateInfo.typeName || '通用模板' }}</div>
        </div>
        <div class="meta-item">
          <div class="label text-sm text-gray-500">创建时间</div>
          <div class="value font-medium">{{ formatDate(templateInfo.createdAt) }}</div>
        </div>
      </div>
    </div>

    <!-- 模板描述 -->
    <div class="description-section mb-8">
      <div class="section-header mb-4">
        <h2 class="text-lg font-semibold flex items-center">
          <FileTextOutlined class="mr-2" />
          模板描述
        </h2>
      </div>
      <div class="description-content">
        <div v-if="templateInfo.description" class="prose max-w-none p-4 bg-white border rounded-lg" v-html="formatDescription(templateInfo.description)">
        </div>
        <div v-else class="empty-description p-8 text-center text-gray-500 bg-gray-50 rounded-lg">
          <FileTextOutlined class="text-4xl mb-2" />
          <p>暂无模板描述</p>
          <a-button type="link" @click="handleEdit" v-if="hasEditPermission"> 添加描述 </a-button>
        </div>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="statistics-section mb-8">
      <div class="section-header mb-4">
        <h2 class="text-lg font-semibold flex items-center">
          <BarChartOutlined class="mr-2" />
          模板统计
        </h2>
      </div>
      <div class="statistics-grid grid grid-cols-2 md:grid-cols-4 gap-4">
        <div class="stat-card p-4 bg-blue-50 rounded-lg text-center">
          <div class="stat-number text-2xl font-bold text-blue-600">
            {{ templateStats.wbsCount || 0 }}
          </div>
          <div class="stat-label text-sm text-gray-600">WBS活动</div>
        </div>
        <div class="stat-card p-4 bg-green-50 rounded-lg text-center">
          <div class="stat-number text-2xl font-bold text-green-600">
            {{ templateStats.phaseCount || 0 }}
          </div>
          <div class="stat-label text-sm text-gray-600">项目阶段</div>
        </div>
        <div class="stat-card p-4 bg-orange-50 rounded-lg text-center">
          <div class="stat-number text-2xl font-bold text-orange-600">
            {{ templateStats.milestoneCount || 0 }}
          </div>
          <div class="stat-label text-sm text-gray-600">里程碑</div>
        </div>
        <div class="stat-card p-4 bg-purple-50 rounded-lg text-center">
          <div class="stat-number text-2xl font-bold text-purple-600">
            {{ templateStats.usageCount || 0 }}
          </div>
          <div class="stat-label text-sm text-gray-600">使用次数</div>2
        </div>
      </div>
    </div>

    <!-- 配置完整度 -->
    <div class="completeness-section mb-8">
      <div class="section-header mb-4">
        <h2 class="text-lg font-semibold flex items-center">
          <CheckCircleOutlined class="mr-2" />
          配置完整度
        </h2>
      </div>
      <div class="completeness-content">
        <div class="completeness-progress mb-4">
          <div class="flex justify-between items-center mb-2">
            <span class="text-sm font-medium">整体完整度</span>
            <span class="text-sm text-gray-500">{{ completenessPercentage }}%</span>
          </div>
          <a-progress
            :percent="completenessPercentage"
            :status="completenessPercentage >= 80 ? 'success' : completenessPercentage >= 50 ? 'active' : 'exception'" />
        </div>

        <div class="completeness-items grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="item p-3 border rounded-lg" :class="templateStats.hasWbs ? 'bg-green-50 border-green-200' : 'bg-gray-50 border-gray-200'">
            <div class="flex items-center">
              <CheckCircleOutlined v-if="templateStats.hasWbs" class="text-green-500 mr-2" />
              <CloseCircleOutlined v-else class="text-gray-400 mr-2" />
              <span class="text-sm">WBS结构</span>
            </div>
          </div>
          <div class="item p-3 border rounded-lg" :class="templateStats.hasPhase ? 'bg-green-50 border-green-200' : 'bg-gray-50 border-gray-200'">
            <div class="flex items-center">
              <CheckCircleOutlined v-if="templateStats.hasPhase" class="text-green-500 mr-2" />
              <CloseCircleOutlined v-else class="text-gray-400 mr-2" />
              <span class="text-sm">阶段配置</span>
            </div>
          </div>
          <div class="item p-3 border rounded-lg" :class="templateStats.hasApproval ? 'bg-green-50 border-green-200' : 'bg-gray-50 border-gray-200'">
            <div class="flex items-center">
              <CheckCircleOutlined v-if="templateStats.hasApproval" class="text-green-500 mr-2" />
              <CloseCircleOutlined v-else class="text-gray-400 mr-2" />
              <span class="text-sm">审批流程</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 使用指南 -->
    <div class="guide-section">
      <div class="section-header mb-4">
        <h2 class="text-lg font-semibold flex items-center">
          <BookOutlined class="mr-2" />
          使用指南
        </h2>
      </div>
      <div class="guide-content">
        <a-steps direction="vertical" :current="-1">
          <a-step title="选择模板" description="在项目创建时选择此模板作为基础模板" />
          <a-step title="配置项目信息" description="根据实际项目需求调整项目基本信息和参数" />
          <a-step title="定制WBS结构" description="根据项目特点调整WBS活动和工作包结构" />
          <a-step title="设置项目阶段" description="确认项目阶段和里程碑，调整时间计划" />
          <a-step title="分配团队角色" description="为项目团队成员分配相应的角色和权限" />
          <a-step title="启动项目" description="完成配置后启动项目，开始项目执行" />
        </a-steps>
      </div>
    </div>

    <!-- 编辑弹窗 -->
    <a-modal v-model:open="editModalVisible" title="编辑模板说明" width="800px" @ok="handleSave" @cancel="handleCancel">
      <a-form :model="editForm" layout="vertical">
        <a-form-item label="模板描述" name="description">
          <a-textarea v-model:value="editForm.description" :rows="8" placeholder="请输入模板描述..." :maxlength="2000" show-count />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted, reactive } from 'vue';
  import {
    Tag as ATag,
    Button as AButton,
    Steps as ASteps,
    Step as AStep,
    Progress as AProgress,
    Modal as AModal,
    Form as AForm,
    FormItem as AFormItem,
    Textarea as ATextarea,
  } from 'ant-design-vue';
  import { EditOutlined, FileTextOutlined, BarChartOutlined, CheckCircleOutlined, CloseCircleOutlined, BookOutlined } from '@ant-design/icons-vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { usePermission } from '/@/hooks/web/usePermission';
  import { formatToDateTime } from '/@/utils/dateUtil';
  import { getProjectTemplateDetailInfo, updateProjectTemplate } from '/@/api/project/projectTemplate';

  interface Props {
    templateId: string;
  }

  const props = defineProps<Props>();
  const { createMessage } = useMessage();
  const { hasPermission } = usePermission();

  // 响应式数据
  const templateInfo = ref<any>({});
  const templateStats = ref<any>({});
  const loading = ref(false);
  const editModalVisible = ref(false);
  const editForm = reactive({
    description: '',
  });

  // 状态处理函数
  function getStatusText(status: number | string): string {
    const statusValue = typeof status === 'string' ? parseInt(status) : status;
    switch (statusValue) {
      case 0:
        return '启用';
      case 1:
        return '禁用';
      default:
        return '未知';
    }
  }

  function getStatusColor(status: number | string): string {
    const statusValue = typeof status === 'string' ? parseInt(status) : status;
    switch (statusValue) {
      case 0:
        return 'green';
      case 1:
        return 'red';
      default:
        return 'default';
    }
  }

  // 计算属性
  const hasEditPermission = computed(() => hasPermission('project:template:edit'));

  const completenessPercentage = computed(() => {
    const total = 3;
    let completed = 0;
    if (templateStats.value.hasWbs) completed++;
    if (templateStats.value.hasPhase) completed++;
    if (templateStats.value.hasApproval) completed++;
    return Math.round((completed / total) * 100);
  });

  // 方法
  function formatDate(dateStr: string) {
    if (!dateStr) return '未知';
    return formatToDateTime(dateStr);
  }

  function formatDescription(description: string) {
    if (!description) return '';
    // 简单的换行处理
    return description.replace(/\n/g, '<br/>');
  }

  function handleEdit() {
    editForm.description = templateInfo.value.description || '';
    editModalVisible.value = true;
  }

  async function handleSave() {
    try {
      const response = await updateProjectTemplate(props.templateId, {
        ...templateInfo.value,
        description: editForm.description,
      });

      if (response.code === 200) {
        createMessage.success('保存成功');
        editModalVisible.value = false;
        await loadTemplateInfo();
      } else {
        createMessage.error(response.msg || '保存失败');
      }
    } catch (error) {
      console.error('保存失败:', error);
      createMessage.error('保存失败，请稍后重试');
    }
  }

  function handleCancel() {
    editModalVisible.value = false;
    editForm.description = '';
  }

  // 加载模板信息
  async function loadTemplateInfo() {
    if (!props.templateId) return;

    try {
      loading.value = true;
      const response = await getProjectTemplateDetailInfo(props.templateId);

      if (response.code === 200) {
        templateInfo.value = response.data || {};

        // 设置统计信息
        templateStats.value = {
          wbsCount: response.data?.wbsCount || 0,
          phaseCount: response.data?.phaseCount || 0,
          milestoneCount: response.data?.milestoneCount || 0,
          usageCount: response.data?.usageCount || 0,
          hasWbs: (response.data?.wbsCount || 0) > 0,
          hasPhase: (response.data?.phaseCount || 0) > 0,
          hasApproval: response.data?.hasApproval || false,
        };
      } else {
        createMessage.error(response.msg || '加载模板信息失败');
      }
    } catch (error) {
      console.error('加载模板信息失败:', error);
      createMessage.error('加载模板信息失败，请稍后重试');
    } finally {
      loading.value = false;
    }
  }

  // 初始化
  onMounted(() => {
    loadTemplateInfo();
  });
</script>

<style scoped>
  .template-description-page {
    max-width: 1200px;
    margin: 0 auto;
  }

  .section-header {
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 8px;
  }

  .template-meta {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  }

  .meta-item .label {
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
  }

  .meta-item .value {
    font-size: 14px;
    color: #333;
    font-weight: 500;
  }

  .stat-card {
    transition: transform 0.2s ease;
  }

  .stat-card:hover {
    transform: translateY(-2px);
  }

  .empty-description {
    border: 2px dashed #d9d9d9;
  }

  .prose {
    line-height: 1.6;
    color: #333;
  }

  .prose h1,
  .prose h2,
  .prose h3 {
    margin-top: 1.5em;
    margin-bottom: 0.5em;
  }

  .prose p {
    margin-bottom: 1em;
  }

  .completeness-items .item {
    transition: all 0.2s ease;
  }

  .completeness-items .item:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  :deep(.ant-steps-item-description) {
    color: #666;
  }
</style>
