<template>
  <div class="opportunity-analysis p-4">
    <div class="page-header mb-4">
      <h2 class="text-xl font-semibold mb-2">商机分析</h2>
      <p class="text-gray-600">分析商机的数据和趋势</p>
    </div>

    <a-result status="info" title="商机分析页面" sub-title="该功能正在开发中，敬请期待...">
      <template #icon>
        <i class="icon-ym icon-ym-analysis text-6xl text-red-500"></i>
      </template>
      <template #extra>
        <a-space>
          <a-button type="primary"> 查看分析报告 </a-button>
          <a-button> 返回概览 </a-button>
        </a-space>
      </template>
    </a-result>
  </div>
</template>

<script lang="ts" setup>
  const props = defineProps<{
    projectId?: string;
  }>();

  console.log('商机分析页面 - 项目ID:', props.projectId);
</script>

<style lang="less" scoped>
  .opportunity-analysis {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
</style>
