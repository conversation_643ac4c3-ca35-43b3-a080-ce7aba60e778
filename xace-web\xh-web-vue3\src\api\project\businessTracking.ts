import { defHttp } from '/@/utils/http/axios';
import { ListResult } from '/@/api/model/baseModel';

/**
 * 商机跟踪记录API
 */

// API URL前缀
const API_PREFIX = '/api/project/business-tracking';

/**
 * 商机跟踪记录对象接口
 */
export interface BusinessTrackingModel {
  id: string;
  businessId: string;
  trackDate: string;
  content: string;
  trackUser: string;
  createTime?: string;
  updateTime?: string;
}

/**
 * 商机跟踪记录查询参数接口
 */
export interface BusinessTrackingQueryParams {
  businessId: string;
  page?: number;
  pageSize?: number;
}

/**
 * 获取商机跟踪记录列表
 * @param params 查询参数
 * @returns 商机跟踪记录列表
 */
export const getBusinessTrackingList = (params: BusinessTrackingQueryParams) => {
  return defHttp.get<ListResult<BusinessTrackingModel>>({
    url: API_PREFIX,
    params,
  });
};

/**
 * 获取商机跟踪记录详情
 * @param id 跟踪记录ID
 * @returns 商机跟踪记录详情
 */
export const getBusinessTracking = (id: string) => {
  return defHttp.get<BusinessTrackingModel>({
    url: `${API_PREFIX}/${id}`,
  });
};

/**
 * 添加商机跟踪记录
 * @param params 跟踪记录参数
 * @returns 操作结果
 */
export const addBusinessTracking = (params: Omit<BusinessTrackingModel, 'id' | 'createTime' | 'updateTime'>) => {
  return defHttp.post<string>({
    url: API_PREFIX,
    data: params,
  });
};

/**
 * 更新商机跟踪记录
 * @param id 跟踪记录ID
 * @param params 跟踪记录参数
 * @returns 操作结果
 */
export const updateBusinessTracking = (id: string, params: Omit<BusinessTrackingModel, 'id' | 'createTime' | 'updateTime'>) => {
  return defHttp.put<void>({
    url: `${API_PREFIX}/${id}`,
    data: params,
  });
};

/**
 * 删除商机跟踪记录
 * @param id 跟踪记录ID
 * @returns 操作结果
 */
export const deleteBusinessTracking = (id: string) => {
  return defHttp.delete<void>({
    url: `${API_PREFIX}/${id}`,
  });
};
