<template>
  <div v-if="showCurrentApp" :class="[prefixCls, `${prefixCls}--${theme}`]">
    <a-dropdown v-if="isMultipleApps" placement="bottom" :trigger="['click']" :overlayClassName="`${prefixCls}-dropdown`">
      <a-button type="text" :class="`${prefixCls}__trigger`">
        <template #icon>
          <i class="icon-ym icon-ym-systemToggle"></i>
        </template>
        {{ currentSystemName }}
        <DownOutlined :class="`${prefixCls}__arrow`" />
      </a-button>
      <template #overlay>
        <a-menu @click="handleSystemSwitch" :selectedKeys="[currentSystemId]">
          <a-menu-item v-for="system in systemList" :key="system.id" :disabled="system.currentSystem" :class="{ 'current-system': system.currentSystem }">
            <div class="flex items-center">
              <i :class="`${system.icon} mr-2`" v-if="system.icon"></i>
              <span>{{ system.name }}</span>
              <a-tag v-if="system.currentSystem" color="success" size="small" class="ml-2"> 当前 </a-tag>
            </div>
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>

    <!-- 单应用模式：仅显示当前应用名称 -->
    <div v-else :class="`${prefixCls}__single`">
      <i class="icon-ym icon-ym-systemToggle mr-2"></i>
      <span>{{ currentSystemName }}</span>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { computed } from 'vue';
  import { DownOutlined } from '@ant-design/icons-vue';
  import { useUserStore } from '/@/store/modules/user';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { propTypes } from '/@/utils/propTypes';
  import { setMajor } from '/@/api/permission/userSetting';

  defineOptions({ name: 'AppSwitcher' });

  interface SystemInfo {
    id: string;
    name: string;
    icon?: string;
    currentSystem: boolean;
  }

  defineProps({
    theme: propTypes.oneOf(['dark', 'light']).def('light'),
  });

  const { prefixCls } = useDesign('header-app-switcher');
  const { createMessage } = useMessage();
  const userStore = useUserStore();

  const getUserInfo = computed(() => userStore.getUserInfo || {});

  // 计算是否显示当前应用（单应用或多应用都显示）
  const showCurrentApp = computed(() => {
    const systemIds = getUserInfo.value.systemIds;
    return systemIds && systemIds.length >= 1;
  });

  // 计算是否为多应用模式
  const isMultipleApps = computed(() => {
    const systemIds = getUserInfo.value.systemIds;
    return systemIds && systemIds.length > 1;
  });

  // 获取系统列表
  const systemList = computed<SystemInfo[]>(() => {
    return getUserInfo.value.systemIds || [];
  });

  // 获取当前系统名称
  const currentSystemName = computed(() => {
    const current = systemList.value.find(system => system.currentSystem);
    return current?.name || '请选择应用';
  });

  // 获取当前系统ID
  const currentSystemId = computed(() => {
    const current = systemList.value.find(system => system.currentSystem);
    return current?.id || '';
  });

  // 处理系统切换
  async function handleSystemSwitch({ key }: { key: string }) {
    if (key === currentSystemId.value) return;

    try {
      const loading = createMessage.loading('正在切换应用...', 0);
      const query = { majorId: key, majorType: 'System' };
      const res = await setMajor(query);
      loading();
      createMessage.success(res.msg || '切换成功').then(() => {
        location.reload();
      });
    } catch (error) {
      createMessage.error('切换失败，请重试');
    }
  }
</script>

<style lang="less">
  @prefix-cls: ~'@{namespace}-header-app-switcher';

  .@{prefix-cls} {
    margin-right: 16px;
    display: flex;
    align-items: center;
    height: @header-height;

    &__trigger {
      height: 32px;
      border-radius: 6px;
      transition: all 0.3s;
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;
      padding: 0 12px;
      border: 1px solid transparent;

      &:hover {
        background-color: rgba(0, 0, 0, 0.06);
        border-color: rgba(0, 0, 0, 0.1);
      }

      &:focus {
        border-color: @primary-color;
        box-shadow: 0 0 0 2px fade(@primary-color, 20%);
      }
    }

    &__single {
      display: flex;
      align-items: center;
      height: 32px;
      padding: 0 12px;
      border-radius: 6px;
      font-weight: 500;
      background-color: rgba(0, 0, 0, 0.04);
      border: 1px solid rgba(0, 0, 0, 0.1);
    }

    &__arrow {
      font-size: 12px;
      transition: transform 0.3s;
    }

    &--dark {
      .@{prefix-cls}__trigger {
        color: #fff;

        &:hover {
          background-color: rgba(255, 255, 255, 0.1);
          border-color: rgba(255, 255, 255, 0.2);
        }

        &:focus {
          border-color: @primary-color;
          box-shadow: 0 0 0 2px fade(@primary-color, 20%);
        }
      }

      .@{prefix-cls}__single {
        color: #fff;
        background-color: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.2);
      }
    }

    &--light {
      .@{prefix-cls}__trigger {
        color: #333;
      }

      .@{prefix-cls}__single {
        color: #333;
      }
    }

    &-dropdown {
      .ant-dropdown-menu {
        border-radius: 8px;
        box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
        border: 1px solid #f0f0f0;
        min-width: 200px;
      }

      .ant-dropdown-menu-item {
        padding: 8px 16px;

        &:hover {
          background-color: #f5f5f5;
        }

        &.current-system {
          background-color: #e6f7ff;

          &:hover {
            background-color: #e6f7ff;
          }
        }

        &.ant-dropdown-menu-item-disabled {
          opacity: 0.7;
          cursor: not-allowed;
        }
      }
    }
  }

  // 下拉菜单打开时箭头旋转
  .ant-dropdown-open .@{prefix-cls}__arrow {
    transform: rotate(180deg);
  }
</style>
