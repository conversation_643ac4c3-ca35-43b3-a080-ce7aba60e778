package com.xinghuo.project.biz.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 项目客户实体类
 * 对应数据库表：zz_proj_customer
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode
@TableName("zz_proj_customer")
public class BizCustomerEntity {

    /**
     * 主键ID
     */
    @TableId("id")
    private String id;

    /**
     * 客户类型
     */
    @TableField("cust_type")
    private String custType;

    /**
     * 业务线
     */
    @TableField("cust_line")
    private String custLine;

    /**
     * 负责人
     */
    @TableField("leader")
    private String leader;

    /**
     * 客户名称
     */
    @TableField("name")
    private String name;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 排序码
     */
    @TableField("sortCode")
    private Long sortCode;

    /**
     * 创建用户
     */
    @TableField("creatorUserId")
    private String creatorUserId;

    /**
     * 创建时间
     */
    @TableField("creatorTime")
    private Date creatorTime;

    /**
     * 最后修改人
     */
    @TableField("lastModifyUserId")
    private String lastModifyUserId;

    /**
     * 修改时间
     */
    @TableField("lastModifyTime")
    private Date lastModifyTime;

    /**
     * 租户ID
     */
    @TableField("f_tenantid")
    private String tenantId;

    /**
     * 流程ID
     */
    @TableField("f_flowid")
    private String flowId;

    /**
     * 删除标志
     */
    @TableField("f_deletemark")
    private Integer deleteMark;
}
