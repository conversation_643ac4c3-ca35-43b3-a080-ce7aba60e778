package com.xinghuo.manhour.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.manhour.dao.ManhourMapper;
import com.xinghuo.manhour.entity.ManhourEntity;
import com.xinghuo.manhour.model.analysis.*;
import com.xinghuo.manhour.service.ManhourAnalysisService;
import com.xinghuo.permission.service.OrganizeService;
import com.xinghuo.permission.service.UserService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 工时分析服务实现类
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Slf4j
@Service
public class ManhourAnalysisServiceImpl extends BaseServiceImpl<ManhourMapper, ManhourEntity> implements ManhourAnalysisService {

    @Autowired
    private UserService userService;

    @Autowired
    private OrganizeService organizeService;



    @Override
    public WorkhourAnalysisOverview getOverview(WorkhourAnalysisParams params) {
        WorkhourAnalysisOverview overview = new WorkhourAnalysisOverview();
        
        // 构建查询条件
        LambdaQueryWrapper<ManhourEntity> queryWrapper = buildQueryWrapper(params);
        
        // 查询所有符合条件的工时记录
        List<ManhourEntity> manhourList = baseMapper.selectList(queryWrapper);
        
        if (manhourList.isEmpty()) {
            // 返回空数据
            overview.setTotalWorkMonth(BigDecimal.ZERO);
            overview.setProjectCount(0);
            overview.setUserCount(0);
            overview.setAvgEfficiency(BigDecimal.ZERO);
            overview.setTotalRecords(0);
            overview.setActiveProjectCount(0);
            overview.setAvgProjectParticipation(BigDecimal.ZERO);
            overview.setCompletionRate(BigDecimal.ZERO);
            return overview;
        }
        
        // 计算总工时
        BigDecimal totalWorkMonth = manhourList.stream()
                .map(ManhourEntity::getWorkMonth)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        // 计算参与项目数
        long projectCount = manhourList.stream()
                .map(ManhourEntity::getProjectId)
                .filter(Objects::nonNull)
                .distinct()
                .count();
        
        // 计算参与人员数
        long userCount = manhourList.stream()
                .map(ManhourEntity::getUserId)
                .filter(Objects::nonNull)
                .distinct()
                .count();
        
        // 计算平均工时效率（这里简化为工时完成率）
        long completedCount = manhourList.stream()
                .filter(m -> m.getStatus() != null && m.getStatus() >= 3) // 假设状态3以上为已完成
                .count();
        BigDecimal completionRate = manhourList.size() > 0 ? 
                BigDecimal.valueOf(completedCount * 100.0 / manhourList.size()).setScale(2, RoundingMode.HALF_UP) : 
                BigDecimal.ZERO;
        
        // 计算活跃项目数（有工时记录的项目）
        long activeProjectCount = manhourList.stream()
                .filter(m -> m.getWorkMonth() != null && m.getWorkMonth().compareTo(BigDecimal.ZERO) > 0)
                .map(ManhourEntity::getProjectId)
                .distinct()
                .count();
        
        // 计算平均项目参与度
        BigDecimal avgProjectParticipation = userCount > 0 ? 
                BigDecimal.valueOf(projectCount * 1.0 / userCount).setScale(2, RoundingMode.HALF_UP) : 
                BigDecimal.ZERO;
        
        // 设置概览数据
        overview.setTotalWorkMonth(totalWorkMonth);
        overview.setProjectCount((int) projectCount);
        overview.setUserCount((int) userCount);
        overview.setAvgEfficiency(completionRate); // 这里用完成率代替效率
        overview.setTotalRecords(manhourList.size());
        overview.setActiveProjectCount((int) activeProjectCount);
        overview.setAvgProjectParticipation(avgProjectParticipation);
        overview.setCompletionRate(completionRate);
        
        return overview;
    }

    @Override
    public ChartDataModel getCharts(WorkhourAnalysisParams params) {
        ChartDataModel chartData = new ChartDataModel();
        
        // 构建查询条件
        LambdaQueryWrapper<ManhourEntity> queryWrapper = buildQueryWrapper(params);
        List<ManhourEntity> manhourList = baseMapper.selectList(queryWrapper);
        
        if (manhourList.isEmpty()) {
            return chartData;
        }
        
        // 项目工时分布
        Map<String, BigDecimal> projectDistribution = manhourList.stream()
                .filter(m -> StringUtils.hasText(m.getProjectName()) && m.getWorkMonth() != null)
                .collect(Collectors.groupingBy(
                        ManhourEntity::getProjectName,
                        Collectors.reducing(BigDecimal.ZERO, ManhourEntity::getWorkMonth, BigDecimal::add)
                ));
        
        List<ChartDataModel.ProjectDistributionData> projectDistributionList = projectDistribution.entrySet().stream()
                .map(entry -> {
                    ChartDataModel.ProjectDistributionData data = new ChartDataModel.ProjectDistributionData();
                    data.setProjectName(entry.getKey());
                    data.setWorkMonth(entry.getValue());
                    return data;
                })
                .sorted((a, b) -> b.getWorkMonth().compareTo(a.getWorkMonth()))
                .limit(10) // 只取前10个项目
                .collect(Collectors.toList());

        chartData.setProjectDistribution(projectDistributionList);

        // 分部工时分布 - 按分部ID分组，然后关联查询分部名称
        Map<String, BigDecimal> departmentDistribution = manhourList.stream()
                .filter(m -> StringUtils.hasText(m.getFbId()) && m.getWorkMonth() != null)
                .collect(Collectors.groupingBy(
                        ManhourEntity::getFbId,
                        Collectors.reducing(BigDecimal.ZERO, ManhourEntity::getWorkMonth, BigDecimal::add)
                ));

        List<ChartDataModel.DepartmentDistributionData> departmentDistributionList = departmentDistribution.entrySet().stream()
                .map(entry -> {
                    ChartDataModel.DepartmentDistributionData data = new ChartDataModel.DepartmentDistributionData();

                    // 关联查询分部名称
                    try {
                        var organizeEntity = organizeService.getInfo(entry.getKey());
                        if (organizeEntity != null) {
                            data.setFbName(organizeEntity.getFullName());
                        } else {
                            data.setFbName("分部" + entry.getKey());
                        }
                    } catch (Exception e) {
                        data.setFbName("分部" + entry.getKey());
                    }

                    data.setWorkMonth(entry.getValue());
                    return data;
                })
                .sorted((a, b) -> b.getWorkMonth().compareTo(a.getWorkMonth()))
                .collect(Collectors.toList());

        chartData.setDepartmentDistribution(departmentDistributionList);

        // 项目类型分布 - 按项目类型代码分组，然后转换为中文名称
        Map<String, BigDecimal> projectTypeDistribution = manhourList.stream()
                .filter(m -> StringUtils.hasText(m.getProjType()) && m.getWorkMonth() != null)
                .collect(Collectors.groupingBy(
                        ManhourEntity::getProjType,
                        Collectors.reducing(BigDecimal.ZERO, ManhourEntity::getWorkMonth, BigDecimal::add)
                ));

        List<ChartDataModel.ProjectTypeDistributionData> projectTypeDistributionList = projectTypeDistribution.entrySet().stream()
                .map(entry -> {
                    ChartDataModel.ProjectTypeDistributionData data = new ChartDataModel.ProjectTypeDistributionData();

                    // 转换项目类型代码为中文名称
                    String projTypeName = convertProjTypeToName(entry.getKey());
                    data.setProjTypeName(projTypeName);
                    data.setWorkMonth(entry.getValue());
                    return data;
                })
                .collect(Collectors.toList());

        chartData.setProjectTypeDistribution(projectTypeDistributionList);

        // 月度趋势
        Map<String, BigDecimal> monthlyTrend = manhourList.stream()
                .filter(m -> StringUtils.hasText(m.getMonth()) && m.getWorkMonth() != null)
                .collect(Collectors.groupingBy(
                        ManhourEntity::getMonth,
                        Collectors.reducing(BigDecimal.ZERO, ManhourEntity::getWorkMonth, BigDecimal::add)
                ));

        List<ChartDataModel.MonthlyTrendData> monthlyTrendList = monthlyTrend.entrySet().stream()
                .map(entry -> {
                    ChartDataModel.MonthlyTrendData data = new ChartDataModel.MonthlyTrendData();
                    data.setMonth(entry.getKey());
                    data.setWorkMonth(entry.getValue());
                    return data;
                })
                .sorted(Comparator.comparing(ChartDataModel.MonthlyTrendData::getMonth))
                .collect(Collectors.toList());
        
        chartData.setMonthlyTrend(monthlyTrendList);
        
        return chartData;
    }

    @Override
    public List<PersonalAnalysisVO> getPersonalAnalysisList(WorkhourAnalysisPagination pagination) {
        // 构建查询条件
        LambdaQueryWrapper<ManhourEntity> queryWrapper = buildQueryWrapper(pagination);
        
        // 分页查询
        IPage<ManhourEntity> page = new Page<>(pagination.getCurrentPage(), pagination.getPageSize());
        IPage<ManhourEntity> result = baseMapper.selectPage(page, queryWrapper);
        
        // 按用户分组统计
        Map<String, List<ManhourEntity>> userGroupMap = result.getRecords().stream()
                .filter(m -> StringUtils.hasText(m.getUserId()))
                .collect(Collectors.groupingBy(ManhourEntity::getUserId));
        
        List<PersonalAnalysisVO> personalAnalysisList = new ArrayList<>();
        
        for (Map.Entry<String, List<ManhourEntity>> entry : userGroupMap.entrySet()) {
            List<ManhourEntity> userManhours = entry.getValue();
            if (userManhours.isEmpty()) continue;
            
            ManhourEntity firstRecord = userManhours.get(0);
            PersonalAnalysisVO personalAnalysis = new PersonalAnalysisVO();
            
            personalAnalysis.setUserId(firstRecord.getUserId());

            // 关联查询用户信息
            try {
                var userEntity = userService.getInfo(firstRecord.getUserId());
                if (userEntity != null) {
                    personalAnalysis.setUserName(userEntity.getRealName());
                    personalAnalysis.setFbId(userEntity.getOrganizeId());

                    // 关联查询分部信息
                    if (StringUtils.hasText(userEntity.getOrganizeId())) {
                        var organizeEntity = organizeService.getInfo(userEntity.getOrganizeId());
                        if (organizeEntity != null) {
                            personalAnalysis.setFbName(organizeEntity.getFullName());
                        }
                    }
                } else {
                    // 如果用户不存在，使用原有数据
                    personalAnalysis.setUserName(firstRecord.getUserName());
                    personalAnalysis.setFbName(firstRecord.getFbName());
                    personalAnalysis.setFbId(firstRecord.getFbId());
                }
            } catch (Exception e) {
                // 查询失败时使用原有数据
                personalAnalysis.setUserName(firstRecord.getUserName());
                personalAnalysis.setFbName(firstRecord.getFbName());
                personalAnalysis.setFbId(firstRecord.getFbId());
            }
            
            // 计算总工时
            BigDecimal totalWorkMonth = userManhours.stream()
                    .map(ManhourEntity::getWorkMonth)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            personalAnalysis.setTotalWorkMonth(totalWorkMonth);
            
            // 计算参与项目数
            long projectCount = userManhours.stream()
                    .map(ManhourEntity::getProjectId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .count();
            personalAnalysis.setProjectCount((int) projectCount);
            
            // 计算主要项目类型
            Map<String, Long> projTypeCount = userManhours.stream()
                    .filter(m -> StringUtils.hasText(m.getProjTypeName()))
                    .collect(Collectors.groupingBy(ManhourEntity::getProjTypeName, Collectors.counting()));
            
            String mainProjType = projTypeCount.entrySet().stream()
                    .max(Map.Entry.comparingByValue())
                    .map(Map.Entry::getKey)
                    .orElse("");
            personalAnalysis.setMainProjType(mainProjType);
            
            // 计算工时效率（完成率）
            long completedCount = userManhours.stream()
                    .filter(m -> m.getStatus() != null && m.getStatus() >= 3)
                    .count();
            BigDecimal efficiency = userManhours.size() > 0 ? 
                    BigDecimal.valueOf(completedCount * 100.0 / userManhours.size()).setScale(2, RoundingMode.HALF_UP) : 
                    BigDecimal.ZERO;
            personalAnalysis.setEfficiency(efficiency);
            
            // 生成技能标签（基于项目类型）
            String skillTags = projTypeCount.keySet().stream()
                    .limit(3)
                    .collect(Collectors.joining(", "));
            personalAnalysis.setSkillTags(skillTags);
            
            // 计算平均月工时
            Set<String> months = userManhours.stream()
                    .map(ManhourEntity::getMonth)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            BigDecimal avgMonthWorkHour = months.size() > 0 ? 
                    totalWorkMonth.divide(BigDecimal.valueOf(months.size()), 2, RoundingMode.HALF_UP) : 
                    BigDecimal.ZERO;
            personalAnalysis.setAvgMonthWorkHour(avgMonthWorkHour);
            
            // 计算项目参与度
            BigDecimal projectParticipation = BigDecimal.valueOf(projectCount);
            personalAnalysis.setProjectParticipation(projectParticipation);
            
            // 设置完成率
            personalAnalysis.setCompletionRate(efficiency);
            
            // 找出最活跃项目
            Map<String, BigDecimal> projectWorkMonth = userManhours.stream()
                    .filter(m -> StringUtils.hasText(m.getProjectName()) && m.getWorkMonth() != null)
                    .collect(Collectors.groupingBy(
                            ManhourEntity::getProjectName,
                            Collectors.reducing(BigDecimal.ZERO, ManhourEntity::getWorkMonth, BigDecimal::add)
                    ));
            
            String mostActiveProject = projectWorkMonth.entrySet().stream()
                    .max(Map.Entry.comparingByValue())
                    .map(Map.Entry::getKey)
                    .orElse("");
            personalAnalysis.setMostActiveProject(mostActiveProject);
            
            // 计算工时分布均匀度（简化计算）
            BigDecimal distributionBalance = projectCount > 0 ? 
                    BigDecimal.valueOf(100.0 / projectCount).setScale(2, RoundingMode.HALF_UP) : 
                    BigDecimal.ZERO;
            personalAnalysis.setDistributionBalance(distributionBalance);
            
            personalAnalysisList.add(personalAnalysis);
        }
        
        // 设置分页信息
        pagination.setTotal(result.getTotal());
        
        return personalAnalysisList;
    }

    @Override
    public List<ProjectAnalysisVO> getProjectAnalysisList(WorkhourAnalysisPagination pagination) {
        try {
            // 构建查询条件
            LambdaQueryWrapper<ManhourEntity> queryWrapper = buildQueryWrapper(pagination);

            // 查询工时数据
            List<ManhourEntity> manhourList = baseMapper.selectList(queryWrapper);

            // 按项目ID分组统计
            Map<String, List<ManhourEntity>> projectGroup = manhourList.stream()
                    .filter(m -> StringUtils.hasText(m.getProjectId()))
                    .collect(Collectors.groupingBy(ManhourEntity::getProjectId));

            List<ProjectAnalysisVO> projectList = projectGroup.entrySet().stream()
                    .map(entry -> {
                        List<ManhourEntity> projectTasks = entry.getValue();
                        return convertToProjectAnalysisVO(entry.getKey(), projectTasks);
                    })
                    .sorted((a, b) -> b.getTotalWorkMonth().compareTo(a.getTotalWorkMonth()))
                    .collect(Collectors.toList());

            // 设置分页信息
            pagination.setTotal((long) projectList.size());

            // 分页处理
            int start = (pagination.getCurrentPage() - 1) * pagination.getPageSize();
            int end = Math.min(start + pagination.getPageSize(), projectList.size());

            return start < projectList.size() ? projectList.subList(start, end) : new ArrayList<>();

        } catch (Exception e) {
            log.error("获取项目分析列表失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<DepartmentAnalysisVO> getDepartmentAnalysisList(WorkhourAnalysisPagination pagination) {
        try {
            // 构建查询条件
            LambdaQueryWrapper<ManhourEntity> queryWrapper = buildQueryWrapper(pagination);

            // 查询工时数据
            List<ManhourEntity> manhourList = baseMapper.selectList(queryWrapper);

            // 按分部ID分组统计
            Map<String, List<ManhourEntity>> departmentGroup = manhourList.stream()
                    .filter(m -> StringUtils.hasText(m.getFbId()))
                    .collect(Collectors.groupingBy(ManhourEntity::getFbId));

            List<DepartmentAnalysisVO> departmentList = departmentGroup.entrySet().stream()
                    .map(entry -> {
                        List<ManhourEntity> deptTasks = entry.getValue();
                        return convertToDepartmentAnalysisVO(entry.getKey(), deptTasks);
                    })
                    .sorted((a, b) -> b.getTotalWorkMonth().compareTo(a.getTotalWorkMonth()))
                    .collect(Collectors.toList());

            // 设置分页信息
            pagination.setTotal((long) departmentList.size());

            // 分页处理
            int start = (pagination.getCurrentPage() - 1) * pagination.getPageSize();
            int end = Math.min(start + pagination.getPageSize(), departmentList.size());

            return start < departmentList.size() ? departmentList.subList(start, end) : new ArrayList<>();

        } catch (Exception e) {
            log.error("获取分部分析列表失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<WorkhourDetailVO> getWorkhourDetailList(WorkhourAnalysisPagination pagination) {
        try {
            // 构建查询条件
            LambdaQueryWrapper<ManhourEntity> queryWrapper = buildQueryWrapper(pagination);

            // 添加排序
            queryWrapper.orderByDesc(ManhourEntity::getCreateTime);

            // 查询工时数据
            List<ManhourEntity> manhourList = baseMapper.selectList(queryWrapper);

            // 转换为明细VO
            List<WorkhourDetailVO> detailList = manhourList.stream()
                    .map(this::convertToWorkhourDetailVO)
                    .collect(Collectors.toList());

            // 设置分页信息
            pagination.setTotal((long) detailList.size());

            // 分页处理
            int start = (pagination.getCurrentPage() - 1) * pagination.getPageSize();
            int end = Math.min(start + pagination.getPageSize(), detailList.size());

            return start < detailList.size() ? detailList.subList(start, end) : new ArrayList<>();

        } catch (Exception e) {
            log.error("获取工时明细列表失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public void exportWorkhourAnalysis(WorkhourAnalysisParams params, HttpServletResponse response) {
        // TODO: 实现导出功能
    }

    @Override
    public List<Map<String, Object>> getProjectSelector() {
        // TODO: 实现项目选择器
        return new ArrayList<>();
    }

    @Override
    public List<Map<String, Object>> getDepartmentSelector() {
        // TODO: 实现分部选择器
        return new ArrayList<>();
    }

    @Override
    public PersonalEfficiencyDetail getPersonalEfficiencyDetail(String userId, WorkhourAnalysisParams params) {
        // TODO: 实现个人效率详情
        return new PersonalEfficiencyDetail();
    }

    @Override
    public ProjectHealthDetail getProjectHealthDetail(String projectId, WorkhourAnalysisParams params) {
        // TODO: 实现项目健康度详情
        return new ProjectHealthDetail();
    }

    @Override
    public DepartmentUtilizationDetail getDepartmentUtilizationDetail(String fbId, WorkhourAnalysisParams params) {
        // TODO: 实现分部利用率详情
        return new DepartmentUtilizationDetail();
    }

    @Override
    public List<Map<String, Object>> getSkillTagsStatistics(WorkhourAnalysisParams params) {
        // TODO: 实现技能标签统计
        return new ArrayList<>();
    }

    @Override
    public List<Map<String, Object>> getWorkTypeStatistics(WorkhourAnalysisParams params) {
        // TODO: 实现工时类型统计
        return new ArrayList<>();
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<ManhourEntity> buildQueryWrapper(WorkhourAnalysisParams params) {
        LambdaQueryWrapper<ManhourEntity> queryWrapper = new LambdaQueryWrapper<>();
        
        if (params == null) {
            return queryWrapper;
        }
        
        // 时间范围条件
        if (StringUtils.hasText(params.getStartMonth())) {
            queryWrapper.ge(ManhourEntity::getMonth, params.getStartMonth());
        }
        if (StringUtils.hasText(params.getEndMonth())) {
            queryWrapper.le(ManhourEntity::getMonth, params.getEndMonth());
        }
        
        // 项目条件
        if (StringUtils.hasText(params.getProjectId())) {
            queryWrapper.eq(ManhourEntity::getProjectId, params.getProjectId());
        }
        
        // 分部条件
        if (StringUtils.hasText(params.getFbId())) {
            queryWrapper.eq(ManhourEntity::getFbId, params.getFbId());
        }
        
        // 项目类型条件
        if (StringUtils.hasText(params.getProjType())) {
            queryWrapper.eq(ManhourEntity::getProjType, params.getProjType());
        }
        
        // 用户条件
        if (StringUtils.hasText(params.getUserId())) {
            queryWrapper.eq(ManhourEntity::getUserId, params.getUserId());
        }
        
        // 模块条件
        if (StringUtils.hasText(params.getModuleId())) {
            queryWrapper.eq(ManhourEntity::getModuleId, params.getModuleId());
        }
        
        // 工时类型条件
        if (StringUtils.hasText(params.getWorkType())) {
            queryWrapper.eq(ManhourEntity::getWorkType, params.getWorkType());
        }
        
        return queryWrapper;
    }

    /**
     * 构建查询条件（分页版本）
     */
    private LambdaQueryWrapper<ManhourEntity> buildQueryWrapper(WorkhourAnalysisPagination pagination) {
        LambdaQueryWrapper<ManhourEntity> queryWrapper = new LambdaQueryWrapper<>();

        if (pagination == null) {
            return queryWrapper;
        }

        // 时间范围条件
        if (StringUtils.hasText(pagination.getStartMonth())) {
            queryWrapper.ge(ManhourEntity::getMonth, pagination.getStartMonth());
        }
        if (StringUtils.hasText(pagination.getEndMonth())) {
            queryWrapper.le(ManhourEntity::getMonth, pagination.getEndMonth());
        }

        // 项目条件
        if (StringUtils.hasText(pagination.getProjectId())) {
            queryWrapper.eq(ManhourEntity::getProjectId, pagination.getProjectId());
        }

        // 分部条件
        if (StringUtils.hasText(pagination.getFbId())) {
            queryWrapper.eq(ManhourEntity::getFbId, pagination.getFbId());
        }

        // 项目类型条件
        if (StringUtils.hasText(pagination.getProjType())) {
            queryWrapper.eq(ManhourEntity::getProjType, pagination.getProjType());
        }

        // 用户条件
        if (StringUtils.hasText(pagination.getUserId())) {
            queryWrapper.eq(ManhourEntity::getUserId, pagination.getUserId());
        }

        // 模块条件
        if (StringUtils.hasText(pagination.getModuleId())) {
            queryWrapper.eq(ManhourEntity::getModuleId, pagination.getModuleId());
        }

        // 工时类型条件
        if (StringUtils.hasText(pagination.getWorkType())) {
            queryWrapper.eq(ManhourEntity::getWorkType, pagination.getWorkType());
        }

        return queryWrapper;
    }

    /**
     * 转换项目类型代码为中文名称
     */
    private String convertProjTypeToName(String projType) {
        if (StringUtils.hasText(projType)) {
            switch (projType.toUpperCase()) {
                case "C":
                    return "合同项目";
                case "P":
                    return "科研项目";
                case "B":
                    return "商机项目";
                case "T":
                    return "专题任务";
                default:
                    return projType;
            }
        }
        return "未知类型";
    }

    /**
     * 转换为项目分析VO
     */
    private ProjectAnalysisVO convertToProjectAnalysisVO(String projectId, List<ManhourEntity> projectTasks) {
        ProjectAnalysisVO vo = new ProjectAnalysisVO();

        if (!projectTasks.isEmpty()) {
            ManhourEntity firstRecord = projectTasks.get(0);
            vo.setProjectId(projectId);
            vo.setProjectName(firstRecord.getProjectName());
            vo.setProjTypeName(convertProjTypeToName(firstRecord.getProjType()));
        }

        // 计算总工时
        BigDecimal totalWorkMonth = projectTasks.stream()
                .map(ManhourEntity::getWorkMonth)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        vo.setTotalWorkMonth(totalWorkMonth);

        // 计算参与人数
        long userCount = projectTasks.stream()
                .map(ManhourEntity::getUserId)
                .filter(Objects::nonNull)
                .distinct()
                .count();
        vo.setUserCount((int) userCount);

        // 计算平均工时
        BigDecimal avgWorkMonth = userCount > 0 ?
                totalWorkMonth.divide(BigDecimal.valueOf(userCount), 2, RoundingMode.HALF_UP) :
                BigDecimal.ZERO;
        vo.setAvgWorkMonth(avgWorkMonth);

        // 计算项目健康度（简化为完成率）
        long completedCount = projectTasks.stream()
                .filter(m -> m.getStatus() != null && m.getStatus() >= 3)
                .count();
        BigDecimal healthScore = projectTasks.size() > 0 ?
                BigDecimal.valueOf(completedCount * 100.0 / projectTasks.size()).setScale(0, RoundingMode.HALF_UP) :
                BigDecimal.ZERO;
        vo.setHealthScore(healthScore);

        // 主要工时类型
        Map<String, Long> workTypeCount = projectTasks.stream()
                .filter(m -> StringUtils.hasText(m.getWorkType()))
                .collect(Collectors.groupingBy(ManhourEntity::getWorkType, Collectors.counting()));

        String mainWorkType = workTypeCount.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse("");
        vo.setMainWorkType(mainWorkType);

        return vo;
    }

    /**
     * 转换为分部分析VO
     */
    private DepartmentAnalysisVO convertToDepartmentAnalysisVO(String fbId, List<ManhourEntity> deptTasks) {
        DepartmentAnalysisVO vo = new DepartmentAnalysisVO();

        vo.setFbId(fbId);

        // 关联查询分部名称
        String fbName = "分部" + fbId; // 默认值
        try {
            var organizeEntity = organizeService.getInfo(fbId);
            if (organizeEntity != null) {
                fbName = organizeEntity.getFullName();
            }
        } catch (Exception e) {
            log.warn("查询分部信息失败，分部ID: {}", fbId, e);
        }
        vo.setFbName(fbName);

        // 计算总工时
        BigDecimal totalWorkMonth = deptTasks.stream()
                .map(ManhourEntity::getWorkMonth)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        vo.setTotalWorkMonth(totalWorkMonth);

        // 计算人员数量
        long userCount = deptTasks.stream()
                .map(ManhourEntity::getUserId)
                .filter(Objects::nonNull)
                .distinct()
                .count();
        vo.setUserCount((int) userCount);

        // 计算项目数量
        long projectCount = deptTasks.stream()
                .map(ManhourEntity::getProjectId)
                .filter(Objects::nonNull)
                .distinct()
                .count();
        vo.setProjectCount((int) projectCount);

        // 计算资源利用率（简化计算）
        BigDecimal utilizationRate = userCount > 0 ?
                totalWorkMonth.multiply(BigDecimal.valueOf(100)).divide(BigDecimal.valueOf(userCount), 2, RoundingMode.HALF_UP) :
                BigDecimal.ZERO;
        vo.setUtilizationRate(utilizationRate);

        // 主要项目类型
        Map<String, Long> projTypeCount = deptTasks.stream()
                .filter(m -> StringUtils.hasText(m.getProjType()))
                .collect(Collectors.groupingBy(m -> convertProjTypeToName(m.getProjType()), Collectors.counting()));

        String mainProjType = projTypeCount.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse("");
        vo.setMainProjType(mainProjType);

        // 平均效率
        long completedCount = deptTasks.stream()
                .filter(m -> m.getStatus() != null && m.getStatus() >= 3)
                .count();
        BigDecimal avgEfficiency = deptTasks.size() > 0 ?
                BigDecimal.valueOf(completedCount * 100.0 / deptTasks.size()).setScale(2, RoundingMode.HALF_UP) :
                BigDecimal.ZERO;
        vo.setAvgEfficiency(avgEfficiency);

        return vo;
    }

    /**
     * 转换为工时明细VO
     */
    private WorkhourDetailVO convertToWorkhourDetailVO(ManhourEntity entity) {
        WorkhourDetailVO vo = new WorkhourDetailVO();

        vo.setId(entity.getId());
        vo.setUserId(entity.getUserId());

        // 关联查询用户信息
        String userName = "用户" + entity.getUserId(); // 默认值
        String fbName = "分部" + entity.getFbId(); // 默认值

        try {
            var userEntity = userService.getInfo(entity.getUserId());
            if (userEntity != null) {
                userName = userEntity.getRealName();

                // 查询分部名称
                if (StringUtils.hasText(userEntity.getOrganizeId())) {
                    var organizeEntity = organizeService.getInfo(userEntity.getOrganizeId());
                    if (organizeEntity != null) {
                        fbName = organizeEntity.getFullName();
                    }
                }
            }
        } catch (Exception e) {
            log.warn("查询用户信息失败，用户ID: {}", entity.getUserId(), e);
        }

        vo.setUserName(userName);
        vo.setFbName(fbName);
        vo.setProjectName(entity.getProjectName());
        vo.setModuleName(entity.getModuleName());
        vo.setProjTypeName(convertProjTypeToName(entity.getProjType()));
        vo.setWorkType(entity.getWorkType());
        vo.setMonth(entity.getMonth());
        vo.setWorkMonth(entity.getWorkMonth());
        vo.setWorkNote(entity.getWorkNote());

        return vo;
    }
}
