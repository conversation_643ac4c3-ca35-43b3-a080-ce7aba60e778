# CLAUDE.md

本文件为 Claude Code (claude.ai/code) 在此代码仓库中工作时提供指导。

**重要：** 项目开发必须严格遵从开发规范，详见 `docs/framework-standards` 目录。任何代码实现都应该先检查相关规范文档。

请用中文响应。

## 🚨 关键开发要求

### 环境验证
在开始任何开发工作前，必须验证开发环境：

```bash
# 验证JDK版本（必须JDK 17+）
java -version

# 验证Maven版本
mvn -version

# 验证Node.js版本（需要16.15.0+）
node -v

# 验证pnpm版本
pnpm -v
```

### 代码质量检查
每次代码修改完成后，必须执行质量检查：

```bash
# 后端代码检查
cd xace-service/xace-java-boot/xh-admin
mvn clean compile  # 检查编译错误


# 前端代码检查
cd xace-web/xh-web-vue3
pnpm type:check          # TypeScript类型检查
pnpm lint:eslint:fix     # ESLint代码检查和自动修复
pnpm build               # 构建验证
```

## 开发命令

### 后端开发 (Java Spring Boot)
```bash
# 进入主应用目录
cd xace-service/xace-java-boot/xh-admin

# 编译检查（推荐在修改代码后运行）
mvn clean compile

# 运行主应用（开发模式）
mvn spring-boot:run

# 构建整个项目（跳过测试）
mvn clean package -DskipTests

# 完整构建并运行测试
mvn clean package
```

### 前端开发 (Vue 3 + TypeScript)
```bash
# 进入前端目录
cd xace-web/xh-web-vue3

# 安装依赖
pnpm install

# 开发服务器
pnpm dev

# 生产构建
pnpm build

# 测试环境构建
pnpm build:test

# 代码质量检查（开发过程必须）
pnpm type:check          # TypeScript类型检查
pnpm lint:eslint:fix     # ESLint检查和修复  
pnpm lint:prettier       # Prettier格式化
pnpm lint:stylelint:fix  # Stylelint样式检查

# 清理缓存
pnpm clean:cache         # 清理构建缓存
pnpm clean:lib           # 清理node_modules
```

## 项目架构

### 整体结构
这是一个内部管理ERP系统，包含以下主要组件：

- **xace-service/**: Java Spring Boot 后端服务
  - **xace-java-boot/**: 主要后端应用模块
    - **xh-admin/**: 主应用入口和业务逻辑
    - **xh-system/**: 系统管理模块  
    - **xh-oauth/**: OAuth认证服务
    - **xh-visualdev/**: 可视化开发模块
    - **xh-workflow-engine/**: 工作流引擎
    - **xh-file/**: 文件服务
    - **xh-ext-*/**: 扩展模块（应用、定时任务、MagicAPI等）
  - **xace-common/**: 公共依赖和工具
  - **xace-datareport/**: 数据报表服务  
  - **xace-scheduletask/**: 分布式定时任务
  - **xace-resources/**: 静态资源和模板

- **xace-web/**: 前端应用
  - **xh-web-vue3/**: Vue 3 + TypeScript 前端应用
  - **xh-app/**: uni-app 移动端应用
  - **xh-web-datareport/**: 数据报表前端

### 关键业务模块
- **项目管理**: 项目模板、阶段管理、工作流程
- **系统管理**: 用户权限、组织架构
- **可视化开发**: 动态表单、报表设计、门户配置
- **工作流引擎**: 审批流程、任务调度
- **文件管理**: 文档存储、预览、版本控制

### 技术栈

#### 后端技术栈
- **Java 17+** 配合 **Jakarta EE** (注意：不是 javax.*)
- **Spring Boot 3.x** 多模块Maven架构
- **MyBatis-Plus** 数据访问，使用自定义 XHBaseMapper
- **BaseEntityV2** 实体继承体系
- **ActionResult<T>** 统一API响应格式

#### 前端技术栈
- **Vue 3.3.4** 配合 **Composition API**
- **TypeScript 5.0+** 类型安全
- **Ant Design Vue 3.2.20** UI组件库  
- **Vite 4.3+** 构建工具
- **Pinia 2.1+** 状态管理
- **VXE Table** 高性能表格组件
- **ECharts** 数据可视化
- **Monaco Editor** 代码编辑器

## 🚨 关键框架规范

### 后端开发规范 (必须遵循)

#### 1. Jakarta EE 规范 (JDK 17+)
**❌ 错误导入 (会导致编译失败):**
```java
import javax.validation.constraints.NotNull;    // 错误！
import javax.servlet.http.HttpServletRequest;   // 错误！
```

**✅ 正确导入 (Jakarta EE):**
```java
import jakarta.validation.constraints.NotNull;
import jakarta.servlet.http.HttpServletRequest;
```

#### 2. 实体类规范
```java
// ✅ 正确：继承 BaseEntityV2.CUBaseEntityV2
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_user")
public class UserEntity extends BaseEntityV2.CUBaseEntityV2<String> {
    @TableField("FULLNAME")
    private String fullName;
    
    @TableField("ACCOUNT") 
    private String account;
    
    // 注意：系统字段由基类提供，无需手动定义
    // createdAt, lastUpdatedAt, createdBy, lastUpdatedBy 等
}
```

#### 3. Mapper接口规范
```java
// ✅ 正确：继承 XHBaseMapper，不定义自定义方法
@Mapper
public interface UserMapper extends XHBaseMapper<UserEntity> {
    // 保持简洁，不定义自定义方法
    // 所有查询逻辑在Service层使用LambdaQueryWrapper实现
}
```

#### 4. Controller规范
```java
@RestController
@RequestMapping("/api/user")
public class UserController {
    
    @GetMapping("/{id}")
    public ActionResult<UserVO> getInfo(@PathVariable String id) {
        UserEntity entity = userService.getById(id);
        UserVO userVO = BeanCopierUtils.copy(entity, UserVO.class);
        return ActionResult.success(userVO);
    }
    
    @GetMapping
    public ActionResult<PageListVO<UserVO>> getList(UserPagination pagination) {
        return ActionResult.success(userService.getList(pagination));
    }
}
```

### 前端开发规范 (必须遵循)

#### 1. API响应处理
```typescript
// ✅ 正确：始终检查 response.code
const response = await api.getUser(id);
if (response.code === 200) {
    user.value = response.data;
} else {
    message.error(response.msg);
}

// ✅ 分页数据处理
const response = await api.getUserList(params);
if (response.code === 200) {
    userList.value = response.data.list;      // 使用 data.list
    total.value = response.data.total;        // 使用 data.total
}
```

#### 2. 组件数据格式
```typescript
// ✅ 正确：使用 {id, fullName} 格式
const options = [
    { id: '1', fullName: '选项1' },
    { id: '2', fullName: '选项2' }
];

// ❌ 错误：不要使用 {value, label} 格式
const options = [
    { value: '1', label: '选项1' },  // 错误！
    { value: '2', label: '选项2' }   // 错误！
];
```

#### 3. Composition API使用
```vue
<script setup lang="ts">
import { ref, onMounted } from 'vue';
import type { UserInfo } from '/@/types/user';

// ✅ 正确：使用 ref 和 reactive
const userInfo = ref<UserInfo>({});
const loading = ref(false);

// ✅ 正确：定义清晰的类型
interface FormData {
  fullName: string;
  account: string;
}

const formData = ref<FormData>({
  fullName: '',
  account: ''
});
</script>
```

## 关键导入和包结构

### 业务模块包结构
```java
// 标准业务模块结构：
com.xinghuo.[模块名].model.[功能名]
├── [实体名]VO          // API响应视图对象
├── [实体名]Form        // 请求表单对象  
├── [实体名]CrForm      // 创建表单对象
├── [实体名]UpForm      // 更新表单对象
└── [实体名]Pagination  // 分页查询对象

// 示例：用户管理模块
com.xinghuo.system.model.user
├── UserVO              // 用户视图对象
├── UserForm            // 用户表单对象
└── UserPagination      // 用户分页查询对象
```

### 关键框架导入
```java
// ✅ 必须的框架导入
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.model.Pagination;
import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.common.base.dao.XHBaseMapper;
import com.xinghuo.common.base.entity.BaseEntityV2;
import com.xinghuo.common.util.core.BeanCopierUtils;

// ✅ Jakarta EE 导入 (不是 javax.*)
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotBlank;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.persistence.Entity;

// ❌ 禁止使用的导入
import javax.validation.constraints.NotNull;    // 错误！会编译失败
import javax.servlet.http.HttpServletRequest;   // 错误！会编译失败  
import javax.persistence.Entity;                // 错误！会编译失败
```

## 🚀 开发流程指南

### 标准开发流程
1. **环境检查**: 验证JDK 17+、Maven、Node.js 16.15.0+、pnpm版本
2. **规范研读**: 查看 `docs/framework-standards` 相关规范文档
3. **代码实现**: 按照规范编写代码，遵循Jakarta EE规范
4. **质量检查**: 运行编译、测试、类型检查、代码格式化
5. **功能测试**: 本地验证功能完整性
6. **代码提交**: 确保所有检查通过后再提交

### 开发检查清单

#### 后端开发检查
- [ ] JDK版本确认为17+
- [ ] 使用Jakarta EE导入 (不是javax.*)
- [ ] 实体类继承 `BaseEntityV2.CUBaseEntityV2<String>`
- [ ] Mapper接口继承 `XHBaseMapper<T>`
- [ ] Controller返回 `ActionResult<T>` 格式
- [ ] 运行 `mvn clean compile` 检查编译
- [ ] 运行 `mvn test` 执行单元测试
- [ ] 所有业务字段使用 `@TableField` 注解

#### 前端开发检查  
- [ ] Node.js版本16.15.0+
- [ ] 使用 Composition API 和 `<script setup>`
- [ ] API响应检查 `response.code === 200`
- [ ] 组件数据格式使用 `{id, fullName}`
- [ ] 运行 `pnpm type:check` 类型检查
- [ ] 运行 `pnpm lint:eslint:fix` 代码检查
- [ ] 运行 `pnpm build` 构建验证
- [ ] 定义明确的TypeScript类型


## 常见问题排查

### 后端常见问题
1. **Jakarta EE 导入错误**：必须使用 `jakarta.*` 而不是 `javax.*` 导入
2. **构建失败**：确保安装了 Java 17+ 并正确设置了 JAVA_HOME
3. **实体保存失败**：确保实体类继承 `BaseEntityV2.CUBaseEntityV2<String>`
4. **BaseEntityV2 字段引用错误**：使用 `getCreatedAt()` 而不是 `getCreateTime()`
5. **逻辑删除查询错误**：使用 `eq(Entity::getDeleteMark, 0)` 而不是 `isNull(Entity::getDeleteMark)`
6. **日期格式化函数错误**：使用 `formatToDate()` 和 `formatToDateTime()`

### 前端常见问题  
7. **TypeScript 类型错误**：运行 `pnpm type:check` 识别问题
8. **API 响应处理错误**：必须检查 `response.code === 200` 才能访问 `response.data`
9. **表格数据不显示**：确保 API 返回包含 `data.list` 和 `data.total` 的分页格式
10. **选择器选项不显示**：确保选项数据使用 `{id, fullName}` 格式，不要使用 `{value, label}`
11. **组件样式问题**：检查是否正确导入了 Ant Design Vue 样式
12. **路由跳转失败**：确保使用 Vue Router 4.x 语法

## 开发资源

### 编码规范文档
- **核心规则**：`docs/ai-assistants/common/core-rules.md` - AI工具必读的核心开发规则
- **框架规范**：`docs/framework-standards/` - XACE框架完整开发规范  
- **AI工具配置**：`docs/ai-assistants/` - Augment和Claude等AI工具的专用配置

### 重要规范文件
- `docs/ai-assistants/common/core-rules.md` - 核心开发规则摘要（AI工具必读）
- `docs/framework-standards/backend/02_CODING_STANDARDS.md` - 基本编码规范
- `docs/framework-standards/backend/03_ENTITY_LAYER.md` - 实体层规范（包含BaseEntityV2使用指南）
- `docs/framework-standards/frontend/04_API_STYLES.md` - API 调用规范
- `docs/framework-standards/frontend/03_XACE_COMPONENTS.md` - XACE 组件使用规范

### 最新开发资源
- **数据库**: `xace-service/xace-db/` - 包含MySQL初始化脚本和迁移脚本
- **项目模板功能**: 新增项目模板中心，支持模板管理和配置
- **API文档**: 运行应用后访问 `/doc/api` (使用Knife4j)
- **Claude快速命令**: `docs/ai-assistants/claude/quick-commands.md`
- **业务配置优化**: 支持部门配置化，详见 `业务部门配置化优化方案.md`

### 学习资源
- [Spring Boot 官方文档](https://spring.io/projects/spring-boot)
- [Vue 3 官方文档](https://vuejs.org/)
- [Ant Design Vue 组件库](https://antdv.com/)
- [MyBatis-Plus 官方文档](https://baomidou.com/)