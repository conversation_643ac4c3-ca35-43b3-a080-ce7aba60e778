import type { App } from 'vue';
import { Button } from './Button';
import {
  Input,
  InputNumber,
  Layout,
  Form,
  Switch,
  Dropdown,
  Menu,
  Select,
  Table,
  Checkbox,
  Tabs,
  Collapse,
  Card,
  Tooltip,
  Row,
  Col,
  Popconfirm,
  Divider,
  Alert,
  AutoComplete,
  Cascader,
  Rate,
  Slider,
  Avatar,
  Tag,
  Space,
  Steps,
  Popover,
  Radio,
  Progress,
  Image,
  Upload,
  Statistic,
  List,
  Spin,
  Empty,
} from 'ant-design-vue';

// 直接导入 List 子组件
import ListItem from 'ant-design-vue/es/list/Item';
import ListItemMeta from 'ant-design-vue/es/list/ItemMeta';

import { BasicHelp, BasicCaption } from '/@/components/Basic';
import { XhAlert } from '/@/components/Xh/Alert';
import { XhAreaSelect } from '/@/components/Xh/AreaSelect';
import { XhAutoComplete } from '/@/components/Xh/AutoComplete';
import { XhButton } from '/@/components/Xh/Button';
import { XhCron } from '/@/components/Xh/Cron';
import { XhCascader } from '/@/components/Xh/Cascader';
import { XhCheckbox, XhCheckboxSingle } from '/@/components/Xh/Checkbox';
import { XhColorPicker } from '/@/components/Xh/ColorPicker';
import { XhDatePicker, XhDateRange, XhTimePicker, XhTimeRange } from '/@/components/Xh/DatePicker';
import { XhDivider } from '/@/components/Xh/Divider';
import { XhIconPicker } from '/@/components/Xh/IconPicker';
import { XhInput, XhTextarea } from '/@/components/Xh/Input';
import { XhInputNumber } from '/@/components/Xh/InputNumber';
import { XhLink } from '/@/components/Xh/Link';
import { XhOpenData } from '/@/components/Xh/OpenData';
import {
  XhOrganizeSelect,
  XhDepSelect,
  XhPosSelect,
  XhGroupSelect,
  XhRoleSelect,
  XhUserSelect,
  XhUsersSelect,
  XhUserSelectDropdown,
} from '/@/components/Xh/Organize';
import { XhQrcode } from '/@/components/Xh/Qrcode';
import { XhBarcode } from '/@/components/Xh/Barcode';
import { XhRadio } from '/@/components/Xh/Radio';
import { XhSelect } from '/@/components/Xh/Select';
import { XhRate } from '/@/components/Xh/Rate';
import { XhSlider } from '/@/components/Xh/Slider';
import { XhSign } from '/@/components/Xh/Sign';
import { XhSwitch } from '/@/components/Xh/Switch';
import { XhText } from '/@/components/Xh/Text';
import { XhTreeSelect } from '/@/components/Xh/TreeSelect';
import { XhUploadFile, XhUploadImg, XhUploadImgSingle, XhUploadBtn } from '/@/components/Xh/Upload';
import { Tinymce } from '/@/components/Tinymce/index';
import { XhNumberRange } from '/@/components/Xh/NumberRange';
import { XhRelationFormAttr } from '/@/components/Xh/RelationFormAttr';
import { XhPopupSelect } from '/@/components/Xh/PopupSelect';
import { XhPopupAttr } from '/@/components/Xh/PopupAttr';
import { XhCalculate } from '/@/components/Xh/Calculate';
import { XhSysVars } from '/@/components/Xh/SysVars';
import { XhApiSelect } from '/@/components/Xh/ApiSelect';

const XhEditor = Tinymce;
XhEditor.name = 'XhEditor';
const XhGroupTitle = BasicCaption;
XhGroupTitle.name = 'XhGroupTitle';

export function registerGlobComp(app: App) {
  app
    .use(Input)
    .use(InputNumber)
    .use(Button)
    .use(Layout)
    .use(Form)
    .use(Switch)
    .use(Dropdown)
    .use(Menu)
    .use(Select)
    .use(Table)
    .use(Checkbox)
    .use(Tabs)
    .use(Card)
    .use(Collapse)
    .use(Tooltip)
    .use(Row)
    .use(Col)
    .use(Popconfirm)
    .use(Popover)
    .use(Divider)
    .use(Slider)
    .use(Rate)
    .use(Alert)
    .use(AutoComplete)
    .use(Cascader)
    .use(Avatar)
    .use(Tag)
    .use(Space)
    .use(Steps)
    .use(Radio)
    .use(Progress)
    .use(Image)
    .use(Upload)
    .use(Statistic)
    .use(List)
    .use(Spin)
    .use(Empty)
    .use(BasicHelp)
    .use(XhAlert)
    .use(XhRate)
    .use(XhSlider)
    .use(XhAreaSelect)
    .use(XhAutoComplete)
    .use(XhButton)
    .use(XhCron)
    .use(XhCascader)
    .use(XhCheckbox)
    .use(XhCheckboxSingle)
    .use(XhColorPicker)
    .use(XhDatePicker)
    .use(XhDateRange)
    .use(XhTimePicker)
    .use(XhTimeRange)
    .use(XhDivider)
    .use(XhGroupTitle)
    .use(XhIconPicker)
    .use(XhInput)
    .use(XhTextarea)
    .use(XhInputNumber)
    .use(XhLink)
    .use(XhOrganizeSelect)
    .use(XhDepSelect)
    .use(XhPosSelect)
    .use(XhGroupSelect)
    .use(XhRoleSelect)
    .use(XhUserSelect)
    .use(XhUsersSelect)
    .use(XhOpenData)
    .use(XhQrcode)
    .use(XhBarcode)
    .use(XhRadio)
    .use(XhSelect)
    .use(XhSign)
    .use(XhSwitch)
    .use(XhText)
    .use(XhTreeSelect)
    .use(XhEditor)
    .use(XhRelationFormAttr)
    .use(XhPopupSelect)
    .use(XhPopupAttr)
    .use(XhNumberRange)
    .use(XhCalculate)
    .use(XhUploadFile)
    .use(XhUploadImg)
    .use(XhUploadImgSingle)
    .use(XhUploadBtn)
    .use(XhUserSelectDropdown)
    .use(XhSysVars)
    .use(XhApiSelect);

  // 注册 List 子组件
  app.component('AListItem', ListItem);
  app.component('AListItemMeta', ListItemMeta);
}
