package com.xinghuo.project.core.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.core.entity.ProjectBaseEntity;
import com.xinghuo.project.core.model.ProjectPagination;
import com.xinghuo.project.core.model.dto.FavoriteStatusUpdateDTO;
import com.xinghuo.project.core.model.dto.ProjectExtendedDTO;
import com.xinghuo.project.core.model.dto.SimpleProjectInfoDTO;
import com.xinghuo.project.core.model.projectBase.ProjectBaseInfoVO;
import com.xinghuo.project.core.model.projectBase.ProjectBaseInfoForm;
import com.xinghuo.project.core.service.ProjectService;
import com.xinghuo.project.core.service.UserBehaviorService;
import com.xinghuo.permission.service.UserService;
import com.xinghuo.permission.entity.UserEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Collectors;

/**
 * 项目基础管理控制器
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Tag(name = "项目基础管理", description = "项目基础管理相关接口")
@RestController
@RequestMapping("/api/project/core/project")
public class ProjectBaseController {

    @Resource
    private ProjectService projectService;

    @Resource
    private UserBehaviorService userBehaviorService;

    @Resource
    private UserService userService;

    /**
     * 获取项目列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取项目列表")
    public ActionResult<PageListVO<ProjectBaseEntity>> list(@RequestBody ProjectPagination pagination) {
        List<ProjectBaseEntity> list = projectService.getList(pagination);
        PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
        return ActionResult.page(list, page);
    }

    /**
     * 根据项目类型获取项目列表
     */
    @GetMapping("/getListByProjectType/{projectType}")
    @Operation(summary = "根据项目类型获取项目列表")
    public ActionResult<List<ProjectBaseEntity>> getListByProjectType(
            @Parameter(description = "项目类型") @PathVariable String projectType) {
        List<ProjectBaseEntity> list = projectService.getListByProjectType(projectType);
        return ActionResult.success(list);
    }

    /**
     * 根据项目状态获取项目列表
     */
    @GetMapping("/getListByStatus/{status}")
    @Operation(summary = "根据项目状态获取项目列表")
    public ActionResult<List<ProjectBaseEntity>> getListByStatus(
            @Parameter(description = "项目状态") @PathVariable String status) {
        List<ProjectBaseEntity> list = projectService.getListByStatus(status);
        return ActionResult.success(list);
    }

    /**
     * 根据项目经理ID获取项目列表
     */
    @GetMapping("/getListByManagerId/{managerId}")
    @Operation(summary = "根据项目经理ID获取项目列表")
    public ActionResult<List<ProjectBaseEntity>> getListByManagerId(
            @Parameter(description = "项目经理ID") @PathVariable String managerId) {
        List<ProjectBaseEntity> list = projectService.getListByManagerId(managerId);
        return ActionResult.success(list);
    }

    /**
     * 根据部门ID获取项目列表
     */
    @GetMapping("/getListByDeptId/{deptId}")
    @Operation(summary = "根据部门ID获取项目列表")
    public ActionResult<List<ProjectBaseEntity>> getListByDeptId(
            @Parameter(description = "部门ID") @PathVariable String deptId) {
        List<ProjectBaseEntity> list = projectService.getListByDeptId(deptId);
        return ActionResult.success(list);
    }

    /**
     * 获取项目详情
     *
     * 在返回项目详情的同时，异步记录用户访问行为
     */
    @GetMapping("/getInfo/{id}")
    @Operation(summary = "获取项目详情")
    public ActionResult<ProjectBaseEntity> getInfo(
            @Parameter(description = "项目ID") @PathVariable String id) {
        ProjectBaseEntity entity = projectService.getInfo(id);
        if (entity == null) {
            return ActionResult.fail("项目不存在");
        }

        // 异步记录用户访问行为
        try {
            String currentUserId = UserProvider.getUser().getUserId();
            if (currentUserId != null) {
                userBehaviorService.logVisit(currentUserId, id, "PROJECT");
            }
        } catch (Exception e) {
            log.warn("记录项目访问行为失败: {}", e.getMessage());
            // 不影响主业务流程，仅记录警告日志
        }

        return ActionResult.success(entity);
    }

    /**
     * 创建项目
     */
    @PostMapping("/create")
    @Operation(summary = "创建项目")
    public ActionResult<String> create(@RequestBody @Valid ProjectBaseEntity entity) {
        String id = projectService.create(entity);
        return ActionResult.success("创建成功", id);
    }

    /**
     * 更新项目
     */
    @PutMapping("/update/{id}")
    @Operation(summary = "更新项目")
    public ActionResult<String> update(
            @Parameter(description = "项目ID") @PathVariable String id,
            @RequestBody @Valid ProjectBaseEntity entity) {
        projectService.update(id, entity);
        return ActionResult.success("更新成功");
    }

    /**
     * 删除项目
     */
    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除项目")
    public ActionResult<String> delete(
            @Parameter(description = "项目ID") @PathVariable String id) {
        projectService.delete(id);
        return ActionResult.success("删除成功");
    }

    /**
     * 更新项目状态
     */
    @PutMapping("/updateStatus/{id}")
    @Operation(summary = "更新项目状态")
    public ActionResult<String> updateStatus(
            @Parameter(description = "项目ID") @PathVariable String id,
            @RequestParam String status) {
        projectService.updateStatus(id, status);
        return ActionResult.success("状态更新成功");
    }

    /**
     * 更新项目健康度
     */
    @PutMapping("/updateHealth/{id}")
    @Operation(summary = "更新项目健康度")
    public ActionResult<String> updateHealth(
            @Parameter(description = "项目ID") @PathVariable String id,
            @RequestParam String health) {
        projectService.updateHealth(id, health);
        return ActionResult.success("健康度更新成功");
    }

    /**
     * 检查项目编码是否存在
     */
    @GetMapping("/checkCodeExists")
    @Operation(summary = "检查项目编码是否存在")
    public ActionResult<Boolean> checkCodeExists(
            @RequestParam String code,
            @RequestParam(required = false) String excludeId) {
        boolean exists = projectService.isExistByCode(code, excludeId);
        return ActionResult.success(exists);
    }

    /**
     * 获取项目统计数据
     */
    @PostMapping("/getProjectStatistics")
    @Operation(summary = "获取项目统计数据")
    public ActionResult<List<Map<String, Object>>> getProjectStatistics(@RequestBody Map<String, Object> params) {
        List<Map<String, Object>> statistics = projectService.getProjectStatistics(params);
        return ActionResult.success(statistics);
    }

    /**
     * 获取项目健康度统计
     */
    @PostMapping("/getProjectHealthStatistics")
    @Operation(summary = "获取项目健康度统计")
    public ActionResult<List<Map<String, Object>>> getProjectHealthStatistics(@RequestBody Map<String, Object> params) {
        List<Map<String, Object>> statistics = projectService.getProjectHealthStatistics(params);
        return ActionResult.success(statistics);
    }

    /**
     * 项目归档
     */
    @PutMapping("/archive/{id}")
    @Operation(summary = "项目归档")
    public ActionResult<String> archiveProject(
            @Parameter(description = "项目ID") @PathVariable String id) {
        projectService.archiveProject(id);
        return ActionResult.success("项目归档成功");
    }

    /**
     * 项目激活
     */
    @PutMapping("/activate/{id}")
    @Operation(summary = "项目激活")
    public ActionResult<String> activateProject(
            @Parameter(description = "项目ID") @PathVariable String id) {
        projectService.activateProject(id);
        return ActionResult.success("项目激活成功");
    }

    /**
     * 获取项目选择列表
     */
    @GetMapping("/getSelectList")
    @Operation(summary = "获取项目选择列表")
    public ActionResult<List<Map<String, Object>>> getSelectList(
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String status) {
        Map<String, Object> params = new HashMap<>();
        if (keyword != null) {
            params.put("keyword", keyword);
        }
        if (status != null) {
            params.put("status", status);
        }

        // 构建简化的查询条件
        ProjectPagination pagination = new ProjectPagination();
        pagination.setKeyword(keyword);
        pagination.setStatus(status);
        pagination.setPageSize(100); // 限制返回数量

        List<ProjectBaseEntity> list = projectService.getList(pagination);

        // 转换为选择列表格式
        List<Map<String, Object>> selectList = list.stream().map(project -> {
            Map<String, Object> option = new HashMap<>();
            option.put("id", project.getId());
            option.put("code", project.getCode());
            option.put("name", project.getFullName());
            option.put("fullName", project.getCode() + " - " + project.getFullName());
            option.put("status", project.getStatus());
            return option;
        }).collect(Collectors.toList());

        return ActionResult.success(selectList);
    }

    /**
     * 获取项目简要信息
     */
    @GetMapping("/home/<USER>")
    @Operation(summary = "获取项目简要信息")
    public ActionResult<SimpleProjectInfoDTO> getSimpleProjectInfo(@RequestParam String projectID) {
        SimpleProjectInfoDTO dto = projectService.getSimpleProjectInfo(projectID);
        if (dto == null) {
            return ActionResult.fail("项目不存在");
        }
        return ActionResult.success(dto);
    }

    /**
     * 获取项目扩展信息
     */
    @GetMapping("/getExtendedInfo/{id}")
    @Operation(summary = "获取项目扩展信息")
    public ActionResult<ProjectExtendedDTO> getExtendedProjectInfo(
            @Parameter(description = "项目ID") @PathVariable String id) {
        ProjectExtendedDTO dto = projectService.getExtendedProjectInfo(id);
        if (dto == null) {
            return ActionResult.fail("项目不存在");
        }
        return ActionResult.success(dto);
    }

    /**
     * 项目搜索
     */
    @PostMapping("/search")
    @Operation(summary = "项目搜索")
    public ActionResult<List<ProjectExtendedDTO>> searchProjects(@RequestBody Map<String, Object> searchParams) {
        List<ProjectExtendedDTO> list = projectService.searchProjects(searchParams);
        return ActionResult.success(list);
    }

    /**
     * 快速创建项目
     */
    @PostMapping("/saveFastNew")
    @Operation(summary = "快速创建项目")
    public ActionResult<String> saveFastNew(@RequestBody Map<String, Object> requestData) {
        // 解析请求数据
        Map<String, Object> projectFastNewParamVo = (Map<String, Object>) requestData.get("projectFastNewParamVo");
        Map<String, Object> info = (Map<String, Object>) requestData.get("info");
        List<Map<String, Object>> projectTeamList = (List<Map<String, Object>>) requestData.get("projectTeamList");

        // 转换项目基本信息
        ProjectBaseEntity projectInfo = BeanCopierUtils.copy(info, ProjectBaseEntity.class);

        String projectId = projectService.saveFastNew(projectInfo, projectTeamList, projectFastNewParamVo);
        return ActionResult.success(projectId);
    }

    /**
     * 统一的项目查询接口（支持多种查询类型）
     *
     * 支持的查询类型：
     * - ALL: 全部项目（默认）
     * - MY_MANAGED: 我管理的项目
     * - MY_PARTICIPATED: 我参与的项目
     * - MY_FAVORITE: 我关注的项目
     * - RECENT_VISITED: 最近访问的项目
     * - ADVANCED: 高级查询
     */
    @PostMapping("/getListByType")
    @Operation(summary = "统一项目查询接口", description = "支持多种查询类型的统一项目列表接口")
    public ActionResult<PageListVO<ProjectBaseInfoVO>> getListByType(@RequestBody ProjectPagination pagination) {
        List<ProjectBaseEntity> list = projectService.getList(pagination);

        // 转换为VO并填充用户名称
        List<ProjectBaseInfoVO> voList = BeanCopierUtils.copyList(list, ProjectBaseInfoVO.class);

        // 收集所有需要查询的用户ID
        List<String> userIds = voList.stream()
                .map(ProjectBaseInfoVO::getManagerId)
                .filter(StrXhUtil::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());

        // 批量查询用户信息
        Map<String, String> userNameMap = new HashMap<>();
        if (!userIds.isEmpty()) {
            List<UserEntity> users = userService.getUserName(userIds, true);
            for (UserEntity user : users) {
                userNameMap.put(user.getId(), user.getRealName());
            }
        }

        // 填充用户名称
        for (ProjectBaseInfoVO vo : voList) {
            if (StrXhUtil.isNotEmpty(vo.getManagerId())) {
                vo.setManagerName(userNameMap.get(vo.getManagerId()));
            }
        }

        PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
        return ActionResult.page(voList, page);
    }

    /**
     * 获取我参与的项目（兼容性接口）
     *
     * @deprecated 推荐使用 getListByType 接口，设置 queryType = "MY_PARTICIPATED"
     */
    @PostMapping("/getMyParticipatedProjects")
    @Operation(summary = "获取我参与的项目")
    @Deprecated
    public ActionResult<PageListVO<ProjectBaseEntity>> getMyParticipatedProjects(
            @RequestParam(required = false) String userId,
            @RequestBody(required = false) ProjectPagination pagination) {
        if (pagination == null) {
            pagination = new ProjectPagination();
        }
        pagination.setQueryType("MY_PARTICIPATED");
        if (StrXhUtil.isNotEmpty(userId)) {
            pagination.setUserId(userId);
        }

        List<ProjectBaseEntity> list = projectService.getList(pagination);
        PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
        return ActionResult.page(list, page);
    }

    /**
     * 获取我管理的项目（兼容性接口）
     *
     * @deprecated 推荐使用 getListByType 接口，设置 queryType = "MY_MANAGED"
     */
    @PostMapping("/getMyManagedProjects")
    @Operation(summary = "获取我管理的项目")
    @Deprecated
    public ActionResult<PageListVO<ProjectBaseEntity>> getMyManagedProjects(
            @RequestParam(required = false) String userId,
            @RequestBody(required = false) ProjectPagination pagination) {
        if (pagination == null) {
            pagination = new ProjectPagination();
        }
        pagination.setQueryType("MY_MANAGED");
        if (StrXhUtil.isNotEmpty(userId)) {
            pagination.setUserId(userId);
        }

        List<ProjectBaseEntity> list = projectService.getList(pagination);
        PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
        return ActionResult.page(list, page);
    }

    /**
     * 获取我关注的项目（兼容性接口）
     *
     * @deprecated 推荐使用 getListByType 接口，设置 queryType = "MY_FAVORITE"
     */
    @PostMapping("/getMyFollowedProjects")
    @Operation(summary = "获取我关注的项目")
    @Deprecated
    public ActionResult<PageListVO<ProjectBaseEntity>> getMyFollowedProjects(
            @RequestParam(required = false) String userId,
            @RequestBody(required = false) ProjectPagination pagination) {
        if (pagination == null) {
            pagination = new ProjectPagination();
        }
        pagination.setQueryType("MY_FAVORITE");
        if (StrXhUtil.isNotEmpty(userId)) {
            pagination.setUserId(userId);
        }

        List<ProjectBaseEntity> list = projectService.getList(pagination);
        PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
        return ActionResult.page(list, page);
    }

    /**
     * 获取最近访问的项目（兼容性接口）
     *
     * @deprecated 推荐使用 getListByType 接口，设置 queryType = "RECENT_VISITED"
     */
    @PostMapping("/getRecentlyVisitedProjects")
    @Operation(summary = "获取最近访问的项目")
    @Deprecated
    public ActionResult<PageListVO<ProjectBaseEntity>> getRecentlyVisitedProjects(
            @RequestParam(required = false) String userId,
            @RequestBody(required = false) ProjectPagination pagination) {
        if (pagination == null) {
            pagination = new ProjectPagination();
        }
        pagination.setQueryType("RECENT_VISITED");
        if (StrXhUtil.isNotEmpty(userId)) {
            pagination.setUserId(userId);
        }

        List<ProjectBaseEntity> list = projectService.getList(pagination);
        PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
        return ActionResult.page(list, page);
    }

    /**
     * 更新项目访问记录
     */
    @PostMapping("/updateVisitRecord/{projectId}")
    @Operation(summary = "更新项目访问记录")
    public ActionResult<String> updateProjectVisitRecord(
            @Parameter(description = "项目ID") @PathVariable String projectId,
            @RequestParam String userId) {
        projectService.updateProjectVisitRecord(projectId, userId);
        return ActionResult.success("访问记录更新成功");
    }

    /**
     * 关注/取消关注项目
     */
    @PostMapping("/follow/{projectId}")
    @Operation(summary = "关注/取消关注项目")
    public ActionResult<String> followProject(
            @Parameter(description = "项目ID") @PathVariable String projectId,
            @RequestParam String userId,
            @RequestParam boolean follow) {
        projectService.followProject(projectId, userId, follow);
        return ActionResult.success(follow ? "关注成功" : "取消关注成功");
    }

    /**
     * 收藏/取消收藏项目
     */
    @PostMapping("/favorite/{projectId}")
    @Operation(summary = "收藏/取消收藏项目")
    public ActionResult<String> favoriteProject(
            @Parameter(description = "项目ID") @PathVariable String projectId,
            @RequestParam String userId,
            @RequestParam boolean favorite) {
        projectService.favoriteProject(projectId, userId, favorite);
        return ActionResult.success(favorite ? "收藏成功" : "取消收藏成功");
    }

    /**
     * 更新当前用户对指定项目的关注状态
     *
     * 使用 RESTful 风格的 API 设计，通过请求体传递关注状态
     */
    @PostMapping("/{id}/favorite")
    @Operation(summary = "更新项目关注状态")
    public ActionResult<String> updateFavoriteStatus(
            @Parameter(description = "项目ID") @PathVariable String id,
            @RequestBody @Valid FavoriteStatusUpdateDTO request) {
        try {
            // 获取当前登录用户ID
            String currentUserId = UserProvider.getUser().getUserId();
            if (currentUserId == null) {
                return ActionResult.fail("用户未登录");
            }

            // 调用用户行为服务切换关注状态
            userBehaviorService.toggleFavorite(currentUserId, id, "PROJECT", request.isFavorite());

            return ActionResult.success(request.isFavorite() ? "关注成功" : "取消关注成功");
        } catch (Exception e) {
            log.error("更新项目关注状态失败: projectId={}, isFavorite={}", id, request.isFavorite(), e);
            return ActionResult.fail("操作失败: " + e.getMessage());
        }
    }

    /**
     * 检查当前用户是否已关注指定项目
     */
    @GetMapping("/{id}/favorite/status")
    @Operation(summary = "检查项目关注状态")
    public ActionResult<Boolean> getFavoriteStatus(
            @Parameter(description = "项目ID") @PathVariable String id) {
        try {
            // 获取当前登录用户ID
            String currentUserId = UserProvider.getUser().getUserId();
            if (currentUserId == null) {
                return ActionResult.fail("用户未登录");
            }

            // 检查关注状态
            boolean isFavorite = userBehaviorService.isFavorite(currentUserId, id, "PROJECT");
            return ActionResult.success(isFavorite);
        } catch (Exception e) {
            log.error("检查项目关注状态失败: projectId={}", id, e);
            return ActionResult.fail("检查失败: " + e.getMessage());
        }
    }

    /**
     * 获取项目基本信息 (用于项目信息页面查看)
     */
    @GetMapping("/info/{id}")
    @Operation(summary = "获取项目基本信息")
    public ActionResult<ProjectBaseInfoVO> getProjectInfo(
            @Parameter(description = "项目ID") @PathVariable String id) {
        try {
            ProjectBaseInfoVO projectInfo = projectService.getProjectBasicInfo(id);
            if (projectInfo == null) {
                return ActionResult.fail("项目不存在");
            }

            // 异步记录用户访问行为
            try {
                String currentUserId = UserProvider.getUser().getUserId();
                if (currentUserId != null) {
                    userBehaviorService.logVisit(currentUserId, id, "PROJECT");
                }
            } catch (Exception e) {
                log.warn("记录项目访问行为失败: {}", e.getMessage());
            }

            return ActionResult.success(projectInfo);
        } catch (Exception e) {
            log.error("获取项目基本信息失败: projectId={}", id, e);
            return ActionResult.fail("获取项目信息失败: " + e.getMessage());
        }
    }

    /**
     * 更新项目基本信息 (用于项目信息页面编辑)
     */
    @PutMapping("/info/{id}")
    @Operation(summary = "更新项目基本信息")
    public ActionResult<String> updateProjectInfo(
            @Parameter(description = "项目ID") @PathVariable String id,
            @RequestBody @Valid ProjectBaseInfoForm form) {
        try {
            projectService.updateProjectBasicInfo(id, form);
            return ActionResult.success("项目信息更新成功");
        } catch (Exception e) {
            log.error("更新项目基本信息失败: projectId={}", id, e);
            return ActionResult.fail("更新项目信息失败: " + e.getMessage());
        }
    }
}
