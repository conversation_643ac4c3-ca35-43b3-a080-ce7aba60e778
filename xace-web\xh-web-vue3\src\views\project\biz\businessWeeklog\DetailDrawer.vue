<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" title="商机周报详情" width="900px" :showFooter="false">
    <div class="p-4">
      <a-descriptions :column="2" bordered>
        <a-descriptions-item label="商机名称" :span="2">
          {{ detailData.projName }}
        </a-descriptions-item>

        <a-descriptions-item label="项目类型">
          {{ detailData.projTypeName }}
        </a-descriptions-item>

        <a-descriptions-item label="项目级别">
          <a-tag :color="detailData.projectLevel === 'A' ? 'red' : detailData.projectLevel === 'B' ? 'orange' : 'default'">
            {{ detailData.projectLevelName }}
          </a-tag>
        </a-descriptions-item>

        <a-descriptions-item label="所属分部">
          {{ detailData.fbName }}
        </a-descriptions-item>

        <a-descriptions-item label="项目经理">
          {{ detailData.ownName }}
        </a-descriptions-item>

        <a-descriptions-item label="开始日期">
          {{ detailData.startDate }}
        </a-descriptions-item>

        <a-descriptions-item label="结束日期">
          {{ detailData.endDate }}
        </a-descriptions-item>

        <a-descriptions-item label="录入日期">
          {{ detailData.inputDate }}
        </a-descriptions-item>

        <a-descriptions-item label="状态">
          <a-tag :color="getStatusColor(detailData.status)">
            {{ getStatusText(detailData.status) }}
          </a-tag>
        </a-descriptions-item>

        <a-descriptions-item label="显示状态">
          <a-tag :color="detailData.showStatus === 1 ? 'green' : 'default'">
            {{ detailData.showStatus === 1 ? '显示' : '隐藏' }}
          </a-tag>
        </a-descriptions-item>

        <a-descriptions-item label="项目备注" :span="2">
          <div class="whitespace-pre-wrap">{{ detailData.projNote || '无' }}</div>
        </a-descriptions-item>

        <a-descriptions-item label="本次进度" :span="2">
          <div class="whitespace-pre-wrap">{{ detailData.note || '无' }}</div>
        </a-descriptions-item>

        <a-descriptions-item label="下一步计划" :span="2">
          <div class="whitespace-pre-wrap">{{ detailData.plan || '无' }}</div>
        </a-descriptions-item>

        <a-descriptions-item label="存在风险/问题" :span="2">
          <div class="whitespace-pre-wrap text-red-600">{{ detailData.risk || '无' }}</div>
        </a-descriptions-item>

        <a-descriptions-item label="创建人">
          {{ detailData.createdByName }}
        </a-descriptions-item>

        <a-descriptions-item label="创建时间">
          {{ detailData.createdAt }}
        </a-descriptions-item>

        <a-descriptions-item label="最后更新人">
          {{ detailData.lastUpdatedByName }}
        </a-descriptions-item>

        <a-descriptions-item label="最后更新时间">
          {{ detailData.lastUpdatedAt }}
        </a-descriptions-item>
      </a-descriptions>
    </div>
  </BasicDrawer>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { BusinessWeeklogStatusOptions } from '/@/api/project/biz/model/businessWeeklogModel';

  const detailData = ref<any>({});

  // 获取状态颜色
  function getStatusColor(status: number) {
    const option = BusinessWeeklogStatusOptions.find(item => item.value === status);
    return option?.color || 'default';
  }

  // 获取状态文字
  function getStatusText(status: number) {
    const option = BusinessWeeklogStatusOptions.find(item => item.value === status);
    return option?.label || '未知';
  }

  // 注册抽屉
  const [registerDrawer] = useDrawerInner(async data => {
    detailData.value = data.record || {};
  });
</script>
