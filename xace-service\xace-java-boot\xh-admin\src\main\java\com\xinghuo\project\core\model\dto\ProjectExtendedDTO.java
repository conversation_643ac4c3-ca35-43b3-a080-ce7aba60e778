package com.xinghuo.project.core.model.dto;

import com.xinghuo.project.core.entity.ProjectBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

/**
 * 项目扩展DTO
 * 包含项目基本信息和扩展信息
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectExtendedDTO extends ProjectBaseEntity {

    /**
     * 项目状态名称
     */
    private String statusName;

    /**
     * 项目类型名称
     */
    private String typeName;

    /**
     * 项目经理姓名
     */
    private String managerName;

    /**
     * 项目经理显示名称
     */
    private String managerDisplayName;

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 部门路径
     */
    private String departmentPath;

    /**
     * 项目群名称
     */
    private String programName;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 合同名称
     */
    private String contractName;

    /**
     * 显示名称（项目名称 + 编码）
     */
    private String displayName;

    /**
     * 项目图标
     */
    private String icon;

    /**
     * 项目团队列表
     */
    private List<ProjectTeamDTO> projectTeamList;

    /**
     * 项目经理列表
     */
    private List<ProjectTeamDTO> managers;

    /**
     * 项目状态可更新的数据类型映射
     */
    private Map<String, List<String>> projectStatusCanUpdateDataMap;

    /**
     * 项目统计信息
     */
    private ProjectStatisticsDTO statistics;

    /**
     * 项目自定义字段映射
     */
    private Map<String, Object> customFields;

    /**
     * 项目标签列表
     */
    private List<String> tagList;

    /**
     * 是否收藏
     */
    private Boolean isFavorite;

    /**
     * 是否关注
     */
    private Boolean isFollowed;

    /**
     * 最近访问时间
     */
    private java.util.Date lastVisitTime;

    /**
     * 项目权限列表
     */
    private List<String> permissions;

    /**
     * 项目进度百分比
     */
    private Double progressPercent;

    /**
     * 当前阶段名称
     */
    private String currentPhaseName;

    /**
     * 项目风险等级
     */
    private String riskLevel;

    /**
     * 项目风险等级名称
     */
    private String riskLevelName;

    /**
     * 项目健康度名称
     */
    private String healthName;

    /**
     * 优先级名称
     */
    private String priorityName;

    /**
     * 是否异常完成
     */
    private Boolean abnormalCompleted;

    /**
     * 工作流状态
     */
    private Integer wfStatus;

    /**
     * 工作流状态名称
     */
    private String wfStatusName;

    /**
     * 备选状态
     */
    private Integer alternativeStatus;

    /**
     * 备选状态名称
     */
    private String alternativeStatusName;

    /**
     * 预期结束时间
     */
    private java.util.Date expectEndTime;

    /**
     * 项目模板ID
     */
    private Integer schemaId;

    /**
     * 项目模板名称
     */
    private String schemaName;

    /**
     * 扩展信息
     */
    private Map<String, Object> extendInfo;
}
