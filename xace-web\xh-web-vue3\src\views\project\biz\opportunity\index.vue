<template>
  <div class="xh-content-wrapper">
    <div class="xh-content-wrapper-center">
      <div class="xh-content-wrapper-content">
        <BasicTable @register="registerTable" class="opportunity-table">
          <template #tableTitle>
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="handleCreate">新增商机</a-button>
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-copy" @click="handleCopyBusiness" class="ml-2">复制商机</a-button>
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-chart" @click="handleAnalysis" class="ml-2">报表分析</a-button>
          </template>
          <template #form-custSelect="{ model, field }">
            <CustomerSelect v-model:value="model[field]" placeholder="请选择客户单位" />
          </template>
          <template #form-userSelect="{ model, field }">
            <UserSelect v-model:value="model[field]" placeholder="请选择项目负责人" />
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'projectLevel'">
              <a-tag :color="getProjectLevelColor(record.projectLevel)">
                {{ record.projectLevel }}
              </a-tag>
            </template>
            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ record.status }}
              </a-tag>
            </template>
            <template v-if="column.dataIndex === 'action'">
              <TableAction :actions="getActions(record)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <FormDrawer @register="registerFormDrawer" @reload="reload" />
    <DetailDrawer @register="registerDetailDrawer" @reload="reload" />
    <StatusForm @register="registerStatusForm" @reload="reload" />
    <TrackForm @register="registerTrackForm" @reload="reload" />
    <CopyBusinessModal @register="registerCopyModal" @success="handleCopySuccess" />
  </div>
</template>

<script lang="ts" setup>
  import { h } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { getBusinessList, deleteBusiness, getBusinessInfo, BusinessModel } from '/@/api/project/business';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useModal } from '/@/components/Modal';
  import { useDrawer } from '/@/components/Drawer';
  import { formatToDate } from '/@/utils/dateUtil';
  import FormDrawer from './FormDrawer.vue';
  import DetailDrawer from './DetailDrawer.vue';
  import StatusForm from './StatusForm.vue';
  import TrackForm from './TrackForm.vue';
  import CopyBusinessModal from './CopyBusinessModal.vue';
  import CustomerSelect from '/@/views/project/components/CustomerSelect.vue';
  import UserSelect from '/@/components/Xh/Organize/src/UserSelect.vue';
  import { useRouter } from 'vue-router';

  const { createMessage } = useMessage();
  const [registerFormDrawer, { openDrawer }] = useDrawer();
  const [registerDetailDrawer, { openDrawer: openDetailDrawer }] = useDrawer();
  const [registerStatusForm, { openModal: openStatusModal }] = useModal();
  const [registerTrackForm, { openModal: openTrackModal }] = useModal();
  const [registerCopyModal, { openModal: openCopyModal }] = useModal();
  const router = useRouter();

  defineOptions({ name: 'project-biz-opportunity' });

  // 项目等级颜色映射
  const projectLevelColors = {
    A: 'red',
    B: 'orange',
    C: 'blue',
    D: 'green',
  };

  // 商机状态颜色映射
  const statusColors = {
    跟踪中: 'blue',
    方案报价中: 'purple',
    商务谈判中: 'orange',
    已签: 'green',
    已废弃: 'red',
    明年跟踪: 'cyan',
  };

  // 获取项目等级颜色
  function getProjectLevelColor(level: string) {
    return projectLevelColors[level] || 'default';
  }

  // 获取商机状态颜色
  function getStatusColor(status: string) {
    return statusColors[status] || 'default';
  }

  // 根据记录状态获取操作按钮
  function getActions(record: BusinessModel) {
    const actions = [
      {
        icon: 'clarity:info-standard-line',
        label: '查看详情',
        onClick: handleView.bind(null, record),
      },
      {
        icon: 'clarity:note-edit-line',
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        ifShow: record.status !== '已签' && record.status !== '已废弃',
      },
    ];

    // 根据状态添加不同的操作按钮
    if (record.status !== '已签' && record.status !== '已废弃') {
      actions.push({
        icon: 'ant-design:swap-outlined',
        label: '更新状态',
        onClick: handleUpdateStatus.bind(null, record),
      });

      actions.push({
        icon: 'ant-design:message-outlined',
        label: '添加跟踪记录',
        onClick: handleAddTrack.bind(null, record),
      });
    }

    if (record.projType === '提前项目') {
      actions.push({
        icon: 'ant-design:clock-circle-outlined',
        label: '工时填写状态',
        onClick: handleWorkStatus.bind(null, record),
      });
    }

    actions.push({
      icon: 'ant-design:delete-outlined',
      color: 'error',
      label: '删除',
      popConfirm: {
        title: '是否确认删除',
        confirm: handleDelete.bind(null, record),
      },
      ifShow: record.status !== '已签',
    });

    return actions;
  }

  // 表格列定义
  const columns = [
    {
      title: '项目编号',
      dataIndex: 'businessNo',
      width: 138,
      sorter: true,
    },
    {
      title: '项目等级',
      key: 'projectLevel',
      dataIndex: 'projectLevel',
      width: 138,
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
      width: 138,
    },
    {
      title: '客户单位',
      dataIndex: 'custName',
      width: 138,
    },
    {
      title: '项目负责人',
      dataIndex: 'projectLeaderName',
      width: 138,
      sorter: true,
    },
    {
      title: '软件部金额',
      dataIndex: 'deptMoney',
      width: 138,
      sorter: true,
      customRender: ({ text }) => {
        return text ? `${text}万元` : '';
      },
    },
    {
      title: '外采金额',
      dataIndex: 'purchaseMoney',
      width: 138,
      sorter: true,
      customRender: ({ text }) => {
        return text ? `${text}万元` : '';
      },
    },
    {
      title: '所属分部',
      dataIndex: 'deptName',
      width: 138,
      sorter: true,
    },
    {
      title: '商机标签',
      dataIndex: 'businessTag',
      width: 138,
      customRender: ({ text }) => {
        const colorMap = {
          可靠项目: 'green',
          竞争项目: 'orange',
          暂无经费: 'red',
        };
        const color = colorMap[text] || 'default';
        return h('span', { class: `ant-tag ant-tag-${color} !mr-1` }, text);
      },
    },
    {
      title: '项目跟踪状态',
      key: 'status',
      dataIndex: 'status',
      width: 138,
      sorter: true,
    },
    {
      title: '最后更新时间',
      dataIndex: 'lastModifyTime',
      width: 138,
      sorter: true,
    },
    {
      title: '预计落地月份',
      dataIndex: 'evaSignMonth',
      width: 138,
      sorter: true,
      customRender: ({ text }) => {
        return text ? formatToDate(text, 'YYYY-MM') : '';
      },
    },
    {
      title: '预计首比回款时间',
      dataIndex: 'evaFirstMonth',
      width: 138,
      sorter: true,
      customRender: ({ text }) => {
        return text ? formatToDate(text, 'YYYY-MM') : '';
      },
    },
    {
      title: '预计首比回款金额(万元)',
      dataIndex: 'evaFirstAmount',
      width: 138,
      sorter: true,
      customRender: ({ text }) => {
        return text ? `${text}万元` : '';
      },
    },
    {
      title: '第二笔回款时间',
      dataIndex: 'evaSecondMonth',
      width: 138,
      sorter: true,
      customRender: ({ text }) => {
        return text ? formatToDate(text, 'YYYY-MM') : '';
      },
    },
    {
      title: '二笔回款金额(万元)',
      dataIndex: 'evaSecondAmount',
      width: 138,
      sorter: true,
      customRender: ({ text }) => {
        return text ? `${text}万元` : '';
      },
    },
    {
      title: '首次外采月份',
      dataIndex: 'evaFirstexternalMonth',
      width: 138,
      customRender: ({ text }) => {
        return text ? formatToDate(text, 'YYYY-MM') : '';
      },
    },
    {
      title: '首次外采金额',
      dataIndex: 'evaFirstexternalAmount',
      width: 138,
      customRender: ({ text }) => {
        return text ? `${text}万元` : '';
      },
    },
    {
      title: '二次外采月份',
      dataIndex: 'evaSecondexternalMonth',
      width: 138,
      customRender: ({ text }) => {
        return text ? formatToDate(text, 'YYYY-MM') : '';
      },
    },
    {
      title: '二次外采金额',
      dataIndex: 'evaSecondexternalAmount',
      width: 138,
      customRender: ({ text }) => {
        return text ? `${text}万元` : '';
      },
    },
    {
      title: '一部金额',
      dataIndex: 'oneDeptMoney',
      width: 138,
      customRender: ({ text }) => {
        return text ? `${text}万元` : '';
      },
    },
    {
      title: '一部外采',
      dataIndex: 'oneDeptPurchase',
      width: 138,
      customRender: ({ text }) => {
        return text ? `${text}万元` : '';
      },
    },
    {
      title: '二部金额',
      dataIndex: 'twoDeptMoney',
      width: 138,
      customRender: ({ text }) => {
        return text ? `${text}万元` : '';
      },
    },
    {
      title: '二部外采',
      dataIndex: 'twoDeptPurchase',
      width: 138,
      customRender: ({ text }) => {
        return text ? `${text}万元` : '';
      },
    },
    {
      title: '综合金额',
      dataIndex: 'comprehensiveMoney',
      width: 138,
      customRender: ({ text }) => {
        return text ? `${text}万元` : '';
      },
    },
    {
      title: '综合外采',
      dataIndex: 'comprehensivePurchase',
      width: 139,
      customRender: ({ text }) => {
        return text ? `${text}万元` : '';
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 160,
      fixed: 'right' as const,
    },
  ];

  // 搜索表单配置
  const searchFormSchema = [
    {
      field: 'projectName',
      label: '项目名称',
      component: 'Input',
      colProps: { span: 6 },
    },
    {
      field: 'projectLevel',
      label: '项目等级',
      component: 'Select',
      componentProps: {
        options: [
          { fullName: '全部', id: '' },
          { fullName: 'A', id: 'A' },
          { fullName: 'B', id: 'B' },
          { fullName: 'C', id: 'C' },
          { fullName: 'D', id: 'D' },
        ],
      },
      colProps: { span: 6 },
    },
    {
      field: 'status',
      label: '项目跟踪状态',
      component: 'Select',
      componentProps: {
        options: [
          { fullName: '全部', id: '' },
          { fullName: '跟踪中', id: '跟踪中' },
          { fullName: '方案报价中', id: '方案报价中' },
          { fullName: '商务谈判中', id: '商务谈判中' },
          { fullName: '已签', id: '已签' },
          { fullName: '已废弃', id: '已废弃' },
          { fullName: '明年跟踪', id: '明年跟踪' },
        ],
      },
      colProps: { span: 6 },
    },
    {
      field: 'projectLeader',
      label: '项目负责人',
      component: 'Input',
      slot: 'userSelect',
      colProps: { span: 6 },
    },
    {
      field: 'deptId',
      label: '所属分部',
      component: 'Select',
      componentProps: {
        options: [
          { fullName: '全部', id: '' },
          { fullName: '软件一部', id: '软件一部' },
          { fullName: '软件二部', id: '软件二部' },
          { fullName: '综合部', id: '综合部' },
        ],
      },
      colProps: { span: 6 },
    },
    {
      field: '[evaSignMonthStart, evaSignMonthEnd]',
      label: '预计落地月份',
      component: 'RangePicker',
      componentProps: {
        picker: 'month',
        valueFormat: 'YYYY-MM',
      },
      colProps: { span: 6 },
    },
    {
      field: 'businessTag',
      label: '商机标签',
      component: 'Select',
      componentProps: {
        options: [
          { fullName: '全部', id: '' },
          { fullName: '可靠项目', id: '可靠项目' },
          { fullName: '竞争项目', id: '竞争项目' },
          { fullName: '暂无经费', id: '暂无经费' },
        ],
      },
      colProps: { span: 6 },
    },
  ];

  // 注册表格
  const [registerTable, { reload }] = useTable({
    title: '商机列表',
    api: getBusinessList,
    columns,
    formConfig: {
      alwaysShowLines: 1,
      showAdvancedButton: true,
      labelWidth: 80,
      schemas: searchFormSchema,
    },
    useSearchForm: true,
    showTableSetting: true,
    bordered: true,
    showIndexColumn: true,
    canResize: true,
    showSummary: true,
    pagination: {
      current: 1,
      pageSize: 20,
      total: 0,
      showTotal: (total, range) => `显示 ${range[0]}-${range[1]} 条，共 ${total} 条`,
      showSizeChanger: true,
      showQuickJumper: true,
      pageSizeOptions: ['10', '20', '50', '100'],
    },
    showPagination: true,
    beforeFetch: params => {
      // 处理日期范围
      if (params['[evaSignMonthStart, evaSignMonthEnd]'] && params['[evaSignMonthStart, evaSignMonthEnd]'].length === 2) {
        const [start, end] = params['[evaSignMonthStart, evaSignMonthEnd]'];
        params.evaSignMonthStart = start;
        params.evaSignMonthEnd = end;
        delete params['[evaSignMonthStart, evaSignMonthEnd]'];
      }

      if (params['[createTimeStart, createTimeEnd]'] && params['[createTimeStart, createTimeEnd]'].length === 2) {
        const [start, end] = params['[createTimeStart, createTimeEnd]'];
        params.createTimeStart = formatToDate(start);
        params.createTimeEnd = formatToDate(end);
        delete params['[createTimeStart, createTimeEnd]'];
      }

      return params;
    },
    summaryFunc: data => {
      // 基于当前页面的数据计算合计
      if (!data || !Array.isArray(data) || data.length === 0) {
        return [];
      }

      // 计算各项金额合计
      let totalDeptMoney = 0;
      let totalPurchaseMoney = 0;
      let totalEvaFirstAmount = 0;
      let totalEvaSecondAmount = 0;
      let totalEvaFirstexternalAmount = 0;
      let totalEvaSecondexternalAmount = 0;
      let totalOneDeptMoney = 0;
      let totalOneDeptPurchase = 0;
      let totalTwoDeptMoney = 0;
      let totalTwoDeptPurchase = 0;
      let totalComprehensiveMoney = 0;
      let totalComprehensivePurchase = 0;

      data.forEach(item => {
        // 软件部金额
        if (item.deptMoney) {
          totalDeptMoney += parseFloat(item.deptMoney) || 0;
        }
        // 外采金额
        if (item.purchaseMoney) {
          totalPurchaseMoney += parseFloat(item.purchaseMoney) || 0;
        }
        // 预计首比回款金额
        if (item.evaFirstAmount) {
          totalEvaFirstAmount += parseFloat(item.evaFirstAmount) || 0;
        }
        // 第二笔回款金额
        if (item.evaSecondAmount) {
          totalEvaSecondAmount += parseFloat(item.evaSecondAmount) || 0;
        }
        // 首次外采金额
        if (item.evaFirstexternalAmount) {
          totalEvaFirstexternalAmount += parseFloat(item.evaFirstexternalAmount) || 0;
        }
        // 二次外采金额
        if (item.evaSecondexternalAmount) {
          totalEvaSecondexternalAmount += parseFloat(item.evaSecondexternalAmount) || 0;
        }
        // 一部金额
        if (item.oneDeptMoney) {
          totalOneDeptMoney += parseFloat(item.oneDeptMoney) || 0;
        }
        // 一部外采
        if (item.oneDeptPurchase) {
          totalOneDeptPurchase += parseFloat(item.oneDeptPurchase) || 0;
        }
        // 二部金额
        if (item.twoDeptMoney) {
          totalTwoDeptMoney += parseFloat(item.twoDeptMoney) || 0;
        }
        // 二部外采
        if (item.twoDeptPurchase) {
          totalTwoDeptPurchase += parseFloat(item.twoDeptPurchase) || 0;
        }
        // 综合金额
        if (item.comprehensiveMoney) {
          totalComprehensiveMoney += parseFloat(item.comprehensiveMoney) || 0;
        }
        // 综合外采
        if (item.comprehensivePurchase) {
          totalComprehensivePurchase += parseFloat(item.comprehensivePurchase) || 0;
        }
      });

      return [
        {
          _index: '汇总',
          projectName: '合计',
          deptMoney: totalDeptMoney > 0 ? `${totalDeptMoney.toFixed(2)}万元` : '',
          purchaseMoney: totalPurchaseMoney > 0 ? `${totalPurchaseMoney.toFixed(2)}万元` : '',
          evaFirstAmount: totalEvaFirstAmount > 0 ? `${totalEvaFirstAmount.toFixed(2)}万元` : '',
          evaSecondAmount: totalEvaSecondAmount > 0 ? `${totalEvaSecondAmount.toFixed(2)}万元` : '',
          evaFirstexternalAmount: totalEvaFirstexternalAmount > 0 ? `${totalEvaFirstexternalAmount.toFixed(2)}万元` : '',
          evaSecondexternalAmount: totalEvaSecondexternalAmount > 0 ? `${totalEvaSecondexternalAmount.toFixed(2)}万元` : '',
          oneDeptMoney: totalOneDeptMoney > 0 ? `${totalOneDeptMoney.toFixed(2)}万元` : '',
          oneDeptPurchase: totalOneDeptPurchase > 0 ? `${totalOneDeptPurchase.toFixed(2)}万元` : '',
          twoDeptMoney: totalTwoDeptMoney > 0 ? `${totalTwoDeptMoney.toFixed(2)}万元` : '',
          twoDeptPurchase: totalTwoDeptPurchase > 0 ? `${totalTwoDeptPurchase.toFixed(2)}万元` : '',
          comprehensiveMoney: totalComprehensiveMoney > 0 ? `${totalComprehensiveMoney.toFixed(2)}万元` : '',
          comprehensivePurchase: totalComprehensivePurchase > 0 ? `${totalComprehensivePurchase.toFixed(2)}万元` : '',
        },
      ];
    },
  });

  // 新增商机
  function handleCreate() {
    openDrawer(true, {
      isUpdate: false,
    });
  }

  // 编辑商机
  function handleEdit(record: BusinessModel) {
    openDrawer(true, {
      record,
      isUpdate: true,
    });
  }

  // 复制商机
  async function handleCopyBusiness() {
    try {
      console.log('打开复制商机选择模态框');
      openCopyModal(true, {});
    } catch (error) {
      console.error('打开复制商机模态框失败:', error);
      createMessage.error('打开复制商机模态框失败');
    }
  }

  // 处理复制成功
  async function handleCopySuccess(businessData: any) {
    try {
      console.log('复制商机成功，准备创建新商机:', businessData);

      // 打开新增商机表单，并预填充复制的数据
      openDrawer(true, {
        isUpdate: false,
        copyData: businessData,
      });

      createMessage.success('已复制商机数据，请完善信息后保存');
    } catch (error) {
      console.error('处理复制商机失败:', error);
      createMessage.error('处理复制商机失败');
    }
  }

  // 查看商机详情
  function handleView(record: BusinessModel) {
    openDetailDrawer(true, {
      id: record.id,
    });
  }

  // 更新商机状态
  function handleUpdateStatus(record: BusinessModel) {
    openStatusModal(true, {
      record,
    });
  }

  // 添加跟踪记录
  function handleAddTrack(record: BusinessModel) {
    openTrackModal(true, {
      record,
    });
  }

  // 工时填写状态
  function handleWorkStatus(record: BusinessModel) {
    // 这里可以实现工时填写状态的切换逻辑
    createMessage.info('工时填写状态功能将在后续实现');
  }

  // 跳转到报表分析页面
  function handleAnalysis() {
    router.push('/project/biz/opportunity/analysis');
  }

  // 删除商机
  async function handleDelete(record: BusinessModel) {
    try {
      await deleteBusiness(record.id);
      createMessage.success('删除成功');
      reload();
    } catch (error: any) {
      createMessage.error(error.message || '删除失败');
    }
  }
</script>

<style lang="less" scoped>
  .opportunity-table {
    // 修复汇总行样式
    :deep(.ant-table-summary) {
      background-color: #fafafa;
      font-weight: 500;

      .ant-table-summary-cell {
        border-top: 1px solid #d9d9d9;
      }
    }
  }
</style>
