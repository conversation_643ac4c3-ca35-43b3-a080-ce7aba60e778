package com.xinghuo.project.biz.dao;

import com.xinghuo.common.base.dao.XHBaseMapper;
import com.xinghuo.project.biz.entity.CustomerContactEntity;
import org.apache.ibatis.annotations.Mapper;

/**
 * 客户联系人Mapper接口
 *
 * 注意：此Mapper接口仅继承XHBaseMapper，不再定义自定义SQL方法。
 * 所有数据操作都通过Service层使用MyBatis Plus的QueryWrapper + Lambda表达式实现。
 *
 * 编程规范：
 * - 优先使用QueryWrapper + Lambda表达式替代自定义SQL
 * - 避免字段名硬编码，使用Entity::getField方式
 * - 保持Mapper接口简洁，复杂逻辑在Service层实现
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Mapper
public interface CustomerContactMapper extends XHBaseMapper<CustomerContactEntity> {
    // 不再定义自定义SQL方法，所有操作通过Service层的QueryWrapper + Lambda实现
}
