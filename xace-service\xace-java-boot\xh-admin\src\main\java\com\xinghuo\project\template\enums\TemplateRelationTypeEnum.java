package com.xinghuo.project.template.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 模板关联类型枚举
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Getter
@AllArgsConstructor
public enum TemplateRelationTypeEnum {

    /**
     * 阶段计划模板
     */
    PHASE_TEMPLATE("PHASE_TEMPLATE", "阶段计划模板"),

    /**
     * 交付物模板
     */
    WORKPRODUCT_TEMPLATE("WORKPRODUCT_TEMPLATE", "交付物模板"),

    /**
     * 风险模板
     */
    RISK_TEMPLATE("RISK_TEMPLATE", "风险模板"),

    /**
     * 成本预算模板
     */
    COST_TEMPLATE("COST_TEMPLATE", "成本预算模板"),

    /**
     * 质量检查模板
     */
    QUALITY_TEMPLATE("QUALITY_TEMPLATE", "质量检查模板"),

    /**
     * 项目模板（通常作为目标模板类型）
     */
    PROJECT_TEMPLATE("PROJECT_TEMPLATE", "项目模板");

    private final String code;
    private final String message;

    /**
     * 根据代码获取枚举
     */
    public static TemplateRelationTypeEnum getByCode(String code) {
        for (TemplateRelationTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 检查代码是否有效
     */
    public static boolean isValidCode(String code) {
        return getByCode(code) != null;
    }
}
