package com.xinghuo.project.template.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.template.dao.RiskLibraryMapper;
import com.xinghuo.project.template.entity.RiskLibraryEntity;
import com.xinghuo.project.template.model.RiskLibraryPagination;
import com.xinghuo.project.template.service.RiskLibraryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 标准项目风险库服务实现类
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Slf4j
@Service
public class RiskLibraryServiceImpl extends BaseServiceImpl<RiskLibraryMapper, RiskLibraryEntity> implements RiskLibraryService {

    @Override
    public List<RiskLibraryEntity> getList(RiskLibraryPagination pagination) {
        QueryWrapper<RiskLibraryEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<RiskLibraryEntity> lambda = queryWrapper.lambda();

        // 根据风险编码模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getCode())) {
            lambda.like(RiskLibraryEntity::getCode, pagination.getCode());
        }

        // 根据风险标题模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getTitle())) {
            lambda.like(RiskLibraryEntity::getTitle, pagination.getTitle());
        }

        // 根据状态精确查询
        if (StrXhUtil.isNotEmpty(pagination.getStatus())) {
            lambda.eq(RiskLibraryEntity::getStatus, pagination.getStatus());
        }

        // 根据风险类别查询
        if (StrXhUtil.isNotEmpty(pagination.getRiskCategoryId())) {
            lambda.eq(RiskLibraryEntity::getRiskCategoryId, pagination.getRiskCategoryId());
        }

        // 根据概率等级查询
        if (StrXhUtil.isNotEmpty(pagination.getDefaultProbabilityLevelId())) {
            lambda.eq(RiskLibraryEntity::getDefaultProbabilityLevelId, pagination.getDefaultProbabilityLevelId());
        }

        // 根据影响等级查询
        if (StrXhUtil.isNotEmpty(pagination.getDefaultImpactLevelId())) {
            lambda.eq(RiskLibraryEntity::getDefaultImpactLevelId, pagination.getDefaultImpactLevelId());
        }

        // 创建时间范围查询
        if (pagination.getCreateTimeStart() != null) {
            lambda.ge(RiskLibraryEntity::getCreatedAt, pagination.getCreateTimeStart());
        }
        if (pagination.getCreateTimeEnd() != null) {
            lambda.le(RiskLibraryEntity::getCreatedAt, pagination.getCreateTimeEnd());
        }

        // 根据创建用户查询
        if (StrXhUtil.isNotEmpty(pagination.getCreatedBy())) {
            lambda.eq(RiskLibraryEntity::getCreatedBy, pagination.getCreatedBy());
        }

        // 根据描述关键字查询
        if (StrXhUtil.isNotEmpty(pagination.getDescriptionKeyword())) {
            lambda.like(RiskLibraryEntity::getDescription, pagination.getDescriptionKeyword());
        }

        // 根据应对策略关键字查询
        if (StrXhUtil.isNotEmpty(pagination.getStrategyKeyword())) {
            lambda.like(RiskLibraryEntity::getSuggestedStrategy, pagination.getStrategyKeyword());
        }

        // 根据应对措施关键字查询
        if (StrXhUtil.isNotEmpty(pagination.getActionsKeyword())) {
            lambda.like(RiskLibraryEntity::getSuggestedActions, pagination.getActionsKeyword());
        }

        // 根据关键字搜索编码或标题
        String keyword = pagination.getKeyword();
        if (StrXhUtil.isNotEmpty(keyword)) {
            lambda.and(wrapper -> wrapper
                    .like(RiskLibraryEntity::getCode, keyword)
                    .or()
                    .like(RiskLibraryEntity::getTitle, keyword)
            );
        }

        // 排序：按创建时间倒序
        lambda.orderByDesc(RiskLibraryEntity::getCreatedAt);
        
        return processDataType(queryWrapper, pagination);
    }

    @Override
    public List<RiskLibraryEntity> getListByStatus(String status) {
        if (StrXhUtil.isEmpty(status)) {
            return new ArrayList<>();
        }

        QueryWrapper<RiskLibraryEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(RiskLibraryEntity::getStatus, status)
                .orderByDesc(RiskLibraryEntity::getCreatedAt);
        
        return list(queryWrapper);
    }

    @Override
    public RiskLibraryEntity getInfo(String id) {
        if (StrXhUtil.isEmpty(id)) {
            return null;
        }
        return getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String create(RiskLibraryEntity entity) {
        if (entity == null) {
            throw new RuntimeException("风险库信息不能为空");
        }

        // 验证必填字段
        if (StrXhUtil.isEmpty(entity.getTitle())) {
            throw new RuntimeException("风险标题不能为空");
        }

        if (StrXhUtil.isEmpty(entity.getRiskCategoryId())) {
            throw new RuntimeException("风险类别不能为空");
        }

        // 检查编码是否重复
        if (StrXhUtil.isNotEmpty(entity.getCode()) && isExistByCode(entity.getCode(), null)) {
            throw new RuntimeException("风险编码已存在");
        }

        // 检查标题是否重复
        if (isExistByTitle(entity.getTitle(), null)) {
            throw new RuntimeException("风险标题已存在");
        }

        // 如果没有提供编码，自动生成
        if (StrXhUtil.isEmpty(entity.getCode())) {
            entity.setCode(generateCode());
        }

        // 设置默认状态
        if (StrXhUtil.isEmpty(entity.getStatus())) {
            entity.setStatus("draft"); // 默认草稿状态
        }

        save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(String id, RiskLibraryEntity entity) {
        if (StrXhUtil.isEmpty(id) || entity == null) {
            throw new RuntimeException("参数不能为空");
        }

        RiskLibraryEntity existEntity = getById(id);
        if (existEntity == null) {
            throw new RuntimeException("风险库不存在");
        }

        // 验证必填字段
        if (StrXhUtil.isEmpty(entity.getTitle())) {
            throw new RuntimeException("风险标题不能为空");
        }

        if (StrXhUtil.isEmpty(entity.getRiskCategoryId())) {
            throw new RuntimeException("风险类别不能为空");
        }

        // 检查编码是否重复
        if (StrXhUtil.isNotEmpty(entity.getCode()) && isExistByCode(entity.getCode(), id)) {
            throw new RuntimeException("风险编码已存在");
        }

        // 检查标题是否重复
        if (isExistByTitle(entity.getTitle(), id)) {
            throw new RuntimeException("风险标题已存在");
        }

        entity.setId(id);
        updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        if (StrXhUtil.isEmpty(id)) {
            throw new RuntimeException("风险库ID不能为空");
        }

        RiskLibraryEntity entity = getById(id);
        if (entity == null) {
            throw new RuntimeException("风险库不存在");
        }

        // TODO: 检查是否被其他模块引用，如果被引用则不允许删除
        // 例如：检查是否被项目风险、风险评估等引用

        removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new RuntimeException("风险库ID列表不能为空");
        }

        for (String id : ids) {
            delete(id);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(String id, String status) {
        if (StrXhUtil.isEmpty(id) || StrXhUtil.isEmpty(status)) {
            throw new RuntimeException("参数不能为空");
        }

        RiskLibraryEntity entity = getById(id);
        if (entity == null) {
            throw new RuntimeException("风险库不存在");
        }

        UpdateWrapper<RiskLibraryEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .eq(RiskLibraryEntity::getId, id)
                .set(RiskLibraryEntity::getStatus, status);
        
        update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateStatus(List<String> ids, String status) {
        if (ids == null || ids.isEmpty() || StrXhUtil.isEmpty(status)) {
            throw new RuntimeException("参数不能为空");
        }

        UpdateWrapper<RiskLibraryEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .in(RiskLibraryEntity::getId, ids)
                .set(RiskLibraryEntity::getStatus, status);
        
        update(updateWrapper);
    }

    @Override
    public boolean isExistByCode(String code, String excludeId) {
        if (StrXhUtil.isEmpty(code)) {
            return false;
        }

        QueryWrapper<RiskLibraryEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(RiskLibraryEntity::getCode, code);
        
        if (StrXhUtil.isNotEmpty(excludeId)) {
            queryWrapper.lambda().ne(RiskLibraryEntity::getId, excludeId);
        }

        return count(queryWrapper) > 0;
    }

    @Override
    public boolean isExistByTitle(String title, String excludeId) {
        if (StrXhUtil.isEmpty(title)) {
            return false;
        }

        QueryWrapper<RiskLibraryEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(RiskLibraryEntity::getTitle, title);
        
        if (StrXhUtil.isNotEmpty(excludeId)) {
            queryWrapper.lambda().ne(RiskLibraryEntity::getId, excludeId);
        }

        return count(queryWrapper) > 0;
    }

    @Override
    public RiskLibraryEntity getByCode(String code) {
        if (StrXhUtil.isEmpty(code)) {
            return null;
        }

        QueryWrapper<RiskLibraryEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(RiskLibraryEntity::getCode, code);
        
        return getOne(queryWrapper);
    }

    @Override
    public List<RiskLibraryEntity> getSelectList(String keyword) {
        QueryWrapper<RiskLibraryEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<RiskLibraryEntity> lambda = queryWrapper.lambda();

        // 只查询已发布状态的记录
        lambda.eq(RiskLibraryEntity::getStatus, "published");

        // 根据关键字搜索
        if (StrXhUtil.isNotEmpty(keyword)) {
            lambda.and(wrapper -> wrapper
                    .like(RiskLibraryEntity::getCode, keyword)
                    .or()
                    .like(RiskLibraryEntity::getTitle, keyword)
            );
        }

        // 排序：按标题升序
        lambda.orderByAsc(RiskLibraryEntity::getTitle);
        
        return list(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void publish(String id) {
        updateStatus(id, "published");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void archive(String id) {
        updateStatus(id, "archived");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchPublish(List<String> ids) {
        batchUpdateStatus(ids, "published");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchArchive(List<String> ids) {
        batchUpdateStatus(ids, "archived");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String copy(String id, String newTitle) {
        if (StrXhUtil.isEmpty(id) || StrXhUtil.isEmpty(newTitle)) {
            throw new RuntimeException("参数不能为空");
        }

        RiskLibraryEntity sourceEntity = getById(id);
        if (sourceEntity == null) {
            throw new RuntimeException("源风险库不存在");
        }

        // 检查新标题是否重复
        if (isExistByTitle(newTitle, null)) {
            throw new RuntimeException("风险标题已存在");
        }

        // 创建新的风险库
        RiskLibraryEntity newEntity = new RiskLibraryEntity();
        newEntity.setTitle(newTitle);
        newEntity.setCode(generateCode()); // 生成新的编码
        newEntity.setDescription(sourceEntity.getDescription());
        newEntity.setStatus("draft"); // 复制的风险库默认为草稿状态
        newEntity.setRiskCategoryId(sourceEntity.getRiskCategoryId());
        newEntity.setDefaultProbabilityLevelId(sourceEntity.getDefaultProbabilityLevelId());
        newEntity.setDefaultImpactLevelId(sourceEntity.getDefaultImpactLevelId());
        newEntity.setSuggestedStrategy(sourceEntity.getSuggestedStrategy());
        newEntity.setSuggestedActions(sourceEntity.getSuggestedActions());

        save(newEntity);
        return newEntity.getId();
    }

    @Override
    public String generateCode() {
        String prefix = "RISK";
        String randomStr = RandomUtil.enUuId().substring(0, 8).toUpperCase();
        String code = prefix + randomStr;

        // 确保编码不重复
        while (isExistByCode(code, null)) {
            randomStr = RandomUtil.enUuId().substring(0, 8).toUpperCase();
            code = prefix + randomStr;
        }

        return code;
    }

    @Override
    public Map<String, Object> getRiskLibraryUsageInfo(String id) {
        Map<String, Object> result = new HashMap<>();
        
        if (StrXhUtil.isEmpty(id)) {
            result.put("total", 0);
            result.put("details", new ArrayList<>());
            return result;
        }

        // TODO: 实现风险库使用情况统计
        // 统计在以下模块中的使用情况：
        // 1. 项目风险登记表
        // 2. 风险评估
        // 3. 其他相关模块

        result.put("total", 0);
        result.put("details", new ArrayList<>());
        
        return result;
    }

    @Override
    public List<RiskLibraryEntity> getListByRiskCategory(String riskCategoryId) {
        if (StrXhUtil.isEmpty(riskCategoryId)) {
            return new ArrayList<>();
        }

        QueryWrapper<RiskLibraryEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(RiskLibraryEntity::getRiskCategoryId, riskCategoryId)
                .eq(RiskLibraryEntity::getStatus, "published") // 只查询已发布状态
                .orderByAsc(RiskLibraryEntity::getTitle);
        
        return list(queryWrapper);
    }

    @Override
    public List<RiskLibraryEntity> getListByProbabilityLevel(String probabilityLevelId) {
        if (StrXhUtil.isEmpty(probabilityLevelId)) {
            return new ArrayList<>();
        }

        QueryWrapper<RiskLibraryEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(RiskLibraryEntity::getDefaultProbabilityLevelId, probabilityLevelId)
                .eq(RiskLibraryEntity::getStatus, "published") // 只查询已发布状态
                .orderByAsc(RiskLibraryEntity::getTitle);
        
        return list(queryWrapper);
    }

    @Override
    public List<RiskLibraryEntity> getListByImpactLevel(String impactLevelId) {
        if (StrXhUtil.isEmpty(impactLevelId)) {
            return new ArrayList<>();
        }

        QueryWrapper<RiskLibraryEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(RiskLibraryEntity::getDefaultImpactLevelId, impactLevelId)
                .eq(RiskLibraryEntity::getStatus, "published") // 只查询已发布状态
                .orderByAsc(RiskLibraryEntity::getTitle);
        
        return list(queryWrapper);
    }
}
