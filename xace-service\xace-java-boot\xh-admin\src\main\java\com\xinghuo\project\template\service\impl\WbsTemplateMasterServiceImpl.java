package com.xinghuo.project.template.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.template.dao.WbsTemplateMasterMapper;
import com.xinghuo.project.template.dao.WbsTemplateDetailMapper;
import com.xinghuo.project.template.entity.WbsTemplateMasterEntity;
import com.xinghuo.project.template.entity.WbsTemplateDetailEntity;
import com.xinghuo.project.template.entity.ActivityLibraryEntity;
import com.xinghuo.project.template.model.WbsTemplateMasterPagination;
import com.xinghuo.project.template.model.dto.WbsTemplateVO;
import com.xinghuo.project.template.service.WbsTemplateMasterService;
import com.xinghuo.project.template.service.ActivityLibraryService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * WBS计划模板主表服务实现类
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Slf4j
@Service
public class WbsTemplateMasterServiceImpl extends BaseServiceImpl<WbsTemplateMasterMapper, WbsTemplateMasterEntity> implements WbsTemplateMasterService {

    @Resource
    private WbsTemplateDetailMapper wbsTemplateDetailMapper;

    @Resource
    private ActivityLibraryService activityLibraryService;

    @Override
    public List<WbsTemplateVO> getList(WbsTemplateMasterPagination pagination) {
        QueryWrapper<WbsTemplateMasterEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<WbsTemplateMasterEntity> lambda = queryWrapper.lambda();

        // 根据模板编码模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getCode())) {
            lambda.like(WbsTemplateMasterEntity::getCode, pagination.getCode());
        }

        // 根据模板名称模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getName())) {
            lambda.like(WbsTemplateMasterEntity::getName, pagination.getName());
        }

        // 根据状态精确查询
        if (StrXhUtil.isNotEmpty(pagination.getStatus())) {
            lambda.eq(WbsTemplateMasterEntity::getStatus, pagination.getStatus());
        }

        // 创建时间范围查询
        if (pagination.getCreateTimeStart() != null) {
            lambda.ge(WbsTemplateMasterEntity::getCreatedAt, pagination.getCreateTimeStart());
        }
        if (pagination.getCreateTimeEnd() != null) {
            lambda.le(WbsTemplateMasterEntity::getCreatedAt, pagination.getCreateTimeEnd());
        }

        // 根据创建用户查询
        if (StrXhUtil.isNotEmpty(pagination.getCreatedBy())) {
            lambda.eq(WbsTemplateMasterEntity::getCreatedBy, pagination.getCreatedBy());
        }

        // 根据描述关键字查询
        if (StrXhUtil.isNotEmpty(pagination.getDescriptionKeyword())) {
            lambda.like(WbsTemplateMasterEntity::getDescription, pagination.getDescriptionKeyword());
        }

        // 根据关键字搜索编码或名称
        String keyword = pagination.getKeyword();
        if (StrXhUtil.isNotEmpty(keyword)) {
            lambda.and(wrapper -> wrapper
                    .like(WbsTemplateMasterEntity::getCode, keyword)
                    .or()
                    .like(WbsTemplateMasterEntity::getName, keyword)
            );
        }

        // 排序：按创建时间倒序
        lambda.orderByDesc(WbsTemplateMasterEntity::getCreatedAt);
        
        List<WbsTemplateMasterEntity> masterList = processDataType(queryWrapper, pagination);
        List<WbsTemplateVO> voList = BeanCopierUtils.copyList(masterList, WbsTemplateVO.class);

        // 补充统计信息
        for (WbsTemplateVO vo : voList) {
            enrichTemplateStatistics(vo);
        }

        return voList;
    }

    @Override
    public List<WbsTemplateMasterEntity> getListByStatus(String status) {
        if (StrXhUtil.isEmpty(status)) {
            return new ArrayList<>();
        }

        QueryWrapper<WbsTemplateMasterEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(WbsTemplateMasterEntity::getStatus, status)
                .orderByDesc(WbsTemplateMasterEntity::getCreatedAt);
        
        return list(queryWrapper);
    }

    @Override
    public WbsTemplateVO getDetailInfo(String id) {
        if (StrXhUtil.isEmpty(id)) {
            return null;
        }

        WbsTemplateMasterEntity masterEntity = getById(id);
        if (masterEntity == null) {
            return null;
        }

        WbsTemplateVO vo = BeanCopierUtils.copy(masterEntity, WbsTemplateVO.class);
        
        // 获取WBS明细列表
        List<WbsTemplateDetailEntity> details = getWbsDetails(id);
        vo.setWbsDetails(details);

        // 补充统计信息
        enrichTemplateStatistics(vo);

        return vo;
    }

    @Override
    public WbsTemplateMasterEntity getInfo(String id) {
        if (StrXhUtil.isEmpty(id)) {
            return null;
        }
        return getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String create(WbsTemplateVO templateVO) {
        if (templateVO == null) {
            throw new RuntimeException("WBS模板信息不能为空");
        }

        // 验证必填字段
        if (StrXhUtil.isEmpty(templateVO.getName())) {
            throw new RuntimeException("模板名称不能为空");
        }

        // 检查名称是否重复
        if (isExistByName(templateVO.getName(), null)) {
            throw new RuntimeException("模板名称已存在");
        }

        // 检查编码是否重复
        if (StrXhUtil.isNotEmpty(templateVO.getCode()) && isExistByCode(templateVO.getCode(), null)) {
            throw new RuntimeException("模板编码已存在");
        }

        // 如果没有提供编码，自动生成
        if (StrXhUtil.isEmpty(templateVO.getCode())) {
            templateVO.setCode(generateCode());
        }

        // 设置默认状态
        if (StrXhUtil.isEmpty(templateVO.getStatus())) {
            templateVO.setStatus("draft"); // 默认草稿状态
        }

        // 保存主表
        WbsTemplateMasterEntity masterEntity = BeanCopierUtils.copy(templateVO, WbsTemplateMasterEntity.class);
        save(masterEntity);

        // 保存WBS明细
        if (templateVO.getWbsDetails() != null && !templateVO.getWbsDetails().isEmpty()) {
            saveWbsDetails(masterEntity.getId(), templateVO.getWbsDetails());
        }

        return masterEntity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(String id, WbsTemplateVO templateVO) {
        if (StrXhUtil.isEmpty(id) || templateVO == null) {
            throw new RuntimeException("参数不能为空");
        }

        WbsTemplateMasterEntity existEntity = getById(id);
        if (existEntity == null) {
            throw new RuntimeException("WBS模板不存在");
        }

        // 验证必填字段
        if (StrXhUtil.isEmpty(templateVO.getName())) {
            throw new RuntimeException("模板名称不能为空");
        }

        // 检查名称是否重复
        if (isExistByName(templateVO.getName(), id)) {
            throw new RuntimeException("模板名称已存在");
        }

        // 检查编码是否重复
        if (StrXhUtil.isNotEmpty(templateVO.getCode()) && isExistByCode(templateVO.getCode(), id)) {
            throw new RuntimeException("模板编码已存在");
        }

        // 更新主表
        WbsTemplateMasterEntity masterEntity = BeanCopierUtils.copy(templateVO, WbsTemplateMasterEntity.class);
        masterEntity.setId(id);
        updateById(masterEntity);

        // 删除原有WBS明细
        QueryWrapper<WbsTemplateDetailEntity> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.lambda().eq(WbsTemplateDetailEntity::getWbsTemplateId, id);
        wbsTemplateDetailMapper.delete(deleteWrapper);

        // 保存新的WBS明细
        if (templateVO.getWbsDetails() != null && !templateVO.getWbsDetails().isEmpty()) {
            saveWbsDetails(id, templateVO.getWbsDetails());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        if (StrXhUtil.isEmpty(id)) {
            throw new RuntimeException("模板ID不能为空");
        }

        WbsTemplateMasterEntity entity = getById(id);
        if (entity == null) {
            throw new RuntimeException("WBS模板不存在");
        }

        // TODO: 检查是否被其他模块引用，如果被引用则不允许删除
        // 例如：检查是否被项目计划等引用

        // 删除WBS明细
        QueryWrapper<WbsTemplateDetailEntity> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.lambda().eq(WbsTemplateDetailEntity::getWbsTemplateId, id);
        wbsTemplateDetailMapper.delete(deleteWrapper);

        // 删除主表
        removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new RuntimeException("模板ID列表不能为空");
        }

        for (String id : ids) {
            delete(id);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(String id, String status) {
        if (StrXhUtil.isEmpty(id) || StrXhUtil.isEmpty(status)) {
            throw new RuntimeException("参数不能为空");
        }

        WbsTemplateMasterEntity entity = getById(id);
        if (entity == null) {
            throw new RuntimeException("WBS模板不存在");
        }

        UpdateWrapper<WbsTemplateMasterEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .eq(WbsTemplateMasterEntity::getId, id)
                .set(WbsTemplateMasterEntity::getStatus, status);
        
        update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateStatus(List<String> ids, String status) {
        if (ids == null || ids.isEmpty() || StrXhUtil.isEmpty(status)) {
            throw new RuntimeException("参数不能为空");
        }

        UpdateWrapper<WbsTemplateMasterEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .in(WbsTemplateMasterEntity::getId, ids)
                .set(WbsTemplateMasterEntity::getStatus, status);
        
        update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void publish(String id) {
        updateStatus(id, "published");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void archive(String id) {
        updateStatus(id, "archived");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchPublish(List<String> ids) {
        batchUpdateStatus(ids, "published");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchArchive(List<String> ids) {
        batchUpdateStatus(ids, "archived");
    }

    @Override
    public boolean isExistByName(String name, String excludeId) {
        if (StrXhUtil.isEmpty(name)) {
            return false;
        }

        QueryWrapper<WbsTemplateMasterEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(WbsTemplateMasterEntity::getName, name);
        
        if (StrXhUtil.isNotEmpty(excludeId)) {
            queryWrapper.lambda().ne(WbsTemplateMasterEntity::getId, excludeId);
        }

        return count(queryWrapper) > 0;
    }

    @Override
    public boolean isExistByCode(String code, String excludeId) {
        if (StrXhUtil.isEmpty(code)) {
            return false;
        }

        QueryWrapper<WbsTemplateMasterEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(WbsTemplateMasterEntity::getCode, code);
        
        if (StrXhUtil.isNotEmpty(excludeId)) {
            queryWrapper.lambda().ne(WbsTemplateMasterEntity::getId, excludeId);
        }

        return count(queryWrapper) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String copy(String id, String newName) {
        if (StrXhUtil.isEmpty(id) || StrXhUtil.isEmpty(newName)) {
            throw new RuntimeException("参数不能为空");
        }

        WbsTemplateVO sourceTemplate = getDetailInfo(id);
        if (sourceTemplate == null) {
            throw new RuntimeException("源WBS模板不存在");
        }

        // 检查新名称是否重复
        if (isExistByName(newName, null)) {
            throw new RuntimeException("模板名称已存在");
        }

        // 创建新的模板
        WbsTemplateVO newTemplate = BeanCopierUtils.copy(sourceTemplate, WbsTemplateVO.class);
        newTemplate.setId(null);
        newTemplate.setName(newName);
        newTemplate.setCode(generateCode()); // 生成新的编码
        newTemplate.setStatus("draft"); // 复制的模板默认为草稿状态

        // 复制WBS明细
        if (newTemplate.getWbsDetails() != null) {
            for (WbsTemplateDetailEntity detail : newTemplate.getWbsDetails()) {
                detail.setId(null);
                detail.setWbsTemplateId(null); // 将在保存时设置
            }
        }

        return create(newTemplate);
    }

    @Override
    public List<WbsTemplateMasterEntity> getSelectList(String keyword) {
        QueryWrapper<WbsTemplateMasterEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<WbsTemplateMasterEntity> lambda = queryWrapper.lambda();

        // 只查询已发布状态的记录
        lambda.eq(WbsTemplateMasterEntity::getStatus, "published");

        // 根据关键字搜索
        if (StrXhUtil.isNotEmpty(keyword)) {
            lambda.and(wrapper -> wrapper
                    .like(WbsTemplateMasterEntity::getCode, keyword)
                    .or()
                    .like(WbsTemplateMasterEntity::getName, keyword)
            );
        }

        // 排序：按名称升序
        lambda.orderByAsc(WbsTemplateMasterEntity::getName);
        
        return list(queryWrapper);
    }

    @Override
    public String generateCode() {
        String prefix = "WBS";
        String randomStr = RandomUtil.enUuId().substring(0, 8).toUpperCase();
        String code = prefix + randomStr;

        // 确保编码不重复
        while (isExistByCode(code, null)) {
            randomStr = RandomUtil.enUuId().substring(0, 8).toUpperCase();
            code = prefix + randomStr;
        }

        return code;
    }

    @Override
    public Map<String, Object> getTemplateUsageInfo(String id) {
        Map<String, Object> result = new HashMap<>();
        
        if (StrXhUtil.isEmpty(id)) {
            result.put("total", 0);
            result.put("details", new ArrayList<>());
            return result;
        }

        // TODO: 实现模板使用情况统计
        // 统计在以下模块中的使用情况：
        // 1. 项目计划
        // 2. 其他相关模块

        result.put("total", 0);
        result.put("details", new ArrayList<>());
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addActivitiesFromLibrary(String templateId, List<String> activityIds, String parentId) {
        if (StrXhUtil.isEmpty(templateId) || activityIds == null || activityIds.isEmpty()) {
            throw new RuntimeException("参数不能为空");
        }

        // 获取活动库信息
        for (String activityId : activityIds) {
            ActivityLibraryEntity activity = activityLibraryService.getById(activityId);
            if (activity != null) {
                WbsTemplateDetailEntity detail = new WbsTemplateDetailEntity();
                detail.setWbsTemplateId(templateId);
                detail.setLibraryActivityId(activityId);
                detail.setParentId(parentId);
                detail.setName(activity.getName());
                detail.setDuration(activity.getDuration());
                detail.setIsMilestone(activity.getIsMilestone());
                detail.setResponseRoleId(activity.getResponseRoleId());
                detail.setNodeType(1); // 活动类型
                
                // 设置层级和序号
                setDetailLevelAndSeq(detail, parentId);
                
                wbsTemplateDetailMapper.insert(detail);
            }
        }
    }

    @Override
    public List<WbsTemplateDetailEntity> getWbsDetails(String templateId) {
        if (StrXhUtil.isEmpty(templateId)) {
            return new ArrayList<>();
        }

        QueryWrapper<WbsTemplateDetailEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(WbsTemplateDetailEntity::getWbsTemplateId, templateId)
                .orderByAsc(WbsTemplateDetailEntity::getLevel)    // 先按层级排序
                .orderByAsc(WbsTemplateDetailEntity::getSeqNo);   // 再按序号排序

        List<WbsTemplateDetailEntity> details = wbsTemplateDetailMapper.selectList(queryWrapper);

        log.debug("查询WBS模板明细，模板ID: {}, 数量: {}", templateId, details.size());
        for (WbsTemplateDetailEntity detail : details) {
            log.debug("WBS明细: id={}, name={}, level={}, seqNo={}, parentId={}",
                     detail.getId(), detail.getName(), detail.getLevel(), detail.getSeqNo(), detail.getParentId());
        }

        return details;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveWbsDetails(String templateId, List<WbsTemplateDetailEntity> details) {
        if (StrXhUtil.isEmpty(templateId) || details == null) {
            return;
        }

        // 先删除原有的明细数据
        QueryWrapper<WbsTemplateDetailEntity> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.lambda().eq(WbsTemplateDetailEntity::getWbsTemplateId, templateId);
        wbsTemplateDetailMapper.delete(deleteWrapper);

        // 建立ID映射关系（临时索引 -> 新ID）
        Map<Integer, String> idMapping = new HashMap<>();
        // 保存原始parentId映射关系（索引 -> 原始parentId）
        Map<Integer, String> originalParentIdMapping = new HashMap<>();

        // 按层级和序号排序，确保父节点先创建
        details.sort(Comparator.comparing(WbsTemplateDetailEntity::getLevel)
                .thenComparing(detail -> detail.getSeqNo() != null ? detail.getSeqNo() : 0));

        // 重新计算每个层级的序号
        Map<Integer, Integer> levelSeqCounter = new HashMap<>();
        for (WbsTemplateDetailEntity detail : details) {
            int level = detail.getLevel() != null ? detail.getLevel() : 1;
            int seqNo = levelSeqCounter.getOrDefault(level, 0) + 1;
            levelSeqCounter.put(level, seqNo);
            detail.setSeqNo(seqNo);
        }

        // 第一遍：创建所有节点并建立ID映射
        for (int i = 0; i < details.size(); i++) {
            WbsTemplateDetailEntity detail = details.get(i);
            detail.setWbsTemplateId(templateId);

            // 保存原始parentId，稍后处理
            String originalParentId = detail.getParentId();
            originalParentIdMapping.put(i, originalParentId);
            detail.setParentId(null);

            // 生成新ID并保存
            String newId = RandomUtil.snowId();
            detail.setId(newId);

            // 设置层级（确保不为空）
            if (detail.getLevel() == null || detail.getLevel() == 0) {
                detail.setLevel(1); // 根节点层级为1
            }

            wbsTemplateDetailMapper.insert(detail);

            // 建立映射关系
            idMapping.put(i, newId);

            log.debug("创建WBS明细节点: index={}, newId={}, name={}, level={}",
                     i, newId, detail.getName(), detail.getLevel());
        }

        // 第二遍：更新parentId引用关系
        for (int i = 0; i < details.size(); i++) {
            WbsTemplateDetailEntity detail = details.get(i);
            String originalParentId = originalParentIdMapping.get(i);

            // 处理parentId引用
            if (StrXhUtil.isNotEmpty(originalParentId) && originalParentId.startsWith("temp_")) {
                try {
                    // 解析临时parentId，获取父节点索引
                    int parentIndex = Integer.parseInt(originalParentId.substring(5));
                    String newParentId = idMapping.get(parentIndex);

                    if (StrXhUtil.isNotEmpty(newParentId)) {
                        detail.setParentId(newParentId);

                        // 重新计算层级
                        WbsTemplateDetailEntity parent = wbsTemplateDetailMapper.selectById(newParentId);
                        if (parent != null) {
                            detail.setLevel(parent.getLevel() + 1);
                        }

                        // 更新数据库
                        wbsTemplateDetailMapper.updateById(detail);

                        log.debug("更新parentId引用: childId={}, parentId={}, level={}",
                                 detail.getId(), newParentId, detail.getLevel());
                    }
                } catch (NumberFormatException e) {
                    log.warn("解析临时parentId失败: {}", originalParentId);
                }
            }
        }

        log.info("保存WBS模板明细成功，模板ID: {}, 明细数量: {}", templateId, details.size());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteWbsDetails(List<String> detailIds) {
        if (detailIds == null || detailIds.isEmpty()) {
            return;
        }

        wbsTemplateDetailMapper.deleteBatchIds(detailIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void applyToProjects(String templateId, List<String> projectIds) {
        // TODO: 实现应用到项目的逻辑
        // 将WBS模板应用到指定的项目中
        log.info("应用WBS模板 {} 到项目 {}", templateId, projectIds);
    }

    /**
     * 补充模板统计信息
     */
    private void enrichTemplateStatistics(WbsTemplateVO vo) {
        if (vo == null) {
            return;
        }

        List<WbsTemplateDetailEntity> details = getWbsDetails(vo.getId());
        
        // 活动总数
        vo.setActivityCount(details.size());
        
        // 里程碑数量
        long milestoneCount = details.stream()
                .filter(detail -> detail.getIsMilestone() != null && detail.getIsMilestone() == 1)
                .count();
        vo.setMilestoneCount((int) milestoneCount);
        
        // 总工期
        BigDecimal totalDuration = details.stream()
                .filter(detail -> detail.getDuration() != null)
                .map(WbsTemplateDetailEntity::getDuration)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        vo.setTotalDuration(totalDuration);
        
        // 最大层级深度
        int maxLevel = details.stream()
                .filter(detail -> detail.getLevel() != null)
                .mapToInt(WbsTemplateDetailEntity::getLevel)
                .max()
                .orElse(0);
        vo.setMaxLevel(maxLevel);
    }

    /**
     * 设置明细的层级和序号
     */
    private void setDetailLevelAndSeq(WbsTemplateDetailEntity detail, String parentId) {
        if (StrXhUtil.isEmpty(parentId)) {
            // 根节点
            detail.setLevel(1);
            detail.setSeqNo(getNextSeqNo(detail.getWbsTemplateId(), parentId));
        } else {
            // 子节点
            WbsTemplateDetailEntity parent = wbsTemplateDetailMapper.selectById(parentId);
            if (parent != null) {
                detail.setLevel(parent.getLevel() + 1);
                detail.setSeqNo(getNextSeqNo(detail.getWbsTemplateId(), parentId));
            }
        }
    }

    /**
     * 获取下一个序号
     */
    private Integer getNextSeqNo(String templateId, String parentId) {
        QueryWrapper<WbsTemplateDetailEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(WbsTemplateDetailEntity::getWbsTemplateId, templateId)
                .eq(WbsTemplateDetailEntity::getParentId, parentId)
                .orderByDesc(WbsTemplateDetailEntity::getSeqNo)
                .last("LIMIT 1");
        
        WbsTemplateDetailEntity lastDetail = wbsTemplateDetailMapper.selectOne(queryWrapper);
        return lastDetail != null && lastDetail.getSeqNo() != null ? lastDetail.getSeqNo() + 1 : 1;
    }
}
