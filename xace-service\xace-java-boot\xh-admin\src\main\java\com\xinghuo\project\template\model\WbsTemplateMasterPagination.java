package com.xinghuo.project.template.model;

import com.xinghuo.common.base.model.Pagination;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * WBS计划模板主表分页查询参数
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WbsTemplateMasterPagination extends Pagination {

    /**
     * 模板编码
     */
    private String code;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 发布状态 (如: draft, published)
     */
    private String status;

    /**
     * 关键字搜索（编码或名称）
     */
    private String keyword;

    /**
     * 创建时间开始
     */
    private Date createTimeStart;

    /**
     * 创建时间结束
     */
    private Date createTimeEnd;

    /**
     * 创建用户ID
     */
    private String createdBy;

    /**
     * 描述关键字
     */
    private String descriptionKeyword;
}
