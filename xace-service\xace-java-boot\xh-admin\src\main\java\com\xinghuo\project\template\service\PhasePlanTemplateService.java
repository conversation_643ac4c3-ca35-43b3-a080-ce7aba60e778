package com.xinghuo.project.template.service;

import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.project.template.entity.PhasePlanTemplateEntity;
import com.xinghuo.project.template.entity.PhasePlanTemplateDetailEntity;
import com.xinghuo.project.template.model.PhasePlanTemplatePagination;
import com.xinghuo.project.template.model.dto.PhasePlanTemplateVO;

import java.util.List;
import java.util.Map;

/**
 * 阶段计划模板服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
public interface PhasePlanTemplateService extends BaseService<PhasePlanTemplateEntity> {

    /**
     * 获取阶段计划模板列表
     *
     * @param pagination 分页参数
     * @return 模板列表
     */
    List<PhasePlanTemplateVO> getList(PhasePlanTemplatePagination pagination);

    /**
     * 根据知识状态获取模板列表
     *
     * @param knStatusId 知识状态ID
     * @return 模板列表
     */
    List<PhasePlanTemplateEntity> getListByKnStatus(String knStatusId);

    /**
     * 获取模板详情（包含阶段明细）
     *
     * @param id 模板ID
     * @return 模板详情
     */
    PhasePlanTemplateVO getDetailInfo(String id);

    /**
     * 获取模板基本信息
     *
     * @param id 模板ID
     * @return 模板信息
     */
    PhasePlanTemplateEntity getInfo(String id);

    /**
     * 创建阶段计划模板
     *
     * @param templateDTO 模板信息（包含阶段明细）
     * @return 模板ID
     */
    String create(PhasePlanTemplateVO templateDTO);

    /**
     * 更新阶段计划模板
     *
     * @param id 模板ID
     * @param templateDTO 模板信息（包含阶段明细）
     */
    void update(String id, PhasePlanTemplateVO templateDTO);

    /**
     * 删除阶段计划模板
     *
     * @param id 模板ID
     */
    void delete(String id);

    /**
     * 批量删除阶段计划模板
     *
     * @param ids 模板ID列表
     */
    void batchDelete(List<String> ids);

    /**
     * 更新模板知识状态
     *
     * @param id 模板ID
     * @param knStatusId 知识状态ID
     */
    void updateKnStatus(String id, String knStatusId);

    /**
     * 批量更新知识状态
     *
     * @param ids 模板ID列表
     * @param knStatusId 知识状态ID
     */
    void batchUpdateKnStatus(List<String> ids, String knStatusId);

    /**
     * 发布模板
     *
     * @param id 模板ID
     */
    void publish(String id);

    /**
     * 归档模板
     *
     * @param id 模板ID
     */
    void archive(String id);

    /**
     * 检查模板名称是否存在
     *
     * @param name 模板名称
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean isExistByName(String name, String excludeId);

    /**
     * 获取模板选择列表
     *
     * @param keyword 关键字
     * @param knStatusId 知识状态ID
     * @return 模板列表
     */
    List<PhasePlanTemplateVO> getSelectList(String keyword, String knStatusId);

    /**
     * 复制模板
     *
     * @param id 源模板ID
     * @param newName 新模板名称
     * @return 新模板ID
     */
    String copy(String id, String newName);

    /**
     * 从标准阶段库添加阶段
     *
     * @param templateId 模板ID
     * @param phaseCodes 阶段编码列表
     * @return 添加的阶段明细列表
     */
    List<PhasePlanTemplateDetailEntity> addPhasesFromStandardLibrary(String templateId, List<String> phaseCodes);

    /**
     * 更新阶段顺序
     *
     * @param templateId 模板ID
     * @param phaseOrders 阶段顺序列表（ID和序号的映射）
     */
    void updatePhaseOrder(String templateId, List<Map<String, Object>> phaseOrders);

    /**
     * 获取模板统计信息
     *
     * @param params 查询参数
     * @return 统计信息
     */
    List<Map<String, Object>> getTemplateStatistics(Map<String, Object> params);

    /**
     * 根据项目模板ID查询关联的阶段计划模板
     *
     * @param projectTplId 项目模板ID
     * @return 阶段计划模板列表
     */
    List<PhasePlanTemplateEntity> getByProjectTemplateId(String projectTplId);

    /**
     * 更新模板与项目模板的关联关系
     * 使用通用关联表管理关联关系
     *
     * @param templateId 模板ID
     * @param projectTemplateIds 项目模板ID列表
     */
    void updateProjectTemplateRelations(String templateId, List<String> projectTemplateIds);

    /**
     * 从项目阶段计划创建模板
     *
     * @param projectId 项目ID
     * @param templateName 模板名称
     * @param description 模板描述
     * @return 新模板ID
     */
    String createFromProject(String projectId, String templateName, String description);

    /**
     * 应用模板到项目
     *
     * @param templateId 模板ID
     * @param projectId 项目ID
     * @return 应用结果
     */
    Map<String, Object> applyToProject(String templateId, String projectId);

    /**
     * 获取模板使用情况
     *
     * @param id 模板ID
     * @return 使用情况统计
     */
    Map<String, Object> getTemplateUsageInfo(String id);
}
