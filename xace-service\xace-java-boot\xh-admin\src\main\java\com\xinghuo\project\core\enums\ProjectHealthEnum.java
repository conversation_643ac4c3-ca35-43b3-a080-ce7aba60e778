package com.xinghuo.project.core.enums;

/**
 * 项目健康度枚举
 */
public enum ProjectHealthEnum {
    
    NORMAL("NORMAL", "正常"),
    WARNING("WARNING", "预警"),
    RISK("RISK", "风险"),
    CRITICAL("CRITICAL", "严重");
    
    private final String code;
    private final String name;
    
    ProjectHealthEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    /**
     * 根据代码获取枚举
     */
    public static ProjectHealthEnum getByCode(String code) {
        for (ProjectHealthEnum healthEnum : values()) {
            if (healthEnum.getCode().equals(code)) {
                return healthEnum;
            }
        }
        return null;
    }
}
