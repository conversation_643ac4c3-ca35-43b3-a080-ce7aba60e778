<template>
  <div>
    <TagFilter ref="tagFilterRef" @filter="handleFilterChange" @search="handleSearchChange" />

    <a-spin :spinning="loading">
      <a-empty v-if="filteredDatalogList.length === 0" description="暂无商机标签记录" />

      <a-timeline v-else>
        <a-timeline-item v-for="item in filteredDatalogList" :key="item.id" :color="getTypeColor(item.type)">
          <template #dot>
            <component :is="getTypeIcon(item.type)" />
          </template>

          <div class="datalog-item">
            <div class="datalog-header">
              <a-tag :color="getTypeColor(item.type)" class="datalog-type-tag">
                {{ item.type }}
              </a-tag>
              <span class="datalog-time">{{ item.createTime }}</span>
              <span class="datalog-user">操作人: {{ item.createUserName || '未知' }}</span>

              <a-tooltip title="查看详情">
                <a-button type="link" size="small" class="detail-toggle" @click="toggleDetail(item.id)">
                  <template #icon>
                    <component :is="expandedIds.includes(item.id) ? 'UpOutlined' : 'DownOutlined'" />
                  </template>
                </a-button>
              </a-tooltip>
            </div>

            <div class="datalog-content">
              <div class="datalog-summary">{{ item.changeLog || '无变更摘要' }}</div>

              <a-collapse v-if="item.details && item.details.length > 0" ghost :activeKey="expandedIds.includes(item.id) ? ['1'] : []">
                <a-collapse-panel key="1" :header="null">
                  <a-table
                    :columns="detailColumns"
                    :dataSource="item.details"
                    :pagination="false"
                    size="small"
                    :rowKey="record => record.id || Math.random().toString(36).substring(2)"
                    :bordered="true">
                    <template #bodyCell="{ column, record }">
                      <template v-if="column.key === 'oldValue'">
                        <span class="old-value">{{ record.oldValue || '-' }}</span>
                      </template>
                      <template v-if="column.key === 'newValue'">
                        <span class="new-value">{{ record.newValue || '-' }}</span>
                      </template>
                    </template>
                  </a-table>
                </a-collapse-panel>
              </a-collapse>

              <div v-if="item.note" class="datalog-note">
                <a-divider style="margin: 8px 0" />
                <div>备注: {{ item.note }}</div>
              </div>
            </div>
          </div>
        </a-timeline-item>
      </a-timeline>
    </a-spin>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, onMounted, PropType, h } from 'vue';
  import { getBusinessDatalogListByBusinessId, BusinessDatalogModel } from '/@/api/project/businessDatalog';
  import { useMessage } from '/@/hooks/web/useMessage';
  import {
    EditOutlined,
    SwapOutlined,
    UserOutlined,
    MessageOutlined,
    DollarOutlined,
    TeamOutlined,
    UpOutlined,
    DownOutlined,
    TagsOutlined,
    FileTextOutlined,
  } from '@ant-design/icons-vue';
  import TagFilter from './TagFilter.vue';

  const props = defineProps({
    businessId: {
      type: String as PropType<string>,
      required: true,
    },
  });

  const { createMessage } = useMessage();
  const datalogList = ref<BusinessDatalogModel[]>([]);
  const loading = ref(false);
  const expandedIds = ref<string[]>([]);
  const tagFilterRef = ref(null);

  // 筛选条件
  const filterType = ref<string | null>(null);
  const searchParams = ref<{ field: string | null; value: string | null }>({
    field: null,
    value: null,
  });

  // 详情表格列定义
  const detailColumns = [
    {
      title: '字段',
      dataIndex: 'label',
      width: 150,
    },
    {
      title: '旧值',
      key: 'oldValue',
      dataIndex: 'oldValue',
      width: 200,
    },
    {
      title: '新值',
      key: 'newValue',
      dataIndex: 'newValue',
      width: 200,
    },
  ];

  // 筛选后的数据列表
  const filteredDatalogList = computed(() => {
    let result = [...datalogList.value];

    // 按类型筛选
    if (filterType.value) {
      result = result.filter(item => item.type === filterType.value);
    }

    // 按搜索条件筛选
    if (searchParams.value.field && searchParams.value.value) {
      const field = searchParams.value.field;
      const value = searchParams.value.value.toLowerCase();

      if (field === 'content') {
        result = result.filter(
          item => (item.changeLog && item.changeLog.toLowerCase().includes(value)) || (item.note && item.note.toLowerCase().includes(value)),
        );
      } else if (field === 'createUserName') {
        result = result.filter(item => item.createUserName && item.createUserName.toLowerCase().includes(value));
      } else if (field === 'fieldName') {
        result = result.filter(item => item.details && item.details.some(detail => detail.label && detail.label.toLowerCase().includes(value)));
      }
    }

    return result;
  });

  // 获取变更类型颜色
  function getTypeColor(type: string) {
    switch (type) {
      case '状态变更':
        return 'orange';
      case '跟踪记录更新':
        return 'blue';
      case '负责人变更':
        return 'purple';
      case '金额变更':
        return 'green';
      case '客户变更':
        return 'cyan';
      case '标签变更':
        return 'magenta';
      case '内容变更':
        return 'volcano';
      default:
        return 'default';
    }
  }

  // 获取变更类型图标
  function getTypeIcon(type: string) {
    switch (type) {
      case '状态变更':
        return SwapOutlined;
      case '跟踪记录更新':
        return MessageOutlined;
      case '负责人变更':
        return UserOutlined;
      case '金额变更':
        return DollarOutlined;
      case '客户变更':
        return TeamOutlined;
      case '标签变更':
        return TagsOutlined;
      case '内容变更':
        return FileTextOutlined;
      default:
        return EditOutlined;
    }
  }

  // 切换详情展开状态
  function toggleDetail(id: string) {
    const index = expandedIds.value.indexOf(id);
    if (index === -1) {
      expandedIds.value.push(id);
    } else {
      expandedIds.value.splice(index, 1);
    }
  }

  // 处理筛选变更
  function handleFilterChange(type: string | null) {
    filterType.value = type;
  }

  // 处理搜索变更
  function handleSearchChange(params: { field: string | null; value: string | null }) {
    searchParams.value = params;
  }

  // 加载变更日志列表
  async function loadDatalogList() {
    try {
      loading.value = true;
      const data = await getBusinessDatalogListByBusinessId(props.businessId);
      datalogList.value = data;
    } catch (error) {
      console.error('获取商机标签记录列表失败:', error);
      createMessage.error('获取商机标签记录列表失败');
    } finally {
      loading.value = false;
    }
  }

  onMounted(() => {
    loadDatalogList();
  });
</script>

<style lang="less" scoped>
  .datalog-item {
    margin-bottom: 24px;

    .datalog-header {
      margin-bottom: 12px;
      display: flex;
      align-items: center;
      flex-wrap: wrap;

      .datalog-type-tag {
        margin-right: 16px;
      }

      .datalog-time {
        color: #999;
        margin-right: 16px;
      }

      .datalog-user {
        color: #666;
        flex: 1;
      }

      .detail-toggle {
        padding: 0;
        margin-left: auto;
      }
    }

    .datalog-content {
      background-color: #f9f9f9;
      padding: 16px;
      border-radius: 8px;
      border: 1px solid #f0f0f0;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

      .datalog-summary {
        margin-bottom: 12px;
        font-size: 15px;
      }

      .datalog-note {
        color: #666;
        font-style: italic;
        background-color: #fffbe6;
        padding: 8px;
        border-radius: 4px;
        margin-top: 12px;
      }
    }
  }

  .old-value {
    color: #ff4d4f;
    text-decoration: line-through;
  }

  .new-value {
    color: #52c41a;
    font-weight: 500;
  }

  @media (max-width: 576px) {
    .datalog-header {
      .datalog-user {
        margin-top: 4px;
        width: 100%;
      }

      .detail-toggle {
        margin-top: 4px;
      }
    }
  }
</style>
