package com.xinghuo.project.template.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 标准项目风险库视图对象
 * 用于返回风险库列表和详情信息
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
@Schema(description = "标准项目风险库视图对象")
public class RiskLibraryVO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private String id;

    /**
     * 标准风险编码
     */
    @Schema(description = "标准风险编码")
    private String code;

    /**
     * 标准风险标题/名称
     */
    @Schema(description = "标准风险标题/名称")
    private String title;

    /**
     * 风险的详细描述 (可能的原因、后果等)
     */
    @Schema(description = "风险的详细描述")
    private String description;

    /**
     * 发布状态 (如: draft 草稿, published 已发布, archived 归档)
     */
    @Schema(description = "发布状态")
    private String status;

    /**
     * 发布状态名称 (草稿/已发布/归档)
     */
    @Schema(description = "发布状态名称")
    private String statusName;

    /**
     * 风险类别ID (关联字典表, 如: 技术, 成本, 供应链, 法规)
     */
    @Schema(description = "风险类别ID")
    private String riskCategoryId;

    /**
     * 风险类别名称 (冗余字段，便于显示)
     */
    @Schema(description = "风险类别名称")
    private String riskCategoryName;

    /**
     * 默认概率等级ID (关联字典表, 如: 1-5级)
     */
    @Schema(description = "默认概率等级ID")
    private String defaultProbabilityLevelId;

    /**
     * 默认概率等级名称 (冗余字段，便于显示)
     */
    @Schema(description = "默认概率等级名称")
    private String defaultProbabilityLevelName;

    /**
     * 默认影响等级ID (关联字典表, 如: 1-5级)
     */
    @Schema(description = "默认影响等级ID")
    private String defaultImpactLevelId;

    /**
     * 默认影响等级名称 (冗余字段，便于显示)
     */
    @Schema(description = "默认影响等级名称")
    private String defaultImpactLevelName;

    /**
     * 建议的应对策略 (如: 规避, 转移, 减轻, 接受)
     */
    @Schema(description = "建议的应对策略")
    private String suggestedStrategy;

    /**
     * 建议的具体应对措施
     */
    @Schema(description = "建议的具体应对措施")
    private String suggestedActions;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private String tenantId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    /**
     * 创建用户ID
     */
    @Schema(description = "创建用户ID")
    private String createdBy;

    /**
     * 创建用户名称 (冗余字段，便于显示)
     */
    @Schema(description = "创建用户名称")
    private String createdByName;

    /**
     * 最后修改时间
     */
    @Schema(description = "最后修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdatedAt;

    /**
     * 最后修改用户ID
     */
    @Schema(description = "最后修改用户ID")
    private String lastUpdatedBy;

    /**
     * 最后修改用户名称 (冗余字段，便于显示)
     */
    @Schema(description = "最后修改用户名称")
    private String lastUpdatedByName;
}
