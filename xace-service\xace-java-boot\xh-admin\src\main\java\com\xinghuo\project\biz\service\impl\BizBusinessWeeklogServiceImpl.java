package com.xinghuo.project.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.permission.entity.OrganizeEntity;
import com.xinghuo.permission.service.OrganizeService;
import com.xinghuo.permission.service.UserService;
import com.xinghuo.project.biz.dao.BizBusinessWeeklogMapper;
import com.xinghuo.project.biz.entity.BizBusinessWeeklogEntity;
import com.xinghuo.project.biz.model.bizBusinessWeeklog.BizBusinessWeeklogAuditForm;
import com.xinghuo.project.biz.model.bizBusinessWeeklog.BizBusinessWeeklogHistoryVO;
import com.xinghuo.project.biz.model.bizBusinessWeeklog.BizBusinessWeeklogPagination;
import com.xinghuo.project.biz.service.BizBusinessWeeklogService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 商机周报服务实现类
 * 
 * <AUTHOR>
 * @date 2025-07-08
 */
@Slf4j
@Service
public class BizBusinessWeeklogServiceImpl extends BaseServiceImpl<BizBusinessWeeklogMapper, BizBusinessWeeklogEntity>
        implements BizBusinessWeeklogService {

    @Resource
    private UserService userService;

    @Resource
    private OrganizeService organizeService;

    @Override
    public List<BizBusinessWeeklogEntity> getList(BizBusinessWeeklogPagination pagination) {
        QueryWrapper<BizBusinessWeeklogEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<BizBusinessWeeklogEntity> lambda = queryWrapper.lambda();

        // 根据所属分部查询
        if (StrXhUtil.isNotEmpty(pagination.getFbId())) {
            lambda.eq(BizBusinessWeeklogEntity::getFbId, pagination.getFbId());
        }

        // 根据项目类型查询
        if (StrXhUtil.isNotEmpty(pagination.getProjType())) {
            lambda.eq(BizBusinessWeeklogEntity::getProjType, pagination.getProjType());
        }

        // 根据项目ID查询
        if (StrXhUtil.isNotEmpty(pagination.getProjId())) {
            lambda.eq(BizBusinessWeeklogEntity::getProjId, pagination.getProjId());
        }

        // 根据项目名称模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getProjName())) {
            lambda.like(BizBusinessWeeklogEntity::getProjName, pagination.getProjName());
        }

        // 根据项目级别查询
        if (StrXhUtil.isNotEmpty(pagination.getProjectLevel())) {
            lambda.eq(BizBusinessWeeklogEntity::getProjectLevel, pagination.getProjectLevel());
        }

        // 根据负责人查询
        if (StrXhUtil.isNotEmpty(pagination.getOwnId())) {
            lambda.eq(BizBusinessWeeklogEntity::getOwnId, pagination.getOwnId());
        }

        // 根据状态查询
        if (pagination.getStatus() != null) {
            lambda.eq(BizBusinessWeeklogEntity::getStatus, pagination.getStatus());
        }

        // 根据显示状态查询
        if (pagination.getShowStatus() != null) {
            lambda.eq(BizBusinessWeeklogEntity::getShowStatus, pagination.getShowStatus());
        }

        // 开始日期范围查询
        if (pagination.getStartDateBegin() != null) {
            lambda.ge(BizBusinessWeeklogEntity::getStartDate, pagination.getStartDateBegin());
        }
        if (pagination.getStartDateEnd() != null) {
            lambda.le(BizBusinessWeeklogEntity::getStartDate, pagination.getStartDateEnd());
        }

        // 结束日期范围查询
        if (pagination.getEndDateBegin() != null) {
            lambda.ge(BizBusinessWeeklogEntity::getEndDate, pagination.getEndDateBegin());
        }
        if (pagination.getEndDateEnd() != null) {
            lambda.le(BizBusinessWeeklogEntity::getEndDate, pagination.getEndDateEnd());
        }

        // 录入日期范围查询
        if (pagination.getInputDateBegin() != null) {
            lambda.ge(BizBusinessWeeklogEntity::getInputDate, pagination.getInputDateBegin());
        }
        if (pagination.getInputDateEnd() != null) {
            lambda.le(BizBusinessWeeklogEntity::getInputDate, pagination.getInputDateEnd());
        }

        // 关键字搜索（项目名称或备注）
        String keyword = pagination.getKeyword();
        if (StrXhUtil.isNotEmpty(keyword)) {
            lambda.and(wrapper -> wrapper
                    .like(BizBusinessWeeklogEntity::getProjName, keyword)
                    .or()
                    .like(BizBusinessWeeklogEntity::getNote, keyword)
                    .or()
                    .like(BizBusinessWeeklogEntity::getPlan, keyword)
                    .or()
                    .like(BizBusinessWeeklogEntity::getRisk, keyword)
            );
        }

        // 排序：优先按开始日期倒序，然后按创建时间倒序
        lambda.orderByDesc(BizBusinessWeeklogEntity::getStartDate);
        lambda.orderByDesc(BizBusinessWeeklogEntity::getCreatedAt);

        return processDataType(queryWrapper, pagination);
    }

    @Override
    public BizBusinessWeeklogEntity getInfo(String id) {
        return this.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String create(BizBusinessWeeklogEntity entity) {
        // 生成ID
        String id = RandomUtil.snowId();
        entity.setId(id);

        // 设置默认值
        if (entity.getStatus() == null) {
            entity.setStatus(0); // 默认未填写
        }
        if (entity.getShowStatus() == null) {
            entity.setShowStatus(0); // 默认未显示
        }
        if (entity.getInputDate() == null) {
            entity.setInputDate(new Date()); // 默认当前日期
        }

        // 保存
        this.save(entity);

        return id;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(String id, BizBusinessWeeklogEntity entity) {
        BizBusinessWeeklogEntity oldEntity = this.getById(id);
        if (oldEntity == null) {
            throw new RuntimeException("商机周报不存在");
        }

        // 设置ID
        entity.setId(id);

        // 更新
        this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        BizBusinessWeeklogEntity entity = this.getById(id);
        if (entity == null) {
            throw new RuntimeException("商机周报不存在");
        }

        // 物理删除
        this.removeById(id);
    }

    @Override
    public List<BizBusinessWeeklogEntity> getListByDateRange(Date startDate, Date endDate) {
        LambdaQueryWrapper<BizBusinessWeeklogEntity> queryWrapper = new LambdaQueryWrapper<>();
        
        if (startDate != null) {
            queryWrapper.ge(BizBusinessWeeklogEntity::getStartDate, startDate);
        }
        if (endDate != null) {
            queryWrapper.le(BizBusinessWeeklogEntity::getEndDate, endDate);
        }
        
        queryWrapper.orderByDesc(BizBusinessWeeklogEntity::getStartDate);
        
        return this.list(queryWrapper);
    }

    @Override
    public List<BizBusinessWeeklogEntity> getListByOwnId(String ownId) {
        LambdaQueryWrapper<BizBusinessWeeklogEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BizBusinessWeeklogEntity::getOwnId, ownId);
        queryWrapper.orderByDesc(BizBusinessWeeklogEntity::getStartDate);
        
        return this.list(queryWrapper);
    }

    @Override
    public List<BizBusinessWeeklogHistoryVO> getHistoryByProjId(String projId) {
        LambdaQueryWrapper<BizBusinessWeeklogEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BizBusinessWeeklogEntity::getProjId, projId);
        queryWrapper.orderByDesc(BizBusinessWeeklogEntity::getStartDate);
        queryWrapper.orderByDesc(BizBusinessWeeklogEntity::getCreatedAt);
        
        List<BizBusinessWeeklogEntity> entities = this.list(queryWrapper);
        
        // 转换为历史记录VO
        return entities.stream().map(entity -> {
            BizBusinessWeeklogHistoryVO vo = new BizBusinessWeeklogHistoryVO();
            vo.setId(entity.getId());
            vo.setProjId(entity.getProjId());
            vo.setProjName(entity.getProjName());
            vo.setStartDate(entity.getStartDate());
            vo.setEndDate(entity.getEndDate());
            vo.setInputDate(entity.getInputDate());
            vo.setOwnId(entity.getOwnId());
            vo.setNote(entity.getNote());
            vo.setPlan(entity.getPlan());
            vo.setRisk(entity.getRisk());
            vo.setStatus(entity.getStatus());
            vo.setCreatedAt(entity.getCreatedAt());
            
            // 设置状态名称
            vo.setStatusName(getStatusName(entity.getStatus()));
            
            // 设置负责人名称和创建人名称（需要关联查询用户表）
            // 这里可以根据实际需要进行优化，比如批量查询用户信息
            
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitForAudit(String id) {
        BizBusinessWeeklogEntity entity = this.getById(id);
        if (entity == null) {
            throw new RuntimeException("商机周报不存在");
        }

        if (!Integer.valueOf(1).equals(entity.getStatus())) {
            throw new RuntimeException("只有已填写状态的周报才能提交审核");
        }

        // 更新状态为提交审核
        entity.setStatus(2);
        this.updateById(entity);

        // 发送审核通知给分部负责人
        sendAuditNotification(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void audit(BizBusinessWeeklogAuditForm auditForm) {
        BizBusinessWeeklogEntity entity = this.getById(auditForm.getId());
        if (entity == null) {
            throw new RuntimeException("商机周报不存在");
        }
        
        if (!Integer.valueOf(2).equals(entity.getStatus())) {
            throw new RuntimeException("只有提交审核状态的周报才能进行审核");
        }
        
        // 更新审核状态
        entity.setStatus(auditForm.getStatus());
        
        // 如果有审核意见，可以考虑添加到备注中或单独的审核意见字段
        if (StrXhUtil.isNotEmpty(auditForm.getAuditNote())) {
            String currentNote = entity.getNote();
            String auditNote = "【审核意见】" + auditForm.getAuditNote();
            entity.setNote(StrXhUtil.isNotEmpty(currentNote) ? currentNote + "\n" + auditNote : auditNote);
        }
        
        this.updateById(entity);
    }

    @Override
    public boolean isExistByProjIdAndStartDate(String projId, Date startDate, String excludeId) {
        LambdaQueryWrapper<BizBusinessWeeklogEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BizBusinessWeeklogEntity::getProjId, projId);
        queryWrapper.eq(BizBusinessWeeklogEntity::getStartDate, startDate);
        
        if (StrXhUtil.isNotEmpty(excludeId)) {
            queryWrapper.ne(BizBusinessWeeklogEntity::getId, excludeId);
        }
        
        return this.count(queryWrapper) > 0;
    }

    @Override
    public List<BizBusinessWeeklogEntity> getListByStatus(Integer status) {
        LambdaQueryWrapper<BizBusinessWeeklogEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BizBusinessWeeklogEntity::getStatus, status);
        queryWrapper.orderByDesc(BizBusinessWeeklogEntity::getStartDate);
        
        return this.list(queryWrapper);
    }

    @Override
    public List<BizBusinessWeeklogEntity> getPendingAuditList() {
        return getListByStatus(2); // 状态为提交审核(2)
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateShowStatus(List<String> ids, Integer showStatus) {
        if (ids == null || ids.isEmpty()) {
            return;
        }
        
        LambdaQueryWrapper<BizBusinessWeeklogEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(BizBusinessWeeklogEntity::getId, ids);
        
        BizBusinessWeeklogEntity updateEntity = new BizBusinessWeeklogEntity();
        updateEntity.setShowStatus(showStatus);
        
        this.update(updateEntity, queryWrapper);
    }

    /**
     * 获取状态名称
     */
    private String getStatusName(Integer status) {
        if (status == null) {
            return "未知";
        }

        switch (status) {
            case 0:
                return "未填写";
            case 1:
                return "已填写";
            case 2:
                return "提交审核";
            case 3:
                return "已发布";
            case -1:
                return "已驳回";
            default:
                return "未知";
        }
    }

    /**
     * 发送审核通知给分部负责人
     */
    private void sendAuditNotification(BizBusinessWeeklogEntity entity) {
        try {
            // 获取分部负责人
            String departmentManagerId = getDepartmentManager(entity.getFbId());
            if (StrXhUtil.isNotEmpty(departmentManagerId)) {
                // 这里可以发送消息通知、邮件或其他形式的通知
                log.info("发送审核通知给分部负责人: {}, 商机周报ID: {}, 项目名称: {}",
                        departmentManagerId, entity.getId(), entity.getProjName());

                // TODO: 实现具体的通知逻辑
                // 例如：调用消息服务发送站内信
                // messageService.sendMessage(departmentManagerId, "商机周报审核通知", buildNotificationContent(entity));
            } else {
                log.warn("未找到分部负责人，分部ID: {}", entity.getFbId());
            }
        } catch (Exception e) {
            log.error("发送审核通知失败", e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 获取分部负责人ID
     */
    private String getDepartmentManager(String fbId) {
        if (StrXhUtil.isEmpty(fbId)) {
            return null;
        }

        try {
            // 根据分部ID获取组织信息
            OrganizeEntity organize = organizeService.getInfo(fbId);
            if (organize != null && StrXhUtil.isNotEmpty(organize.getManager())) {
                return organize.getManager();
            }

            // 如果当前组织没有负责人，向上查找父级组织的负责人
            if (organize != null && StrXhUtil.isNotEmpty(organize.getParentId())) {
                return getDepartmentManager(organize.getParentId());
            }
        } catch (Exception e) {
            log.error("获取分部负责人失败，分部ID: {}", fbId, e);
        }

        return null;
    }

    /**
     * 构建通知内容
     */
    private String buildNotificationContent(BizBusinessWeeklogEntity entity) {
        StringBuilder content = new StringBuilder();
        content.append("您有一份商机周报需要审核：\n");
        content.append("项目名称：").append(entity.getProjName()).append("\n");
        content.append("开始日期：").append(entity.getStartDate()).append("\n");
        content.append("结束日期：").append(entity.getEndDate()).append("\n");
        content.append("负责人：").append(entity.getOwnId()).append("\n");
        content.append("请及时进行审核处理。");
        return content.toString();
    }
}
