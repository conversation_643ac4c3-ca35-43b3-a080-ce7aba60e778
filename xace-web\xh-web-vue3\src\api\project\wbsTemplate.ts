import { defHttp } from '/@/utils/http/axios';

enum Api {
  GetList = '/api/project/template/wbsTemplate/getList',
  GetListByStatus = '/api/project/template/wbsTemplate/getListByStatus',
  GetDetailInfo = '/api/project/template/wbsTemplate/getDetailInfo',
  GetInfo = '/api/project/template/wbsTemplate/getInfo',
  Create = '/api/project/template/wbsTemplate/create',
  Update = '/api/project/template/wbsTemplate/update',
  Delete = '/api/project/template/wbsTemplate/delete',
  BatchDelete = '/api/project/template/wbsTemplate/batchDelete',
  UpdateStatus = '/api/project/template/wbsTemplate/updateStatus',
  BatchUpdateStatus = '/api/project/template/wbsTemplate/batchUpdateStatus',
  Publish = '/api/project/template/wbsTemplate/publish',
  Archive = '/api/project/template/wbsTemplate/archive',
  BatchPublish = '/api/project/template/wbsTemplate/batchPublish',
  BatchArchive = '/api/project/template/wbsTemplate/batchArchive',
  Copy = '/api/project/template/wbsTemplate/copy',
  CheckNameExists = '/api/project/template/wbsTemplate/checkNameExists',
  CheckCodeExists = '/api/project/template/wbsTemplate/checkCodeExists',
  GetSelectList = '/api/project/template/wbsTemplate/getSelectList',
  GenerateCode = '/api/project/template/wbsTemplate/generateCode',
  GetTemplateUsageInfo = '/api/project/template/wbsTemplate/getTemplateUsageInfo',
  AddActivitiesFromLibrary = '/api/project/template/wbsTemplate/addActivitiesFromLibrary',
  GetWbsDetails = '/api/project/template/wbsTemplate/getWbsDetails',
  SaveWbsDetails = '/api/project/template/wbsTemplate/saveWbsDetails',
  DeleteWbsDetails = '/api/project/template/wbsTemplate/deleteWbsDetails',
  ApplyToProjects = '/api/project/template/wbsTemplate/applyToProjects',
}

/**
 * WBS计划模板接口
 */

// 获取WBS模板列表
export function getWbsTemplateList(params: any) {
  return defHttp.post({
    url: Api.GetList,
    data: params,
  });
}

// 根据状态获取模板列表
export function getWbsTemplateListByStatus(status: string) {
  return defHttp.get({
    url: `${Api.GetListByStatus}/${status}`,
  });
}

// 获取模板详情（包含WBS明细）
export function getWbsTemplateDetailInfo(id: string) {
  return defHttp.get({
    url: `${Api.GetDetailInfo}/${id}`,
  });
}

// 获取模板基本信息
export function getWbsTemplateInfo(id: string) {
  return defHttp.get({
    url: `${Api.GetInfo}/${id}`,
  });
}

// 创建WBS模板
export function createWbsTemplate(params: any) {
  return defHttp.post({
    url: Api.Create,
    data: params,
  });
}

// 更新WBS模板
export function updateWbsTemplate(id: string, params: any) {
  return defHttp.put({
    url: `${Api.Update}/${id}`,
    data: params,
  });
}

// 删除WBS模板
export function deleteWbsTemplate(id: string) {
  return defHttp.delete({
    url: `${Api.Delete}/${id}`,
  });
}

// 批量删除WBS模板
export function batchDeleteWbsTemplate(ids: string[]) {
  return defHttp.delete({
    url: Api.BatchDelete,
    data: ids,
  });
}

// 更新模板状态
export function updateWbsTemplateStatus(id: string, status: string) {
  return defHttp.put({
    url: `${Api.UpdateStatus}/${id}?status=${status}`,
  });
}

// 批量更新状态
export function batchUpdateWbsTemplateStatus(ids: string[], status: string) {
  return defHttp.put({
    url: `${Api.BatchUpdateStatus}?status=${status}`,
    data: ids,
  });
}

// 发布模板
export function publishWbsTemplate(id: string) {
  return defHttp.put({
    url: `${Api.Publish}/${id}`,
  });
}

// 归档模板
export function archiveWbsTemplate(id: string) {
  return defHttp.put({
    url: `${Api.Archive}/${id}`,
  });
}

// 批量发布模板
export function batchPublishWbsTemplate(ids: string[]) {
  return defHttp.put({
    url: Api.BatchPublish,
    data: ids,
  });
}

// 批量归档模板
export function batchArchiveWbsTemplate(ids: string[]) {
  return defHttp.put({
    url: Api.BatchArchive,
    data: ids,
  });
}

// 复制WBS模板
export function copyWbsTemplate(id: string, newName: string) {
  return defHttp.post({
    url: `${Api.Copy}/${id}?newName=${encodeURIComponent(newName)}`,
  });
}

// 检查模板名称是否存在
export function checkWbsTemplateNameExists(name: string, excludeId?: string) {
  return defHttp.get({
    url: Api.CheckNameExists,
    params: { name, excludeId },
  });
}

// 检查模板编码是否存在
export function checkWbsTemplateCodeExists(code: string, excludeId?: string) {
  return defHttp.get({
    url: Api.CheckCodeExists,
    params: { code, excludeId },
  });
}

// 获取模板选择列表
export function getWbsTemplateSelectList(keyword?: string) {
  return defHttp.get({
    url: Api.GetSelectList,
    params: { keyword },
  });
}

// 生成模板编码
export function generateWbsTemplateCode() {
  return defHttp.get({
    url: Api.GenerateCode,
  });
}

// 获取模板使用情况
export function getWbsTemplateUsageInfo(id: string) {
  return defHttp.get({
    url: `${Api.GetTemplateUsageInfo}/${id}`,
  });
}

// 从活动库添加活动
export function addActivitiesFromLibrary(templateId: string, activityIds: string[], parentId?: string) {
  return defHttp.post({
    url: `${Api.AddActivitiesFromLibrary}/${templateId}?parentId=${parentId || ''}`,
    data: activityIds,
  });
}

// 获取WBS模板明细列表
export function getWbsTemplateDetails(templateId: string) {
  return defHttp.get({
    url: `${Api.GetWbsDetails}/${templateId}`,
  });
}

// 保存WBS明细
export function saveWbsTemplateDetails(templateId: string, details: any[]) {
  return defHttp.post({
    url: `${Api.SaveWbsDetails}/${templateId}`,
    data: details,
  });
}

// 删除WBS明细
export function deleteWbsTemplateDetails(detailIds: string[]) {
  return defHttp.delete({
    url: Api.DeleteWbsDetails,
    data: detailIds,
  });
}

// 应用到项目
export function applyWbsTemplateToProjects(templateId: string, projectIds: string[]) {
  return defHttp.post({
    url: `${Api.ApplyToProjects}/${templateId}`,
    data: projectIds,
  });
}
