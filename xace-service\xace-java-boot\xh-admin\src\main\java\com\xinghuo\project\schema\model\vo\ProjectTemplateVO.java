package com.xinghuo.project.schema.model.vo;

import com.xinghuo.project.schema.entity.ProjectTemplateEntity;
import com.xinghuo.project.schema.entity.ProjectSchemaWbsEntity;
import com.xinghuo.project.schema.entity.ProjectSchemaPhaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 项目模板VO类
 * 包含主表信息和关联的WBS、阶段配置
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectTemplateVO extends ProjectTemplateEntity {

    /**
     * WBS计划列表
     */
    private List<ProjectSchemaWbsEntity> wbsList;

    /**
     * 阶段配置列表
     */
    private List<ProjectSchemaPhaseEntity> phaseList;

    /**
     * 状态名称（冗余字段，便于显示）
     */
    private String statusName;

    /**
     * 模板类型名称（冗余字段，便于显示）
     */
    private String typeName;

    /**
     * 创建人姓名（冗余字段，便于显示）
     */
    private String creatorUserName;

    /**
     * WBS活动总数
     */
    private Integer wbsCount;

    /**
     * 阶段总数
     */
    private Integer phaseCount;

    /**
     * 里程碑数量
     */
    private Integer milestoneCount;

    /**
     * 总工期（所有阶段工期之和）
     */
    private Integer totalDuration;

    /**
     * 最大WBS层级深度
     */
    private Integer maxWbsLevel;

    /**
     * 是否包含审批流程
     */
    private Boolean hasApproval;

    /**
     * 是否包含检查清单
     */
    private Boolean hasChecklist;

    /**
     * 是否包含交付物模板
     */
    private Boolean hasWorkProduct;
}
