package com.xinghuo.project.template.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 标准项目风险库选择列表视图对象
 * 用于下拉选择框等场景，只包含必要的id和fullName字段
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
@Schema(description = "标准项目风险库选择列表视图对象")
public class RiskLibrarySelectVO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private String id;

    /**
     * 完整名称 (格式: [编码] 标题)
     * 用于option显示，符合fullName和id的规范要求
     */
    @Schema(description = "完整名称")
    private String fullName;

    /**
     * 风险编码 (用于构建fullName)
     */
    @Schema(description = "风险编码")
    private String code;

    /**
     * 风险标题 (用于构建fullName)
     */
    @Schema(description = "风险标题")
    private String title;

    /**
     * 发布状态 (如: draft 草稿, published 已发布, archived 归档)
     * 用于过滤，通常只显示已发布状态的选项
     */
    @Schema(description = "发布状态")
    private String status;

    /**
     * 风险类别ID
     */
    @Schema(description = "风险类别ID")
    private String riskCategoryId;

    /**
     * 默认概率等级ID
     */
    @Schema(description = "默认概率等级ID")
    private String defaultProbabilityLevelId;

    /**
     * 默认影响等级ID
     */
    @Schema(description = "默认影响等级ID")
    private String defaultImpactLevelId;
}
