<template>
  <div></div>
</template>
<script lang="ts" setup>
  import { unref, nextTick } from 'vue';
  import { useRouter } from 'vue-router';
  import { PageEnum } from '/@/enums/pageEnum';

  const { currentRoute, replace } = useRouter();

  // 安全地获取路由参数
  const route = unref(currentRoute);
  if (!route) {
    console.error('Current route is null, redirecting to home');
    replace(PageEnum.BASE_HOME);
  } else {
    const { params, query } = route;
    const { path, _redirect_type = 'path' } = params || {};

    // 安全地删除重定向参数
    if (params) {
      Reflect.deleteProperty(params, '_redirect_type');
      Reflect.deleteProperty(params, 'path');
    }

    // 验证路径是否有效
    if (!path) {
      console.warn('Redirect path is empty, redirecting to home');
      replace(PageEnum.BASE_HOME);
    } else {
      const _path = Array.isArray(path) ? path.join('/') : path;

      // 使用nextTick确保DOM更新完成后再进行路由跳转
      nextTick(() => {
        try {
          if (_redirect_type === 'name') {
            const originParams = params?._origin_params;
            const parsedParams = originParams ? JSON.parse(originParams as string) : {};

            replace({
              name: _path,
              query: query || {},
              params: parsedParams,
            }).catch(error => {
              console.error('Route replace by name failed:', error);
              // 降级到路径跳转
              replace({
                path: _path.startsWith('/') ? _path : '/' + _path,
                query: query || {},
              }).catch(() => {
                // 最终降级到首页
                replace(PageEnum.BASE_HOME);
              });
            });
          } else {
            replace({
              path: _path.startsWith('/') ? _path : '/' + _path,
              query: query || {},
            }).catch(error => {
              console.error('Route replace by path failed:', error);
              // 降级到首页
              replace(PageEnum.BASE_HOME);
            });
          }
        } catch (error) {
          console.error('Redirect error:', error);
          replace(PageEnum.BASE_HOME);
        }
      });
    }
  }
</script>
