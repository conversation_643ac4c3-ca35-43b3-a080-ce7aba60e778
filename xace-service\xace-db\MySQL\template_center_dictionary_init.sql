-- 模板中心字典数据初始化脚本
-- 用于补充模板中心相关的字典数据

-- 1. 风险类别字典类型
INSERT INTO `base_dictionarytype` (`F_Id`, `F_ParentId`, `F_FullName`, `F_EnCode`, `F_IsTree`, `F_Description`, `F_SortCode`, `F_EnabledMark`, `F_CreatorTime`, `F_CreatorUserId`, `F_LastModifyTime`, `F_LastModifyUserId`, `F_DeleteMark`, `F_DeleteTime`, `F_DeleteUserId`) 
VALUES ('712288260759489001', '0', '风险类别', 'riskCategory', '0', '项目风险类别字典', '1', '1', NOW(), 'admin', NULL, NULL, NULL, NULL, NULL);

-- 风险类别字典数据
INSERT INTO `base_dictionarydata` (`F_Id`, `F_ParentId`, `F_FullName`, `F_EnCode`, `F_SimpleSpelling`, `F_IsDefault`, `F_Description`, `F_SortCode`, `F_EnabledMark`, `F_CreatorTime`, `F_CreatorUserId`, `F_LastModifyTime`, `F_LastModifyUserId`, `F_DeleteMark`, `F_DeleteTime`, `F_DeleteUserId`, `F_DictionaryTypeId`, `F_CssClass`, `F_ListClass`) 
VALUES 
('712288260759489002', '0', '技术风险', 'technical', 'JSFX', NULL, '技术相关风险', '1', '1', NOW(), 'admin', NULL, NULL, NULL, NULL, NULL, '712288260759489001', NULL, NULL),
('712288260759489003', '0', '管理风险', 'management', 'GLFX', NULL, '项目管理相关风险', '2', '1', NOW(), 'admin', NULL, NULL, NULL, NULL, NULL, '712288260759489001', NULL, NULL),
('712288260759489004', '0', '商业风险', 'business', 'SYFX', NULL, '商业和市场相关风险', '3', '1', NOW(), 'admin', NULL, NULL, NULL, NULL, NULL, '712288260759489001', NULL, NULL),
('712288260759489005', '0', '外部风险', 'external', 'WBFX', NULL, '外部环境相关风险', '4', '1', NOW(), 'admin', NULL, NULL, NULL, NULL, NULL, '712288260759489001', NULL, NULL),
('712288260759489006', '0', '组织风险', 'organizational', 'ZZFX', NULL, '组织结构相关风险', '5', '1', NOW(), 'admin', NULL, NULL, NULL, NULL, NULL, '712288260759489001', NULL, NULL);

-- 2. 概率等级字典类型
INSERT INTO `base_dictionarytype` (`F_Id`, `F_ParentId`, `F_FullName`, `F_EnCode`, `F_IsTree`, `F_Description`, `F_SortCode`, `F_EnabledMark`, `F_CreatorTime`, `F_CreatorUserId`, `F_LastModifyTime`, `F_LastModifyUserId`, `F_DeleteMark`, `F_DeleteTime`, `F_DeleteUserId`) 
VALUES ('712288260759489007', '0', '概率等级', 'probabilityLevel', '0', '风险发生概率等级', '2', '1', NOW(), 'admin', NULL, NULL, NULL, NULL, NULL);

-- 概率等级字典数据
INSERT INTO `base_dictionarydata` (`F_Id`, `F_ParentId`, `F_FullName`, `F_EnCode`, `F_SimpleSpelling`, `F_IsDefault`, `F_Description`, `F_SortCode`, `F_EnabledMark`, `F_CreatorTime`, `F_CreatorUserId`, `F_LastModifyTime`, `F_LastModifyUserId`, `F_DeleteMark`, `F_DeleteTime`, `F_DeleteUserId`, `F_DictionaryTypeId`, `F_CssClass`, `F_ListClass`) 
VALUES 
('712288260759489008', '0', '很低', 'veryLow', 'HD', NULL, '发生概率很低(0-10%)', '1', '1', NOW(), 'admin', NULL, NULL, NULL, NULL, NULL, '712288260759489007', NULL, 'success'),
('712288260759489009', '0', '低', 'low', 'D', NULL, '发生概率低(10-30%)', '2', '1', NOW(), 'admin', NULL, NULL, NULL, NULL, NULL, '712288260759489007', NULL, 'info'),
('712288260759489010', '0', '中', 'medium', 'Z', NULL, '发生概率中等(30-60%)', '3', '1', NOW(), 'admin', NULL, NULL, NULL, NULL, NULL, '712288260759489007', NULL, 'warning'),
('712288260759489011', '0', '高', 'high', 'G', NULL, '发生概率高(60-80%)', '4', '1', NOW(), 'admin', NULL, NULL, NULL, NULL, NULL, '712288260759489007', NULL, 'danger'),
('712288260759489012', '0', '很高', 'veryHigh', 'HG', NULL, '发生概率很高(80-100%)', '5', '1', NOW(), 'admin', NULL, NULL, NULL, NULL, NULL, '712288260759489007', NULL, 'danger');

-- 3. 影响等级字典类型
INSERT INTO `base_dictionarytype` (`F_Id`, `F_ParentId`, `F_FullName`, `F_EnCode`, `F_IsTree`, `F_Description`, `F_SortCode`, `F_EnabledMark`, `F_CreatorTime`, `F_CreatorUserId`, `F_LastModifyTime`, `F_LastModifyUserId`, `F_DeleteMark`, `F_DeleteTime`, `F_DeleteUserId`) 
VALUES ('712288260759489013', '0', '影响等级', 'impactLevel', '0', '风险影响程度等级', '3', '1', NOW(), 'admin', NULL, NULL, NULL, NULL, NULL);

-- 影响等级字典数据
INSERT INTO `base_dictionarydata` (`F_Id`, `F_ParentId`, `F_FullName`, `F_EnCode`, `F_SimpleSpelling`, `F_IsDefault`, `F_Description`, `F_SortCode`, `F_EnabledMark`, `F_CreatorTime`, `F_CreatorUserId`, `F_LastModifyTime`, `F_LastModifyUserId`, `F_DeleteMark`, `F_DeleteTime`, `F_DeleteUserId`, `F_DictionaryTypeId`, `F_CssClass`, `F_ListClass`) 
VALUES 
('712288260759489014', '0', '很低', 'veryLow', 'HD', NULL, '影响程度很低', '1', '1', NOW(), 'admin', NULL, NULL, NULL, NULL, NULL, '712288260759489013', NULL, 'success'),
('712288260759489015', '0', '低', 'low', 'D', NULL, '影响程度低', '2', '1', NOW(), 'admin', NULL, NULL, NULL, NULL, NULL, '712288260759489013', NULL, 'info'),
('712288260759489016', '0', '中', 'medium', 'Z', NULL, '影响程度中等', '3', '1', NOW(), 'admin', NULL, NULL, NULL, NULL, NULL, '712288260759489013', NULL, 'warning'),
('712288260759489017', '0', '高', 'high', 'G', NULL, '影响程度高', '4', '1', NOW(), 'admin', NULL, NULL, NULL, NULL, NULL, '712288260759489013', NULL, 'danger'),
('712288260759489018', '0', '很高', 'veryHigh', 'HG', NULL, '影响程度很高', '5', '1', NOW(), 'admin', NULL, NULL, NULL, NULL, NULL, '712288260759489013', NULL, 'danger');

-- 4. 知识状态字典类型
INSERT INTO `base_dictionarytype` (`F_Id`, `F_ParentId`, `F_FullName`, `F_EnCode`, `F_IsTree`, `F_Description`, `F_SortCode`, `F_EnabledMark`, `F_CreatorTime`, `F_CreatorUserId`, `F_LastModifyTime`, `F_LastModifyUserId`, `F_DeleteMark`, `F_DeleteTime`, `F_DeleteUserId`) 
VALUES ('712288260759489019', '0', '知识状态', 'knowledgeStatus', '0', '模板知识状态', '4', '1', NOW(), 'admin', NULL, NULL, NULL, NULL, NULL);

-- 知识状态字典数据
INSERT INTO `base_dictionarydata` (`F_Id`, `F_ParentId`, `F_FullName`, `F_EnCode`, `F_SimpleSpelling`, `F_IsDefault`, `F_Description`, `F_SortCode`, `F_EnabledMark`, `F_CreatorTime`, `F_CreatorUserId`, `F_LastModifyTime`, `F_LastModifyUserId`, `F_DeleteMark`, `F_DeleteTime`, `F_DeleteUserId`, `F_DictionaryTypeId`, `F_CssClass`, `F_ListClass`) 
VALUES 
('712288260759489020', '0', '草稿', 'draft', 'CG', NULL, '草稿状态', '1', '1', NOW(), 'admin', NULL, NULL, NULL, NULL, NULL, '712288260759489019', NULL, 'info'),
('712288260759489021', '0', '已发布', 'published', 'YFB', NULL, '已发布状态', '2', '1', NOW(), 'admin', NULL, NULL, NULL, NULL, NULL, '712288260759489019', NULL, 'success'),
('712288260759489022', '0', '已归档', 'archived', 'YGD', NULL, '已归档状态', '3', '1', NOW(), 'admin', NULL, NULL, NULL, NULL, NULL, '712288260759489019', NULL, 'warning');

-- 5. 项目类型字典类型
INSERT INTO `base_dictionarytype` (`F_Id`, `F_ParentId`, `F_FullName`, `F_EnCode`, `F_IsTree`, `F_Description`, `F_SortCode`, `F_EnabledMark`, `F_CreatorTime`, `F_CreatorUserId`, `F_LastModifyTime`, `F_LastModifyUserId`, `F_DeleteMark`, `F_DeleteTime`, `F_DeleteUserId`) 
VALUES ('712288260759489023', '0', '项目类型', 'projectType', '0', '项目类型分类', '5', '1', NOW(), 'admin', NULL, NULL, NULL, NULL, NULL);

-- 项目类型字典数据
INSERT INTO `base_dictionarydata` (`F_Id`, `F_ParentId`, `F_FullName`, `F_EnCode`, `F_SimpleSpelling`, `F_IsDefault`, `F_Description`, `F_SortCode`, `F_EnabledMark`, `F_CreatorTime`, `F_CreatorUserId`, `F_LastModifyTime`, `F_LastModifyUserId`, `F_DeleteMark`, `F_DeleteTime`, `F_DeleteUserId`, `F_DictionaryTypeId`, `F_CssClass`, `F_ListClass`) 
VALUES 
('712288260759489024', '0', '软件开发', 'softwareDev', 'RJKF', NULL, '软件开发项目', '1', '1', NOW(), 'admin', NULL, NULL, NULL, NULL, NULL, '712288260759489023', NULL, NULL),
('712288260759489025', '0', '系统集成', 'systemIntegration', 'XTJC', NULL, '系统集成项目', '2', '1', NOW(), 'admin', NULL, NULL, NULL, NULL, NULL, '712288260759489023', NULL, NULL),
('712288260759489026', '0', '咨询服务', 'consulting', 'ZXFW', NULL, '咨询服务项目', '3', '1', NOW(), 'admin', NULL, NULL, NULL, NULL, NULL, '712288260759489023', NULL, NULL),
('712288260759489027', '0', '运维服务', 'maintenance', 'YWFW', NULL, '运维服务项目', '4', '1', NOW(), 'admin', NULL, NULL, NULL, NULL, NULL, '712288260759489023', NULL, NULL);

-- 6. 项目状态字典类型
INSERT INTO `base_dictionarytype` (`F_Id`, `F_ParentId`, `F_FullName`, `F_EnCode`, `F_IsTree`, `F_Description`, `F_SortCode`, `F_EnabledMark`, `F_CreatorTime`, `F_CreatorUserId`, `F_LastModifyTime`, `F_LastModifyUserId`, `F_DeleteMark`, `F_DeleteTime`, `F_DeleteUserId`) 
VALUES ('712288260759489028', '0', '项目状态', 'projectStatus', '0', '项目执行状态', '6', '1', NOW(), 'admin', NULL, NULL, NULL, NULL, NULL);

-- 项目状态字典数据
INSERT INTO `base_dictionarydata` (`F_Id`, `F_ParentId`, `F_FullName`, `F_EnCode`, `F_SimpleSpelling`, `F_IsDefault`, `F_Description`, `F_SortCode`, `F_EnabledMark`, `F_CreatorTime`, `F_CreatorUserId`, `F_LastModifyTime`, `F_LastModifyUserId`, `F_DeleteMark`, `F_DeleteTime`, `F_DeleteUserId`, `F_DictionaryTypeId`, `F_CssClass`, `F_ListClass`) 
VALUES 
('712288260759489029', '0', '未开始', 'notStarted', 'WKS', NULL, '项目未开始', '1', '1', NOW(), 'admin', NULL, NULL, NULL, NULL, NULL, '712288260759489028', NULL, 'info'),
('712288260759489030', '0', '进行中', 'inProgress', 'JXZ', NULL, '项目进行中', '2', '1', NOW(), 'admin', NULL, NULL, NULL, NULL, NULL, '712288260759489028', NULL, 'primary'),
('712288260759489031', '0', '已暂停', 'paused', 'YZT', NULL, '项目已暂停', '3', '1', NOW(), 'admin', NULL, NULL, NULL, NULL, NULL, '712288260759489028', NULL, 'warning'),
('712288260759489032', '0', '已完成', 'completed', 'YWC', NULL, '项目已完成', '4', '1', NOW(), 'admin', NULL, NULL, NULL, NULL, NULL, '712288260759489028', NULL, 'success'),
('712288260759489033', '0', '已取消', 'cancelled', 'YQX', NULL, '项目已取消', '5', '1', NOW(), 'admin', NULL, NULL, NULL, NULL, NULL, '712288260759489028', NULL, 'danger');

-- 7. 项目健康度字典类型
INSERT INTO `base_dictionarytype` (`F_Id`, `F_ParentId`, `F_FullName`, `F_EnCode`, `F_IsTree`, `F_Description`, `F_SortCode`, `F_EnabledMark`, `F_CreatorTime`, `F_CreatorUserId`, `F_LastModifyTime`, `F_LastModifyUserId`, `F_DeleteMark`, `F_DeleteTime`, `F_DeleteUserId`) 
VALUES ('712288260759489034', '0', '项目健康度', 'projectHealth', '0', '项目健康状况评级', '7', '1', NOW(), 'admin', NULL, NULL, NULL, NULL, NULL);

-- 项目健康度字典数据
INSERT INTO `base_dictionarydata` (`F_Id`, `F_ParentId`, `F_FullName`, `F_EnCode`, `F_SimpleSpelling`, `F_IsDefault`, `F_Description`, `F_SortCode`, `F_EnabledMark`, `F_CreatorTime`, `F_CreatorUserId`, `F_LastModifyTime`, `F_LastModifyUserId`, `F_DeleteMark`, `F_DeleteTime`, `F_DeleteUserId`, `F_DictionaryTypeId`, `F_CssClass`, `F_ListClass`) 
VALUES 
('712288260759489035', '0', '健康', 'healthy', 'JK', NULL, '项目健康', '1', '1', NOW(), 'admin', NULL, NULL, NULL, NULL, NULL, '712288260759489034', NULL, 'success'),
('712288260759489036', '0', '注意', 'attention', 'ZY', NULL, '需要注意', '2', '1', NOW(), 'admin', NULL, NULL, NULL, NULL, NULL, '712288260759489034', NULL, 'warning'),
('712288260759489037', '0', '风险', 'risk', 'FX', NULL, '存在风险', '3', '1', NOW(), 'admin', NULL, NULL, NULL, NULL, NULL, '712288260759489034', NULL, 'danger');

-- 8. 交付物类型字典类型
INSERT INTO `base_dictionarytype` (`F_Id`, `F_ParentId`, `F_FullName`, `F_EnCode`, `F_IsTree`, `F_Description`, `F_SortCode`, `F_EnabledMark`, `F_CreatorTime`, `F_CreatorUserId`, `F_LastModifyTime`, `F_LastModifyUserId`, `F_DeleteMark`, `F_DeleteTime`, `F_DeleteUserId`) 
VALUES ('712288260759489038', '0', '交付物类型', 'deliverableType', '0', '项目交付物类型分类', '8', '1', NOW(), 'admin', NULL, NULL, NULL, NULL, NULL);

-- 交付物类型字典数据
INSERT INTO `base_dictionarydata` (`F_Id`, `F_ParentId`, `F_FullName`, `F_EnCode`, `F_SimpleSpelling`, `F_IsDefault`, `F_Description`, `F_SortCode`, `F_EnabledMark`, `F_CreatorTime`, `F_CreatorUserId`, `F_LastModifyTime`, `F_LastModifyUserId`, `F_DeleteMark`, `F_DeleteTime`, `F_DeleteUserId`, `F_DictionaryTypeId`, `F_CssClass`, `F_ListClass`) 
VALUES 
('712288260759489039', '0', '文档', 'document', 'WD', NULL, '文档类交付物', '1', '1', NOW(), 'admin', NULL, NULL, NULL, NULL, NULL, '712288260759489038', NULL, NULL),
('712288260759489040', '0', '软件', 'software', 'RJ', NULL, '软件类交付物', '2', '1', NOW(), 'admin', NULL, NULL, NULL, NULL, NULL, '712288260759489038', NULL, NULL),
('712288260759489041', '0', '硬件', 'hardware', 'YJ', NULL, '硬件类交付物', '3', '1', NOW(), 'admin', NULL, NULL, NULL, NULL, NULL, '712288260759489038', NULL, NULL),
('712288260759489042', '0', '服务', 'service', 'FW', NULL, '服务类交付物', '4', '1', NOW(), 'admin', NULL, NULL, NULL, NULL, NULL, '712288260759489038', NULL, NULL);
