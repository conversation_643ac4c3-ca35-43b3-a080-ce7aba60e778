# XACE 自定义组件文档

## 概述

本文档详细介绍了 XACE 系统中 `src/components` 目录下的自定义组件，这些组件是基于 Ant Design Vue 进行二次封装和扩展的企业级组件库。

## 组件分类

### 1. Xh 系列组件 (核心业务组件)

Xh 系列组件是 XACE 系统的核心组件，提供了完整的业务功能封装。

#### 1.1 基础表单组件

| 组件名称 | 功能说明 | 与 Ant Design Vue 的区别 |
|---------|----------|-------------------------|
| **XhInput** | 增强型输入框 | 支持前后缀图标、自定义样式、业务验证 |
| **XhTextarea** | 多行文本输入 | 集成字数统计、自动调整高度 |
| **XhInputNumber** | 数字输入框 | 支持数值格式化、精度控制 |
| **XhSelect** | 下拉选择器 | 支持远程数据源、动态选项加载 |
| **XhTreeSelect** | 树形选择器 | 集成组织架构、部门选择等业务逻辑 |
| **XhCascader** | 级联选择器 | 支持地区选择、分类选择等场景 |
| **XhCheckbox** | 复选框 | 支持全选/反选、动态选项 |
| **XhRadio** | 单选框 | 支持数据字典、远程选项 |
| **XhSwitch** | 开关组件 | 集成业务状态切换逻辑 |
| **XhSlider** | 滑块输入 | 支持数值范围、步长控制 |
| **XhRate** | 评分组件 | 支持自定义图标、半星评分 |

#### 1.2 日期时间组件

| 组件名称 | 功能说明 | 特色功能 |
|---------|----------|----------|
| **XhDatePicker** | 日期选择器 | 支持业务日期格式、禁用日期规则 |
| **XhDateRange** | 日期范围选择 | 支持快捷选择、预设范围 |
| **XhTimePicker** | 时间选择器 | 支持时间格式化、时间段限制 |
| **XhTimeRange** | 时间范围选择 | 支持工作时间、业务时间段 |
| **XhMonthPicker** | 月份选择器 | 基于 Ant Design Vue 扩展 |
| **XhWeekPicker** | 周选择器 | 基于 Ant Design Vue 扩展 |

#### 1.3 文件上传组件

| 组件名称 | 功能说明 | 特色功能 |
|---------|----------|----------|
| **XhUploadFile** | 文件上传 | 支持多文件、断点续传、文件类型限制 |
| **XhUploadImg** | 图片上传 | 支持图片压缩、裁剪、预览 |
| **XhUploadImgSingle** | 单图片上传 | 支持头像上传、图片编辑 |

#### 1.4 组织架构组件

| 组件名称 | 功能说明 | 业务特色 |
|---------|----------|----------|
| **XhOrganizeSelect** | 组织架构选择 | 支持多级组织、权限控制 |
| **XhDepSelect** | 部门选择器 | 集成部门树、权限过滤 |
| **XhPosSelect** | 岗位选择器 | 支持岗位层级、职责管理 |
| **XhGroupSelect** | 分组选择器 | 支持用户分组、角色分组 |
| **XhRoleSelect** | 角色选择器 | 集成角色权限、动态角色 |
| **XhUserSelect** | 用户选择器 | 支持用户搜索、批量选择 |
| **XhUsersSelect** | 多用户选择 | 支持用户列表、权限验证 |
| **XhUserSelectDropdown** | 用户下拉选择 | 支持快速用户选择 |

#### 1.5 特殊业务组件

| 组件名称 | 功能说明 | 业务场景 |
|---------|----------|----------|
| **XhPopupSelect** | 弹出选择器 | 支持复杂数据选择、多条件筛选 |
| **XhPopupAttr** | 弹出属性配置 | 支持动态属性配置、表单生成 |
| **XhAreaSelect** | 地区选择器 | 支持省市区联动、地址选择 |
| **XhApiSelect** | API 数据选择 | 支持远程数据源、动态加载 |
| **XhRelationForm** | 关联表单 | 支持表单关联、数据联动 |
| **XhRelationFormAttr** | 关联表单属性 | 支持属性配置、动态表单 |
| **XhCron** | 定时任务配置 | 支持可视化 Cron 表达式配置 |
| **XhCalculate** | 计算器组件 | 支持表达式计算、公式配置 |
| **XhInputTable** | 表格输入 | 支持表格内编辑、数据录入 |
| **XhNumberRange** | 数值范围 | 支持数值区间选择 |
| **XhSysVars** | 系统变量 | 支持系统变量选择、动态变量 |

#### 1.6 显示增强组件

| 组件名称 | 功能说明 | 特色功能 |
|---------|----------|----------|
| **XhQrcode** | 二维码生成 | 支持多种二维码格式、自定义样式 |
| **XhBarcode** | 条形码生成 | 支持多种条形码格式 |
| **XhSign** | 电子签名 | 支持手写签名、签名验证 |
| **XhColorPicker** | 颜色选择器 | 支持色彩管理、主题配色 |
| **XhIconPicker** | 图标选择器 | 支持图标库、自定义图标 |
| **XhOpenData** | 开放数据显示 | 支持第三方数据展示 |
| **XhAlert** | 提示组件 | 基于 Ant Design Vue 扩展 |
| **XhText** | 文本显示 | 支持富文本、格式化显示 |
| **XhLink** | 链接组件 | 支持路由跳转、外部链接 |
| **XhDivider** | 分割线 | 基于 Ant Design Vue 扩展 |

### 2. 表单组件 (Form)

#### 2.1 核心表单组件

| 组件名称 | 功能说明 | 核心特性 |
|---------|----------|----------|
| **BasicForm** | 基础表单 | 支持 Schema 配置、动态表单、验证规则 |
| **FormGenerator** | 表单生成器 | 可视化表单设计、拖拽配置、实时预览 |

#### 2.2 表单特色功能

- **Schema 驱动**：通过 JSON Schema 配置表单结构
- **动态表单**：支持条件显示、字段联动
- **验证集成**：内置验证规则、自定义验证
- **布局灵活**：支持多种布局模式
- **组件映射**：自动映射业务组件

### 3. 表格组件 (Table)

#### 3.1 核心表格组件

| 组件名称 | 功能说明 | 核心特性 |
|---------|----------|----------|
| **BasicTable** | 基础表格 | 支持 CRUD 操作、分页、排序、筛选 |
| **TableAction** | 表格操作 | 支持批量操作、行内编辑 |
| **EditTableHeaderIcon** | 可编辑表头 | 支持表头自定义、列配置 |

#### 3.2 表格特色功能

- **数据管理**：支持本地和远程数据源
- **行编辑**：支持行内编辑、批量编辑
- **导入导出**：支持 Excel 导入导出
- **列配置**：支持动态列、列拖拽
- **分页优化**：支持虚拟滚动、懒加载

#### 3.3 useTable Hook 使用指南

`useTable` 是表格组件的核心 Hook，提供了完整的表格数据管理和操作功能。

##### 3.3.1 基本用法

```typescript
import { useTable } from '/@/components/Table';

const [registerTable, { reload, getSelectRows }] = useTable({
  api: getDataList,
  columns: columns,
  rowKey: 'id',
  pagination: true,
  rowSelection: {
    type: 'checkbox',
  },
});
```

##### 3.3.2 API 函数要求

useTable 的 API 函数必须返回特定格式的响应对象：

```typescript
// ✅ 正确的 API 函数返回格式
async function getDataList(params: any) {
  const response = await httpRequest('/api/data', params);
  return response; // 必须包含 data 字段
}

// 响应格式示例
{
  "code": 200,
  "msg": "Success",
  "data": [
    { "id": "1", "name": "张三", "email": "<EMAIL>" },
    { "id": "2", "name": "李四", "email": "<EMAIL>" }
  ]
}
```

##### 3.3.3 数据处理机制

useTable 内部会自动处理 API 响应：

```typescript
// useTable 内部处理逻辑
const res = await api(params);
const data = res.data; // 自动提取 data 字段

const isArrayResult = Array.isArray(data);
let resultItems = isArrayResult ? data : get(data, listField);
```

##### 3.3.4 搜索功能实现

```typescript
const [registerTable, { reload }] = useTable({
  api: getDataList,
  columns: columns,
  searchInfo: {
    keyword: '', // 初始搜索参数
  },
});

// 搜索处理函数
function handleSearch() {
  reload({
    searchInfo: {
      keyword: searchKeyword.value,
    },
  });
}
```

##### 3.3.5 分页配置

```typescript
const [registerTable] = useTable({
  api: getDataList,
  columns: columns,
  pagination: {
    pageSize: 10,
    showSizeChanger: true,
    showQuickJumper: true,
  },
  // 或者禁用分页
  // pagination: false,
});
```

##### 3.3.6 常见问题解决

**问题1：数据不显示**
- 确保 API 返回格式包含 `data` 字段
- 检查 `rowKey` 配置是否正确
- 验证列配置的 `dataIndex` 与数据字段匹配

**问题2：搜索不生效**
- 使用 `searchInfo` 传递搜索参数
- 确保 API 函数正确处理搜索参数

**问题3：分页异常**
- 检查 API 返回的总数据量字段
- 确认分页参数传递正确

##### 3.3.7 API 适配示例

当后端 API 格式与 useTable 期望不匹配时，可以创建适配函数：

```typescript
// 适配函数示例
const adaptedApi = async (params: any) => {
  // 提取搜索参数
  const keyword = params?.keyword || '';

  // 调用原始 API
  const response = await originalApi(keyword);

  // 返回 useTable 期望的格式
  return response; // 确保包含 data 字段
};

const [registerTable] = useTable({
  api: adaptedApi,
  columns: columns,
});
```

### 4. 模态框组件 (Modal)

#### 4.1 核心模态框组件

| 组件名称 | 功能说明 | 使用场景 |
|---------|----------|----------|
| **BasicModal** | 基础模态框 | 支持拖拽、全屏、嵌套模态框 |
| **BasicDrawer** | 抽屉组件 | 支持多层抽屉、响应式布局 |
| **CustomDialog** | 自定义对话框 | 支持业务对话框、确认框 |

#### 4.2 业务模态框组件

| 组件名称 | 功能说明 | 业务场景 |
|---------|----------|----------|
| **ExportModal** | 导出模态框 | 支持数据导出、格式选择 |
| **ImportModal** | 导入模态框 | 支持数据导入、模板下载 |
| **InterfaceModal** | 接口调用模态框 | 支持 API 调用、参数配置 |
| **PreviewModal** | 预览模态框 | 支持文件预览、数据预览 |
| **SelectModal** | 选择模态框 | 支持复杂数据选择 |
| **SuperQueryModal** | 超级查询模态框 | 支持高级查询、条件组合 |

### 5. 可视化设计组件

#### 5.1 设计器组件

| 组件名称 | 功能说明 | 应用场景 |
|---------|----------|----------|
| **FormGenerator** | 表单设计器 | 可视化表单设计、拖拽配置 |
| **VisualPortal** | 门户设计器 | 支持仪表板设计、组件拖拽 |
| **FlowProcess** | 流程设计器 | 支持工作流设计、节点配置 |
| **PrintDesign** | 打印设计器 | 支持打印模板设计、布局配置 |

#### 5.2 设计器特色功能

- **拖拽设计**：支持组件拖拽、位置调整
- **属性配置**：支持组件属性实时配置
- **实时预览**：支持设计过程实时预览
- **模板管理**：支持模板保存、加载、分享
- **代码生成**：支持配置代码生成

### 6. 增强功能组件

#### 6.1 编辑器组件

| 组件名称 | 功能说明 | 特色功能 |
|---------|----------|----------|
| **CodeEditor** | 代码编辑器 | 支持语法高亮、代码提示、格式化 |
| **Tinymce** | 富文本编辑器 | 支持富文本编辑、图片上传、表格编辑 |
| **JsonPreview** | JSON 预览 | 支持 JSON 格式化、语法高亮 |
| **MonacoEditor** | Monaco 编辑器 | 支持多语言、代码补全、调试 |

#### 6.2 图表组件

| 组件名称 | 功能说明 | 支持图表类型 |
|---------|----------|-------------|
| **Chart** | 图表组件 | 支持 ECharts、多种图表类型 |
| **FlowChart** | 流程图 | 支持流程图绘制、节点编辑 |

#### 6.3 工具组件

| 组件名称 | 功能说明 | 应用场景 |
|---------|----------|----------|
| **Cropper** | 图片裁剪 | 支持图片裁剪、头像上传 |
| **CountDown** | 倒计时 | 支持倒计时、验证码倒计时 |
| **Qrcode** | 二维码 | 支持二维码生成、自定义样式 |
| **Verify** | 验证组件 | 支持拖拽验证、图片验证 |

## 使用指南

### 1. 组件引入

#### 1.1 全局引入

```typescript
// main.ts
import { createApp } from 'vue';
import { setupGlobComp } from './components/registerGlobComp';

const app = createApp(App);
setupGlobComp(app);
```

#### 1.2 按需引入

```typescript
// 引入单个组件
import { XhInput } from '/@/components/Xh/Input';
import { BasicForm } from '/@/components/Form';
import { BasicTable } from '/@/components/Table';

// 引入多个组件
import { XhInput, XhSelect, XhDatePicker } from '/@/components/Xh';
```

### 2. 基础使用示例

#### 2.1 Xh 组件使用

```vue
<template>
  <div>
    <!-- 基础输入框 -->
    <XhInput 
      v-model:value="formData.name"
      placeholder="请输入姓名"
      :prefix-icon="'user'"
      :suffix-icon="'search'"
    />
    
    <!-- 用户选择器 -->
    <XhUserSelect
      v-model:value="formData.userId"
      :multiple="true"
      :org-id="currentOrgId"
      placeholder="请选择用户"
    />
    
    <!-- 日期选择器 -->
    <XhDatePicker
      v-model:value="formData.date"
      format="YYYY-MM-DD"
      placeholder="请选择日期"
    />
    
    <!-- 文件上传 -->
    <XhUploadFile
      v-model:value="formData.files"
      :max-count="5"
      :accept="'.pdf,.doc,.docx'"
      placeholder="请选择文件"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { XhInput, XhUserSelect, XhDatePicker, XhUploadFile } from '/@/components/Xh';

const formData = ref({
  name: '',
  userId: [],
  date: '',
  files: []
});

const currentOrgId = ref('org001');
</script>
```

#### 2.2 表单组件使用

```vue
<template>
  <BasicForm @register="register" @submit="handleSubmit" />
</template>

<script setup lang="ts">
import { BasicForm, useForm } from '/@/components/Form';

const [register, { validate, setFieldsValue }] = useForm({
  labelWidth: 100,
  schemas: [
    {
      field: 'name',
      label: '姓名',
      component: 'XhInput',
      required: true,
      componentProps: {
        placeholder: '请输入姓名'
      }
    },
    {
      field: 'userId',
      label: '用户',
      component: 'XhUserSelect',
      required: true,
      componentProps: {
        multiple: true,
        placeholder: '请选择用户'
      }
    },
    {
      field: 'date',
      label: '日期',
      component: 'XhDatePicker',
      componentProps: {
        format: 'YYYY-MM-DD',
        placeholder: '请选择日期'
      }
    }
  ],
  showActionButtonGroup: true,
  actionColOptions: {
    span: 24
  }
});

async function handleSubmit() {
  try {
    const values = await validate();
    console.log('表单数据:', values);
  } catch (error) {
    console.error('验证失败:', error);
  }
}
</script>
```

#### 2.3 表格组件使用

```vue
<template>
  <BasicTable @register="registerTable" @row-click="handleRowClick">
    <template #toolbar>
      <a-button type="primary" @click="handleAdd">新增</a-button>
      <a-button @click="handleBatchDelete">批量删除</a-button>
    </template>
    
    <template #action="{ record }">
      <TableAction
        :actions="[
          { label: '编辑', onClick: handleEdit.bind(null, record) },
          { label: '删除', onClick: handleDelete.bind(null, record) }
        ]"
      />
    </template>
  </BasicTable>
</template>

<script setup lang="ts">
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import { getUserList } from '/@/api/user';

const [registerTable, { reload, getSelectRows }] = useTable({
  title: '用户列表',
  api: getUserList,
  columns: [
    { title: '姓名', dataIndex: 'name', width: 120 },
    { title: '邮箱', dataIndex: 'email', width: 200 },
    { title: '部门', dataIndex: 'department', width: 150 },
    { title: '创建时间', dataIndex: 'createTime', width: 180 }
  ],
  rowSelection: {
    type: 'checkbox'
  },
  actionColumn: {
    width: 120,
    title: '操作',
    dataIndex: 'action',
    slots: { customRender: 'action' }
  }
});

function handleAdd() {
  // 新增逻辑
}

function handleEdit(record: any) {
  // 编辑逻辑
}

function handleDelete(record: any) {
  // 删除逻辑
}

function handleBatchDelete() {
  const selectedRows = getSelectRows();
  console.log('选中的行:', selectedRows);
}

function handleRowClick(record: any) {
  console.log('点击行:', record);
}
</script>
```

### 3. 高级使用

#### 3.1 表单生成器使用

```vue
<template>
  <FormGenerator
    v-model:value="formSchema"
    :preview="false"
    @save="handleSaveForm"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { FormGenerator } from '/@/components/FormGenerator';

const formSchema = ref([]);

function handleSaveForm(schema: any) {
  console.log('保存表单配置:', schema);
}
</script>
```

#### 3.2 门户设计器使用

```vue
<template>
  <VisualPortal
    v-model:value="portalConfig"
    :edit-mode="true"
    @save="handleSavePortal"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { VisualPortal } from '/@/components/VisualPortal';

const portalConfig = ref({});

function handleSavePortal(config: any) {
  console.log('保存门户配置:', config);
}
</script>
```


  XACE 项目的后端实体设计采用统一命名规范：
  - 所有业务实体都使用 fullName 字段表示显示名称
  - 所有实体都使用 id 作为唯一标识
  - 这包括用户、部门、角色、数据字典等所有下拉选择数据

  2. 减少数据转换开销

  // ❌ 如果使用 label/value，需要转换数据
  const options = data.list.map(item => ({
    label: item.fullName,  // 需要字段映射
    value: item.id
  }));

  // ✅ 使用 fullName/id，直接使用后端数据
  state.optionsObj.eventTypeOptions = res.data.list; // 无需转换

  3. XhSelect 组件的灵活设计

  组件支持自定义字段映射：

  // 默认配置
  fieldNames: {
    value: 'id',
    label: 'fullName',
    disabled: 'disabled'
  }

  // 可以自定义
  <XhSelect
    :field-names="{ value: 'code', label: 'name' }"
    :options="customOptions"
  />

  4. 企业级系统的一致性考虑

  - 开发效率：前后端字段名称一致，减少理解成本
  - 维护成本：统一的数据结构便于维护
  - 扩展性：新增业务实体时自动符合规范

  5. 数据库设计的体现

  数据库层面使用：
  - F_FULLNAME - 完整名称字段
  - F_ID - 主键字段

  前端直接映射这种设计，保持了整个技术栈的一致性。

  如何使用标准的 label/value

  如果需要使用标准的 label 和 value，可以通过以下方式：

  <template>
    <!-- 方式1：自定义字段映射 -->
    <XhSelect
      v-model:value="selectedValue"
      :field-names="{ value: 'value', label: 'label' }"
      :options="standardOptions"
    />

    <!-- 方式2：使用 Ant Design Vue 原生 Select -->
    <a-select
      v-model:value="selectedValue"
      :options="standardOptions"
    />
  </template>

  <script setup lang="ts">
  // 数据转换示例
  const standardOptions = computed(() => {
    return rawData.value.map(item => ({
      label: item.fullName,
      value: item.id
    }));
  });
  </script>

  总结

  XhSelect 使用 fullName/id 是基于 XACE 系统整体架构设计的合理决策：
  - 系统一致性：与后端实体设计保持一致
  - 性能优化：避免不必要的数据转换
  - 开发效率：减少前端数据处理工作
  - 灵活性：仍然支持自定义字段映射

  这种设计在企业级应用中是常见且合理的做法，特别是当整个系统都遵循相同的数据模型规范时。

## 组件特色

### 1. 与 Ant Design Vue 的主要区别

#### 1.1 业务集成
- **Xh 组件**：深度集成业务逻辑，如组织架构、权限控制、数据字典等
- **Ant Design Vue**：通用 UI 组件，需要额外开发业务逻辑

#### 1.2 数据绑定
- **Xh 组件**：支持远程数据源、动态加载、数据联动
- **Ant Design Vue**：主要支持静态数据绑定

#### 1.3 表单支持
- **BasicForm**：支持 Schema 配置、动态表单、可视化设计
- **Ant Design Vue Form**：需要手动编写表单结构

#### 1.4 表格功能
- **BasicTable**：内置 CRUD 操作、导入导出、高级筛选
- **Ant Design Vue Table**：基础表格功能，需要额外开发

### 2. 核心优势

#### 2.1 开发效率
- **配置驱动**：通过配置生成复杂 UI
- **组件封装**：常用业务逻辑封装成组件
- **模板系统**：提供丰富的模板和示例

#### 2.2 业务适配
- **权限集成**：组件级别的权限控制
- **数据字典**：支持系统数据字典
- **多租户**：支持多租户数据隔离

#### 2.3 可维护性
- **TypeScript**：完整的类型支持
- **组件化**：高度组件化的架构
- **文档完善**：详细的使用文档和示例

### 3. 最佳实践

#### 3.1 组件选择
- 优先使用 Xh 系列组件进行业务开发
- 使用 BasicForm 和 BasicTable 进行数据管理
- 使用设计器组件进行可视化配置

#### 3.2 性能优化
- 合理使用组件懒加载
- 避免不必要的组件重渲染
- 使用虚拟滚动处理大量数据

#### 3.3 扩展开发
- 继承现有组件进行功能扩展
- 遵循组件命名和目录规范
- 提供完整的 TypeScript 类型定义

## 总结

XACE 自定义组件库基于 Ant Design Vue 构建，提供了丰富的业务组件和可视化设计工具。通过深度集成业务逻辑、支持配置化开发和提供完整的 TypeScript 支持，大大提高了企业级应用的开发效率和可维护性。

主要特点：
- **业务导向**：深度集成企业业务逻辑
- **配置化**：支持 Schema 驱动的组件配置
- **可视化**：提供丰富的可视化设计工具
- **类型安全**：完整的 TypeScript 类型支持
- **高性能**：优化的组件架构和渲染机制

建议在使用过程中，优先选择 Xh 系列组件，配合 BasicForm 和 BasicTable 进行业务开发，并充分利用可视化设计工具提高开发效率。
