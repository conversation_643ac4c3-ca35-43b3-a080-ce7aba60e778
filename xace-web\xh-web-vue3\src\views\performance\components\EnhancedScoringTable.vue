<template>
  <div class="enhanced-scoring-table">
    <!-- 表格工具栏 -->
    <div class="table-toolbar">
      <div class="toolbar-left">
        <a-space>
          <a-button @click="handleBatchLock" :disabled="!hasSelection" size="small">
            <template #icon><Icon icon="ant-design:lock-outlined" /></template>
            批量锁定
          </a-button>
          <a-button @click="handleBatchUnlock" :disabled="!hasSelection" size="small">
            <template #icon><Icon icon="ant-design:unlock-outlined" /></template>
            批量解锁
          </a-button>
          <a-button @click="handleBatchScore" :disabled="!hasSelection" size="small">
            <template #icon><Icon icon="ant-design:edit-outlined" /></template>
            批量打分
          </a-button>
        </a-space>
      </div>

      <div class="toolbar-right">
        <a-space>
          <a-input-search v-model:value="searchKeyword" placeholder="搜索员工姓名" size="small" style="width: 200px" @search="handleSearch" />
          <a-select v-model:value="sortBy" size="small" style="width: 120px" @change="handleSortChange">
            <a-select-option value="name">按姓名</a-select-option>
            <a-select-option value="score">按分数</a-select-option>
            <a-select-option value="workdays">按工作天数</a-select-option>
          </a-select>
          <a-button @click="handleTableSettings" size="small">
            <template #icon><Icon icon="ant-design:setting-outlined" /></template>
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 增强型表格 -->
    <div class="scoring-table-container">
      <a-table
        :columns="enhancedColumns"
        :data-source="filteredData"
        :row-selection="rowSelection"
        :pagination="false"
        :scroll="{ x: 1200, y: 500 }"
        size="small"
        bordered
        :row-class-name="getRowClassName">
        <template #bodyCell="{ column, record, index }">
          <!-- 员工信息列 -->
          <template v-if="column.key === 'employee'">
            <div class="employee-info">
              <a-avatar :src="record.avatar" size="small" class="employee-avatar">
                {{ record.userName?.charAt(0) }}
              </a-avatar>
              <div class="employee-details">
                <div class="employee-name">
                  <span>{{ record.userName }}</span>
                  <a-tag v-if="record.isLock" color="red" size="small">锁定</a-tag>
                </div>
                <div class="employee-meta">
                  <span class="department">{{ record.departmentName }}</span>
                  <span class="position">{{ record.position }}</span>
                </div>
              </div>
            </div>
          </template>

          <!-- 工作统计列 -->
          <template v-if="column.key === 'workStats'">
            <div class="work-stats">
              <div class="stat-item">
                <span class="label">工作:</span>
                <span class="value">{{ record.reviewActDays || 0 }}天</span>
              </div>
              <div class="stat-item">
                <span class="label">请假:</span>
                <span class="value">{{ record.reviewHolidays || 0 }}天</span>
              </div>
              <div class="stat-item">
                <span class="label">比例:</span>
                <span class="value">{{ record.ratio || 0 }}%</span>
              </div>
            </div>
          </template>

          <!-- 团队得分列 -->
          <template v-if="column.key === 'teamScore'">
            <div class="score-input-container">
              <a-input-number
                v-if="!record.isLock && editable"
                v-model:value="record.checkDeptAvgScore"
                :min="0"
                :max="100"
                :step="1"
                size="small"
                @change="handleScoreChange(record, 'team')"
                @blur="handleScoreBlur(record, 'team')" />
              <span v-else class="score-display">{{ record.checkDeptAvgScore || 0 }}</span>

              <!-- 分数建议 -->
              <div v-if="record.scoreSuggestion?.team" class="score-suggestion">
                <a-tooltip :title="`建议分数: ${record.scoreSuggestion.team}`">
                  <Icon icon="ant-design:bulb-outlined" class="suggestion-icon" />
                </a-tooltip>
              </div>
            </div>
          </template>

          <!-- 个人得分列 -->
          <template v-if="column.key === 'personalScore'">
            <div class="score-input-container">
              <a-input-number
                v-if="!record.isLock && editable"
                v-model:value="record.checkPersonScore"
                :min="0"
                :max="100"
                :step="1"
                size="small"
                @change="handleScoreChange(record, 'personal')"
                @blur="handleScoreBlur(record, 'personal')" />
              <span v-else class="score-display">{{ record.checkPersonScore || 0 }}</span>

              <!-- 分数建议 -->
              <div v-if="record.scoreSuggestion?.personal" class="score-suggestion">
                <a-tooltip :title="`建议分数: ${record.scoreSuggestion.personal}`">
                  <Icon icon="ant-design:bulb-outlined" class="suggestion-icon" />
                </a-tooltip>
              </div>
            </div>
          </template>

          <!-- 实际得分列 -->
          <template v-if="column.key === 'actualScore'">
            <div class="actual-score">
              <div class="score-item">
                <span class="label">团队:</span>
                <span class="value">{{ record.checkDeptActScore || 0 }}</span>
              </div>
              <div class="score-item">
                <span class="label">个人:</span>
                <span class="value">{{ record.personActScore || 0 }}</span>
              </div>
            </div>
          </template>

          <!-- 评分备注列 -->
          <template v-if="column.key === 'comment'">
            <div class="comment-container">
              <a-textarea
                v-if="!record.isLock && editable"
                v-model:value="record.checkComment"
                :auto-size="{ minRows: 2, maxRows: 4 }"
                :maxlength="500"
                placeholder="请输入评分备注..."
                size="small"
                @change="handleCommentChange(record)" />
              <div v-else class="comment-display">
                {{ record.checkComment || '暂无备注' }}
              </div>

              <!-- 字数统计 -->
              <div v-if="!record.isLock && editable" class="comment-count"> {{ (record.checkComment || '').length }}/500 </div>
            </div>
          </template>

          <!-- 操作列 -->
          <template v-if="column.key === 'actions'">
            <div class="action-buttons">
              <a-space size="small">
                <a-tooltip :title="record.isLock ? '解锁' : '锁定'">
                  <a-button size="small" :type="record.isLock ? 'primary' : 'default'" @click="handleToggleLock(record)">
                    <Icon :icon="record.isLock ? 'ant-design:unlock-outlined' : 'ant-design:lock-outlined'" />
                  </a-button>
                </a-tooltip>

                <a-tooltip title="查看详情">
                  <a-button size="small" @click="handleViewDetail(record)">
                    <Icon icon="ant-design:eye-outlined" />
                  </a-button>
                </a-tooltip>

                <a-tooltip title="历史记录">
                  <a-button size="small" @click="handleViewHistory(record)">
                    <Icon icon="ant-design:history-outlined" />
                  </a-button>
                </a-tooltip>
              </a-space>
            </div>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 批量打分模态框 -->
    <a-modal v-model:open="batchScoreVisible" title="批量打分" @ok="handleBatchScoreConfirm" @cancel="handleBatchScoreCancel">
      <div class="batch-score-form">
        <a-form :model="batchScoreForm" layout="vertical">
          <a-form-item label="团队得分">
            <a-input-number v-model:value="batchScoreForm.teamScore" :min="0" :max="100" :step="1" style="width: 100%" />
          </a-form-item>
          <a-form-item label="个人得分">
            <a-input-number v-model:value="batchScoreForm.personalScore" :min="0" :max="100" :step="1" style="width: 100%" />
          </a-form-item>
          <a-form-item label="评分备注">
            <a-textarea v-model:value="batchScoreForm.comment" :auto-size="{ minRows: 3, maxRows: 6 }" :maxlength="500" placeholder="请输入批量评分备注..." />
          </a-form-item>
        </a-form>

        <div class="selected-employees">
          <h4>选中的员工 ({{ selectedRowKeys.length }}人)</h4>
          <div class="employee-tags">
            <a-tag v-for="employee in selectedEmployees" :key="employee.userId" closable @close="handleRemoveFromBatch(employee.userId)">
              {{ employee.userName }}
            </a-tag>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, reactive, watch } from 'vue';
  import { Icon } from '/@/components/Icon';
  import { useMessage } from '/@/hooks/web/useMessage';

  interface Employee {
    userId: string;
    userName: string;
    departmentName?: string;
    position?: string;
    avatar?: string;
    isLock: boolean;
    checkDeptAvgScore?: number;
    checkPersonScore?: number;
    checkDeptActScore?: number;
    personActScore?: number;
    checkComment?: string;
    reviewActDays?: number;
    reviewHolidays?: number;
    ratio?: number;
    scoreSuggestion?: {
      team?: number;
      personal?: number;
    };
  }

  const props = defineProps({
    dataSource: {
      type: Array as PropType<Employee[]>,
      default: () => [],
    },
    editable: {
      type: Boolean,
      default: true,
    },
  });

  const emit = defineEmits(['scoreChange', 'lockChange', 'viewDetail', 'viewHistory']);

  const { createMessage } = useMessage();

  // 响应式数据
  const searchKeyword = ref('');
  const sortBy = ref('name');
  const selectedRowKeys = ref<string[]>([]);
  const batchScoreVisible = ref(false);

  const batchScoreForm = reactive({
    teamScore: undefined,
    personalScore: undefined,
    comment: '',
  });

  // 表格列配置
  const enhancedColumns = [
    {
      title: '员工信息',
      key: 'employee',
      width: 200,
      fixed: 'left',
    },
    {
      title: '工作统计',
      key: 'workStats',
      width: 150,
      align: 'center',
    },
    {
      title: '团队得分',
      key: 'teamScore',
      width: 120,
      align: 'center',
    },
    {
      title: '个人得分',
      key: 'personalScore',
      width: 120,
      align: 'center',
    },
    {
      title: '实际得分',
      key: 'actualScore',
      width: 120,
      align: 'center',
    },
    {
      title: '评分备注',
      key: 'comment',
      width: 250,
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      fixed: 'right',
      align: 'center',
    },
  ];

  // 计算属性
  const filteredData = computed(() => {
    let data = [...props.dataSource];

    // 搜索过滤
    if (searchKeyword.value) {
      data = data.filter(item => item.userName?.toLowerCase().includes(searchKeyword.value.toLowerCase()));
    }

    // 排序
    data.sort((a, b) => {
      switch (sortBy.value) {
        case 'name':
          return (a.userName || '').localeCompare(b.userName || '');
        case 'score':
          return (b.checkPersonScore || 0) - (a.checkPersonScore || 0);
        case 'workdays':
          return (b.reviewActDays || 0) - (a.reviewActDays || 0);
        default:
          return 0;
      }
    });

    return data;
  });

  const hasSelection = computed(() => selectedRowKeys.value.length > 0);

  const selectedEmployees = computed(() => props.dataSource.filter(emp => selectedRowKeys.value.includes(emp.userId)));

  // 行选择配置
  const rowSelection = {
    selectedRowKeys: selectedRowKeys,
    onChange: (keys: string[]) => {
      selectedRowKeys.value = keys;
    },
    getCheckboxProps: (record: Employee) => ({
      disabled: record.isLock,
    }),
  };

  // 方法
  const getRowClassName = (record: Employee) => {
    const classes = [];
    if (record.isLock) classes.push('row-locked');
    if (selectedRowKeys.value.includes(record.userId)) classes.push('row-selected');
    return classes.join(' ');
  };

  const handleScoreChange = (record: Employee, type: 'team' | 'personal') => {
    emit('scoreChange', { record, type });
  };

  const handleScoreBlur = (record: Employee, type: 'team' | 'personal') => {
    // 分数输入失焦时的处理
  };

  const handleCommentChange = (record: Employee) => {
    // 备注变更处理
  };

  const handleToggleLock = (record: Employee) => {
    record.isLock = !record.isLock;
    emit('lockChange', record);
  };

  const handleViewDetail = (record: Employee) => {
    emit('viewDetail', record);
  };

  const handleViewHistory = (record: Employee) => {
    emit('viewHistory', record);
  };

  const handleBatchLock = () => {
    selectedEmployees.value.forEach(emp => {
      emp.isLock = true;
    });
    createMessage.success(`已锁定 ${selectedRowKeys.value.length} 名员工`);
    selectedRowKeys.value = [];
  };

  const handleBatchUnlock = () => {
    selectedEmployees.value.forEach(emp => {
      emp.isLock = false;
    });
    createMessage.success(`已解锁 ${selectedRowKeys.value.length} 名员工`);
    selectedRowKeys.value = [];
  };

  const handleBatchScore = () => {
    if (selectedRowKeys.value.length === 0) {
      createMessage.warning('请先选择要批量打分的员工');
      return;
    }
    batchScoreVisible.value = true;
  };

  const handleBatchScoreConfirm = () => {
    selectedEmployees.value.forEach(emp => {
      if (batchScoreForm.teamScore !== undefined) {
        emp.checkDeptAvgScore = batchScoreForm.teamScore;
      }
      if (batchScoreForm.personalScore !== undefined) {
        emp.checkPersonScore = batchScoreForm.personalScore;
      }
      if (batchScoreForm.comment) {
        emp.checkComment = batchScoreForm.comment;
      }
    });

    createMessage.success(`已为 ${selectedRowKeys.value.length} 名员工批量打分`);
    batchScoreVisible.value = false;
    selectedRowKeys.value = [];

    // 重置表单
    Object.assign(batchScoreForm, {
      teamScore: undefined,
      personalScore: undefined,
      comment: '',
    });
  };

  const handleBatchScoreCancel = () => {
    batchScoreVisible.value = false;
    Object.assign(batchScoreForm, {
      teamScore: undefined,
      personalScore: undefined,
      comment: '',
    });
  };

  const handleRemoveFromBatch = (userId: string) => {
    selectedRowKeys.value = selectedRowKeys.value.filter(key => key !== userId);
  };

  const handleSearch = () => {
    // 搜索处理已在计算属性中实现
  };

  const handleSortChange = () => {
    // 排序处理已在计算属性中实现
  };

  const handleTableSettings = () => {
    // 表格设置
    createMessage.info('表格设置功能开发中...');
  };
</script>

<style lang="less" scoped>
  // 响应式设计
  @media (max-width: 1200px) {
    .enhanced-scoring-table {
      .table-toolbar {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;

        .toolbar-left,
        .toolbar-right {
          justify-content: center;
        }
      }
    }
  }

  @media (max-width: 768px) {
    .enhanced-scoring-table {
      .scoring-table-container {
        :deep(.ant-table) {
          .ant-table-tbody > tr > td {
            padding: 4px;
          }
        }

        .employee-info {
          flex-direction: column;
          align-items: flex-start;
          gap: 4px;

          .employee-details {
            .employee-meta {
              flex-direction: column;
              gap: 2px;

              .department,
              .position {
                &::after {
                  display: none;
                }
              }
            }
          }
        }

        .work-stats,
        .actual-score {
          .stat-item,
          .score-item {
            font-size: 11px;
          }
        }
      }
    }
  }
  .enhanced-scoring-table {
    .table-toolbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding: 12px 16px;
      background: #fafafa;
      border-radius: 6px;

      .toolbar-left,
      .toolbar-right {
        display: flex;
        align-items: center;
      }
    }

    .scoring-table-container {
      .employee-info {
        display: flex;
        align-items: center;
        gap: 8px;

        .employee-avatar {
          flex-shrink: 0;
        }

        .employee-details {
          flex: 1;
          min-width: 0;

          .employee-name {
            display: flex;
            align-items: center;
            gap: 4px;
            font-weight: 500;
            margin-bottom: 2px;
          }

          .employee-meta {
            display: flex;
            gap: 8px;
            font-size: 12px;
            color: #666;

            .department,
            .position {
              &:not(:last-child)::after {
                content: '|';
                margin-left: 8px;
                color: #d9d9d9;
              }
            }
          }
        }
      }

      .work-stats {
        .stat-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: 2px;
          font-size: 12px;

          .label {
            color: #666;
          }

          .value {
            font-weight: 500;
            color: #333;
          }
        }
      }

      .score-input-container {
        position: relative;
        display: flex;
        align-items: center;
        gap: 4px;

        .score-display {
          font-weight: 500;
          color: #333;
        }

        .score-suggestion {
          .suggestion-icon {
            color: #faad14;
            cursor: pointer;

            &:hover {
              color: #ffc53d;
            }
          }
        }
      }

      .actual-score {
        .score-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: 2px;
          font-size: 12px;

          .label {
            color: #666;
          }

          .value {
            font-weight: 500;
            color: #1890ff;
          }
        }
      }

      .comment-container {
        position: relative;

        .comment-display {
          font-size: 12px;
          color: #666;
          line-height: 1.4;
          max-height: 60px;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .comment-count {
          position: absolute;
          bottom: 4px;
          right: 8px;
          font-size: 10px;
          color: #999;
          background: rgba(255, 255, 255, 0.8);
          padding: 0 4px;
          border-radius: 2px;
        }
      }

      .action-buttons {
        display: flex;
        justify-content: center;
      }

      // 行状态样式
      :deep(.row-locked) {
        background-color: #fff2f0;

        .ant-table-cell {
          color: #999;
        }
      }

      :deep(.row-selected) {
        background-color: #e6f7ff;
      }

      // 表格样式优化
      :deep(.ant-table) {
        .ant-table-thead > tr > th {
          background: #fafafa;
          font-weight: 600;
          color: #333;
        }

        .ant-table-tbody > tr > td {
          padding: 8px;
          vertical-align: top;
        }

        .ant-table-tbody > tr:hover > td {
          background: #f5f5f5;
        }
      }
    }

    .batch-score-form {
      .selected-employees {
        margin-top: 20px;
        padding-top: 16px;
        border-top: 1px solid #f0f0f0;

        h4 {
          margin-bottom: 12px;
          font-size: 14px;
          color: #333;
        }

        .employee-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          max-height: 120px;
          overflow-y: auto;
        }
      }
    }
  }
</style>
