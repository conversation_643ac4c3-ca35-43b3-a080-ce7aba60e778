import { defHttp } from '/@/utils/http/axios';

const Api = {
  InvoiceReportList: '/project/report/invoice/list',
  InvoiceReportSummary: '/project/report/invoice/summary',
  InvoiceReportExport: '/project/report/invoice/export',
  DepartmentList: '/project/report/invoice/departments',
};

// 已开票未收款明细数据接口
export interface InvoiceReportItem {
  month: string;
  deptname: string;
  fRealname: string; // 注意：后端返回的是 frealname，需要在API层处理
  signYear: number;
  name: string;
  fktj: string;
  ratio: number;
  cmMoney: number;
  payStatus: number;
  yingshouDate: string;
  shoukuanDate: string;
  kaipiaoDate: string;
  yushouDate: string;
  ybAmount: number;
  ebAmount: number;
  otherAmount: number;
  allAmount: number;
  note: string;
  fullNote?: string;
}

// 项目经理汇总数据接口
export interface ManagerSummaryItem {
  manager: string;
  totalAmount: number;
}

// 查询参数接口
export interface InvoiceReportParams {
  dept?: string;
  xmjl?: string;
}

/**
 * 获取已开票未收款明细列表
 */
export function getInvoiceReportList(params: InvoiceReportParams) {
  return defHttp
    .post<InvoiceReportItem[]>({
      url: Api.InvoiceReportList,
      data: params,
    })
    .then((response: any) => {
      // 处理后端返回的 {code, msg, data} 格式
      let data = response;
      if (response && response.data) {
        data = response.data;
      }

      // 处理字段名不匹配问题
      if (Array.isArray(data)) {
        return data.map((item: any) => ({
          ...item,
          fRealname: item.frealname || item.fRealname, // 兼容后端字段名
        }));
      }

      return data || [];
    });
}

/**
 * 获取项目经理汇总统计
 */
export function getInvoiceReportSummary(params: InvoiceReportParams) {
  return defHttp
    .post<ManagerSummaryItem[]>({
      url: Api.InvoiceReportSummary,
      data: params,
    })
    .then((response: any) => {
      // 处理后端返回的 {code, msg, data} 格式
      if (response && response.data) {
        return response.data;
      }
      return response || [];
    });
}

/**
 * 导出已开票未收款报表
 */
export function exportInvoiceReport(params: InvoiceReportParams) {
  return defHttp.post({
    url: Api.InvoiceReportExport,
    data: params,
    responseType: 'blob',
  });
}

/**
 * 获取部门列表
 */
export function getDepartmentList() {
  return defHttp.get<Array<{ label: string; value: string }>>({
    url: Api.DepartmentList,
  });
}
