<template>
  <div>
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-lg font-medium">采购合同列表</h3>
      <a-button type="primary" @click="handleCreate">新增采购合同</a-button>
    </div>

    <a-table :columns="columns" :dataSource="paycontractList" :pagination="false" :loading="loading" rowKey="pcId">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'amount'">
          {{ formatAmount(record.amount) }}
        </template>
        <template v-if="column.key === 'yfAmount'">
          {{ formatAmount(record.yfAmount) }}
        </template>
        <template v-if="column.key === 'status'">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusText(record.status) }}
          </a-tag>
        </template>
        <template v-if="column.key === 'moneyStatus'">
          <a-tag :color="getMoneyStatusColor(record.moneyStatus)">
            {{ getMoneyStatusText(record.moneyStatus) }}
          </a-tag>
        </template>
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'clarity:info-standard-line',
                tooltip: '查看详情',
                onClick: handleView.bind(null, record),
              },
              {
                icon: 'clarity:note-edit-line',
                tooltip: '编辑',
                onClick: handleEdit.bind(null, record),
                ifShow: record.status === 'draft' || record.status === 'pending',
              },
              {
                icon: 'ant-design:file-done-outlined',
                tooltip: '签订合同',
                onClick: handleSign.bind(null, record),
                ifShow: record.status === 'approved',
              },
            ]" />
        </template>
      </template>
    </a-table>

    <PaycontractDrawer @register="registerPaycontractDrawer" @success="loadPaycontractList" />
    <SignForm @register="registerSignForm" @reload="loadPaycontractList" />
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, PropType } from 'vue';
  import { TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import { useDrawer } from '/@/components/Drawer';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useRouter } from 'vue-router';
  import { getPaycontractListByContractId, PaycontractModel } from '/@/api/project/paycontract';
  import PaycontractDrawer from '../paymentContract/PaycontractDrawer.vue';
  import SignForm from '../paymentContract/SignForm.vue';

  const props = defineProps({
    contractId: {
      type: String as PropType<string>,
      required: true,
    },
  });

  const { createMessage } = useMessage();
  const [registerPaycontractDrawer, { openDrawer: openPaycontractDrawer }] = useDrawer();
  const [registerSignForm, { openModal: openSignModal }] = useModal();
  const router = useRouter();

  const paycontractList = ref<PaycontractModel[]>([]);
  const loading = ref(false);

  // 采购合同状态映射
  const statusMap = {
    draft: { text: '草稿', color: 'default' },
    pending: { text: '待审批', color: 'warning' },
    approved: { text: '待签订', color: 'processing' },
    executing: { text: '执行中', color: 'processing' },
    completed: { text: '已完成', color: 'success' },
    terminated: { text: '已中止', color: 'error' },
  };

  // 付款状态映射
  const moneyStatusMap = {
    unpaid: { text: '未付款', color: 'default' },
    partial: { text: '部分付款', color: 'processing' },
    paid: { text: '已付讫', color: 'success' },
  };

  // 获取采购合同状态文本
  function getStatusText(status: string) {
    return statusMap[status]?.text || status;
  }

  // 获取采购合同状态颜色
  function getStatusColor(status: string) {
    return statusMap[status]?.color || 'default';
  }

  // 获取付款状态文本
  function getMoneyStatusText(status: string) {
    return moneyStatusMap[status]?.text || status;
  }

  // 获取付款状态颜色
  function getMoneyStatusColor(status: string) {
    return moneyStatusMap[status]?.color || 'default';
  }

  // 格式化金额
  function formatAmount(amount: number) {
    return amount ? amount.toLocaleString('zh-CN', { style: 'currency', currency: 'CNY' }) : '¥0.00';
  }

  // 表格列定义
  const columns = [
    {
      title: '采购合同名称',
      dataIndex: 'name',
      width: 200,
    },
    {
      title: '采购合同编号',
      dataIndex: 'cNo',
      width: 150,
    },
    {
      title: '供应商',
      dataIndex: 'supplierName',
      width: 150,
    },
    {
      title: '采购合同金额',
      key: 'amount',
      dataIndex: 'amount',
      width: 150,
    },
    {
      title: '已付金额',
      key: 'yfAmount',
      dataIndex: 'yfAmount',
      width: 150,
    },
    {
      title: '采购合同状态',
      key: 'status',
      dataIndex: 'status',
      width: 100,
    },
    {
      title: '付款状态',
      key: 'moneyStatus',
      dataIndex: 'moneyStatus',
      width: 100,
    },
    {
      title: '签订日期',
      dataIndex: 'signDate',
      width: 120,
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      fixed: 'right' as const,
    },
  ];

  // 加载采购合同列表
  async function loadPaycontractList() {
    try {
      loading.value = true;
      const data = await getPaycontractListByContractId(props.contractId);
      paycontractList.value = data;
    } catch (error) {
      console.error('获取采购合同列表失败:', error);
      createMessage.error('获取采购合同列表失败');
    } finally {
      loading.value = false;
    }
  }

  // 新增采购合同
  function handleCreate() {
    openPaycontractDrawer(true, {
      contractId: props.contractId,
      isUpdate: false,
    });
  }

  // 编辑采购合同
  function handleEdit(record: PaycontractModel) {
    openPaycontractDrawer(true, {
      record,
      isUpdate: true,
    });
  }

  // 查看采购合同详情
  function handleView(record: PaycontractModel) {
    openPaycontractDrawer(true, {
      record,
      isUpdate: false,
      readonly: true,
    });
  }

  // 签订采购合同
  function handleSign(record: PaycontractModel) {
    openSignModal(true, {
      paycontractId: record.pcId,
      record,
    });
  }

  onMounted(() => {
    loadPaycontractList();
  });
</script>
