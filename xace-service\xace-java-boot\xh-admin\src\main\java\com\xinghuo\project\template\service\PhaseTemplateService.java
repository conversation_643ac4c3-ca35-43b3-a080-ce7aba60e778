package com.xinghuo.project.template.service;

import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.project.template.entity.PhaseTemplateEntity;
import com.xinghuo.project.template.model.PhaseTemplatePagination;

import java.util.List;
import java.util.Map;

/**
 * 阶段模板服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
public interface PhaseTemplateService extends BaseService<PhaseTemplateEntity> {

    /**
     * 获取阶段模板列表
     *
     * @param pagination 分页参数
     * @return 阶段模板列表
     */
    List<PhaseTemplateEntity> getList(PhaseTemplatePagination pagination);



    /**
     * 获取阶段模板详情
     *
     * @param id 阶段模板ID
     * @return 阶段模板信息
     */
    PhaseTemplateEntity getInfo(String id);

    /**
     * 创建阶段模板
     *
     * @param entity 阶段模板信息
     * @return 阶段模板ID
     */
    String create(PhaseTemplateEntity entity);

    /**
     * 更新阶段模板
     *
     * @param id 阶段模板ID
     * @param entity 阶段模板信息
     */
    void update(String id, PhaseTemplateEntity entity);

    /**
     * 删除阶段模板
     *
     * @param id 阶段模板ID
     */
    void delete(String id);

    /**
     * 批量删除阶段模板
     *
     * @param ids 阶段模板ID列表
     */
    void batchDelete(List<String> ids);

    /**
     * 更新阶段模板状态
     *
     * @param id 阶段模板ID
     * @param status 状态
     */
    void updateStatus(String id, Integer status);

    /**
     * 批量更新状态
     *
     * @param ids 阶段模板ID列表
     * @param status 状态
     */
    void batchUpdateStatus(List<String> ids, Integer status);

    /**
     * 检查阶段编码是否存在
     *
     * @param code 阶段编码
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean isExistByCode(String code, String excludeId);

    /**
     * 根据编码获取阶段模板
     *
     * @param code 阶段编码
     * @return 阶段模板信息
     */
    PhaseTemplateEntity getByCode(String code);

    /**
     * 获取阶段模板选择列表
     *
     * @param keyword 关键字
     * @return 阶段模板列表
     */
    List<PhaseTemplateEntity> getSelectList(String keyword);

    /**
     * 启用阶段模板
     *
     * @param id 阶段模板ID
     */
    void enable(String id);

    /**
     * 禁用阶段模板
     *
     * @param id 阶段模板ID
     */
    void disable(String id);

    /**
     * 复制阶段模板
     *
     * @param id 源阶段模板ID
     * @param newName 新名称
     * @return 新阶段模板ID
     */
    String copy(String id, String newName);

    /**
     * 生成阶段编码
     *
     * @return 阶段编码
     */
    String generateCode();

    /**
     * 获取阶段模板使用情况
     *
     * @param id 阶段模板ID
     * @return 使用情况统计
     */
    Map<String, Object> getPhaseTemplateUsageInfo(String id);
}
