<template>
  <BasicModal v-bind="$attrs" @register="registerModal" title="添加跟踪记录" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, unref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form';
  import { FormSchema } from '/@/components/Form/src/types/form';
  import { updateBusinessLastNote, BusinessModel } from '/@/api/project/business';
  import { useMessage } from '/@/hooks/web/useMessage';

  const { createMessage } = useMessage();
  const emit = defineEmits(['register', 'reload']);
  const businessId = ref('');
  const businessInfo = ref<Partial<BusinessModel>>({});

  // 表单配置
  const formSchemas: FormSchema[] = [
    {
      field: 'projectName',
      label: '项目名称',
      component: 'Input',
      show: true,
      componentProps: { disabled: true },
    },
    {
      field: 'status',
      label: '当前状态',
      component: 'Input',
      show: true,
      componentProps: { disabled: true },
    },
    {
      field: 'lastNote',
      label: '当前跟踪记录',
      component: 'InputTextArea',
      show: true,
      componentProps: { disabled: true, rows: 3 },
    },
    {
      field: 'newNote',
      label: '新跟踪记录',
      component: 'InputTextArea',
      required: true,
      componentProps: { placeholder: '请输入新的跟踪记录', maxlength: 2000, showCount: true, rows: 5 },
      rules: [
        { required: true, trigger: 'blur', message: '请输入跟踪记录' },
        { max: 2000, message: '跟踪记录最多为2000个字符', trigger: 'blur' },
      ],
    },
  ];

  // 注册表单
  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 100,
    schemas: formSchemas,
    showActionButtonGroup: false,
  });

  // 注册模态框
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async data => {
    resetFields();
    setModalProps({ confirmLoading: false });

    if (data.record) {
      businessId.value = data.record.id;
      businessInfo.value = data.record;

      setFieldsValue({
        projectName: data.record.projectName,
        status: data.record.status,
        lastNote: data.record.lastNote || '暂无跟踪记录',
      });
    }
  });

  // 提交表单
  async function handleSubmit() {
    try {
      const values = await validate();
      setModalProps({ confirmLoading: true });

      // 更新最后跟踪记录
      await updateBusinessLastNote(businessId.value, values.newNote);
      createMessage.success('跟踪记录添加成功');

      closeModal();
      emit('reload');
    } catch (error) {
      console.error('表单验证失败或提交出错:', error);
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
