import { defHttp } from '/@/utils/http/axios';

// 绩效预览
export function performancePreviewList(data) {
  return new Promise((resolve, reject) => {
    defHttp
      .post({
        url: `/api/checkscore/userConfig/preview`,
        data,
      })
      .then(({ data }) => {
        const { cecList, canPublic, monthLength, noPlatScore, reviewMonthAndStatus, missingUserNames, extraUserNames, currentUserCount, totalConfigUserCount } =
          data;
        const extra = { canPublic, monthLength, noPlatScore, reviewMonthAndStatus, missingUserNames, extraUserNames, currentUserCount, totalConfigUserCount };
        const retData = {
          data: {
            list: cecList,
            extra,
            pagination: {
              currentPage: 1,
              pageSize: cecList.length,
              total: cecList.length,
            },
          },
        };
        resolve(retData);
      })
      .catch(err => {
        reject(err);
      });
  });
}

/**
 * 绩效成绩发布
 * @returns
 */
export function performancePublish() {
  return defHttp.put({
    url: `/api/checkscore/userConfig/publish`,
  });
}
