<template>
  <div class="member-form">
    <a-spin :spinning="loading">
      <a-form ref="formRef" :model="formData" :rules="formRules" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }" :disabled="readonly">
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="姓名" name="name">
              <a-input v-model:value="formData.name" placeholder="请输入姓名" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="工号" name="employeeId">
              <a-input v-model:value="formData.employeeId" placeholder="请输入工号" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="项目角色" name="role">
              <a-select v-model:value="formData.role" placeholder="请选择项目角色">
                <a-select-option value="1">项目经理</a-select-option>
                <a-select-option value="2">开发工程师</a-select-option>
                <a-select-option value="3">测试工程师</a-select-option>
                <a-select-option value="4">产品经理</a-select-option>
                <a-select-option value="5">UI设计师</a-select-option>
                <a-select-option value="6">架构师</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="部门" name="department">
              <a-input v-model:value="formData.department" placeholder="请输入部门" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="职位" name="position">
              <a-input v-model:value="formData.position" placeholder="请输入职位" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="工作经验" name="experience">
              <a-input-number v-model:value="formData.experience" :min="0" :max="50" placeholder="请输入工作经验" style="width: 100%" addon-after="年" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="邮箱" name="email">
              <a-input v-model:value="formData.email" placeholder="请输入邮箱" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="手机号" name="phone">
              <a-input v-model:value="formData.phone" placeholder="请输入手机号" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="加入日期" name="joinDate">
              <a-date-picker v-model:value="formData.joinDate" placeholder="请选择加入日期" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="成员状态" name="status">
              <a-select v-model:value="formData.status" placeholder="请选择成员状态">
                <a-select-option value="1">在职</a-select-option>
                <a-select-option value="2">请假</a-select-option>
                <a-select-option value="3">离职</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="工作负载" name="workload">
              <a-slider v-model:value="formData.workload" :min="0" :max="100" :marks="workloadMarks" :tip-formatter="value => `${value}%`" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="技能等级" name="skillLevel">
              <a-rate v-model:value="formData.skillLevel" :count="5" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="24">
            <a-form-item label="技能标签" name="skills" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }">
              <a-select v-model:value="formData.skills" mode="tags" placeholder="请选择或输入技能标签" style="width: 100%" :options="skillOptions" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="24">
            <a-form-item label="个人简介" name="bio" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }">
              <a-textarea v-model:value="formData.bio" placeholder="请输入个人简介" :rows="4" />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 权限设置 -->
        <a-divider>权限设置</a-divider>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="项目权限" name="projectPermission">
              <a-select v-model:value="formData.projectPermission" placeholder="请选择项目权限">
                <a-select-option value="1">只读</a-select-option>
                <a-select-option value="2">编辑</a-select-option>
                <a-select-option value="3">管理</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="文档权限" name="documentPermission">
              <a-select v-model:value="formData.documentPermission" placeholder="请选择文档权限">
                <a-select-option value="1">只读</a-select-option>
                <a-select-option value="2">编辑</a-select-option>
                <a-select-option value="3">管理</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="24">
            <a-form-item label="功能权限" name="functionPermissions" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }">
              <a-checkbox-group v-model:value="formData.functionPermissions" :options="functionOptions" />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 通知设置 -->
        <a-divider>通知设置</a-divider>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="邮件通知">
              <a-switch v-model:checked="formData.emailNotification" />
              <span class="ml-2 text-sm text-gray-500">接收项目相关邮件通知</span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="短信通知">
              <a-switch v-model:checked="formData.smsNotification" />
              <span class="ml-2 text-sm text-gray-500">接收重要事件短信通知</span>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="任务提醒">
              <a-switch v-model:checked="formData.taskReminder" />
              <span class="ml-2 text-sm text-gray-500">接收任务截止提醒</span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="进度通知">
              <a-switch v-model:checked="formData.progressNotification" />
              <span class="ml-2 text-sm text-gray-500">接收项目进度通知</span>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>

    <!-- 底部操作按钮 -->
    <div v-if="!readonly" class="footer-actions mt-6 text-center">
      <a-space>
        <a-button type="primary" @click="handleSave" :loading="saveLoading"> 保存 </a-button>
        <a-button @click="handleCancel"> 取消 </a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import { Form, Row, Col, Input, InputNumber, DatePicker, Select, Textarea, Slider, Rate, Checkbox, Switch, Divider, Button, Space } from 'ant-design-vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import dayjs from 'dayjs';

  const props = defineProps({
    memberId: {
      type: String,
      default: '',
    },
    readonly: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits(['save', 'close']);

  const { createMessage } = useMessage();

  // 响应式数据
  const loading = ref(false);
  const saveLoading = ref(false);
  const formRef = ref();

  // 表单数据
  const formData = reactive({
    name: '',
    employeeId: '',
    role: '',
    department: '',
    position: '',
    experience: 0,
    email: '',
    phone: '',
    joinDate: null,
    status: '1',
    workload: 80,
    skillLevel: 3,
    skills: [],
    bio: '',
    projectPermission: '2',
    documentPermission: '2',
    functionPermissions: [],
    emailNotification: true,
    smsNotification: false,
    taskReminder: true,
    progressNotification: true,
  });

  // 表单验证规则
  const formRules = {
    name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
    employeeId: [{ required: true, message: '请输入工号', trigger: 'blur' }],
    role: [{ required: true, message: '请选择项目角色', trigger: 'change' }],
    department: [{ required: true, message: '请输入部门', trigger: 'blur' }],
    position: [{ required: true, message: '请输入职位', trigger: 'blur' }],
    email: [
      { required: true, message: '请输入邮箱', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' },
    ],
    phone: [
      { required: true, message: '请输入手机号', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' },
    ],
    joinDate: [{ required: true, message: '请选择加入日期', trigger: 'change' }],
  };

  // 工作负载标记
  const workloadMarks = {
    0: '0%',
    25: '25%',
    50: '50%',
    75: '75%',
    100: '100%',
  };

  // 技能选项
  const skillOptions = [
    { label: 'Vue.js', value: 'vue' },
    { label: 'React', value: 'react' },
    { label: 'Angular', value: 'angular' },
    { label: 'JavaScript', value: 'javascript' },
    { label: 'TypeScript', value: 'typescript' },
    { label: 'Java', value: 'java' },
    { label: 'Python', value: 'python' },
    { label: 'Node.js', value: 'nodejs' },
    { label: 'MySQL', value: 'mysql' },
    { label: 'Redis', value: 'redis' },
    { label: 'Docker', value: 'docker' },
    { label: 'Kubernetes', value: 'k8s' },
  ];

  // 功能权限选项
  const functionOptions = [
    { label: '任务管理', value: 'task' },
    { label: '进度跟踪', value: 'progress' },
    { label: '文档管理', value: 'document' },
    { label: '风险管理', value: 'risk' },
    { label: '资源管理', value: 'resource' },
    { label: '报告生成', value: 'report' },
    { label: '系统设置', value: 'setting' },
    { label: '用户管理', value: 'user' },
  ];

  onMounted(() => {
    if (props.memberId) {
      loadMemberDetail();
    }
  });

  // 加载成员详情
  const loadMemberDetail = async () => {
    loading.value = true;
    try {
      // 这里调用实际的API
      // const result = await getMemberDetail(props.memberId);
      // Object.assign(formData, result.data);

      // 模拟数据
      Object.assign(formData, {
        name: '张三',
        employeeId: 'EMP001',
        role: '1',
        department: '技术部',
        position: '高级开发工程师',
        experience: 5,
        email: '<EMAIL>',
        phone: '13800138001',
        joinDate: dayjs('2024-01-15'),
        status: '1',
        workload: 85,
        skillLevel: 4,
        skills: ['vue', 'javascript', 'java', 'mysql'],
        bio: '具有5年软件开发经验，擅长前后端开发，熟悉Vue.js、Java等技术栈。',
        projectPermission: '3',
        documentPermission: '2',
        functionPermissions: ['task', 'progress', 'document', 'report'],
        emailNotification: true,
        smsNotification: false,
        taskReminder: true,
        progressNotification: true,
      });
    } catch (error) {
      console.error('加载成员详情失败:', error);
      createMessage.error('加载成员详情失败');
    } finally {
      loading.value = false;
    }
  };

  // 事件处理函数
  const handleSave = async () => {
    try {
      await formRef.value.validate();
      saveLoading.value = true;

      // 这里调用保存API
      // await saveMember(formData);

      createMessage.success('保存成功');
      emit('save');
    } catch (error) {
      console.error('保存失败:', error);
    } finally {
      saveLoading.value = false;
    }
  };

  const handleCancel = () => {
    emit('close');
  };
</script>

<style lang="less" scoped>
  .member-form {
    .footer-actions {
      border-top: 1px solid #f0f0f0;
      padding-top: 16px;
    }
  }
</style>
