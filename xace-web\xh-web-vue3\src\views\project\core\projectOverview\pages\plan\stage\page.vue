<template>
  <div class="stage-page p-4">
    <!-- 页面标题 -->
    <div class="page-header mb-4">
      <h2 class="text-xl font-semibold mb-2">阶段管理</h2>
      <p class="text-gray-600">查看和管理项目的阶段计划，跟踪各阶段的进度和状态</p>
    </div>

    <a-spin :spinning="loading">
      <!-- 工具栏 -->
      <div class="toolbar mb-4 flex justify-between items-center">
        <div class="search-area flex items-center space-x-4">
          <a-input-search v-model:value="searchText" placeholder="搜索阶段名称" style="width: 300px" @search="handleSearch" />
          <a-select v-model:value="statusFilter" placeholder="筛选状态" style="width: 150px" allow-clear @change="handleStatusFilter">
            <a-select-option value="completed">已完成</a-select-option>
            <a-select-option value="in_progress">进行中</a-select-option>
            <a-select-option value="pending">未开始</a-select-option>
            <a-select-option value="delayed">延期</a-select-option>
          </a-select>
        </div>
        <div class="actions">
          <a-space>
            <a-button type="primary" @click="handleAddStage">
              <template #icon><PlusOutlined /></template>
              添加阶段
            </a-button>
            <a-button @click="handleExport">
              <template #icon><ExportOutlined /></template>
              导出
            </a-button>
            <a-button @click="handleRefresh">
              <template #icon><ReloadOutlined /></template>
              刷新
            </a-button>
          </a-space>
        </div>
      </div>

      <!-- 阶段统计卡片 -->
      <div class="stage-stats grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
        <div class="stat-card bg-blue-50 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-blue-600">{{ stageStats.total }}</div>
          <div class="text-sm text-blue-600">总阶段数</div>
        </div>
        <div class="stat-card bg-green-50 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-green-600">{{ stageStats.completed }}</div>
          <div class="text-sm text-green-600">已完成</div>
        </div>
        <div class="stat-card bg-orange-50 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-orange-600">{{ stageStats.inProgress }}</div>
          <div class="text-sm text-orange-600">进行中</div>
        </div>
        <div class="stat-card bg-gray-50 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-gray-600">{{ stageStats.pending }}</div>
          <div class="text-sm text-gray-600">未开始</div>
        </div>
        <div class="stat-card bg-red-50 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-red-600">{{ stageStats.delayed }}</div>
          <div class="text-sm text-red-600">延期</div>
        </div>
      </div>

      <!-- 阶段表格 -->
      <div class="stage-table bg-white rounded-lg shadow-sm border">
        <a-table
          :columns="stageColumns"
          :data-source="filteredStages"
          :pagination="pagination"
          :scroll="{ x: 1500 }"
          row-key="id"
          :row-selection="{
            selectedRowKeys: selectedRowKeys,
            onChange: onSelectChange,
          }">
          <!-- 阶段名称列 -->
          <template #stageName="{ record }">
            <div class="flex items-center">
              <div class="stage-icon mr-2">
                <a-avatar :style="{ backgroundColor: getStageColor(record.stageType) }" :size="24">
                  <template #icon>
                    <component :is="getStageIcon(record.stageType)" />
                  </template>
                </a-avatar>
              </div>
              <div>
                <div class="font-medium">{{ record.stageName }}</div>
                <div class="text-sm text-gray-500">{{ record.stageCode }}</div>
              </div>
            </div>
          </template>

          <!-- 状态列 -->
          <template #status="{ record }">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>

          <!-- 进度列 -->
          <template #progress="{ record }">
            <div class="flex items-center">
              <a-progress
                :percent="record.completionRate"
                :show-info="false"
                size="small"
                class="flex-1 mr-2"
                :stroke-color="getProgressColor(record.completionRate)" />
              <span class="text-sm">{{ record.completionRate }}%</span>
            </div>
          </template>

          <!-- 时间列 -->
          <template #timeRange="{ record }">
            <div class="text-sm">
              <div>开始：{{ formatDate(record.planStartDate) }}</div>
              <div>结束：{{ formatDate(record.planEndDate) }}</div>
            </div>
          </template>

          <!-- 实际时间列 -->
          <template #actualTime="{ record }">
            <div class="text-sm">
              <div>开始：{{ formatDate(record.actualStartDate) }}</div>
              <div>结束：{{ formatDate(record.actualEndDate) }}</div>
            </div>
          </template>

          <!-- 评审状态列 -->
          <template #reviewStatus="{ record }">
            <a-tag v-if="record.reviewStatus" :color="getReviewStatusColor(record.reviewStatus)">
              {{ getReviewStatusText(record.reviewStatus) }}
            </a-tag>
            <span v-else class="text-gray-400">-</span>
          </template>

          <!-- 操作列 -->
          <template #action="{ record }">
            <a-space>
              <a-button type="link" size="small" @click="handleView(record)"> 查看 </a-button>
              <a-button type="link" size="small" @click="handleEdit(record)"> 编辑 </a-button>
              <a-dropdown>
                <a-button type="link" size="small"> 更多 <DownOutlined /> </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="handleCopy(record)">
                      <CopyOutlined />
                      复制
                    </a-menu-item>
                    <a-menu-item @click="handleStartStage(record)" :disabled="record.status === 'completed'">
                      <PlayCircleOutlined />
                      开始
                    </a-menu-item>
                    <a-menu-item @click="handleCompleteStage(record)" :disabled="record.status === 'completed'">
                      <CheckCircleOutlined />
                      完成
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item @click="handleDelete(record)" danger>
                      <DeleteOutlined />
                      删除
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </a-table>
      </div>
    </a-spin>

    <!-- 阶段详情抽屉 -->
    <a-modal v-model:open="drawerVisible" title="阶段详情" width="80%" :footer="null" :mask-closable="false">
      <div v-if="selectedStage" class="stage-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="阶段名称">
            {{ selectedStage.stageName }}
          </a-descriptions-item>
          <a-descriptions-item label="阶段编码">
            {{ selectedStage.stageCode }}
          </a-descriptions-item>
          <a-descriptions-item label="阶段类型">
            {{ getStageTypeText(selectedStage.stageType) }}
          </a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getStatusColor(selectedStage.status)">
              {{ getStatusText(selectedStage.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="计划开始">
            {{ formatDate(selectedStage.planStartDate) }}
          </a-descriptions-item>
          <a-descriptions-item label="计划结束">
            {{ formatDate(selectedStage.planEndDate) }}
          </a-descriptions-item>
          <a-descriptions-item label="实际开始">
            {{ formatDate(selectedStage.actualStartDate) }}
          </a-descriptions-item>
          <a-descriptions-item label="实际结束">
            {{ formatDate(selectedStage.actualEndDate) }}
          </a-descriptions-item>
          <a-descriptions-item label="完成率">
            <div class="flex items-center">
              <a-progress :percent="selectedStage.completionRate" :show-info="false" size="small" class="flex-1 mr-2" />
              <span>{{ selectedStage.completionRate }}%</span>
            </div>
          </a-descriptions-item>
          <a-descriptions-item label="评审状态">
            <a-tag v-if="selectedStage.reviewStatus" :color="getReviewStatusColor(selectedStage.reviewStatus)">
              {{ getReviewStatusText(selectedStage.reviewStatus) }}
            </a-tag>
            <span v-else class="text-gray-400">-</span>
          </a-descriptions-item>
          <a-descriptions-item label="工作包" :span="2">
            <a-tag v-for="workPackage in selectedStage.workPackages" :key="workPackage.id" class="mb-1">
              {{ workPackage.name }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="交付物" :span="2">
            <a-tag v-for="deliverable in selectedStage.deliverables" :key="deliverable.id" class="mb-1">
              {{ deliverable.name }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="阶段描述" :span="2">
            {{ selectedStage.description || '暂无描述' }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import dayjs from 'dayjs';
  import {
    PlusOutlined,
    ExportOutlined,
    ReloadOutlined,
    DownOutlined,
    CopyOutlined,
    PlayCircleOutlined,
    CheckCircleOutlined,
    DeleteOutlined,
    ProjectOutlined,
    CheckSquareOutlined,
    ClockCircleOutlined,
    WarningOutlined,
  } from '@ant-design/icons-vue';

  const { createMessage } = useMessage();

  // 响应式数据
  const loading = ref(false);
  const searchText = ref('');
  const statusFilter = ref('');
  const selectedRowKeys = ref([]);
  const drawerVisible = ref(false);
  const selectedStage = ref(null);

  // 阶段统计数据
  const stageStats = ref({
    total: 7,
    completed: 1,
    inProgress: 1,
    pending: 4,
    delayed: 1,
  });

  // 分页配置
  const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number) => `共 ${total} 条记录`,
  });

  // 阶段数据
  const stageList = ref([
    {
      id: '1',
      stageName: '项目开始',
      stageCode: 'STAGE_START',
      stageType: 'start',
      status: 'completed',
      planStartDate: '2025-03-01',
      planEndDate: '2025-03-07',
      actualStartDate: '2025-03-01',
      actualEndDate: '2025-03-07',
      completionRate: 100,
      reviewStatus: 'approved',
      workPackages: [
        { id: '1', name: '项目启动会' },
        { id: '2', name: '项目章程制定' },
      ],
      deliverables: [
        { id: '1', name: '项目启动文档' },
        { id: '2', name: '项目章程' },
      ],
      description: '项目启动阶段，完成项目的初始化工作',
    },
    {
      id: '2',
      stageName: '项目启动',
      stageCode: 'STAGE_INIT',
      stageType: 'process',
      status: 'delayed',
      planStartDate: '2025-03-08',
      planEndDate: '2025-03-22',
      actualStartDate: '2025-03-08',
      actualEndDate: null,
      completionRate: 0,
      reviewStatus: null,
      workPackages: [
        { id: '3', name: '确定项目目标和范围' },
        { id: '4', name: '资源分配' },
      ],
      deliverables: [
        { id: '3', name: '项目需求文档' },
        { id: '4', name: '资源分配计划' },
      ],
      description: '项目启动阶段，明确项目目标和范围',
    },
    {
      id: '3',
      stageName: '审计计划',
      stageCode: 'STAGE_AUDIT_PLAN',
      stageType: 'process',
      status: 'pending',
      planStartDate: '2025-03-23',
      planEndDate: '2025-04-05',
      actualStartDate: null,
      actualEndDate: null,
      completionRate: 0,
      reviewStatus: null,
      workPackages: [
        { id: '5', name: '制定审计计划' },
        { id: '6', name: '风险评估' },
      ],
      deliverables: [
        { id: '5', name: '审计计划书' },
        { id: '6', name: '风险评估报告' },
      ],
      description: '制定详细的审计计划和风险评估',
    },
    {
      id: '4',
      stageName: '现场审计',
      stageCode: 'STAGE_FIELD_AUDIT',
      stageType: 'process',
      status: 'pending',
      planStartDate: '2025-04-06',
      planEndDate: '2025-04-19',
      actualStartDate: null,
      actualEndDate: null,
      completionRate: 0,
      reviewStatus: null,
      workPackages: [
        { id: '7', name: '现场调研' },
        { id: '8', name: '数据收集' },
      ],
      deliverables: [
        { id: '7', name: '现场调研报告' },
        { id: '8', name: '审计工作底稿' },
      ],
      description: '实施现场审计工作，收集相关数据',
    },
    {
      id: '5',
      stageName: '报告编制',
      stageCode: 'STAGE_REPORT',
      stageType: 'process',
      status: 'pending',
      planStartDate: '2025-04-20',
      planEndDate: '2025-05-03',
      actualStartDate: null,
      actualEndDate: null,
      completionRate: 0,
      reviewStatus: null,
      workPackages: [
        { id: '9', name: '撰写审计报告' },
        { id: '10', name: '报告审核' },
      ],
      deliverables: [{ id: '9', name: '审计报告' }],
      description: '编制审计报告，总结审计结果',
    },
    {
      id: '6',
      stageName: '质量控制',
      stageCode: 'STAGE_QC',
      stageType: 'process',
      status: 'pending',
      planStartDate: '2025-05-04',
      planEndDate: '2025-05-17',
      actualStartDate: null,
      actualEndDate: null,
      completionRate: 0,
      reviewStatus: null,
      workPackages: [
        { id: '11', name: '质量检查' },
        { id: '12', name: '质量改进' },
      ],
      deliverables: [
        { id: '10', name: '质量检查报告' },
        { id: '11', name: '改进建议' },
      ],
      description: '进行质量控制，确保交付物质量',
    },
    {
      id: '7',
      stageName: '项目完成',
      stageCode: 'STAGE_END',
      stageType: 'end',
      status: 'pending',
      planStartDate: '2025-05-18',
      planEndDate: '2025-05-17',
      actualStartDate: null,
      actualEndDate: null,
      completionRate: 0,
      reviewStatus: null,
      workPackages: [{ id: '13', name: '项目收尾' }],
      deliverables: [{ id: '12', name: '项目总结报告' }],
      description: '项目收尾工作，完成项目交付',
    },
  ]);

  // 表格列配置
  const stageColumns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 60,
      customRender: ({ index }) => index + 1,
    },
    {
      title: '阶段名称',
      dataIndex: 'stageName',
      key: 'stageName',
      width: 200,
      slots: { customRender: 'stageName' },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      slots: { customRender: 'status' },
    },
    {
      title: '完成率',
      dataIndex: 'completionRate',
      key: 'completionRate',
      width: 120,
      slots: { customRender: 'progress' },
    },
    {
      title: '计划时间',
      dataIndex: 'timeRange',
      key: 'timeRange',
      width: 180,
      slots: { customRender: 'timeRange' },
    },
    {
      title: '实际时间',
      dataIndex: 'actualTime',
      key: 'actualTime',
      width: 180,
      slots: { customRender: 'actualTime' },
    },
    {
      title: '评审状态',
      dataIndex: 'reviewStatus',
      key: 'reviewStatus',
      width: 100,
      slots: { customRender: 'reviewStatus' },
    },
    {
      title: '工作包',
      dataIndex: 'workPackages',
      key: 'workPackages',
      width: 80,
      customRender: ({ record }) => record.workPackages?.length || 0,
    },
    {
      title: '交付物',
      dataIndex: 'deliverables',
      key: 'deliverables',
      width: 80,
      customRender: ({ record }) => record.deliverables?.length || 0,
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      fixed: 'right',
      slots: { customRender: 'action' },
    },
  ];

  // 计算属性
  const filteredStages = computed(() => {
    let result = stageList.value;

    // 搜索过滤
    if (searchText.value) {
      result = result.filter(
        stage =>
          stage.stageName.toLowerCase().includes(searchText.value.toLowerCase()) || stage.stageCode.toLowerCase().includes(searchText.value.toLowerCase()),
      );
    }

    // 状态过滤
    if (statusFilter.value) {
      result = result.filter(stage => stage.status === statusFilter.value);
    }

    return result;
  });

  onMounted(() => {
    loadStageData();
  });

  // 加载阶段数据
  const loadStageData = async () => {
    loading.value = true;
    try {
      // 这里调用实际的API
      // const result = await getStageList();
      // stageList.value = result.data;

      pagination.total = stageList.value.length;
    } catch (error) {
      console.error('加载阶段数据失败:', error);
      createMessage.error('加载阶段数据失败');
    } finally {
      loading.value = false;
    }
  };

  // 工具函数
  const getStageColor = (stageType: string) => {
    const colorMap = {
      start: '#52c41a',
      process: '#1890ff',
      end: '#722ed1',
    };
    return colorMap[stageType] || '#1890ff';
  };

  const getStageIcon = (stageType: string) => {
    const iconMap = {
      start: PlayCircleOutlined,
      process: ProjectOutlined,
      end: CheckCircleOutlined,
    };
    return iconMap[stageType] || ProjectOutlined;
  };

  const getStageTypeText = (stageType: string) => {
    const textMap = {
      start: '启动阶段',
      process: '执行阶段',
      end: '结束阶段',
    };
    return textMap[stageType] || '未知类型';
  };

  const getStatusColor = (status: string) => {
    const colorMap = {
      completed: 'green',
      in_progress: 'blue',
      pending: 'default',
      delayed: 'red',
    };
    return colorMap[status] || 'default';
  };

  const getStatusText = (status: string) => {
    const textMap = {
      completed: '已完成',
      in_progress: '进行中',
      pending: '未开始',
      delayed: '延期',
    };
    return textMap[status] || '未知';
  };

  const getProgressColor = (progress: number) => {
    if (progress === 100) return '#52c41a';
    if (progress >= 80) return '#1890ff';
    if (progress >= 60) return '#faad14';
    if (progress >= 40) return '#fa8c16';
    return '#ff4d4f';
  };

  const getReviewStatusColor = (reviewStatus: string) => {
    const colorMap = {
      approved: 'green',
      reviewing: 'blue',
      rejected: 'red',
    };
    return colorMap[reviewStatus] || 'default';
  };

  const getReviewStatusText = (reviewStatus: string) => {
    const textMap = {
      approved: '已通过',
      reviewing: '评审中',
      rejected: '已拒绝',
    };
    return textMap[reviewStatus] || '未知';
  };

  const formatDate = (date: string) => {
    return date ? dayjs(date).format('YYYY-MM-DD') : '-';
  };

  // 事件处理函数
  const handleSearch = () => {
    // 触发搜索
  };

  const handleStatusFilter = () => {
    // 触发状态过滤
  };

  const handleAddStage = () => {
    createMessage.info('添加阶段功能开发中...');
  };

  const handleExport = () => {
    createMessage.info('导出功能开发中...');
  };

  const handleRefresh = () => {
    loadStageData();
  };

  const handleView = (record: any) => {
    selectedStage.value = record;
    drawerVisible.value = true;
  };

  const handleEdit = (record: any) => {
    createMessage.info('编辑阶段功能开发中...');
  };

  const handleCopy = (record: any) => {
    createMessage.info('复制阶段功能开发中...');
  };

  const handleStartStage = (record: any) => {
    createMessage.info('开始阶段功能开发中...');
  };

  const handleCompleteStage = (record: any) => {
    createMessage.info('完成阶段功能开发中...');
  };

  const handleDelete = (record: any) => {
    createMessage.info('删除阶段功能开发中...');
  };

  const onSelectChange = (selectedKeys: string[]) => {
    selectedRowKeys.value = selectedKeys;
  };
</script>

<style scoped>
  .stage-page {
    background: var(--section-bg-color);
  }

  .stat-card {
    transition: all 0.3s ease;
  }

  .stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .stage-table {
    min-height: 400px;
  }

  .stage-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }
</style>
