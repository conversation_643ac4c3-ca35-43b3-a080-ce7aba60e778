package com.xinghuo.manhour.model.completion;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 工时填写情况查询参数
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@Schema(description = "工时填写情况查询参数")
public class WorkhourCompletionParams {

    @Schema(description = "时间类型：month/quarter/year/total")
    private String timeType;

    @Schema(description = "开始月份")
    private String startMonth;

    @Schema(description = "结束月份")
    private String endMonth;

    @Schema(description = "季度")
    private String quarter;

    @Schema(description = "年份")
    private String year;

    @Schema(description = "分部ID")
    private String fbId;

    @Schema(description = "填写状态")
    private String status;

    @Schema(description = "用户ID")
    private String userId;

    @Schema(description = "负责人ID")
    private String leaderId;
}
