<template>
  <div class="contract-paycontract-management">
    <div class="page-header">
      <div class="flex items-center">
        <i class="icon-ym icon-ym-paycontract mr-2 text-lg"></i>
        <span class="text-base font-medium">关联付款合同</span>
      </div>
      <div class="flex items-center space-x-2">
        <a-button type="primary" @click="handleAdd" :disabled="!hasContract">
          <template #icon><PlusOutlined /></template>
          新增付款合同
        </a-button>
        <a-button @click="handleRefresh">
          <template #icon><ReloadOutlined /></template>
          刷新
        </a-button>
      </div>
    </div>

    <!-- 无合同提示 -->
    <div v-if="!hasContract" class="no-contract-tip">
      <a-card class="no-contract-card">
        <a-empty image="https://gw.alipayobjects.com/zos/antfincdn/ZHrcdLPrvN/empty.svg" :image-style="{ height: '80px' }" description="">
          <template #description>
            <div class="empty-description">
              <h3>尚未创建收款合同</h3>
              <p>付款合同需要关联到收款合同，请先创建收款合同</p>
            </div>
          </template>
          <a-button type="primary" size="large" @click="goToContractInfo">
            <template #icon><PlusOutlined /></template>
            创建收款合同
          </a-button>
        </a-empty>
      </a-card>
    </div>

    <div class="page-content" v-if="hasContract">
      <a-spin :spinning="loading">
        <!-- 付款合同统计 -->
        <div class="paycontract-summary mb-4">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-card>
                <a-statistic title="外采金额" :value="statistics.totalAmount" :precision="2" suffix="元" :value-style="{ color: '#1890ff' }" />
              </a-card>
            </a-col>
            <a-col :span="6">
              <a-card>
                <a-statistic title="已付金额" :value="statistics.paidAmount" :precision="2" suffix="元" :value-style="{ color: '#52c41a' }" />
              </a-card>
            </a-col>
            <a-col :span="6">
              <a-card>
                <a-statistic title="未付金额" :value="statistics.unpaidAmount" :precision="2" suffix="元" :value-style="{ color: '#f5222d' }" />
              </a-card>
            </a-col>
            <a-col :span="6">
              <a-card>
                <a-statistic title="付款进度" :value="statistics.paymentProgress" suffix="%" :precision="1" :value-style="{ color: '#faad14' }" />
              </a-card>
            </a-col>
          </a-row>
        </div>

        <!-- 付款合同列表 -->
        <div class="paycontract-list">
          <a-card title="付款合同列表">
            <a-table :columns="columns" :data-source="paycontractList" :pagination="pagination" :loading="loading" row-key="id">
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'amount'">
                  <span class="text-blue-600 font-medium"> ¥{{ formatMoney(record.amount) }} </span>
                </template>
                <template v-else-if="column.key === 'yfAmount'">
                  <span class="text-green-600 font-medium"> ¥{{ formatMoney(record.yfAmount) }} </span>
                </template>
                <template v-else-if="column.key === 'moneyStatus'">
                  <a-tag :color="getStatusColor(record.moneyStatus)">
                    {{ getStatusText(record.moneyStatus) }}
                  </a-tag>
                </template>
                <template v-else-if="column.key === 'signDate'">
                  {{ formatDate(record.signDate) }}
                </template>
                <template v-else-if="column.key === 'action'">
                  <a-space>
                    <a-button type="link" size="small" @click="handleView(record)"> 查看 </a-button>
                    <a-button type="link" size="small" @click="handleEdit(record)"> 编辑 </a-button>
                    <a-popconfirm title="确定要删除这个付款合同吗？" @confirm="handleDelete(record)">
                      <a-button type="link" size="small" danger> 删除 </a-button>
                    </a-popconfirm>
                  </a-space>
                </template>
              </template>
            </a-table>
          </a-card>
        </div>
      </a-spin>
    </div>

    <!-- 新增/编辑表单抽屉 -->
    <PaycontractForm @register="registerFormDrawer" @success="handleFormSuccess" />

    <!-- 详情查看模态框 -->
    <PaycontractDetail v-model:visible="detailVisible" :record="currentRecord" @cancel="handleDetailCancel" />
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, inject, reactive } from 'vue';
  import { PlusOutlined, ReloadOutlined } from '@ant-design/icons-vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { formatToDate } from '/@/utils/dateUtil';
  import paymentContractApi, { type PaymentContractVO, type PaymentContractForm } from '/@/api/project/paymentContract';
  import { useDrawer } from '/@/components/Drawer';

  // Import form and detail components
  import PaycontractForm from './components/PaycontractForm.vue';
  import PaycontractDetail from './components/PaycontractDetail.vue';

  // 从父组件注入项目ID和合同信息
  const projectId = inject('projectId', ref(''));
  const contractId = inject('contractId', ref(''));
  const contractInfo = inject('contractInfo', ref(null));
  const hasContract = inject('hasContract', ref(false));

  const { createMessage } = useMessage();

  const loading = ref(false);
  const paycontractList = ref<PaymentContractVO[]>([]);
  const pagination = ref({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
  });

  // Form and modal states
  const detailVisible = ref(false);
  const currentRecord = ref<PaymentContractVO | null>(null);

  // 使用框架抽屉组件
  const [registerFormDrawer, { openDrawer: openFormDrawer }] = useDrawer();

  // Statistics
  const statistics = ref({
    totalAmount: 0,
    paidAmount: 0,
    unpaidAmount: 0,
    paymentProgress: 0,
  });

  // 表格列定义 - 采购合同字段
  const columns = [
    {
      title: '采购合同名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
    },
    {
      title: '采购合同编号',
      dataIndex: 'cno',
      key: 'cno',
      width: 150,
    },
    {
      title: '供应商',
      dataIndex: 'suppilerName',
      key: 'suppilerName',
      width: 150,
    },
    {
      title: '负责人',
      dataIndex: 'ownName',
      key: 'ownName',
      width: 100,
    },
    {
      title: '合同金额',
      dataIndex: 'amount',
      key: 'amount',
      width: 120,
      customRender: ({ text }) => `¥${text?.toLocaleString() || 0}`,
    },
    {
      title: '已付金额',
      dataIndex: 'yfAmount',
      key: 'yfAmount',
      width: 120,
      customRender: ({ text }) => `¥${text?.toLocaleString() || 0}`,
    },
    {
      title: '合同状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      customRender: ({ text }) => getContractStatusText(text),
    },
    {
      title: '签订日期',
      dataIndex: 'signDate',
      key: 'signDate',
      width: 120,
      customRender: ({ text }) => formatToDate(text),
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      fixed: 'right',
    },
  ];

  // 格式化金额
  const formatMoney = (amount: number | string) => {
    if (!amount) return '0.00';
    return Number(amount).toLocaleString('zh-CN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  };

  // 格式化日期
  const formatDate = (date: string) => {
    return date ? formatToDate(date) : '-';
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      '1': 'orange',
      '2': 'blue',
      '3': 'green',
      '4': 'red',
    };
    return colorMap[status] || 'default';
  };

  // 获取合同状态文本
  const getContractStatusText = (status: string) => {
    const textMap: Record<string, string> = {
      draft: '草稿',
      signed: '已签订',
      executing: '执行中',
      completed: '已完成',
      cancelled: '已取消',
      suspended: '已暂停',
    };
    return textMap[status] || '未知';
  };

  // 获取付款状态文本
  const getPayStatusText = (status: string) => {
    const textMap: Record<string, string> = {
      UNPAID: '未付款',
      PARTIAL_PAID: '部分付款',
      FULLY_PAID: '已付清',
      OVERDUE: '逾期',
      PENDING: '待付款',
      PROCESSING: '付款中',
      CANCELLED: '已取消',
    };
    return textMap[status] || '未知';
  };

  // 加载关联的采购合同列表
  const loadPaycontractList = async () => {
    if (!contractId.value) {
      console.log('⚠️ 合同ID为空，无法加载关联的采购合同');
      paycontractList.value = [];
      pagination.value.total = 0;
      return;
    }

    loading.value = true;
    try {
      console.log('加载关联的采购合同，合同ID:', contractId.value);

      // 调用真实API获取与销售合同关联的采购合同
      const response = await paymentContractApi.getByContractId(contractId.value);
      if (response.code === 200) {
        paycontractList.value = response.data || [];
        pagination.value.total = response.data?.length || 0;
        console.log('✅ 关联采购合同加载成功:', paycontractList.value.length, '条记录');

        // 计算统计数据
        calculateStatistics();
      } else {
        createMessage.error(response.msg || '加载关联采购合同失败');
        paycontractList.value = [];
        pagination.value.total = 0;
      }
    } catch (error) {
      console.error('加载关联采购合同失败:', error);
      createMessage.error('加载关联采购合同失败');
      paycontractList.value = [];
      pagination.value.total = 0;
    } finally {
      loading.value = false;
    }
  };

  // 计算统计数据
  const calculateStatistics = () => {
    const list = paycontractList.value;
    statistics.value.totalAmount = list.reduce((sum, item) => sum + (item.amount || 0), 0);
    statistics.value.paidAmount = list.reduce((sum, item) => sum + (item.yfAmount || 0), 0);
    statistics.value.unpaidAmount = statistics.value.totalAmount - statistics.value.paidAmount;
    statistics.value.paymentProgress = statistics.value.totalAmount > 0 ? (statistics.value.paidAmount / statistics.value.totalAmount) * 100 : 0;
  };

  // 新增付款合同
  const handleAdd = () => {
    if (!contractId.value) {
      createMessage.warning({
        content: '当前项目尚未创建收款合同，请先创建收款合同',
        duration: 3,
      });
      return;
    }
    openFormDrawer(true, {
      isUpdate: false,
      contractId: contractId.value,
      contractInfo: contractInfo.value,
      record: null,
    });
  };

  // 查看付款合同
  const handleView = (record: PaymentContractVO) => {
    currentRecord.value = record;
    detailVisible.value = true;
  };

  // 编辑付款合同
  const handleEdit = (record: PaymentContractVO) => {
    openFormDrawer(true, {
      isUpdate: true,
      contractId: contractId.value,
      contractInfo: contractInfo.value,
      record: record,
    });
  };

  // 删除付款合同
  const handleDelete = async (record: PaymentContractVO) => {
    try {
      const response = await paymentContractApi.delete(record.id);
      if (response.code === 200) {
        createMessage.success('删除成功');
        loadPaycontractList();
      } else {
        createMessage.error(response.msg || '删除失败');
      }
    } catch (error) {
      console.error('删除付款合同失败:', error);
      createMessage.error('删除失败');
    }
  };

  // 表单提交成功回调
  const handleFormSuccess = () => {
    loadPaycontractList();
  };

  // 关闭详情模态框
  const handleDetailCancel = () => {
    detailVisible.value = false;
    currentRecord.value = null;
  };

  // 跳转到合同信息页面
  const goToContractInfo = () => {
    // 发送事件给父组件，让父组件切换到合同信息页面
    // 这样可以保持在同一个项目概览页面内，只是切换标签页
    const event = new CustomEvent('switchToContractInfo', {
      bubbles: true,
      detail: { targetTab: 'contractManagement', targetSubTab: 'info' },
    });
    document.dispatchEvent(event);

    // 如果上面的事件方式不工作，使用简单的提示
    createMessage.info('请点击左侧菜单中的"合同信息"来创建收款合同');
  };

  // 刷新
  const handleRefresh = () => {
    loadPaycontractList();
  };

  onMounted(() => {
    loadPaycontractList();
  });
</script>

<style lang="less" scoped>
  .contract-paycontract-management {
    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding: 16px;
      background: #fff;
      border-radius: 6px;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    }

    .no-contract-tip {
      margin-bottom: 16px;

      .no-contract-card {
        text-align: center;
        padding: 40px 20px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);

        .empty-description {
          margin-bottom: 24px;

          h3 {
            color: #262626;
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 8px;
          }

          p {
            color: #8c8c8c;
            font-size: 14px;
            margin: 0;
          }
        }
      }
    }

    .page-content {
      .paycontract-summary {
        .ant-card {
          text-align: center;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
        }
      }

      .paycontract-list {
        .ant-card {
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
        }
      }
    }
  }
</style>
