package com.xinghuo.project.template.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 交付物计划模板主表实体类
 * 对应数据库表：zz_proj_tpl_workproduct_plan
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("zz_proj_tpl_workproduct_plan")
public class WorkProductPlanTemplateEntity extends BaseEntityV2.CUBaseEntityV2<String> {

    /**
     * 模板名称
     */
    @TableField("name")
    private String name;

    /**
     * 模板描述
     */
    @TableField("description")
    private String description;

    /**
     * 知识状态ID (关联字典表, 如: 未发布, 已发布)
     */
    @TableField("kn_status_id")
    private String knStatusId;
}
