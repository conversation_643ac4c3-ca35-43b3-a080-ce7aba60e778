import { defHttp } from '/@/utils/http/axios';

/**
 * 商机变更日志API
 */

// API URL前缀
const API_PREFIX = '/api/project/business/datalog';

/**
 * 商机变更日志详情对象接口
 */
export interface BusinessDatalogDetailModel {
  id: string;
  changeId: string;
  datatype: string;
  label: string;
  oldValue: string;
  newValue: string;
  createUserId?: string;
  createUserName?: string;
  createTime?: string;
}

/**
 * 商机变更日志对象接口
 */
export interface BusinessDatalogModel {
  id: string;
  relateId: string;
  projectName?: string;
  xmjlName?: string;
  type: string;
  changeLog?: string;
  note?: string;
  createUserId?: string;
  createUserName?: string;
  createTime?: string;
  details?: BusinessDatalogDetailModel[];
}

/**
 * 根据商机ID获取变更日志列表
 * @param businessId 商机ID
 * @returns 变更日志列表
 */
export const getBusinessDatalogListByBusinessId = (businessId: string) => {
  return defHttp.get<BusinessDatalogModel[]>({
    url: `${API_PREFIX}/business/${businessId}`,
  });
};

/**
 * 获取变更日志详情
 * @param id 变更日志ID
 * @returns 变更日志详情
 */
export const getBusinessDatalogInfo = (id: string) => {
  return defHttp.get<BusinessDatalogModel>({
    url: `${API_PREFIX}/${id}`,
  });
};
