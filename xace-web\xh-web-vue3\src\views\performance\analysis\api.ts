import { defHttp } from '/@/utils/http/axios';

const API_PREFIX = '/api/performance/analysis';

/**
 * 绩效分析查询参数接口
 */
export interface PerformanceAnalysisParams {
  dateRange?: string[]; // 时间范围
  fbId?: string; // 分部ID
  scoreRange?: number[]; // 得分范围
  startMonth?: string; // 开始月份
  endMonth?: string; // 结束月份
  userId?: string; // 员工ID
}

/**
 * 绩效分析概览数据接口
 */
export interface PerformanceAnalysisOverview {
  totalUsers: number; // 总参与人数
  avgScore: number; // 平均绩效得分
  excellentRate: number; // 优秀率
  completedUsers: number; // 完成评分人数
}

/**
 * 个人绩效分析数据接口
 */
export interface PersonalPerformanceModel {
  userId: string;
  userName: string;
  fbName: string;
  totalScore: number;
  ranking: number;
  status: string;
  period: string;
  dimensionScores: string;
}

/**
 * 分部绩效分析数据接口
 */
export interface DepartmentPerformanceModel {
  fbId: string;
  fbName: string;
  userCount: number;
  avgScore: number;
  excellentRate: number;
  completionRate: number;
}

/**
 * 绩效排名数据接口
 */
export interface PerformanceRankingModel {
  ranking: number;
  userId: string;
  userName: string;
  fbName: string;
  totalScore: number;
  dimensionScores: string;
}

/**
 * 图表数据接口
 */
export interface PerformanceChartDataModel {
  scoreDistribution: Array<{ scoreRange: string; count: number }>;
  departmentComparison: Array<{ fbName: string; avgScore: number }>;
  trend: Array<{ period: string; avgScore: number }>;
  dimension: Array<{ dimensionName: string; avgScore: number }>;
}

/**
 * 格式化查询参数
 */
function formatParams(params?: PerformanceAnalysisParams) {
  if (!params) return {};

  const formatted = { ...params };

  // 处理日期范围
  if (params.dateRange && params.dateRange.length === 2) {
    formatted.startMonth = params.dateRange[0];
    formatted.endMonth = params.dateRange[1];
    delete formatted.dateRange;
  }

  // 处理得分范围
  if (params.scoreRange && params.scoreRange.length === 2) {
    formatted.minScore = params.scoreRange[0];
    formatted.maxScore = params.scoreRange[1];
    delete formatted.scoreRange;
  }

  return formatted;
}

/**
 * 获取绩效分析概览数据
 * @param params 查询参数
 * @returns 概览数据
 */
export const getPerformanceAnalysisOverview = (params?: PerformanceAnalysisParams) => {
  return defHttp.get<PerformanceAnalysisOverview>({
    url: `${API_PREFIX}/overview`,
    params: formatParams(params),
  });
};

/**
 * 获取绩效分析图表数据
 * @param params 查询参数
 * @returns 图表数据
 */
export const getPerformanceAnalysisCharts = (params?: PerformanceAnalysisParams) => {
  return defHttp.get<PerformanceChartDataModel>({
    url: `${API_PREFIX}/charts`,
    params: formatParams(params),
  });
};

/**
 * 获取个人绩效分析列表
 * @param params 查询参数
 * @returns 个人绩效分析数据列表
 */
export const getPersonalPerformanceList = (params?: PerformanceAnalysisParams) => {
  return defHttp.post<{ list: PersonalPerformanceModel[]; total: number }>({
    url: `${API_PREFIX}/personal/list`,
    data: formatParams(params),
  });
};

/**
 * 获取分部绩效分析列表
 * @param params 查询参数
 * @returns 分部绩效分析数据列表
 */
export const getDepartmentPerformanceList = (params?: PerformanceAnalysisParams) => {
  return defHttp.post<{ list: DepartmentPerformanceModel[]; total: number }>({
    url: `${API_PREFIX}/department/list`,
    data: formatParams(params),
  });
};

/**
 * 获取绩效排名列表
 * @param params 查询参数
 * @returns 绩效排名数据列表
 */
export const getPerformanceRankingList = (params?: PerformanceAnalysisParams) => {
  return defHttp.post<{ list: PerformanceRankingModel[]; total: number }>({
    url: `${API_PREFIX}/ranking/list`,
    data: formatParams(params),
  });
};

/**
 * 导出绩效分析报表
 * @param params 查询参数
 * @returns 导出结果
 */
export const exportPerformanceAnalysis = (params?: PerformanceAnalysisParams) => {
  return defHttp.post({
    url: `${API_PREFIX}/export`,
    data: formatParams(params),
    responseType: 'blob',
  });
};

/**
 * 获取绩效详情
 * @param userId 用户ID
 * @param params 查询参数
 * @returns 绩效详情
 */
export const getPerformanceDetail = (userId: string, params?: PerformanceAnalysisParams) => {
  return defHttp.get({
    url: `${API_PREFIX}/personal/${userId}/detail`,
    params: formatParams(params),
  });
};

/**
 * 获取分部绩效详情
 * @param fbId 分部ID
 * @param params 查询参数
 * @returns 分部绩效详情
 */
export const getDepartmentPerformanceDetail = (fbId: string, params?: PerformanceAnalysisParams) => {
  return defHttp.get({
    url: `${API_PREFIX}/department/${fbId}/detail`,
    params: formatParams(params),
  });
};

/**
 * 获取绩效维度统计
 * @param params 查询参数
 * @returns 维度统计数据
 */
export const getPerformanceDimensionStats = (params?: PerformanceAnalysisParams) => {
  return defHttp.get({
    url: `${API_PREFIX}/dimension/stats`,
    params: formatParams(params),
  });
};

/**
 * 获取绩效趋势数据
 * @param params 查询参数
 * @returns 趋势数据
 */
export const getPerformanceTrend = (params?: PerformanceAnalysisParams) => {
  return defHttp.get({
    url: `${API_PREFIX}/trend`,
    params: formatParams(params),
  });
};
