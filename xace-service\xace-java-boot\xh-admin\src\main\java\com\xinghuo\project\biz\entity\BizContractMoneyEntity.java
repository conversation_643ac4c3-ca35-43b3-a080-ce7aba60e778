package com.xinghuo.project.biz.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目合同收款实体类
 */
@Data
@EqualsAndHashCode
@TableName("zz_proj_contract_money_v2")
public class BizContractMoneyEntity extends BaseEntityV2.IBaseEntityV2 {

    /**
     * 基础项目ID  关联： ProjectBaseEntity
     */
    @TableField("proj_base_id")
    private String projBaseId;

    /**
     * 合同ID 关联合同表 逐渐ID
     */
    @TableField("contract_id")
    private String contractId;

    /**
     * 付款条件
     */
    @TableField("fktj")
    private String fktj;

    /**
     * 比例
     */
    @TableField("ratio")
    private String ratio;

    /**
     * 收款金额
     */
    @TableField("cm_money")
    private BigDecimal cmMoney;

    /**
     * 项目经理
     */
    @TableField("own_id")
    private Long ownId;

    /**
     * 支付状态
     */
    @TableField("pay_status")
    private Integer payStatus;

    /**
     * 开票日期
     */
    @TableField("kaipiao_date")
    private Date kaipiaoDate;

    /**
     * 应收日期
     */
    @TableField("yingshou_date")
    private Date yingshouDate;

    /**
     * 预收日期
     */
    @TableField("yushou_date")
    private Date yushouDate;

    /**
     * 收款日期
     */
    @TableField("shoukuan_date")
    private Date shoukuanDate;

    /**
     * 临时保存预收日期
     */
    @TableField("tmp_date")
    private Date tmpDate;

    /**
     * 最后备注
     */
    @TableField("last_note")
    private String lastNote;

    /**
     * 备注
     */
    @TableField("note")
    private String note;

    /**
     * 部门ID
     */
    @TableField("dept_id")
    private String deptId;

    /**
     * 创建用户
     */
    @TableField("create_user_id")
    private String createUserId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 最后修改人
     */
    @TableField("last_modified_user_id")
    private String lastModifiedUserId;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 一部金额
     */
    @TableField("yb_amount")
    private BigDecimal ybAmount;

    /**
     * 二部金额
     */
    @TableField("eb_amount")
    private BigDecimal ebAmount;

    /**
     * 综合金额
     */
    @TableField("other_amount")
    private BigDecimal otherAmount;

    /**
     * 租户ID
     */
    @TableField("f_tenantid")
    private String tenantId;

    /**
     * 流程ID
     */
    @TableField("f_flowid")
    private String flowId;
}
