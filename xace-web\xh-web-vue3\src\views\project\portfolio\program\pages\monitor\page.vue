<template>
  <div class="xh-common-layout">
    <a-card title="项目监控" :bordered="false">
      <p>请选择具体的监控模块：</p>
      <a-space>
        <a-button type="primary" @click="$emit('menuChange', { enCode: 'monitor/projectList' })"> 项目列表 </a-button>
        <a-button type="primary" @click="$emit('menuChange', { enCode: 'monitor/projectHealth' })"> 项目健康状态 </a-button>
        <a-button type="primary" @click="$emit('menuChange', { enCode: 'monitor/projectPhase' })"> 项目阶段 </a-button>
        <a-button type="primary" @click="$emit('menuChange', { enCode: 'monitor/projectPlan' })"> 项目计划 </a-button>
      </a-space>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  defineOptions({ name: 'ProgramMonitor' });

  defineEmits(['menuChange']);
</script>
