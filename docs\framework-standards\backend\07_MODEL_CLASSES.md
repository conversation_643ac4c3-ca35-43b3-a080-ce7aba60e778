# Model 类规范

## 简化设计原则

XACE项目采用简化的Model类设计，主要包含三种类型：
- **VO (View Object)**：视图对象，用于数据展示
- **Form**：表单对象，用于接收前端数据，同时支持创建和更新
- **Pagination**：分页查询对象，继承基础分页类

## 包结构规范

```
com.xinghuo.[模块名].model.[业务功能]/
│   ├── [Business]VO.java
│   └── [Business]ListVO.java
│   └── [Business]Form.java
    └── [Business]Pagination.java    # 分页查询对象
```

### 命名规范
- **VO对象**：以 `VO` 结尾，如 `UserVO`
- **Form对象**：以 `Form` 结尾，如 `UserForm`  
- **分页对象**：以 `Pagination` 结尾，如 `UserPagination`（导入：import com.xinghuo.common.base.model.Pagination）

## 模型类型定义

### 1. VO (View Object) - 视图对象

用于向前端返回数据，包含业务字段和系统字段。

```java
import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

@Data
@Schema(description = "用户视图对象")
public class UserVO {
    
    @Schema(description = "用户ID")
    private String id;
    
    @Schema(description = "用户账号")
    private String account;
    
    @Schema(description = "用户姓名")
    private String fullName;
    
    @Schema(description = "邮箱")
    private String email;
    
    @Schema(description = "状态名称")
    private String statusName;
    
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;
}
```

### 2. Form - 表单对象

推荐使用**合并Form设计**，同时支持创建和更新操作：

```java
import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Email;

@Data
@Schema(description = "用户表单对象")
public class UserForm {
    
    @Schema(description = "用户ID，新增时为空，更新时必填")
    private String id;
    
    @Schema(description = "用户账号")
    @NotBlank(message = "用户账号不能为空")
    private String account;
    
    @Schema(description = "用户姓名")
    @NotBlank(message = "用户姓名不能为空")
    private String fullName;
    
    @Schema(description = "邮箱")
    @Email(message = "邮箱格式不正确")
    private String email;
    
    @Schema(description = "密码，新增时必填，更新时选填")
    private String password;
    
    @Schema(description = "启用状态")
    private Integer enabledMark;
}
```

**使用说明**：
- **新增时**：id为空，Controller层自动生成
- **更新时**：id必填，用于定位要更新的记录
- **密码字段**：可在Service层根据是否为空判断是否更新密码

### 3. Pagination - 分页查询对象

继承基础分页类，添加业务查询条件：

```java
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import com.xinghuo.common.base.model.Pagination;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "用户分页查询")
public class UserPagination extends Pagination {
    
    @Schema(description = "用户账号")
    private String account;
    
    @Schema(description = "用户姓名")
    private String fullName;
    
    @Schema(description = "用户状态")
    private Integer enabledMark;
}
```

## 基础注解规范

### 必需导入
```java
// 基础注解
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

// 验证注解（Form对象）
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Email;

// 日期格式化（VO对象）
import com.fasterxml.jackson.annotation.JsonFormat;

// 分页基类（Pagination对象）
import com.xinghuo.common.base.model.Pagination;
```

### 常用验证注解
- `@NotBlank`：字符串不能为空
- `@NotNull`：对象不能为null
- `@Email`：邮箱格式验证
- `@Pattern`：正则表达式验证

## 数据转换规范

### Entity ↔ VO/Form 转换

使用 `BeanCopierUtils` 进行对象转换：

```java
// Entity → VO
UserEntity entity = userService.getById(id);
UserVO userVO = BeanCopierUtils.copy(entity, UserVO.class);

// Form → Entity  
UserForm form = new UserForm();
UserEntity entity = BeanCopierUtils.copy(form, UserEntity.class);

// 列表转换
List<UserEntity> entities = userService.list();
List<UserVO> userVOs = BeanCopierUtils.copyList(entities, UserVO.class);
```

### 注意事项
- BaseEntityV2字段映射：使用 `getCreatedAt()`，不是 `getCreateTime()`
- 日期格式：VO中使用 `@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")`
- 状态转换：在转换后手动设置状态名称等业务字段

## 最佳实践

### 1. 字段定义原则
- **VO对象**：包含所有需要展示的字段，包括关联对象的名称字段
- **Form对象**：只包含可编辑的业务字段，不包含系统字段
- **Pagination对象**：只包含查询条件字段，继承分页基类

### 2. 嵌套对象处理
```java
// ✅ 正确：使用扁平化设计
@Schema(description = "部门名称")
private String departmentName;

// ❌ 避免：复杂嵌套对象
private DepartmentVO department;
```

### 3. 状态字段处理
```java
// Entity中的状态字段
private Integer status;

// VO中增加状态名称字段
private String statusName;

// 在转换后设置状态名称
userVO.setStatusName(userVO.getStatus() == 1 ? "启用" : "禁用");
```

## 开发检查清单

**Model类设计：**
- [ ] 使用正确的包结构和命名规范
- [ ] VO对象包含 @JsonFormat 日期格式化
- [ ] Form对象包含完整的验证注解
- [ ] Pagination对象继承基础分页类
- [ ] 使用 BeanCopierUtils 进行对象转换
- [ ] 避免复杂嵌套对象，使用扁平化设计

**Jakarta EE规范：**
- [ ] 验证注解使用 `jakarta.validation.*`
- [ ] 不使用 `javax.*` 包

遵循以上规范，可以构建出简洁、统一、易维护的Model类体系。