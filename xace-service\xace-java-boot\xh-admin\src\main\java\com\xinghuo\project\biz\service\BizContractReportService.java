package com.xinghuo.project.biz.service;

import com.xinghuo.project.biz.model.report.DashboardDataVO;
import com.xinghuo.project.biz.model.report.PaymentTrendVO;
import com.xinghuo.project.biz.model.report.ContractStatusVO;
import com.xinghuo.project.biz.model.report.DepartmentRankVO;
import com.xinghuo.project.biz.model.report.ConversionFunnelVO;

import java.util.List;
import java.util.Map;

/**
 * 合同报表服务接口
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface BizContractReportService {

    /**
     * 获取仪表板概览数据
     *
     * @param params 查询参数
     * @return 仪表板数据
     */
    DashboardDataVO getDashboardData(Map<String, Object> params);

    /**
     * 获取收款趋势数据
     *
     * @param params 查询参数
     * @return 收款趋势数据
     */
    PaymentTrendVO getPaymentTrend(Map<String, Object> params);

    /**
     * 获取合同状态分布数据
     *
     * @param params 查询参数
     * @return 合同状态分布数据
     */
    List<ContractStatusVO> getContractStatus(Map<String, Object> params);

    /**
     * 获取部门收款排行数据
     *
     * @param params 查询参数
     * @return 部门收款排行数据
     */
    DepartmentRankVO getDepartmentRank(Map<String, Object> params);

    /**
     * 获取商机转化漏斗数据
     *
     * @param params 查询参数
     * @return 商机转化漏斗数据
     */
    List<ConversionFunnelVO> getConversionFunnel(Map<String, Object> params);

    /**
     * 获取合同分析数据
     *
     * @param projectId 项目ID
     * @param contractId 合同ID
     * @param params 查询参数
     * @return 合同分析数据
     */
    Map<String, Object> getContractAnalysis(String projectId, String contractId, Map<String, Object> params);

    /**
     * 获取合同月度趋势数据
     *
     * @param contractId 合同ID
     * @return 月度趋势数据
     */
    Map<String, Object> getContractMonthlyTrend(String contractId);

    /**
     * 获取合同执行状态数据
     *
     * @param contractId 合同ID
     * @return 执行状态数据
     */
    List<Map<String, Object>> getContractExecutionStatus(String contractId);

    /**
     * 获取合同风险提醒数据
     *
     * @param contractId 合同ID
     * @return 风险提醒数据
     */
    List<Map<String, Object>> getContractRiskAlerts(String contractId);
}
