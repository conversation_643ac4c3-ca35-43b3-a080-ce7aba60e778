<template>
  <BasicModal v-model:visible="modalVisible" title="付款合同详情" :width="900" :footer="null" @cancel="handleCancel">
    <div v-if="record" class="paycontract-detail">
      <!-- 基本信息 -->
      <a-card title="基本信息" class="mb-4">
        <a-row :gutter="24">
          <a-col :span="8">
            <div class="detail-item">
              <label class="detail-label">合同名称：</label>
              <span class="detail-value">{{ record.name || '-' }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="detail-item">
              <label class="detail-label">合同编号：</label>
              <span class="detail-value">{{ record.cno || '-' }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="detail-item">
              <label class="detail-label">合同状态：</label>
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </div>
          </a-col>
        </a-row>

        <a-row :gutter="24" class="mt-3">
          <a-col :span="8">
            <div class="detail-item">
              <label class="detail-label">供应商：</label>
              <span class="detail-value">{{ record.suppilerName || '-' }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="detail-item">
              <label class="detail-label">负责人：</label>
              <span class="detail-value">{{ record.ownName || '-' }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="detail-item">
              <label class="detail-label">关联收款合同：</label>
              <span class="detail-value">{{ record.contractName || '-' }}</span>
            </div>
          </a-col>
        </a-row>
      </a-card>

      <!-- 金额信息 -->
      <a-card title="金额信息" class="mb-4">
        <a-row :gutter="24">
          <a-col :span="6">
            <a-statistic title="合同金额" :value="record.amount || 0" :precision="2" suffix="元" :value-style="{ color: '#1890ff', fontSize: '20px' }" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="已付金额" :value="record.yfAmount || 0" :precision="2" suffix="元" :value-style="{ color: '#52c41a', fontSize: '20px' }" />
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="未付金额"
              :value="(record.amount || 0) - (record.yfAmount || 0)"
              :precision="2"
              suffix="元"
              :value-style="{ color: '#f5222d', fontSize: '20px' }" />
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="付款进度"
              :value="record.amount && record.amount > 0 ? ((record.yfAmount || 0) / record.amount) * 100 : 0"
              :precision="1"
              suffix="%"
              :value-style="{ color: '#faad14', fontSize: '20px' }" />
          </a-col>
        </a-row>
      </a-card>

      <!-- 部门分配 -->
      <a-card title="部门分配" class="mb-4">
        <a-row :gutter="24">
          <a-col :span="8">
            <div class="detail-item">
              <label class="detail-label">开发一部金额：</label>
              <span class="detail-value amount-text"> ¥{{ formatMoney(record.kfybAmount || 0) }} </span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="detail-item">
              <label class="detail-label">开发二部金额：</label>
              <span class="detail-value amount-text"> ¥{{ formatMoney(record.kfebAmount || 0) }} </span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="detail-item">
              <label class="detail-label">综合金额：</label>
              <span class="detail-value amount-text"> ¥{{ formatMoney(record.otherAmount || 0) }} </span>
            </div>
          </a-col>
        </a-row>
      </a-card>

      <!-- 日期信息 -->
      <a-card title="日期信息" class="mb-4">
        <a-row :gutter="24">
          <a-col :span="8">
            <div class="detail-item">
              <label class="detail-label">签订日期：</label>
              <span class="detail-value">{{ formatDate(record.signDate) }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="detail-item">
              <label class="detail-label">预计签订日期：</label>
              <span class="detail-value">{{ formatDate(record.estSignDate) }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="detail-item">
              <label class="detail-label">签署年份：</label>
              <span class="detail-value">{{ record.signYear || '-' }}</span>
            </div>
          </a-col>
        </a-row>
      </a-card>

      <!-- 其他信息 -->
      <a-card title="其他信息">
        <a-row :gutter="24">
          <a-col :span="24">
            <div class="detail-item">
              <label class="detail-label">备注：</label>
              <div class="detail-value note-content">
                {{ record.note || '暂无备注' }}
              </div>
            </div>
          </a-col>
        </a-row>

        <a-row :gutter="24" class="mt-3">
          <a-col :span="12">
            <div class="detail-item">
              <label class="detail-label">创建时间：</label>
              <span class="detail-value">{{ formatDateTime(record.createTime) }}</span>
            </div>
          </a-col>
          <a-col :span="12">
            <div class="detail-item">
              <label class="detail-label">更新时间：</label>
              <span class="detail-value">{{ formatDateTime(record.updateTime) }}</span>
            </div>
          </a-col>
        </a-row>
      </a-card>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { computed } from 'vue';
  import { formatToDate, formatToDateTime } from '/@/utils/dateUtil';
  import type { PaymentContractVO } from '/@/api/project/paymentContract';
  import { BasicModal } from '/@/components/Modal';

  // Props定义
  interface Props {
    visible: boolean;
    record?: PaymentContractVO | null;
  }

  // Props
  const props = withDefaults(defineProps<Props>(), {
    visible: false,
    record: null,
  });

  // Emits
  const emit = defineEmits<{
    'update:visible': [value: boolean];
    cancel: [];
  }>();

  // 模态框可见性
  const modalVisible = computed({
    get: () => props.visible,
    set: value => emit('update:visible', value),
  });

  // 格式化金额
  const formatMoney = (amount: number | string) => {
    if (!amount) return '0.00';
    return Number(amount).toLocaleString('zh-CN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  };

  // 格式化日期
  const formatDate = (date: string) => {
    return date ? formatToDate(date) : '-';
  };

  // 格式化日期时间
  const formatDateTime = (dateTime: string) => {
    return dateTime ? formatToDateTime(dateTime) : '-';
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      draft: 'orange',
      signed: 'blue',
      executing: 'green',
      completed: 'purple',
      cancelled: 'red',
      suspended: 'default',
    };
    return colorMap[status] || 'default';
  };

  // 获取状态文本
  const getStatusText = (status: string) => {
    const textMap: Record<string, string> = {
      draft: '草稿',
      signed: '已签订',
      executing: '执行中',
      completed: '已完成',
      cancelled: '已取消',
      suspended: '已暂停',
    };
    return textMap[status] || '未知';
  };

  // 取消操作
  const handleCancel = () => {
    emit('cancel');
  };
</script>

<style lang="less" scoped>
  .paycontract-detail {
    .ant-card {
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);

      &.mb-4 {
        margin-bottom: 16px;
      }
    }

    .detail-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 12px;

      .detail-label {
        display: inline-block;
        width: 120px;
        font-weight: 500;
        color: #666;
        flex-shrink: 0;
      }

      .detail-value {
        flex: 1;
        color: #333;
        word-break: break-all;

        &.amount-text {
          color: #1890ff;
          font-weight: 500;
        }

        &.note-content {
          padding: 8px 12px;
          background-color: #fafafa;
          border-radius: 4px;
          min-height: 60px;
          white-space: pre-wrap;
          word-break: break-word;
        }
      }
    }

    .mt-3 {
      margin-top: 16px;
    }
  }
</style>
