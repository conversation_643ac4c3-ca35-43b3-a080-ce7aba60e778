<template>
  <div class="template-info-page p-6">
    <div class="max-w-4xl mx-auto">
      <!-- 页面标题 -->
      <div class="header-section mb-6">
        <h1 class="text-2xl font-bold mb-2">基本信息</h1>
        <p class="text-gray-600">管理项目模板的基本信息和配置参数</p>
      </div>

      <!-- 基本信息表单 -->
      <div class="form-section">
        <a-card title="基本信息" :bordered="false" class="mb-6">
          <template #extra>
            <a-space>
              <a-button @click="handleReset" :disabled="!hasChanges"> 重置 </a-button>
              <a-button type="primary" @click="handleSave" :loading="saving" :disabled="!hasChanges"> 保存更改 </a-button>
            </a-space>
          </template>

          <a-form ref="formRef" :model="formData" :rules="formRules" layout="vertical" @valuesChange="onFormChange">
            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item label="模板编码" name="code">
                  <a-input v-model:value="formData.code" placeholder="请输入模板编码" :maxlength="50" allow-clear>
                    <template #suffix>
                      <a-button type="link" size="small" @click="generateCode"> 自动生成 </a-button>
                    </template>
                  </a-input>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="模板名称" name="name" required>
                  <a-input v-model:value="formData.name" placeholder="请输入模板名称" :maxlength="255" allow-clear />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item label="模板类型" name="typeId">
                  <a-select
                    v-model:value="formData.typeId"
                    placeholder="请选择模板类型"
                    allow-clear
                    :options="templateTypeOptions"
                    :field-names="{ label: 'fullName', value: 'id' }" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="模板状态" name="status">
                  <a-radio-group v-model:value="formData.status">
                    <a-radio :value="0">启用</a-radio>
                    <a-radio :value="1">禁用</a-radio>
                  </a-radio-group>
                </a-form-item>
              </a-col>
            </a-row>

            <a-form-item label="模板描述" name="description">
              <a-textarea v-model:value="formData.description" placeholder="请输入模板描述" :rows="4" :maxlength="1000" show-count allow-clear />
            </a-form-item>

            <a-form-item label="模板图标" name="icon">
              <div class="icon-selector">
                <a-input v-model:value="formData.icon" placeholder="请输入图标类名或选择图标" :maxlength="100" allow-clear>
                  <template #addonAfter>
                    <a-button @click="showIconSelector = true"> 选择图标 </a-button>
                  </template>
                </a-input>
                <div v-if="formData.icon" class="icon-preview mt-2">
                  <i :class="formData.icon" class="text-2xl text-gray-600"></i>
                  <span class="ml-2 text-sm text-gray-500">图标预览</span>
                </div>
              </div>
            </a-form-item>
          </a-form>
        </a-card>

        <!-- 扩展配置 -->
        <a-card title="扩展配置" :bordered="false" class="mb-6">
          <a-form :model="configData" layout="vertical">
            <a-row :gutter="24">
              <a-col :span="8">
                <a-form-item label="配置字段1" name="configStr01">
                  <a-input v-model:value="configData.configStr01" placeholder="自定义配置字段1" :maxlength="255" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="配置字段2" name="configStr02">
                  <a-input v-model:value="configData.configStr02" placeholder="自定义配置字段2" :maxlength="255" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="配置字段3" name="configStr03">
                  <a-input v-model:value="configData.configStr03" placeholder="自定义配置字段3" :maxlength="255" allow-clear />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-card>

        <!-- 创建信息 -->
        <a-card title="创建信息" :bordered="false">
          <a-descriptions :column="2" bordered>
            <a-descriptions-item label="创建人">
              {{ templateInfo.creatorUserName || '未知' }}
            </a-descriptions-item>
            <a-descriptions-item label="创建时间">
              {{ formatDate(templateInfo.createdAt) || '未知' }}
            </a-descriptions-item>
            <a-descriptions-item label="最后更新人">
              {{ templateInfo.lastUpdateUserName || '未知' }}
            </a-descriptions-item>
            <a-descriptions-item label="最后更新时间">
              {{ formatDate(templateInfo.lastUpdatedAt) || '未知' }}
            </a-descriptions-item>
          </a-descriptions>
        </a-card>
      </div>
    </div>

    <!-- 图标选择器弹窗 -->
    <a-modal v-model:open="showIconSelector" title="选择图标" width="800px" @ok="handleIconSelect" @cancel="showIconSelector = false">
      <div class="icon-grid">
        <div v-for="icon in iconList" :key="icon.name" class="icon-item" :class="{ active: selectedIcon === icon.name }" @click="selectedIcon = icon.name">
          <i :class="icon.name" class="text-xl"></i>
          <div class="icon-name">{{ icon.title }}</div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted, watch } from 'vue';
  import {
    Card as ACard,
    Form as AForm,
    FormItem as AFormItem,
    Input as AInput,
    Textarea as ATextarea,
    Select as ASelect,
    RadioGroup as ARadioGroup,
    Radio as ARadio,
    Button as AButton,
    Space as ASpace,
    Row as ARow,
    Col as ACol,
    Descriptions as ADescriptions,
    DescriptionsItem as ADescriptionsItem,
    Modal as AModal,
  } from 'ant-design-vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { formatToDateTime } from '/@/utils/dateUtil';
  import {
    getProjectTemplateInfo,
    updateProjectTemplate,
    generateProjectTemplateCode,
    checkProjectTemplateNameExists,
    checkProjectTemplateCodeExists,
  } from '/@/api/project/projectTemplate';

  interface Props {
    templateId: string;
  }

  const props = defineProps<Props>();
  const { createMessage } = useMessage();

  // 响应式数据
  const formRef = ref();
  const templateInfo = ref<any>({});
  const originalData = ref<any>({});
  const saving = ref(false);
  const showIconSelector = ref(false);
  const selectedIcon = ref('');
  const templateTypeOptions = ref([]);

  // 表单数据
  const formData = reactive({
    code: '',
    name: '',
    description: '',
    typeId: '',
    status: 0,
    icon: '',
  });

  // 配置数据
  const configData = reactive({
    configStr01: '',
    configStr02: '',
    configStr03: '',
  });

  // 表单验证规则
  const formRules = {
    name: [
      { required: true, message: '请输入模板名称', trigger: 'blur' },
      { max: 255, message: '模板名称不能超过255个字符', trigger: 'blur' },
    ],
    code: [
      { max: 50, message: '模板编码不能超过50个字符', trigger: 'blur' },
      { validator: validateCode, trigger: 'blur' },
    ],
    description: [{ max: 1000, message: '描述不能超过1000个字符', trigger: 'blur' }],
  };

  // 图标列表
  const iconList = ref([
    { name: 'icon-ym icon-ym-generator-template', title: '模板' },
    { name: 'icon-ym icon-ym-generator-flow', title: '流程' },
    { name: 'icon-ym icon-ym-generator-menu', title: '菜单' },
    { name: 'icon-ym icon-ym-generator-calendar', title: '日历' },
    { name: 'icon-ym icon-ym-generator-team', title: '团队' },
    { name: 'icon-ym icon-ym-generator-info', title: '信息' },
    { name: 'icon-ym icon-ym-generator-settings', title: '设置' },
    { name: 'icon-ym icon-ym-generator-dashboard', title: '仪表盘' },
    // 更多图标...
  ]);

  // 计算属性
  const hasChanges = computed(() => {
    if (!originalData.value) return false;
    return (
      JSON.stringify(formData) !== JSON.stringify(originalData.value) ||
      JSON.stringify(configData) !==
        JSON.stringify({
          configStr01: originalData.value.configStr01 || '',
          configStr02: originalData.value.configStr02 || '',
          configStr03: originalData.value.configStr03 || '',
        })
    );
  });

  // 方法
  function formatDate(dateStr: string) {
    if (!dateStr) return '';
    return formatToDateTime(dateStr);
  }

  // 验证编码唯一性
  async function validateCode(rule: any, value: string) {
    if (!value) return Promise.resolve();

    try {
      const response = await checkProjectTemplateCodeExists(value, props.templateId);
      if (response.code === 200 && response.data) {
        return Promise.reject('该编码已存在');
      }
      return Promise.resolve();
    } catch (error) {
      return Promise.resolve(); // 网络错误时跳过验证
    }
  }

  // 生成编码
  async function generateCode() {
    try {
      const response = await generateProjectTemplateCode();
      if (response.code === 200) {
        formData.code = response.data;
        createMessage.success('编码生成成功');
      } else {
        createMessage.error(response.msg || '生成编码失败');
      }
    } catch (error) {
      console.error('生成编码失败:', error);
      createMessage.error('生成编码失败，请稍后重试');
    }
  }

  // 表单值变化
  function onFormChange() {
    // 触发验证状态更新
  }

  // 处理图标选择
  function handleIconSelect() {
    if (selectedIcon.value) {
      formData.icon = selectedIcon.value;
    }
    showIconSelector.value = false;
  }

  // 重置表单
  function handleReset() {
    if (originalData.value) {
      Object.assign(formData, originalData.value);
      Object.assign(configData, {
        configStr01: originalData.value.configStr01 || '',
        configStr02: originalData.value.configStr02 || '',
        configStr03: originalData.value.configStr03 || '',
      });
    }
  }

  // 保存
  async function handleSave() {
    try {
      // 表单验证
      await formRef.value?.validate();

      saving.value = true;
      const updateData = {
        ...formData,
        ...configData,
      };

      const response = await updateProjectTemplate(props.templateId, updateData);

      if (response.code === 200) {
        createMessage.success('保存成功');
        await loadTemplateInfo(); // 重新加载数据
      } else {
        createMessage.error(response.msg || '保存失败');
      }
    } catch (error) {
      console.error('保存失败:', error);
      if (error.errorFields) {
        createMessage.error('请检查表单填写是否正确');
      } else {
        createMessage.error('保存失败，请稍后重试');
      }
    } finally {
      saving.value = false;
    }
  }

  // 加载模板信息
  async function loadTemplateInfo() {
    if (!props.templateId) return;

    try {
      const response = await getProjectTemplateInfo(props.templateId);

      if (response.code === 200) {
        const data = response.data || {};
        templateInfo.value = data;

        // 设置表单数据
        Object.assign(formData, {
          code: data.code || '',
          name: data.name || '',
          description: data.description || '',
          typeId: data.typeId || '',
          status: data.status ?? 0,
          icon: data.icon || '',
        });

        // 设置配置数据
        Object.assign(configData, {
          configStr01: data.configStr01 || '',
          configStr02: data.configStr02 || '',
          configStr03: data.configStr03 || '',
        });

        // 保存原始数据
        originalData.value = { ...formData };
      } else {
        createMessage.error(response.msg || '加载模板信息失败');
      }
    } catch (error) {
      console.error('加载模板信息失败:', error);
      createMessage.error('加载模板信息失败，请稍后重试');
    }
  }

  // 加载模板类型选项
  async function loadTemplateTypes() {
    // TODO: 从字典API加载模板类型
    templateTypeOptions.value = [
      { id: '1', fullName: '通用模板' },
      { id: '2', fullName: '软件开发' },
      { id: '3', fullName: '项目管理' },
      { id: '4', fullName: '业务流程' },
    ];
  }

  // 监听templateId变化
  watch(
    () => props.templateId,
    newId => {
      if (newId) {
        loadTemplateInfo();
      }
    },
    { immediate: true },
  );

  // 初始化
  onMounted(() => {
    loadTemplateTypes();
  });
</script>

<style scoped>
  .template-info-page {
    background: #f5f5f5;
    min-height: 100%;
  }

  .form-section {
    background: white;
    border-radius: 8px;
    overflow: hidden;
  }

  .icon-selector .icon-preview {
    display: flex;
    align-items: center;
    padding: 8px;
    background: #f8f9fa;
    border-radius: 4px;
  }

  .icon-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 8px;
    max-height: 400px;
    overflow-y: auto;
  }

  .icon-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 12px 8px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .icon-item:hover {
    border-color: #1890ff;
    background: #f0f8ff;
  }

  .icon-item.active {
    border-color: #1890ff;
    background: #e6f7ff;
  }

  .icon-name {
    font-size: 12px;
    margin-top: 4px;
    text-align: center;
    color: #666;
  }

  :deep(.ant-card-head) {
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
  }

  :deep(.ant-descriptions-item-label) {
    background: #fafafa;
    font-weight: 500;
  }
</style>
