import { defHttp } from '/@/utils/http/axios';

enum Api {
  GetList = '/api/project/schema/projectSchemaPhase/getList',
  GetListByTemplateId = '/api/project/schema/projectSchemaPhase/getListByTemplateId',
  GetInfo = '/api/project/schema/projectSchemaPhase/getInfo',
  Create = '/api/project/schema/projectSchemaPhase/create',
  Update = '/api/project/schema/projectSchemaPhase/update',
  Delete = '/api/project/schema/projectSchemaPhase/delete',
  BatchDelete = '/api/project/schema/projectSchemaPhase/batchDelete',
  DeleteByTemplateId = '/api/project/schema/projectSchemaPhase/deleteByTemplateId',
  GetSelectList = '/api/project/schema/projectSchemaPhase/getSelectList',
  CopyPhaseConfigs = '/api/project/schema/projectSchemaPhase/copyPhaseConfigs',
  BatchSave = '/api/project/schema/projectSchemaPhase/batchSave',
  UpdateSeqNo = '/api/project/schema/projectSchemaPhase/updateSeqNo',
  BatchUpdateSeqNo = '/api/project/schema/projectSchemaPhase/batchUpdateSeqNo',
  AdjustSeqNo = '/api/project/schema/projectSchemaPhase/adjustSeqNo',
  GetNextSeqNo = '/api/project/schema/projectSchemaPhase/getNextSeqNo',
  GetUsageInfo = '/api/project/schema/projectSchemaPhase/getUsageInfo',
  CheckExist = '/api/project/schema/projectSchemaPhase/checkExist',
  GetByPhaseLibraryId = '/api/project/schema/projectSchemaPhase/getByPhaseLibraryId',
}

/**
 * 项目模板阶段配置接口
 */

// 获取阶段配置列表
export function getProjectSchemaPhaseList(params: any) {
  return defHttp.post({
    url: Api.GetList,
    data: params,
  });
}

// 根据项目模板ID获取阶段配置列表
export function getProjectSchemaPhaseListByTemplateId(projectTemplateId: string) {
  return defHttp.get({
    url: `${Api.GetListByTemplateId}/${projectTemplateId}`,
  });
}

// 获取阶段配置详情
export function getProjectSchemaPhaseInfo(id: string) {
  return defHttp.get({
    url: `${Api.GetInfo}/${id}`,
  });
}

// 创建阶段配置
export function createProjectSchemaPhase(params: any) {
  return defHttp.post({
    url: Api.Create,
    data: params,
  });
}

// 更新阶段配置
export function updateProjectSchemaPhase(id: string, params: any) {
  return defHttp.post({
    url: `${Api.Update}/${id}`,
    data: params,
  });
}

// 删除阶段配置
export function deleteProjectSchemaPhase(id: string) {
  return defHttp.delete({
    url: `${Api.Delete}/${id}`,
  });
}

// 批量删除阶段配置
export function batchDeleteProjectSchemaPhase(ids: string[]) {
  return defHttp.delete({
    url: Api.BatchDelete,
    data: ids,
  });
}

// 根据项目模板ID删除所有阶段配置
export function deleteProjectSchemaPhaseByTemplateId(projectTemplateId: string) {
  return defHttp.delete({
    url: `${Api.DeleteByTemplateId}/${projectTemplateId}`,
  });
}

// 获取阶段配置选择列表
export function getProjectSchemaPhaseSelectList(projectTemplateId?: string, keyword?: string) {
  return defHttp.get({
    url: Api.GetSelectList,
    params: { projectTemplateId, keyword },
  });
}

// 复制阶段配置
export function copyProjectSchemaPhaseConfigs(sourceTemplateId: string, targetTemplateId: string) {
  return defHttp.post({
    url: Api.CopyPhaseConfigs,
    params: { sourceTemplateId, targetTemplateId },
  });
}

// 批量保存阶段配置
export function batchSaveProjectSchemaPhase(projectTemplateId: string, phaseConfigs: any[]) {
  return defHttp.post({
    url: `${Api.BatchSave}/${projectTemplateId}`,
    data: phaseConfigs,
  });
}

// 更新序号
export function updateProjectSchemaPhaseSeqNo(id: string, seqNo: number) {
  return defHttp.post({
    url: `${Api.UpdateSeqNo}/${id}`,
    params: { seqNo },
  });
}

// 批量更新序号
export function batchUpdateProjectSchemaPhaseSeqNo(seqNoMap: Record<string, number>) {
  return defHttp.post({
    url: Api.BatchUpdateSeqNo,
    data: seqNoMap,
  });
}

// 调整序号（上移/下移）
export function adjustProjectSchemaPhaseSeqNo(id: string, direction: 'up' | 'down') {
  return defHttp.post({
    url: `${Api.AdjustSeqNo}/${id}`,
    params: { direction },
  });
}

// 获取下一个序号
export function getProjectSchemaPhaseNextSeqNo(projectTemplateId: string) {
  return defHttp.get({
    url: `${Api.GetNextSeqNo}/${projectTemplateId}`,
  });
}

// 获取阶段配置使用情况
export function getProjectSchemaPhaseUsageInfo(id: string) {
  return defHttp.get({
    url: `${Api.GetUsageInfo}/${id}`,
  });
}

// 检查阶段库ID在指定模板中是否已存在
export function checkProjectSchemaPhaseExist(projectTemplateId: string, phaseLibraryId: string, excludeId?: string) {
  return defHttp.get({
    url: Api.CheckExist,
    params: { projectTemplateId, phaseLibraryId, excludeId },
  });
}

// 根据阶段库ID获取阶段配置
export function getProjectSchemaPhaseByPhaseLibraryId(projectTemplateId: string, phaseLibraryId: string) {
  return defHttp.get({
    url: Api.GetByPhaseLibraryId,
    params: { projectTemplateId, phaseLibraryId },
  });
}
