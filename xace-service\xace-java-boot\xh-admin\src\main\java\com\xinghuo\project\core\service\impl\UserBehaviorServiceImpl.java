package com.xinghuo.project.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.DateXhUtil;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.core.dao.ProjBehaviorLogMapper;
import com.xinghuo.project.core.entity.ProjBehaviorLogEntity;
import com.xinghuo.project.core.service.UserBehaviorService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * 用户行为服务实现类
 * 
 * 实现用户行为记录的核心逻辑，包括访问记录和关注状态管理。
 * 使用 UPSERT 模式处理数据库操作，确保数据一致性。
 * 
 * <AUTHOR>
 * @date 2025-01-08
 */
@Slf4j
@Service
public class UserBehaviorServiceImpl extends BaseServiceImpl<ProjBehaviorLogMapper, ProjBehaviorLogEntity> implements UserBehaviorService {

    @Resource
    private ProjBehaviorLogMapper projBehaviorLogMapper;

    /**
     * 记录一次访问行为（异步执行）
     * 
     * 使用 UPSERT 模式：如果记录存在则更新最后访问时间，否则创建新记录。
     * 异步执行以避免阻塞主业务流程。
     */
    @Async
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void logVisit(String userId, String targetId, String targetType) {
        try {
            // 参数校验
            if (StrXhUtil.isEmpty(userId) || StrXhUtil.isEmpty(targetId) || StrXhUtil.isEmpty(targetType)) {
                log.warn("记录访问行为失败：参数不能为空 - userId: {}, targetId: {}, targetType: {}", 
                        userId, targetId, targetType);
                return;
            }

            // 查询是否已存在访问记录
            QueryWrapper<ProjBehaviorLogEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda()
                    .eq(ProjBehaviorLogEntity::getUserId, userId)
                    .eq(ProjBehaviorLogEntity::getTargetId, targetId)
                    .eq(ProjBehaviorLogEntity::getTargetType, targetType)
                    .eq(ProjBehaviorLogEntity::getBehaviorType, "VISIT");

            ProjBehaviorLogEntity existingRecord = this.getOne(queryWrapper);
            Date currentTime = DateXhUtil.date();

            if (existingRecord != null) {
                // 更新现有记录的最后访问时间
                existingRecord.setLastActionTime(currentTime);
                existingRecord.setIsValid(1); // 确保记录有效
                this.updateById(existingRecord);
                log.debug("更新访问记录成功 - userId: {}, targetId: {}, targetType: {}", 
                        userId, targetId, targetType);
            } else {
                // 创建新的访问记录
                ProjBehaviorLogEntity newRecord = new ProjBehaviorLogEntity();
                newRecord.setId(RandomUtil.snowId());
                newRecord.setUserId(userId);
                newRecord.setTargetId(targetId);
                newRecord.setTargetType(targetType);
                newRecord.setBehaviorType("VISIT");
                newRecord.setLastActionTime(currentTime);
                newRecord.setIsValid(1);
                
                this.save(newRecord);
                log.debug("创建访问记录成功 - userId: {}, targetId: {}, targetType: {}", 
                        userId, targetId, targetType);
            }
        } catch (Exception e) {
            log.error("记录访问行为异常 - userId: {}, targetId: {}, targetType: {}", 
                    userId, targetId, targetType, e);
        }
    }

    /**
     * 切换对象的关注状态
     * 
     * 使用 UPSERT 模式处理关注状态变更，通过 is_valid 字段实现软删除。
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void toggleFavorite(String userId, String targetId, String targetType, boolean isFavorite) {
        try {
            // 参数校验
            if (StrXhUtil.isEmpty(userId) || StrXhUtil.isEmpty(targetId) || StrXhUtil.isEmpty(targetType)) {
                log.warn("切换关注状态失败：参数不能为空 - userId: {}, targetId: {}, targetType: {}", 
                        userId, targetId, targetType);
                throw new RuntimeException("参数不能为空");
            }

            // 查询是否已存在关注记录
            QueryWrapper<ProjBehaviorLogEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda()
                    .eq(ProjBehaviorLogEntity::getUserId, userId)
                    .eq(ProjBehaviorLogEntity::getTargetId, targetId)
                    .eq(ProjBehaviorLogEntity::getTargetType, targetType)
                    .eq(ProjBehaviorLogEntity::getBehaviorType, "FAVORITE");

            ProjBehaviorLogEntity existingRecord = this.getOne(queryWrapper);
            Date currentTime = DateXhUtil.date();

            if (existingRecord != null) {
                // 更新现有记录的状态和时间
                existingRecord.setIsValid(isFavorite ? 1 : 0);
                existingRecord.setLastActionTime(currentTime);
                this.updateById(existingRecord);
                log.info("更新关注状态成功 - userId: {}, targetId: {}, targetType: {}, isFavorite: {}", 
                        userId, targetId, targetType, isFavorite);
            } else {
                // 创建新的关注记录
                ProjBehaviorLogEntity newRecord = new ProjBehaviorLogEntity();
                newRecord.setId(RandomUtil.snowId());
                newRecord.setUserId(userId);
                newRecord.setTargetId(targetId);
                newRecord.setTargetType(targetType);
                newRecord.setBehaviorType("FAVORITE");
                newRecord.setLastActionTime(currentTime);
                newRecord.setIsValid(isFavorite ? 1 : 0);
                
                this.save(newRecord);
                log.info("创建关注记录成功 - userId: {}, targetId: {}, targetType: {}, isFavorite: {}", 
                        userId, targetId, targetType, isFavorite);
            }
        } catch (Exception e) {
            log.error("切换关注状态异常 - userId: {}, targetId: {}, targetType: {}, isFavorite: {}", 
                    userId, targetId, targetType, isFavorite, e);
            throw new RuntimeException("切换关注状态失败: " + e.getMessage());
        }
    }

    /**
     * 检查用户是否已关注指定对象
     */
    @Override
    public boolean isFavorite(String userId, String targetId, String targetType) {
        if (StrXhUtil.isEmpty(userId) || StrXhUtil.isEmpty(targetId) || StrXhUtil.isEmpty(targetType)) {
            return false;
        }

        QueryWrapper<ProjBehaviorLogEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(ProjBehaviorLogEntity::getUserId, userId)
                .eq(ProjBehaviorLogEntity::getTargetId, targetId)
                .eq(ProjBehaviorLogEntity::getTargetType, targetType)
                .eq(ProjBehaviorLogEntity::getBehaviorType, "FAVORITE")
                .eq(ProjBehaviorLogEntity::getIsValid, 1);

        ProjBehaviorLogEntity record = this.getOne(queryWrapper);
        return record != null;
    }

    /**
     * 获取用户最近访问的对象列表
     */
    @Override
    public java.util.List<ProjBehaviorLogEntity> getRecentVisits(String userId, String targetType, int limit) {
        if (StrXhUtil.isEmpty(userId) || limit <= 0) {
            return new java.util.ArrayList<>();
        }

        QueryWrapper<ProjBehaviorLogEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(ProjBehaviorLogEntity::getUserId, userId)
                .eq(ProjBehaviorLogEntity::getBehaviorType, "VISIT")
                .eq(ProjBehaviorLogEntity::getIsValid, 1);

        // 如果指定了对象类型，则添加类型过滤
        if (StrXhUtil.isNotEmpty(targetType)) {
            queryWrapper.lambda().eq(ProjBehaviorLogEntity::getTargetType, targetType);
        }

        // 按最后访问时间倒序排列，限制返回数量
        queryWrapper.lambda()
                .orderByDesc(ProjBehaviorLogEntity::getLastActionTime)
                .last("LIMIT " + limit);

        return this.list(queryWrapper);
    }

    /**
     * 批量检查关注状态
     */
    @Override
    public java.util.Map<String, Boolean> batchCheckFavoriteStatus(String userId, java.util.List<java.util.Map<String, String>> targets) {
        java.util.Map<String, Boolean> results = new java.util.HashMap<>();

        if (StrXhUtil.isEmpty(userId) || targets == null || targets.isEmpty()) {
            return results;
        }

        for (java.util.Map<String, String> target : targets) {
            String targetId = target.get("targetId");
            String targetType = target.get("targetType");

            if (StrXhUtil.isNotEmpty(targetId) && StrXhUtil.isNotEmpty(targetType)) {
                boolean isFavorite = this.isFavorite(userId, targetId, targetType);
                results.put(targetId, isFavorite);
            }
        }

        return results;
    }
}
