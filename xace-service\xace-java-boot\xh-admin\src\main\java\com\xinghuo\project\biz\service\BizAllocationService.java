package com.xinghuo.project.biz.service;

import com.xinghuo.project.biz.entity.BizAllocationDetailEntity;
import com.xinghuo.project.biz.enums.AllocationTypeEnum;
import com.xinghuo.project.biz.enums.BusinessTypeEnum;
import com.xinghuo.common.base.service.BaseService;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 业务分配服务接口
 * 
 * <AUTHOR>
 * @version V2.0
 * @copyright 易趋集团
 * @date 2024-01-01
 */
public interface BizAllocationService extends BaseService<BizAllocationDetailEntity> {

    /**
     * 根据业务ID和业务类型获取分配明细
     * 
     * @param businessId 业务ID
     * @param businessType 业务类型
     * @return 分配明细列表
     */
    List<BizAllocationDetailEntity> getByBusinessId(String businessId, BusinessTypeEnum businessType);

    /**
     * 批量保存业务分配
     * 
     * @param businessId 业务ID
     * @param businessType 业务类型
     * @param allocations 分配数据
     */
    void saveBatchAllocations(String businessId, BusinessTypeEnum businessType, 
                            List<BizAllocationDetailEntity> allocations);

    /**
     * 获取业务分配汇总数据
     * 
     * @param businessId 业务ID
     * @param businessType 业务类型
     * @return 按部门和分配类型汇总的金额
     */
    Map<String, Map<AllocationTypeEnum, BigDecimal>> getAllocationSummary(String businessId, 
                                                                          BusinessTypeEnum businessType);

    /**
     * 删除业务分配数据
     * 
     * @param businessId 业务ID
     * @param businessType 业务类型
     */
    void deleteByBusinessId(String businessId, BusinessTypeEnum businessType);

    /**
     * 更新分配数据
     * 
     * @param businessId 业务ID
     * @param businessType 业务类型
     * @param allocations 新的分配数据
     */
    void updateAllocations(String businessId, BusinessTypeEnum businessType, 
                          List<BizAllocationDetailEntity> allocations);

    /**
     * 验证分配数据的合理性
     * 
     * @param allocations 分配数据
     * @param totalAmount 总金额
     * @return 验证结果
     */
    boolean validateAllocations(List<BizAllocationDetailEntity> allocations, BigDecimal totalAmount);

    /**
     * 获取部门分配报表数据
     * 
     * @param businessType 业务类型
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 报表数据
     */
    List<Map<String, Object>> getDepartmentAllocationReport(BusinessTypeEnum businessType, 
                                                           String startDate, String endDate);
}