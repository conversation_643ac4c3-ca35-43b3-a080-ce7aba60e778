package com.xinghuo.project.biz.model.bizContract;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 合同视图对象
 */
@Data
@Schema(description = "合同视图对象")
public class BizContractVO {

    /**
     * 合同ID
     */
    @Schema(description = "合同ID")
    private String id;

    /**
     * 合同ID（兼容前端字段）
     */
    @Schema(description = "合同ID（兼容前端字段）")
    private String cId;

    /**
     * 合同名称
     */
    @Schema(description = "合同名称")
    private String name;

    /**
     * 合同编号
     */
    @Schema(description = "合同编号")
    private String cno;

    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    private String custId;

    /**
     * 客户名称（非数据库字段，需要关联查询）
     */
    @Schema(description = "客户名称")
    private String custName;

    /**
     * 最终用户ID
     */
    @Schema(description = "最终用户ID")
    private String finalUserId;

    /**
     * 最终用户名称（非数据库字段，需要关联查询）
     */
    @Schema(description = "最终用户名称")
    private String finalUserName;

    /**
     * 负责人ID
     */
    @Schema(description = "负责人ID")
    private String ownId;

    /**
     * 负责人名称（非数据库字段，需要关联查询）
     */
    @Schema(description = "负责人名称")
    private String ownName;

    /**
     * 汇报频率
     */
    @Schema(description = "汇报频率")
    private String reportFrequency;

    /**
     * 合同金额
     */
    @Schema(description = "合同金额")
    private BigDecimal amount;

    /**
     * 已收金额
     */
    @Schema(description = "已收金额")
    private BigDecimal ysAmount;

    /**
     * 本年度收款金额
     */
    @Schema(description = "本年度收款金额")
    private BigDecimal yearYsAmount;

    /**
     * 外采金额
     */
    @Schema(description = "外采金额")
    private BigDecimal externalAmount;

    /**
     * 收款状态
     */
    @Schema(description = "收款状态")
    private String moneyStatus;

    /**
     * 合同状态
     */
    @Schema(description = "合同状态")
    private String contractStatus;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 部门ID
     */
    @Schema(description = "部门ID")
    private String deptId;

    /**
     * 部门名称（非数据库字段，需要关联查询）
     */
    @Schema(description = "部门名称")
    private String deptName;

    /**
     * 联系人
     */
    @Schema(description = "联系人")
    private String linkman;

    /**
     * 联系电话
     */
    @Schema(description = "联系电话")
    private String linkTelephone;

    /**
     * 合同签订日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "合同签订日期")
    private Date signDate;

    /**
     * 中标日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "中标日期")
    private Date bidDate;

    /**
     * 开工日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "开工日期")
    private Date commencementDate;

    /**
     * 初验日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "初验日期")
    private Date initialCheckDate;

    /**
     * 终验日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "终验日期")
    private Date finalCheckDate;

    /**
     * 审计日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "审计日期")
    private Date auditDate;

    /**
     * 合同年度
     */
    @Schema(description = "合同年度")
    private Integer signYear;

    /**
     * 合同开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "合同开始日期")
    private Date cstartDate;

    /**
     * 合同结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "合同结束日期")
    private Date cendDate;

    /**
     * 维保开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "维保开始日期")
    private Date mstartDate;

    /**
     * 维保结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "维保结束日期")
    private Date mendDate;

    /**
     * 预估毛利
     */
    @Schema(description = "预估毛利")
    private BigDecimal estProbit;

    /**
     * 实际毛利
     */
    @Schema(description = "实际毛利")
    private BigDecimal actProbit;

    /**
     * 预估毛利率
     */
    @Schema(description = "预估毛利率")
    private BigDecimal estProbitRatio;

    /**
     * 实际毛利率
     */
    @Schema(description = "实际毛利率")
    private BigDecimal actProbitRatio;

    /**
     * 采购费用预测
     */
    @Schema(description = "采购费用预测")
    private BigDecimal evaExternalAmount;

    /**
     * 费用预测
     */
    @Schema(description = "费用预测")
    private BigDecimal evaCostAmount;

    /**
     * 实际采购金额
     */
    @Schema(description = "实际采购金额")
    private BigDecimal actExternalAmount;

    /**
     * 实际费用
     */
    @Schema(description = "实际费用")
    private BigDecimal actCostAmount;

    /**
     * 待签外采金额
     */
    @Schema(description = "待签外采金额")
    private BigDecimal unsignExternalAmount;

    /**
     * 合同类型状态
     */
    @Schema(description = "合同类型状态")
    private String typeStatus;

    /**
     * 合同类型状态文本（非数据库字段，需要字典转换）
     */
    @Schema(description = "合同类型状态文本")
    private String typeStatusText;

    /**
     * 是否外采状态
     */
    @Schema(description = "是否外采状态")
    private String externalStatus;

    /**
     * 是否外采状态文本（非数据库字段，需要字典转换）
     */
    @Schema(description = "是否外采状态文本")
    private String externalStatusText;

    /**
     * 一部金额
     */
    @Schema(description = "一部金额")
    private BigDecimal yfYbAmount;

    /**
     * 二部金额
     */
    @Schema(description = "二部金额")
    private BigDecimal yfEbAmount;

    /**
     * 交付分部金额
     */
    @Schema(description = "交付分部金额")
    private BigDecimal yfJfAmount;

    /**
     * 综合分配金额
     */
    @Schema(description = "综合分配金额")
    private BigDecimal yfOtherAmount;

    /**
     * 一部外采金额
     */
    @Schema(description = "一部外采金额")
    private BigDecimal outYbAmount;

    /**
     * 二部外采金额
     */
    @Schema(description = "二部外采金额")
    private BigDecimal outEbAmount;

    /**
     * 交付外采金额
     */
    @Schema(description = "交付外采金额")
    private BigDecimal outJfAmount;

    /**
     * 综合外采金额
     */
    @Schema(description = "综合外采金额")
    private BigDecimal outOtherAmount;

    /**
     * 待签一部外采
     */
    @Schema(description = "待签一部外采")
    private BigDecimal unsignOutYbAmount;

    /**
     * 待签二部待采
     */
    @Schema(description = "待签二部待采")
    private BigDecimal unsignOutEbAmount;

    /**
     * 待签交付待采
     */
    @Schema(description = "待签交付待采")
    private BigDecimal unsignOutJfAmount;

    /**
     * 待签综合外采
     */
    @Schema(description = "待签综合外采")
    private BigDecimal unsignOutOtherAmount;

    /**
     * 一部外采已付
     */
    @Schema(description = "一部外采已付")
    private BigDecimal outYfYbAmount;

    /**
     * 二部外采已付
     */
    @Schema(description = "二部外采已付")
    private BigDecimal outYfEbAmount;

    /**
     * 交付外采已付
     */
    @Schema(description = "交付外采已付")
    private BigDecimal outYfJfAmount;

    /**
     * 综合外采已付
     */
    @Schema(description = "综合外采已付")
    private BigDecimal outYfOtherAmount;

    /**
     * 续签状态
     */
    @Schema(description = "续签状态")
    private Integer isContinue;

    /**
     * 续签状态文本（非数据库字段，需要转换）
     */
    @Schema(description = "续签状态文本")
    private String isContinueText;

    /**
     * 合同状态文本（非数据库字段，需要字典转换）
     */
    @Schema(description = "合同状态文本")
    private String contractStatusText;

    /**
     * 收款状态文本（非数据库字段，需要字典转换）
     */
    @Schema(description = "收款状态文本")
    private String moneyStatusText;

    /**
     * 创建用户
     */
    @Schema(description = "创建用户")
    private String createUserId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 最后修改人
     */
    @Schema(description = "最后修改人")
    private String lastModifiedUserId;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "更新时间")
    private Date updateTime;
}
