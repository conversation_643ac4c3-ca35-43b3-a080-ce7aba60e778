package com.xinghuo.project.template.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 标准项目问题库选择列表视图对象
 * 用于下拉选择框等场景，只包含必要的id和fullName字段
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
@Schema(description = "标准项目问题库选择列表视图对象")
public class IssueLibrarySelectVO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private String id;

    /**
     * 完整名称 (格式: [编码] 标题)
     * 用于option显示，符合fullName和id的规范要求
     */
    @Schema(description = "完整名称")
    private String fullName;

    /**
     * 问题编码 (用于构建fullName)
     */
    @Schema(description = "问题编码")
    private String code;

    /**
     * 问题标题 (用于构建fullName)
     */
    @Schema(description = "问题标题")
    private String title;

    /**
     * 发布状态 (如: draft 草稿, published 已发布, archived 归档)
     * 用于过滤，通常只显示已发布状态的选项
     */
    @Schema(description = "发布状态")
    private String status;

    /**
     * 问题类别ID
     */
    @Schema(description = "问题类别ID")
    private String issueCategoryId;

    /**
     * 默认优先级/严重性ID
     */
    @Schema(description = "默认优先级/严重性ID")
    private String defaultPriorityId;
}
