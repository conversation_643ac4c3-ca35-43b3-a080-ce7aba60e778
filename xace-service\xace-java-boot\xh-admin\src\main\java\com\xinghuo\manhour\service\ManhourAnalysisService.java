package com.xinghuo.manhour.service;

import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.manhour.entity.ManhourEntity;
import com.xinghuo.manhour.model.analysis.*;

import jakarta.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 工时分析服务接口
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
public interface ManhourAnalysisService extends BaseService<ManhourEntity> {

    /**
     * 获取工时分析概览数据
     *
     * @param params 查询参数
     * @return 概览数据
     */
    WorkhourAnalysisOverview getOverview(WorkhourAnalysisParams params);

    /**
     * 获取工时分析图表数据
     *
     * @param params 查询参数
     * @return 图表数据
     */
    ChartDataModel getCharts(WorkhourAnalysisParams params);

    /**
     * 获取个人分析列表
     *
     * @param pagination 分页参数
     * @return 个人分析数据列表
     */
    List<PersonalAnalysisVO> getPersonalAnalysisList(WorkhourAnalysisPagination pagination);

    /**
     * 获取项目分析列表
     *
     * @param pagination 分页参数
     * @return 项目分析数据列表
     */
    List<ProjectAnalysisVO> getProjectAnalysisList(WorkhourAnalysisPagination pagination);

    /**
     * 获取分部分析列表
     *
     * @param pagination 分页参数
     * @return 分部分析数据列表
     */
    List<DepartmentAnalysisVO> getDepartmentAnalysisList(WorkhourAnalysisPagination pagination);

    /**
     * 获取工时明细列表
     *
     * @param pagination 分页参数
     * @return 工时明细数据列表
     */
    List<WorkhourDetailVO> getWorkhourDetailList(WorkhourAnalysisPagination pagination);

    /**
     * 导出工时分析报表
     *
     * @param params   查询参数
     * @param response HTTP响应
     */
    void exportWorkhourAnalysis(WorkhourAnalysisParams params, HttpServletResponse response);

    /**
     * 获取项目选择器数据
     *
     * @return 项目列表
     */
    List<Map<String, Object>> getProjectSelector();

    /**
     * 获取分部选择器数据
     *
     * @return 分部列表
     */
    List<Map<String, Object>> getDepartmentSelector();

    /**
     * 获取个人工时效率详情
     *
     * @param userId 用户ID
     * @param params 查询参数
     * @return 个人效率详情
     */
    PersonalEfficiencyDetail getPersonalEfficiencyDetail(String userId, WorkhourAnalysisParams params);

    /**
     * 获取项目健康度详情
     *
     * @param projectId 项目ID
     * @param params    查询参数
     * @return 项目健康度详情
     */
    ProjectHealthDetail getProjectHealthDetail(String projectId, WorkhourAnalysisParams params);

    /**
     * 获取分部资源利用率详情
     *
     * @param fbId   分部ID
     * @param params 查询参数
     * @return 分部资源利用率详情
     */
    DepartmentUtilizationDetail getDepartmentUtilizationDetail(String fbId, WorkhourAnalysisParams params);

    /**
     * 获取技能标签统计
     *
     * @param params 查询参数
     * @return 技能标签统计
     */
    List<Map<String, Object>> getSkillTagsStatistics(WorkhourAnalysisParams params);

    /**
     * 获取工时类型统计
     *
     * @param params 查询参数
     * @return 工时类型统计
     */
    List<Map<String, Object>> getWorkTypeStatistics(WorkhourAnalysisParams params);
}
