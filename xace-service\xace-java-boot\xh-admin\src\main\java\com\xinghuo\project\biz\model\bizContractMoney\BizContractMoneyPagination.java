package com.xinghuo.project.biz.model.bizContractMoney;


import com.xinghuo.common.base.model.Pagination;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 合同收款分页查询对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "合同收款分页查询对象")
public class BizContractMoneyPagination extends Pagination {

    /**
     * 基础项目ID
     */
    @Schema(description = "基础项目ID")
    private String projBaseId;

    /**
     * 合同ID
     */
    @Schema(description = "合同ID")
    private String contractId;

    /**
     * 合同名称
     */
    @Schema(description = "合同名称")
    private String contractName;

    /**
     * 合同编号
     */
    @Schema(description = "合同编号")
    private String contractNo;

    /**
     * 收款状态
     */
    @Schema(description = "收款状态")
    private Integer payStatus;

    /**
     * 项目经理
     */
    @Schema(description = "项目经理")
    private String ownId;

    /**
     * 部门ID
     */
    @Schema(description = "部门ID")
    private String deptId;


    private String dateType;

    private Date startTime;

    private Date endTime;


    /**
     * 关键字搜索
     */
    @Schema(description = "关键字搜索")
    private String keyword;




}

