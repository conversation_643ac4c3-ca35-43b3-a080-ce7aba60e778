package com.xinghuo.performance.model.analysis;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 分部绩效分析VO
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class DepartmentPerformanceAnalysisVO {

    /**
     * 分部ID
     */
    private String fbId;

    /**
     * 分部名称
     */
    private String fbName;

    /**
     * 参与人数
     */
    private Integer userCount;

    /**
     * 平均得分
     */
    private BigDecimal avgScore;

    /**
     * 优秀率
     */
    private BigDecimal excellentRate;

    /**
     * 完成率
     */
    private BigDecimal completionRate;

    /**
     * 最高分
     */
    private BigDecimal maxScore;

    /**
     * 最低分
     */
    private BigDecimal minScore;

    /**
     * 分数标准差
     */
    private BigDecimal scoreStdDev;

    /**
     * 分部排名
     */
    private Integer ranking;
}
