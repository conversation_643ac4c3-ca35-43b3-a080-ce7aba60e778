<template>
  <div class="template-status-page p-4">
    <!-- 页面标题 -->
    <div class="page-header mb-4">
      <h2 class="text-xl font-semibold mb-2">状态配置</h2>
      <p class="text-gray-600">配置项目模板的任务状态和流转规则</p>
    </div>

    <a-spin :spinning="loading">
      <!-- 工具栏 -->
      <div class="toolbar mb-4 flex justify-between items-center">
        <div class="search-area flex items-center space-x-4">
          <a-input-search v-model:value="searchText" placeholder="搜索状态名称" style="width: 300px" @search="handleSearch" />
          <a-select v-model:value="categoryFilter" placeholder="筛选分类" style="width: 150px" allow-clear @change="handleCategoryFilter">
            <a-select-option value="todo">待办</a-select-option>
            <a-select-option value="progress">进行中</a-select-option>
            <a-select-option value="review">评审</a-select-option>
            <a-select-option value="done">完成</a-select-option>
          </a-select>
        </div>
        <div class="actions">
          <a-space>
            <a-button type="primary" @click="handleAdd">
              <template #icon><PlusOutlined /></template>
              添加状态
            </a-button>
            <a-button @click="handleWorkflowDesign">
              <template #icon><BranchesOutlined /></template>
              状态流转设计
            </a-button>
            <a-button @click="handleImportFromLibrary">
              <template #icon><ImportOutlined /></template>
              从状态库导入
            </a-button>
            <a-button @click="handleRefresh">
              <template #icon><ReloadOutlined /></template>
              刷新
            </a-button>
          </a-space>
        </div>
      </div>

      <!-- 状态表格 -->
      <BasicTable @register="registerTable">
        <!-- 自定义列 -->
        <template #statusName="{ record }">
          <div class="flex items-center">
            <div class="status-icon mr-2">
              <a-avatar :style="{ backgroundColor: record.color }" :size="24">
                <template #icon>
                  <component :is="getCategoryIcon(record.category)" />
                </template>
              </a-avatar>
            </div>
            <div>
              <div class="font-medium">{{ record.statusName }}</div>
              <div class="text-sm text-gray-500">{{ record.statusCode }}</div>
            </div>
          </div>
        </template>

        <template #category="{ record }">
          <a-tag :color="getCategoryColor(record.category)">
            {{ getCategoryText(record.category) }}
          </a-tag>
        </template>

        <template #isActive="{ record }">
          <a-tag :color="record.isActive ? 'green' : 'red'">
            {{ record.isActive ? '启用' : '禁用' }}
          </a-tag>
        </template>

        <template #transitions="{ record }">
          <div class="transitions">
            <a-tag v-for="transition in record.transitions" :key="transition.to" size="small" class="mb-1"> → {{ transition.toName }} </a-tag>
            <span v-if="!record.transitions?.length" class="text-gray-400">-</span>
          </div>
        </template>

        <template #action="{ record }">
          <TableAction
            :actions="[
              {
                icon: 'ant-design:edit-outlined',
                label: '编辑',
                onClick: handleEdit.bind(null, record),
              },
              {
                icon: 'ant-design:copy-outlined',
                label: '复制',
                onClick: handleCopy.bind(null, record),
              },
              {
                icon: 'ant-design:arrow-right-outlined',
                label: '流转规则',
                onClick: handleTransitions.bind(null, record),
              },
              {
                icon: 'ant-design:delete-outlined',
                label: '删除',
                color: 'error',
                popConfirm: {
                  title: '是否确认删除？',
                  placement: 'left',
                  confirm: handleDelete.bind(null, record),
                },
              },
            ]" />
        </template>
      </BasicTable>
    </a-spin>

    <!-- 添加/编辑弹窗 -->
    <BasicDrawer @register="registerDrawer" :title="drawerTitle" width="50%" @ok="handleSubmit">
      <BasicForm @register="registerForm" />
    </BasicDrawer>

    <!-- 状态流转设计弹窗 -->
    <BasicModal @register="registerWorkflowModal" title="状态流转设计" width="80%" :footer="null">
      <div class="workflow-content">
        <div class="workflow-toolbar mb-4 flex justify-between items-center">
          <div class="info">
            <h4 class="font-medium">状态流转图</h4>
            <p class="text-sm text-gray-500">拖拽节点可调整位置，点击连线可设置条件</p>
          </div>
          <div class="actions">
            <a-space>
              <a-button @click="handleSaveWorkflow">
                <SaveOutlined />
                保存流转图
              </a-button>
              <a-button @click="handleResetWorkflow">
                <ReloadOutlined />
                重置
              </a-button>
            </a-space>
          </div>
        </div>

        <!-- 工作流设计器占位区域 -->
        <div class="workflow-designer" style="height: 500px; border: 1px dashed #d9d9d9; display: flex; align-items: center; justify-content: center">
          <div class="text-center text-gray-500">
            <BranchesOutlined style="font-size: 48px; margin-bottom: 16px" />
            <div>状态流转设计器开发中...</div>
            <div class="text-sm mt-2">将在此显示可视化的状态流转设计器</div>
          </div>
        </div>
      </div>
    </BasicModal>

    <!-- 流转规则设置弹窗 -->
    <BasicModal @register="registerTransitionModal" title="流转规则设置" width="60%" @ok="handleTransitionSubmit">
      <div class="transition-content">
        <div class="current-status mb-4 p-3 bg-gray-50 rounded">
          <h4 class="font-medium">当前状态：{{ currentTransitionRecord?.statusName }}</h4>
        </div>

        <div class="transition-list">
          <div class="mb-4">
            <a-button type="dashed" @click="handleAddTransition">
              <PlusOutlined />
              添加流转规则
            </a-button>
          </div>

          <BasicTable @register="registerTransitionTable" :can-resize="false">
            <template #toStatus="{ record }">
              <a-select v-model:value="record.to" placeholder="选择目标状态" style="width: 100%" :options="availableStatuses" />
            </template>

            <template #condition="{ record }">
              <a-input v-model:value="record.condition" placeholder="流转条件（可选）" />
            </template>

            <template #action="{ record, index }">
              <a-button type="link" size="small" danger @click="handleRemoveTransition(index)"> 移除 </a-button>
            </template>
          </BasicTable>
        </div>
      </div>
    </BasicModal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed, inject, onMounted, h } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { BasicForm, useForm } from '/@/components/Form';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import {
    PlusOutlined,
    BranchesOutlined,
    ImportOutlined,
    ReloadOutlined,
    SaveOutlined,
    ClockCircleOutlined,
    PlayCircleOutlined,
    CheckCircleOutlined,
    StopOutlined,
  } from '@ant-design/icons-vue';

  defineOptions({ name: 'ProjectTemplateStatusConfigPage' });

  // 注入模板ID
  const templateId = inject<any>('templateId');
  const { createMessage } = useMessage();

  const loading = ref(false);
  const searchText = ref('');
  const categoryFilter = ref('');
  const drawerTitle = ref('');
  const currentRecord = ref<any>(null);
  const currentTransitionRecord = ref<any>(null);

  // 流转规则数据
  const transitionData = ref<any[]>([]);
  const availableStatuses = ref([
    { label: '进行中', value: 'progress' },
    { label: '评审中', value: 'review' },
    { label: '已完成', value: 'done' },
    { label: '已取消', value: 'cancelled' },
  ]);

  // 表格列配置
  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      width: 60,
      customRender: ({ index }) => index + 1,
    },
    {
      title: '状态名称',
      dataIndex: 'statusName',
      width: 200,
      slots: { customRender: 'statusName' },
    },
    {
      title: '分类',
      dataIndex: 'category',
      width: 100,
      slots: { customRender: 'category' },
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      width: 80,
      slots: { customRender: 'isActive' },
    },
    {
      title: '排序',
      dataIndex: 'sort',
      width: 80,
    },
    {
      title: '颜色',
      dataIndex: 'color',
      width: 80,
      customRender: ({ record }) =>
        h('div', { style: { display: 'flex', alignItems: 'center' } }, [
          h('div', {
            style: {
              width: '16px',
              height: '16px',
              borderRadius: '2px',
              backgroundColor: record.color,
              marginRight: '8px',
            },
          }),
          h('span', {}, record.color),
        ]),
    },
    {
      title: '可流转至',
      dataIndex: 'transitions',
      width: 200,
      slots: { customRender: 'transitions' },
    },
    {
      title: '描述',
      dataIndex: 'description',
      width: 200,
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 150,
      customRender: ({ text }) => (text ? new Date(text).toLocaleDateString() : '-'),
    },
  ];

  // 表单配置
  const formSchemas = [
    {
      field: 'statusName',
      label: '状态名称',
      component: 'Input',
      required: true,
      colProps: { span: 12 },
    },
    {
      field: 'statusCode',
      label: '状态编码',
      component: 'Input',
      required: true,
      colProps: { span: 12 },
    },
    {
      field: 'category',
      label: '分类',
      component: 'Select',
      required: true,
      componentProps: {
        options: [
          { label: '待办', value: 'todo' },
          { label: '进行中', value: 'progress' },
          { label: '评审', value: 'review' },
          { label: '完成', value: 'done' },
        ],
      },
      colProps: { span: 12 },
    },
    {
      field: 'isActive',
      label: '状态',
      component: 'RadioGroup',
      defaultValue: true,
      componentProps: {
        options: [
          { label: '启用', value: true },
          { label: '禁用', value: false },
        ],
      },
      colProps: { span: 12 },
    },
    {
      field: 'sort',
      label: '排序',
      component: 'InputNumber',
      required: true,
      componentProps: {
        min: 1,
      },
      colProps: { span: 12 },
    },
    {
      field: 'color',
      label: '颜色',
      component: 'Input',
      componentProps: {
        placeholder: '#1890ff',
      },
      colProps: { span: 12 },
    },
    {
      field: 'description',
      label: '描述',
      component: 'InputTextArea',
      componentProps: {
        rows: 3,
      },
      colProps: { span: 24 },
    },
  ];

  // 流转规则表格列
  const transitionColumns = [
    {
      title: '目标状态',
      dataIndex: 'to',
      width: 200,
      slots: { customRender: 'toStatus' },
    },
    {
      title: '流转条件',
      dataIndex: 'condition',
      width: 200,
      slots: { customRender: 'condition' },
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      slots: { customRender: 'action' },
    },
  ];

  // 表格实例
  const [registerTable, { reload, getDataSource }] = useTable({
    api: loadStatusData,
    columns,
    useSearchForm: false,
    actionColumn: {
      width: 200,
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
    },
  });

  // 表单实例
  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 100,
    schemas: formSchemas,
    showActionButtonGroup: false,
  });

  // 抽屉实例
  const [registerDrawer, { openDrawer, closeDrawer }] = useDrawerInner();

  // 工作流设计弹窗实例
  const [registerWorkflowModal, { openModal: openWorkflowModal, closeModal: closeWorkflowModal }] = useModalInner();

  // 流转规则弹窗实例
  const [registerTransitionModal, { openModal: openTransitionModal, closeModal: closeTransitionModal }] = useModalInner();

  // 流转规则表格
  const [registerTransitionTable] = useTable({
    dataSource: transitionData,
    columns: transitionColumns,
    useSearchForm: false,
    pagination: false,
  });

  // 加载状态数据
  async function loadStatusData() {
    if (!templateId?.value) return { list: [], total: 0 };

    loading.value = true;
    try {
      // 模拟数据
      const mockData = [
        {
          id: '1',
          statusName: '待办',
          statusCode: 'TODO',
          category: 'todo',
          isActive: true,
          sort: 1,
          color: '#f5f5f5',
          transitions: [{ to: 'progress', toName: '进行中' }],
          description: '任务创建后的初始状态',
          createdAt: '2025-01-15T10:00:00Z',
        },
        {
          id: '2',
          statusName: '进行中',
          statusCode: 'IN_PROGRESS',
          category: 'progress',
          isActive: true,
          sort: 2,
          color: '#1890ff',
          transitions: [
            { to: 'review', toName: '评审中' },
            { to: 'done', toName: '已完成' },
          ],
          description: '任务正在执行中',
          createdAt: '2025-01-15T10:00:00Z',
        },
        {
          id: '3',
          statusName: '评审中',
          statusCode: 'REVIEW',
          category: 'review',
          isActive: true,
          sort: 3,
          color: '#fa8c16',
          transitions: [
            { to: 'progress', toName: '进行中' },
            { to: 'done', toName: '已完成' },
          ],
          description: '任务完成后等待评审',
          createdAt: '2025-01-15T10:00:00Z',
        },
        {
          id: '4',
          statusName: '已完成',
          statusCode: 'DONE',
          category: 'done',
          isActive: true,
          sort: 4,
          color: '#52c41a',
          transitions: [],
          description: '任务已完成并通过验收',
          createdAt: '2025-01-15T10:00:00Z',
        },
      ];

      return { list: mockData, total: mockData.length };
    } catch (error) {
      console.error('加载状态数据失败:', error);
      createMessage.error('加载状态数据失败');
      return { list: [], total: 0 };
    } finally {
      loading.value = false;
    }
  }

  // 工具函数
  const getCategoryColor = (category: string) => {
    const colorMap = {
      todo: 'default',
      progress: 'blue',
      review: 'orange',
      done: 'green',
    };
    return colorMap[category] || 'default';
  };

  const getCategoryIcon = (category: string) => {
    const iconMap = {
      todo: ClockCircleOutlined,
      progress: PlayCircleOutlined,
      review: StopOutlined,
      done: CheckCircleOutlined,
    };
    return iconMap[category] || ClockCircleOutlined;
  };

  const getCategoryText = (category: string) => {
    const textMap = {
      todo: '待办',
      progress: '进行中',
      review: '评审',
      done: '完成',
    };
    return textMap[category] || '未知分类';
  };

  // 事件处理
  const handleSearch = () => {
    reload();
  };

  const handleCategoryFilter = () => {
    reload();
  };

  const handleAdd = () => {
    currentRecord.value = null;
    drawerTitle.value = '添加状态';
    resetFields();
    openDrawer();
  };

  const handleEdit = (record: any) => {
    currentRecord.value = record;
    drawerTitle.value = '编辑状态';
    setFieldsValue(record);
    openDrawer();
  };

  const handleCopy = (record: any) => {
    currentRecord.value = null;
    drawerTitle.value = '复制状态';
    const copyData = { ...record };
    delete copyData.id;
    copyData.statusName = `${record.statusName} - 副本`;
    copyData.statusCode = `${record.statusCode}_COPY`;
    setFieldsValue(copyData);
    openDrawer();
  };

  const handleTransitions = (record: any) => {
    currentTransitionRecord.value = record;
    // 初始化流转规则数据
    transitionData.value =
      record.transitions?.map(t => ({
        to: t.to,
        condition: t.condition || '',
      })) || [];
    openTransitionModal();
  };

  const handleDelete = (record: any) => {
    createMessage.success('删除成功');
    reload();
  };

  const handleSubmit = async () => {
    try {
      const values = await validate();
      console.log('提交状态数据:', values);

      createMessage.success('保存成功');
      closeDrawer();
      reload();
    } catch (error) {
      console.error('保存失败:', error);
      createMessage.error('保存失败');
    }
  };

  const handleWorkflowDesign = () => {
    openWorkflowModal();
  };

  const handleImportFromLibrary = () => {
    createMessage.info('从状态库导入功能开发中...');
  };

  const handleRefresh = () => {
    reload();
  };

  const handleSaveWorkflow = () => {
    createMessage.info('保存工作流功能开发中...');
  };

  const handleResetWorkflow = () => {
    createMessage.info('重置工作流功能开发中...');
  };

  const handleAddTransition = () => {
    transitionData.value.push({
      to: '',
      condition: '',
    });
  };

  const handleRemoveTransition = (index: number) => {
    transitionData.value.splice(index, 1);
  };

  const handleTransitionSubmit = () => {
    console.log('保存流转规则:', {
      statusId: currentTransitionRecord.value?.id,
      transitions: transitionData.value,
    });

    createMessage.success('流转规则保存成功');
    closeTransitionModal();
    reload();
  };

  onMounted(() => {
    console.log('状态配置页面挂载，模板ID:', templateId?.value);
  });
</script>

<style lang="less" scoped>
  .template-status-page {
    .status-icon {
      display: inline-flex;
      align-items: center;
      justify-content: center;
    }

    .transitions {
      .ant-tag {
        margin-bottom: 4px;
      }
    }

    .workflow-content {
      .workflow-designer {
        background: #fafafa;
        border-radius: 6px;
      }
    }

    .transition-content {
      .current-status {
        border-left: 4px solid #1890ff;
      }
    }
  }
</style>
