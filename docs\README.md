# XACE 项目开发文档

简化的文档结构，专为AI工具和开发效率优化。

## 📁 目录结构

```
docs/
├── framework-standards/    # XACE框架开发规范
├── project-guides/        # 项目级业务指引（按需添加）
├── ai-assistants/         # AI工具专用规则和命令
└── templates/             # 代码模板
```

## 🚀 快速开始

### 新开发者
1. **必读**：[AI核心规则](./ai-assistants/common/core-rules.md) - 5分钟掌握关键要点
2. **框架规范**：[framework-standards](./framework-standards/) - 完整开发规范
3. **AI工具配置**：[ai-assistants](./ai-assistants/) - 提升开发效率

### AI工具配置
- **Augment**：[自定义命令](./ai-assistants/augment/commands.md)
- **Claude**：[快速提示词](./ai-assistants/claude/quick-commands.md)
- **通用规则**：[核心规则摘要](./ai-assistants/common/core-rules.md)

## 🎯 核心特色

### AI友好设计
- 精简的文档结构便于AI检索
- 重点突出的规则摘要
- 专门优化的提示词和命令

### 实用导向
- 聚焦常用场景和易错点
- 提供快速生成命令
- 包含完整的检查清单

## ⚠️ 关键提醒

### 必须遵循的核心约定
1. **Jakarta EE**：`jakarta.*` 不是 `javax.*`
2. **ActionResult**：API统一返回格式
3. **BaseEntityV2**：实体类继承规范
4. **fullName/id**：前端组件数据格式

详见：[AI核心规则](./ai-assistants/common/core-rules.md)

---

**建议**：配置你的AI工具使用本文档体系，可显著提升开发效率和代码质量。