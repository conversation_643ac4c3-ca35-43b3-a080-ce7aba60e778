package com.xinghuo.performance.model.analysis;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 个人绩效分析VO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class PersonalPerformanceAnalysisVO {

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 分部ID
     */
    private String fbId;

    /**
     * 分部名称
     */
    private String fbName;

    /**
     * 绩效总分
     */
    private BigDecimal totalScore;

    /**
     * 排名
     */
    private Integer ranking;

    /**
     * 评分状态
     */
    private String status;

    /**
     * 评分周期
     */
    private String period;

    /**
     * 各维度得分（JSON格式）
     */
    private String dimensionScores;

    /**
     * 评分完成时间
     */
    private String completionTime;

    /**
     * 评分人
     */
    private String evaluatorName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 等级
     */
    private String grade;

    /**
     * 是否优秀（90分以上）
     */
    private Boolean isExcellent;

    /**
     * 同分部排名
     */
    private Integer departmentRanking;

    /**
     * 历史最高分
     */
    private BigDecimal historicalHighScore;

    /**
     * 历史平均分
     */
    private BigDecimal historicalAvgScore;
}
