<template>
  <div class="xh-common-layout">
    <a-card title="项目健康状态总览" :bordered="false" class="mb-4">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-statistic title="健康项目" :value="healthyCount" :precision="0">
            <template #suffix>
              <span style="color: #52c41a">个</span>
            </template>
          </a-statistic>
        </a-col>
        <a-col :span="6">
          <a-statistic title="警告项目" :value="warningCount" :precision="0">
            <template #suffix>
              <span style="color: #faad14">个</span>
            </template>
          </a-statistic>
        </a-col>
        <a-col :span="6">
          <a-statistic title="风险项目" :value="riskCount" :precision="0">
            <template #suffix>
              <span style="color: #f5222d">个</span>
            </template>
          </a-statistic>
        </a-col>
        <a-col :span="6">
          <a-statistic title="总项目数" :value="totalCount" :precision="0">
            <template #suffix>个</template>
          </a-statistic>
        </a-col>
      </a-row>
    </a-card>

    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'healthStatus'">
          <a-tag :color="getHealthColor(record.healthStatus)">
            {{ record.healthStatus }}
          </a-tag>
        </template>
        <template v-if="column.key === 'action'">
          <TableAction :actions="getTableActions(record)" />
        </template>
      </template>
    </BasicTable>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { getTableSchemas } from './tableSchema';
  import { useMessage } from '/@/hooks/web/useMessage';

  defineOptions({ name: 'ProgramProjectHealth' });

  const { createMessage } = useMessage();

  const projectList = ref([
    {
      id: '152068',
      projectName: '智能制造系统实施与优化项目',
      projectCode: '**********',
      projectManager: '高峰',
      healthStatus: '警告',
      scheduleStatus: '延期',
      budgetStatus: '正常',
      qualityStatus: '良好',
      riskLevel: '中',
      progressDeviation: -15,
      budgetDeviation: 8,
      currentPhase: '需求分析',
      phaseProgress: 30,
      deliverableProgress: 0,
      riskCount: 2,
      issueCount: 1,
      weeklyReport: '2025年第27周 07-06',
      stageColor: '#FF576E',
    },
    {
      id: '151649',
      projectName: '人力资源管理系统（HRMS）咨询项目',
      projectCode: '**********',
      projectManager: '王子豪,曹静',
      healthStatus: '风险',
      scheduleStatus: '延期',
      budgetStatus: '预警',
      qualityStatus: '需改进',
      riskLevel: '高',
      progressDeviation: -30,
      budgetDeviation: 15,
      currentPhase: '需求调研阶段',
      phaseProgress: 7,
      deliverableProgress: 0,
      riskCount: 3,
      issueCount: 2,
      weeklyReport: '2025年第27周 07-06',
      stageColor: '#FF576E',
    },
    {
      id: '150470',
      projectName: 'ERP系统优化项目',
      projectCode: '**********',
      projectManager: '陈静,王子豪',
      healthStatus: '健康',
      scheduleStatus: '正常',
      budgetStatus: '正常',
      qualityStatus: '优秀',
      riskLevel: '低',
      progressDeviation: 5,
      budgetDeviation: -3,
      currentPhase: '解决方案设计与规划',
      phaseProgress: 44,
      deliverableProgress: 0,
      riskCount: 0,
      issueCount: 0,
      weeklyReport: '2025年第27周 07-06',
      stageColor: '#FF576E',
    },
    {
      id: '150163',
      projectName: 'Demo-组织结构优化与流程再造项目',
      projectCode: '**********',
      projectManager: '陈静,Demo演示用户,PM',
      healthStatus: '健康',
      scheduleStatus: '正常',
      budgetStatus: '正常',
      qualityStatus: '优秀',
      riskLevel: '低',
      progressDeviation: 2,
      budgetDeviation: -5,
      currentPhase: '解决方案设计与规划',
      phaseProgress: 40,
      deliverableProgress: 64,
      riskCount: 1,
      issueCount: 1,
      weeklyReport: '2025年第27周 07-06',
      stageColor: '#FF576E',
    },
    {
      id: '151862',
      projectName: 'Demo-ERP系统实施与升级项目',
      projectCode: '**********',
      projectManager: '陈涛,Demo演示用户,PM',
      healthStatus: '警告',
      scheduleStatus: '轻微延期',
      budgetStatus: '预警',
      qualityStatus: '良好',
      riskLevel: '中',
      progressDeviation: -8,
      budgetDeviation: 12,
      currentPhase: '需求分析',
      phaseProgress: 46,
      deliverableProgress: 50,
      riskCount: 2,
      issueCount: 1,
      weeklyReport: '2025年第27周 07-06',
      stageColor: '#FF576E',
    },
    {
      id: '153001',
      projectName: '数字化办公平台建设项目',
      projectCode: '**********',
      projectManager: '李明,张华',
      healthStatus: '健康',
      scheduleStatus: '正常',
      budgetStatus: '正常',
      qualityStatus: '优秀',
      riskLevel: '低',
      progressDeviation: 0,
      budgetDeviation: 0,
      currentPhase: '项目启动',
      phaseProgress: 10,
      deliverableProgress: 15,
      riskCount: 0,
      issueCount: 0,
      weeklyReport: '2025年第27周 07-06',
      stageColor: '#52c41a',
    },
    {
      id: '153002',
      projectName: '客户关系管理系统升级项目',
      projectCode: '**********',
      projectManager: '刘强,赵敏',
      healthStatus: '健康',
      scheduleStatus: '提前完成',
      budgetStatus: '节约',
      qualityStatus: '优秀',
      riskLevel: '低',
      progressDeviation: 8,
      budgetDeviation: -5,
      currentPhase: '项目收尾',
      phaseProgress: 100,
      deliverableProgress: 100,
      riskCount: 0,
      issueCount: 0,
      weeklyReport: '2025年第27周 07-06',
      stageColor: '#52c41a',
    },
    {
      id: '153003',
      projectName: '供应链管理优化项目',
      projectCode: '**********',
      projectManager: '孙伟,钱丽',
      healthStatus: '风险',
      scheduleStatus: '严重延期',
      budgetStatus: '超支',
      qualityStatus: '需改进',
      riskLevel: '高',
      progressDeviation: -35,
      budgetDeviation: 25,
      currentPhase: '需求分析',
      phaseProgress: 25,
      deliverableProgress: 10,
      riskCount: 4,
      issueCount: 3,
      weeklyReport: '2025年第27周 07-06',
      stageColor: '#FF576E',
    },
  ]);

  const healthyCount = computed(() => projectList.value.filter(p => p.healthStatus === '健康').length);
  const warningCount = computed(() => projectList.value.filter(p => p.healthStatus === '警告').length);
  const riskCount = computed(() => projectList.value.filter(p => p.healthStatus === '风险').length);
  const totalCount = computed(() => projectList.value.length);

  const [registerTable] = useTable({
    api: async () => {
      return {
        list: projectList.value,
        pagination: {
          total: projectList.value.length,
        },
      };
    },
    columns: getTableSchemas(),
    useSearchForm: true,
    formConfig: {
      labelWidth: 100,
      schemas: [
        {
          field: 'projectName',
          label: '项目名称',
          component: 'Input',
          colProps: { span: 6 },
        },
        {
          field: 'healthStatus',
          label: '健康状态',
          component: 'Select',
          colProps: { span: 6 },
          componentProps: {
            options: [
              { label: '健康', value: '健康' },
              { label: '警告', value: '警告' },
              { label: '风险', value: '风险' },
            ],
          },
        },
        {
          field: 'currentPhase',
          label: '当前阶段',
          component: 'Select',
          colProps: { span: 6 },
          componentProps: {
            options: [
              { label: '项目启动', value: '项目启动' },
              { label: '需求分析', value: '需求分析' },
              { label: '需求调研阶段', value: '需求调研阶段' },
              { label: '解决方案设计与规划', value: '解决方案设计与规划' },
              { label: '项目收尾', value: '项目收尾' },
            ],
          },
        },
        {
          field: 'riskLevel',
          label: '风险等级',
          component: 'Select',
          colProps: { span: 6 },
          componentProps: {
            options: [
              { label: '低', value: '低' },
              { label: '中', value: '中' },
              { label: '高', value: '高' },
            ],
          },
        },
      ],
    },
    actionColumn: {
      width: 120,
      title: '操作',
      dataIndex: 'action',
    },
  });

  function getHealthColor(status: string) {
    const colorMap = {
      健康: 'success',
      警告: 'warning',
      风险: 'error',
    };
    return colorMap[status] || 'default';
  }

  function getTableActions(record: any) {
    return [
      {
        label: '查看详情',
        onClick: () => handleDetail(record),
      },
      {
        label: '健康报告',
        onClick: () => handleReport(record),
      },
    ];
  }

  function handleDetail(record: any) {
    createMessage.info(`查看项目健康详情: ${record.projectName}`);
  }

  function handleReport(record: any) {
    createMessage.info(`生成健康报告: ${record.projectName}`);
  }
</script>
