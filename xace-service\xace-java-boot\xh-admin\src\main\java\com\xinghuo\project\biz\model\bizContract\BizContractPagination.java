package com.xinghuo.project.biz.model.bizContract;

import com.xinghuo.common.base.model.Pagination;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 合同分页查询参数
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "合同分页查询参数")
public class BizContractPagination extends Pagination {

    /**
     * 合同名称
     */
    @Schema(description = "合同名称")
    private String name;

    /**
     * 合同编号
     */
    @Schema(description = "合同编号")
    private String cNo;

    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    private String custId;

    /**
     * 负责人ID
     */
    @Schema(description = "负责人ID")
    private String ownId;

    /**
     * 合同状态
     */
    @Schema(description = "合同状态")
    private String contractStatus;

    /**
     * 收款状态
     */
    @Schema(description = "收款状态")
    private String moneyStatus;

    /**
     * 签订年份
     */
    @Schema(description = "签订年份")
    private Integer signYear;

    /**
     * 最小金额
     */
    @Schema(description = "最小金额")
    private BigDecimal minAmount;

    /**
     * 最大金额
     */
    @Schema(description = "最大金额")
    private BigDecimal maxAmount;

    /**
     * 签订日期开始
     */
    @Schema(description = "签订日期开始")
    private Date signDateStart;

    /**
     * 签订日期结束
     */
    @Schema(description = "签订日期结束")
    private Date signDateEnd;

    /**
     * 部门ID
     */
    @Schema(description = "部门ID")
    private String deptId;

    /**
     * 基础项目ID
     */
    @Schema(description = "基础项目ID")
    private String projBaseId;

    /**
     * 项目ID
     */
    @Schema(description = "项目ID")
    private String projectId;
}
