<template>
  <div class="project-query">
    <!-- 查询类型标签页 -->
    <a-tabs v-model:activeKey="activeTab" @change="handleTabChange" class="query-tabs">
      <a-tab-pane key="RECENT_VISITED">
        <template #tab>
          <Icon icon="ant-design:clock-circle-outlined" />
          最近访问
        </template>
      </a-tab-pane>
      <a-tab-pane key="MY_FAVORITE">
        <template #tab>
          <Icon icon="ant-design:star-outlined" />
          我关注的
        </template>
      </a-tab-pane>
      <a-tab-pane key="ADVANCED">
        <template #tab>
          <Icon icon="ant-design:search-outlined" />
          高级查询
        </template>
      </a-tab-pane>
    </a-tabs>

    <!-- 搜索区域 -->
    <div class="search-area" v-if="activeTab === 'ADVANCED'">
      <BasicForm @register="registerForm" @submit="handleSearch" @reset="handleReset" />
    </div>

    <!-- 快速搜索 -->
    <div class="quick-search" v-else>
      <a-input-search
        v-model:value="quickSearchValue"
        placeholder="请输入项目名称或编码进行搜索"
        allow-clear
        enter-button="搜索"
        size="large"
        @search="handleQuickSearch"
        style="max-width: 400px; margin-bottom: 16px" />
    </div>

    <!-- 项目列表 -->
    <BasicTable @register="registerTable" :searchInfo="searchInfo">
      <template #toolbar>
        <a-button type="primary" @click="handleCreate" v-auth="'project:core:project:create'">
          <Icon size="13" icon="ant-design:plus-outlined" />
          新增项目
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'clarity:info-standard-line',
                tooltip: '查看详情',
                onClick: handleView.bind(null, record),
                auth: 'project:core:project:view',
              },
              {
                icon: 'clarity:note-edit-line',
                tooltip: '编辑',
                onClick: handleEdit.bind(null, record),
                auth: 'project:core:project:edit',
              },
              {
                icon: 'ant-design:delete-outlined',
                color: 'error',
                tooltip: '删除',
                popConfirm: {
                  title: '是否确认删除',
                  placement: 'left',
                  confirm: handleDelete.bind(null, record),
                },
                auth: 'project:core:project:delete',
              },
            ]"
            :dropDownActions="[
              {
                label: '更新状态',
                onClick: handleUpdateStatus.bind(null, record),
                auth: 'project:core:project:updateStatus',
              },
              {
                label: '更新健康度',
                onClick: handleUpdateHealth.bind(null, record),
                auth: 'project:core:project:updateHealth',
              },
              {
                label: record.status === 'archived' ? '激活项目' : '归档项目',
                onClick: record.status === 'archived' ? handleActivate.bind(null, record) : handleArchive.bind(null, record),
                auth: 'project:core:project:archive',
              },
            ]" />
        </template>
        <template v-else-if="column.key === 'status'">
          <Tag :color="getStatusColor(record.status)">
            {{ getStatusText(record.status) }}
          </Tag>
        </template>
        <template v-else-if="column.key === 'health'">
          <Tag :color="getHealthColor(record.health)">
            {{ getHealthText(record.health) }}
          </Tag>
        </template>
        <template v-else-if="column.key === 'priority'">
          <Tag :color="getPriorityColor(record.priority)">
            {{ getPriorityText(record.priority) }}
          </Tag>
        </template>
      </template>
    </BasicTable>
    <ProjectBaseModal @register="registerModal" @success="handleSuccess" />
  </div>
</template>

<script lang="ts" setup>
  import { reactive, ref, onMounted } from 'vue';
  import { Tag } from 'ant-design-vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { BasicForm, useForm } from '/@/components/Form';
  import { useModal } from '/@/components/Modal';
  import { Icon } from '/@/components/Icon';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { usePermission } from '/@/hooks/web/usePermission';

  import ProjectBaseModal from './ProjectBaseModal.vue';
  import { queryColumns, advancedSearchFormSchema, getColumnsForQueryType, queryTypeConfig, statusMap, healthMap, priorityMap } from './projectQuery.data';
  import { getProjectListByType, deleteProject, updateProjectStatus, updateProjectHealth, archiveProject, activateProject } from '/@/api/project/projectBase';

  defineOptions({ name: 'ProjectQuery' });

  const { createMessage } = useMessage();
  const { hasPermission } = usePermission();
  const [registerModal, { openModal }] = useModal();
  const searchInfo = reactive<Recordable>({});
  const activeTab = ref('RECENT_VISITED');
  const quickSearchValue = ref('');

  // 高级搜索表单
  const [registerForm, { getFieldsValue, resetFields }] = useForm({
    labelWidth: 120,
    schemas: advancedSearchFormSchema,
    autoSubmitOnEnter: true,
    submitButtonOptions: {
      text: '搜索',
    },
    resetButtonOptions: {
      text: '重置',
    },
    actionColOptions: {
      span: 24,
    },
    baseColProps: {
      span: 8,
    },
  });

  const [registerTable, { reload, setTableData, setColumns }] = useTable({
    title: '项目列表',
    api: getProjectListByType,
    rowKey: 'id',
    columns: getColumnsForQueryType(activeTab.value),
    useSearchForm: false,
    showTableSetting: true,
    bordered: true,
    beforeFetch: params => {
      return {
        ...params,
        queryType: activeTab.value,
        keyword: quickSearchValue.value,
      };
    },
    actionColumn: {
      width: 120,
      title: '操作',
      dataIndex: 'action',
      fixed: undefined,
    },
  });

  // 标签页切换
  function handleTabChange(key: string) {
    activeTab.value = key;
    quickSearchValue.value = '';
    if (key !== 'ADVANCED') {
      resetFields();
    }
    // 更新表格列配置
    setColumns(getColumnsForQueryType(key));
    reload();
  }

  // 快速搜索
  function handleQuickSearch(value: string) {
    quickSearchValue.value = value;
    reload();
  }

  // 高级搜索
  function handleSearch() {
    const values = getFieldsValue();
    Object.assign(searchInfo, values);
    reload();
  }

  // 重置搜索
  function handleReset() {
    Object.keys(searchInfo).forEach(key => {
      delete searchInfo[key];
    });
    reload();
  }

  function handleCreate() {
    openModal(true, {
      isUpdate: false,
    });
  }

  function handleEdit(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
    });
  }

  function handleView(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: false,
      isView: true,
    });
  }

  async function handleDelete(record: Recordable) {
    try {
      await deleteProject(record.id);
      createMessage.success('删除成功');
      reload();
    } catch (error) {
      createMessage.error('删除失败');
    }
  }

  function handleUpdateStatus(record: Recordable) {
    createMessage.info('状态更新功能待实现');
  }

  function handleUpdateHealth(record: Recordable) {
    createMessage.info('健康度更新功能待实现');
  }

  async function handleArchive(record: Recordable) {
    try {
      await archiveProject(record.id);
      createMessage.success('项目归档成功');
      reload();
    } catch (error) {
      createMessage.error('项目归档失败');
    }
  }

  async function handleActivate(record: Recordable) {
    try {
      await activateProject(record.id);
      createMessage.success('项目激活成功');
      reload();
    } catch (error) {
      createMessage.error('项目激活失败');
    }
  }

  function handleSuccess() {
    reload();
  }

  // 状态相关方法
  function getStatusColor(status: string) {
    return statusMap[status]?.color || 'default';
  }

  function getStatusText(status: string) {
    return statusMap[status]?.text || status;
  }

  function getHealthColor(health: string) {
    return healthMap[health]?.color || 'default';
  }

  function getHealthText(health: string) {
    return healthMap[health]?.text || health;
  }

  function getPriorityColor(priority: string) {
    return priorityMap[priority]?.color || 'default';
  }

  function getPriorityText(priority: string) {
    return priorityMap[priority]?.text || priority;
  }

  // 暴露给父组件的方法
  function switchToQueryType(queryType: string) {
    activeTab.value = queryType;
    quickSearchValue.value = '';
    if (queryType !== 'ADVANCED') {
      resetFields();
    }
    // 更新表格列配置
    setColumns(getColumnsForQueryType(queryType));
    reload();
  }

  // 暴露方法给父组件
  defineExpose({
    switchToQueryType,
    reload,
  });

  onMounted(() => {
    reload();
  });
</script>

<style lang="less" scoped>
  .project-query {
    .query-tabs {
      margin-bottom: 16px;

      :deep(.ant-tabs-tab) {
        .anticon {
          margin-right: 4px;
        }
      }
    }

    .search-area {
      background: #fafafa;
      padding: 16px;
      border-radius: 6px;
      margin-bottom: 16px;
    }

    .quick-search {
      margin-bottom: 16px;
    }
  }
</style>
