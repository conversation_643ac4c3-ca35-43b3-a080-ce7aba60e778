import { createMessage } from '/@/hooks/web/useMessage';
import { logger, DATE_FIELDS, AMOUNT_FIELDS } from '../constants';
import type { ContractModel } from '/@/api/project/contract';
import dayjs from 'dayjs';

/**
 * 统一错误处理
 */
export function handleApiError(error: any, context: string): void {
  logger.error(`${context}失败:`, error);
  const message = error?.response?.data?.msg || error?.message || `${context}失败`;
  createMessage.error(message);
}

/**
 * 格式化金额显示
 */
export function formatMoney(amount: number | string | undefined): string {
  if (!amount && amount !== 0) return '0.00';
  return Number(amount).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
}

/**
 * 格式化百分比显示
 */
export function formatPercent(value: number | undefined, precision = 1): string {
  if (!value && value !== 0) return '0%';
  return `${value.toFixed(precision)}%`;
}

/**
 * 提取合同数据的特定部分
 */
export function extractContractData(contract: ContractModel | null, section: string): Record<string, any> {
  if (!contract) return {};

  const data: Record<string, any> = {};

  switch (section) {
    case 'basic':
      return {
        name: contract.name || '',
        cno: contract.cno || '',
        contractStatus: contract.contractStatus || '',
        custId: contract.custId || '',
        finalUserId: contract.finalUserId || '',
        ownId: contract.ownId || '',
        deptId: contract.deptId || '',
        reportFrequency: contract.reportFrequency || '',
        signYear: contract.signYear || null,
      };

    case 'amount':
      AMOUNT_FIELDS.forEach(field => {
        data[field] = contract[field as keyof ContractModel] || 0;
      });
      data.moneyStatus = contract.moneyStatus || '';
      return data;

    case 'date':
      DATE_FIELDS.slice(0, 6).forEach(field => {
        data[field] = contract[field as keyof ContractModel] || undefined;
      });
      return data;

    case 'cycle':
      ['cstartDate', 'cendDate', 'mstartDate', 'mendDate'].forEach(field => {
        data[field] = contract[field as keyof ContractModel] || undefined;
      });
      return data;

    case 'contact':
      const contactFields = [
        'linkman',
        'linkTelephone',
        'svDeptId',
        'svLinkman',
        'svTelephone',
        'reviewDeptId',
        'reviewLinkman',
        'reviewTelephone',
        'dbDeptId',
        'dbLinkman',
        'dbTelephone',
        'smDeptId',
        'smLinkman',
        'smTelephone',
        'jsDeptId',
        'jsLinkman',
        'jsTelephone',
      ];
      contactFields.forEach(field => {
        data[field] = contract[field as keyof ContractModel] || '';
      });
      return data;

    case 'status':
      return {
        contractStatus: contract.contractStatus || '',
        moneyStatus: contract.moneyStatus || '',
        accdocStatus: contract.accdocStatus || '',
        isContinue: contract.isContinue || 0,
        isArchive: contract.isArchive || 0,
        workStatus: contract.workStatus || 0,
      };

    case 'profit':
      const profitFields = [
        'estProbit',
        'actProbit',
        'estProbitRatio',
        'actProbitRatio',
        'evaExternalAmount',
        'actExternalAmount',
        'evaCostAmount',
        'actCostAmount',
        'autoManhours',
        'autoRatioLevel',
      ];
      profitFields.forEach(field => {
        data[field] = contract[field as keyof ContractModel] || (field.includes('Ratio') || field === 'autoManhours' ? 0 : '');
      });
      return data;

    case 'note':
      return {
        note: contract.note || '',
        continueDesc: contract.continueDesc || '',
        accdocPath: contract.accdocPath || '',
        accdocNote: contract.accdocNote || '',
      };

    default:
      return {};
  }
}

/**
 * 处理日期字段
 * 将日期字符串转换为 dayjs 对象或反向转换
 */
export function processDateFields(data: Record<string, any>, direction: 'toForm' | 'toSubmit'): Record<string, any> {
  const result = { ...data };

  DATE_FIELDS.forEach(field => {
    if (data[field]) {
      if (direction === 'toForm') {
        // 转换为 dayjs 对象供表单使用
        result[field] = dayjs(data[field]);
      } else {
        // 转换为字符串供提交使用
        if (dayjs.isDayjs(data[field])) {
          result[field] = data[field].format('YYYY-MM-DD');
        }
      }
    }
  });

  return result;
}

/**
 * 等待表单就绪
 * 使用 Promise 和 requestAnimationFrame 替代 setTimeout
 */
export function waitForFormReady(setterFn: Function, formName: string, maxRetries = 20): Promise<boolean> {
  return new Promise(resolve => {
    let retries = 0;

    const checkReady = () => {
      try {
        // 尝试调用表单方法
        setterFn({});
        logger.log(`✅ ${formName} 表单就绪`);
        resolve(true);
      } catch (error) {
        retries++;
        if (retries >= maxRetries) {
          logger.warn(`⚠️ ${formName} 表单未能就绪`);
          resolve(false);
        } else {
          requestAnimationFrame(checkReady);
        }
      }
    };

    checkReady();
  });
}

/**
 * 批量设置表单数据
 */
export async function batchSetFormData(contract: ContractModel, formSetters: Record<string, { setter: Function; section: string }>): Promise<void> {
  const promises = Object.entries(formSetters).map(async ([name, { setter, section }]) => {
    const isReady = await waitForFormReady(setter, name);
    if (isReady) {
      const data = extractContractData(contract, section);
      const processedData = section.includes('date') || section === 'cycle' ? processDateFields(data, 'toForm') : data;
      await setter(processedData);
      logger.log(`✅ ${name} 数据设置完成`);
    }
  });

  await Promise.all(promises);
}

/**
 * 计算收款进度
 */
export function calculateReceiveProgress(received: number, total: number): number {
  if (total <= 0) return 0;
  return Math.min(Math.round((received / total) * 100), 100);
}

/**
 * 计算毛利率
 */
export function calculateProfitRate(profit: number, revenue: number): number {
  if (revenue <= 0) return 0;
  return Math.round((profit / revenue) * 100 * 100) / 100; // 保留两位小数
}

/**
 * 判断合同是否即将到期
 */
export function isContractExpiringSoon(endDate: string | undefined, days = 30): boolean {
  if (!endDate) return false;

  const end = dayjs(endDate);
  const now = dayjs();
  const daysLeft = end.diff(now, 'day');

  return daysLeft > 0 && daysLeft <= days;
}

/**
 * 获取合同风险等级
 */
export function getContractRiskLevel(contract: ContractModel): 'low' | 'medium' | 'high' {
  const risks = [];

  // 检查收款风险
  const receiveProgress = calculateReceiveProgress(contract.ysAmount, contract.amount);
  if (receiveProgress < 30) risks.push('payment');

  // 检查到期风险
  if (isContractExpiringSoon(contract.cendDate, 30)) risks.push('expiry');

  // 检查毛利率风险
  const profitRate = contract.actProbitRatio || contract.estProbitRatio || 0;
  if (profitRate < 10) risks.push('profit');

  // 根据风险数量判断等级
  if (risks.length === 0) return 'low';
  if (risks.length <= 2) return 'medium';
  return 'high';
}

/**
 * 生成合同统计数据
 */
export function generateContractStatistics(contracts: ContractModel[]): {
  totalAmount: number;
  receivedAmount: number;
  externalAmount: number;
  averageProfitRate: number;
  contractsByStatus: Record<string, number>;
} {
  const stats = contracts.reduce(
    (acc, contract) => {
      acc.totalAmount += contract.amount || 0;
      acc.receivedAmount += contract.ysAmount || 0;
      acc.externalAmount += contract.externalAmount || 0;

      const status = contract.contractStatus || 'unknown';
      acc.contractsByStatus[status] = (acc.contractsByStatus[status] || 0) + 1;

      const profitRate = contract.actProbitRatio || contract.estProbitRatio || 0;
      acc.profitRates.push(profitRate);

      return acc;
    },
    {
      totalAmount: 0,
      receivedAmount: 0,
      externalAmount: 0,
      contractsByStatus: {} as Record<string, number>,
      profitRates: [] as number[],
    },
  );

  const averageProfitRate = stats.profitRates.length > 0 ? stats.profitRates.reduce((a, b) => a + b, 0) / stats.profitRates.length : 0;

  return {
    totalAmount: stats.totalAmount,
    receivedAmount: stats.receivedAmount,
    externalAmount: stats.externalAmount,
    averageProfitRate: Math.round(averageProfitRate * 100) / 100,
    contractsByStatus: stats.contractsByStatus,
  };
}
