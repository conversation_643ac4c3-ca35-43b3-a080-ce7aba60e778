package com.xinghuo.project.schema.model.vo;

import com.xinghuo.project.schema.entity.ProjectSchemaWbsEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 项目模板WBS计划VO类
 * 包含基本信息和扩展字段
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectSchemaWbsVO extends ProjectSchemaWbsEntity {

    /**
     * 子节点列表
     */
    private List<ProjectSchemaWbsVO> children;

    /**
     * 是否有子节点
     */
    private Boolean hasChildren;

    /**
     * 子节点数量
     */
    private Integer childrenCount;

    /**
     * 项目模板名称（冗余字段，便于显示）
     */
    private String projectTemplateName;

    /**
     * 父级名称（冗余字段，便于显示）
     */
    private String parentName;

    /**
     * 源WBS模板名称（冗余字段，便于显示）
     */
    private String sourceWbsTemplateName;

    /**
     * 源活动库名称（冗余字段，便于显示）
     */
    private String sourceLibraryActivityName;

    /**
     * 责任角色名称（冗余字段，便于显示）
     */
    private String responseRoleName;

    /**
     * 约束类型名称（冗余字段，便于显示）
     */
    private String constraintTypeName;

    /**
     * 节点类型名称（冗余字段，便于显示）
     */
    private String nodeTypeName;

    /**
     * 里程碑标识名称（冗余字段，便于显示）
     */
    private String isMilestoneName;

    /**
     * 创建人姓名（冗余字段，便于显示）
     */
    private String creatorUserName;

    /**
     * 更新人姓名（冗余字段，便于显示）
     */
    private String updaterUserName;

    /**
     * WBS路径（从根节点到当前节点的路径）
     */
    private String wbsPath;

    /**
     * 层级路径（从根节点到当前节点的名称路径）
     */
    private String levelPath;

    /**
     * 是否为叶子节点
     */
    private Boolean isLeaf;

    /**
     * 是否为根节点
     */
    private Boolean isRoot;

    /**
     * 前置任务列表（解析后的对象列表）
     */
    private List<PredecessorVO> predecessorList;

    /**
     * 后续任务列表
     */
    private List<ProjectSchemaWbsVO> successorList;

    /**
     * 关键路径标识
     */
    private Boolean isCriticalPath;

    /**
     * 总浮动时间
     */
    private Integer totalFloat;

    /**
     * 自由浮动时间
     */
    private Integer freeFloat;

    /**
     * 计划开始时间（计算得出）
     */
    private String plannedStartDate;

    /**
     * 计划结束时间（计算得出）
     */
    private String plannedEndDate;

    /**
     * 实际开始时间
     */
    private String actualStartDate;

    /**
     * 实际结束时间
     */
    private String actualEndDate;

    /**
     * 完成百分比
     */
    private Integer completionPercentage;

    /**
     * 状态（计划中、进行中、已完成、已延期等）
     */
    private String status;

    /**
     * 状态名称
     */
    private String statusName;

    /**
     * 风险等级
     */
    private String riskLevel;

    /**
     * 风险等级名称
     */
    private String riskLevelName;

    /**
     * 备注信息
     */
    private String remarks;

    /**
     * 扩展属性（JSON格式）
     */
    private String extendedProperties;

    /**
     * 排序权重
     */
    private Integer sortWeight;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 前置任务VO类
     */
    @Data
    public static class PredecessorVO {
        /**
         * 前置任务ID
         */
        private String predecessorId;

        /**
         * 前置任务名称
         */
        private String predecessorName;

        /**
         * 关系类型（FS、SS、FF、SF）
         */
        private String relationType;

        /**
         * 滞后时间（天）
         */
        private Integer lagTime;

        /**
         * 关系类型名称
         */
        private String relationTypeName;
    }
}
