import { defHttp } from '/@/utils/http/axios';
import { ListResult } from '/@/api/model/baseModel';

/**
 * 采购合同管理API
 */

// API URL前缀 - 更新为新的架构路径
const API_PREFIX = '/api/project/biz/paymentContract';

/**
 * 采购合同对象接口
 */
export interface PaycontractModel {
  pcId: string;
  name: string;
  cno: string;
  cid: string;
  contractName?: string;
  contractNo?: string;
  suppilerId: string;
  supplierName?: string;
  ownId: string;
  ownName?: string;
  amount: number;
  yfAmount: number;
  moneyStatus: string;
  status: string;
  note: string;
  estSignDate: string;
  signDate: string;
  signYear: number;
  kfybAmount: number;
  kfebAmount: number;
  otherAmount: number;
  createUserId: string;
  createUserName?: string;
  createTime: string;
  lastModifiedUserId: string;
  lastModifiedUserName?: string;
  updateTime: string;
  deleteMark: number;
}

/**
 * 采购合同表单接口
 */
export interface PaycontractFormModel {
  name: string;
  cno?: string;
  cid: string;
  suppilerId: string;
  ownId?: string;
  amount: number;
  moneyStatus?: string;
  status?: string;
  note?: string;
  estSignDate?: string;
  signDate?: string;
  kfybAmount?: number;
  kfebAmount?: number;
  otherAmount?: number;
}

/**
 * 采购合同查询参数接口
 */
export interface PaycontractQueryParams {
  name?: string;
  cno?: string;
  cid?: string;
  suppilerId?: string;
  ownId?: string;
  status?: string;
  moneyStatus?: string;
  signYear?: number;
  minAmount?: number;
  maxAmount?: number;
  signDateStart?: string;
  signDateEnd?: string;
  keyword?: string;
  pageSize?: number;
  currentPage?: number;
}

/**
 * 获取采购合同列表
 * @param params 查询参数
 * @returns 采购合同列表
 */
export const getPaycontractList = (params?: PaycontractQueryParams) => {
  return defHttp.post<ListResult<PaycontractModel>>({
    url: `${API_PREFIX}/getList`,
    data: params,
  });
};

/**
 * 根据收款合同ID获取采购合同列表
 * @param contractId 收款合同ID
 * @returns 采购合同列表
 */
export const getPaycontractListByContractId = (contractId: string) => {
  return defHttp.get<PaycontractModel[]>({
    url: `${API_PREFIX}/contract/${contractId}`,
  });
};

/**
 * 根据供应商ID获取采购合同列表
 * @param supplierId 供应商ID
 * @returns 采购合同列表
 */
export const getPaycontractListBySupplierId = (supplierId: string) => {
  return defHttp.get<PaycontractModel[]>({
    url: `${API_PREFIX}/supplier/${supplierId}`,
  });
};

/**
 * 获取采购合同详情
 * @param id 采购合同ID
 * @returns 采购合同详情
 */
export const getPaycontractInfo = (id: string) => {
  return defHttp.get<PaycontractModel>({
    url: `${API_PREFIX}/${id}`,
  });
};

/**
 * 创建采购合同
 * @param params 采购合同创建参数
 * @returns 操作结果
 */
export const createPaycontract = (params: PaycontractFormModel) => {
  return defHttp.post<void>({
    url: API_PREFIX,
    data: params,
  });
};

/**
 * 更新采购合同
 * @param id 采购合同ID
 * @param params 采购合同更新参数
 * @returns 操作结果
 */
export const updatePaycontract = (id: string, params: PaycontractFormModel) => {
  return defHttp.put<void>({
    url: `${API_PREFIX}/${id}`,
    data: params,
  });
};

/**
 * 删除采购合同
 * @param id 采购合同ID
 * @returns 操作结果
 */
export const deletePaycontract = (id: string) => {
  return defHttp.delete<void>({
    url: `${API_PREFIX}/${id}`,
  });
};

/**
 * 更新采购合同状态
 * @param id 采购合同ID
 * @param status 采购合同状态
 * @returns 操作结果
 */
export const updatePaycontractStatus = (id: string, status: string) => {
  return defHttp.put<void>({
    url: `${API_PREFIX}/${id}/status/${status}`,
  });
};

/**
 * 签订采购合同
 * @param id 采购合同ID
 * @param cNo 采购合同编号
 * @param signDate 签订日期
 * @returns 操作结果
 */
export const signPaycontract = (id: string, cNo: string, signDate: string) => {
  return defHttp.put<void>({
    url: `${API_PREFIX}/${id}/sign`,
    params: {
      cNo,
      signDate,
    },
  });
};

/**
 * 检查采购合同编号是否已存在
 * @param cNo 采购合同编号
 * @param id 采购合同ID（更新时使用，新增时为null）
 * @returns 是否存在
 */
export const checkPaycontractCNo = (cNo: string, id?: string) => {
  return defHttp.get<boolean>({
    url: `${API_PREFIX}/check-cno`,
    params: {
      cNo,
      id,
    },
  });
};

/**
 * 采购合同选择器选项接口
 */
export interface PaycontractSelectorOption {
  id: string;
  fullName: string;
  name: string;
  cNo?: string;
}

/**
 * 获取采购合同选择器选项
 * @param keyword 搜索关键字
 * @returns 采购合同选择器选项列表
 */
export const getPaycontractSelector = (keyword?: string) => {
  return defHttp.get<PaycontractSelectorOption[]>({
    url: `${API_PREFIX}/selector`,
    params: {
      keyword,
    },
  });
};
