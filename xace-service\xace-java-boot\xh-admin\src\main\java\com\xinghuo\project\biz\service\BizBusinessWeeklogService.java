package com.xinghuo.project.biz.service;

import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.project.biz.entity.BizBusinessWeeklogEntity;
import com.xinghuo.project.biz.model.bizBusinessWeeklog.BizBusinessWeeklogAuditForm;
import com.xinghuo.project.biz.model.bizBusinessWeeklog.BizBusinessWeeklogHistoryVO;
import com.xinghuo.project.biz.model.bizBusinessWeeklog.BizBusinessWeeklogPagination;

import java.util.Date;
import java.util.List;

/**
 * 商机周报服务接口
 * 
 * <AUTHOR>
 * @date 2025-07-08
 */
public interface BizBusinessWeeklogService extends BaseService<BizBusinessWeeklogEntity> {

    /**
     * 分页查询商机周报列表
     *
     * @param pagination 查询条件
     * @return 商机周报列表
     */
    List<BizBusinessWeeklogEntity> getList(BizBusinessWeeklogPagination pagination);

    /**
     * 根据ID查询商机周报信息
     *
     * @param id 商机周报ID
     * @return 商机周报信息
     */
    BizBusinessWeeklogEntity getInfo(String id);

    /**
     * 创建商机周报
     *
     * @param entity 商机周报信息
     * @return 商机周报ID
     */
    String create(BizBusinessWeeklogEntity entity);

    /**
     * 更新商机周报
     *
     * @param id 商机周报ID
     * @param entity 更新信息
     */
    void update(String id, BizBusinessWeeklogEntity entity);

    /**
     * 删除商机周报
     *
     * @param id 商机周报ID
     */
    void delete(String id);

    /**
     * 根据时间段查询商机周报
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 商机周报列表
     */
    List<BizBusinessWeeklogEntity> getListByDateRange(Date startDate, Date endDate);

    /**
     * 根据项目经理/负责人查询商机周报
     *
     * @param ownId 负责人ID
     * @return 商机周报列表
     */
    List<BizBusinessWeeklogEntity> getListByOwnId(String ownId);

    /**
     * 根据项目ID查询历史商机周报记录
     * 按时间倒序排列，用于快速参考
     *
     * @param projId 项目ID
     * @return 历史商机周报记录
     */
    List<BizBusinessWeeklogHistoryVO> getHistoryByProjId(String projId);

    /**
     * 提交审核
     * 将状态从已填写(1)变更为提交审核(2)
     *
     * @param id 商机周报ID
     */
    void submitForAudit(String id);

    /**
     * 审核商机周报
     * 分部经理进行审核，状态变更为已发布(3)或已驳回(-1)
     *
     * @param auditForm 审核表单
     */
    void audit(BizBusinessWeeklogAuditForm auditForm);

    /**
     * 检查是否存在重复的商机周报
     * 根据项目ID和开始日期进行唯一性检查
     *
     * @param projId 项目ID
     * @param startDate 开始日期
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 是否存在重复
     */
    boolean isExistByProjIdAndStartDate(String projId, Date startDate, String excludeId);

    /**
     * 根据状态查询商机周报列表
     *
     * @param status 状态
     * @return 商机周报列表
     */
    List<BizBusinessWeeklogEntity> getListByStatus(Integer status);

    /**
     * 获取待审核的商机周报列表
     * 状态为提交审核(2)的记录
     *
     * @return 待审核商机周报列表
     */
    List<BizBusinessWeeklogEntity> getPendingAuditList();

    /**
     * 批量更新显示状态
     *
     * @param ids 商机周报ID列表
     * @param showStatus 显示状态
     */
    void batchUpdateShowStatus(List<String> ids, Integer showStatus);
}
