package com.xinghuo.project.template.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 标准交付物库实体类
 * 对应数据库表：zz_proj_workproduct_library
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("zz_proj_workproduct_library")
public class WorkProductLibraryEntity extends BaseEntityV2.CUBaseEntityV2<String> {

    /**
     * 交付物编码,可自动生成
     */
    @TableField("code")
    private String code;

    /**
     * 交付物名称
     */
    @TableField("name")
    private String name;

    /**
     * 描述
     */
    @TableField("description")
    private String description;

    /**
     * 交付物类型ID (关联字典表, 如: 文档, 软件)
     */
    @TableField("type_id")
    private String typeId;

    /**
     * 交付物子类型ID (关联字典表)
     */
    @TableField("sub_type_id")
    private String subTypeId;

    /**
     * 默认责任角色ID (关联角色库)
     */
    @TableField("default_role_id")
    private String defaultRoleId;

    /**
     * 是否需要评审 (1:是, 0:否)
     */
    @TableField("need_review")
    private Integer needReview;

    /**
     * 是否是项目最终交付成果 (1:是, 0:否)
     */
    @TableField("is_deliverable")
    private Integer isDeliverable;

    /**
     * 是否可裁剪 (1:可, 0:不可)
     */
    @TableField("can_cut")
    private Integer canCut;

    /**
     * 状态ID (关联字典表, 如: 启用, 禁用)
     */
    @TableField("status_id")
    private String statusId;
}
