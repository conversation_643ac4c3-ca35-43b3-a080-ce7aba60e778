<template>
  <div class="empty-template">
    <div class="empty-content">
      <div class="empty-icon">
        <Icon icon="ant-design:file-text-outlined" size="64" />
      </div>
      <div class="empty-title">暂无项目模板</div>
      <div class="empty-description">
        项目模板是项目管理的重要基础，用于标准化项目执行流程。
        <br />
        请先创建您的第一个项目模板。
      </div>
      <div class="empty-actions">
        <a-button type="primary" size="large" @click="handleCreate">
          <Icon icon="ant-design:plus-outlined" />
          新建项目模板
        </a-button>
        <a-button type="link" @click="handleLearnMore">
          <Icon icon="ant-design:question-circle-outlined" />
          了解更多
        </a-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { Icon } from '/@/components/Icon';

  const emit = defineEmits(['create', 'learn-more']);

  function handleCreate() {
    emit('create');
  }

  function handleLearnMore() {
    emit('learn-more');
  }
</script>

<style lang="less" scoped>
  .empty-template {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    background: #fafafa;
    border-radius: 8px;

    .empty-content {
      text-align: center;
      max-width: 400px;

      .empty-icon {
        margin-bottom: 16px;
        color: #d9d9d9;
      }

      .empty-title {
        margin-bottom: 8px;
        font-size: 16px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
      }

      .empty-description {
        margin-bottom: 24px;
        color: rgba(0, 0, 0, 0.45);
        line-height: 1.6;
      }

      .empty-actions {
        display: flex;
        justify-content: center;
        gap: 16px;

        .ant-btn-large {
          height: 40px;
          padding: 0 24px;
          font-size: 14px;
        }
      }
    }
  }
</style>
