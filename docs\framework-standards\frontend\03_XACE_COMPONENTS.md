# XACE 组件库使用规范

## 组件库概述

XACE 项目基于 Ant Design Vue 构建了完整的企业级组件库，提供了丰富的业务组件和可视化设计工具。

### 组件分类体系

| 组件类别 | 描述 | 特点 | 示例 |
|---------|------|------|------|
| **Xh 系列组件** | 业务增强组件 | 内置业务逻辑，开箱即用 | `XhUserSelect`, `XhSelect`, `XhDatePicker` |
| **Basic 组件** | 基础封装组件 | 功能强化，配置驱动 | `BasicForm`, `BasicTable`, `BasicModal` |
| **设计器组件** | 可视化设计工具 | 拖拽配置，动态生成 | `FormGenerator`, `VisualPortal` |
| **业务模态框** | 专用对话框组件 | 标准化业务流程 | `ExportModal`, `ImportModal` |

### 组件选择优先级

1. **优先使用 Xh 系列组件**：业务集成度高，数据格式标准化
2. **次选 Basic 组件**：功能强大，开发效率高
3. **特殊场景使用设计器组件**：动态配置需求

## 核心数据格式约定

### 字段映射规范

**重要**：XACE 项目中所有选择类组件默认使用统一的字段映射：

```typescript
// 默认字段映射
const defaultFieldNames = {
  label: 'fullName',    // 显示字段
  value: 'id',          // 值字段
  disabled: 'disabled'  // 禁用字段
}

// ✅ 正确的数据格式
const options = [
  { id: '1', fullName: '张三' },
  { id: '2', fullName: '李四' },
  { id: '3', fullName: '王五', disabled: true }
];

// ❌ 错误的数据格式
const wrongOptions = [
  { value: '1', label: '张三' }, // 错误！
  { value: '2', label: '李四' }  // 错误！
];
```

**设计原理**：
- 后端实体统一使用 `fullName` 字段表示显示名称
- 后端实体统一使用 `id` 作为主键
- 避免前端数据转换，提高性能
- 保持前后端数据模型一致性

## Xh 系列组件

### 基础表单组件

```vue
<template>
  <div>
    <!-- 通用选择器 -->
    <XhSelect 
      v-model:value="formData.typeId"
      :options="typeOptions"
      placeholder="请选择类型"
      :style="{ width: '200px' }"
    />
    
    <!-- 用户选择器 -->
    <XhUserSelect 
      v-model:value="formData.userId"
      placeholder="请选择用户"
      :multiple="false"
    />
    
    <!-- 部门选择器 -->
    <XhDepSelect 
      v-model:value="formData.departmentId"
      placeholder="请选择部门"
    />
    
    <!-- 日期选择器 -->
    <XhDatePicker
      v-model:value="formData.date"
      format="YYYY-MM-DD"
      placeholder="请选择日期"
    />
    
    <!-- 树形选择器 -->
    <XhTreeSelect
      v-model:value="formData.orgId"
      :tree-data="orgTree"
      placeholder="请选择组织"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { 
  XhSelect, 
  XhUserSelect, 
  XhDepSelect, 
  XhDatePicker,
  XhTreeSelect 
} from '/@/components/Xh';

const formData = ref({
  typeId: '',
  userId: '',
  departmentId: '',
  date: '',
  orgId: ''
});

// 选项数据必须使用 fullName/id 格式
const typeOptions = ref([
  { id: '1', fullName: '类型一' },
  { id: '2', fullName: '类型二' },
  { id: '3', fullName: '类型三' }
]);
</script>
```

### 自定义字段映射

当数据源不符合默认格式时，可以自定义字段映射：

```vue
<template>
  <!-- 自定义字段映射 -->
  <XhSelect 
    v-model:value="selectedValue"
    :options="customOptions"
    :field-names="{ label: 'name', value: 'code' }"
    placeholder="自定义映射"
  />
</template>

<script setup lang="ts">
const customOptions = ref([
  { code: 'A01', name: '选项A' },
  { code: 'B02', name: '选项B' }
]);

const fieldNames = {
  label: 'name',    // 自定义显示字段
  value: 'code',    // 自定义值字段
  disabled: 'disabled'
};
</script>
```

## Xh 系列组件详解

### XhDatePicker 日期选择器

XhDatePicker 是项目中的标准日期选择组件，基于 Ant Design DatePicker 封装。

#### 重要特性

**值类型要求**：
- ✅ 接受：`Number`（时间戳）| `String`（日期字符串）
- ❌ 不接受：`dayjs` 对象、`Date` 对象

**自动处理能力**：
- 自动识别时间戳格式（10位秒级或13位毫秒级）
- 自动转换日期字符串格式
- 内置格式化和显示逻辑

#### 基础用法

```vue
<template>
  <!-- 基础日期选择 -->
  <XhDatePicker
    v-model:value="formData.date"
    format="YYYY-MM-DD"
    placeholder="请选择日期"
  />

  <!-- 日期时间选择 -->
  <XhDatePicker
    v-model:value="formData.datetime"
    format="YYYY-MM-DD HH:mm:ss"
    :show-time="true"
    placeholder="请选择日期时间"
  />

  <!-- 时间戳模式 -->
  <XhDatePicker
    v-model:value="formData.timestamp"
    :use-timestamp="true"
    format="YYYY-MM-DD"
    placeholder="输出时间戳"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { XhDatePicker } from '/@/components/Xh';

const formData = ref({
  date: '',           // 字符串格式
  datetime: '',       // 日期时间字符串
  timestamp: null     // 时间戳数字
});
</script>
```

#### 表单中的使用

```typescript
// ✅ 正确：表单 Schema 配置
const formSchema: FormSchema[] = [
  {
    field: 'plannedStartDate',
    label: '计划开始日期',
    component: 'DatePicker', // 映射到 XhDatePicker
    componentProps: {
      format: 'YYYY-MM-DD',
      placeholder: '请选择计划开始日期',
    },
  },
];

// ✅ 正确：设置表单值
setFieldsValue({
  plannedStartDate: 1761317600000, // 直接传递时间戳
  // 或
  plannedStartDate: '2024-01-15',  // 直接传递日期字符串
});

// ❌ 错误：传递 dayjs 对象
setFieldsValue({
  plannedStartDate: dayjs(timestamp), // 会导致类型错误
});
```

#### 常见问题解决

**问题1：类型错误警告**
```
[Vue warn]: Invalid prop: type check failed for prop "value". Expected Number | String, got Object
```

**解决方案**：
```typescript
// ❌ 错误做法
const processedData = {
  ...rawData,
  plannedStartDate: dayjs(rawData.plannedStartDate) // 错误！
};

// ✅ 正确做法
const processedData = {
  ...rawData,
  plannedStartDate: rawData.plannedStartDate // 直接使用原始值
};
```

**问题2：显示格式化**
```typescript
// ✅ 使用统一工具函数进行显示格式化
import { formatToDate } from '/@/utils/dateUtil';

// 在模板中显示
const displayDate = formatToDate(timestamp); // 自动处理各种输入类型
```

#### 属性配置

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `value` | `Number \| String` | - | 组件值，支持时间戳或日期字符串 |
| `format` | `String` | `'YYYY-MM-DD'` | 显示格式 |
| `useTimestamp` | `Boolean` | `true` | 是否输出时间戳 |
| `showTime` | `Boolean \| Object` | `false` | 是否显示时间选择 |
| `startTime` | `Number` | `null` | 可选择的最早时间 |
| `endTime` | `Number` | `null` | 可选择的最晚时间 |

## Basic 组件系列

### BasicForm 表单组件

BasicForm 提供 Schema 驱动的表单解决方案：
component取值来源文件：xace-web\xh-web-vue3\src\components\Form\src\types\index.ts ComponentType

```vue
<template>
  <BasicForm @register="register" @submit="handleSubmit" />
</template>

<script setup lang="ts">
import { BasicForm, useForm, FormSchema } from '/@/components/Form';

// 表单 Schema 配置
const schemas: FormSchema[] = [
  {
    field: 'name',
    label: '姓名',
    component: 'Input',
    componentProps: {
      placeholder: '请输入姓名'
    },
    rules: [{ required: true, message: '请输入姓名' }]
  },
  {
    field: 'userId',
    label: '负责人',
    component: 'XhUserSelect',  // 使用 Xh 组件
    componentProps: {
      placeholder: '请选择负责人',
      multiple: false
    }
  },
  {
    field: 'departmentId',
    label: '所属部门',
    component: 'XhDepSelect',
    componentProps: {
      placeholder: '请选择部门'
    }
  },
  {
    field: 'status',
    label: '状态',
    component: 'XhSelect',
    componentProps: {
      options: [
        { id: '1', fullName: '启用' },
        { id: '0', fullName: '禁用' }
      ]
    }
  }
];

const [register, { validate, setFieldsValue, resetFields }] = useForm({
  labelWidth: 100,
  schemas,
  showResetButton: true,
  showSubmitButton: true,
  resetButtonOptions: { text: '重置' },
  submitButtonOptions: { text: '保存' }
});

async function handleSubmit(values: any) {
  try {
    console.log('表单数据:', values);
    // 提交逻辑
  } catch (error) {
    console.error('提交失败:', error);
  }
}
</script>
```

一个表单字段映射成两个字段，如：数字范围，时间范围
```vue
<template>
  ...
</template>

<script setup lang="ts">
import { BasicForm, useForm, FormSchema } from '/@/components/Form';

const [register, { validate, setFieldsValue, resetFields }] = useForm({
  // ...
  schemas: [
    {
      field: 'seqNoRange',
      label: '序号范围',
      component: 'NumberRange',
    },
    {
      field: 'durationRange',
      label: '时长范围',
      component: 'DateRange',
    },
  ],
  // 字段映射到时间范围
  fieldMapToTime: [
    ['seqNoRange', ['seqNoRangeMin', 'seqNoRangeMax']],
    ['durationRange', ['durationRangeMin', 'durationRangeMax']],
  ]
});
</script>
```

### BasicTable 表格组件

BasicTable 提供功能强大的数据表格解决方案：

```vue
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" @click="handleAdd">新增</a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'clarity:note-edit-line',
                label: '编辑',
                onClick: handleEdit.bind(null, record)
              },
              {
                icon: 'ant-design:delete-outlined',
                color: 'error',
                label: '删除',
                popConfirm: {
                  title: '是否确认删除',
                  confirm: handleDelete.bind(null, record)
                }
              }
            ]"
          />
        </template>
      </template>
    </BasicTable>
  </div>
</template>

<script setup lang="ts">
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import { getUserList } from '/@/api/modules/user';

// 表格列配置
const columns = [
  {
    title: '姓名',
    dataIndex: 'fullName',
    width: 120
  },
  {
    title: '账号',
    dataIndex: 'account',
    width: 120
  },
  {
    title: '部门',
    dataIndex: 'departmentName',
    width: 150
  },
  {
    title: '状态',
    dataIndex: 'enabledMark',
    width: 80,
    customRender: ({ text }) => text === 1 ? '启用' : '禁用'
  }
];

// 表格配置
const [registerTable, { reload, deleteTableDataRecord }] = useTable({
  title: '用户列表',
  columns,
  // 操作列配置（关键：不要在 columns 中定义）
  actionColumn: {
    width: 200,
    title: '操作',
    dataIndex: 'action',
  },
  api: getUserList,  // API 函数
  useSearchForm: true,  // 启用搜索表单
  clickToRowSelect: false,
  searchInfo: {
    // 默认搜索参数
  },
  beforeFetch: (params) => {
    // 请求前参数处理
    return params;
  }
});

function handleAdd() {
  // 新增逻辑
}

function handleEdit(record: any) {
  // 编辑逻辑
}

function handleDelete(record: any) {
  // 删除逻辑
  deleteTableDataRecord(record.id);
}
</script>
```

**重要**：API 函数必须返回 ActionResult 格式：

```typescript
// ✅ 正确的 API 返回格式
interface ActionResult<T> {
  code: number;     // 200 表示成功
  msg: string;      // 响应消息
  data: {
    list: T[];      // 数据列表
    pagination: {   // 分页信息
      current: number;
      pageSize: number;
      total: number;
    };
  };
}

// API 函数示例
export async function getUserList(params: any): Promise<ActionResult<UserInfo>> {
  return request.post('/api/user/list', params);
}
```

### BasicModal 弹窗组件

```vue
<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    :title="getTitle"
    :width="600"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { BasicForm, useForm } from '/@/components/Form';

const emit = defineEmits(['success', 'register']);

const isUpdate = ref(false);
const recordId = ref('');

const getTitle = computed(() => 
  !isUpdate.value ? '新增用户' : '编辑用户'
);

// 表单配置（与 BasicForm 相同）
const schemas = [
  // ... 表单字段配置
];

const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
  labelWidth: 90,
  schemas,
  showActionButtonGroup: false
});

const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  resetFields();
  setModalProps({ confirmLoading: false });
  
  isUpdate.value = !!data?.isUpdate;
  
  if (data.record) {
    recordId.value = data.record.id;
    setFieldsValue(data.record);
  }
});

async function handleSubmit() {
  try {
    const values = await validate();
    if (!values) return;
    setModalProps({ confirmLoading: true });
    
    // 提交逻辑
    // await saveUser(values);
    
    closeModal();
    emit('success');
  } catch (error) {
    console.error('保存失败:', error);
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
```

## Ant Design Vue 按需导入

### 推荐导入方式

```typescript
// 组件内局部注册
import { 
  Form, 
  FormItem,
  Input, 
  Select, 
  Button,
  Table,
  Modal
} from 'ant-design-vue';

// 在组件中使用
export default defineComponent({
  components: {
    AForm: Form,
    AFormItem: FormItem,
    AInput: Input,
    ASelect: Select,
    AButton: Button,
    ATable: Table,
    AModal: Modal
  }
});
```

### 常用组件清单

#### 表单相关
- `Form`, `FormItem`
- `Input`, `InputNumber`, `Textarea`
- `Select`, `TreeSelect`, `Cascader`
- `DatePicker`, `TimePicker`, `RangePicker`
- `Switch`, `Checkbox`, `Radio`
- `Upload`, `AutoComplete`

#### 数据展示
- `Table`, `Descriptions`, `List`
- `Badge`, `Avatar`, `Tag`
- `Tabs`, `TabPane`
- `Empty`, `Skeleton`

#### 布局导航
- `Layout`, `Header`, `Sider`, `Content`
- `Row`, `Col`, `Space`, `Divider`
- `Menu`, `Breadcrumb`, `Pagination`

#### 反馈交互
- `Modal`, `Drawer`
- `Tooltip`, `Popover`, `Dropdown`
- `Message`, `Notification`
- `Progress`, `Spin`

## 样式规范

### CSS 类命名约定

```scss
// 使用 xh- 前缀
.xh-user-card {
  &__header {
    // BEM 命名法
  }
  
  &__content {
    // 内容样式
  }
  
  &--disabled {
    // 修饰符样式
  }
}

// 响应式设计
.xh-form {
  @media (max-width: 768px) {
    // 移动端适配
  }
}
```

### 主题变量使用

```scss
// 使用主题变量
.xh-component {
  color: var(--primary-color);
  background: var(--component-background);
  border: 1px solid var(--border-color-base);
}

// 支持暗黑模式
[data-theme='dark'] .xh-component {
  background: var(--component-background-dark);
}
```

## 常见问题与解决方案

### 1. 组件数据不显示

**问题**：选择器组件下拉选项为空

```typescript
// ❌ 错误：数据格式不对
const options = [
  { value: '1', label: '选项一' }  // 错误格式
];

// ✅ 正确：使用标准格式
const options = [
  { id: '1', fullName: '选项一' }  // 正确格式
];
```

### 2. 表格数据加载失败

**问题**：BasicTable 显示空数据

```typescript
// ❌ 错误：API 返回格式不对
{
  success: true,
  data: [...],  // 错误格式
  total: 100
}

// ✅ 正确：ActionResult 格式
{
  code: 200,
  msg: '查询成功',
  data: {
    list: [...],     // 数据列表
    pagination: {    // 分页信息
      current: 1,
      pageSize: 10,
      total: 100
    }
  }
}
```

### 3. 表单组件不生效

**问题**：BasicForm 中组件无法正常显示
component取值来源文件：xace-web\xh-web-vue3\src\components\Form\src\types\index.ts ComponentType

```ts
// component 组件类型
export type ComponentType =
  | 'InputGroup'
  | 'InputSearch'
  | 'InputCountDown'
  | 'AutoComplete'
  | 'MonthPicker'
  | 'WeekPicker'
  | 'StrengthMeter'
  | 'IconPicker'
  | 'Render'
  | 'Alert'
  | 'AreaSelect'
  | 'Button'
  | 'Cron'
  | 'Cascader'
  | 'ColorPicker'
  | 'Checkbox'
  | 'XhCheckboxSingle'
  | 'DatePicker'
  | 'DateRange'
  | 'TimePicker'
  | 'TimeRange'
  | 'Divider'
  | 'Editor'
  | 'GroupTitle'
  | 'Input'
  | 'InputPassword'
  | 'Textarea'
  | 'InputNumber'
  | 'Link'
  | 'OrganizeSelect'
  | 'DepSelect'
  | 'PosSelect'
  | 'GroupSelect'
  | 'RoleSelect'
  | 'UserSelect'
  | 'UserSelectDropdown'
  | 'UsersSelect'
  | 'Qrcode'
  | 'Barcode'
  | 'Radio'
  | 'Rate'
  | 'Select'
  | 'Slider'
  | 'Sign'
  | 'Switch'
  | 'Text'
  | 'TreeSelect'
  | 'UploadFile'
  | 'UploadImg'
  | 'UploadImgSingle'
  | 'RelationForm'
  | 'RelationFormAttr'
  | 'PopupSelect'
  | 'PopupTableSelect'
  | 'PopupAttr'
  | 'NumberRange'
  | 'Calculate'
  | 'InputTable'
  | 'BillRule'
  | 'ModifyUser'
  | 'ModifyTime'
  | 'CreateUser'
  | 'CreateTime'
  | 'CurrOrganize'
  | 'SysVars'
  | 'ApiSelect'
  | 'CurrPosition';
```

```typescript
// ✅ 正确：使用完整组件名
{
  component: 'UserSelect',  // 正确
}

// ❌ 错误：组件名称不正确

{
  component: 'XhUserSelect',  // 错误
}
```

### 组件使用优先级

1. 优先使用 Xh 系列组件
2. 其次使用vbenComponents组件，参考[vbenComponents](https://vbenjs.github.io/vben-admin-doc/components/table)
3. 最后使用ant-design-vue组件，参考[ant-design-vue](https://3x.antdv.com/docs/vue/introduce-cn)

```vue
<!-- ✅ 正确：优先使用vbenComponents组件 -->
<template>
  <BasicDrawer v-bind="$attrs" title="Drawer Title" width="50%"> Drawer Info. </BasicDrawer>
</template>
<script lang="ts">
  import { defineComponent } from 'vue';
  import { BasicDrawer } from '/@/components/Drawer';
  export default defineComponent({
    components: { BasicDrawer },
  });
</script>
```

```vue
<!-- ❌ 错误：vbenComponents有drawer组件，不应该使用ant-design-vue组件 -->
<template>
  <a-button type="primary" @click="showDrawer">Open</a-button>
  <a-drawer
    v-model:visible="visible"
    class="custom-class"
    style="color: red"
    title="Basic Drawer"
    placement="right"
    @after-visible-change="afterVisibleChange"
  >
  </a-drawer>
</template>
<script lang="ts">
import { defineComponent, ref } from 'vue';
export default defineComponent({
  setup() {
    const visible = ref<boolean>(false);

    const afterVisibleChange = (bool: boolean) => {
      console.log('visible', bool);
    };

    const showDrawer = () => {
      visible.value = true;
    };

    return {
      visible,
      afterVisibleChange,
      showDrawer,
    };
  },
});
</script>
```

```vue
<!-- ✅ 正确：优先使用vbenComponents组件 -->
<template>
  <BasicModal v-bind="$attrs" title="Modal Title" :helpMessage="['提示1', '提示2']">
    Modal Info.
  </BasicModal>
</template>
<script lang="ts">
  import { defineComponent } from 'vue';
  import { BasicModal } from '/@/components/Modal';
  export default defineComponent({
    components: { BasicModal },
    setup() {
      return {};
    },
  });
</script>
```

```vue
<!-- ❌ 错误：vbenComponents有modal组件，不应该使用ant-design-vue组件 -->
<template>
  <div>
    <a-button type="primary" @click="showModal">Open Modal</a-button>
    <a-modal v-model:visible="visible" title="Basic Modal" @ok="handleOk">
      <p>Some contents...</p>
      <p>Some contents...</p>
      <p>Some contents...</p>
    </a-modal>
  </div>
</template>
<script lang="ts">
import { defineComponent, ref } from 'vue';
export default defineComponent({
  setup() {
    const visible = ref<boolean>(false);

    const showModal = () => {
      visible.value = true;
    };

    const handleOk = (e: MouseEvent) => {
      console.log(e);
      visible.value = false;
    };
    return {
      visible,
      showModal,
      handleOk,
    };
  },
});
</script>
```

```vue
<!-- ✅ 正确：优先使用vbenComponents组件 -->
<template>
  <BasicTable @register="registerTable" />
</template>
<script lang="ts">
  import { defineComponent } from 'vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import { getBasicColumns, getBasicShortColumns } from './tableData';
  import { demoListApi } from '/@/api/demo/table';
  export default defineComponent({
    components: { BasicTable },
    setup() {
      const [
        registerTable,
        {
          setLoading,
        },
      ] = useTable({
        api: demoListApi,
        columns: getBasicColumns(),
      });

      function changeLoading() {
        setLoading(true);
        setTimeout(() => {
          setLoading(false);
        }, 1000);
      }
      }
      return {
        registerTable,
        changeLoading,
      };
    },
  });
</script>
```

```vue
<!-- ❌ 错误：vbenComponents有table组件，不应该使用ant-design-vue组件 -->
<template>
  <a-table :dataSource="dataSource" :columns="columns" />
</template>
<script>
  export default {
    setup() {
      return {
        dataSource: [
          {
            key: '1',
            name: '胡彦斌',
            age: 32,
            address: '西湖区湖底公园1号',
          },
          {
            key: '2',
            name: '胡彦祖',
            age: 42,
            address: '西湖区湖底公园1号',
          },
        ],

        columns: [
          {
            title: '姓名',
            dataIndex: 'name',
            key: 'name',
          },
          {
            title: '年龄',
            dataIndex: 'age',
            key: 'age',
          },
          {
            title: '住址',
            dataIndex: 'address',
            key: 'address',
          },
        ],
      };
    },
  };
</script>
```

```vue
<!-- ✅ 正确：优先使用vbenComponents组件 -->
<template>
  <div class="p-4">
    <Description
      title="基础示例"
      :collapseOptions="{ canExpand: true, helpMessage: 'help me' }"
      :column="3"
      :data="mockData"
      :schema="schema"
    />
    <Description @register="register" class="mt-4" />
  </div>
</template>
<script lang="ts">
  import { defineComponent } from 'vue';
  import { Alert } from 'ant-design-vue';
  import { Description, DescItem, useDescription } from '/@/components/Description/index';
  const mockData: any = {
    username: 'test',
    nickName: 'VB',
    age: 123,
    phone: '15695909xxx',
    email: '<EMAIL>',
    addr: '厦门市思明区',
    sex: '男',
    certy: '3504256199xxxxxxxxx',
    tag: 'orange',
  };
  const schema: DescItem[] = [
    {
      field: 'username',
      label: '用户名',
    },
    {
      field: 'nickName',
      label: '昵称',
      render: (curVal, data) => {
        return `${data.username}-${curVal}`;
      },
    },
    {
      field: 'phone',
      label: '联系电话',
    },
    {
      field: 'email',
      label: '邮箱',
    },
    {
      field: 'addr',
      label: '地址',
    },
  ];
  export default defineComponent({
    components: { Description, Alert },
    setup() {
      const [register] = useDescription({
        title: 'useDescription',
        data: mockData,
        schema: schema,
      });
      return { mockData, schema, register };
    },
  });
</script>
```

```vue
<!-- ❌ 错误：vbenComponents有description组件，不应该使用ant-design-vue组件 -->
<template>
  <a-descriptions title="User Info">
    <a-descriptions-item label="UserName">Zhou Maomao</a-descriptions-item>
    <a-descriptions-item label="Telephone">1810000000</a-descriptions-item>
    <a-descriptions-item label="Live">Hangzhou, Zhejiang</a-descriptions-item>
    <a-descriptions-item label="Remark">empty</a-descriptions-item>
    <a-descriptions-item label="Address">
      No. 18, Wantang Road, Xihu District, Hangzhou, Zhejiang, China
    </a-descriptions-item>
  </a-descriptions>
</template>
```

### 全局引入组件
项目未完全引入ant-design-vue组件，部分组件如使用需要先手动引入。

#### 已全局引入的组件，无需再次引入
```ts
import {
  Input,
  InputNumber,
  Layout,
  Form,
  Switch,
  Dropdown,
  Menu,
  Select,
  Table,
  Checkbox,
  Tabs,
  Collapse,
  Card,
  Tooltip,
  Row,
  Col,
  Popconfirm,
  Divider,
  Alert,
  AutoComplete,
  Cascader,
  Rate,
  Slider,
  Avatar,
  Tag,
  Space,
  Steps,
  Popover,
  Radio,
  Progress,
  Image,
  Upload,
  Statistic,
  List,
  Spin,
  Empty,
} from 'ant-design-vue';
```

#### 完全未引入的组件类别

为引用全局的组件使用前需要先引入，如：

```vue 
<!-- ✅ 正确：先引入组件 -->
<template>
  <a-back-top />
  Scroll down to see the bottom-right
  <strong style="color: rgba(64, 64, 64, 0.6)">gray</strong>
  button.
</template>

<script lang="ts" setup>
import {BackTop as ABackTop} from 'ant-design-vue';
</script>
```

```vue 
<!-- ❌ 错误：BackTop组件未引入 -->
<template>
  <a-back-top />
  Scroll down to see the bottom-right
  <strong style="color: rgba(64, 64, 64, 0.6)">gray</strong>
  button.
</template>

<script lang="ts" setup>
</script>
```


+ 这些是整个组件（包括其子组件）都没有在全局引入列表中的。
Affix (固钉)
Anchor, AnchorLink (锚点)
BackTop (回到顶部)
Badge, BadgeRibbon (徽标数)
Breadcrumb, BreadcrumbItem, BreadcrumbSeparator (面包屑)
Button, ButtonGroup (按钮)
Calendar (日历)
Carousel (走马灯)
Comment (评论)
ConfigProvider (全局化配置)
DatePicker, MonthPicker, WeekPicker, RangePicker, QuarterPicker (日期选择框)
Descriptions, DescriptionsItem (描述列表)
Drawer (抽屉)
Grid (栅格) - 注意：Grid 是一个独立的组件，Row 和 Col 已经被你引入了。
Mentions, MentionsOption (提及)
Modal (对话框)
PageHeader (页头)
Pagination (分页)
Result (结果)
Skeleton, SkeletonButton, SkeletonAvatar, SkeletonInput, SkeletonImage, SkeletonTitle (骨架屏)
Transfer (穿梭框)
Tree, TreeNode, DirectoryTree (树形控件)
TreeSelect, TreeSelectNode (树选择)
TimePicker, TimeRangePicker (时间选择框)
Timeline, TimelineItem (时间轴)
Typography, TypographyLink, TypographyParagraph, TypographyText, TypographyTitle (排版)
LocaleProvider (国际化)
+ 方法/函数式组件
这些不是 Vue 组件，而是需要通过 JS 调用的方法，它们也没有被引入。
message (全局提示)
notification (通知提醒框)
+ 已引入主组件，但其子组件未引入
你已经引入了这些组件的主体，但它们的子组件或关联组件没有被引入。在模板中使用这些功能时可能会报错。
AutoComplete: 缺少 AutoCompleteOptGroup, AutoCompleteOption
Avatar: 缺少 AvatarGroup
Card: 缺少 CardGrid, CardMeta
Collapse: 缺少 CollapsePanel
Checkbox: 缺少 CheckboxGroup
Dropdown: 缺少 DropdownButton
Form: 缺少 FormItem, FormItemRest
Input: 缺少 InputGroup, InputPassword, InputSearch, Textarea
Image: 缺少 ImagePreviewGroup
Layout: 缺少 LayoutHeader, LayoutSider, LayoutFooter, LayoutContent
List: 缺少 ListItem, ListItemMeta
Menu: 缺少 MenuDivider, MenuItem, MenuItemGroup, SubMenu
Radio: 缺少 RadioButton, RadioGroup
Select: 缺少 SelectOptGroup, SelectOption
Statistic: 缺少 StatisticCountdown
Steps: 缺少 Step
Table: 缺少 TableColumn, TableColumnGroup, TableSummary, TableSummaryRow, TableSummaryCell
Tabs: 缺少 TabPane
Tag: 缺少 CheckableTag
Upload: 缺少 UploadDragger

## 性能优化建议

### 1. 组件懒加载

```typescript
// 大型组件延迟加载
const HeavyComponent = defineAsyncComponent(() => 
  import('/@/components/Heavy/HeavyComponent.vue')
);
```

### 2. 条件渲染优化

```vue
<template>
  <!-- 频繁切换使用 v-show -->
  <XhUserSelect v-show="showUserSelect" />
  
  <!-- 条件渲染使用 v-if -->
  <XhDepSelect v-if="needDepSelect" />
</template>
```

## 开发检查清单

### 组件使用
- [ ] 优先使用 Xh 系列组件
- [ ] 数据格式使用 `{id, fullName}` 结构
- [ ] API 返回 ActionResult 格式
- [ ] 组件命名正确（XhUserSelect 而不是 UserSelect）

### 性能优化
- [ ] 大型组件使用懒加载
- [ ] 适当使用虚拟滚动
- [ ] 避免不必要的响应式数据
- [ ] 合理使用 v-show 和 v-if

### 样式规范
- [ ] 使用 `xh-` 前缀命名
- [ ] 支持响应式设计
- [ ] 使用主题变量
- [ ] 支持暗黑模式