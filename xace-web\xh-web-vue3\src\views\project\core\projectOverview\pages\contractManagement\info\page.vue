<template>
  <div class="contract-info-management">
    <div class="page-header">
      <div class="flex items-center">
        <i class="icon-ym icon-ym-contract mr-2 text-lg"></i>
        <span class="text-base font-medium">合同信息</span>
      </div>
      <div class="flex items-center space-x-2">
        <a-button type="primary" @click="handleEdit" v-if="contractInfo && !editMode">
          <template #icon><EditOutlined /></template>
          编辑
        </a-button>
        <a-space v-if="editMode">
          <a-button type="primary" @click="handleSave" :loading="saveLoading"> 保存 </a-button>
          <a-button @click="handleCancel"> 取消 </a-button>
        </a-space>
        <a-button @click="handleRefresh">
          <template #icon><ReloadOutlined /></template>
          刷新
        </a-button>
      </div>
    </div>

    <div class="page-content">
      <a-spin :spinning="loading">
        <!-- 无合同状态 -->
        <div v-if="!hasContract" class="empty-state">
          <a-empty description="当前项目暂无合同信息">
            <a-button type="primary" @click="handleCreateContract">
              <template #icon><PlusOutlined /></template>
              创建合同
            </a-button>
          </a-empty>
        </div>

        <!-- 有合同信息 - 使用 BasicForm 组件 -->
        <div v-else-if="contractInfo" class="contract-detail">
          <!-- 基础信息表单 -->
          <a-card title="基础信息" class="mb-4">
            <BasicForm @register="registerBasicForm" v-if="!editMode" />
            <BasicForm @register="registerBasicEditForm" v-else />
          </a-card>

          <!-- 金额信息表单 -->
          <a-card title="金额信息" class="mb-4">
            <BasicForm @register="registerAmountForm" v-if="!editMode" />
            <BasicForm @register="registerAmountEditForm" v-else />
          </a-card>

          <!-- 日期信息表单 -->
          <a-card title="日期信息" class="mb-4">
            <BasicForm @register="registerDateForm" v-if="!editMode" />
            <BasicForm @register="registerDateEditForm" v-else />
          </a-card>

          <!-- 合同周期表单 -->
          <a-card title="合同周期" class="mb-4">
            <BasicForm @register="registerCycleForm" v-if="!editMode" />
            <BasicForm @register="registerCycleEditForm" v-else />
          </a-card>

          <!-- 联系人信息表单 -->
          <a-card title="联系人信息" class="mb-4">
            <BasicForm @register="registerContactForm" v-if="!editMode" />
            <BasicForm @register="registerContactEditForm" v-else />
          </a-card>

          <!-- 状态管理表单 -->
          <a-card title="状态管理" class="mb-4">
            <BasicForm @register="registerStatusForm" v-if="!editMode" />
            <BasicForm @register="registerStatusEditForm" v-else />
          </a-card>

          <!-- 毛利分析表单 -->
          <a-card title="毛利分析" class="mb-4">
            <BasicForm @register="registerProfitForm" v-if="!editMode" />
            <BasicForm @register="registerProfitEditForm" v-else />
          </a-card>

          <!-- 备注信息表单 -->
          <a-card title="备注信息" class="mb-4">
            <BasicForm @register="registerNoteForm" v-if="!editMode" />
            <BasicForm @register="registerNoteEditForm" v-else />
          </a-card>
        </div>
      </a-spin>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted, onActivated, inject, watch, nextTick } from 'vue';
  import { EditOutlined, ReloadOutlined, PlusOutlined } from '@ant-design/icons-vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { updateContract, type ContractFormModel } from '/@/api/project/contract';
  import { useProjectContext } from '/@/hooks/web/useProjectContext';

  import { BasicForm, useForm } from '/@/components/Form';
  import type { FormSchema } from '/@/components/Form';

  // 从父组件注入合同信息
  const contractId = inject('contractId', ref(''));
  const contractInfo = inject('contractInfo', ref(null));
  const hasContract = inject('hasContract', ref(false));

  // 调试输出：组件初始化时的inject值
  console.log('🚀 [合同管理] 组件初始化，inject的数据:', {
    contractId: contractId.value,
    contractInfo: contractInfo.value,
    hasContract: hasContract.value,
  });

  const { createMessage } = useMessage();

  // 响应式数据
  const loading = ref(false);
  const editMode = ref(false);
  const saveLoading = ref(false);

  // 使用项目上下文Hook（在变量定义之后）
  const { projectId } = useProjectContext({
    onProjectChange: (newProjectId, oldProjectId) => {
      console.log('🔄 [合同管理] 检测到项目切换:', { 旧项目: oldProjectId, 新项目: newProjectId });
      // 项目切换时清空编辑模式
      editMode.value = false;
    },
  });

  // 再次输出初始化后的完整状态
  console.log('🔧 [合同管理] Hook初始化完成，完整状态:', {
    projectId: projectId.value,
    contractId: contractId.value,
    contractInfo: contractInfo.value,
    hasContract: hasContract.value,
    editMode: editMode.value,
    loading: loading.value,
  });

  // 基础信息表单配置 - 查看模式
  const basicFormSchemas: FormSchema[] = [
    {
      field: 'name',
      label: '合同名称',
      component: 'Input',
      colProps: { span: 12 },
      componentProps: { disabled: true },
    },
    {
      field: 'cno',
      label: '合同编号',
      component: 'Input',
      colProps: { span: 12 },
      componentProps: { disabled: true },
    },
    {
      field: 'contractStatus',
      label: '合同状态',
      component: 'Input',
      colProps: { span: 12 },
      componentProps: { disabled: true },
      render: ({ model }) => {
        const status = model.contractStatus;
        const statusMap: Record<string, string> = {
          '1': '草稿',
          '2': '待审核',
          '3': '已审核',
          '4': '执行中',
          '5': '已完成',
          '6': '已终止',
        };
        return statusMap[status] || '未知';
      },
    },
    {
      field: 'custId',
      label: '客户单位',
      component: 'PopupSelect',
      colProps: { span: 12 },
      componentProps: {
        disabled: true,
        placeholder: '请选择客户单位',
        interfaceId: 'customer',
        modalTitle: '选择客户单位',
        selectType: 'all',
        multiple: false,
        allowClear: true,
      },
    },
    {
      field: 'finalUserId',
      label: '最终用户',
      component: 'PopupSelect',
      colProps: { span: 12 },
      componentProps: {
        disabled: true,
        placeholder: '请选择最终用户',
        interfaceId: 'customer',
        modalTitle: '选择最终用户',
        selectType: 'all',
        multiple: false,
        allowClear: true,
      },
    },
    {
      field: 'ownId',
      label: '负责人',
      component: 'UserSelect',
      colProps: { span: 12 },
      componentProps: {
        disabled: true,
        placeholder: '请选择负责人',
      },
    },
    {
      field: 'deptId',
      label: '所属部门',
      component: 'DepSelect',
      colProps: { span: 12 },
      componentProps: {
        disabled: true,
        placeholder: '请选择部门',
      },
    },
    {
      field: 'reportFrequency',
      label: '汇报频率',
      component: 'Select',
      colProps: { span: 12 },
      componentProps: {
        disabled: true,
        options: [
          { id: 'weekly', fullName: '每周' },
          { id: 'monthly', fullName: '每月' },
          { id: 'quarterly', fullName: '每季度' },
        ],
      },
    },
    {
      field: 'signYear',
      label: '合同年度',
      component: 'InputNumber',
      colProps: { span: 12 },
      componentProps: {
        disabled: true,
        precision: 0,
        min: 2000,
        max: 2100,
      },
    },
  ];

  // 基础信息表单配置 - 编辑模式
  const basicEditFormSchemas: FormSchema[] = [
    {
      field: 'name',
      label: '合同名称',
      component: 'Input',
      colProps: { span: 12 },
      required: true,
      componentProps: { placeholder: '请输入合同名称' },
    },
    {
      field: 'cno',
      label: '合同编号',
      component: 'Input',
      colProps: { span: 12 },
      required: true,
      componentProps: { placeholder: '请输入合同编号' },
    },
    {
      field: 'contractStatus',
      label: '合同状态',
      component: 'Select',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请选择合同状态',
        options: [
          { id: '1', fullName: '草稿' },
          { id: '2', fullName: '待审核' },
          { id: '3', fullName: '已审核' },
          { id: '4', fullName: '执行中' },
          { id: '5', fullName: '已完成' },
          { id: '6', fullName: '已终止' },
        ],
      },
    },
    {
      field: 'custId',
      label: '客户单位',
      component: 'PopupSelect',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请选择客户单位',
        interfaceId: 'customer',
        modalTitle: '选择客户单位',
        selectType: 'all',
        multiple: false,
        allowClear: true,
      },
    },
    {
      field: 'finalUserId',
      label: '最终用户',
      component: 'PopupSelect',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请选择最终用户',
        interfaceId: 'customer',
        modalTitle: '选择最终用户',
        selectType: 'all',
        multiple: false,
        allowClear: true,
      },
    },
    {
      field: 'ownId',
      label: '负责人',
      component: 'UserSelect',
      colProps: { span: 12 },
      componentProps: { placeholder: '请选择负责人' },
    },
    {
      field: 'deptId',
      label: '所属部门',
      component: 'DepSelect',
      colProps: { span: 12 },
      componentProps: { placeholder: '请选择部门' },
    },
    {
      field: 'reportFrequency',
      label: '汇报频率',
      component: 'Select',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请选择汇报频率',
        options: [
          { id: 'weekly', fullName: '每周' },
          { id: 'monthly', fullName: '每月' },
          { id: 'quarterly', fullName: '每季度' },
        ],
      },
    },
    {
      field: 'signYear',
      label: '合同年度',
      component: 'InputNumber',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请输入合同年度',
        precision: 0,
        min: 2000,
        max: 2100,
      },
    },
  ];

  // 金额信息表单配置 - 查看模式
  const amountFormSchemas: FormSchema[] = [
    {
      field: 'amount',
      label: '合同金额',
      component: 'InputNumber',
      colProps: { span: 12 },
      componentProps: {
        disabled: true,
        precision: 2,
        formatter: (value: number) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
      },
    },
    {
      field: 'ysAmount',
      label: '已收金额',
      component: 'InputNumber',
      colProps: { span: 12 },
      componentProps: {
        disabled: true,
        precision: 2,
        formatter: (value: number) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
      },
    },
    {
      field: 'yearYsAmount',
      label: '本年度收款金额',
      component: 'InputNumber',
      colProps: { span: 12 },
      componentProps: {
        disabled: true,
        precision: 2,
        formatter: (value: number) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
      },
    },
    {
      field: 'externalAmount',
      label: '外采金额',
      component: 'InputNumber',
      colProps: { span: 12 },
      componentProps: {
        disabled: true,
        precision: 2,
        formatter: (value: number) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
      },
    },
    {
      field: 'moneyStatus',
      label: '收款状态',
      component: 'Input',
      colProps: { span: 12 },
      componentProps: { disabled: true },
      render: ({ model }) => {
        const status = model.moneyStatus;
        const statusMap: Record<string, string> = {
          '0': '未收款',
          '1': '部分收款',
          '2': '已收款',
        };
        return statusMap[status] || '未知';
      },
    },
  ];

  // 日期信息表单配置 - 查看模式
  const dateFormSchemas: FormSchema[] = [
    {
      field: 'signDate',
      label: '签订日期',
      component: 'DatePicker',
      colProps: { span: 12 },
      componentProps: {
        disabled: true,
        format: 'YYYY-MM-DD',
        placeholder: '签订日期',
      },
    },
    {
      field: 'bidDate',
      label: '中标日期',
      component: 'DatePicker',
      colProps: { span: 12 },
      componentProps: {
        disabled: true,
        format: 'YYYY-MM-DD',
        placeholder: '中标日期',
      },
    },
    {
      field: 'commencementDate',
      label: '开工日期',
      component: 'DatePicker',
      colProps: { span: 12 },
      componentProps: {
        disabled: true,
        format: 'YYYY-MM-DD',
        placeholder: '开工日期',
      },
    },
    {
      field: 'initialCheckDate',
      label: '初验日期',
      component: 'DatePicker',
      colProps: { span: 12 },
      componentProps: {
        disabled: true,
        format: 'YYYY-MM-DD',
        placeholder: '初验日期',
      },
    },
    {
      field: 'finalCheckDate',
      label: '终验日期',
      component: 'DatePicker',
      colProps: { span: 12 },
      componentProps: {
        disabled: true,
        format: 'YYYY-MM-DD',
        placeholder: '终验日期',
      },
    },
    {
      field: 'auditDate',
      label: '审计日期',
      component: 'DatePicker',
      colProps: { span: 12 },
      componentProps: {
        disabled: true,
        format: 'YYYY-MM-DD',
        placeholder: '审计日期',
      },
    },
  ];

  // 日期信息表单配置 - 编辑模式
  const dateEditFormSchemas: FormSchema[] = dateFormSchemas.map(schema => ({
    ...schema,
    componentProps: {
      ...schema.componentProps,
      disabled: false,
    },
  }));

  // 合同周期表单配置 - 查看模式
  const cycleFormSchemas: FormSchema[] = [
    {
      field: 'cstartDate',
      label: '合同开始日期',
      component: 'DatePicker',
      colProps: { span: 12 },
      componentProps: {
        disabled: true,
        format: 'YYYY-MM-DD',
        placeholder: '合同开始日期',
      },
    },
    {
      field: 'cendDate',
      label: '合同结束日期',
      component: 'DatePicker',
      colProps: { span: 12 },
      componentProps: {
        disabled: true,
        format: 'YYYY-MM-DD',
        placeholder: '合同结束日期',
      },
    },
    {
      field: 'mstartDate',
      label: '维保开始日期',
      component: 'DatePicker',
      colProps: { span: 12 },
      componentProps: {
        disabled: true,
        format: 'YYYY-MM-DD',
        placeholder: '维保开始日期',
      },
    },
    {
      field: 'mendDate',
      label: '维保结束日期',
      component: 'DatePicker',
      colProps: { span: 12 },
      componentProps: {
        disabled: true,
        format: 'YYYY-MM-DD',
        placeholder: '维保结束日期',
      },
    },
  ];

  // 合同周期表单配置 - 编辑模式
  const cycleEditFormSchemas: FormSchema[] = cycleFormSchemas.map(schema => ({
    ...schema,
    componentProps: {
      ...schema.componentProps,
      disabled: false,
    },
  }));

  // 联系人信息表单配置 - 查看模式
  const contactFormSchemas: FormSchema[] = [
    {
      field: 'linkman',
      label: '合同联系人',
      component: 'Input',
      colProps: { span: 12 },
      componentProps: { disabled: true, placeholder: '合同联系人' },
    },
    {
      field: 'linkTelephone',
      label: '联系电话',
      component: 'Input',
      colProps: { span: 12 },
      componentProps: { disabled: true, placeholder: '联系电话' },
    },
    {
      field: 'svDeptId',
      label: '监理单位',
      component: 'PopupSelect',
      colProps: { span: 12 },
      componentProps: { disabled: true, placeholder: '监理单位' },
    },
    {
      field: 'svLinkman',
      label: '监理联系人',
      component: 'Input',
      colProps: { span: 12 },
      componentProps: { disabled: true, placeholder: '监理联系人' },
    },
    {
      field: 'svTelephone',
      label: '监理联系电话',
      component: 'Input',
      colProps: { span: 12 },
      componentProps: { disabled: true, placeholder: '监理联系电话' },
    },
    {
      field: 'reviewDeptId',
      label: '测评单位',
      component: 'PopupSelect',
      colProps: { span: 12 },
      componentProps: { disabled: true, placeholder: '测评单位' },
    },
    {
      field: 'reviewLinkman',
      label: '测评联系人',
      component: 'Input',
      colProps: { span: 12 },
      componentProps: { disabled: true, placeholder: '测评联系人' },
    },
    {
      field: 'reviewTelephone',
      label: '测评联系电话',
      component: 'Input',
      colProps: { span: 12 },
      componentProps: { disabled: true, placeholder: '测评联系电话' },
    },
    {
      field: 'dbDeptId',
      label: '等保单位',
      component: 'PopupSelect',
      colProps: { span: 12 },
      componentProps: { disabled: true, placeholder: '等保单位' },
    },
    {
      field: 'dbLinkman',
      label: '等保联系人',
      component: 'Input',
      colProps: { span: 12 },
      componentProps: { disabled: true, placeholder: '等保联系人' },
    },
    {
      field: 'dbTelephone',
      label: '等保联系电话',
      component: 'Input',
      colProps: { span: 12 },
      componentProps: { disabled: true, placeholder: '等保联系电话' },
    },
    {
      field: 'smDeptId',
      label: '商密评测单位',
      component: 'PopupSelect',
      colProps: { span: 12 },
      componentProps: { disabled: true, placeholder: '商密评测单位' },
    },
    {
      field: 'smLinkman',
      label: '商密联系人',
      component: 'Input',
      colProps: { span: 12 },
      componentProps: { disabled: true, placeholder: '商密联系人' },
    },
    {
      field: 'smTelephone',
      label: '商密联系电话',
      component: 'Input',
      colProps: { span: 12 },
      componentProps: { disabled: true, placeholder: '商密联系电话' },
    },
    {
      field: 'jsDeptId',
      label: '结算单位',
      component: 'PopupSelect',
      colProps: { span: 12 },
      componentProps: { disabled: true, placeholder: '结算单位' },
    },
    {
      field: 'jsLinkman',
      label: '结算联系人',
      component: 'Input',
      colProps: { span: 12 },
      componentProps: { disabled: true, placeholder: '结算联系人' },
    },
    {
      field: 'jsTelephone',
      label: '结算联系电话',
      component: 'Input',
      colProps: { span: 12 },
      componentProps: { disabled: true, placeholder: '结算联系电话' },
    },
  ];

  // 联系人信息表单配置 - 编辑模式
  const contactEditFormSchemas: FormSchema[] = contactFormSchemas.map(schema => ({
    ...schema,
    componentProps: {
      ...schema.componentProps,
      disabled: false,
    },
  }));

  // 状态管理表单配置 - 查看模式
  const statusFormSchemas: FormSchema[] = [
    {
      field: 'contractStatus',
      label: '合同状态',
      component: 'Input',
      colProps: { span: 12 },
      componentProps: { disabled: true },
      render: ({ model }) => {
        const status = model.contractStatus;
        const statusMap: Record<string, string> = {
          '1': '草稿',
          '2': '待审核',
          '3': '已审核',
          '4': '执行中',
          '5': '已完成',
          '6': '已终止',
        };
        return statusMap[status] || '未知';
      },
    },
    {
      field: 'moneyStatus',
      label: '收款状态',
      component: 'Input',
      colProps: { span: 12 },
      componentProps: { disabled: true },
      render: ({ model }) => {
        const status = model.moneyStatus;
        const statusMap: Record<string, string> = {
          '0': '未收款',
          '1': '部分收款',
          '2': '已收款',
        };
        return statusMap[status] || '未知';
      },
    },
    {
      field: 'accdocStatus',
      label: '验收状态',
      component: 'Select',
      colProps: { span: 12 },
      componentProps: {
        disabled: true,
        options: [
          { id: '0', fullName: '未验收' },
          { id: '1', fullName: '初验完成' },
          { id: '2', fullName: '终验完成' },
        ],
      },
    },
    {
      field: 'isContinue',
      label: '续签状态',
      component: 'Select',
      colProps: { span: 12 },
      componentProps: {
        disabled: true,
        options: [
          { id: 0, fullName: '正常' },
          { id: 1, fullName: '已续签' },
          { id: 9, fullName: '不续签' },
        ],
      },
    },
    {
      field: 'isArchive',
      label: '归档状态',
      component: 'Select',
      colProps: { span: 12 },
      componentProps: {
        disabled: true,
        options: [
          { id: 0, fullName: '未归档' },
          { id: 1, fullName: '已归档' },
        ],
      },
    },
    {
      field: 'workStatus',
      label: '工时状态',
      component: 'Select',
      colProps: { span: 12 },
      componentProps: {
        disabled: true,
        options: [
          { id: 0, fullName: '已结束' },
          { id: 1, fullName: '可填写' },
        ],
      },
    },
  ];

  // 状态管理表单配置 - 编辑模式
  const statusEditFormSchemas: FormSchema[] = [
    {
      field: 'contractStatus',
      label: '合同状态',
      component: 'Select',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请选择合同状态',
        options: [
          { id: '1', fullName: '草稿' },
          { id: '2', fullName: '待审核' },
          { id: '3', fullName: '已审核' },
          { id: '4', fullName: '执行中' },
          { id: '5', fullName: '已完成' },
          { id: '6', fullName: '已终止' },
        ],
      },
    },
    {
      field: 'accdocStatus',
      label: '验收状态',
      component: 'Select',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请选择验收状态',
        options: [
          { id: '0', fullName: '未验收' },
          { id: '1', fullName: '初验完成' },
          { id: '2', fullName: '终验完成' },
        ],
      },
    },
    {
      field: 'isContinue',
      label: '续签状态',
      component: 'Select',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请选择续签状态',
        options: [
          { id: 0, fullName: '正常' },
          { id: 1, fullName: '已续签' },
          { id: 9, fullName: '不续签' },
        ],
      },
    },
    {
      field: 'isArchive',
      label: '归档状态',
      component: 'Select',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请选择归档状态',
        options: [
          { id: 0, fullName: '未归档' },
          { id: 1, fullName: '已归档' },
        ],
      },
    },
  ];

  // 毛利分析表单配置 - 查看模式
  const profitFormSchemas: FormSchema[] = [
    {
      field: 'estProbit',
      label: '预估毛利',
      component: 'InputNumber',
      colProps: { span: 12 },
      componentProps: {
        disabled: true,
        precision: 2,
        formatter: (value: number) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
        placeholder: '预估毛利',
      },
    },
    {
      field: 'actProbit',
      label: '实际毛利',
      component: 'InputNumber',
      colProps: { span: 12 },
      componentProps: {
        disabled: true,
        precision: 2,
        formatter: (value: number) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
        placeholder: '实际毛利',
      },
    },
    {
      field: 'estProbitRatio',
      label: '预估毛利率',
      component: 'InputNumber',
      colProps: { span: 12 },
      componentProps: {
        disabled: true,
        precision: 2,
        max: 100,
        formatter: (value: number) => `${value}%`,
        placeholder: '预估毛利率',
      },
    },
    {
      field: 'actProbitRatio',
      label: '实际毛利率',
      component: 'InputNumber',
      colProps: { span: 12 },
      componentProps: {
        disabled: true,
        precision: 2,
        max: 100,
        formatter: (value: number) => `${value}%`,
        placeholder: '实际毛利率',
      },
    },
    {
      field: 'evaExternalAmount',
      label: '采购费用预测',
      component: 'InputNumber',
      colProps: { span: 12 },
      componentProps: {
        disabled: true,
        precision: 2,
        formatter: (value: number) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
        placeholder: '采购费用预测',
      },
    },
    {
      field: 'actExternalAmount',
      label: '实际采购金额',
      component: 'InputNumber',
      colProps: { span: 12 },
      componentProps: {
        disabled: true,
        precision: 2,
        formatter: (value: number) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
        placeholder: '实际采购金额',
      },
    },
    {
      field: 'evaCostAmount',
      label: '费用预测',
      component: 'InputNumber',
      colProps: { span: 12 },
      componentProps: {
        disabled: true,
        precision: 2,
        formatter: (value: number) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
        placeholder: '费用预测',
      },
    },
    {
      field: 'actCostAmount',
      label: '实际费用',
      component: 'InputNumber',
      colProps: { span: 12 },
      componentProps: {
        disabled: true,
        precision: 2,
        formatter: (value: number) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
        placeholder: '实际费用',
      },
    },
    {
      field: 'autoManhours',
      label: '累计工时',
      component: 'InputNumber',
      colProps: { span: 12 },
      componentProps: {
        disabled: true,
        precision: 1,
        formatter: (value: number) => `${value}小时`,
        placeholder: '累计工时',
      },
    },
    {
      field: 'autoRatioLevel',
      label: '毛利率区间',
      component: 'Input',
      colProps: { span: 12 },
      componentProps: { disabled: true, placeholder: '毛利率区间' },
    },
  ];

  // 毛利分析表单配置 - 编辑模式
  const profitEditFormSchemas: FormSchema[] = profitFormSchemas.map(schema => ({
    ...schema,
    componentProps: {
      ...schema.componentProps,
      disabled:
        schema.field === 'actProbit' ||
        schema.field === 'actExternalAmount' ||
        schema.field === 'actCostAmount' ||
        schema.field === 'autoManhours' ||
        schema.field === 'autoRatioLevel'
          ? true
          : false, // 实际数据和自动计算字段保持只读
    },
  }));

  // 备注信息表单配置 - 查看模式
  const noteFormSchemas: FormSchema[] = [
    {
      field: 'note',
      label: '备注',
      component: 'Textarea',
      colProps: { span: 24 },
      componentProps: {
        disabled: true,
        rows: 4,
        placeholder: '备注信息',
      },
    },
    {
      field: 'continueDesc',
      label: '续签说明',
      component: 'Textarea',
      colProps: { span: 24 },
      componentProps: {
        disabled: true,
        rows: 3,
        placeholder: '续签说明',
      },
    },
    {
      field: 'accdocPath',
      label: '验收文档路径',
      component: 'Input',
      colProps: { span: 24 },
      componentProps: {
        disabled: true,
        placeholder: '验收文档路径',
      },
    },
  ];

  // 备注信息表单配置 - 编辑模式
  const noteEditFormSchemas: FormSchema[] = noteFormSchemas.map(schema => ({
    ...schema,
    componentProps: {
      ...schema.componentProps,
      disabled: false,
    },
  }));

  // 创建表单实例
  const [registerBasicForm, { setFieldsValue: setBasicFieldsValue }] = useForm({
    labelWidth: 120,
    schemas: basicFormSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });

  const [registerBasicEditForm, { validate: validateBasic, setFieldsValue: setBasicEditFieldsValue }] = useForm({
    labelWidth: 120,
    schemas: basicEditFormSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });

  const [registerAmountForm, { setFieldsValue: setAmountFieldsValue }] = useForm({
    labelWidth: 120,
    schemas: amountFormSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });

  const [registerAmountEditForm, { validate: validateAmount, setFieldsValue: setAmountEditFieldsValue }] = useForm({
    labelWidth: 120,
    schemas: amountFormSchemas.map(schema => ({ ...schema, componentProps: { ...schema.componentProps, disabled: false } })),
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });

  // 创建其他表单实例
  const [registerDateForm, { setFieldsValue: setDateFieldsValue }] = useForm({
    labelWidth: 120,
    schemas: dateFormSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });

  const [registerDateEditForm, { validate: validateDate, setFieldsValue: setDateEditFieldsValue }] = useForm({
    labelWidth: 120,
    schemas: dateEditFormSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });

  const [registerCycleForm, { setFieldsValue: setCycleFieldsValue }] = useForm({
    labelWidth: 120,
    schemas: cycleFormSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });

  const [registerCycleEditForm, { validate: validateCycle, setFieldsValue: setCycleEditFieldsValue }] = useForm({
    labelWidth: 120,
    schemas: cycleEditFormSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });

  const [registerContactForm, { setFieldsValue: setContactFieldsValue }] = useForm({
    labelWidth: 120,
    schemas: contactFormSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });

  const [registerContactEditForm, { validate: validateContact, setFieldsValue: setContactEditFieldsValue }] = useForm({
    labelWidth: 120,
    schemas: contactEditFormSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });

  const [registerStatusForm, { setFieldsValue: setStatusFieldsValue }] = useForm({
    labelWidth: 120,
    schemas: statusFormSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });

  const [registerStatusEditForm, { validate: validateStatus, setFieldsValue: setStatusEditFieldsValue }] = useForm({
    labelWidth: 120,
    schemas: statusEditFormSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });

  const [registerProfitForm, { setFieldsValue: setProfitFieldsValue }] = useForm({
    labelWidth: 120,
    schemas: profitFormSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });

  const [registerProfitEditForm, { validate: validateProfit, setFieldsValue: setProfitEditFieldsValue }] = useForm({
    labelWidth: 120,
    schemas: profitEditFormSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });

  const [registerNoteForm, { setFieldsValue: setNoteFieldsValue }] = useForm({
    labelWidth: 120,
    schemas: noteFormSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });

  const [registerNoteEditForm, { validate: validateNote, setFieldsValue: setNoteEditFieldsValue }] = useForm({
    labelWidth: 120,
    schemas: noteEditFormSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });

  // 检查所有表单是否都已注册
  const checkAllFormsRegistered = (): boolean => {
    // 只检查查看模式的表单，编辑表单在需要时才检查
    const viewForms = [
      { fn: setBasicFieldsValue, name: '基础信息查看' },
      { fn: setAmountFieldsValue, name: '金额信息查看' },
      { fn: setDateFieldsValue, name: '日期信息查看' },
      { fn: setCycleFieldsValue, name: '合同周期查看' },
      { fn: setContactFieldsValue, name: '联系人信息查看' },
      { fn: setStatusFieldsValue, name: '状态管理查看' },
      { fn: setProfitFieldsValue, name: '毛利分析查看' },
      { fn: setNoteFieldsValue, name: '备注信息查看' },
    ];

    let allReady = true;
    for (const form of viewForms) {
      try {
        form.fn({});
      } catch (error) {
        console.log(`⚠️ ${form.name} 表单尚未注册`);
        allReady = false;
      }
    }
    return allReady;
  };

  // 检查表单是否就绪的函数
  const checkFormReady = (setterFn: Function, formName: string): boolean => {
    try {
      // 尝试调用表单方法来检查是否就绪
      setterFn({});
      return true;
    } catch (error) {
      console.log(`⚠️ ${formName} 表单尚未就绪`);
      return false;
    }
  };

  // 等待表单就绪的函数
  const waitForFormReady = async (setterFn: Function, formName: string, maxRetries = 10): Promise<boolean> => {
    for (let i = 0; i < maxRetries; i++) {
      if (checkFormReady(setterFn, formName)) {
        return true;
      }
      await new Promise(resolve => setTimeout(resolve, 200));
    }
    console.error(`❌ ${formName} 表单在 ${maxRetries * 200}ms 后仍未就绪`);
    return false;
  };

  // 安全设置表单数据的辅助函数
  const safeSetFieldsValue = async (setterFn: Function, data: any, formName: string) => {
    try {
      // 检查表单名是否为编辑表单且当前不在编辑模式
      if (formName.includes('编辑') && !editMode.value) {
        // console.log(`⚠️ ${formName} 非编辑模式，跳过数据设置`);
        return;
      }

      // 先等待表单就绪
      const isReady = await waitForFormReady(setterFn, formName);
      if (isReady) {
        await setterFn(data);
        //  console.log(`✅ ${formName} 表单数据设置成功`);
      } else {
        console.warn(`⚠️ ${formName} 表单未就绪，跳过数据设置`);
      }
    } catch (error) {
      console.error(`❌ ${formName} 表单数据设置失败:`, error);
    }
  };

  // 加载合同信息
  const loadContractInfo = async () => {
    if (!contractInfo.value) {
      console.log('⚠️ 没有合同信息');
      return;
    }

    loading.value = true;
    try {
      console.log('📋 开始加载合同详细信息:', contractInfo.value);

      const contract = contractInfo.value as any;

      // 等待一小段时间确保所有表单组件都已渲染
      await new Promise(resolve => setTimeout(resolve, 300));

      // 检查所有表单是否都已注册
      if (!checkAllFormsRegistered()) {
        console.warn('⚠️ 部分表单尚未注册，等待更长时间...');
        await new Promise(resolve => setTimeout(resolve, 500));

        if (!checkAllFormsRegistered()) {
          console.error('❌ 表单注册超时，部分数据可能无法设置');
        }
      }

      // 基础信息数据
      const basicData = {
        name: contract.name || '',
        cno: contract.cno || '',
        contractStatus: contract.contractStatus || '',
        custId: contract.custId || '',
        finalUserId: contract.finalUserId || '',
        ownId: contract.ownId || '',
        deptId: contract.deptId || '',
        reportFrequency: contract.reportFrequency || '',
        signYear: contract.signYear || null,
      };

      // 安全设置基础信息表单数据
      await safeSetFieldsValue(setBasicFieldsValue, basicData, '基础信息查看');
      await safeSetFieldsValue(setBasicEditFieldsValue, basicData, '基础信息编辑');

      // 金额信息数据
      const amountData = {
        amount: contract.amount || 0,
        ysAmount: contract.ysAmount || 0,
        yearYsAmount: contract.yearYsAmount || 0,
        externalAmount: contract.externalAmount || 0,
        moneyStatus: contract.moneyStatus || '',
      };
      await safeSetFieldsValue(setAmountFieldsValue, amountData, '金额信息查看');
      await safeSetFieldsValue(setAmountEditFieldsValue, amountData, '金额信息编辑');

      // 日期信息数据
      const dateData = {
        signDate: contract.signDate || undefined,
        bidDate: contract.bidDate || undefined,
        commencementDate: contract.commencementDate || undefined,
        initialCheckDate: contract.initialCheckDate || undefined,
        finalCheckDate: contract.finalCheckDate || undefined,
        auditDate: contract.auditDate || undefined,
      };
      await safeSetFieldsValue(setDateFieldsValue, dateData, '日期信息查看');
      await safeSetFieldsValue(setDateEditFieldsValue, dateData, '日期信息编辑');

      // 合同周期数据
      const cycleData = {
        cstartDate: contract.cstartDate || undefined,
        cendDate: contract.cendDate || undefined,
        mstartDate: contract.mstartDate || undefined,
        mendDate: contract.mendDate || undefined,
      };
      await safeSetFieldsValue(setCycleFieldsValue, cycleData, '合同周期查看');
      await safeSetFieldsValue(setCycleEditFieldsValue, cycleData, '合同周期编辑');

      // 联系人信息数据
      const contactData = {
        linkman: contract.linkman || '',
        linkTelephone: contract.linkTelephone || '',
        svDeptId: contract.svDeptId || '',
        svLinkman: contract.svLinkman || '',
        svTelephone: contract.svTelephone || '',
        reviewDeptId: contract.reviewDeptId || '',
        reviewLinkman: contract.reviewLinkman || '',
        reviewTelephone: contract.reviewTelephone || '',
        dbDeptId: contract.dbDeptId || '',
        dbLinkman: contract.dbLinkman || '',
        dbTelephone: contract.dbTelephone || '',
        smDeptId: contract.smDeptId || '',
        smLinkman: contract.smLinkman || '',
        smTelephone: contract.smTelephone || '',
        jsDeptId: contract.jsDeptId || '',
        jsLinkman: contract.jsLinkman || '',
        jsTelephone: contract.jsTelephone || '',
      };
      await safeSetFieldsValue(setContactFieldsValue, contactData, '联系人信息查看');
      await safeSetFieldsValue(setContactEditFieldsValue, contactData, '联系人信息编辑');

      // 状态管理数据
      const statusData = {
        contractStatus: contract.contractStatus || '',
        moneyStatus: contract.moneyStatus || '',
        accdocStatus: contract.accdocStatus || '',
        isContinue: contract.isContinue || 0,
        isArchive: contract.isArchive || 0,
        workStatus: contract.workStatus || 0,
      };
      await safeSetFieldsValue(setStatusFieldsValue, statusData, '状态管理查看');
      await safeSetFieldsValue(setStatusEditFieldsValue, statusData, '状态管理编辑');

      // 毛利分析数据
      const profitData = {
        estProbit: contract.estProbit || 0,
        actProbit: contract.actProbit || 0,
        estProbitRatio: contract.estProbitRatio || 0,
        actProbitRatio: contract.actProbitRatio || 0,
        evaExternalAmount: contract.evaExternalAmount || 0,
        actExternalAmount: contract.actExternalAmount || 0,
        evaCostAmount: contract.evaCostAmount || 0,
        actCostAmount: contract.actCostAmount || 0,
        autoManhours: contract.autoManhours || 0,
        autoRatioLevel: contract.autoRatioLevel || '',
      };
      await safeSetFieldsValue(setProfitFieldsValue, profitData, '毛利分析查看');
      await safeSetFieldsValue(setProfitEditFieldsValue, profitData, '毛利分析编辑');

      // 备注信息数据
      const noteData = {
        note: contract.note || '',
        continueDesc: contract.continueDesc || '',
        accdocPath: contract.accdocPath || '',
      };
      await safeSetFieldsValue(setNoteFieldsValue, noteData, '备注信息查看');
      await safeSetFieldsValue(setNoteEditFieldsValue, noteData, '备注信息编辑');

      console.log('✅ 所有表单数据设置完成');
    } catch (error) {
      console.error('❌ 加载合同信息失败:', error);
      createMessage.error('加载合同信息失败');
    } finally {
      loading.value = false;
      console.log('📋 合同信息加载流程结束');
    }
  };

  // 编辑合同
  const handleEdit = async () => {
    editMode.value = true;

    // 切换到编辑模式后，需要为编辑表单设置数据
    if (contractInfo.value) {
      await nextTick(); // 等待DOM更新

      const contract = contractInfo.value;

      // 基础信息数据
      const basicData = {
        name: contract.name || '',
        cno: contract.cno || '',
        contractStatus: contract.contractStatus || '',
        custId: contract.custId || '',
        finalUserId: contract.finalUserId || '',
        ownId: contract.ownId || '',
        deptId: contract.deptId || '',
        reportFrequency: contract.reportFrequency || '',
        signYear: contract.signYear || null,
      };
      await safeSetFieldsValue(setBasicEditFieldsValue, basicData, '基础信息编辑');

      // 金额信息数据
      const amountData = {
        amount: contract.amount || 0,
        ysAmount: contract.ysAmount || 0,
        externalAmount: contract.externalAmount || 0,
        moneyStatus: contract.moneyStatus || '',
      };
      await safeSetFieldsValue(setAmountEditFieldsValue, amountData, '金额信息编辑');

      // 日期信息数据
      const dateData = {
        signDate: contract.signDate || undefined,
        bidDate: contract.bidDate || undefined,
        commencementDate: contract.commencementDate || undefined,
        initialCheckDate: contract.initialCheckDate || undefined,
        finalCheckDate: contract.finalCheckDate || undefined,
        auditDate: contract.auditDate || undefined,
      };
      await safeSetFieldsValue(setDateEditFieldsValue, dateData, '日期信息编辑');

      // 合同周期数据
      const cycleData = {
        cstartDate: contract.cstartDate || undefined,
        cendDate: contract.cendDate || undefined,
        mstartDate: contract.mstartDate || undefined,
        mendDate: contract.mendDate || undefined,
      };
      await safeSetFieldsValue(setCycleEditFieldsValue, cycleData, '合同周期编辑');

      // 联系人信息数据
      const contactData = {
        linkman: contract.linkman || '',
        linkTelephone: contract.linkTelephone || '',
        svDeptId: contract.svDeptId || '',
        svLinkman: contract.svLinkman || '',
        svTelephone: contract.svTelephone || '',
        reviewDeptId: contract.reviewDeptId || '',
        reviewLinkman: contract.reviewLinkman || '',
        reviewTelephone: contract.reviewTelephone || '',
        dbDeptId: contract.dbDeptId || '',
        dbLinkman: contract.dbLinkman || '',
        dbTelephone: contract.dbTelephone || '',
        smDeptId: contract.smDeptId || '',
        smLinkman: contract.smLinkman || '',
        smTelephone: contract.smTelephone || '',
        jsDeptId: contract.jsDeptId || '',
        jsLinkman: contract.jsLinkman || '',
        jsTelephone: contract.jsTelephone || '',
      };
      await safeSetFieldsValue(setContactEditFieldsValue, contactData, '联系人信息编辑');

      // 状态管理数据
      const statusData = {
        contractStatus: contract.contractStatus || '',
        moneyStatus: contract.moneyStatus || '',
        accdocStatus: contract.accdocStatus || '',
        isContinue: contract.isContinue || 0,
        isArchive: contract.isArchive || 0,
        workStatus: contract.workStatus || 0,
      };
      await safeSetFieldsValue(setStatusEditFieldsValue, statusData, '状态管理编辑');

      // 毛利分析数据
      const profitData = {
        estProbit: contract.estProbit || 0,
        actProbit: contract.actProbit || 0,
        estProbitRatio: contract.estProbitRatio || 0,
        actProbitRatio: contract.actProbitRatio || 0,
        evaExternalAmount: contract.evaExternalAmount || 0,
        actExternalAmount: contract.actExternalAmount || 0,
        evaCostAmount: contract.evaCostAmount || 0,
        actCostAmount: contract.actCostAmount || 0,
        autoManhours: contract.autoManhours || 0,
        autoRatioLevel: contract.autoRatioLevel || '',
      };
      await safeSetFieldsValue(setProfitEditFieldsValue, profitData, '毛利分析编辑');

      // 备注信息数据
      const noteData = {
        note: contract.note || '',
        continueDesc: contract.continueDesc || '',
        accdocPath: contract.accdocPath || '',
      };
      await safeSetFieldsValue(setNoteEditFieldsValue, noteData, '备注信息编辑');
    }
  };

  // 保存合同信息
  const handleSave = async () => {
    try {
      saveLoading.value = true;

      // 验证所有表单
      const basicData = await validateBasic();
      const amountData = await validateAmount();
      const dateData = await validateDate();
      const cycleData = await validateCycle();
      const contactData = await validateContact();
      const statusData = await validateStatus();
      const profitData = await validateProfit();
      const noteData = await validateNote();

      // 构建提交数据 - 只包含ContractFormModel接口中定义的字段
      const submitData: ContractFormModel = {
        name: basicData.name,
        cno: basicData.cno,
        custId: basicData.custId || '',
        finalUserId: basicData.finalUserId || '',
        ownId: basicData.ownId || '',
        reportFrequency: basicData.reportFrequency || '',
        amount: amountData.amount || 0,
        externalAmount: amountData.externalAmount || undefined,
        moneyStatus: (contractInfo.value as any)?.moneyStatus || '',
        contractStatus: statusData.contractStatus || basicData.contractStatus || '',
        typeStatus: (contractInfo.value as any)?.typeStatus || '',
        note: noteData.note || '',
        deptId: basicData.deptId || '',
        linkman: contactData.linkman || '',
        linkTelephone: contactData.linkTelephone || '',
        // 日期信息
        signDate: dateData.signDate || undefined,
        bidDate: dateData.bidDate || undefined,
        commencementDate: dateData.commencementDate || undefined,
        initialCheckDate: dateData.initialCheckDate || undefined,
        finalCheckDate: dateData.finalCheckDate || undefined,
        auditDate: dateData.auditDate || undefined,
        // 合同周期
        cstartDate: cycleData.cstartDate || undefined,
        cendDate: cycleData.cendDate || undefined,
        mstartDate: cycleData.mstartDate || undefined,
        mendDate: cycleData.mendDate || undefined,
        // 毛利分析
        evaExternalAmount: profitData.evaExternalAmount || undefined,
        evaCostAmount: profitData.evaCostAmount || undefined,
        actExternalAmount: profitData.actExternalAmount || undefined,
        actCostAmount: profitData.actCostAmount || undefined,
        unsignExternalAmount: profitData.unsignExternalAmount || undefined,
        estProbit: profitData.estProbit || undefined,
        actProbit: profitData.actProbit || undefined,
        estProbitRatio: profitData.estProbitRatio || undefined,
        actProbitRatio: profitData.actProbitRatio || undefined,
      };

      console.log('📤 提交合同更新数据:', submitData);

      // 调用真实的合同更新API
      const response = await updateContract(contractId.value, submitData);
      console.log('✅ 合同更新成功:', response);

      createMessage.success('合同信息更新成功');
      editMode.value = false;

      // 重新加载数据
      loadContractInfo();
    } catch (error) {
      console.error('保存合同信息失败:', error);
      createMessage.error('保存合同信息失败');
    } finally {
      saveLoading.value = false;
    }
  };

  // 取消编辑
  const handleCancel = () => {
    editMode.value = false;
    loadContractInfo(); // 重新加载数据
  };

  // 创建合同
  const handleCreateContract = () => {
    createMessage.info('创建合同功能开发中...');
  };

  // 刷新
  const handleRefresh = () => {
    loadContractInfo();
  };

  // 监听合同信息变化
  watch(
    contractInfo,
    (newContractInfo, oldContractInfo) => {
      console.log('🔄 [合同管理] 合同信息变化监听器触发:', {
        新合同信息: newContractInfo,
        旧合同信息: oldContractInfo,
        项目ID: projectId.value,
      });

      if (newContractInfo) {
        console.log('✅ [合同管理] 监听到合同信息变化，准备加载数据:', (newContractInfo as any)?.name);
        // 延迟加载，确保表单组件已准备就绪
        setTimeout(() => {
          console.log('📋 [合同管理] 开始监听器触发的数据加载');
          loadContractInfo();
        }, 300);
      } else {
        console.log('⚠️ [合同管理] 合同信息被清空，退出编辑模式');
        // 合同信息清空时，退出编辑模式
        editMode.value = false;
      }
    },
    { immediate: false, deep: true },
  );

  onMounted(() => {
    console.log('🔄 组件已挂载，准备加载合同信息');
    // 延迟加载，确保所有表单组件都已渲染
    setTimeout(() => {
      if (contractInfo.value) {
        console.log('📋 开始延迟加载合同信息');
        loadContractInfo();
      } else {
        console.log('⚠️ 挂载时没有合同信息');
      }
    }, 500);
  });

  onActivated(() => {
    console.log('🔄 组件已激活，准备加载合同信息');
    // 延迟加载，确保所有表单组件都已渲染
    setTimeout(() => {
      if (contractInfo.value) {
        console.log('📋 开始延迟加载合同信息');
        loadContractInfo();
      } else {
        console.log('⚠️ 激活时没有合同信息');
      }
    }, 500);
  });
</script>

<style lang="less" scoped>
  .contract-info-management {
    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding: 16px;
      background: #fff;
      border-radius: 6px;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    }

    .page-content {
      .contract-detail {
        .ant-card {
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
        }
      }

      .empty-state {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 400px;
        background: #fff;
        border-radius: 6px;
      }
    }
  }
</style>
