import { Input, DatePicker } from 'ant-design-vue';

// xh 组件
import { BasicCaption } from '/@/components/Basic';
import { XhAlert } from '/@/components/Xh/Alert';
import { XhAreaSelect } from '/@/components/Xh/AreaSelect';
import { XhAutoComplete } from '/@/components/Xh/AutoComplete';
import { XhButton } from '/@/components/Xh/Button';
import { XhCron } from '/@/components/Xh/Cron';
import { XhCascader } from '/@/components/Xh/Cascader';
import { XhCheckbox, XhCheckboxSingle } from '/@/components/Xh/Checkbox';
import { XhColorPicker } from '/@/components/Xh/ColorPicker';
import { XhDatePicker, XhDateRange, XhTimePicker, XhTimeRange } from '/@/components/Xh/DatePicker';
import { XhDivider } from '/@/components/Xh/Divider';
import { XhIconPicker } from '/@/components/Xh/IconPicker';
import { XhInput, XhTextarea } from '/@/components/Xh/Input';
import { XhInputNumber } from '/@/components/Xh/InputNumber';
import { XhLink } from '/@/components/Xh/Link';
import { XhOpenData } from '/@/components/Xh/OpenData';
import {
  XhOrganizeSelect,
  XhDepSelect,
  XhPosSelect,
  XhGroupSelect,
  XhRoleSelect,
  XhUserSelect,
  XhUsersSelect,
  XhUserSelectDropdown,
} from '/@/components/Xh/Organize';
import { XhQrcode } from '/@/components/Xh/Qrcode';
import { XhBarcode } from '/@/components/Xh/Barcode';
import { XhRadio } from '/@/components/Xh/Radio';
import { XhSelect } from '/@/components/Xh/Select';
import { XhRate } from '/@/components/Xh/Rate';
import { XhSlider } from '/@/components/Xh/Slider';
import { XhSign } from '/@/components/Xh/Sign';
import { XhSwitch } from '/@/components/Xh/Switch';
import { XhText } from '/@/components/Xh/Text';
import { XhTreeSelect } from '/@/components/Xh/TreeSelect';
import { XhUploadFile, XhUploadImg, XhUploadImgSingle } from '/@/components/Xh/Upload';
import { Tinymce } from '/@/components/Tinymce/index';
import { XhRelationForm } from '/@/components/Xh/RelationForm';
import { XhRelationFormAttr } from '/@/components/Xh/RelationFormAttr';
import { XhPopupSelect } from '/@/components/Xh/PopupSelect';
import { XhPopupAttr } from '/@/components/Xh/PopupAttr';
import { XhNumberRange } from '/@/components/Xh/NumberRange';
import { XhCalculate } from '/@/components/Xh/Calculate';
import { XhInputTable } from '/@/components/Xh/InputTable';
import { XhSysVars } from '/@/components/Xh/SysVars';
import { XhApiSelect } from '/@/components/Xh/ApiSelect';

const XhInputPassword = Input.Password;
XhInputPassword.name = 'XhInputPassword';
const XhInputGroup = Input.Group;
XhInputGroup.name = 'XhInputGroup';
const XhInputSearch = Input.Search;
XhInputSearch.name = 'XhInputSearch';
const XhEditor = Tinymce;
XhEditor.name = 'XhEditor';
const XhGroupTitle = BasicCaption;
XhGroupTitle.name = 'XhGroupTitle';
const XhMonthPicker = DatePicker.MonthPicker;
XhMonthPicker.name = 'XhMonthPicker';
const XhWeekPicker = DatePicker.WeekPicker;
XhWeekPicker.name = 'XhWeekPicker';

export {
  XhAlert,
  XhAreaSelect,
  XhAutoComplete,
  XhButton,
  XhCron,
  XhCascader,
  XhColorPicker,
  XhCheckbox,
  XhCheckboxSingle,
  XhDatePicker,
  XhDateRange,
  XhTimePicker,
  XhTimeRange,
  XhMonthPicker,
  XhWeekPicker,
  XhDivider,
  XhEditor,
  XhGroupTitle,
  XhIconPicker,
  XhInput,
  XhInputPassword,
  XhInputGroup,
  XhInputSearch,
  XhTextarea,
  XhInputNumber,
  XhLink,
  XhOpenData,
  XhOrganizeSelect,
  XhDepSelect,
  XhPosSelect,
  XhGroupSelect,
  XhRoleSelect,
  XhUserSelect,
  XhUsersSelect,
  XhUserSelectDropdown,
  XhQrcode,
  XhBarcode,
  XhRadio,
  XhRate,
  XhSelect,
  XhSlider,
  XhSign,
  XhSwitch,
  XhText,
  XhTreeSelect,
  XhUploadFile,
  XhUploadImg,
  XhUploadImgSingle,
  XhRelationForm,
  XhRelationFormAttr,
  XhPopupSelect,
  XhPopupAttr,
  XhNumberRange,
  XhCalculate,
  XhInputTable,
  XhSysVars,
  XhApiSelect,
};
