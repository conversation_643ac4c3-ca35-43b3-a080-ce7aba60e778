package com.xinghuo.project.biz.model.report;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 仪表板数据VO
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Schema(description = "仪表板数据VO")
public class DashboardDataVO {

    /**
     * 合同总数
     */
    @Schema(description = "合同总数")
    private Integer contractCount;

    /**
     * 本月新增合同数
     */
    @Schema(description = "本月新增合同数")
    private Integer monthlyNewContracts;

    /**
     * 合同总金额
     */
    @Schema(description = "合同总金额")
    private BigDecimal totalContractAmount;

    /**
     * 已收金额
     */
    @Schema(description = "已收金额")
    private BigDecimal receivedAmount;

    /**
     * 待收金额
     */
    @Schema(description = "待收金额")
    private BigDecimal pendingAmount;

    /**
     * 逾期金额
     */
    @Schema(description = "逾期金额")
    private BigDecimal overdueAmount;

    /**
     * 商机总数
     */
    @Schema(description = "商机总数")
    private Integer opportunityCount;

    /**
     * 转化率
     */
    @Schema(description = "转化率")
    private BigDecimal conversionRate;

    /**
     * 已付款金额
     */
    @Schema(description = "已付款金额")
    private BigDecimal paidAmount;

    /**
     * 待付款金额
     */
    @Schema(description = "待付款金额")
    private BigDecimal unpaidAmount;

    /**
     * 毛利金额
     */
    @Schema(description = "毛利金额")
    private BigDecimal grossProfit;

    /**
     * 毛利率
     */
    @Schema(description = "毛利率")
    private BigDecimal grossProfitRate;
}
