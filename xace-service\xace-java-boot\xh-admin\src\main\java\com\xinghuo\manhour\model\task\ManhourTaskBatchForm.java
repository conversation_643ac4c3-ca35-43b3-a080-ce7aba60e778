package com.xinghuo.manhour.model.task;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 工时任务批量提交Form
 *
 * <AUTHOR>
 * @date 2023-12-13
 */
@Data
public class ManhourTaskBatchForm {
    @Schema(description = "填写状态  0-填写中，1-主管已审核 2-分部已确认")
    private Integer status;
    @Schema(description = "批量提交的数据ID")
    private List<String> manhourTaskIds = new ArrayList<>();
}
