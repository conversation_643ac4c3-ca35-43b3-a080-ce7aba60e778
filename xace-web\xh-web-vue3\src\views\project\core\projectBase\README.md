# 项目查询功能说明

## 功能概述

项目查询功能提供了多种方式来查找和管理项目，支持不同的查询类型和灵活的搜索条件。

## 文件结构

```
projectBase/
├── index.vue                 # 原始项目管理页面
├── ProjectQuery.vue          # 项目查询核心组件
├── ProjectQueryIndex.vue     # 项目查询入口页面
├── ProjectBaseModal.vue      # 项目编辑模态框
├── projectBase.data.ts       # 原始数据配置
├── projectQuery.data.ts      # 项目查询数据配置
├── route.ts                  # 路由配置
└── README.md                 # 功能说明文档
```

## 主要功能

### 1. 多种查询类型

- **最近访问**: 显示用户最近访问的项目
- **我关注的**: 显示用户关注的项目
- **我管理的**: 显示用户作为项目经理的项目
- **我参与的**: 显示用户参与的项目团队
- **高级查询**: 支持复杂查询条件
- **全部项目**: 显示所有项目列表

### 2. 灵活的搜索方式

#### 快速搜索
- 在非高级查询标签页中提供快速搜索框
- 支持按项目名称或编码进行搜索
- 实时搜索，输入即查询

#### 高级搜索
- 支持多个查询条件组合
- 包括项目状态、健康度、优先级、类型等
- 支持日期范围查询
- 支持预算金额范围查询

### 3. 动态列配置

根据不同的查询类型，动态显示相关的列：
- 最近访问：显示"最后访问时间"列
- 我关注的：显示"关注时间"列
- 其他类型：隐藏时间相关列

### 4. 统计信息

在入口页面显示各种查询类型的项目数量统计，帮助用户快速了解项目分布情况。

## 技术实现

### 1. 组件架构

```
ProjectQueryIndex (入口页面)
├── 快速访问卡片
├── 统计信息展示
└── ProjectQuery (查询组件)
    ├── 标签页切换
    ├── 搜索表单
    └── 数据表格
```

### 2. 数据配置

#### queryColumns
定义项目查询表格的列配置，支持动态显示/隐藏。

#### advancedSearchFormSchema
定义高级搜索表单的字段配置。

#### queryTypeConfig
定义各种查询类型的配置信息，包括标题、图标、描述等。

### 3. API接口

使用统一的项目查询接口 `getProjectListByType`，通过 `queryType` 参数区分不同的查询类型。

```javascript
// 调用示例
const params = {
  queryType: 'RECENT_VISITED',
  keyword: '搜索关键字',
  pageNum: 1,
  pageSize: 20,
  // 其他查询条件...
};
const result = await getProjectListByType(params);
```

## 使用方法

### 1. 基本查询

1. 访问项目查询页面
2. 点击快速访问卡片或切换标签页
3. 在快速搜索框中输入关键字（可选）
4. 查看查询结果

### 2. 高级查询

1. 切换到"高级查询"标签页
2. 填写查询条件表单
3. 点击"搜索"按钮
4. 查看查询结果

### 3. 项目操作

在查询结果中可以进行以下操作：
- 查看项目详情
- 编辑项目信息
- 删除项目
- 更新项目状态
- 更新项目健康度
- 归档/激活项目

## 扩展说明

### 1. 添加新的查询类型

1. 在 `projectQuery.data.ts` 中的 `queryTypeConfig` 添加新配置
2. 在后端 `ProjectServiceImpl.getList()` 方法中添加对应的查询逻辑
3. 在 `ProjectQuery.vue` 中添加对应的标签页

### 2. 添加新的搜索条件

1. 在 `advancedSearchFormSchema` 中添加新的表单字段
2. 在后端查询逻辑中处理新的查询参数

### 3. 自定义列显示

1. 在 `queryColumns` 中添加新列
2. 在 `queryTypeConfig` 中配置该列在哪些查询类型中显示
3. 使用 `getColumnsForQueryType()` 函数获取对应的列配置

## 注意事项

1. 确保后端接口 `/api/project/core/project/getListByType` 已正确实现
2. 权限控制通过 `v-auth` 指令实现，确保相关权限码已配置
3. 统计数据通过 `getProjectStatistics` 接口获取，需要后端支持
4. 响应式设计已考虑移动端适配

## 相关文件

- 后端控制器: `ProjectBaseController.java`
- 后端服务: `ProjectServiceImpl.java`
- 前端API: `projectBase.ts`
- 路由配置: `route.ts`
