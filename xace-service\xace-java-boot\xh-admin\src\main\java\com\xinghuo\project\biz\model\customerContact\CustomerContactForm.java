package com.xinghuo.project.biz.model.customerContact;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * 客户联系人表单对象
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@Schema(description = "客户联系人表单对象")
public class CustomerContactForm {

    /**
     * 客户ID
     */
    @NotBlank(message = "客户ID不能为空")
    @Schema(description = "客户ID")
    private String cuId;

    /**
     * 联系人姓名
     */
    @NotBlank(message = "联系人姓名不能为空")
    @Size(max = 50, message = "联系人姓名长度不能超过50个字符")
    @Schema(description = "联系人姓名")
    private String linkman;

    /**
     * 联系电话
     */
    @NotBlank(message = "联系电话不能为空")
    @Size(max = 20, message = "联系电话长度不能超过20个字符")
    @Schema(description = "联系电话")
    private String telephone;

    /**
     * 状态 1-有效，0-无效
     */
    @NotNull(message = "状态不能为空")
    @Schema(description = "状态")
    private Integer status;

    /**
     * 岗位/主题
     */
    @Size(max = 100, message = "岗位长度不能超过100个字符")
    @Schema(description = "岗位")
    private String topic;

    /**
     * 内容
     */
    @Size(max = 500, message = "内容长度不能超过500个字符")
    @Schema(description = "内容")
    private String content;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @Schema(description = "备注")
    private String note;
}
