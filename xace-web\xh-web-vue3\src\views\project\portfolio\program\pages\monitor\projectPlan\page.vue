<template>
  <div class="xh-common-layout">
    <a-card title="项目计划概览" :bordered="false" class="mb-4">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-statistic title="按计划执行" :value="onTrackCount" :precision="0">
            <template #suffix>
              <span style="color: #52c41a">个</span>
            </template>
          </a-statistic>
        </a-col>
        <a-col :span="6">
          <a-statistic title="轻微延期" :value="slightDelayCount" :precision="0">
            <template #suffix>
              <span style="color: #faad14">个</span>
            </template>
          </a-statistic>
        </a-col>
        <a-col :span="6">
          <a-statistic title="严重延期" :value="severeDelayCount" :precision="0">
            <template #suffix>
              <span style="color: #f5222d">个</span>
            </template>
          </a-statistic>
        </a-col>
        <a-col :span="6">
          <a-statistic title="提前完成" :value="aheadCount" :precision="0">
            <template #suffix>
              <span style="color: #1890ff">个</span>
            </template>
          </a-statistic>
        </a-col>
      </a-row>
    </a-card>

    <a-card title="甘特图" :bordered="false" class="mb-4">
      <div ref="ganttRef" style="height: 400px"></div>
    </a-card>

    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'planStatus'">
          <a-tag :color="getPlanStatusColor(record.planStatus)">
            {{ record.planStatus }}
          </a-tag>
        </template>
        <template v-if="column.key === 'progress'">
          <a-progress :percent="record.progress" :status="getProgressStatus(record.progress, record.planStatus)" size="small" />
        </template>
        <template v-if="column.key === 'action'">
          <TableAction :actions="getTableActions(record)" />
        </template>
      </template>
    </BasicTable>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, onMounted } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { getTableSchemas } from './tableSchema';
  import { useMessage } from '/@/hooks/web/useMessage';
  import * as echarts from 'echarts';

  defineOptions({ name: 'ProgramProjectPlan' });

  const { createMessage } = useMessage();

  const ganttRef = ref<HTMLDivElement>();

  const projectPlans = [
    {
      id: '152068',
      projectName: '智能制造系统实施与优化项目',
      projectCode: '2025000026',
      planStatus: '轻微延期',
      progress: 30,
      plannedStartDate: '2024-12-31',
      plannedEndDate: '2026-02-26',
      actualStartDate: '2025-03-06',
      estimatedEndDate: '2026-03-15',
      daysDeviation: 17,
      milestoneCount: 8,
      completedMilestones: 2,
      projectManager: '高峰',
      department: '智能制造事业部',
      budget: 2800000,
      actualCost: 840000,
      budgetUsageRate: 30,
      riskLevel: '中',
    },
    {
      id: '151649',
      projectName: '人力资源管理系统（HRMS）咨询项目',
      projectCode: '2025000022',
      planStatus: '按计划',
      progress: 7,
      plannedStartDate: '2025-03-05',
      plannedEndDate: '2025-12-31',
      actualStartDate: '2025-03-06',
      estimatedEndDate: '2025-12-31',
      daysDeviation: 1,
      milestoneCount: 6,
      completedMilestones: 0,
      projectManager: '王子豪,曹静',
      department: '数字化创新研究院',
      budget: 980000,
      actualCost: 68600,
      budgetUsageRate: 7,
      riskLevel: '低',
    },
    {
      id: '150470',
      projectName: 'ERP系统优化项目',
      projectCode: '2025000015',
      planStatus: '按计划',
      progress: 44,
      plannedStartDate: '2025-02-04',
      plannedEndDate: '2025-10-21',
      actualStartDate: '2025-03-05',
      estimatedEndDate: '2025-10-21',
      daysDeviation: 0,
      milestoneCount: 5,
      completedMilestones: 2,
      projectManager: '陈静,王子豪',
      department: '软件科技事业部',
      budget: 1800000,
      actualCost: 792000,
      budgetUsageRate: 44,
      riskLevel: '低',
    },
    {
      id: '150163',
      projectName: 'Demo-组织结构优化与流程再造项目',
      projectCode: '2025000012',
      planStatus: '按计划',
      progress: 40,
      plannedStartDate: '2025-03-04',
      plannedEndDate: '2026-02-26',
      actualStartDate: '2025-03-05',
      estimatedEndDate: '2026-02-26',
      daysDeviation: 1,
      milestoneCount: 7,
      completedMilestones: 3,
      projectManager: '陈静,Demo演示用户,PM',
      department: '战略决策委员会',
      budget: 1500000,
      actualCost: 600000,
      budgetUsageRate: 40,
      riskLevel: '低',
    },
    {
      id: '151862',
      projectName: 'Demo-ERP系统实施与升级项目',
      projectCode: '2025000024',
      planStatus: '严重延期',
      progress: 46,
      plannedStartDate: '2024-01-15',
      plannedEndDate: '2026-03-27',
      actualStartDate: '2025-03-06',
      estimatedEndDate: '2026-05-15',
      daysDeviation: 49,
      milestoneCount: 12,
      completedMilestones: 4,
      projectManager: '陈涛,Demo演示用户,PM',
      department: '合规与审计中心',
      budget: 5200000,
      actualCost: 2392000,
      budgetUsageRate: 46,
      riskLevel: '高',
    },
    {
      id: '153001',
      projectName: '数字化办公平台建设项目',
      projectCode: '2025000028',
      planStatus: '按计划',
      progress: 10,
      plannedStartDate: '2025-08-01',
      plannedEndDate: '2026-06-30',
      actualStartDate: '',
      estimatedEndDate: '2026-06-30',
      daysDeviation: 0,
      milestoneCount: 8,
      completedMilestones: 0,
      projectManager: '李明,张华',
      department: '信息技术部',
      budget: 3200000,
      actualCost: 320000,
      budgetUsageRate: 10,
      riskLevel: '低',
    },
    {
      id: '153002',
      projectName: '客户关系管理系统升级项目',
      projectCode: '2025000029',
      planStatus: '提前完成',
      progress: 100,
      plannedStartDate: '2024-05-01',
      plannedEndDate: '2025-02-28',
      actualStartDate: '2024-05-01',
      estimatedEndDate: '2025-02-20',
      daysDeviation: -8,
      milestoneCount: 6,
      completedMilestones: 6,
      projectManager: '刘强,赵敏',
      department: '销售事业部',
      budget: 1600000,
      actualCost: 1520000,
      budgetUsageRate: 95,
      riskLevel: '低',
    },
    {
      id: '153003',
      projectName: '供应链管理优化项目',
      projectCode: '2025000030',
      planStatus: '严重延期',
      progress: 25,
      plannedStartDate: '2024-10-01',
      plannedEndDate: '2025-12-31',
      actualStartDate: '2024-10-15',
      estimatedEndDate: '2026-03-31',
      daysDeviation: 90,
      milestoneCount: 8,
      completedMilestones: 1,
      projectManager: '孙伟,钱丽',
      department: '采购供应链部',
      budget: 2200000,
      actualCost: 550000,
      budgetUsageRate: 25,
      riskLevel: '高',
    },
    {
      id: '153004',
      projectName: '移动办公APP开发项目',
      projectCode: '2025000031',
      planStatus: '按计划',
      progress: 75,
      plannedStartDate: '2024-11-01',
      plannedEndDate: '2025-08-31',
      actualStartDate: '2024-11-01',
      estimatedEndDate: '2025-08-31',
      daysDeviation: 0,
      milestoneCount: 6,
      completedMilestones: 4,
      projectManager: '周杰,陈美',
      department: '移动互联网事业部',
      budget: 1800000,
      actualCost: 1350000,
      budgetUsageRate: 75,
      riskLevel: '低',
    },
    {
      id: '153005',
      projectName: '数据中心建设项目',
      projectCode: '2025000032',
      planStatus: '轻微延期',
      progress: 60,
      plannedStartDate: '2025-01-01',
      plannedEndDate: '2025-12-31',
      actualStartDate: '2025-01-01',
      estimatedEndDate: '2026-01-15',
      daysDeviation: 15,
      milestoneCount: 10,
      completedMilestones: 6,
      projectManager: '吴强,林晓',
      department: '基础设施部',
      budget: 8500000,
      actualCost: 5100000,
      budgetUsageRate: 60,
      riskLevel: '中',
    },
  ];

  const onTrackCount = computed(() => projectPlans.filter(p => p.planStatus === '按计划').length);
  const slightDelayCount = computed(() => projectPlans.filter(p => p.planStatus === '轻微延期').length);
  const severeDelayCount = computed(() => projectPlans.filter(p => p.planStatus === '严重延期').length);
  const aheadCount = computed(() => projectPlans.filter(p => p.planStatus === '提前完成').length);

  const [registerTable] = useTable({
    api: async () => {
      return {
        list: projectPlans,
        pagination: {
          total: projectPlans.length,
        },
      };
    },
    columns: getTableSchemas(),
    useSearchForm: true,
    formConfig: {
      labelWidth: 100,
      schemas: [
        {
          field: 'projectName',
          label: '项目名称',
          component: 'Input',
          colProps: { span: 6 },
        },
        {
          field: 'planStatus',
          label: '计划状态',
          component: 'Select',
          colProps: { span: 6 },
          componentProps: {
            options: [
              { label: '按计划', value: '按计划' },
              { label: '轻微延期', value: '轻微延期' },
              { label: '严重延期', value: '严重延期' },
              { label: '提前完成', value: '提前完成' },
            ],
          },
        },
      ],
    },
    actionColumn: {
      width: 150,
      title: '操作',
      dataIndex: 'action',
    },
  });

  function getPlanStatusColor(status: string) {
    const colorMap = {
      按计划: 'success',
      轻微延期: 'warning',
      严重延期: 'error',
      提前完成: 'processing',
    };
    return colorMap[status] || 'default';
  }

  function getProgressStatus(progress: number, planStatus: string) {
    if (progress === 100) return 'success';
    if (planStatus === '严重延期') return 'exception';
    return 'active';
  }

  function getTableActions(record) {
    return [
      {
        label: '查看详情',
        onClick: () => handleDetail(record),
      },
      {
        label: '计划调整',
        onClick: () => handleAdjust(record),
      },
    ];
  }

  function handleDetail(record) {
    createMessage.info(`查看计划详情: ${record.projectName}`);
  }

  function handleAdjust(record) {
    createMessage.info(`调整项目计划: ${record.projectName}`);
  }

  onMounted(() => {
    if (ganttRef.value) {
      const chart = echarts.init(ganttRef.value);

      const categories = projectPlans.map(p => p.projectName);
      const series = projectPlans.map((project, index) => {
        const startDate = new Date(project.actualStartDate).getTime();
        const endDate = new Date(project.estimatedEndDate).getTime();
        return {
          name: project.projectName,
          value: [index, startDate, endDate, project.progress],
          itemStyle: {
            color:
              getPlanStatusColor(project.planStatus) === 'success'
                ? '#52c41a'
                : getPlanStatusColor(project.planStatus) === 'warning'
                ? '#faad14'
                : getPlanStatusColor(project.planStatus) === 'error'
                ? '#f5222d'
                : '#1890ff',
          },
        };
      });

      const option = {
        tooltip: {
          formatter: params => {
            const data = params.data.value;
            return `${params.name}<br/>
                    开始时间: ${new Date(data[1]).toLocaleDateString()}<br/>
                    结束时间: ${new Date(data[2]).toLocaleDateString()}<br/>
                    进度: ${data[3]}%`;
          },
        },
        grid: {
          left: '10%',
          right: '10%',
          top: '10%',
          bottom: '10%',
        },
        xAxis: {
          type: 'time',
          position: 'top',
          splitLine: {
            lineStyle: {
              color: ['#E9EDFF'],
            },
          },
          axisLine: {
            show: false,
          },
        },
        yAxis: {
          data: categories,
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
        },
        series: [
          {
            type: 'custom',
            renderItem: (params, api) => {
              const categoryIndex = api.value(0);
              const start = api.coord([api.value(1), categoryIndex]);
              const end = api.coord([api.value(2), categoryIndex]);
              const height = api.size([0, 1])[1] * 0.6;

              return {
                type: 'rect',
                shape: {
                  x: start[0],
                  y: start[1] - height / 2,
                  width: end[0] - start[0],
                  height: height,
                },
                style: api.style(),
              };
            },
            data: series,
          },
        ],
      };

      chart.setOption(option);
    }
  });
</script>
