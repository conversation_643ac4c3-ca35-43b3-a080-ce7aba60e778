package com.xinghuo.project.biz.model.bizBusinessWeeklog;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 商机周报视图对象
 * 
 * <AUTHOR>
 * @date 2025-07-08
 */
@Data
@Schema(description = "商机周报视图对象")
public class BizBusinessWeeklogVO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private String id;

    /**
     * 所属分部
     */
    @Schema(description = "所属分部")
    private String fbId;

    /**
     * 所属分部名称（非数据库字段，需要关联查询）
     */
    @Schema(description = "所属分部名称")
    private String fbName;

    /**
     * 项目类型
     */
    @Schema(description = "项目类型")
    private String projType;

    /**
     * 项目类型名称（非数据库字段，需要转换）
     */
    @Schema(description = "项目类型名称")
    private String projTypeName;

    /**
     * 项目ID
     */
    @Schema(description = "项目ID")
    private String projId;

    /**
     * 项目名称
     */
    @Schema(description = "项目名称")
    private String projName;

    /**
     * 项目备注
     */
    @Schema(description = "项目备注")
    private String projNote;

    /**
     * 项目级别
     */
    @Schema(description = "项目级别")
    private String projectLevel;

    /**
     * 项目级别名称（非数据库字段，需要转换）
     */
    @Schema(description = "项目级别名称")
    private String projectLevelName;

    /**
     * 开始日期
     */
    @Schema(description = "开始日期")
    private Date startDate;

    /**
     * 结束日期
     */
    @Schema(description = "结束日期")
    private Date endDate;

    /**
     * 录入日期
     */
    @Schema(description = "录入日期")
    private Date inputDate;

    /**
     * 负责人ID
     */
    @Schema(description = "负责人ID")
    private String ownId;

    /**
     * 负责人名称（非数据库字段，需要关联查询）
     */
    @Schema(description = "负责人名称")
    private String ownName;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 计划
     */
    @Schema(description = "计划")
    private String plan;

    /**
     * 风险
     */
    @Schema(description = "风险")
    private String risk;

    /**
     * 状态 (1-表示已填写，0-未填写，2-提交审核，3-已发布，-1-已驳回)
     */
    @Schema(description = "状态")
    private Integer status;

    /**
     * 状态名称（非数据库字段，需要转换）
     */
    @Schema(description = "状态名称")
    private String statusName;

    /**
     * 显示状态 (0-表示未显示，1-表示显示)
     */
    @Schema(description = "显示状态")
    private Integer showStatus;

    /**
     * 显示状态名称（非数据库字段，需要转换）
     */
    @Schema(description = "显示状态名称")
    private String showStatusName;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createdAt;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createdBy;

    /**
     * 创建人名称（非数据库字段，需要关联查询）
     */
    @Schema(description = "创建人名称")
    private String createdByName;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date lastUpdatedAt;

    /**
     * 更新人
     */
    @Schema(description = "更新人")
    private String lastUpdatedBy;

    /**
     * 更新人名称（非数据库字段，需要关联查询）
     */
    @Schema(description = "更新人名称")
    private String lastUpdatedByName;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private String tenantId;

    /**
     * 关联商机信息（非数据库字段，用于编辑页面数据支持）
     */
    @Schema(description = "关联商机信息")
    private OpportunityInfo opportunityInfo;

    /**
     * 商机信息内部类
     */
    @Data
    @Schema(description = "商机信息")
    public static class OpportunityInfo {
        /**
         * 商机ID
         */
        @Schema(description = "商机ID")
        private String opportunityId;

        /**
         * 商机名称
         */
        @Schema(description = "商机名称")
        private String opportunityName;

        /**
         * 商机状态
         */
        @Schema(description = "商机状态")
        private String opportunityStatus;

        /**
         * 商机阶段
         */
        @Schema(description = "商机阶段")
        private String opportunityStage;

        /**
         * 预计金额
         */
        @Schema(description = "预计金额")
        private java.math.BigDecimal expectedAmount;

        /**
         * 客户名称
         */
        @Schema(description = "客户名称")
        private String customerName;
    }
}
