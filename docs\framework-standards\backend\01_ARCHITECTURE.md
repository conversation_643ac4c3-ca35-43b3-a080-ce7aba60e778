# XACE 后端架构与技术栈

## 核心技术栈

### 基础技术
- **语言**：Java 17+
- **构建工具**：Maven 3.6.3+
- **主要框架**：
  - Spring Boot 3.x
  - MyBatis-Plus（数据持久化）
  - Spring Security（安全认证）
  - Spring Cloud（微服务支持）
  - Lombok（代码简化）

### 数据库支持
- **主数据库**：MySQL 5.7+ / SQL Server 2012+ / Oracle 11g+ / PostgreSQL 12+
- **缓存**：Redis 5.0+（缓存和会话管理）

## 项目架构

### 多模块Maven架构
```
xace-service/
├── xace-common/           # 公共模块
│   └── xh-boot-common/    # 基础工具类、配置等
├── xace-java-boot/        # 主应用模块
│   ├── xh-admin/          # 系统管理与业务主模块
│   ├── xh-system/         # 系统功能支持模块
│   ├── xh-oauth/          # 身份验证与授权模块
│   ├── xh-file/           # 文件管理模块
│   ├── xh-workflow-engine/# 工作流引擎模块
│   ├── xh-visualdev/      # 可视化开发模块
│   └── xh-ext-*/          # 扩展功能模块
└── xace-datareport/       # 报表服务
```

### 分层架构原则
- **控制器层（Controller）**：只负责接收请求、参数校验和返回结果，不包含业务逻辑
- **服务层（Service）**：包含所有业务逻辑，事务控制在此层进行
- **数据访问层（Mapper）**：只负责数据库操作，不包含业务逻辑
- **实体层（Entity）**：与数据库表结构一一对应
- **模型层（Model）**：包含Form、VO、Pagination等数据传输对象

### 模块化原则
代码应根据功能划分到相应的子模块中，每个模块应相对独立，通过接口进行交互，避免紧耦合依赖。

## 包结构规范

### 基础包命名
所有代码使用 `com.xinghuo` 作为基础包名。

### 标准包结构
```
com.xinghuo.[模块名]/
├── controller/            # 控制器层
├── service/               # 服务接口
├── service.impl/          # 服务实现
├── mapper/                # MyBatis映射接口
├── entity/                # 数据库实体
├── model.[业务功能]/       # 模型类（VO、Form、Pagination）
├── config/                # 配置类
├── util/                  # 工具类
├── enums/                 # 枚举类
├── constant/              # 常量类
└── exception/             # 异常类
```

### 命名规则
| 类型 | 命名规则 | 示例 |
|------|----------|------|
| 控制器 | `[业务名]Controller` | `UserController`, `ProjectController` |
| 服务接口 | `[业务名]Service` | `UserService`, `ProjectService` |
| 服务实现 | `[业务名]ServiceImpl` | `UserServiceImpl`, `ProjectServiceImpl` |
| 数据访问 | `[业务名]Mapper` | `UserMapper`, `ProjectMapper` |
| 实体 | `[业务名]Entity` | `UserEntity`, `ProjectEntity` |
| 表单对象 | `[业务名][操作]Form` | `UserForm`, `UserCrForm`, `ProjectUpForm` |
| 视图对象 | `[业务名]VO` | `UserVO`, `ProjectVO` |
| 分页查询 | `[业务名]Pagination` | `UserPagination`, `ProjectPagination` |
| 异常类 | `[业务名]Exception` | `UserNotFoundException`, `ProjectOperationException` |

## Jakarta EE 规范

### 重要说明
> 由于使用 JDK 17+ 和 Spring Boot 3.x，必须使用 Jakarta EE 规范。详细包名对照和迁移要点请查看：[后端开发规范总览](./README.md#重要jakarta-ee-规范jdk-17)

## 项目内部包导入

### 关键导入路径
```java
// 分页基类
import com.xinghuo.common.base.model.Pagination;

// 服务基类
import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.common.base.service.BaseServiceImpl;

// Mapper基类
import com.xinghuo.common.base.dao.XHBaseMapper;

// 实体基类
import com.xinghuo.common.base.entity.BaseEntityV2;

// 响应结果
import com.xinghuo.common.base.ActionResult;

// 工具类
import com.xinghuo.common.util.core.BeanCopierUtils;
```

## 资源文件结构

```
src/main/resources/
├── application.yml                    # 主配置文件
├── application-dev.yml               # 开发环境配置
├── application-test.yml              # 测试环境配置
├── application-prod.yml              # 生产环境配置
├── static/                           # 静态资源
└── templates/                        # 模板文件
```

## 开发工具推荐

### IDEA 插件
- **Lombok**（必须）- 代码简化
- **Alibaba Java Coding Guidelines** - 代码规范检查
- **MyBatisX** - MyBatis 增强
- **SonarLint** - 代码质量检查
- **Maven Helper** - 依赖管理
- **Spring Boot Assistant** - Spring 配置辅助

### 开发配置要求
1. 配置 IDE 自动导入 `jakarta.*` 包
2. 使用统一的代码格式化配置
3. 启用自动保存和格式化
4. 配置 Maven 使用项目指定版本

## 参考资源

- [Spring Boot 官方文档](https://spring.io/projects/spring-boot)
- [MyBatis-Plus 官方文档](https://baomidou.com/)
- [Jakarta EE 规范](https://jakarta.ee/)
- [阿里巴巴Java开发手册](https://github.com/alibaba/p3c)