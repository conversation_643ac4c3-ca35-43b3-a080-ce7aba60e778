import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { h } from 'vue';
import { Tag } from 'ant-design-vue';

export const columns: BasicColumn[] = [
  {
    title: '项目编码',
    dataIndex: 'code',
    width: 120,
    fixed: 'left',
  },
  {
    title: '项目名称',
    dataIndex: 'name',
    width: 200,
    fixed: 'left',
  },
  {
    title: '项目类型',
    dataIndex: 'typeId',
    width: 120,
  },
  {
    title: '项目经理',
    dataIndex: 'managerId',
    width: 120,
  },
  {
    title: '项目状态',
    dataIndex: 'status',
    width: 100,
  },
  {
    title: '健康度',
    dataIndex: 'health',
    width: 100,
  },
  {
    title: '优先级',
    dataIndex: 'priority',
    width: 100,
  },
  {
    title: '计划开始日期',
    dataIndex: 'plannedStartDate',
    width: 120,
    format: 'date|YYYY-MM-DD',
  },
  {
    title: '计划结束日期',
    dataIndex: 'plannedEndDate',
    width: 120,
    format: 'date|YYYY-MM-DD',
  },
  {
    title: '实际开始日期',
    dataIndex: 'actualStartDate',
    width: 120,
    format: 'date|YYYY-MM-DD',
  },
  {
    title: '实际结束日期',
    dataIndex: 'actualEndDate',
    width: 120,
    format: 'date|YYYY-MM-DD',
  },
  {
    title: '预算金额',
    dataIndex: 'budgetAmount',
    width: 120,
    format: 'currency',
  },
  {
    title: '实际花费',
    dataIndex: 'actualCost',
    width: 120,
    format: 'currency',
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    width: 150,
    format: 'date|YYYY-MM-DD HH:mm:ss',
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'keyword',
    label: '关键字',
    component: 'Input',
    componentProps: {
      placeholder: '请输入项目名称或编码',
    },
    colProps: { span: 8 },
  },
  {
    field: 'status',
    label: '项目状态',
    component: 'Select',
    componentProps: {
      options: [
        { label: '规划中', value: 'planning' },
        { label: '执行中', value: 'executing' },
        { label: '已完成', value: 'completed' },
        { label: '已关闭', value: 'closed' },
        { label: '已取消', value: 'cancelled' },
        { label: '已归档', value: 'archived' },
      ],
    },
    colProps: { span: 8 },
  },
  {
    field: 'health',
    label: '健康度',
    component: 'Select',
    componentProps: {
      options: [
        { label: '正常', value: 'normal' },
        { label: '预警', value: 'warning' },
        { label: '风险', value: 'risk' },
      ],
    },
    colProps: { span: 8 },
  },
  {
    field: 'priority',
    label: '优先级',
    component: 'Select',
    componentProps: {
      options: [
        { label: '高', value: 'high' },
        { label: '中', value: 'medium' },
        { label: '低', value: 'low' },
      ],
    },
    colProps: { span: 8 },
  },
  {
    field: 'managerId',
    label: '项目经理',
    component: 'Input',
    componentProps: {
      placeholder: '请输入项目经理ID',
    },
    colProps: { span: 8 },
  },
  {
    field: 'deptId',
    label: '部门',
    component: 'Input',
    componentProps: {
      placeholder: '请输入部门ID',
    },
    colProps: { span: 8 },
  },
  {
    field: '[startDateStart, startDateEnd]',
    label: '计划开始日期',
    component: 'RangePicker',
    colProps: { span: 8 },
  },
  {
    field: '[endDateStart, endDateEnd]',
    label: '计划结束日期',
    component: 'RangePicker',
    colProps: { span: 8 },
  },
];

export const formSchema: FormSchema[] = [
  {
    field: 'id',
    label: 'ID',
    component: 'Input',
    show: false,
  },
  {
    field: 'code',
    label: '项目编码',
    component: 'Input',
    componentProps: {
      placeholder: '请输入项目编码（可自动生成）',
    },
    colProps: { span: 12 },
  },
  {
    field: 'name',
    label: '项目名称',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入项目名称',
    },
    colProps: { span: 12 },
  },
  {
    field: 'typeId',
    label: '项目类型',
    component: 'Select',
    componentProps: {
      placeholder: '请选择项目类型',
      options: [
        { label: '软件开发', value: 'software' },
        { label: '系统集成', value: 'integration' },
        { label: '咨询服务', value: 'consulting' },
        { label: '运维服务', value: 'maintenance' },
      ],
    },
    colProps: { span: 12 },
  },
  {
    field: 'managerId',
    label: '项目经理',
    component: 'Input',
    componentProps: {
      placeholder: '请选择项目经理',
    },
    colProps: { span: 12 },
  },
  {
    field: 'programId',
    label: '所属项目群',
    component: 'Input',
    componentProps: {
      placeholder: '请选择所属项目群',
    },
    colProps: { span: 12 },
  },
  {
    field: 'status',
    label: '项目状态',
    component: 'Select',
    componentProps: {
      options: [
        { label: '规划中', value: 'planning' },
        { label: '执行中', value: 'executing' },
        { label: '已完成', value: 'completed' },
        { label: '已关闭', value: 'closed' },
        { label: '已取消', value: 'cancelled' },
      ],
    },
    colProps: { span: 12 },
  },
  {
    field: 'health',
    label: '健康度',
    component: 'Select',
    componentProps: {
      options: [
        { label: '正常', value: 'normal' },
        { label: '预警', value: 'warning' },
        { label: '风险', value: 'risk' },
      ],
    },
    colProps: { span: 12 },
  },
  {
    field: 'priority',
    label: '优先级',
    component: 'Select',
    componentProps: {
      options: [
        { label: '高', value: 'high' },
        { label: '中', value: 'medium' },
        { label: '低', value: 'low' },
      ],
    },
    colProps: { span: 12 },
  },
  {
    field: 'plannedStartDate',
    label: '计划开始日期',
    component: 'DatePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      placeholder: '请选择计划开始日期',
    },
    colProps: { span: 12 },
  },
  {
    field: 'plannedEndDate',
    label: '计划结束日期',
    component: 'DatePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      placeholder: '请选择计划结束日期',
    },
    colProps: { span: 12 },
  },
  {
    field: 'actualStartDate',
    label: '实际开始日期',
    component: 'DatePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      placeholder: '请选择实际开始日期',
    },
    colProps: { span: 12 },
  },
  {
    field: 'actualEndDate',
    label: '实际结束日期',
    component: 'DatePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      placeholder: '请选择实际结束日期',
    },
    colProps: { span: 12 },
  },
  {
    field: 'budgetAmount',
    label: '预算金额',
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入预算金额',
      min: 0,
      precision: 2,
    },
    colProps: { span: 12 },
  },
  {
    field: 'actualCost',
    label: '实际花费',
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入实际花费',
      min: 0,
      precision: 2,
    },
    colProps: { span: 12 },
  },
  {
    field: 'departmentId',
    label: '所属部门',
    component: 'Input',
    componentProps: {
      placeholder: '请选择所属部门',
    },
    colProps: { span: 12 },
  },
  {
    field: 'customerId',
    label: '客户',
    component: 'Input',
    componentProps: {
      placeholder: '请选择客户',
    },
    colProps: { span: 12 },
  },
  {
    field: 'contractId',
    label: '相关合同',
    component: 'Input',
    componentProps: {
      placeholder: '请选择相关合同',
    },
    colProps: { span: 12 },
  },
  {
    field: 'description',
    label: '项目描述',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入项目描述',
      rows: 4,
    },
    colProps: { span: 24 },
  },
];
