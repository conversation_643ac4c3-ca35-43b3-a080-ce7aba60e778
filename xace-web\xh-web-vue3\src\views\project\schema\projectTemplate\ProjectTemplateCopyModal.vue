<template>
  <BasicModal v-bind="$attrs" @register="registerModal" title="复制项目模板" :width="500" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { copyProjectTemplate, checkProjectTemplateNameExists } from '/@/api/project/projectTemplate';

  defineOptions({ name: 'ProjectTemplateCopyModal' });

  const emit = defineEmits(['success', 'register']);
  const { createMessage } = useMessage();

  const sourceRecord = ref<Recordable>({});

  const [registerForm, { setFieldsValue, resetFields, validate, updateSchema }] = useForm({
    labelWidth: 100,
    baseColProps: { span: 24 },
    schemas: [
      {
        field: 'sourceName',
        label: '源模板',
        component: 'Input',
        componentProps: {
          disabled: true,
        },
      },
      {
        field: 'newName',
        label: '新模板名称',
        component: 'Input',
        required: true,
        rules: [
          { required: true, message: '请输入新的模板名称' },
          { min: 2, max: 255, message: '模板名称长度必须在2-255个字符之间' },
          { pattern: /^[^<>"'&]*$/, message: '模板名称不能包含特殊字符' },
        ],
        componentProps: {
          placeholder: '请输入新的模板名称',
          maxlength: 255,
          showCount: true,
        },
      },
    ],
    showActionButtonGroup: false,
  });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async data => {
    resetFields();
    setModalProps({ confirmLoading: false });

    sourceRecord.value = data.record;

    setFieldsValue({
      sourceName: data.record.name,
      newName: `${data.record.name}_副本`,
    });

    // 设置名称验证规则
    updateSchema({
      field: 'newName',
      componentProps: {
        onBlur: async (e: any) => {
          const name = e.target.value?.trim();
          if (name) {
            try {
              const response = await checkProjectTemplateNameExists(name);
              if (response.code === 200 && response.data) {
                createMessage.error('模板名称已存在，请修改后重试');
                return Promise.reject('模板名称已存在');
              } else if (response.code === 200) {
                createMessage.success('模板名称可用');
              }
            } catch (error) {
              console.error('检查名称失败:', error);
              createMessage.error('检查名称失败，请稍后重试');
            }
          }
        },
      },
    });
  });

  async function handleSubmit() {
    try {
      const values = await validate();
      if (!values) {
        return;
      }

      // 数据清理
      const cleanedName = values.newName?.trim();
      if (!cleanedName) {
        createMessage.error('请输入有效的模板名称');
        return;
      }

      // 再次检查名称是否存在
      const nameCheckResponse = await checkProjectTemplateNameExists(cleanedName);
      if (nameCheckResponse.code === 200 && nameCheckResponse.data) {
        createMessage.error('模板名称已存在，请修改后重试');
        return;
      }

      setModalProps({ confirmLoading: true });

      const response = await copyProjectTemplate(sourceRecord.value.id, cleanedName);
      if (response.code === 200) {
        createMessage.success(`模板复制成功！新模板“${cleanedName}”已创建`);
        closeModal();
        emit('success');
      } else {
        createMessage.error(response.msg || '复制失败，请检查输入信息后重试');
      }
    } catch (error) {
      console.error('复制失败:', error);
      createMessage.error('复制失败，请检查网络连接后重试');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
