<template>
  <a-card :loading="loading" class="project-info-card">
    <template #title>
      <div class="flex items-center">
        <i class="icon-ym icon-ym-project mr-2 text-lg text-blue-500"></i>
        <span>项目信息</span>
      </div>
    </template>

    <template #extra>
      <a-space>
        <a-tooltip title="刷新项目信息">
          <a-button type="text" size="small" @click="refreshProjectInfo">
            <template #icon><ReloadOutlined /></template>
          </a-button>
        </a-tooltip>
        <a-tooltip title="收藏项目">
          <a-button type="text" size="small" @click="toggleFavorite">
            <template #icon>
              <StarFilled v-if="projectInfo?.isFavorite" class="text-yellow-500" />
              <StarOutlined v-else />
            </template>
          </a-button>
        </a-tooltip>
      </a-space>
    </template>

    <div v-if="!projectId" class="text-center py-8 text-gray-400">
      <i class="icon-ym icon-ym-project text-4xl mb-4 block"></i>
      <p>请选择一个项目查看详细信息</p>
    </div>

    <div v-else-if="projectInfo" class="project-info-content">
      <!-- 基本信息 -->
      <div class="grid grid-cols-2 gap-4 mb-6">
        <div>
          <label class="text-gray-600 text-sm">项目编码</label>
          <p class="font-medium">{{ projectInfo.code || '-' }}</p>
        </div>
        <div>
          <label class="text-gray-600 text-sm">项目状态</label>
          <p>
            <a-tag :color="getStatusColor(projectInfo.status)">
              {{ getStatusText(projectInfo.status) }}
            </a-tag>
          </p>
        </div>
        <div>
          <label class="text-gray-600 text-sm">项目类型</label>
          <p>
            <a-tag color="blue">{{ getTypeText(projectInfo.typeId) }}</a-tag>
          </p>
        </div>
        <div>
          <label class="text-gray-600 text-sm">项目经理</label>
          <p class="font-medium">经理ID: {{ projectInfo.managerId || '-' }}</p>
        </div>
        <div>
          <label class="text-gray-600 text-sm">计划开始</label>
          <p>{{ formatToDate(projectInfo.plannedStartDate) }}</p>
        </div>
        <div>
          <label class="text-gray-600 text-sm">计划结束</label>
          <p>{{ formatToDate(projectInfo.plannedEndDate) }}</p>
        </div>
      </div>

      <!-- 项目描述 -->
      <div v-if="projectInfo.description" class="mb-6">
        <label class="text-gray-600 text-sm">项目描述</label>
        <p class="mt-1 text-gray-800">{{ projectInfo.description }}</p>
      </div>

      <!-- 快速操作 -->
      <div class="border-t pt-4">
        <label class="text-gray-600 text-sm mb-3 block">快速操作</label>
        <div class="grid grid-cols-2 gap-3">
          <a-button type="primary" block @click="enterProject">
            <template #icon><RightOutlined /></template>
            进入项目
          </a-button>
          <a-button block @click="viewProjectDetail">
            <template #icon><EyeOutlined /></template>
            查看详情
          </a-button>
          <a-button block @click="editProject">
            <template #icon><EditOutlined /></template>
            编辑项目
          </a-button>
          <a-button block @click="projectSettings">
            <template #icon><SettingOutlined /></template>
            项目设置
          </a-button>
        </div>
      </div>
    </div>
  </a-card>
</template>

<script setup lang="ts">
  import { ref, watch } from 'vue';
  import { ReloadOutlined, StarFilled, StarOutlined, RightOutlined, EyeOutlined, EditOutlined, SettingOutlined } from '@ant-design/icons-vue';
  import { getProjectInfo } from '/@/api/project/projectBase';
  import { to } from '/@/utils/xh';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useRouter } from 'vue-router';
  import { formatToDate } from '/@/utils/dateUtil';

  const { createMessage } = useMessage();
  const router = useRouter();

  const loading = ref(false);
  const projectInfo = ref(null);

  const props = defineProps({
    projectId: {
      type: String,
      default: '',
    },
  });

  const emit = defineEmits(['enter-project']);

  // 监听项目ID变化
  watch(
    () => props.projectId,
    newId => {
      if (newId) {
        loadProjectInfo(newId);
      } else {
        projectInfo.value = null;
      }
    },
    { immediate: true },
  );

  async function loadProjectInfo(projectId: string) {
    if (!projectId) return;

    loading.value = true;
    try {
      const [err, res] = await to(getProjectInfo(projectId));
      if (!err && res?.data) {
        projectInfo.value = res.data;
      } else {
        createMessage.error('获取项目信息失败');
      }
    } catch (error) {
      console.error('加载项目信息失败:', error);
      createMessage.error('获取项目信息失败');
    } finally {
      loading.value = false;
    }
  }

  async function refreshProjectInfo() {
    if (props.projectId) {
      await loadProjectInfo(props.projectId);
      createMessage.success('项目信息已刷新');
    }
  }

  async function toggleFavorite() {
    // TODO: 实现收藏/取消收藏功能
    createMessage.info('收藏功能开发中...');
  }

  function enterProject() {
    if (props.projectId) {
      emit('enter-project', props.projectId);
      // 可以跳转到项目详情页面或者触发父组件的菜单切换
    }
  }

  function viewProjectDetail() {
    if (props.projectId) {
      // 跳转到项目详情页面
      router.push(`/project/detail/${props.projectId}`);
    }
  }

  function editProject() {
    if (props.projectId) {
      // 跳转到项目编辑页面
      router.push(`/project/edit/${props.projectId}`);
    }
  }

  function projectSettings() {
    if (props.projectId) {
      // 跳转到项目设置页面
      router.push(`/project/settings/${props.projectId}`);
    }
  }

  function getStatusColor(status: string) {
    const statusMap = {
      PLANNING: 'blue',
      EXECUTING: 'green',
      TRACKING: 'cyan',
      SIGNED: 'purple',
      SUSPENDED: 'orange',
      COMPLETED: 'green',
      CANCELLED: 'red',
    };
    return statusMap[status] || 'default';
  }

  function getStatusText(status: string) {
    const statusMap = {
      PLANNING: '规划中',
      EXECUTING: '执行中',
      TRACKING: '跟踪中',
      SIGNED: '已签约',
      SUSPENDED: '暂停',
      COMPLETED: '已完成',
      CANCELLED: '已取消',
    };
    return statusMap[status] || status;
  }

  function getTypeText(typeId: string) {
    const typeMap = {
      S: '软件项目',
      M: '维护项目',
      I: '基础设施',
      C: '建设项目',
      R: '研发项目',
    };
    return typeMap[typeId] || typeId;
  }
</script>

<style lang="less" scoped>
  .project-info-card {
    .project-info-content {
      label {
        display: block;
        margin-bottom: 4px;
        font-size: 12px;
        font-weight: 500;
      }

      p {
        margin-bottom: 0;
        min-height: 22px;
      }
    }
  }
</style>
