package com.xinghuo.project.biz.model;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 商机状态更新表单
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@Schema(description = "商机状态更新表单")
public class OpportunityStatusForm {

    /**
     * 状态
     */
    @Schema(description = "状态", required = true)
    @NotBlank(message = "状态不能为空")
    private String status;

    /**
     * 最后跟踪记录
     */
    @Schema(description = "最后跟踪记录")
    private String lastNote;

    /**
     * 合同编号（当状态为"已签"时需要）
     */
    @Schema(description = "合同编号")
    private String projectNo;
}
