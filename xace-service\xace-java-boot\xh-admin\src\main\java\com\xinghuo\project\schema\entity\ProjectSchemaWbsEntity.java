package com.xinghuo.project.schema.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目模板的私有WBS计划表实体类
 * 对应数据库表：zz_proj_schema_wbs
 *
 * 此表存储项目模板的专属WBS配置，与传统的直接引用WBS模板不同，
 * 每个项目模板都有自己的私有WBS配置，可以从WBS模板或活动库导入，
 * 但导入后成为项目模板的专属配置，可以独立修改而不影响源模板。
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("zz_proj_schema_wbs")
public class ProjectSchemaWbsEntity extends BaseEntityV2.CUBaseEntityV2<String> {

    /**
     * 所属项目模板ID (关联 zz_proj_template)
     */
    @TableField("project_template_id")
    private String projectTemplateId;

    /**
     * 源自哪个WBS模板明细ID (用于追溯)
     */
    @TableField("source_wbs_detail_id")
    private String sourceWbsDetailId;

    /**
     * 源自哪个标准活动库ID
     */
    @TableField("source_library_activity_id")
    private String sourceLibraryActivityId;

    /**
     * 父级ID (指向本表的f_id)
     */
    @TableField("parent_id")
    private String parentId;

    /**
     * WBS编码
     */
    @TableField("wbs_code")
    private String wbsCode;

    /**
     * 活动/工作包名称
     */
    @TableField("name")
    private String name;

    /**
     * 显示顺序
     */
    @TableField("seq_no")
    private Integer seqNo;

    /**
     * 层级深度
     */
    @TableField("level")
    private Integer level;

    /**
     * 节点类型 (1:活动, 3:工作包/摘要)
     */
    @TableField("node_type")
    private Integer nodeType;

    /**
     * 是否是里程碑 (1:是, 0:否)
     */
    @TableField("is_milestone")
    private Integer isMilestone;

    /**
     * 计划工期 (天)
     */
    @TableField("duration")
    private BigDecimal duration;

    /**
     * 计划开始偏移天数
     */
    @TableField("plan_start_offset")
    private Integer planStartOffset;

    /**
     * 约束类型ID
     */
    @TableField("constraint_type_id")
    private String constraintTypeId;

    /**
     * 约束日期
     */
    @TableField("constraint_date")
    private Date constraintDate;

    /**
     * 责任角色ID
     */
    @TableField("response_role_id")
    private String responseRoleId;

    /**
     * 前置任务ID列表 (指向本表的f_id)
     */
    @TableField("predecessors")
    private String predecessors;
}
