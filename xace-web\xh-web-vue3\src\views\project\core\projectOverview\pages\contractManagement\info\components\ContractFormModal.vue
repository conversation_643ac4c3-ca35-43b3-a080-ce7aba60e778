<template>
  <a-modal
    v-model:visible="modalVisible"
    :width="1200"
    :title="formTitle"
    :confirm-loading="loading"
    :mask-closable="false"
    :keyboard="false"
    @ok="handleOk"
    @cancel="handleCancel">
    <div class="contract-form-modal">
      <a-form ref="formRef" :model="formData" :rules="formRules" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <a-tabs v-model:active-key="activeTab" type="card">
          <!-- 基本信息 -->
          <a-tab-pane key="basic" tab="基本信息">
            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item label="合同名称" name="name" required>
                  <a-input v-model:value="formData.name" placeholder="请输入合同名称" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="合同编号" name="cno" required>
                  <a-input v-model:value="formData.cno" placeholder="请输入合同编号" />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item label="客户单位" name="custId" required>
                  <XhCustomerSelect v-model:value="formData.custId" placeholder="请选择客户单位" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="合同金额" name="amount">
                  <a-input-number
                    v-model:value="formData.amount"
                    placeholder="请输入合同金额"
                    :min="0"
                    :precision="2"
                    style="width: 100%"
                    :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                    :parser="value => value.replace(/¥\s?|(,*)/g, '')" />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item label="负责人" name="ownId" required>
                  <XhUserSelect v-model:value="formData.ownId" placeholder="请选择负责人" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="所属部门" name="deptId">
                  <XhDepartmentSelect v-model:value="formData.deptId" placeholder="请选择所属部门" allow-clear />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item label="合同状态" name="contractStatus">
                  <a-select v-model:value="formData.contractStatus" placeholder="请选择合同状态">
                    <a-select-option value="draft">草稿</a-select-option>
                    <a-select-option value="signed">已签约</a-select-option>
                    <a-select-option value="executing">执行中</a-select-option>
                    <a-select-option value="completed">已完成</a-select-option>
                    <a-select-option value="terminated">已终止</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="收款状态" name="moneyStatus">
                  <a-select v-model:value="formData.moneyStatus" placeholder="请选择收款状态">
                    <a-select-option value="unpaid">未收款</a-select-option>
                    <a-select-option value="partial">部分收款</a-select-option>
                    <a-select-option value="paid">已收款</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :span="24">
                <a-form-item label="备注" name="note" :label-col="{ span: 3 }" :wrapper-col="{ span: 20 }">
                  <a-textarea v-model:value="formData.note" placeholder="请输入备注信息" :rows="3" :maxlength="500" show-count />
                </a-form-item>
              </a-col>
            </a-row>
          </a-tab-pane>

          <!-- 时间管理 -->
          <a-tab-pane key="dates" tab="时间管理">
            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item label="签订日期" name="signDate">
                  <a-date-picker v-model:value="formData.signDate" placeholder="请选择签订日期" style="width: 100%" format="YYYY-MM-DD" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="招标日期" name="bidDate">
                  <a-date-picker v-model:value="formData.bidDate" placeholder="请选择招标日期" style="width: 100%" format="YYYY-MM-DD" />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item label="开工日期" name="commencementDate">
                  <a-date-picker v-model:value="formData.commencementDate" placeholder="请选择开工日期" style="width: 100%" format="YYYY-MM-DD" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="初检日期" name="initialCheckDate">
                  <a-date-picker v-model:value="formData.initialCheckDate" placeholder="请选择初检日期" style="width: 100%" format="YYYY-MM-DD" />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item label="终检日期" name="finalCheckDate">
                  <a-date-picker v-model:value="formData.finalCheckDate" placeholder="请选择终检日期" style="width: 100%" format="YYYY-MM-DD" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="审计日期" name="auditDate">
                  <a-date-picker v-model:value="formData.auditDate" placeholder="请选择审计日期" style="width: 100%" format="YYYY-MM-DD" />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item label="合同开始日期" name="cstartDate">
                  <a-date-picker v-model:value="formData.cstartDate" placeholder="请选择合同开始日期" style="width: 100%" format="YYYY-MM-DD" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="合同结束日期" name="cendDate">
                  <a-date-picker v-model:value="formData.cendDate" placeholder="请选择合同结束日期" style="width: 100%" format="YYYY-MM-DD" />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item label="监理开始日期" name="mstartDate">
                  <a-date-picker v-model:value="formData.mstartDate" placeholder="请选择监理开始日期" style="width: 100%" format="YYYY-MM-DD" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="监理结束日期" name="mendDate">
                  <a-date-picker v-model:value="formData.mendDate" placeholder="请选择监理结束日期" style="width: 100%" format="YYYY-MM-DD" />
                </a-form-item>
              </a-col>
            </a-row>
          </a-tab-pane>

          <!-- 联系信息 -->
          <a-tab-pane key="contact" tab="联系信息">
            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item label="联系人" name="linkman">
                  <a-input v-model:value="formData.linkman" placeholder="请输入联系人" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="联系电话" name="linkTelephone">
                  <a-input v-model:value="formData.linkTelephone" placeholder="请输入联系电话" />
                </a-form-item>
              </a-col>
            </a-row>

            <a-divider>合作单位信息</a-divider>

            <a-row :gutter="24">
              <a-col :span="8">
                <a-form-item label="勘察部门" name="svDeptId" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                  <XhDepartmentSelect v-model:value="formData.svDeptId" placeholder="请选择勘察部门" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="勘察联系人" name="svLinkman" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
                  <a-input v-model:value="formData.svLinkman" placeholder="请输入联系人" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="联系电话" name="svTelephone" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                  <a-input v-model:value="formData.svTelephone" placeholder="请输入电话" />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :span="8">
                <a-form-item label="审查部门" name="reviewDeptId" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                  <XhDepartmentSelect v-model:value="formData.reviewDeptId" placeholder="请选择审查部门" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="审查联系人" name="reviewLinkman" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
                  <a-input v-model:value="formData.reviewLinkman" placeholder="请输入联系人" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="联系电话" name="reviewTelephone" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                  <a-input v-model:value="formData.reviewTelephone" placeholder="请输入电话" />
                </a-form-item>
              </a-col>
            </a-row>
          </a-tab-pane>

          <!-- 成本预估 -->
          <a-tab-pane key="cost" tab="成本预估">
            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item label="预估毛利" name="estProbit">
                  <a-input-number
                    v-model:value="formData.estProbit"
                    placeholder="请输入预估毛利"
                    :precision="2"
                    style="width: 100%"
                    :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                    :parser="value => value.replace(/¥\s?|(,*)/g, '')" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="预估毛利率" name="estProbitRatio">
                  <a-input-number
                    v-model:value="formData.estProbitRatio"
                    placeholder="请输入预估毛利率"
                    :min="0"
                    :max="100"
                    :precision="2"
                    style="width: 100%"
                    addon-after="%" />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item label="预估外协金额" name="evaExternalAmount">
                  <a-input-number
                    v-model:value="formData.evaExternalAmount"
                    placeholder="请输入预估外协金额"
                    :min="0"
                    :precision="2"
                    style="width: 100%"
                    :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                    :parser="value => value.replace(/¥\s?|(,*)/g, '')" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="预估成本金额" name="evaCostAmount">
                  <a-input-number
                    v-model:value="formData.evaCostAmount"
                    placeholder="请输入预估成本金额"
                    :min="0"
                    :precision="2"
                    style="width: 100%"
                    :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                    :parser="value => value.replace(/¥\s?|(,*)/g, '')" />
                </a-form-item>
              </a-col>
            </a-row>

            <a-divider>部门分配金额</a-divider>

            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item label="一部分配金额" name="yfYbAmount">
                  <a-input-number
                    v-model:value="formData.yfYbAmount"
                    placeholder="请输入一部分配金额"
                    :min="0"
                    :precision="2"
                    style="width: 100%"
                    :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                    :parser="value => value.replace(/¥\s?|(,*)/g, '')" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="二部分配金额" name="yfEbAmount">
                  <a-input-number
                    v-model:value="formData.yfEbAmount"
                    placeholder="请输入二部分配金额"
                    :min="0"
                    :precision="2"
                    style="width: 100%"
                    :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                    :parser="value => value.replace(/¥\s?|(,*)/g, '')" />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item label="监理分配金额" name="yfJfAmount">
                  <a-input-number
                    v-model:value="formData.yfJfAmount"
                    placeholder="请输入监理分配金额"
                    :min="0"
                    :precision="2"
                    style="width: 100%"
                    :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                    :parser="value => value.replace(/¥\s?|(,*)/g, '')" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="其他分配金额" name="yfOtherAmount">
                  <a-input-number
                    v-model:value="formData.yfOtherAmount"
                    placeholder="请输入其他分配金额"
                    :min="0"
                    :precision="2"
                    style="width: 100%"
                    :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                    :parser="value => value.replace(/¥\s?|(,*)/g, '')" />
                </a-form-item>
              </a-col>
            </a-row>
          </a-tab-pane>
        </a-tabs>
      </a-form>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed, watch } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import type { FormInstance } from 'ant-design-vue';
  import contractApi, { type ContractForm } from '/@/api/project/contract';
  import dayjs, { type Dayjs } from 'dayjs';

  // Props & Emits
  const props = defineProps<{
    visible: boolean;
    formData: any;
    loading: boolean;
  }>();

  const emit = defineEmits<{
    'update:visible': [value: boolean];
    ok: [data: ContractForm];
    cancel: [];
  }>();

  const { createMessage } = useMessage();

  // Reactive state
  const formRef = ref<FormInstance>();
  const activeTab = ref('basic');

  const modalVisible = computed({
    get: () => props.visible,
    set: value => emit('update:visible', value),
  });

  const formTitle = computed(() => {
    return props.formData?.id ? '编辑合同' : '新增合同';
  });

  // Form data
  const formData: ContractForm = reactive({
    id: '',
    name: '',
    cno: '',
    custId: '',
    finalUserId: '',
    ownId: '',
    deptId: '',
    amount: undefined,
    reportFrequency: '',
    contractStatus: 'draft',
    moneyStatus: 'unpaid',
    signDate: '',
    bidDate: '',
    commencementDate: '',
    initialCheckDate: '',
    finalCheckDate: '',
    auditDate: '',
    note: '',
    linkman: '',
    linkTelephone: '',
    cstartDate: '',
    cendDate: '',
    mstartDate: '',
    mendDate: '',
    svDeptId: '',
    svLinkman: '',
    svTelephone: '',
    reviewDeptId: '',
    reviewLinkman: '',
    reviewTelephone: '',
    dbDeptId: '',
    dbLinkman: '',
    dbTelephone: '',
    smDeptId: '',
    smLinkman: '',
    smTelephone: '',
    jsDeptId: '',
    jsLinkman: '',
    jsTelephone: '',
    estProbit: undefined,
    estProbitRatio: undefined,
    evaExternalAmount: undefined,
    evaCostAmount: undefined,
    yfYbAmount: undefined,
    yfEbAmount: undefined,
    yfJfAmount: undefined,
    yfOtherAmount: undefined,
  });

  // Form validation rules
  const formRules = {
    name: [
      { required: true, message: '请输入合同名称', trigger: 'blur' },
      { max: 100, message: '合同名称不能超过100个字符', trigger: 'blur' },
    ],
    cno: [
      { required: true, message: '请输入合同编号', trigger: 'blur' },
      { max: 50, message: '合同编号不能超过50个字符', trigger: 'blur' },
      { validator: validateCno, trigger: 'blur' },
    ],
    custId: [{ required: true, message: '请选择客户单位', trigger: 'change' }],
    ownId: [{ required: true, message: '请选择负责人', trigger: 'change' }],
    amount: [{ type: 'number', min: 0, message: '合同金额不能小于0', trigger: 'blur' }],
    linkTelephone: [{ pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }],
  };

  // Custom validator for contract number uniqueness
  async function validateCno(rule: any, value: string) {
    if (!value) return;

    try {
      const response = await contractApi.checkCnoExists(value, formData.id);
      if (response.code === 200 && response.data) {
        throw new Error('合同编号已存在');
      }
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('验证合同编号失败');
    }
  }

  // Watch for props changes
  watch(
    () => props.formData,
    newData => {
      if (newData) {
        // Copy data to form
        Object.assign(formData, newData);

        // Convert date strings to dayjs objects for date pickers
        const dateFields = [
          'signDate',
          'bidDate',
          'commencementDate',
          'initialCheckDate',
          'finalCheckDate',
          'auditDate',
          'cstartDate',
          'cendDate',
          'mstartDate',
          'mendDate',
        ];

        dateFields.forEach(field => {
          if (newData[field]) {
            formData[field] = dayjs(newData[field]);
          }
        });
      }
    },
    { immediate: true, deep: true },
  );

  // Methods
  const handleOk = async () => {
    try {
      await formRef.value?.validate();

      // Convert dayjs objects back to date strings
      const submitData = { ...formData };
      const dateFields = [
        'signDate',
        'bidDate',
        'commencementDate',
        'initialCheckDate',
        'finalCheckDate',
        'auditDate',
        'cstartDate',
        'cendDate',
        'mstartDate',
        'mendDate',
      ];

      dateFields.forEach(field => {
        if (submitData[field] && dayjs.isDayjs(submitData[field])) {
          submitData[field] = (submitData[field] as Dayjs).format('YYYY-MM-DD');
        }
      });

      emit('ok', submitData);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const handleCancel = () => {
    formRef.value?.resetFields();
    activeTab.value = 'basic';
    emit('cancel');
  };
</script>

<style lang="less" scoped>
  .contract-form-modal {
    .ant-tabs-content-holder {
      max-height: 500px;
      overflow-y: auto;
    }

    .ant-form-item {
      margin-bottom: 16px;
    }

    .ant-divider {
      margin: 16px 0;
      font-size: 14px;
      font-weight: 500;
      color: #666;
    }

    // 数字输入框样式
    .ant-input-number {
      &.ant-input-number-focused {
        border-color: #40a9ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }
    }

    // 日期选择器样式
    .ant-picker {
      &.ant-picker-focused {
        border-color: #40a9ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }
    }

    // 选择器样式
    .ant-select {
      &.ant-select-focused {
        .ant-select-selector {
          border-color: #40a9ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
      }
    }
  }
</style>
