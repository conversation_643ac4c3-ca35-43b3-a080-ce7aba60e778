package com.xinghuo.project.biz.model.paymentContract;

import com.xinghuo.common.base.model.Pagination;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 付款合同分页查询参数
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "付款合同分页查询参数")
public class PaymentContractPagination extends Pagination {

    /**
     * 采购合同名称
     */
    @Schema(description = "采购合同名称")
    private String name;

    /**
     * 采购合同编号
     */
    @Schema(description = "采购合同编号")
    private String cno;

    /**
     * 收款合同ID
     */
    @Schema(description = "收款合同ID")
    private String contractId;

    /**
     * 供应商ID
     */
    @Schema(description = "供应商ID")
    private String suppilerId;

    /**
     * 采购负责人ID
     */
    @Schema(description = "采购负责人ID")
    private String ownId;

    /**
     * 采购合同状态
     */
    @Schema(description = "采购合同状态")
    private String status;

    /**
     * 付款状态
     */
    @Schema(description = "付款状态")
    private String moneyStatus;

    /**
     * 签订年份
     */
    @Schema(description = "签订年份")
    private Integer signYear;

    /**
     * 最小金额
     */
    @Schema(description = "最小金额")
    private BigDecimal minAmount;

    /**
     * 最大金额
     */
    @Schema(description = "最大金额")
    private BigDecimal maxAmount;

    /**
     * 签订日期开始
     */
    @Schema(description = "签订日期开始")
    private Date signDateStart;

    /**
     * 签订日期结束
     */
    @Schema(description = "签订日期结束")
    private Date signDateEnd;
}
