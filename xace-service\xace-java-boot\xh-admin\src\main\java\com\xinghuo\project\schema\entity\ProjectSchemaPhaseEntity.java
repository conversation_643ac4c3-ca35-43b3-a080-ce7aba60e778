package com.xinghuo.project.schema.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目模板的阶段配置表实体类
 * 对应数据库表：zz_proj_schema_phase
 *
 * 此表存储项目模板中的阶段配置信息，每条记录关联一个标准阶段库中的阶段，
 * 但可以在项目模板中覆盖标准阶段的某些属性（如工期、权重等），
 * 实现项目模板对标准阶段的个性化配置。
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("zz_proj_schema_phase")
public class ProjectSchemaPhaseEntity extends BaseEntityV2.CUBaseEntityV2<String> {

    /**
     * 所属项目模板ID (关联 zz_proj_template)
     */
    @TableField("project_template_id")
    private String projectTemplateId;

    /**
     * 关联的标准阶段ID (关联 zz_proj_tpl_phase)
     */
    @TableField("phase_library_id")
    private String phaseLibraryId;

    /**
     * 该阶段在此模板中的顺序
     */
    @TableField("seq_no")
    private Integer seqNo;

    /**
     * 此模板中该阶段的计划工期(天) (可覆盖库中的std_duration)
     */
    @TableField("duration")
    private Integer duration;

    /**
     * 此模板中该阶段占项目总体的权重(%)
     */
    @TableField("completion_weight")
    private Integer completionWeight;

    /**
     * 是否可裁剪 (1:是, 0:否)
     */
    @TableField("can_cut")
    private Integer canCut;

    /**
     * 此模板中该阶段的审批流程ID (可覆盖库中的default_approval_id)
     */
    @TableField("approval_id")
    private String approvalId;

    /**
     * 此模板中该阶段的质量检查模板ID
     */
    @TableField("checklist_id")
    private String checklistId;

    /**
     * 此模板中该阶段关联的交付物清单模板ID
     */
    @TableField("workproduct_tpl_id")
    private String workproductTplId;
}
