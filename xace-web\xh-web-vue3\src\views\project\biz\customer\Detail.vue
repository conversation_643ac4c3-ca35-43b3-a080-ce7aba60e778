<template>
  <div class="xh-content-wrapper">
    <div class="xh-content-wrapper-center">
      <div class="xh-content-wrapper-content">
        <PageWrapper :title="customerInfo?.name || '客户单位详情'" contentBackground contentClass="p-4">
          <template #extra>
            <a-button type="primary" @click="handleEdit">编辑</a-button>
            <a-button @click="goBack">返回</a-button>
          </template>
          <Description title="基本信息" :bordered="true" :data="customerData" :column="2" size="middle" />
          <Divider />
          <div class="mt-4">
            <div class="flex justify-between items-center mb-4">
              <h3 class="text-lg font-medium">联系人列表</h3>
              <a-button type="primary" @click="handleCreateLinkman">新增联系人</a-button>
            </div>
            <a-table :columns="linkmanColumns" :dataSource="linkmanList" :pagination="false" :loading="linkmanLoading" rowKey="id">
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'status'">
                  <a-tag :color="record.status === 1 ? 'green' : 'red'">
                    {{ record.status === 1 ? '有效' : '无效' }}
                  </a-tag>
                </template>
                <template v-if="column.key === 'action'">
                  <TableAction
                    :actions="[
                      {
                        icon: 'clarity:note-edit-line',
                        tooltip: '编辑',
                        onClick: handleEditLinkman.bind(null, record),
                      },
                      {
                        icon: record.status === 1 ? 'ant-design:stop-outlined' : 'ant-design:check-outlined',
                        color: record.status === 1 ? 'warning' : 'success',
                        tooltip: record.status === 1 ? '设为无效' : '设为有效',
                        onClick: handleToggleLinkmanStatus.bind(null, record),
                      },
                      {
                        icon: 'ant-design:delete-outlined',
                        color: 'error',
                        tooltip: '删除',
                        popConfirm: {
                          title: '是否确认删除',
                          confirm: handleDeleteLinkman.bind(null, record),
                        },
                      },
                    ]" />
                </template>
              </template>
            </a-table>
          </div>
        </PageWrapper>
      </div>
    </div>
    <LinkmanForm @register="registerLinkmanForm" @reload="loadLinkmanList" />
    <CustomerForm @register="registerCustomerForm" @reload="loadCustomerInfo" />
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted, computed } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { PageWrapper } from '/@/components/Page';
  import { Description } from '/@/components/Description';
  import { Divider } from 'ant-design-vue';
  import { TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { getCustomerInfo, CustomerModel } from '/@/api/project/customer';
  import { getCustomerLinkmanList, deleteCustomerLinkman, updateCustomerLinkmanStatus, CustomerLinkmanModel } from '/@/api/project/customerLinkman';
  import LinkmanForm from './LinkmanForm.vue';
  import CustomerForm from './Form.vue';

  const route = useRoute();
  const router = useRouter();
  const { createMessage } = useMessage();
  const [registerLinkmanForm, { openModal: openLinkmanModal }] = useModal();
  const [registerCustomerForm, { openModal: openCustomerModal }] = useModal();

  const customerId = computed(() => route.params.id as string);
  const customerInfo = ref<CustomerModel | null>(null);
  const linkmanList = ref<CustomerLinkmanModel[]>([]);
  const linkmanLoading = ref(false);

  // 客户类型映射
  const custTypeMap = {
    government: '政府单位',
    enterprise: '企业单位',
    institution: '事业单位',
    other: '其他',
  };

  // 联系人表格列定义
  const linkmanColumns = [
    {
      title: '联系人',
      dataIndex: 'linkman',
      width: 120,
    },
    {
      title: '联系电话',
      dataIndex: 'telephone',
      width: 150,
    },
    {
      title: '状态',
      key: 'status',
      dataIndex: 'status',
      width: 80,
    },
    {
      title: '主题',
      dataIndex: 'topic',
      width: 150,
    },
    {
      title: '内容',
      dataIndex: 'content',
      width: 200,
    },
    {
      title: '备注',
      dataIndex: 'note',
      width: 200,
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      fixed: 'right' as const,
    },
  ];

  // 客户信息展示数据
  const customerData = computed(() => {
    if (!customerInfo.value) return [];

    return [
      {
        field: '客户名称',
        label: '客户名称',
        value: customerInfo.value.name,
      },
      {
        field: '客户类型',
        label: '客户类型',
        value: custTypeMap[customerInfo.value.custType] || customerInfo.value.custType,
      },
      {
        field: '类型',
        label: '类型',
        value: customerInfo.value.type || '-',
      },
      {
        field: '负责人',
        label: '负责人',
        value: customerInfo.value.leader || '-',
      },
      {
        field: '备注',
        label: '备注',
        value: customerInfo.value.remark || '-',
      },
      {
        field: '创建时间',
        label: '创建时间',
        value: customerInfo.value.creatorTime || '-',
      },
    ];
  });

  // 加载客户信息
  async function loadCustomerInfo() {
    try {
      const data = await getCustomerInfo(customerId.value);
      customerInfo.value = data;
    } catch (error) {
      console.error('获取客户信息失败:', error);
      createMessage.error('获取客户信息失败');
    }
  }

  // 加载联系人列表
  async function loadLinkmanList() {
    try {
      linkmanLoading.value = true;
      const data = await getCustomerLinkmanList(customerId.value);
      linkmanList.value = data;
    } catch (error) {
      console.error('获取联系人列表失败:', error);
      createMessage.error('获取联系人列表失败');
    } finally {
      linkmanLoading.value = false;
    }
  }

  // 返回上一页
  function goBack() {
    router.back();
  }

  // 编辑客户信息
  function handleEdit() {
    openCustomerModal(true, {
      record: customerInfo.value,
      isUpdate: true,
    });
  }

  // 新增联系人
  function handleCreateLinkman() {
    openLinkmanModal(true, {
      customerId: customerId.value,
      isUpdate: false,
    });
  }

  // 编辑联系人
  function handleEditLinkman(record: CustomerLinkmanModel) {
    openLinkmanModal(true, {
      customerId: customerId.value,
      record,
      isUpdate: true,
    });
  }

  // 切换联系人状态
  async function handleToggleLinkmanStatus(record: CustomerLinkmanModel) {
    try {
      const newStatus = record.status === 1 ? 0 : 1;
      await updateCustomerLinkmanStatus(record.id, newStatus);
      createMessage.success(newStatus === 1 ? '已设为有效' : '已设为无效');
      loadLinkmanList();
    } catch (error) {
      console.error('更新联系人状态失败:', error);
      createMessage.error('更新联系人状态失败');
    }
  }

  // 删除联系人
  async function handleDeleteLinkman(record: CustomerLinkmanModel) {
    try {
      await deleteCustomerLinkman(record.id);
      createMessage.success('删除成功');
      loadLinkmanList();
    } catch (error) {
      console.error('删除联系人失败:', error);
      createMessage.error('删除联系人失败');
    }
  }

  onMounted(() => {
    loadCustomerInfo();
    loadLinkmanList();
  });
</script>
