<template>
  <div class="performance-alert-system">
    <!-- 预警概览卡片 -->
    <a-row :gutter="16" class="alert-overview">
      <a-col :span="6">
        <a-card size="small" class="alert-card error">
          <a-statistic title="严重预警" :value="alertStats.error" :value-style="{ color: '#ff4d4f' }">
            <template #prefix>
              <Icon icon="ant-design:exclamation-circle-filled" />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card size="small" class="alert-card warning">
          <a-statistic title="一般预警" :value="alertStats.warning" :value-style="{ color: '#faad14' }">
            <template #prefix>
              <Icon icon="ant-design:warning-filled" />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card size="small" class="alert-card info">
          <a-statistic title="提示信息" :value="alertStats.info" :value-style="{ color: '#1890ff' }">
            <template #prefix>
              <Icon icon="ant-design:info-circle-filled" />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card size="small" class="alert-card success">
          <a-statistic title="正常状态" :value="alertStats.success" :value-style="{ color: '#52c41a' }">
            <template #prefix>
              <Icon icon="ant-design:check-circle-filled" />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
    </a-row>

    <!-- 预警控制面板 -->
    <a-card size="small" class="alert-control-panel">
      <template #title>
        <div class="panel-title">
          <Icon icon="ant-design:control-outlined" class="title-icon" />
          <span>预警控制台</span>
        </div>
      </template>

      <template #extra>
        <a-space>
          <a-button @click="handleRefreshAlerts" :loading="loading" size="small">
            <template #icon><Icon icon="ant-design:reload-outlined" /></template>
            刷新
          </a-button>
          <a-button @click="handleAlertSettings" size="small">
            <template #icon><Icon icon="ant-design:setting-outlined" /></template>
            设置
          </a-button>
        </a-space>
      </template>

      <div class="control-filters">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-select v-model:value="filterParams.level" placeholder="预警级别" allow-clear size="small" @change="handleFilterChange">
              <a-select-option value="error">严重预警</a-select-option>
              <a-select-option value="warning">一般预警</a-select-option>
              <a-select-option value="info">提示信息</a-select-option>
            </a-select>
          </a-col>
          <a-col :span="6">
            <a-select v-model:value="filterParams.type" placeholder="预警类型" allow-clear size="small" @change="handleFilterChange">
              <a-select-option value="score_anomaly">分数异常</a-select-option>
              <a-select-option value="distribution_uneven">分配不均</a-select-option>
              <a-select-option value="deadline_approaching">截止临近</a-select-option>
              <a-select-option value="approval_delay">审批延迟</a-select-option>
            </a-select>
          </a-col>
          <a-col :span="6">
            <a-select v-model:value="filterParams.department" placeholder="部门筛选" allow-clear size="small" @change="handleFilterChange">
              <a-select-option v-for="dept in departments" :key="dept.id" :value="dept.id">
                {{ dept.name }}
              </a-select-option>
            </a-select>
          </a-col>
          <a-col :span="6">
            <a-input-search v-model:value="filterParams.keyword" placeholder="搜索预警内容" size="small" @search="handleFilterChange" />
          </a-col>
        </a-row>
      </div>
    </a-card>

    <!-- 预警列表 -->
    <a-card size="small" class="alert-list-card">
      <template #title>
        <div class="list-title">
          <Icon icon="ant-design:bell-outlined" class="title-icon" />
          <span>预警列表</span>
          <a-badge :count="filteredAlerts.length" :offset="[10, 0]" />
        </div>
      </template>

      <template #extra>
        <a-space>
          <a-button @click="handleMarkAllRead" size="small" :disabled="!hasUnreadAlerts">
            <template #icon><Icon icon="ant-design:check-outlined" /></template>
            全部已读
          </a-button>
          <a-button @click="handleClearAll" size="small" :disabled="filteredAlerts.length === 0">
            <template #icon><Icon icon="ant-design:clear-outlined" /></template>
            清空
          </a-button>
        </a-space>
      </template>

      <div class="alert-list">
        <div
          v-for="alert in paginatedAlerts"
          :key="alert.id"
          class="alert-item"
          :class="[alert.level, { unread: !alert.isRead }]"
          @click="handleAlertClick(alert)">
          <div class="alert-icon">
            <Icon :icon="getAlertIcon(alert.level)" />
          </div>

          <div class="alert-content">
            <div class="alert-header">
              <span class="alert-title">{{ alert.title }}</span>
              <div class="alert-meta">
                <a-tag :color="getAlertColor(alert.level)" size="small">
                  {{ getAlertLevelText(alert.level) }}
                </a-tag>
                <a-tag color="blue" size="small">
                  {{ getAlertTypeText(alert.type) }}
                </a-tag>
                <Time class="alert-time" :value="alert.createTime" mode="relative" />
              </div>
            </div>

            <div class="alert-message">{{ alert.message }}</div>

            <div class="alert-details" v-if="alert.details">
              <div class="detail-item" v-for="(value, key) in alert.details" :key="key">
                <span class="detail-label">{{ key }}:</span>
                <span class="detail-value">{{ value }}</span>
              </div>
            </div>

            <div class="alert-actions">
              <a-space size="small">
                <a-button v-if="!alert.isRead" size="small" @click.stop="handleMarkRead(alert)"> 标记已读 </a-button>
                <a-button size="small" @click.stop="handleViewDetail(alert)"> 查看详情 </a-button>
                <a-button v-if="alert.actionable" type="primary" size="small" @click.stop="handleTakeAction(alert)">
                  {{ alert.actionText || '处理' }}
                </a-button>
                <a-button size="small" danger @click.stop="handleDismissAlert(alert)"> 忽略 </a-button>
              </a-space>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="filteredAlerts.length === 0" class="alert-empty">
          <a-empty description="暂无预警信息">
            <template #image>
              <Icon icon="ant-design:bell-outlined" style="font-size: 48px; color: #d9d9d9" />
            </template>
          </a-empty>
        </div>
      </div>

      <!-- 分页 -->
      <div class="alert-pagination" v-if="filteredAlerts.length > pageSize">
        <a-pagination
          v-model:current="currentPage"
          :total="filteredAlerts.length"
          :page-size="pageSize"
          :show-size-changer="false"
          :show-quick-jumper="true"
          size="small"
          @change="handlePageChange" />
      </div>
    </a-card>

    <!-- 预警详情模态框 -->
    <a-modal v-model:open="detailModalVisible" :title="selectedAlert?.title" width="600px" :footer="null">
      <div class="alert-detail-content" v-if="selectedAlert">
        <div class="detail-header">
          <a-tag :color="getAlertColor(selectedAlert.level)">
            {{ getAlertLevelText(selectedAlert.level) }}
          </a-tag>
          <a-tag color="blue">
            {{ getAlertTypeText(selectedAlert.type) }}
          </a-tag>
          <Time class="detail-time" :value="selectedAlert.createTime" mode="relative" />
        </div>

        <div class="detail-message">
          <h4>预警信息</h4>
          <p>{{ selectedAlert.message }}</p>
        </div>

        <div class="detail-info" v-if="selectedAlert.details">
          <h4>详细信息</h4>
          <a-descriptions :column="1" size="small" bordered>
            <a-descriptions-item v-for="(value, key) in selectedAlert.details" :key="key" :label="key">
              {{ value }}
            </a-descriptions-item>
          </a-descriptions>
        </div>

        <div class="detail-suggestion" v-if="selectedAlert.suggestion">
          <h4>处理建议</h4>
          <div class="suggestion-content">
            <Icon icon="ant-design:bulb-outlined" class="suggestion-icon" />
            <span>{{ selectedAlert.suggestion }}</span>
          </div>
        </div>

        <div class="detail-actions">
          <a-space>
            <a-button v-if="selectedAlert.actionable" type="primary" @click="handleTakeAction(selectedAlert)">
              {{ selectedAlert.actionText || '处理' }}
            </a-button>
            <a-button @click="handleMarkRead(selectedAlert)"> 标记已读 </a-button>
            <a-button @click="detailModalVisible = false"> 关闭 </a-button>
          </a-space>
        </div>
      </div>
    </a-modal>

    <!-- 预警设置模态框 -->
    <a-modal v-model:open="settingsModalVisible" title="预警设置" width="800px" @ok="handleSaveSettings" @cancel="handleCancelSettings">
      <div class="alert-settings">
        <a-tabs v-model:activeKey="settingsTab">
          <a-tab-pane key="rules" tab="预警规则">
            <div class="settings-rules">
              <h4>分数异常预警</h4>
              <a-form layout="inline">
                <a-form-item label="高分阈值">
                  <a-input-number v-model:value="alertSettings.scoreHigh" :min="80" :max="100" />
                </a-form-item>
                <a-form-item label="低分阈值">
                  <a-input-number v-model:value="alertSettings.scoreLow" :min="0" :max="80" />
                </a-form-item>
              </a-form>

              <h4>截止时间预警</h4>
              <a-form layout="inline">
                <a-form-item label="提前天数">
                  <a-input-number v-model:value="alertSettings.deadlineDays" :min="1" :max="30" />
                </a-form-item>
              </a-form>
            </div>
          </a-tab-pane>

          <a-tab-pane key="notifications" tab="通知设置">
            <div class="settings-notifications">
              <a-checkbox-group v-model:value="alertSettings.notificationMethods">
                <a-checkbox value="system">系统通知</a-checkbox>
                <a-checkbox value="email">邮件通知</a-checkbox>
                <a-checkbox value="sms">短信通知</a-checkbox>
              </a-checkbox-group>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed, onMounted } from 'vue';
  import { Icon } from '/@/components/Icon';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { Time } from '/@/components/Time';

  interface PerformanceAlert {
    id: string;
    type: 'score_anomaly' | 'distribution_uneven' | 'deadline_approaching' | 'approval_delay';
    level: 'error' | 'warning' | 'info' | 'success';
    title: string;
    message: string;
    details?: Record<string, any>;
    suggestion?: string;
    userId?: string;
    departmentId?: string;
    createTime: Date;
    isRead: boolean;
    actionable: boolean;
    actionText?: string;
  }

  interface AlertStats {
    error: number;
    warning: number;
    info: number;
    success: number;
  }

  interface Department {
    id: string;
    name: string;
  }

  const { createMessage } = useMessage();

  // 响应式数据
  const loading = ref(false);
  const currentPage = ref(1);
  const pageSize = ref(10);
  const detailModalVisible = ref(false);
  const settingsModalVisible = ref(false);
  const settingsTab = ref('rules');
  const selectedAlert = ref<PerformanceAlert | null>(null);

  const filterParams = reactive({
    level: undefined,
    type: undefined,
    department: undefined,
    keyword: '',
  });

  const alertSettings = reactive({
    scoreHigh: 95,
    scoreLow: 60,
    deadlineDays: 3,
    notificationMethods: ['system', 'email'],
  });

  // 模拟数据
  const departments = ref<Department[]>([
    { id: '1', name: '技术部' },
    { id: '2', name: '产品部' },
    { id: '3', name: '运营部' },
    { id: '4', name: '市场部' },
  ]);

  const alerts = ref<PerformanceAlert[]>([
    {
      id: '1',
      type: 'score_anomaly',
      level: 'error',
      title: '异常高分预警',
      message: '张三的绩效分数(98分)异常偏高，请确认评分是否准确',
      details: {
        员工姓名: '张三',
        绩效分数: '98分',
        部门: '技术部',
        评分人: '李经理',
      },
      suggestion: '建议核实该员工的工作表现和具体贡献，确保评分客观公正',
      userId: 'user1',
      departmentId: '1',
      createTime: new Date(Date.now() - 2 * 60 * 60 * 1000),
      isRead: false,
      actionable: true,
      actionText: '核实评分',
    },
    {
      id: '2',
      type: 'deadline_approaching',
      level: 'warning',
      title: '截止时间临近',
      message: '绩效评分将在2天后截止，还有15名员工未完成评分',
      details: {
        截止时间: '2024-06-30 18:00',
        未完成人数: '15人',
        完成率: '75%',
      },
      suggestion: '建议及时提醒相关管理者完成绩效评分工作',
      createTime: new Date(Date.now() - 4 * 60 * 60 * 1000),
      isRead: false,
      actionable: true,
      actionText: '发送提醒',
    },
  ]);

  // 计算属性
  const alertStats = computed<AlertStats>(() => {
    const stats = { error: 0, warning: 0, info: 0, success: 0 };
    alerts.value.forEach(alert => {
      stats[alert.level]++;
    });
    return stats;
  });

  const filteredAlerts = computed(() => {
    return alerts.value.filter(alert => {
      if (filterParams.level && alert.level !== filterParams.level) return false;
      if (filterParams.type && alert.type !== filterParams.type) return false;
      if (filterParams.department && alert.departmentId !== filterParams.department) return false;
      if (filterParams.keyword && !alert.message.includes(filterParams.keyword)) return false;
      return true;
    });
  });

  const paginatedAlerts = computed(() => {
    const start = (currentPage.value - 1) * pageSize.value;
    const end = start + pageSize.value;
    return filteredAlerts.value.slice(start, end);
  });

  const hasUnreadAlerts = computed(() => {
    return filteredAlerts.value.some(alert => !alert.isRead);
  });

  // 生命周期
  onMounted(() => {
    loadAlerts();
  });

  // 方法
  const loadAlerts = async () => {
    loading.value = true;
    try {
      // 模拟加载数据
      await new Promise(resolve => setTimeout(resolve, 1000));
      // 这里可以调用API加载真实数据
    } catch (error) {
      createMessage.error('加载预警数据失败');
    } finally {
      loading.value = false;
    }
  };

  const handleRefreshAlerts = () => {
    loadAlerts();
  };

  const handleFilterChange = () => {
    currentPage.value = 1;
  };

  const handlePageChange = (page: number) => {
    currentPage.value = page;
  };

  const handleAlertClick = (alert: PerformanceAlert) => {
    selectedAlert.value = alert;
    detailModalVisible.value = true;

    if (!alert.isRead) {
      handleMarkRead(alert);
    }
  };

  const handleMarkRead = (alert: PerformanceAlert) => {
    alert.isRead = true;
    createMessage.success('已标记为已读');
  };

  const handleMarkAllRead = () => {
    filteredAlerts.value.forEach(alert => {
      alert.isRead = true;
    });
    createMessage.success('已全部标记为已读');
  };

  const handleClearAll = () => {
    alerts.value = alerts.value.filter(alert => !filteredAlerts.value.includes(alert));
    createMessage.success('已清空预警列表');
  };

  const handleViewDetail = (alert: PerformanceAlert) => {
    selectedAlert.value = alert;
    detailModalVisible.value = true;
  };

  const handleTakeAction = (alert: PerformanceAlert) => {
    // 根据预警类型执行相应操作
    switch (alert.type) {
      case 'score_anomaly':
        createMessage.info('正在核实评分...');
        break;
      case 'deadline_approaching':
        createMessage.info('正在发送提醒...');
        break;
      default:
        createMessage.info('正在处理预警...');
    }
  };

  const handleDismissAlert = (alert: PerformanceAlert) => {
    const index = alerts.value.findIndex(a => a.id === alert.id);
    if (index > -1) {
      alerts.value.splice(index, 1);
      createMessage.success('已忽略该预警');
    }
  };

  const handleAlertSettings = () => {
    settingsModalVisible.value = true;
  };

  const handleSaveSettings = () => {
    createMessage.success('预警设置已保存');
    settingsModalVisible.value = false;
  };

  const handleCancelSettings = () => {
    settingsModalVisible.value = false;
  };

  // 工具方法
  const getAlertIcon = (level: string) => {
    const iconMap = {
      error: 'ant-design:exclamation-circle-filled',
      warning: 'ant-design:warning-filled',
      info: 'ant-design:info-circle-filled',
      success: 'ant-design:check-circle-filled',
    };
    return iconMap[level] || 'ant-design:info-circle-filled';
  };

  const getAlertColor = (level: string) => {
    const colorMap = {
      error: 'red',
      warning: 'orange',
      info: 'blue',
      success: 'green',
    };
    return colorMap[level] || 'blue';
  };

  const getAlertLevelText = (level: string) => {
    const textMap = {
      error: '严重',
      warning: '警告',
      info: '信息',
      success: '正常',
    };
    return textMap[level] || '未知';
  };

  const getAlertTypeText = (type: string) => {
    const textMap = {
      score_anomaly: '分数异常',
      distribution_uneven: '分配不均',
      deadline_approaching: '截止临近',
      approval_delay: '审批延迟',
    };
    return textMap[type] || '未知类型';
  };
</script>

<style lang="less" scoped>
  // 响应式设计
  @media (max-width: 1200px) {
    .performance-alert-system {
      .alert-overview {
        .ant-col {
          margin-bottom: 16px;
        }
      }
    }
  }

  @media (max-width: 768px) {
    .performance-alert-system {
      .alert-control-panel {
        .control-filters {
          .ant-col {
            margin-bottom: 8px;
          }
        }
      }

      .alert-list-card {
        .alert-list {
          .alert-item {
            flex-direction: column;
            gap: 8px;

            .alert-content {
              .alert-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;

                .alert-meta {
                  flex-wrap: wrap;
                }
              }

              .alert-details {
                flex-direction: column;
                gap: 4px;
              }

              .alert-actions {
                justify-content: flex-start;

                :deep(.ant-space) {
                  flex-wrap: wrap;
                }
              }
            }
          }
        }
      }
    }
  }
  .performance-alert-system {
    .alert-overview {
      margin-bottom: 16px;

      .alert-card {
        &.error {
          border-left: 4px solid #ff4d4f;
        }

        &.warning {
          border-left: 4px solid #faad14;
        }

        &.info {
          border-left: 4px solid #1890ff;
        }

        &.success {
          border-left: 4px solid #52c41a;
        }
      }
    }

    .alert-control-panel {
      margin-bottom: 16px;

      .panel-title {
        display: flex;
        align-items: center;
        gap: 8px;

        .title-icon {
          color: #1890ff;
          font-size: 16px;
        }
      }

      .control-filters {
        margin-top: 16px;
      }
    }

    .alert-list-card {
      .list-title {
        display: flex;
        align-items: center;
        gap: 8px;

        .title-icon {
          color: #1890ff;
          font-size: 16px;
        }
      }

      .alert-list {
        .alert-item {
          display: flex;
          gap: 12px;
          padding: 16px;
          margin-bottom: 12px;
          border: 1px solid #f0f0f0;
          border-radius: 6px;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            border-color: #d9d9d9;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }

          &.unread {
            background: #f6ffed;
            border-color: #b7eb8f;
          }

          &.error {
            border-left: 4px solid #ff4d4f;

            .alert-icon {
              color: #ff4d4f;
            }
          }

          &.warning {
            border-left: 4px solid #faad14;

            .alert-icon {
              color: #faad14;
            }
          }

          &.info {
            border-left: 4px solid #1890ff;

            .alert-icon {
              color: #1890ff;
            }
          }

          .alert-icon {
            font-size: 20px;
            margin-top: 2px;
          }

          .alert-content {
            flex: 1;

            .alert-header {
              display: flex;
              justify-content: space-between;
              align-items: flex-start;
              margin-bottom: 8px;

              .alert-title {
                font-weight: 600;
                color: #333;
                font-size: 14px;
              }

              .alert-meta {
                display: flex;
                align-items: center;
                gap: 8px;

                .alert-time {
                  font-size: 12px;
                  color: #999;
                }
              }
            }

            .alert-message {
              color: #666;
              line-height: 1.5;
              margin-bottom: 12px;
              font-size: 13px;
            }

            .alert-details {
              display: flex;
              flex-wrap: wrap;
              gap: 12px;
              margin-bottom: 12px;

              .detail-item {
                font-size: 12px;

                .detail-label {
                  color: #999;
                  margin-right: 4px;
                }

                .detail-value {
                  color: #333;
                  font-weight: 500;
                }
              }
            }

            .alert-actions {
              display: flex;
              justify-content: flex-end;
            }
          }
        }

        .alert-empty {
          text-align: center;
          padding: 40px 0;
        }
      }

      .alert-pagination {
        margin-top: 16px;
        text-align: center;
      }
    }

    .alert-detail-content {
      .detail-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 16px;

        .detail-time {
          margin-left: auto;
          font-size: 12px;
          color: #999;
        }
      }

      .detail-message,
      .detail-info,
      .detail-suggestion {
        margin-bottom: 20px;

        h4 {
          margin-bottom: 8px;
          font-size: 14px;
          color: #333;
        }

        p {
          color: #666;
          line-height: 1.6;
          margin: 0;
        }
      }

      .detail-suggestion {
        .suggestion-content {
          display: flex;
          align-items: flex-start;
          gap: 8px;
          padding: 12px;
          background: #fffbe6;
          border: 1px solid #ffe58f;
          border-radius: 6px;

          .suggestion-icon {
            color: #faad14;
            font-size: 16px;
            margin-top: 2px;
          }
        }
      }

      .detail-actions {
        text-align: right;
        padding-top: 16px;
        border-top: 1px solid #f0f0f0;
      }
    }

    .alert-settings {
      .settings-rules {
        h4 {
          margin: 20px 0 12px;
          font-size: 14px;
          color: #333;

          &:first-child {
            margin-top: 0;
          }
        }
      }

      .settings-notifications {
        :deep(.ant-checkbox-group) {
          display: flex;
          flex-direction: column;
          gap: 12px;
        }
      }
    }
  }
</style>
