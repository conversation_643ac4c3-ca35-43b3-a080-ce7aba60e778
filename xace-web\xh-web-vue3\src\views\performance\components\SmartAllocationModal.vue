<template>
  <a-modal v-model:open="visible" title="智能分配" width="800px" :confirm-loading="loading" @ok="handleConfirm" @cancel="handleCancel">
    <template #title>
      <div class="modal-title">
        <Icon icon="ant-design:thunderbolt-outlined" class="title-icon" />
        <span>智能分配</span>
      </div>
    </template>

    <div class="smart-allocation-content">
      <!-- 分配策略选择 -->
      <a-card size="small" title="分配策略" class="strategy-card">
        <a-radio-group v-model:value="allocationStrategy" class="strategy-options">
          <a-radio-button value="history">历史绩效</a-radio-button>
          <a-radio-button value="workhour">工时表现</a-radio-button>
          <a-radio-button value="attendance">考勤情况</a-radio-button>
          <a-radio-button value="comprehensive">综合评估</a-radio-button>
        </a-radio-group>

        <div class="strategy-description">
          <p>{{ strategyDescriptions[allocationStrategy] }}</p>
        </div>
      </a-card>

      <!-- 权重配置 -->
      <a-card size="small" title="权重配置" class="weight-card" v-if="allocationStrategy === 'comprehensive'">
        <div class="weight-sliders">
          <div class="weight-item">
            <div class="weight-header">
              <span class="weight-label">历史绩效权重</span>
              <span class="weight-value">{{ weights.history }}%</span>
            </div>
            <a-slider v-model:value="weights.history" :min="0" :max="100" :step="5" @change="handleWeightChange" />
          </div>

          <div class="weight-item">
            <div class="weight-header">
              <span class="weight-label">工时表现权重</span>
              <span class="weight-value">{{ weights.workhour }}%</span>
            </div>
            <a-slider v-model:value="weights.workhour" :min="0" :max="100" :step="5" @change="handleWeightChange" />
          </div>

          <div class="weight-item">
            <div class="weight-header">
              <span class="weight-label">考勤情况权重</span>
              <span class="weight-value">{{ weights.attendance }}%</span>
            </div>
            <a-slider v-model:value="weights.attendance" :min="0" :max="100" :step="5" @change="handleWeightChange" />
          </div>

          <div class="weight-summary">
            <a-alert
              :type="weightTotal === 100 ? 'success' : 'warning'"
              :message="`权重总和: ${weightTotal}%`"
              :description="weightTotal !== 100 ? '权重总和应为100%' : '权重配置正确'"
              show-icon />
          </div>
        </div>
      </a-card>

      <!-- 预览结果 -->
      <a-card size="small" title="分配预览" class="preview-card">
        <div class="preview-actions">
          <a-button @click="handlePreview" :loading="previewLoading" size="small">
            <template #icon><Icon icon="ant-design:eye-outlined" /></template>
            预览分配结果
          </a-button>
          <a-button @click="handleRefresh" size="small">
            <template #icon><Icon icon="ant-design:reload-outlined" /></template>
            重新计算
          </a-button>
        </div>

        <div class="preview-table" v-if="previewData.length > 0">
          <a-table :columns="previewColumns" :data-source="previewData" :pagination="false" size="small" :scroll="{ y: 300 }">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'scoreChange'">
                <span :class="getScoreChangeClass(record.scoreChange)"> {{ record.scoreChange > 0 ? '+' : '' }}{{ record.scoreChange }} </span>
              </template>
              <template v-if="column.key === 'newScore'">
                <a-tag :color="getScoreColor(record.newScore)"> {{ record.newScore }}分 </a-tag>
              </template>
            </template>
          </a-table>
        </div>

        <div class="preview-empty" v-else>
          <a-empty description="点击预览按钮查看分配结果" />
        </div>
      </a-card>

      <!-- 分配统计 -->
      <a-card size="small" title="分配统计" class="stats-card" v-if="allocationStats">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-statistic title="平均分数" :value="allocationStats.averageScore" suffix="分" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="最高分数" :value="allocationStats.maxScore" suffix="分" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="最低分数" :value="allocationStats.minScore" suffix="分" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="标准差" :value="allocationStats.standardDeviation" :precision="2" />
          </a-col>
        </a-row>
      </a-card>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed, watch } from 'vue';
  import { Icon } from '/@/components/Icon';
  import { useMessage } from '/@/hooks/web/useMessage';

  interface Employee {
    userId: string;
    userName: string;
    currentScore: number;
    historyScore?: number;
    workhourScore?: number;
    attendanceScore?: number;
  }

  interface PreviewData {
    userId: string;
    userName: string;
    currentScore: number;
    newScore: number;
    scoreChange: number;
  }

  interface AllocationStats {
    averageScore: number;
    maxScore: number;
    minScore: number;
    standardDeviation: number;
  }

  const props = defineProps({
    open: {
      type: Boolean,
      default: false,
    },
    employees: {
      type: Array as PropType<Employee[]>,
      default: () => [],
    },
    totalScore: {
      type: Number,
      default: 0,
    },
  });

  const emit = defineEmits(['update:open', 'confirm', 'cancel']);

  const { createMessage } = useMessage();

  // 响应式数据
  const visible = computed({
    get: () => props.open,
    set: val => emit('update:open', val),
  });

  const loading = ref(false);
  const previewLoading = ref(false);
  const allocationStrategy = ref('comprehensive');
  const previewData = ref<PreviewData[]>([]);
  const allocationStats = ref<AllocationStats | null>(null);

  const weights = reactive({
    history: 40,
    workhour: 35,
    attendance: 25,
  });

  // 策略描述
  const strategyDescriptions = {
    history: '基于员工历史绩效表现进行分配，表现优秀的员工获得更高分数',
    workhour: '基于员工工时填写完成情况和质量进行分配，工时表现好的员工获得更高分数',
    attendance: '基于员工考勤情况进行分配，出勤率高的员工获得更高分数',
    comprehensive: '综合考虑历史绩效、工时表现和考勤情况，可自定义各项权重',
  };

  // 预览表格列
  const previewColumns = [
    { title: '员工姓名', dataIndex: 'userName', key: 'userName', width: 120 },
    { title: '当前分数', dataIndex: 'currentScore', key: 'currentScore', width: 100, align: 'center' },
    { title: '建议分数', dataIndex: 'newScore', key: 'newScore', width: 100, align: 'center' },
    { title: '分数变化', dataIndex: 'scoreChange', key: 'scoreChange', width: 100, align: 'center' },
  ];

  // 计算属性
  const weightTotal = computed(() => {
    return weights.history + weights.workhour + weights.attendance;
  });

  // 监听器
  watch(
    () => props.open,
    newVal => {
      if (newVal) {
        resetData();
      }
    },
  );

  // 方法
  const resetData = () => {
    previewData.value = [];
    allocationStats.value = null;
    allocationStrategy.value = 'comprehensive';
    weights.history = 40;
    weights.workhour = 35;
    weights.attendance = 25;
  };

  const handleWeightChange = () => {
    // 权重变化时自动重新计算预览
    if (previewData.value.length > 0) {
      handlePreview();
    }
  };

  const handlePreview = async () => {
    if (allocationStrategy.value === 'comprehensive' && weightTotal.value !== 100) {
      createMessage.warning('权重总和必须为100%');
      return;
    }

    previewLoading.value = true;

    try {
      // 模拟智能分配计算
      await new Promise(resolve => setTimeout(resolve, 1000));

      const results = calculateSmartAllocation();
      previewData.value = results;
      allocationStats.value = calculateStats(results);

      createMessage.success('分配预览计算完成');
    } catch (error) {
      createMessage.error('计算分配预览失败');
    } finally {
      previewLoading.value = false;
    }
  };

  const calculateSmartAllocation = (): PreviewData[] => {
    return props.employees.map(emp => {
      let newScore = emp.currentScore;

      switch (allocationStrategy.value) {
        case 'history':
          newScore = calculateHistoryBasedScore(emp);
          break;
        case 'workhour':
          newScore = calculateWorkhourBasedScore(emp);
          break;
        case 'attendance':
          newScore = calculateAttendanceBasedScore(emp);
          break;
        case 'comprehensive':
          newScore = calculateComprehensiveScore(emp);
          break;
      }

      return {
        userId: emp.userId,
        userName: emp.userName,
        currentScore: emp.currentScore,
        newScore: Math.round(newScore),
        scoreChange: Math.round(newScore - emp.currentScore),
      };
    });
  };

  const calculateHistoryBasedScore = (emp: Employee): number => {
    // 基于历史绩效的分配算法
    const baseScore = props.totalScore / props.employees.length;
    const historyFactor = (emp.historyScore || 75) / 75; // 假设75为平均分
    return baseScore * historyFactor;
  };

  const calculateWorkhourBasedScore = (emp: Employee): number => {
    // 基于工时表现的分配算法
    const baseScore = props.totalScore / props.employees.length;
    const workhourFactor = (emp.workhourScore || 80) / 80; // 假设80为平均分
    return baseScore * workhourFactor;
  };

  const calculateAttendanceBasedScore = (emp: Employee): number => {
    // 基于考勤情况的分配算法
    const baseScore = props.totalScore / props.employees.length;
    const attendanceFactor = (emp.attendanceScore || 90) / 90; // 假设90为平均分
    return baseScore * attendanceFactor;
  };

  const calculateComprehensiveScore = (emp: Employee): number => {
    // 综合评估分配算法
    const baseScore = props.totalScore / props.employees.length;

    const historyFactor = ((emp.historyScore || 75) / 75) * (weights.history / 100);
    const workhourFactor = ((emp.workhourScore || 80) / 80) * (weights.workhour / 100);
    const attendanceFactor = ((emp.attendanceScore || 90) / 90) * (weights.attendance / 100);

    const totalFactor = historyFactor + workhourFactor + attendanceFactor;
    return baseScore * totalFactor;
  };

  const calculateStats = (data: PreviewData[]): AllocationStats => {
    const scores = data.map(item => item.newScore);
    const sum = scores.reduce((a, b) => a + b, 0);
    const avg = sum / scores.length;
    const variance = scores.reduce((a, b) => a + Math.pow(b - avg, 2), 0) / scores.length;

    return {
      averageScore: Math.round(avg * 100) / 100,
      maxScore: Math.max(...scores),
      minScore: Math.min(...scores),
      standardDeviation: Math.round(Math.sqrt(variance) * 100) / 100,
    };
  };

  const handleRefresh = () => {
    if (previewData.value.length > 0) {
      handlePreview();
    }
  };

  const getScoreChangeClass = (change: number) => {
    if (change > 0) return 'score-increase';
    if (change < 0) return 'score-decrease';
    return 'score-unchanged';
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'green';
    if (score >= 80) return 'blue';
    if (score >= 70) return 'orange';
    return 'red';
  };

  const handleConfirm = async () => {
    if (previewData.value.length === 0) {
      createMessage.warning('请先预览分配结果');
      return;
    }

    loading.value = true;

    try {
      emit('confirm', {
        strategy: allocationStrategy.value,
        weights: { ...weights },
        results: previewData.value,
      });

      createMessage.success('智能分配完成');
      visible.value = false;
    } catch (error) {
      createMessage.error('分配失败');
    } finally {
      loading.value = false;
    }
  };

  const handleCancel = () => {
    emit('cancel');
    visible.value = false;
  };
</script>

<style lang="less" scoped>
  .modal-title {
    display: flex;
    align-items: center;
    gap: 8px;

    .title-icon {
      color: #1890ff;
      font-size: 16px;
    }
  }

  .smart-allocation-content {
    .strategy-card,
    .weight-card,
    .preview-card,
    .stats-card {
      margin-bottom: 16px;
    }

    .strategy-options {
      width: 100%;
      margin-bottom: 12px;
    }

    .strategy-description {
      padding: 8px 12px;
      background: #f6f8fa;
      border-radius: 4px;
      font-size: 13px;
      color: #666;
    }

    .weight-sliders {
      .weight-item {
        margin-bottom: 20px;

        .weight-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .weight-label {
            font-size: 14px;
            color: #333;
          }

          .weight-value {
            font-size: 14px;
            font-weight: bold;
            color: #1890ff;
          }
        }
      }

      .weight-summary {
        margin-top: 16px;
      }
    }

    .preview-actions {
      display: flex;
      gap: 8px;
      margin-bottom: 16px;
    }

    .preview-empty {
      text-align: center;
      padding: 40px 0;
    }

    .score-increase {
      color: #52c41a;
      font-weight: bold;
    }

    .score-decrease {
      color: #ff4d4f;
      font-weight: bold;
    }

    .score-unchanged {
      color: #666;
    }
  }
</style>
