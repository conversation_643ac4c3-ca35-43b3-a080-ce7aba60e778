package com.xinghuo.project.biz.model.bizContract;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.Date;

/**
 * 合同日期更新表单对象
 */
@Data
@Schema(description = "合同日期更新表单对象")
public class BizContractDateUpdateForm {

    @Schema(description = "合同主键ID")
    private String id;

    /**
     * 日期类型
     */
    @NotBlank(message = "日期类型不能为空")
    @Schema(description = "日期类型")
    private String dateType;

    /**
     * 新日期
     */
    @NotNull(message = "新日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "新日期")
    private Date newDate;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @Schema(description = "备注")
    private String note;
}

