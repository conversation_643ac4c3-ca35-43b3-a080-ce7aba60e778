package com.xinghuo.project.biz.model.paymentContractMoney;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 采购付款计划统计信息
 */
@Data
@Schema(description = "采购付款计划统计信息")
public class PaymentContractMoneyStats {

    /**
     * 计划总金额
     */
    @Schema(description = "计划总金额")
    private BigDecimal totalAmount = BigDecimal.ZERO;

    /**
     * 已付金额
     */
    @Schema(description = "已付金额")
    private BigDecimal paidAmount = BigDecimal.ZERO;

    /**
     * 待付金额
     */
    @Schema(description = "待付金额")
    private BigDecimal unpaidAmount = BigDecimal.ZERO;

    /**
     * 已完成计划数量
     */
    @Schema(description = "已完成计划数量")
    private Integer completedCount = 0;

    /**
     * 待付计划数量
     */
    @Schema(description = "待付计划数量")
    private Integer pendingCount = 0;

    /**
     * 执行进度（百分比）
     */
    @Schema(description = "执行进度（百分比）")
    private BigDecimal progressRate = BigDecimal.ZERO;
}
