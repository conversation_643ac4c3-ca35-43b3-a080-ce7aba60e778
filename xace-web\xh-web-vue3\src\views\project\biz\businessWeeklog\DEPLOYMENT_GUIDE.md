# 商机周报管理模块部署指南

## 🚀 快速开始

### 1. 前端部署

前端代码已经完成，无需额外安装依赖。

**已包含的文件：**
- ✅ `index.vue` - 商机周报管理主页面
- ✅ `BusinessWeeklogDrawer.vue` - 新增/编辑抽屉
- ✅ `DetailDrawer.vue` - 详情抽屉
- ✅ `AuditDrawer.vue` - 审核抽屉
- ✅ `HistoryDrawer.vue` - 历史记录抽屉
- ✅ `businessWeeklog.data.ts` - 数据配置文件
- ✅ `route.ts` - 路由配置
- ✅ API接口文件

### 2. 后端部署

**已包含的文件：**
- ✅ `BizBusinessWeeklogEntity.java` - 实体类
- ✅ `BizBusinessWeeklogMapper.java` - Mapper接口
- ✅ `BizBusinessWeeklogService.java` - 服务接口
- ✅ `BizBusinessWeeklogServiceImpl.java` - 服务实现
- ✅ `BizBusinessWeeklogController.java` - 控制器
- ✅ 所有VO/DTO模型类

**部署步骤：**
1. 确保后端服务正常运行
2. 数据库表结构已存在（`zz_proj_business_weeklog_v2`）
3. 重启后端服务以加载新的Controller

### 3. 路由配置

在主路由文件中引入商机周报管理路由：

```typescript
// src/router/routes/modules/project.ts
import businessWeeklogRoute from '/@/views/project/biz/businessWeeklog/route';

export default {
  path: '/project',
  name: 'Project',
  redirect: '/project/dashboard',
  meta: {
    orderNo: 10,
    icon: 'ion:grid-outline',
    title: '项目管理',
  },
  children: [
    businessWeeklogRoute,
    // 其他路由...
  ],
};
```

### 4. 菜单配置

在后端管理系统中配置菜单：

```sql
-- 主菜单（如果不存在）
INSERT INTO base_menu (id, parent_id, en_code, full_name, icon, url_address, type, sort_code, enabled_mark) 
VALUES ('project_biz', 'project_main', 'project.biz', '项目业务', 'ant-design:project-outlined', '', 0, 20, 1);

-- 商机周报管理菜单
INSERT INTO base_menu (id, parent_id, en_code, full_name, icon, url_address, type, sort_code, enabled_mark) 
VALUES ('business_weeklog', 'project_biz', 'project.business.weeklog', '商机周报管理', 'ant-design:file-text-outlined', 'project/biz/businessWeeklog', 1, 30, 1);
```

### 5. 权限配置

配置相关权限：

```sql
-- 权限配置
INSERT INTO base_permission (id, en_code, full_name, type, enabled_mark) VALUES 
('project.business.weeklog.view', 'project.business.weeklog.view', '查看商机周报', 1, 1),
('project.business.weeklog.create', 'project.business.weeklog.create', '创建商机周报', 1, 1),
('project.business.weeklog.edit', 'project.business.weeklog.edit', '编辑商机周报', 1, 1),
('project.business.weeklog.delete', 'project.business.weeklog.delete', '删除商机周报', 1, 1),
('project.business.weeklog.submit', 'project.business.weeklog.submit', '提交审核', 1, 1),
('project.business.weeklog.audit', 'project.business.weeklog.audit', '审核商机周报', 1, 1);

-- 菜单权限关联
INSERT INTO base_menu_permission (menu_id, permission_id) VALUES 
('business_weeklog', 'project.business.weeklog.view'),
('business_weeklog', 'project.business.weeklog.create'),
('business_weeklog', 'project.business.weeklog.edit'),
('business_weeklog', 'project.business.weeklog.delete'),
('business_weeklog', 'project.business.weeklog.submit'),
('business_weeklog', 'project.business.weeklog.audit');
```

### 6. 角色权限分配

为不同角色分配权限：

```sql
-- 管理员角色（拥有所有权限）
INSERT INTO base_role_permission (role_id, permission_id) 
SELECT 'admin', permission_id FROM base_permission WHERE en_code LIKE 'project.business.weeklog.%';

-- 分部经理角色（拥有审核权限）
INSERT INTO base_role_permission (role_id, permission_id) VALUES 
('dept_manager', 'project.business.weeklog.view'),
('dept_manager', 'project.business.weeklog.audit');

-- 项目经理角色（拥有基本操作权限）
INSERT INTO base_role_permission (role_id, permission_id) VALUES 
('project_manager', 'project.business.weeklog.view'),
('project_manager', 'project.business.weeklog.create'),
('project_manager', 'project.business.weeklog.edit'),
('project_manager', 'project.business.weeklog.submit');
```

## 📋 验证部署

### 1. 前端验证
1. 启动前端开发服务器
2. 登录系统
3. 导航到"项目管理" → "商机周报管理"
4. 验证页面正常加载

### 2. 后端验证
1. 检查后端服务日志，确认Controller加载成功
2. 访问API接口测试：
   ```bash
   # 测试获取列表接口
   POST /api/project/biz/businessWeeklog/getList
   Content-Type: application/json
   
   {
     "currentPage": 1,
     "pageSize": 10
   }
   ```

### 3. 功能验证
1. **新增功能**：点击"新增商机周报"，填写表单并保存
2. **查看功能**：点击"查看详情"，验证详情页面显示
3. **编辑功能**：点击"编辑"，修改信息并保存
4. **审核功能**：提交审核后，使用分部经理账号进行审核
5. **历史记录**：点击"查看历史"，验证历史记录显示

## 🔧 故障排除

### 常见问题

#### 1. 页面无法访问
**问题**：点击菜单后页面显示404
**解决方案**：
- 检查路由配置是否正确
- 确认菜单URL地址是否正确
- 检查权限配置

#### 2. API接口调用失败
**问题**：前端调用后端接口返回404或500错误
**解决方案**：
- 检查后端服务是否正常启动
- 确认Controller类是否被正确扫描
- 检查数据库连接和表结构

#### 3. 权限验证失败
**问题**：用户无法访问某些功能
**解决方案**：
- 检查用户角色权限配置
- 确认权限代码是否正确
- 验证Sa-Token配置

#### 4. 审核功能异常
**问题**：审核操作失败或权限不足
**解决方案**：
- 检查分部负责人配置
- 确认组织架构数据完整性
- 验证审核权限逻辑

### 日志检查

#### 前端日志
```bash
# 浏览器控制台查看错误信息
F12 → Console → 查看错误日志
```

#### 后端日志
```bash
# 查看应用日志
tail -f logs/application.log

# 查看错误日志
tail -f logs/error.log
```

## 📊 性能优化

### 1. 前端优化
- 使用虚拟滚动处理大量数据
- 实现懒加载和分页
- 优化组件渲染性能

### 2. 后端优化
- 添加数据库索引
- 实现查询缓存
- 优化SQL查询性能

### 3. 数据库优化
```sql
-- 添加索引优化查询性能
CREATE INDEX idx_proj_id_start_date ON zz_proj_business_weeklog_v2(proj_id, start_date);
CREATE INDEX idx_fb_id_status ON zz_proj_business_weeklog_v2(fb_id, status);
CREATE INDEX idx_own_id_created_at ON zz_proj_business_weeklog_v2(own_id, f_created_at);
```

## 🔄 升级指南

### 版本升级步骤
1. 备份现有数据
2. 更新代码文件
3. 执行数据库迁移脚本
4. 重启服务
5. 验证功能正常

### 数据迁移
如果需要从旧版本迁移数据：
```sql
-- 示例：从旧表迁移数据到新表
INSERT INTO zz_proj_business_weeklog_v2 (...)
SELECT ... FROM old_table WHERE ...;
```

## 📞 技术支持

如果在部署过程中遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查系统日志获取详细错误信息
3. 联系技术支持团队

---

**部署完成后，商机周报管理功能即可正常使用！** 🎉
