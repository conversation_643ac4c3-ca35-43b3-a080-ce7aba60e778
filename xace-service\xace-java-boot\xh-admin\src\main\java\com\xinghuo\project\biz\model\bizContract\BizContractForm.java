package com.xinghuo.project.biz.model.bizContract;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 合同表单对象
 */
@Data
@Schema(description = "合同表单对象")
public class BizContractForm {

    @Schema(description = "合同主键ID")
    private String id;
    /**
     * 合同名称
     */
    @NotBlank(message = "合同名称不能为空")
    @Size(max = 500, message = "合同名称长度不能超过500个字符")
    @Schema(description = "合同名称")
    private String name;

    /**
     * 合同编号
     */
    @NotBlank(message = "合同编号不能为空")
    @Size(max = 50, message = "合同编号长度不能超过50个字符")
    @Schema(description = "合同编号")
    private String cno;

    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    private String custId;

    /**
     * 最终用户ID
     */
    @Schema(description = "最终用户ID")
    private String finalUserId;

    /**
     * 负责人ID
     */
    @NotBlank(message = "负责人不能为空")
    @Schema(description = "负责人ID")
    private String ownId;

    /**
     * 汇报频率
     */
    @Schema(description = "汇报频率")
    private String reportFrequency;

    /**
     * 合同金额
     */
    @NotNull(message = "合同金额不能为空")
    @Schema(description = "合同金额")
    private BigDecimal amount;

    /**
     * 外采金额
     */
    @Schema(description = "外采金额")
    private BigDecimal externalAmount;

    /**
     * 收款状态
     */
    @Schema(description = "收款状态")
    private String moneyStatus;

    /**
     * 合同状态
     */
    @Schema(description = "合同状态")
    private String contractStatus;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @Schema(description = "备注")
    private String note;

    /**
     * 部门ID
     */
    @NotBlank(message = "部门不能为空")
    @Schema(description = "部门ID")
    private String deptId;

    /**
     * 联系人ID
     */
    @Schema(description = "联系人ID")
    private String linkmanId;

    /**
     * 联系人
     */
    @Size(max = 50, message = "联系人长度不能超过50个字符")
    @Schema(description = "联系人")
    private String linkman;

    /**
     * 联系电话
     */
    @Size(max = 50, message = "联系电话长度不能超过50个字符")
    @Schema(description = "联系电话")
    private String linktelephone;

    /**
     * 合同签订日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "合同签订日期")
    private Date signDate;

    /**
     * 中标日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "中标日期")
    private Date bidDate;

    /**
     * 开工日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "开工日期")
    private Date commencementDate;

    /**
     * 初验日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "初验日期")
    private Date initialCheckDate;

    /**
     * 终验日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "终验日期")
    private Date finalCheckDate;

    /**
     * 审计日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "审计日期")
    private Date auditDate;

    /**
     * 合同开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "合同开始日期")
    private Date cstartDate;

    /**
     * 合同结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "合同结束日期")
    private Date cendDate;

    /**
     * 维保开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "维保开始日期")
    private Date mstartDate;

    /**
     * 维保结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "维保结束日期")
    private Date mendDate;

    /**
     * 采购费用预测
     */
    @Schema(description = "采购费用预测")
    private BigDecimal evaExternalAmount;

    /**
     * 费用预测
     */
    @Schema(description = "费用预测")
    private BigDecimal evaCostAmount;

    /**
     * 实际采购金额
     */
    @Schema(description = "实际采购金额")
    private BigDecimal actExternalAmount;

    /**
     * 实际费用
     */
    @Schema(description = "实际费用")
    private BigDecimal actCostAmount;

    /**
     * 待签外采金额
     */
    @Schema(description = "待签外采金额")
    private BigDecimal unsignExternalAmount;

    /**
     * 合同类型状态
     */
    @Schema(description = "合同类型状态")
    private String typeStatus;

    /**
     * 是否外采状态
     */
    @Schema(description = "是否外采状态")
    private String externalStatus;

    /**
     * 预估毛利
     */
    @Schema(description = "预估毛利")
    private BigDecimal estProbit;

    /**
     * 实际毛利
     */
    @Schema(description = "实际毛利")
    private BigDecimal actProbit;

    /**
     * 预估毛利率
     */
    @Schema(description = "预估毛利率")
    private BigDecimal estProbitRatio;

    /**
     * 实际毛利率
     */
    @Schema(description = "实际毛利率")
    private BigDecimal actProbitRatio;

    /**
     * 一部金额
     */
    @Schema(description = "一部金额")
    private BigDecimal yfYbAmount;

    /**
     * 二部金额
     */
    @Schema(description = "二部金额")
    private BigDecimal yfEbAmount;

    /**
     * 综合金额
     */
    @Schema(description = "综合金额")
    private BigDecimal yfOtherAmount;

    /**
     * 交付分部金额
     */
    @Schema(description = "交付分部金额")
    private BigDecimal yfJfAmount;

    /**
     * 一部外采金额
     */
    @Schema(description = "一部外采金额")
    private BigDecimal outYbAmount;

    /**
     * 二部外采金额
     */
    @Schema(description = "二部外采金额")
    private BigDecimal outEbAmount;

    /**
     * 交付外采金额
     */
    @Schema(description = "交付外采金额")
    private BigDecimal outJfAmount;

    /**
     * 综合外采金额
     */
    @Schema(description = "综合外采金额")
    private BigDecimal outOtherAmount;

    /**
     * 待签一部外采
     */
    @Schema(description = "待签一部外采")
    private BigDecimal unsignOutYbAmount;

    /**
     * 待签二部待采
     */
    @Schema(description = "待签二部待采")
    private BigDecimal unsignOutEbAmount;

    /**
     * 待签交付待采
     */
    @Schema(description = "待签交付待采")
    private BigDecimal unsignOutJfAmount;

    /**
     * 待签综合外采
     */
    @Schema(description = "待签综合外采")
    private BigDecimal unsignOutOtherAmount;

    /**
     * 一部外采已付
     */
    @Schema(description = "一部外采已付")
    private BigDecimal outYfYbAmount;

    /**
     * 二部外采已付
     */
    @Schema(description = "二部外采已付")
    private BigDecimal outYfEbAmount;

    /**
     * 交付外采已付
     */
    @Schema(description = "交付外采已付")
    private BigDecimal outYfJfAmount;

    /**
     * 综合外采已付
     */
    @Schema(description = "综合外采已付")
    private BigDecimal outYfOtherAmount;
}
