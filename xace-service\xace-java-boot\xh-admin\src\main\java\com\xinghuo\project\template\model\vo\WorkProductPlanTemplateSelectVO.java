package com.xinghuo.project.template.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 交付物计划模板选择列表视图对象
 * 用于下拉选择框等场景，只包含必要的id和fullName字段
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@Data
@Schema(description = "交付物计划模板选择列表视图对象")
public class WorkProductPlanTemplateSelectVO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private String id;

    /**
     * 完整名称 (格式: 模板名称)
     * 用于option显示，符合fullName和id的规范要求
     */
    @Schema(description = "完整名称")
    private String fullName;

    /**
     * 模板名称 (用于构建fullName)
     */
    @Schema(description = "模板名称")
    private String name;

    /**
     * 模板描述
     */
    @Schema(description = "模板描述")
    private String description;

    /**
     * 知识状态ID
     */
    @Schema(description = "知识状态ID")
    private String knStatusId;

    /**
     * 知识状态名称
     */
    @Schema(description = "知识状态名称")
    private String knStatusName;

    /**
     * 交付物总数
     */
    @Schema(description = "交付物总数")
    private Integer workProductCount;

    /**
     * 需要评审的交付物数量
     */
    @Schema(description = "需要评审的交付物数量")
    private Integer reviewRequiredCount;

    /**
     * 最终交付成果数量
     */
    @Schema(description = "最终交付成果数量")
    private Integer deliverableCount;
}
