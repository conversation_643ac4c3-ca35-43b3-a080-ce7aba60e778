package com.xinghuo.project.template.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * WBS计划模板明细表实体类 (活动编排)
 * 对应数据库表：zz_proj_tpl_wbs_detail
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("zz_proj_tpl_wbs_detail")
public class WbsTemplateDetailEntity extends BaseEntityV2.CUBaseEntityV2<String> {

    /**
     * 所属WBS模板ID (关联 zz_proj_tpl_wbs_master)
     */
    @TableField("wbs_template_id")
    private String wbsTemplateId;

    /**
     * 关联的标准活动库ID (如果此节点源于库)
     */
    @TableField("library_activity_id")
    private String libraryActivityId;

    /**
     * 父级ID (指向本表的f_id, 构建层级)
     */
    @TableField("parent_id")
    private String parentId;

    /**
     * WBS编码 (如: 1.1, 1.2.1)
     */
    @TableField("wbs_code")
    private String wbsCode;

    /**
     * 活动/工作包名称 (可从库继承)
     */
    @TableField("name")
    private String name;

    /**
     * 显示顺序
     */
    @TableField("seq_no")
    private Integer seqNo;

    /**
     * 层级深度
     */
    @TableField("level")
    private Integer level;

    /**
     * 节点类型 (1:活动, 3:工作包/摘要)
     */
    @TableField("node_type")
    private Integer nodeType;

    /**
     * 是否是里程碑 (1:是, 0:否)
     */
    @TableField("is_milestone")
    private Integer isMilestone;

    /**
     * 计划工期 (天)
     */
    @TableField("duration")
    private BigDecimal duration;

    /**
     * 计划开始偏移天数 (相对于父节点或项目开始)
     */
    @TableField("plan_start_offset")
    private Integer planStartOffset;

    /**
     * 约束类型ID (关联字典表)
     */
    @TableField("constraint_type_id")
    private String constraintTypeId;

    /**
     * 约束日期
     */
    @TableField("constraint_date")
    private Date constraintDate;

    /**
     * 责任角色ID (关联字典表)
     */
    @TableField("response_role_id")
    private String responseRoleId;

    /**
     * 前置任务ID列表(格式如: [{"id":"...", "type":"FS"}] )
     */
    @TableField("predecessors")
    private String predecessors;
}
