package com.xinghuo.manhour.model.completion;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 负责人提醒请求
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@Schema(description = "负责人提醒请求")
public class NotifyLeaderRequest {

    @Schema(description = "负责人ID")
    private String leaderId;

    @Schema(description = "提醒类型")
    private String notifyType;

    @Schema(description = "提醒内容")
    private String message;
}
