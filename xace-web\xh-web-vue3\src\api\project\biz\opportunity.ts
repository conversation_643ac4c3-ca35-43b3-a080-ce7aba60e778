import { defHttp } from '/@/utils/http/axios';

enum Api {
  GetList = '/api/project/biz/opportunity/getList',
  GetInfo = '/api/project/biz/opportunity',
  Create = '/api/project/biz/opportunity',
  Update = '/api/project/biz/opportunity',
  Delete = '/api/project/biz/opportunity',
}

// 获取商机列表
export const getOpportunityList = (params?: any) => {
  return defHttp.get({ url: Api.GetList, params });
};

// 获取商机详情
export const getOpportunityInfo = (id: string) => {
  return defHttp.get({ url: `${Api.GetInfo}/${id}` });
};

// 创建商机
export const createOpportunity = (data: any) => {
  return defHttp.post({ url: Api.Create, data });
};

// 更新商机
export const updateOpportunity = (id: string, data: any) => {
  return defHttp.put({ url: `${Api.Update}/${id}`, data });
};

// 删除商机
export const deleteOpportunity = (id: string) => {
  return defHttp.delete({ url: `${Api.Delete}/${id}` });
};
