package com.xinghuo.manhour.model.analysis;

import com.xinghuo.common.base.model.Pagination;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 工时分析分页查询参数
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "工时分析分页查询参数")
public class WorkhourAnalysisPagination extends Pagination {

    @Schema(description = "开始月份")
    private String startMonth;

    @Schema(description = "结束月份")
    private String endMonth;

    @Schema(description = "项目ID")
    private String projectId;

    @Schema(description = "分部ID")
    private String fbId;

    @Schema(description = "项目类型")
    private String projType;

    @Schema(description = "员工ID")
    private String userId;

    @Schema(description = "模块ID")
    private String moduleId;

    @Schema(description = "工时类型")
    private String workType;
}
