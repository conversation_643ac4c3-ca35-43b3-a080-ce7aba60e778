import { defHttp } from '/@/utils/http/axios';
import { ActionResult } from '/@/api/model/baseModel';

/**
 * 项目基础信息API
 */

enum Api {
  // 项目基础信息管理
  GetProjectBaseInfo = '/api/project/core/project/base-info',
  UpdateProjectBaseInfo = '/api/project/core/project/base-info',
}

/**
 * 项目基础信息VO
 */
export interface ProjectBaseInfoVO {
  id: string;
  code: string;
  fullName: string;
  description?: string;
  status: string;
  typeId?: string;
  typeName?: string;
  managerId?: string;
  managerName?: string;
  sponsorId?: string;
  sponsorName?: string;
  plannedStartDate?: Date;
  plannedEndDate?: Date;
  actualStartDate?: Date;
  actualEndDate?: Date;
  investmentBudget?: number;
  strategicObjective?: string;
  strategicObjectiveName?: string;
  priority?: string;
  priorityName?: string;
  estimatedWorkload?: number;
  riskLevel?: string;
  riskLevelName?: string;
  departmentId?: string;
  departmentName?: string;
  programId?: string;
  programName?: string;
  customerId?: string;
  customerName?: string;
  contractId?: string;
  contractName?: string;
  projectScope?: string;
  createdAt?: Date;
  createdBy?: string;
  createdByName?: string;
  lastUpdatedAt?: Date;
  lastUpdatedBy?: string;
  lastUpdatedByName?: string;
}

/**
 * 项目基础信息表单
 */
export interface ProjectBaseInfoForm {
  fullName: string;
  description?: string;
  managerId?: string;
  sponsorId?: string;
  plannedStartDate?: string;
  plannedEndDate?: string;
  investmentBudget?: number;
  strategicObjective?: string;
  priority?: string;
  estimatedWorkload?: number;
  riskLevel?: string;
  departmentId?: string;
  programId?: string;
  customerId?: string;
  contractId?: string;
  projectScope?: string;
}

/**
 * 获取项目基础信息详情
 *
 * @param id 项目ID
 * @returns 项目基础信息
 */
export function getInfo(id: string) {
  return defHttp.get<ActionResult<ProjectBaseInfoVO>>({
    url: `${Api.GetProjectBaseInfo}/${id}`,
  });
}

/**
 * 更新项目基础信息
 *
 * @param id 项目ID
 * @param data 项目基础信息表单
 * @returns 更新结果
 */
export function update(id: string, data: ProjectBaseInfoForm) {
  return defHttp.put<ActionResult<string>>({
    url: `${Api.UpdateProjectBaseInfo}/${id}`,
    data,
  });
}
