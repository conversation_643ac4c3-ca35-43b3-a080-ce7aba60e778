package com.xinghuo.common.base.model.tenant;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 *
 */
@Data
public class TenantMenuVO {
    @Schema(description = "权限模型集合")
    List<TenantMenuTreeReturnModel> list = new ArrayList<>();
    @Schema(description = "id集合")
    List<String> ids = new ArrayList<>();
    //all字段里面不包括菜单id
    @Schema(description = "所有的id")
    List<String> all = new ArrayList<>();
}
