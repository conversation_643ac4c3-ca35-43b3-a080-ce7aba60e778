/**
 * 项目上下文管理 Hook
 * 用于统一处理项目切换时的数据响应和状态管理
 */
import { ref, inject, watch, onUnmounted, type Ref } from 'vue';

export interface ProjectContextOptions {
  /** 项目切换时的回调函数 */
  onProjectChange?: (newProjectId: string, oldProjectId?: string) => void | Promise<void>;
  /** 是否立即执行回调（默认false） */
  immediate?: boolean;
  /** 是否在组件卸载时清理（默认true） */
  autoCleanup?: boolean;
}

export interface ProjectContextReturn {
  /** 当前项目ID */
  projectId: Ref<string>;
  /** 手动刷新项目数据 */
  refreshProject: () => void;
  /** 清理监听器 */
  cleanup: () => void;
}

/**
 * 项目上下文管理 Hook
 * @param options 配置选项
 * @returns 项目上下文对象
 */
export function useProjectContext(options: ProjectContextOptions = {}): ProjectContextReturn {
  const { onProjectChange, immediate = false, autoCleanup = true } = options;

  // 从父组件注入项目ID
  const projectId = inject<Ref<string>>('projectId', ref(''));

  console.log('🔧 [useProjectContext] Hook初始化:', {
    初始项目ID: projectId.value,
    hasCallback: !!onProjectChange,
    immediate,
    autoCleanup,
  });

  let stopWatcher: (() => void) | null = null;

  // 监听项目ID变化
  if (onProjectChange) {
    stopWatcher = watch(
      projectId,
      async (newProjectId, oldProjectId) => {
        console.log(`🔄 [useProjectContext] 检测到projectId变化: ${oldProjectId} → ${newProjectId}`);
        if (newProjectId && (!oldProjectId || newProjectId !== oldProjectId)) {
          console.log(`✅ [useProjectContext] 执行项目切换回调: ${oldProjectId} → ${newProjectId}`);
          try {
            await onProjectChange(newProjectId, oldProjectId);
            console.log(`✅ [useProjectContext] 项目切换回调执行成功`);
          } catch (error) {
            console.error('❌ [useProjectContext] 项目切换回调执行失败:', error);
          }
        } else {
          console.log(`⚠️ [useProjectContext] 跳过回调执行 - 项目ID无效或相同`);
        }
      },
      { immediate },
    );
    console.log('🔧 [useProjectContext] 项目ID监听器已设置');
  } else {
    console.log('⚠️ [useProjectContext] 未提供回调函数，跳过监听器设置');
  }

  // 手动刷新项目数据
  const refreshProject = () => {
    if (onProjectChange && projectId.value) {
      console.log(`🔄 [useProjectContext] 手动刷新项目数据: ${projectId.value}`);
      onProjectChange(projectId.value);
    }
  };

  // 清理监听器
  const cleanup = () => {
    if (stopWatcher) {
      stopWatcher();
      stopWatcher = null;
      console.log('🧹 [useProjectContext] 已清理项目监听器');
    }
  };

  // 组件卸载时自动清理
  if (autoCleanup) {
    onUnmounted(() => {
      cleanup();
    });
  }

  return {
    projectId,
    refreshProject,
    cleanup,
  };
}

/**
 * 简化版项目上下文Hook - 仅获取项目ID
 * @returns 项目ID引用
 */
export function useProjectId(): Ref<string> {
  return inject<Ref<string>>('projectId', ref(''));
}
