<template>
  <div class="schedule-manager">
    <a-card title="主计划管理" :bordered="false">
      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleAddTask">
            <Icon icon="ant-design:plus-outlined" />
            新增任务
          </a-button>
          <a-button @click="handleViewGantt">
            <Icon icon="ant-design:bar-chart-outlined" />
            甘特图视图
          </a-button>
          <a-button @click="handleImportSchedule">
            <Icon icon="ant-design:import-outlined" />
            导入计划模板
          </a-button>
        </a-space>
      </template>

      <div class="schedule-content">
        <!-- 计划任务表格 -->
        <BasicTable @register="registerTable" :searchInfo="searchInfo">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <TableAction
                :actions="[
                  {
                    icon: 'clarity:note-edit-line',
                    label: '编辑',
                    onClick: handleEditTask.bind(null, record),
                  },
                  {
                    icon: 'ant-design:partition-outlined',
                    label: '设置依赖',
                    onClick: handleSetDependency.bind(null, record),
                  },
                  {
                    icon: 'ant-design:delete-outlined',
                    color: 'error',
                    label: '删除',
                    popConfirm: {
                      title: '是否确认删除',
                      placement: 'left',
                      confirm: handleDeleteTask.bind(null, record),
                    },
                  },
                ]" />
            </template>
            <template v-else-if="column.key === 'isMilestone'">
              <a-tag :color="record.isMilestone ? 'gold' : 'default'">
                <Icon :icon="record.isMilestone ? 'ant-design:flag-filled' : 'ant-design:flag-outlined'" />
                {{ record.isMilestone ? '里程碑' : '普通任务' }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'duration'">
              {{ record.duration ? `${record.duration}天` : '-' }}
            </template>
            <template v-else-if="column.key === 'predecessors'">
              <div v-if="record.predecessors && record.predecessors.length > 0">
                <a-tag v-for="pred in record.predecessors" :key="pred" size="small" color="blue">
                  {{ getTaskName(pred) }}
                </a-tag>
              </div>
              <span v-else class="text-gray-400">无</span>
            </template>
            <template v-else-if="column.key === 'responsibleRole'">
              <a-tag>{{ getRoleLabel(record.responsibleRole) }}</a-tag>
            </template>
          </template>
        </BasicTable>
      </div>
    </a-card>

    <!-- 任务编辑抽屉 -->
    <BasicDrawer @register="registerDrawer" :title="drawerTitle" width="800px" @ok="handleSubmitTask">
      <BasicForm @register="registerForm" />
    </BasicDrawer>

    <!-- 依赖关系设置模态框 -->
    <BasicModal @register="registerDependencyModal" title="设置任务依赖" width="600px" @ok="handleSubmitDependency">
      <div class="dependency-setting">
        <div class="current-task">
          <h4>当前任务: {{ currentTask?.name }}</h4>
        </div>
        <a-divider />
        <div class="predecessor-selection">
          <h5>前置任务选择:</h5>
          <a-select v-model:value="selectedPredecessors" mode="multiple" placeholder="请选择前置任务" style="width: 100%" :options="predecessorOptions" />
        </div>
      </div>
    </BasicModal>

    <!-- 甘特图视图模态框 -->
    <BasicModal @register="registerGanttModal" title="甘特图视图" width="1200px" :footer="null">
      <div class="gantt-view">
        <div class="gantt-placeholder">
          <a-empty description="甘特图视图功能开发中..." />
          <!-- 这里可以集成第三方甘特图组件，如 dhtmlx-gantt 或 frappe-gantt -->
        </div>
      </div>
    </BasicModal>

    <!-- 导入计划模板模态框 -->
    <BasicModal @register="registerImportModal" title="导入计划模板" width="800px" @ok="handleImportSubmit">
      <ScheduleTemplateSelector @register="registerTemplateSelector" />
    </BasicModal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed, onMounted } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { BasicDrawer, useDrawer } from '/@/components/Drawer';
  import { BasicModal, useModal } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form';
  import { Icon } from '/@/components/Icon';
  import { useMessage } from '/@/hooks/web/useMessage';
  import ScheduleTemplateSelector from './components/ScheduleTemplateSelector.vue';
  import { scheduleFormSchema } from './formSchemas';

  const props = defineProps({
    templateId: {
      type: String,
      required: true,
    },
  });

  const emit = defineEmits(['success']);

  const { createMessage } = useMessage();

  // 搜索信息
  const searchInfo = reactive<Recordable>({});

  // 任务数据
  const taskList = ref([]);
  const currentTask = ref<any>(null);
  const selectedPredecessors = ref<string[]>([]);

  // 表格列配置
  const columns = [
    {
      title: '任务名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
    },
    {
      title: '任务编码',
      dataIndex: 'code',
      key: 'code',
      width: 120,
    },
    {
      title: '计划工期',
      dataIndex: 'duration',
      key: 'duration',
      width: 100,
    },
    {
      title: '前置任务',
      dataIndex: 'predecessors',
      key: 'predecessors',
      width: 150,
    },
    {
      title: '负责角色',
      dataIndex: 'responsibleRole',
      key: 'responsibleRole',
      width: 120,
    },
    {
      title: '里程碑',
      dataIndex: 'isMilestone',
      key: 'isMilestone',
      width: 100,
    },
    {
      title: '排序',
      dataIndex: 'sortOrder',
      key: 'sortOrder',
      width: 80,
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
  ];

  // 表格配置
  const [registerTable, { reload, getDataSource, setTableData }] = useTable({
    api: loadScheduleData,
    columns,
    useSearchForm: false,
    actionColumn: {
      width: 150,
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      fixed: 'right',
    },
    rowKey: 'id',
    pagination: false,
  });

  // 抽屉配置
  const [registerDrawer, { openDrawer, closeDrawer }] = useDrawer();

  // 表单配置
  const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
    labelWidth: 120,
    schemas: scheduleFormSchema,
    showActionButtonGroup: false,
  });

  // 模态框配置
  const [registerDependencyModal, { openModal: openDependencyModal, closeModal: closeDependencyModal }] = useModal();
  const [registerGanttModal, { openModal: openGanttModal }] = useModal();
  const [registerImportModal, { openModal: openImportModal }] = useModal();
  const [registerTemplateSelector, { getSelectedTemplate }] = useComponentRegister();

  // 编辑状态
  const isEdit = ref(false);
  const currentRecord = ref<any>(null);

  const drawerTitle = computed(() => {
    return isEdit.value ? '编辑任务' : '新增任务';
  });

  // 前置任务选项
  const predecessorOptions = computed(() => {
    return taskList.value
      .filter(task => task.id !== currentTask.value?.id)
      .map(task => ({
        label: task.name,
        value: task.id,
      }));
  });

  // 角色标签映射
  const roleLabels = {
    PM: '项目经理',
    TECH_LEAD: '技术负责人',
    DEVELOPER: '开发工程师',
    TESTER: '测试工程师',
    PRODUCT_MANAGER: '产品经理',
    DESIGNER: '设计师',
  };

  function getRoleLabel(role: string) {
    return roleLabels[role] || role;
  }

  function getTaskName(taskId: string) {
    const task = taskList.value.find(t => t.id === taskId);
    return task?.name || taskId;
  }

  // 加载计划数据
  async function loadScheduleData() {
    if (!props.templateId) return { list: [] };

    try {
      // 这里需要调用实际的API
      // const response = await getProjectTemplateScheduleConfigs(props.templateId);

      // 模拟数据
      const mockData = [
        {
          id: '1',
          name: '需求分析',
          code: 'REQ_ANALYSIS',
          duration: 5,
          predecessors: [],
          responsibleRole: 'PRODUCT_MANAGER',
          isMilestone: false,
          sortOrder: 1,
          description: '收集和分析项目需求',
        },
        {
          id: '2',
          name: '系统设计',
          code: 'SYS_DESIGN',
          duration: 8,
          predecessors: ['1'],
          responsibleRole: 'TECH_LEAD',
          isMilestone: true,
          sortOrder: 2,
          description: '设计系统架构和详细设计',
        },
      ];

      taskList.value = mockData;
      return { list: mockData };
    } catch (error) {
      console.error('加载计划数据失败:', error);
      return { list: [] };
    }
  }

  // 新增任务
  function handleAddTask() {
    isEdit.value = false;
    currentRecord.value = null;
    resetFields();
    openDrawer(true);
  }

  // 编辑任务
  function handleEditTask(record: any) {
    isEdit.value = true;
    currentRecord.value = record;
    setFieldsValue(record);
    openDrawer(true);
  }

  // 删除任务
  function handleDeleteTask(record: any) {
    try {
      taskList.value = taskList.value.filter(task => task.id !== record.id);
      setTableData(taskList.value);
      createMessage.success('删除成功');
      emit('success');
    } catch (error) {
      console.error('删除失败:', error);
      createMessage.error('删除失败');
    }
  }

  // 设置依赖关系
  function handleSetDependency(record: any) {
    currentTask.value = record;
    selectedPredecessors.value = record.predecessors || [];
    openDependencyModal(true);
  }

  // 提交依赖设置
  function handleSubmitDependency() {
    if (currentTask.value) {
      const taskIndex = taskList.value.findIndex(task => task.id === currentTask.value.id);
      if (taskIndex !== -1) {
        taskList.value[taskIndex].predecessors = [...selectedPredecessors.value];
        setTableData(taskList.value);
        createMessage.success('依赖关系设置成功');
        emit('success');
      }
    }
    closeDependencyModal();
  }

  // 查看甘特图
  function handleViewGantt() {
    openGanttModal(true);
  }

  // 导入计划模板
  function handleImportSchedule() {
    openImportModal(true);
  }

  // 导入提交
  async function handleImportSubmit() {
    try {
      const selectedTemplate = await getSelectedTemplate();
      if (!selectedTemplate) {
        createMessage.warning('请选择要导入的计划模板');
        return;
      }

      // 这里需要调用实际的导入API
      createMessage.success('导入成功');
      reload();
      emit('success');
    } catch (error) {
      console.error('导入失败:', error);
      createMessage.error('导入失败');
    }
  }

  // 提交任务表单
  async function handleSubmitTask() {
    try {
      const values = await validate();

      if (isEdit.value && currentRecord.value) {
        // 编辑模式
        const taskIndex = taskList.value.findIndex(task => task.id === currentRecord.value.id);
        if (taskIndex !== -1) {
          taskList.value[taskIndex] = { ...taskList.value[taskIndex], ...values };
        }
      } else {
        // 新增模式
        const newTask = {
          id: Date.now().toString(), // 临时ID
          templateId: props.templateId,
          predecessors: [],
          ...values,
        };
        taskList.value.push(newTask);
      }

      setTableData(taskList.value);
      createMessage.success(isEdit.value ? '编辑成功' : '新增成功');
      closeDrawer();
      emit('success');
    } catch (error) {
      console.error('保存失败:', error);
      createMessage.error('保存失败');
    }
  }

  // 临时的组件注册器
  function useComponentRegister() {
    const getSelectedTemplate = async () => {
      return null;
    };

    return [() => {}, { getSelectedTemplate }];
  }

  onMounted(() => {
    reload();
  });
</script>

<style lang="less" scoped>
  .schedule-manager {
    padding: 16px;

    :deep(.ant-card) {
      .ant-card-head {
        border-bottom: 1px solid #f0f0f0;
      }

      .ant-card-body {
        padding: 24px;
      }
    }

    .text-gray-400 {
      color: #9ca3af;
    }

    .schedule-content {
      .ant-table-wrapper {
        margin-top: 16px;
      }
    }

    .dependency-setting {
      .current-task {
        h4 {
          color: #1890ff;
          margin-bottom: 8px;
        }
      }

      .predecessor-selection {
        h5 {
          margin-bottom: 12px;
          color: rgba(0, 0, 0, 0.85);
        }
      }
    }

    .gantt-view {
      .gantt-placeholder {
        height: 400px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
</style>
