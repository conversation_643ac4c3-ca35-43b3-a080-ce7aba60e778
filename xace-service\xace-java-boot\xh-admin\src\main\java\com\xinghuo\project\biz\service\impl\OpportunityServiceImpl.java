package com.xinghuo.project.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.biz.dao.OpportunityMapper;
import com.xinghuo.project.biz.entity.OpportunityEntity;
import com.xinghuo.project.biz.model.OpportunityPagination;
import com.xinghuo.project.biz.model.OpportunityStatusForm;
import com.xinghuo.project.biz.model.opportunity.OpportunityInfoVO;
import com.xinghuo.project.biz.model.vo.OpportunityListVO;
import com.xinghuo.project.biz.service.OpportunityService;
import com.xinghuo.common.util.core.BeanCopierUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 商机服务实现类
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Service
public class OpportunityServiceImpl extends BaseServiceImpl<OpportunityMapper, OpportunityEntity> implements OpportunityService {

    @Resource
    private OpportunityMapper opportunityMapper;

    @Override
    public List<OpportunityEntity> getList(OpportunityPagination pagination) {
        QueryWrapper<OpportunityEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<OpportunityEntity> lambda = queryWrapper.lambda();

        // 根据商机编号模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getBusinessNo())) {
            lambda.like(OpportunityEntity::getBusinessNo, pagination.getBusinessNo());
        }

        // 根据项目名称模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getProjectName())) {
            lambda.like(OpportunityEntity::getProjectName, pagination.getProjectName());
        }

        // 根据客户单位ID精确查询
        if (StrXhUtil.isNotEmpty(pagination.getCustId())) {
            lambda.eq(OpportunityEntity::getCustId, pagination.getCustId());
        }

        // 根据项目等级精确查询
        if (StrXhUtil.isNotEmpty(pagination.getProjectLevel())) {
            lambda.eq(OpportunityEntity::getProjectLevel, pagination.getProjectLevel());
        }

        // 根据项目负责人ID精确查询
        if (StrXhUtil.isNotEmpty(pagination.getProjectLeader())) {
            lambda.eq(OpportunityEntity::getProjectLeader, pagination.getProjectLeader());
        }

        // 根据商机状态精确查询
        if (StrXhUtil.isNotEmpty(pagination.getStatus())) {
            lambda.eq(OpportunityEntity::getStatus, pagination.getStatus());
        }

        // 根据项目类型精确查询
        if (StrXhUtil.isNotEmpty(pagination.getProjType())) {
            lambda.eq(OpportunityEntity::getProjType, pagination.getProjType());
        }

        // 根据所属分部ID精确查询
        if (StrXhUtil.isNotEmpty(pagination.getDeptId())) {
            lambda.eq(OpportunityEntity::getDeptId, pagination.getDeptId());
        }

        // 根据研发分部ID精确查询
        if (StrXhUtil.isNotEmpty(pagination.getYfDeptId())) {
            lambda.eq(OpportunityEntity::getYfDeptId, pagination.getYfDeptId());
        }

        // 根据预计落地月份范围查询
        if (StrXhUtil.isNotEmpty(pagination.getEvaSignMonthStart())) {
            lambda.ge(OpportunityEntity::getEvaSignMonth, pagination.getEvaSignMonthStart());
        }
        if (StrXhUtil.isNotEmpty(pagination.getEvaSignMonthEnd())) {
            lambda.le(OpportunityEntity::getEvaSignMonth, pagination.getEvaSignMonthEnd());
        }

        // 根据预计启动日期范围查询
        if (pagination.getStartDateStart() != null) {
            lambda.ge(OpportunityEntity::getStartDate, pagination.getStartDateStart());
        }
        if (pagination.getStartDateEnd() != null) {
            lambda.le(OpportunityEntity::getStartDate, pagination.getStartDateEnd());
        }

        // 根据创建日期范围查询
        if (pagination.getCreateTimeStart() != null) {
            lambda.ge(OpportunityEntity::getCreatedAt, pagination.getCreateTimeStart());
        }
        if (pagination.getCreateTimeEnd() != null) {
            lambda.le(OpportunityEntity::getCreatedAt, pagination.getCreateTimeEnd());
        }

        // 根据商机标签精确查询
        if (StrXhUtil.isNotEmpty(pagination.getBusinessTag())) {
            lambda.eq(OpportunityEntity::getBusinessTag, pagination.getBusinessTag());
        }

        // 根据工时填写状态精确查询
        if (StrXhUtil.isNotEmpty(pagination.getWorkStatus())) {
            lambda.eq(OpportunityEntity::getWorkStatus, pagination.getWorkStatus());
        }

        // 根据项目基础ID精确查询
        if (StrXhUtil.isNotEmpty(pagination.getProjBaseId())) {
            lambda.eq(OpportunityEntity::getProjBaseId, pagination.getProjBaseId());
        }

        // 根据关键字搜索项目名称或商机编号
        String keyword = pagination.getKeyword();
        if (StrXhUtil.isNotEmpty(keyword)) {
            lambda.and(wrapper -> wrapper
                    .like(OpportunityEntity::getProjectName, keyword)
                    .or()
                    .like(OpportunityEntity::getBusinessNo, keyword)
            );
        }

        // 排除已删除的记录
       lambda.isNull(OpportunityEntity::getDeleteMark);

        // 排序
        lambda.orderByDesc(OpportunityEntity::getCreatedAt);
        
        return processDataType(queryWrapper, pagination);
    }

    @Override
    public List<OpportunityListVO> getListWithDetails(OpportunityPagination pagination) {
        // 获取实体列表
        List<OpportunityEntity> entities = getList(pagination);
        
        // 转换为VO列表
        List<OpportunityListVO> voList = BeanCopierUtils.copyList(entities, OpportunityListVO.class);
        
        // TODO: 填充关联信息（用户名、部门名等）
        // 这里需要调用用户服务和组织服务来填充相关信息
        
        return voList;
    }

    @Override
    public List<OpportunityEntity> getListByCustId(String custId) {
        LambdaQueryWrapper<OpportunityEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OpportunityEntity::getCustId, custId);
        queryWrapper.isNull(OpportunityEntity::getDeleteMark);
        queryWrapper.orderByDesc(OpportunityEntity::getCreatedAt);
        return this.list(queryWrapper);
    }

    @Override
    public OpportunityEntity getInfo(String id) {
        return this.getById(id);
    }

    @Override
    public OpportunityInfoVO getDetailInfo(String id) {
        // 获取实体信息
        OpportunityEntity entity = getInfo(id);
        if (entity == null) {
            return null;
        }
        
        // 转换为VO
        OpportunityInfoVO vo = BeanCopierUtils.copy(entity, OpportunityInfoVO.class);
        
        // TODO: 填充关联信息（用户名、部门名等）
        // 这里需要调用用户服务和组织服务来填充相关信息
        
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String create(OpportunityEntity entity) {
        // 生成商机ID
        String id = RandomUtil.snowId();
        entity.setId(id);

        // 设置初始状态
        if (StrXhUtil.isEmpty(entity.getStatus())) {
            entity.setStatus("跟踪中");
        }

        // 设置删除标记
        entity.setDeleteMark(1);

        // 保存商机
        this.save(entity);

        return id;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(String id, OpportunityEntity entity, boolean trackChanges) {
        // 获取原始商机信息
        OpportunityEntity oldEntity = this.getById(id);
        if (oldEntity == null) {
            throw new RuntimeException("商机不存在");
        }

        // 设置ID
        entity.setId(id);

        // 更新商机
        this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(String id, OpportunityEntity entity) {
        update(id, entity, false);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        OpportunityEntity entity = this.getById(id);
        if (entity == null) {
            throw new RuntimeException("商机不存在");
        }

        // 逻辑删除
        entity.setDeleteMark(1);
        this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(String id, OpportunityStatusForm form) {
        OpportunityEntity entity = this.getById(id);
        if (entity == null) {
            throw new RuntimeException("商机不存在");
        }

        // 更新状态
        entity.setStatus(form.getStatus());

        // 更新最后跟踪记录
        if (StrXhUtil.isNotEmpty(form.getLastNote())) {
            entity.setLastNote(form.getLastNote());
        }

        // 如果状态为"已签"，则更新合同编号
        if ("已签".equals(form.getStatus()) && StrXhUtil.isNotEmpty(form.getProjectNo())) {
            entity.setProjectNo(form.getProjectNo());
        }

        // 更新商机
        this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLastNote(String id, String lastNote) {
        OpportunityEntity entity = this.getById(id);
        if (entity == null) {
            throw new RuntimeException("商机不存在");
        }

        // 更新最后跟踪记录
        entity.setLastNote(lastNote);

        // 更新商机
        this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateWorkStatus(String id, Integer workStatus) {
        OpportunityEntity entity = this.getById(id);
        if (entity == null) {
            throw new RuntimeException("商机不存在");
        }

        // 更新工时填写状态
        entity.setWorkStatus(workStatus);

        // 更新商机
        this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String convertToContract(String id, String projectNo) {
        OpportunityEntity opportunity = this.getById(id);
        if (opportunity == null) {
            throw new RuntimeException("商机不存在");
        }

        // 更新商机状态和合同编号
        opportunity.setStatus("已签");
        opportunity.setProjectNo(projectNo);
        this.updateById(opportunity);

        // TODO: 创建合同逻辑
        // 这里需要调用合同服务来创建合同

        return "contract_id"; // 返回创建的合同ID
    }

    @Override
    public List<Map<String, Object>> getSalesFunnelData(Map<String, Object> params) {
        // 使用QueryWrapper实现销售漏斗数据统计
        QueryWrapper<OpportunityEntity> queryWrapper = new QueryWrapper<>();

        // 根据参数添加查询条件
        if (params != null) {
            String year = (String) params.get("year");
            String month = (String) params.get("month");
            String deptId = (String) params.get("deptId");
            String leaderId = (String) params.get("leaderId");

            if (StrXhUtil.isNotEmpty(year)) {
                queryWrapper.lambda().like(OpportunityEntity::getEvaSignMonth, year);
            }

            if (StrXhUtil.isNotEmpty(month)) {
                queryWrapper.lambda().like(OpportunityEntity::getEvaSignMonth, year + "-" + month);
            }

            if (StrXhUtil.isNotEmpty(deptId)) {
                queryWrapper.lambda().eq(OpportunityEntity::getDeptId, deptId);
            }

            if (StrXhUtil.isNotEmpty(leaderId)) {
                queryWrapper.lambda().eq(OpportunityEntity::getProjectLeader, leaderId);
            }
        }

        // 排除已删除的记录
        queryWrapper.lambda().isNull(OpportunityEntity::getDeleteMark);

        // 按状态分组统计
        queryWrapper.select("status, COUNT(*) as count, SUM(eva_amount) as totalAmount")
                   .groupBy("status");

        return this.listMaps(queryWrapper);
    }

    @Override
    public List<Map<String, Object>> getBusinessForecastData(Map<String, Object> params) {
        // 使用QueryWrapper实现商机预测数据统计
        QueryWrapper<OpportunityEntity> queryWrapper = new QueryWrapper<>();

        // 根据参数添加查询条件
        if (params != null) {
            String year = (String) params.get("year");
            String deptId = (String) params.get("deptId");
            String leaderId = (String) params.get("leaderId");

            if (StrXhUtil.isNotEmpty(year)) {
                queryWrapper.lambda().like(OpportunityEntity::getEvaSignMonth, year);
            }

            if (StrXhUtil.isNotEmpty(deptId)) {
                queryWrapper.lambda().eq(OpportunityEntity::getDeptId, deptId);
            }

            if (StrXhUtil.isNotEmpty(leaderId)) {
                queryWrapper.lambda().eq(OpportunityEntity::getProjectLeader, leaderId);
            }
        }

        // 排除已删除的记录
        queryWrapper.lambda().isNull(OpportunityEntity::getDeleteMark);

        // 按月份分组统计预测金额
        queryWrapper.select("eva_sign_month, COUNT(*) as count, SUM(eva_amount) as forecastAmount")
                   .groupBy("eva_sign_month")
                   .orderBy(true, true, "eva_sign_month");

        return this.listMaps(queryWrapper);
    }

    @Override
    public List<Map<String, Object>> getWinLoseAnalysisData(Map<String, Object> params) {
        // 使用QueryWrapper实现赢单/输单分析数据统计
        QueryWrapper<OpportunityEntity> queryWrapper = new QueryWrapper<>();

        // 根据参数添加查询条件
        if (params != null) {
            String year = (String) params.get("year");
            String deptId = (String) params.get("deptId");
            String leaderId = (String) params.get("leaderId");
            String startDate = (String) params.get("startDate");
            String endDate = (String) params.get("endDate");

            if (StrXhUtil.isNotEmpty(year)) {
                queryWrapper.lambda().like(OpportunityEntity::getEvaSignMonth, year);
            }

            if (StrXhUtil.isNotEmpty(deptId)) {
                queryWrapper.lambda().eq(OpportunityEntity::getDeptId, deptId);
            }

            if (StrXhUtil.isNotEmpty(leaderId)) {
                queryWrapper.lambda().eq(OpportunityEntity::getProjectLeader, leaderId);
            }

            if (StrXhUtil.isNotEmpty(startDate)) {
                queryWrapper.lambda().ge(OpportunityEntity::getCreatedAt, startDate);
            }

            if (StrXhUtil.isNotEmpty(endDate)) {
                queryWrapper.lambda().le(OpportunityEntity::getCreatedAt, endDate);
            }
        }

        // 排除已删除的记录
        queryWrapper.lambda().isNull(OpportunityEntity::getDeleteMark);

        // 只统计已签和已废弃的商机
        queryWrapper.lambda().in(OpportunityEntity::getStatus, "已签", "已废弃");

        // 按状态分组统计
        queryWrapper.select("status, COUNT(*) as count, SUM(eva_amount) as totalAmount")
                   .groupBy("status");

        return this.listMaps(queryWrapper);
    }
}
