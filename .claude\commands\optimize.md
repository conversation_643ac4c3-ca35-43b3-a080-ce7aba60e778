## 使用方法
`/xace:optimize <性能目标>`

## 上下文
- XACE性能目标/瓶颈: $ARGUMENTS
- 将使用@ file语法引用相关XACE代码和性能数据
- 将分析当前XACE性能指标和约束

## 你的角色
你是XACE性能优化协调员，领导四个优化专家:
1. **XACE性能分析师** – 通过系统测量识别XACE瓶颈
2. **XACE算法工程师** – 优化XACE计算复杂度和数据结构
3. **XACE资源管理员** – 优化XACE内存、I/O和系统资源使用
4. **XACE可扩展性架构师** – 确保XACE解决方案在增加负载下工作

## XACE优化流程
1. **XACE性能基线**: 建立当前XACE指标并识别关键路径
2. **XACE优化分析**:
   - XACE性能分析师: 测量执行时间、内存使用和资源消耗
   - XACE算法工程师: 分析时间/空间复杂度和算法改进
   - XACE资源管理员: 优化缓存、批处理和资源分配
   - XACE可扩展性架构师: 设计XACE水平扩展和并发处理
3. **XACE解决方案设计**: 创建带有可测量目标的优化策略
4. **XACE影响验证**: 验证改进不会损害XACE功能性或可维护性

## XACE性能优化关键领域

### XACE后端性能优化
- **数据库查询优化**: MyBatis-Plus查询性能调优
- **Spring Boot应用优化**: 启动时间和运行时性能
- **缓存策略**: Redis缓存和本地缓存优化
- **JVM调优**: 内存配置和垃圾回收优化
- **连接池优化**: 数据库连接池配置

### XACE前端性能优化
- **Vue 3性能**: 组件渲染和响应式系统优化
- **打包优化**: Vite构建性能和代码分割
- **资源加载**: 懒加载和预加载策略
- **内存管理**: 防止内存泄漏和组件销毁
- **网络请求**: API调用优化和缓存策略

## XACE输出格式
1. **XACE性能分析** – 当前瓶颈及量化影响
2. **XACE优化策略** – 系统方法及技术实现
3. **XACE实现计划** – 代码更改及性能影响估算
4. **XACE测量框架** – 基准测试和监控设置
5. **XACE后续行动** – 持续优化和监控要求
6. **XACE性能验证命令**:
   - 后端性能测试: `mvn test -Dtest=PerformanceTest`
   - 前端构建分析: `pnpm build --analyze`
   - 内存分析: `java -XX:+PrintGCDetails -jar app.jar`

## XACE性能优化实践示例

### XACE后端性能优化示例
```java
// ✅ 优化前 - 数据库查询性能问题
@Service
public class UserServiceImpl {
    public List<UserVO> getUserList(UserPagination pagination) {
        // 问题：N+1查询问题
        List<UserEntity> users = baseMapper.selectList(wrapper);
        return users.stream().map(user -> {
            // 每个用户都查询一次部门信息
            DepartmentEntity dept = departmentMapper.selectById(user.getDeptId());
            UserVO vo = BeanCopierUtils.copy(user, UserVO.class);
            vo.setDeptName(dept.getDeptName());
            return vo;
        }).collect(Collectors.toList());
    }
}

// ✅ 优化后 - 使用连表查询或批量查询
@Service
public class UserServiceImpl {
    public PageListVO<UserVO> getUserList(UserPagination pagination) {
        // 解决方案：使用连表查询或先查询所有部门信息
        QueryWrapper<UserEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("DELETE_MARK", 0); // 逻辑删除条件
        
        IPage<UserEntity> page = new Page<>(pagination.getCurrentPage(), pagination.getPageSize());
        IPage<UserEntity> result = baseMapper.selectUserWithDept(page, wrapper);
        
        List<UserVO> list = result.getRecords().stream()
            .map(entity -> BeanCopierUtils.copy(entity, UserVO.class))
            .collect(Collectors.toList());
            
        return PageListVO.create(list, result.getTotal());
    }
}
```

### XACE前端性能优化示例
```vue
<!-- ✅ 优化前 - 性能问题 -->
<template>
  <div v-for="user in users" :key="user.id">
    {{ expensiveCalculation(user) }} <!-- 每次渲染都重新计算 -->
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const users = ref<UserInfo[]>([]);

// 问题：昂贵的计算在每次渲染时都执行
function expensiveCalculation(user: UserInfo) {
  // 复杂计算逻辑
  return someComplexLogic(user);
}
</script>

<!-- ✅ 优化后 - 使用计算属性和缓存 -->
<template>
  <div v-for="user in processedUsers" :key="user.id">
    {{ user.displayName }} <!-- 使用预计算的值 -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';

const users = ref<UserInfo[]>([]);

// 解决方案：使用计算属性缓存结果
const processedUsers = computed(() => {
  return users.value.map(user => ({
    ...user,
    displayName: expensiveCalculation(user) // 只在依赖变化时重新计算
  }));
});

// 使用缓存装饰符进一步优化
const memoizedCalculation = useMemoize(expensiveCalculation);
</script>
```

## XACE性能监控和测量

### XACE后端性能监控
```java
// 使用Spring Boot Actuator和Micrometer
@RestController
public class UserController {
    
    private final MeterRegistry meterRegistry;
    private final Timer.Sample sample;
    
    @GetMapping("/api/users")
    @Timed(name = "user.list.timer", description = "Time taken to return user list")
    public ActionResult<PageListVO<UserVO>> getUserList(UserPagination pagination) {
        Timer.Sample sample = Timer.start(meterRegistry);
        
        try {
            PageListVO<UserVO> result = userService.getList(pagination);
            meterRegistry.counter("user.list.success").increment();
            return ActionResult.success(result);
        } catch (Exception e) {
            meterRegistry.counter("user.list.error").increment();
            throw e;
        } finally {
            sample.stop(Timer.builder("user.list.duration").register(meterRegistry));
        }
    }
}
```

### XACE前端性能监控
```typescript
// 使用Web Vitals监控核心性能指标
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

// 监控XACE应用性能
export function initPerformanceMonitoring() {
  getCLS(console.log);
  getFID(console.log);
  getFCP(console.log);
  getLCP(console.log);
  getTTFB(console.log);
}

// API调用性能监控
export function createPerformanceInterceptor() {
  return {
    request: (config: any) => {
      config.metadata = { startTime: Date.now() };
      return config;
    },
    response: (response: any) => {
      const duration = Date.now() - response.config.metadata.startTime;
      console.log(`API ${response.config.url} took ${duration}ms`);
      return response;
    }
  };
}
```

## XACE性能优化检查清单
### 数据库优化
- [ ] 查询语句优化，避免N+1问题
- [ ] 索引使用情况检查
- [ ] 分页查询优化
- [ ] 连接池配置调优
- [ ] 慢查询日志分析

### 应用层优化
- [ ] 缓存策略实施
- [ ] 批处理操作优化
- [ ] 异步处理实现
- [ ] 线程池配置
- [ ] JVM参数调优

### 前端优化
- [ ] 组件懒加载实现
- [ ] 代码分割配置
- [ ] 图片资源优化
- [ ] CDN使用
- [ ] 缓存策略配置

### 监控和测量
- [ ] 性能指标收集
- [ ] 监控告警设置
- [ ] 负载测试执行
- [ ] 性能基线建立
- [ ] 持续优化流程