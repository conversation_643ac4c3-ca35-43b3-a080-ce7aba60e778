<template>
  <BasicDrawer
    v-bind="$attrs"
    @register="registerDrawer"
    :title="getTitle"
    width="70%"
    showFooter
    :canFullscreen="true"
    :maskClosable="false"
    :keyboard="false"
    class="supplier-form-drawer"
    @ok="handleSubmit">
    <template #footer>
      <a-space>
        <a-button @click="closeDrawer">
          <template #icon><CloseOutlined /></template>
          取消
        </a-button>
        <a-button type="primary" @click="handleSubmit" :loading="loading">
          <template #icon><SaveOutlined /></template>
          {{ isUpdate ? '更新' : '保存' }}
        </a-button>
      </a-space>
    </template>

    <div class="supplier-form-container">
      <!-- 顶部概览卡片 - 仅显示标题 -->
      <a-card :bordered="false" class="overview-card mb-4" v-if="isUpdate">
        <template #title>
          <div class="card-title">
            <EditOutlined class="title-icon" />
            编辑供应商 【{{ supplierInfo?.name || '-' }}】
          </div>
        </template>
        <template #extra>
          <a-space>
            <a-tag color="blue" v-if="supplierInfo?.name">
              <ShopOutlined class="tag-icon" />
              供应商
            </a-tag>
          </a-space>
        </template>

        <div class="overview-content">
          <div class="overview-text">
            <p>正在编辑供应商信息，请在下方表单中修改相关字段。</p>
          </div>
        </div>
      </a-card>

      <!-- 新增供应商的欢迎卡片 -->
      <a-card :bordered="false" class="welcome-card mb-4" v-else>
        <template #title>
          <div class="card-title">
            <PlusOutlined class="title-icon" />
            新增供应商
          </div>
        </template>
        <div class="welcome-content">
          <div class="welcome-text">
            <h3>创建新的供应商</h3>
            <p>请填写完整的供应商信息，包括联系方式和备注信息，以便后续管理和联系。</p>
          </div>
        </div>
      </a-card>

      <!-- 表单区域 -->
      <a-card :bordered="false" class="form-card">
        <template #title>
          <div class="card-title">
            <FormOutlined class="title-icon" />
            供应商信息
          </div>
        </template>
        <div class="form-section">
          <BasicForm @register="registerForm">
            <template #remark="{ model, field }">
              <a-textarea v-model:value="model[field]" placeholder="请输入备注信息" :rows="4" :maxlength="500" show-count class="enhanced-textarea" />
            </template>
          </BasicForm>
        </div>
      </a-card>
    </div>
  </BasicDrawer>
</template>

<script lang="ts" setup>
  import { computed, ref, unref, nextTick } from 'vue';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { BasicForm, useForm } from '/@/components/Form';
  import { FormSchema } from '/@/components/Form/src/types/form';
  import { createSupplier, updateSupplier, getSupplierInfo } from '/@/api/project/supplier';
  import { useMessage } from '/@/hooks/web/useMessage';
  import {
    EditOutlined,
    PlusOutlined,
    SaveOutlined,
    CloseOutlined,
    UserOutlined,
    PhoneOutlined,
    SortAscendingOutlined,
    FileTextOutlined,
    FormOutlined,
    ShopOutlined,
  } from '@ant-design/icons-vue';

  const { createMessage } = useMessage();
  const emit = defineEmits(['register', 'reload']);
  const isUpdate = ref(false);
  const supplierId = ref('');
  const supplierInfo = ref<any>({});
  const loading = ref(false);

  // 表单配置
  const formSchemas: FormSchema[] = [
    {
      field: 'name',
      label: '供应商名称',
      component: 'Input',
      required: true,
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请输入供应商名称',
        size: 'large',
      },
      rules: [{ required: true, trigger: 'blur', message: '请输入供应商名称' }],
    },
    {
      field: 'linkman',
      label: '联系人',
      component: 'Input',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请输入联系人姓名',
        size: 'large',
      },
    },
    {
      field: 'telephone',
      label: '联系电话',
      component: 'Input',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请输入联系电话',
        size: 'large',
      },
    },
    {
      field: 'sortCode',
      label: '排序码',
      component: 'InputNumber',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请输入排序码',
        size: 'large',
        style: { width: '100%' },
        min: 0,
        max: 9999,
      },
    },
    {
      field: 'remark',
      label: '备注信息',
      component: 'Input',
      colProps: { span: 24 },
      slot: 'remark',
    },
  ];

  // 注册表单
  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 120,
    schemas: formSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
  });

  // 注册抽屉
  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async data => {
    try {
      setDrawerProps({ confirmLoading: false, loading: true });

      // 重置状态
      resetFields();
      supplierInfo.value = {};

      isUpdate.value = !!data?.isUpdate;

      if (unref(isUpdate)) {
        supplierId.value = data.record.id;
        supplierInfo.value = data.record;
        setFieldsValue({
          ...data.record,
        });
      }
    } catch (error) {
      console.error('初始化抽屉失败:', error);
      createMessage.error('初始化失败');
    } finally {
      setDrawerProps({ loading: false });
    }
  });

  // 获取标题
  const getTitle = computed(() => {
    return unref(isUpdate) ? '编辑供应商' : '新增供应商';
  });

  // 提交表单
  async function handleSubmit() {
    try {
      const values = await validate();
      loading.value = true;
      setDrawerProps({ confirmLoading: true });

      console.log('提交的表单数据:', values);

      if (unref(isUpdate)) {
        await updateSupplier(supplierId.value, values);
        createMessage.success('供应商信息更新成功');
      } else {
        await createSupplier(values);
        createMessage.success('供应商创建成功');
      }

      closeDrawer();
      emit('reload');
    } catch (error) {
      console.error('表单验证失败或提交出错:', error);
      createMessage.error(`操作失败: ${(error as any)?.message || '未知错误'}`);
    } finally {
      loading.value = false;
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>

<style lang="less" scoped>
  // 响应式设计
  @media (max-width: 1200px) {
    .supplier-form-drawer {
      :deep(.ant-drawer) {
        width: 85% !important;
      }
    }

    .supplier-form-container {
      padding: 16px;
    }

    .overview-content {
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 16px;
    }
  }

  @media (max-width: 768px) {
    .supplier-form-drawer {
      :deep(.ant-drawer) {
        width: 95% !important;
      }
    }

    .supplier-form-container {
      padding: 12px;
    }

    .overview-content {
      grid-template-columns: 1fr;
      gap: 12px;
    }

    .card-title {
      .title-icon {
        font-size: 16px;
      }
    }

    :deep(.ant-form) {
      .ant-form-item {
        margin-bottom: 16px;
      }
    }
  }
  .supplier-form-drawer {
    :deep(.ant-drawer-body) {
      padding: 0;
      background: #f5f7fa;
    }

    :deep(.ant-drawer-header) {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-bottom: none;

      .ant-drawer-title {
        color: #fff;
        font-weight: 600;
        font-size: 16px;
      }

      .ant-drawer-close {
        color: rgba(255, 255, 255, 0.8);

        &:hover {
          color: #fff;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 4px;
        }
      }
    }

    :deep(.ant-drawer-footer) {
      background: #fff;
      border-top: 1px solid #e8eaec;
      padding: 16px 24px;
    }
  }

  .supplier-form-container {
    height: 100%;
    padding: 24px;
    overflow-y: auto;
  }

  .overview-card,
  .welcome-card,
  .form-card {
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid #e8eaec;

    :deep(.ant-card-head) {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-bottom: 1px solid #e8eaec;
      border-radius: 12px 12px 0 0;
      padding: 16px 24px;

      .ant-card-head-title {
        padding: 0;
        font-size: 18px;
        font-weight: 600;
      }
    }

    :deep(.ant-card-body) {
      padding: 24px;
    }
  }

  .overview-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;

    .overview-item {
      .overview-label {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
        font-weight: 500;

        .label-icon {
          margin-right: 6px;
          font-size: 16px;
          color: #1890ff;
        }
      }

      .overview-value {
        font-size: 16px;
        font-weight: 600;
        color: #2c3e50;
        word-break: break-all;
      }
    }
  }

  .welcome-content,
  .overview-content {
    .welcome-text,
    .overview-text {
      text-align: center;
      padding: 20px 0;

      h3 {
        color: #2c3e50;
        margin-bottom: 12px;
        font-size: 20px;
        font-weight: 600;
      }

      p {
        color: #666;
        font-size: 14px;
        line-height: 1.6;
        margin: 0;
      }
    }
  }

  .card-title {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #2c3e50;

    .title-icon {
      font-size: 18px;
      color: #1890ff;
    }
  }

  .form-section {
    background: #fff;
    border-radius: 8px;
    padding: 0;
  }

  .enhanced-textarea {
    border-radius: 8px !important;
    transition: all 0.3s ease;

    &:hover {
      border-color: #40a9ff !important;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1) !important;
    }

    &:focus {
      border-color: #1890ff !important;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
    }
  }

  .mb-4 {
    margin-bottom: 24px;
  }

  .tag-icon {
    margin-right: 4px;
  }

  :deep(.ant-form) {
    .ant-form-item-label > label {
      font-weight: 500;
      color: #2c3e50;
    }

    .ant-input,
    .ant-input-number {
      border-radius: 6px;
      border: 1px solid #d9d9d9;
      transition: all 0.3s ease;

      &:hover {
        border-color: #40a9ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
      }

      &:focus,
      &.ant-input-focused {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }
    }

    .ant-input-number {
      width: 100%;
    }
  }

  // 滚动条美化
  .supplier-form-container {
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }

  // 标签样式
  :deep(.ant-tag) {
    border-radius: 6px;
    padding: 4px 12px;
    font-weight: 500;
    border: none;
  }
</style>
