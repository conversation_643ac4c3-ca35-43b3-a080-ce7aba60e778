package com.xinghuo.project.template.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.template.dao.IssueLibraryMapper;
import com.xinghuo.project.template.entity.IssueLibraryEntity;
import com.xinghuo.project.template.model.IssueLibraryPagination;
import com.xinghuo.project.template.service.IssueLibraryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 标准项目问题库服务实现类
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Slf4j
@Service
public class IssueLibraryServiceImpl extends BaseServiceImpl<IssueLibraryMapper, IssueLibraryEntity> implements IssueLibraryService {

    @Override
    public List<IssueLibraryEntity> getList(IssueLibraryPagination pagination) {
        QueryWrapper<IssueLibraryEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<IssueLibraryEntity> lambda = queryWrapper.lambda();

        // 根据问题编码模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getCode())) {
            lambda.like(IssueLibraryEntity::getCode, pagination.getCode());
        }

        // 根据问题标题模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getTitle())) {
            lambda.like(IssueLibraryEntity::getTitle, pagination.getTitle());
        }

        // 根据状态精确查询
        if (StrXhUtil.isNotEmpty(pagination.getStatus())) {
            lambda.eq(IssueLibraryEntity::getStatus, pagination.getStatus());
        }

        // 根据问题类别查询
        if (StrXhUtil.isNotEmpty(pagination.getIssueCategoryId())) {
            lambda.eq(IssueLibraryEntity::getIssueCategoryId, pagination.getIssueCategoryId());
        }

        // 根据优先级查询
        if (StrXhUtil.isNotEmpty(pagination.getDefaultPriorityId())) {
            lambda.eq(IssueLibraryEntity::getDefaultPriorityId, pagination.getDefaultPriorityId());
        }

        // 创建时间范围查询
        if (pagination.getCreateTimeStart() != null) {
            lambda.ge(IssueLibraryEntity::getCreatedAt, pagination.getCreateTimeStart());
        }
        if (pagination.getCreateTimeEnd() != null) {
            lambda.le(IssueLibraryEntity::getCreatedAt, pagination.getCreateTimeEnd());
        }

        // 根据创建用户查询
        if (StrXhUtil.isNotEmpty(pagination.getCreatedBy())) {
            lambda.eq(IssueLibraryEntity::getCreatedBy, pagination.getCreatedBy());
        }

        // 根据描述关键字查询
        if (StrXhUtil.isNotEmpty(pagination.getDescriptionKeyword())) {
            lambda.like(IssueLibraryEntity::getDescription, pagination.getDescriptionKeyword());
        }

        // 根据解决方案关键字查询
        if (StrXhUtil.isNotEmpty(pagination.getSolutionKeyword())) {
            lambda.like(IssueLibraryEntity::getSuggestedSolution, pagination.getSolutionKeyword());
        }

        // 根据关键字搜索编码或标题
        String keyword = pagination.getKeyword();
        if (StrXhUtil.isNotEmpty(keyword)) {
            lambda.and(wrapper -> wrapper
                    .like(IssueLibraryEntity::getCode, keyword)
                    .or()
                    .like(IssueLibraryEntity::getTitle, keyword)
            );
        }

        // 排序：按创建时间倒序
        lambda.orderByDesc(IssueLibraryEntity::getCreatedAt);
        
        return processDataType(queryWrapper, pagination);
    }

    @Override
    public List<IssueLibraryEntity> getListByStatus(String status) {
        if (StrXhUtil.isEmpty(status)) {
            return new ArrayList<>();
        }

        QueryWrapper<IssueLibraryEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(IssueLibraryEntity::getStatus, status)
                .orderByDesc(IssueLibraryEntity::getCreatedAt);
        
        return list(queryWrapper);
    }

    @Override
    public IssueLibraryEntity getInfo(String id) {
        if (StrXhUtil.isEmpty(id)) {
            return null;
        }
        return getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String create(IssueLibraryEntity entity) {
        if (entity == null) {
            throw new RuntimeException("问题库信息不能为空");
        }

        // 验证必填字段
        if (StrXhUtil.isEmpty(entity.getTitle())) {
            throw new RuntimeException("问题标题不能为空");
        }

        if (StrXhUtil.isEmpty(entity.getIssueCategoryId())) {
            throw new RuntimeException("问题类别不能为空");
        }

        // 检查编码是否重复
        if (StrXhUtil.isNotEmpty(entity.getCode()) && isExistByCode(entity.getCode(), null)) {
            throw new RuntimeException("问题编码已存在");
        }

        // 检查标题是否重复
        if (isExistByTitle(entity.getTitle(), null)) {
            throw new RuntimeException("问题标题已存在");
        }

        // 如果没有提供编码，自动生成
        if (StrXhUtil.isEmpty(entity.getCode())) {
            entity.setCode(generateCode());
        }

        // 设置默认状态
        if (StrXhUtil.isEmpty(entity.getStatus())) {
            entity.setStatus("draft"); // 默认草稿状态
        }

        save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(String id, IssueLibraryEntity entity) {
        if (StrXhUtil.isEmpty(id) || entity == null) {
            throw new RuntimeException("参数不能为空");
        }

        IssueLibraryEntity existEntity = getById(id);
        if (existEntity == null) {
            throw new RuntimeException("问题库不存在");
        }

        // 验证必填字段
        if (StrXhUtil.isEmpty(entity.getTitle())) {
            throw new RuntimeException("问题标题不能为空");
        }

        if (StrXhUtil.isEmpty(entity.getIssueCategoryId())) {
            throw new RuntimeException("问题类别不能为空");
        }

        // 检查编码是否重复
        if (StrXhUtil.isNotEmpty(entity.getCode()) && isExistByCode(entity.getCode(), id)) {
            throw new RuntimeException("问题编码已存在");
        }

        // 检查标题是否重复
        if (isExistByTitle(entity.getTitle(), id)) {
            throw new RuntimeException("问题标题已存在");
        }

        entity.setId(id);
        updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        if (StrXhUtil.isEmpty(id)) {
            throw new RuntimeException("问题库ID不能为空");
        }

        IssueLibraryEntity entity = getById(id);
        if (entity == null) {
            throw new RuntimeException("问题库不存在");
        }

        // TODO: 检查是否被其他模块引用，如果被引用则不允许删除
        // 例如：检查是否被项目问题、问题跟踪等引用

        removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new RuntimeException("问题库ID列表不能为空");
        }

        for (String id : ids) {
            delete(id);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(String id, String status) {
        if (StrXhUtil.isEmpty(id) || StrXhUtil.isEmpty(status)) {
            throw new RuntimeException("参数不能为空");
        }

        IssueLibraryEntity entity = getById(id);
        if (entity == null) {
            throw new RuntimeException("问题库不存在");
        }

        UpdateWrapper<IssueLibraryEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .eq(IssueLibraryEntity::getId, id)
                .set(IssueLibraryEntity::getStatus, status);
        
        update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateStatus(List<String> ids, String status) {
        if (ids == null || ids.isEmpty() || StrXhUtil.isEmpty(status)) {
            throw new RuntimeException("参数不能为空");
        }

        UpdateWrapper<IssueLibraryEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .in(IssueLibraryEntity::getId, ids)
                .set(IssueLibraryEntity::getStatus, status);
        
        update(updateWrapper);
    }

    @Override
    public boolean isExistByCode(String code, String excludeId) {
        if (StrXhUtil.isEmpty(code)) {
            return false;
        }

        QueryWrapper<IssueLibraryEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(IssueLibraryEntity::getCode, code);
        
        if (StrXhUtil.isNotEmpty(excludeId)) {
            queryWrapper.lambda().ne(IssueLibraryEntity::getId, excludeId);
        }

        return count(queryWrapper) > 0;
    }

    @Override
    public boolean isExistByTitle(String title, String excludeId) {
        if (StrXhUtil.isEmpty(title)) {
            return false;
        }

        QueryWrapper<IssueLibraryEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(IssueLibraryEntity::getTitle, title);
        
        if (StrXhUtil.isNotEmpty(excludeId)) {
            queryWrapper.lambda().ne(IssueLibraryEntity::getId, excludeId);
        }

        return count(queryWrapper) > 0;
    }

    @Override
    public IssueLibraryEntity getByCode(String code) {
        if (StrXhUtil.isEmpty(code)) {
            return null;
        }

        QueryWrapper<IssueLibraryEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(IssueLibraryEntity::getCode, code);
        
        return getOne(queryWrapper);
    }

    @Override
    public List<IssueLibraryEntity> getSelectList(String keyword) {
        QueryWrapper<IssueLibraryEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<IssueLibraryEntity> lambda = queryWrapper.lambda();

        // 只查询已发布状态的记录
        lambda.eq(IssueLibraryEntity::getStatus, "published");

        // 根据关键字搜索
        if (StrXhUtil.isNotEmpty(keyword)) {
            lambda.and(wrapper -> wrapper
                    .like(IssueLibraryEntity::getCode, keyword)
                    .or()
                    .like(IssueLibraryEntity::getTitle, keyword)
            );
        }

        // 排序：按标题升序
        lambda.orderByAsc(IssueLibraryEntity::getTitle);
        
        return list(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void publish(String id) {
        updateStatus(id, "published");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void archive(String id) {
        updateStatus(id, "archived");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchPublish(List<String> ids) {
        batchUpdateStatus(ids, "published");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchArchive(List<String> ids) {
        batchUpdateStatus(ids, "archived");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String copy(String id, String newTitle) {
        if (StrXhUtil.isEmpty(id) || StrXhUtil.isEmpty(newTitle)) {
            throw new RuntimeException("参数不能为空");
        }

        IssueLibraryEntity sourceEntity = getById(id);
        if (sourceEntity == null) {
            throw new RuntimeException("源问题库不存在");
        }

        // 检查新标题是否重复
        if (isExistByTitle(newTitle, null)) {
            throw new RuntimeException("问题标题已存在");
        }

        // 创建新的问题库
        IssueLibraryEntity newEntity = new IssueLibraryEntity();
        newEntity.setTitle(newTitle);
        newEntity.setCode(generateCode()); // 生成新的编码
        newEntity.setDescription(sourceEntity.getDescription());
        newEntity.setStatus("draft"); // 复制的问题库默认为草稿状态
        newEntity.setIssueCategoryId(sourceEntity.getIssueCategoryId());
        newEntity.setDefaultPriorityId(sourceEntity.getDefaultPriorityId());
        newEntity.setSuggestedSolution(sourceEntity.getSuggestedSolution());

        save(newEntity);
        return newEntity.getId();
    }

    @Override
    public String generateCode() {
        String prefix = "ISSUE";
        String randomStr = RandomUtil.enUuId().substring(0, 8).toUpperCase();
        String code = prefix + randomStr;

        // 确保编码不重复
        while (isExistByCode(code, null)) {
            randomStr = RandomUtil.enUuId().substring(0, 8).toUpperCase();
            code = prefix + randomStr;
        }

        return code;
    }

    @Override
    public Map<String, Object> getIssueLibraryUsageInfo(String id) {
        Map<String, Object> result = new HashMap<>();
        
        if (StrXhUtil.isEmpty(id)) {
            result.put("total", 0);
            result.put("details", new ArrayList<>());
            return result;
        }

        // TODO: 实现问题库使用情况统计
        // 统计在以下模块中的使用情况：
        // 1. 项目问题登记表
        // 2. 问题跟踪
        // 3. 其他相关模块

        result.put("total", 0);
        result.put("details", new ArrayList<>());
        
        return result;
    }

    @Override
    public List<IssueLibraryEntity> getListByIssueCategory(String issueCategoryId) {
        if (StrXhUtil.isEmpty(issueCategoryId)) {
            return new ArrayList<>();
        }

        QueryWrapper<IssueLibraryEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(IssueLibraryEntity::getIssueCategoryId, issueCategoryId)
                .eq(IssueLibraryEntity::getStatus, "published") // 只查询已发布状态
                .orderByAsc(IssueLibraryEntity::getTitle);
        
        return list(queryWrapper);
    }

    @Override
    public List<IssueLibraryEntity> getListByPriority(String priorityId) {
        if (StrXhUtil.isEmpty(priorityId)) {
            return new ArrayList<>();
        }

        QueryWrapper<IssueLibraryEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(IssueLibraryEntity::getDefaultPriorityId, priorityId)
                .eq(IssueLibraryEntity::getStatus, "published") // 只查询已发布状态
                .orderByAsc(IssueLibraryEntity::getTitle);
        
        return list(queryWrapper);
    }
}
