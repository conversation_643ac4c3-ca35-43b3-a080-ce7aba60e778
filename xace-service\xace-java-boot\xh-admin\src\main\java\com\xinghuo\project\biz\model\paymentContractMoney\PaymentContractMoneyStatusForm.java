package com.xinghuo.project.biz.model.paymentContractMoney;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.Date;

/**
 * 采购付款计划状态更新表单对象
 */
@Data
@Schema(description = "采购付款计划状态更新表单对象")
public class PaymentContractMoneyStatusForm {

    /**
     * 付款状态 1-已付款，0-未付款
     */
    @NotBlank(message = "付款状态不能为空")
    @Schema(description = "付款状态 1-已付款，0-未付款")
    private String payStatus;

    /**
     * 付款日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "付款日期")
    private Date fukuanDate;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @Schema(description = "备注")
    private String lastNote;
}
