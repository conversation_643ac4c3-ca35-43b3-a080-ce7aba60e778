package com.xinghuo.project.core.service;

import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.project.core.entity.ProjBehaviorLogEntity;

/**
 * 用户行为服务接口
 * 
 * 提供用户行为记录的通用服务，包括访问记录和关注状态管理。
 * 这是一个内部服务，供其他业务模块调用，不直接暴露为RESTful API。
 * 
 * <AUTHOR>
 * @date 2025-01-08
 */
public interface UserBehaviorService extends BaseService<ProjBehaviorLogEntity> {

    /**
     * 记录一次访问行为
     * 
     * 这是一个异步方法，用于记录用户对指定对象的访问行为。
     * 如果已存在相同的访问记录，则更新最后访问时间；否则创建新记录。
     * 
     * @param userId     当前登录用户ID
     * @param targetId   被访问对象ID
     * @param targetType 被访问对象类型 (如: PROJECT, PROGRAM, PORTFOLIO)
     */
    void logVisit(String userId, String targetId, String targetType);

    /**
     * 切换对象的关注状态
     * 
     * 根据目标关注状态，添加或取消用户对指定对象的关注。
     * 使用 is_valid 字段实现软删除，而不是物理删除记录。
     * 
     * @param userId      当前登录用户ID
     * @param targetId    被操作对象ID
     * @param targetType  被操作对象类型 (如: PROJECT, PROGRAM, PORTFOLIO)
     * @param isFavorite  目标关注状态 (true: 关注, false: 取消关注)
     */
    void toggleFavorite(String userId, String targetId, String targetType, boolean isFavorite);

    /**
     * 检查用户是否已关注指定对象
     *
     * @param userId     用户ID
     * @param targetId   对象ID
     * @param targetType 对象类型
     * @return true: 已关注, false: 未关注
     */
    boolean isFavorite(String userId, String targetId, String targetType);

    /**
     * 获取用户最近访问的对象列表
     *
     * @param userId     用户ID
     * @param targetType 对象类型 (可选，为null时返回所有类型)
     * @param limit      返回数量限制
     * @return 最近访问记录列表
     */
    java.util.List<ProjBehaviorLogEntity> getRecentVisits(String userId, String targetType, int limit);

    /**
     * 批量检查关注状态
     *
     * @param userId  用户ID
     * @param targets 目标对象列表 (包含 targetId 和 targetType)
     * @return 关注状态映射 (targetId -> isFavorite)
     */
    java.util.Map<String, Boolean> batchCheckFavoriteStatus(String userId, java.util.List<java.util.Map<String, String>> targets);
}
