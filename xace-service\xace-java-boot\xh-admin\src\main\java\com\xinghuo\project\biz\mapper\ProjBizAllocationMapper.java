package com.xinghuo.project.biz.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xinghuo.common.base.dao.XHBaseMapper;
import com.xinghuo.project.biz.entity.ProjBizAllocationEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 项目业务分部分配 Mapper接口
 * 
 * <AUTHOR>
 * @version V3.0
 * @copyright 易趋集团
 * @date 2024-01-01
 */
@Mapper
public interface ProjBizAllocationMapper extends XHBaseMapper<ProjBizAllocationEntity> {

    /**
     * 根据业务ID和业务类型查询分配信息
     */
    default ProjBizAllocationEntity selectByBusinessIdAndType(String businessId, Integer businessType) {
        QueryWrapper<ProjBizAllocationEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("BUSINESS_ID", businessId)
               .eq("BUSINESS_TYPE", businessType);
        return selectOne(wrapper);
    }

    /**
     * 根据商机ID查询分配信息
     */
    default ProjBizAllocationEntity selectByOpportunityId(String opportunityId) {
        QueryWrapper<ProjBizAllocationEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("OPPORTUNITY_ID", opportunityId)
               .eq("BUSINESS_TYPE", 1);
        return selectOne(wrapper);
    }

    /**
     * 根据合同ID查询分配信息
     */
    default ProjBizAllocationEntity selectByContractId(String contractId) {
        QueryWrapper<ProjBizAllocationEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("CONTRACT_ID", contractId)
               .eq("BUSINESS_TYPE", 2);
        return selectOne(wrapper);
    }

    /**
     * 根据项目基础ID查询所有相关分配
     */
    default List<ProjBizAllocationEntity> selectByProjBaseId(String projBaseId) {
        QueryWrapper<ProjBizAllocationEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("PROJ_BASE_ID", projBaseId)
               .orderByAsc("BUSINESS_TYPE");
        return selectList(wrapper);
    }

    /**
     * 查询项目全链路分部分配汇总
     */
    @Select("SELECT " +
            "BUSINESS_TYPE, " +
            "COUNT(*) as count, " +
            "SUM(YB_AMOUNT) as yb_total, " +
            "SUM(EB_AMOUNT) as eb_total, " +
            "SUM(JF_AMOUNT) as jf_total, " +
            "SUM(OTHER_AMOUNT) as other_total, " +
            "SUM(OUT_YB_AMOUNT) as out_yb_total, " +
            "SUM(OUT_EB_AMOUNT) as out_eb_total, " +
            "SUM(OUT_JF_AMOUNT) as out_jf_total, " +
            "SUM(OUT_OTHER_AMOUNT) as out_other_total " +
            "FROM proj_biz_allocation " +
            "WHERE PROJ_BASE_ID = #{projBaseId} " +
            "GROUP BY BUSINESS_TYPE " +
            "ORDER BY BUSINESS_TYPE")
    List<Map<String, Object>> selectAllocationSummaryByProjId(@Param("projBaseId") String projBaseId);

    /**
     * 查询分部营收汇总（按分部统计）
     */
    @Select("SELECT " +
            "'YB' as dept_code, '一部' as dept_name, " +
            "SUM(YB_AMOUNT) as amount, SUM(OUT_YB_AMOUNT) as out_amount " +
            "FROM proj_biz_allocation WHERE BUSINESS_TYPE = #{businessType} " +
            "UNION ALL " +
            "SELECT " +
            "'EB' as dept_code, '二部' as dept_name, " +
            "SUM(EB_AMOUNT) as amount, SUM(OUT_EB_AMOUNT) as out_amount " +
            "FROM proj_biz_allocation WHERE BUSINESS_TYPE = #{businessType} " +
            "UNION ALL " +
            "SELECT " +
            "'JF' as dept_code, '交付' as dept_name, " +
            "SUM(JF_AMOUNT) as amount, SUM(OUT_JF_AMOUNT) as out_amount " +
            "FROM proj_biz_allocation WHERE BUSINESS_TYPE = #{businessType} " +
            "UNION ALL " +
            "SELECT " +
            "'OTHER' as dept_code, '综合' as dept_name, " +
            "SUM(OTHER_AMOUNT) as amount, SUM(OUT_OTHER_AMOUNT) as out_amount " +
            "FROM proj_biz_allocation WHERE BUSINESS_TYPE = #{businessType}")
    List<Map<String, Object>> selectDeptSummaryByBusinessType(@Param("businessType") Integer businessType);

    /**
     * 查询交付部门的分配汇总
     */
    @Select("SELECT " +
            "BUSINESS_TYPE, " +
            "SUM(JF_AMOUNT) as jf_total_amount, " +
            "SUM(OUT_JF_AMOUNT) as jf_total_out_amount, " +
            "COUNT(*) as record_count " +
            "FROM proj_biz_allocation " +
            "WHERE (JF_AMOUNT > 0 OR OUT_JF_AMOUNT > 0) " +
            "GROUP BY BUSINESS_TYPE " +
            "ORDER BY BUSINESS_TYPE")
    List<Map<String, Object>> selectJfDeptSummary();

    /**
     * 根据业务类型分页查询
     */
    default IPage<ProjBizAllocationEntity> selectPageByBusinessType(IPage<ProjBizAllocationEntity> page, 
                                                                    Integer businessType) {
        QueryWrapper<ProjBizAllocationEntity> wrapper = new QueryWrapper<>();
        if (businessType != null) {
            wrapper.eq("BUSINESS_TYPE", businessType);
        }
        wrapper.orderByDesc("F_CREATED_AT");
        return selectPage(page, wrapper);
    }

    /**
     * 检查业务是否已存在分配记录
     */
    @Select("SELECT COUNT(*) FROM proj_biz_allocation " +
            "WHERE BUSINESS_ID = #{businessId} AND BUSINESS_TYPE = #{businessType}")
    int countByBusinessIdAndType(@Param("businessId") String businessId, 
                                @Param("businessType") Integer businessType);

    /**
     * 批量查询多个业务的分配信息
     */
    default List<ProjBizAllocationEntity> selectByBusinessIds(List<String> businessIds, Integer businessType) {
        QueryWrapper<ProjBizAllocationEntity> wrapper = new QueryWrapper<>();
        wrapper.in("BUSINESS_ID", businessIds)
               .eq("BUSINESS_TYPE", businessType);
        return selectList(wrapper);
    }

    /**
     * 查询指定分部的总分配金额
     */
    @Select("SELECT " +
            "SUM(CASE WHEN #{deptCode} = 'YB' THEN YB_AMOUNT " +
            "         WHEN #{deptCode} = 'EB' THEN EB_AMOUNT " +
            "         WHEN #{deptCode} = 'JF' THEN JF_AMOUNT " +
            "         WHEN #{deptCode} = 'OTHER' THEN OTHER_AMOUNT " +
            "         ELSE 0 END) as total_amount, " +
            "SUM(CASE WHEN #{deptCode} = 'YB' THEN OUT_YB_AMOUNT " +
            "         WHEN #{deptCode} = 'EB' THEN OUT_EB_AMOUNT " +
            "         WHEN #{deptCode} = 'JF' THEN OUT_JF_AMOUNT " +
            "         WHEN #{deptCode} = 'OTHER' THEN OUT_OTHER_AMOUNT " +
            "         ELSE 0 END) as total_out_amount " +
            "FROM proj_biz_allocation " +
            "WHERE BUSINESS_TYPE = #{businessType}")
    Map<String, BigDecimal> selectDeptTotalAmount(@Param("deptCode") String deptCode, 
                                                  @Param("businessType") Integer businessType);

    /**
     * 删除指定业务的分配记录
     */
    default int deleteByBusinessIdAndType(String businessId, Integer businessType) {
        QueryWrapper<ProjBizAllocationEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("BUSINESS_ID", businessId)
               .eq("BUSINESS_TYPE", businessType);
        return delete(wrapper);
    }
}