<template>
  <PageWrapper dense contentFullHeight fixedHeight contentClass="flex">
    <BasicTable @register="registerTable" :searchInfo="searchInfo">
      <template #toolbar>
        <a-button type="primary" @click="handleCreate"> 新增商机周报 </a-button>
        <a-button @click="handleBatchShowStatus(1)"> 批量显示 </a-button>
        <a-button @click="handleBatchShowStatus(0)"> 批量隐藏 </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'clarity:info-standard-line',
                label: '查看详情',
                onClick: handleView.bind(null, record),
              },
              {
                icon: 'clarity:note-edit-line',
                label: '编辑',
                onClick: handleEdit.bind(null, record),
                ifShow: () => record.status !== 3, // 已发布状态不能编辑
              },
              {
                icon: 'ant-design:audit-outlined',
                label: '提交审核',
                onClick: handleSubmitAudit.bind(null, record),
                ifShow: () => record.status === 1, // 只有已填写状态可以提交审核
              },
              {
                icon: 'ant-design:check-circle-outlined',
                label: '审核',
                onClick: handleAudit.bind(null, record),
                ifShow: () => record.status === 2, // 只有提交审核状态可以审核
              },
              {
                icon: 'ant-design:history-outlined',
                label: '查看历史',
                onClick: handleViewHistory.bind(null, record),
              },
              {
                icon: 'ant-design:delete-outlined',
                color: 'error',
                label: '删除',
                popConfirm: {
                  title: '是否确认删除',
                  placement: 'left',
                  confirm: handleDelete.bind(null, record),
                },
                ifShow: () => record.status !== 3, // 已发布状态不能删除
              },
            ]" />
        </template>
      </template>
    </BasicTable>
    <BusinessWeeklogEdit @register="registerModal" @success="handleSuccess" />
    <DetailDrawer @register="registerDetailModal" />
    <AuditDrawer @register="registerAuditModal" @success="handleSuccess" />
    <HistoryDrawer @register="registerHistoryModal" />
  </PageWrapper>
</template>

<script lang="ts" setup>
  import { reactive } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { PageWrapper } from '/@/components/Page';
  import { useDrawer } from '/@/components/Drawer';
  import { useMessage } from '/@/hooks/web/useMessage';

  import BusinessWeeklogEdit from './BusinessWeeklogEdit.vue';
  import DetailDrawer from './DetailDrawer.vue';
  import AuditDrawer from './AuditDrawer.vue';
  import HistoryDrawer from './HistoryDrawer.vue';

  import { columns, searchFormSchema } from './businessWeeklog.data';
  import {
    getBusinessWeeklogList,
    deleteBusinessWeeklog,
    submitBusinessWeeklogForAudit,
    batchUpdateBusinessWeeklogShowStatus,
  } from '/@/api/project/biz/businessWeeklog';

  defineOptions({ name: 'project-biz-businessWeeklog' });

  const { createMessage } = useMessage();
  const [registerModal, { openDrawer }] = useDrawer();
  const [registerDetailModal, { openDrawer: openDetailDrawer }] = useDrawer();
  const [registerAuditModal, { openDrawer: openAuditDrawer }] = useDrawer();
  const [registerHistoryModal, { openDrawer: openHistoryDrawer }] = useDrawer();

  const searchInfo = reactive<Recordable>({});

  const [registerTable, { reload, getSelectRows, getDataSource }] = useTable({
    title: '商机周报列表',
    api: getBusinessWeeklogList,
    rowKey: 'id',
    columns,
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
    },
    useSearchForm: true,
    showTableSetting: true,
    bordered: true,
    handleSearchInfoFn(info) {
      // 处理日期范围查询
      if (info.dateRange && info.dateRange.length === 2) {
        info.startDateBegin = info.dateRange[0];
        info.startDateEnd = info.dateRange[1];
        delete info.dateRange;
      }
      return info;
    },
    actionColumn: {
      width: 200,
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
    },
  });

  function handleCreate() {
    openDrawer(true, {
      isUpdate: false,
    });
  }

  function handleEdit(record: Recordable) {
    const dataSource = getDataSource();
    const currentIndex = dataSource.findIndex(item => item.id === record.id);

    openDrawer(true, {
      record,
      isUpdate: true,
      recordList: dataSource,
      currentIndex,
    });
  }

  function handleView(record: Recordable) {
    openDetailDrawer(true, { record });
  }

  async function handleDelete(record: Recordable) {
    try {
      await deleteBusinessWeeklog(record.id);
      createMessage.success('删除成功');
      reload();
    } catch (error) {
      createMessage.error('删除失败');
    }
  }

  async function handleSubmitAudit(record: Recordable) {
    try {
      await submitBusinessWeeklogForAudit(record.id);
      createMessage.success('提交审核成功');
      reload();
    } catch (error) {
      createMessage.error('提交审核失败');
    }
  }

  function handleAudit(record: Recordable) {
    openAuditDrawer(true, { record });
  }

  function handleViewHistory(record: Recordable) {
    openHistoryDrawer(true, { record });
  }

  async function handleBatchShowStatus(showStatus: number) {
    const rows = getSelectRows();
    if (rows.length === 0) {
      createMessage.warning('请选择要操作的数据');
      return;
    }

    try {
      const ids = rows.map(row => row.id);
      await batchUpdateBusinessWeeklogShowStatus(ids, showStatus);
      createMessage.success(`批量${showStatus === 1 ? '显示' : '隐藏'}成功`);
      reload();
    } catch (error) {
      createMessage.error(`批量${showStatus === 1 ? '显示' : '隐藏'}失败`);
    }
  }

  function handleSuccess() {
    reload();
  }
</script>
