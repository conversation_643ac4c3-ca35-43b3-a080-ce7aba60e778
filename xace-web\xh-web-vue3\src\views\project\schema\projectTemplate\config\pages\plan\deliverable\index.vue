<template>
  <div class="deliverable-manager">
    <a-card title="交付物管理" :bordered="false">
      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleAdd">
            <Icon icon="ant-design:plus-outlined" />
            新增交付物
          </a-button>
          <a-button @click="handleImportFromWbs">
            <Icon icon="ant-design:import-outlined" />
            从WBS模板导入
          </a-button>
        </a-space>
      </template>

      <BasicTable @register="registerTable" :searchInfo="searchInfo">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <TableAction
              :actions="[
                {
                  icon: 'clarity:note-edit-line',
                  label: '编辑',
                  onClick: handleEdit.bind(null, record),
                },
                {
                  icon: 'ant-design:download-outlined',
                  label: '下载模板',
                  onClick: handleDownloadTemplate.bind(null, record),
                  ifShow: () => record.templateFile,
                },
                {
                  icon: 'ant-design:delete-outlined',
                  color: 'error',
                  label: '删除',
                  popConfirm: {
                    title: '是否确认删除',
                    placement: 'left',
                    confirm: handleDelete.bind(null, record),
                  },
                },
              ]" />
          </template>
          <template v-else-if="column.key === 'phaseName'">
            <a-tag color="blue">{{ record.phaseName || '-' }}</a-tag>
          </template>
          <template v-else-if="column.key === 'responsibleRole'">
            <a-tag>{{ getRoleLabel(record.responsibleRole) }}</a-tag>
          </template>
          <template v-else-if="column.key === 'templateFile'">
            <span v-if="record.templateFile">
              <Icon icon="ant-design:file-outlined" />
              已上传
            </span>
            <span v-else class="text-gray-400">未上传</span>
          </template>
        </template>
      </BasicTable>
    </a-card>

    <!-- 交付物编辑抽屉 -->
    <BasicDrawer @register="registerDrawer" :title="drawerTitle" width="800px" @ok="handleSubmit">
      <BasicForm @register="registerForm" />
    </BasicDrawer>

    <!-- 从WBS模板导入模态框 -->
    <BasicModal @register="registerImportModal" title="从WBS模板导入" width="800px" @ok="handleImportSubmit">
      <WbsTemplateSelector @register="registerWbsSelector" />
    </BasicModal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed, onMounted } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { BasicDrawer, useDrawer } from '/@/components/Drawer';
  import { BasicModal, useModal } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form';
  import { Icon } from '/@/components/Icon';
  import { useMessage } from '/@/hooks/web/useMessage';
  import WbsTemplateSelector from './components/WbsTemplateSelector.vue';
  import {
    getProjectTemplateWbsConfigs,
    saveProjectTemplateWbsConfigs,
    deleteProjectTemplateWbsConfigs,
    importFromWbsTemplate,
    getProjectTemplatePhaseConfigs,
  } from '/@/api/project/projectTemplate';
  import { deliverableFormSchema } from '../../../formSchemas';

  const props = defineProps({
    templateId: {
      type: String,
      required: true,
    },
  });

  const emit = defineEmits(['success']);

  const { createMessage } = useMessage();

  // 搜索信息
  const searchInfo = reactive<Recordable>({});

  // 阶段选项
  const phaseOptions = ref([]);

  // 表格列配置
  const columns = [
    {
      title: '交付物名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
    },
    {
      title: '交付物编码',
      dataIndex: 'code',
      key: 'code',
      width: 120,
    },
    {
      title: '所属阶段',
      dataIndex: 'phaseName',
      key: 'phaseName',
      width: 120,
    },
    {
      title: '负责角色',
      dataIndex: 'responsibleRole',
      key: 'responsibleRole',
      width: 120,
    },
    {
      title: '模板文件',
      dataIndex: 'templateFile',
      key: 'templateFile',
      width: 100,
    },
    {
      title: '排序',
      dataIndex: 'sortOrder',
      key: 'sortOrder',
      width: 80,
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
  ];

  // 表格配置
  const [registerTable, { reload, getDataSource }] = useTable({
    api: loadDeliverableData,
    columns,
    useSearchForm: false,
    actionColumn: {
      width: 150,
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      fixed: 'right',
    },
    rowKey: 'id',
    pagination: false,
  });

  // 抽屉配置
  const [registerDrawer, { openDrawer, closeDrawer }] = useDrawer();

  // 表单配置
  const [registerForm, { setFieldsValue, resetFields, validate, updateSchema }] = useForm({
    labelWidth: 120,
    schemas: deliverableFormSchema,
    showActionButtonGroup: false,
  });

  // 导入模态框配置
  const [registerImportModal, { openModal: openImportModal }] = useModal();
  const [registerWbsSelector, { getSelectedWbs }] = useComponentRegister();

  // 编辑状态
  const isEdit = ref(false);
  const currentRecord = ref<any>(null);

  const drawerTitle = computed(() => {
    return isEdit.value ? '编辑交付物' : '新增交付物';
  });

  // 角色标签映射
  const roleLabels = {
    PM: '项目经理',
    TECH_LEAD: '技术负责人',
    DEVELOPER: '开发工程师',
    TESTER: '测试工程师',
    PRODUCT_MANAGER: '产品经理',
    DESIGNER: '设计师',
  };

  function getRoleLabel(role: string) {
    return roleLabels[role] || role;
  }

  // 加载交付物数据
  async function loadDeliverableData() {
    if (!props.templateId) return { list: [] };

    try {
      const response = await getProjectTemplateWbsConfigs(props.templateId);
      if (response.code === 200) {
        return { list: response.data || [] };
      }
    } catch (error) {
      console.error('加载交付物数据失败:', error);
    }
    return { list: [] };
  }

  // 加载阶段选项
  async function loadPhaseOptions() {
    if (!props.templateId) return;

    try {
      const response = await getProjectTemplatePhaseConfigs(props.templateId);
      if (response.code === 200) {
        const phases = response.data || [];
        phaseOptions.value = phases.map(phase => ({
          label: phase.name,
          value: phase.id,
        }));

        // 更新表单schema中的阶段选项
        updateSchema({
          field: 'phaseId',
          componentProps: {
            options: phaseOptions.value,
          },
        });
      }
    } catch (error) {
      console.error('加载阶段选项失败:', error);
    }
  }

  // 新增交付物
  function handleAdd() {
    isEdit.value = false;
    currentRecord.value = null;
    resetFields();
    openDrawer(true);
  }

  // 编辑交付物
  function handleEdit(record: any) {
    isEdit.value = true;
    currentRecord.value = record;
    setFieldsValue(record);
    openDrawer(true);
  }

  // 删除交付物
  async function handleDelete(record: any) {
    try {
      const response = await deleteProjectTemplateWbsConfigs([record.id]);
      if (response.code === 200) {
        createMessage.success('删除成功');
        reload();
        emit('success');
      } else {
        createMessage.error(response.msg || '删除失败');
      }
    } catch (error) {
      console.error('删除失败:', error);
      createMessage.error('删除失败');
    }
  }

  // 下载模板文件
  function handleDownloadTemplate(record: any) {
    if (record.templateFile) {
      // 下载文件逻辑
      window.open(record.templateFile, '_blank');
    }
  }

  // 提交表单
  async function handleSubmit() {
    try {
      const values = await validate();
      const dataSource = getDataSource();

      let newDataSource;
      if (isEdit.value && currentRecord.value) {
        // 编辑模式
        newDataSource = dataSource.map(item => (item.id === currentRecord.value.id ? { ...item, ...values } : item));
      } else {
        // 新增模式
        const newDeliverable = {
          id: Date.now().toString(), // 临时ID
          templateId: props.templateId,
          ...values,
        };
        newDataSource = [...dataSource, newDeliverable];
      }

      const response = await saveProjectTemplateWbsConfigs(props.templateId, newDataSource);
      if (response.code === 200) {
        createMessage.success(isEdit.value ? '编辑成功' : '新增成功');
        closeDrawer();
        reload();
        emit('success');
      } else {
        createMessage.error(response.msg || '保存失败');
      }
    } catch (error) {
      console.error('保存失败:', error);
      createMessage.error('保存失败');
    }
  }

  // 从WBS模板导入
  function handleImportFromWbs() {
    openImportModal(true);
  }

  // 导入提交
  async function handleImportSubmit() {
    try {
      const selectedWbs = await getSelectedWbs();
      if (!selectedWbs || selectedWbs.length === 0) {
        createMessage.warning('请选择要导入的WBS模板');
        return;
      }

      const response = await importFromWbsTemplate(props.templateId, selectedWbs[0].id);

      if (response.code === 200) {
        createMessage.success('导入成功');
        reload();
        emit('success');
      } else {
        createMessage.error(response.msg || '导入失败');
      }
    } catch (error) {
      console.error('导入失败:', error);
      createMessage.error('导入失败');
    }
  }

  // 临时的组件注册器（需要根据实际组件调整）
  function useComponentRegister() {
    const getSelectedWbs = async () => {
      // 这里需要根据实际的WbsTemplateSelector组件实现
      return [];
    };

    return [() => {}, { getSelectedWbs }];
  }

  onMounted(() => {
    loadPhaseOptions();
    reload();
  });
</script>

<style lang="less" scoped>
  .deliverable-manager {
    padding: 16px;

    :deep(.ant-card) {
      .ant-card-head {
        border-bottom: 1px solid #f0f0f0;
      }

      .ant-card-body {
        padding: 24px;
      }
    }

    .text-gray-400 {
      color: #9ca3af;
    }
  }
</style>
