<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinghuo.project.biz.dao.CustomerContactMapper">

    <!-- 基础字段映射 -->
    <sql id="Base_Column_List">
        cc.id,
        cc.cu_id as cuId,
        cc.linkman,
        cc.telephone,
        cc.status,
        cc.topic,
        cc.content,
        cc.note,
        cc.creator_user_id as creatorUserId,
        cc.creator_time as creatorTime,
        cc.last_modify_user_id as lastModifyUserId,
        cc.last_modify_time as lastModifyTime,
        cc.f_tenantid as tenantId,
        cc.f_flowid as flowId
    </sql>

    <!-- 包含客户名称的字段映射 -->
    <sql id="Base_Column_List_With_Customer_Name">
        <include refid="Base_Column_List"/>,
        bc.name as customerName
    </sql>

    <!-- 查询客户联系人列表（包含客户名称） -->
    <select id="selectListWithCustomerName" resultType="com.xinghuo.project.biz.entity.CustomerContactEntity">
        SELECT
        <include refid="Base_Column_List_With_Customer_Name"/>
        FROM zz_proj_customer_linkman cc
        LEFT JOIN zz_proj_customer bc ON cc.cu_id = bc.id
        <where>
            <if test="customerId != null and customerId != ''">
                AND cc.cu_id = #{customerId}
            </if>
            <if test="status != null">
                AND cc.status = #{status}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (
                    cc.linkman LIKE CONCAT('%', #{keyword}, '%')
                    OR cc.telephone LIKE CONCAT('%', #{keyword}, '%')
                    OR bc.name LIKE CONCAT('%', #{keyword}, '%')
                )
            </if>
        </where>
        ORDER BY cc.creator_time DESC
    </select>

    <!-- 根据客户ID查询联系人列表（包含客户名称） -->
    <select id="selectByCustomerIdWithCustomerName" resultType="com.xinghuo.project.biz.entity.CustomerContactEntity">
        SELECT
        <include refid="Base_Column_List_With_Customer_Name"/>
        FROM zz_proj_customer_linkman cc
        LEFT JOIN zz_proj_customer bc ON cc.cu_id = bc.id
        WHERE cc.cu_id = #{customerId}
        ORDER BY cc.creator_time DESC
    </select>

    <!-- 根据客户ID和关键字查询联系人列表（包含客户名称） -->
    <select id="selectByCustomerIdAndKeywordWithCustomerName" resultType="com.xinghuo.project.biz.entity.CustomerContactEntity">
        SELECT
        <include refid="Base_Column_List_With_Customer_Name"/>
        FROM zz_proj_customer_linkman cc
        LEFT JOIN zz_proj_customer bc ON cc.cu_id = bc.id
        <where>
            <if test="customerId != null and customerId != ''">
                AND cc.cu_id = #{customerId}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (
                    cc.linkman LIKE CONCAT('%', #{keyword}, '%')
                    OR cc.telephone LIKE CONCAT('%', #{keyword}, '%')
                    OR bc.name LIKE CONCAT('%', #{keyword}, '%')
                )
            </if>
        </where>
        ORDER BY cc.creator_time DESC
    </select>

</mapper>
