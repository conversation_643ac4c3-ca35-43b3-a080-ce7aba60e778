package com.xinghuo.project.schema.model;

import com.xinghuo.common.base.model.Pagination;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 项目模板阶段配置分页查询参数
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "项目模板阶段配置分页查询参数")
public class ProjectSchemaPhasePagination extends Pagination {

    /**
     * 项目模板ID
     */
    @Schema(description = "项目模板ID")
    private String projectTemplateId;

    /**
     * 阶段库ID
     */
    @Schema(description = "阶段库ID")
    private String phaseLibraryId;

    /**
     * 序号最小值
     */
    @Schema(description = "序号最小值")
    private Integer seqNoMin;

    /**
     * 序号最大值
     */
    @Schema(description = "序号最大值")
    private Integer seqNoMax;

    /**
     * 工期最小值
     */
    @Schema(description = "工期最小值（天）")
    private Integer durationMin;

    /**
     * 工期最大值
     */
    @Schema(description = "工期最大值（天）")
    private Integer durationMax;

    /**
     * 权重最小值
     */
    @Schema(description = "权重最小值（%）")
    private Integer completionWeightMin;

    /**
     * 权重最大值
     */
    @Schema(description = "权重最大值（%）")
    private Integer completionWeightMax;

    /**
     * 是否可裁剪
     */
    @Schema(description = "是否可裁剪（1:是, 0:否）")
    private Integer canCut;

    /**
     * 审批流程ID
     */
    @Schema(description = "审批流程ID")
    private String approvalId;

    /**
     * 检查单模板ID
     */
    @Schema(description = "检查单模板ID")
    private String checklistId;

    /**
     * 交付物模板ID
     */
    @Schema(description = "交付物模板ID")
    private String workproductTplId;

    /**
     * 创建时间开始
     */
    @Schema(description = "创建时间开始")
    private LocalDateTime createTimeStart;

    /**
     * 创建时间结束
     */
    @Schema(description = "创建时间结束")
    private LocalDateTime createTimeEnd;

    /**
     * 更新时间开始
     */
    @Schema(description = "更新时间开始")
    private LocalDateTime updateTimeStart;

    /**
     * 更新时间结束
     */
    @Schema(description = "更新时间结束")
    private LocalDateTime updateTimeEnd;

    /**
     * 关键字搜索（阶段名称、编码等）
     */
    @Schema(description = "关键字搜索")
    private String keyword;
}
