package com.xinghuo.project.biz.model.bizBusinessWeeklog;

import com.xinghuo.common.base.model.Pagination;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 商机周报分页查询参数
 * 
 * <AUTHOR>
 * @date 2025-07-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "商机周报分页查询参数")
public class BizBusinessWeeklogPagination extends Pagination {

    /**
     * 所属分部
     */
    @Schema(description = "所属分部")
    private String fbId;

    /**
     * 项目类型
     */
    @Schema(description = "项目类型")
    private String projType;

    /**
     * 项目ID
     */
    @Schema(description = "项目ID")
    private String projId;

    /**
     * 项目名称（模糊查询）
     */
    @Schema(description = "项目名称")
    private String projName;

    /**
     * 项目级别
     */
    @Schema(description = "项目级别")
    private String projectLevel;

    /**
     * 负责人ID
     */
    @Schema(description = "负责人ID")
    private String ownId;

    /**
     * 状态 (1-表示已填写，0-未填写，2-提交审核，3-已发布，-1-已驳回)
     */
    @Schema(description = "状态")
    private Integer status;

    /**
     * 显示状态 (0-表示未显示，1-表示显示)
     */
    @Schema(description = "显示状态")
    private Integer showStatus;

    /**
     * 开始日期范围查询 - 起始日期
     */
    @Schema(description = "开始日期范围查询 - 起始日期")
    private Date startDateBegin;

    /**
     * 开始日期范围查询 - 结束日期
     */
    @Schema(description = "开始日期范围查询 - 结束日期")
    private Date startDateEnd;

    /**
     * 结束日期范围查询 - 起始日期
     */
    @Schema(description = "结束日期范围查询 - 起始日期")
    private Date endDateBegin;

    /**
     * 结束日期范围查询 - 结束日期
     */
    @Schema(description = "结束日期范围查询 - 结束日期")
    private Date endDateEnd;

    /**
     * 录入日期范围查询 - 起始日期
     */
    @Schema(description = "录入日期范围查询 - 起始日期")
    private Date inputDateBegin;

    /**
     * 录入日期范围查询 - 结束日期
     */
    @Schema(description = "录入日期范围查询 - 结束日期")
    private Date inputDateEnd;
}
