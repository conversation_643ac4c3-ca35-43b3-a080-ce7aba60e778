package com.xinghuo.project.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.core.dao.ProjectBaseMapper;
import com.xinghuo.project.core.dao.ProjectTeamMapper;
import com.xinghuo.project.core.dao.ProjectUserInteractionMapper;
import com.xinghuo.project.core.entity.ProjBehaviorLogEntity;
import com.xinghuo.project.core.entity.ProjectBaseEntity;
import com.xinghuo.project.core.entity.ProjectTeamEntity;
import com.xinghuo.project.core.entity.ProjectUserInteractionEntity;
import com.xinghuo.project.core.enums.ProjectStatusEnum;
import com.xinghuo.project.core.model.ProjectPagination;
import com.xinghuo.project.core.model.dto.ProjectExtendedDTO;
import com.xinghuo.project.core.model.dto.SimpleProjectInfoDTO;
import com.xinghuo.project.core.model.projectBase.ProjectBaseInfoVO;
import com.xinghuo.project.core.model.projectBase.ProjectBaseInfoForm;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.project.core.service.ProjectService;
import com.xinghuo.project.core.service.UserBehaviorService;
import com.xinghuo.permission.service.UserService;
import com.xinghuo.permission.service.OrganizeService;
import com.xinghuo.permission.entity.UserEntity;
import com.xinghuo.permission.entity.OrganizeEntity;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 项目服务实现类
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Service
public class ProjectServiceImpl extends BaseServiceImpl<ProjectBaseMapper, ProjectBaseEntity> implements ProjectService {

    @Resource
    private ProjectBaseMapper projectBaseMapper;

    @Resource
    private ProjectTeamMapper projectTeamMapper;

    @Resource
    private ProjectUserInteractionMapper projectUserInteractionMapper;

    @Resource
    private UserBehaviorService userBehaviorService;

    @Resource
    private UserService userService;

    @Resource
    private OrganizeService organizeService;

    @Override
    public List<ProjectBaseEntity> getList(ProjectPagination pagination) {
        // 根据查询类型分发到不同的查询方法
        String queryType = StrXhUtil.isNotEmpty(pagination.getQueryType()) ? pagination.getQueryType() : "ALL";

        switch (queryType.toUpperCase()) {
//            case "MY_MANAGED":
//                return getMyManagedProjectsList(pagination);
//            case "MY_PARTICIPATED":
//                return getMyParticipatedProjectsList(pagination);
//            case "MY_FAVORITE":
//                return getMyFavoriteProjectsList(pagination);
//            case "RECENT_VISITED":
//                return getRecentVisitedProjectsList(pagination);
//            case "ADVANCED":
//                return getAdvancedQueryProjectsList(pagination);
//            case "ALL":
            default:
                return getAllProjectsList(pagination);
        }
    }

    /**
     * 获取全部项目列表（默认查询）
     */
    private List<ProjectBaseEntity> getAllProjectsList(ProjectPagination pagination) {
        QueryWrapper<ProjectBaseEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<ProjectBaseEntity> lambda = queryWrapper.lambda();

        // 应用基础查询条件
        applyBasicQueryConditions(lambda, pagination);

        // 排序：按创建时间倒序
        lambda.orderByDesc(ProjectBaseEntity::getCreatedAt);

        return processDataType(queryWrapper, pagination);
    }

    /**
     * 获取我管理的项目列表
     */
    private List<ProjectBaseEntity> getMyManagedProjectsList(ProjectPagination pagination) {
        String userId = getCurrentUserId(pagination);
        if (StrXhUtil.isEmpty(userId)) {
            return new ArrayList<>();
        }

        QueryWrapper<ProjectBaseEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<ProjectBaseEntity> lambda = queryWrapper.lambda();

        // 管理者条件
        lambda.eq(ProjectBaseEntity::getManagerId, userId);

        // 应用基础查询条件
        applyBasicQueryConditions(lambda, pagination);

        // 排序：按创建时间倒序
        lambda.orderByDesc(ProjectBaseEntity::getCreatedAt);

        return processDataType(queryWrapper, pagination);
    }

    /**
     * 获取我参与的项目列表
     */
    private List<ProjectBaseEntity> getMyParticipatedProjectsList(ProjectPagination pagination) {
        String userId = getCurrentUserId(pagination);
        if (StrXhUtil.isEmpty(userId)) {
            return new ArrayList<>();
        }

        // 先查询用户参与的项目ID列表
        QueryWrapper<ProjectTeamEntity> teamWrapper = new QueryWrapper<>();
        teamWrapper.lambda()
                   .eq(ProjectTeamEntity::getUserId, userId)
                   .eq(ProjectTeamEntity::getTeamStatus, 1); // 活跃状态

        List<ProjectTeamEntity> teamList = projectTeamMapper.selectList(teamWrapper);
        if (teamList.isEmpty()) {
            return new ArrayList<>();
        }

        List<String> projectIds = teamList.stream()
                                         .map(ProjectTeamEntity::getProjectId)
                                         .distinct()
                                         .collect(java.util.stream.Collectors.toList());

        QueryWrapper<ProjectBaseEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<ProjectBaseEntity> lambda = queryWrapper.lambda();

        // 项目ID条件
        lambda.in(ProjectBaseEntity::getId, projectIds);

        // 应用基础查询条件
        applyBasicQueryConditions(lambda, pagination);

        // 排序：按创建时间倒序
        lambda.orderByDesc(ProjectBaseEntity::getCreatedAt);

        return processDataType(queryWrapper, pagination);
    }

    /**
     * 获取我关注的项目列表
     */
    private List<ProjectBaseEntity> getMyFavoriteProjectsList(ProjectPagination pagination) {
        String userId = getCurrentUserId(pagination);
        if (StrXhUtil.isEmpty(userId)) {
            return new ArrayList<>();
        }

        try {
            // 获取用户关注的项目ID列表
            List<ProjBehaviorLogEntity> favoriteRecords = userBehaviorService.getRecentVisits(userId, "PROJECT", Integer.MAX_VALUE);
            List<String> favoriteProjectIds = favoriteRecords.stream()
                    .filter(record -> "FAVORITE".equals(record.getBehaviorType()) && record.getIsValid() == 1)
                    .map(ProjBehaviorLogEntity::getTargetId)
                    .distinct()
                    .collect(java.util.stream.Collectors.toList());

            if (favoriteProjectIds.isEmpty()) {
                return new ArrayList<>();
            }

            QueryWrapper<ProjectBaseEntity> queryWrapper = new QueryWrapper<>();
            LambdaQueryWrapper<ProjectBaseEntity> lambda = queryWrapper.lambda();

            // 项目ID条件
            lambda.in(ProjectBaseEntity::getId, favoriteProjectIds);

            // 应用基础查询条件
            applyBasicQueryConditions(lambda, pagination);

            // 排序：按创建时间倒序
            lambda.orderByDesc(ProjectBaseEntity::getCreatedAt);

            return processDataType(queryWrapper, pagination);
        } catch (Exception e) {
            log.error("获取我关注的项目列表失败: userId={}", userId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取最近访问的项目列表
     */
    private List<ProjectBaseEntity> getRecentVisitedProjectsList(ProjectPagination pagination) {
        String userId = getCurrentUserId(pagination);
        if (StrXhUtil.isEmpty(userId)) {
            return new ArrayList<>();
        }

        try {
            // 获取最近访问的项目记录
            int limit = pagination.getPageSize() != null ? pagination.getPageSize() : 20;
            List<ProjBehaviorLogEntity> visitRecords = userBehaviorService.getRecentVisits(userId, "PROJECT", limit);

            if (visitRecords.isEmpty()) {
                return new ArrayList<>();
            }

            List<String> visitedProjectIds = visitRecords.stream()
                    .map(ProjBehaviorLogEntity::getTargetId)
                    .distinct()
                    .collect(java.util.stream.Collectors.toList());

            QueryWrapper<ProjectBaseEntity> queryWrapper = new QueryWrapper<>();
            LambdaQueryWrapper<ProjectBaseEntity> lambda = queryWrapper.lambda();

            // 项目ID条件
            lambda.in(ProjectBaseEntity::getId, visitedProjectIds);

            // 应用基础查询条件
            applyBasicQueryConditions(lambda, pagination);

            // 按访问时间排序（需要手动排序）
            List<ProjectBaseEntity> projects = this.list(queryWrapper);

            // 按访问时间排序
            Map<String, java.util.Date> visitTimeMap = visitRecords.stream()
                    .collect(java.util.stream.Collectors.toMap(
                            ProjBehaviorLogEntity::getTargetId,
                            ProjBehaviorLogEntity::getLastActionTime,
                            (existing, replacement) -> existing.after(replacement) ? existing : replacement
                    ));

            projects.sort((p1, p2) -> {
                java.util.Date time1 = visitTimeMap.get(p1.getId());
                java.util.Date time2 = visitTimeMap.get(p2.getId());
                if (time1 == null && time2 == null) return 0;
                if (time1 == null) return 1;
                if (time2 == null) return -1;
                return time2.compareTo(time1); // 倒序
            });

            return projects;
        } catch (Exception e) {
            log.error("获取最近访问的项目列表失败: userId={}", userId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 高级查询项目列表
     */
    private List<ProjectBaseEntity> getAdvancedQueryProjectsList(ProjectPagination pagination) {
        QueryWrapper<ProjectBaseEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<ProjectBaseEntity> lambda = queryWrapper.lambda();

        // 应用基础查询条件
        applyBasicQueryConditions(lambda, pagination);

        // 处理高级查询条件（JSON格式）
        if (StrXhUtil.isNotEmpty(pagination.getAdvancedQuery())) {
            try {
                // 这里可以解析JSON格式的高级查询条件
                // 示例：{"customField1": "value1", "customField2": "value2"}
                // 具体实现根据业务需求定制
                log.info("高级查询条件: {}", pagination.getAdvancedQuery());
                // TODO: 解析并应用高级查询条件
            } catch (Exception e) {
                log.warn("解析高级查询条件失败: {}", pagination.getAdvancedQuery(), e);
            }
        }

        // 排序：按创建时间倒序
        lambda.orderByDesc(ProjectBaseEntity::getCreatedAt);

        return processDataType(queryWrapper, pagination);
    }

    /**
     * 应用基础查询条件
     */
    private void applyBasicQueryConditions(LambdaQueryWrapper<ProjectBaseEntity> lambda, ProjectPagination pagination) {
        // 根据项目编码模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getCode())) {
            lambda.like(ProjectBaseEntity::getCode, pagination.getCode());
        }

        // 根据项目名称模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getName())) {
            lambda.like(ProjectBaseEntity::getFullName, pagination.getName());
        }

        // 根据项目类型精确查询
        if (StrXhUtil.isNotEmpty(pagination.getProjectType())) {
            lambda.eq(ProjectBaseEntity::getTypeId, pagination.getProjectType());
        }

        // 根据项目状态精确查询
        if (StrXhUtil.isNotEmpty(pagination.getStatus())) {
            lambda.eq(ProjectBaseEntity::getStatus, pagination.getStatus());
        }

        // 根据项目健康度精确查询
        if (StrXhUtil.isNotEmpty(pagination.getHealth())) {
            lambda.eq(ProjectBaseEntity::getHealth, pagination.getHealth());
        }

        // 根据项目经理ID精确查询
        if (StrXhUtil.isNotEmpty(pagination.getManagerId())) {
            lambda.eq(ProjectBaseEntity::getManagerId, pagination.getManagerId());
        }

        // 根据部门ID精确查询
        if (StrXhUtil.isNotEmpty(pagination.getDeptId())) {
            lambda.eq(ProjectBaseEntity::getDepartmentId, pagination.getDeptId());
        }

        // 根据优先级精确查询
        if (StrXhUtil.isNotEmpty(pagination.getPriority())) {
            lambda.eq(ProjectBaseEntity::getPriority, pagination.getPriority());
        }

        // 项目开始日期范围查询
        if (pagination.getStartDateStart() != null) {
            lambda.ge(ProjectBaseEntity::getPlannedStartDate, pagination.getStartDateStart());
        }
        if (pagination.getStartDateEnd() != null) {
            lambda.le(ProjectBaseEntity::getPlannedStartDate, pagination.getStartDateEnd());
        }

        // 项目结束日期范围查询
        if (pagination.getEndDateStart() != null) {
            lambda.ge(ProjectBaseEntity::getPlannedEndDate, pagination.getEndDateStart());
        }
        if (pagination.getEndDateEnd() != null) {
            lambda.le(ProjectBaseEntity::getPlannedEndDate, pagination.getEndDateEnd());
        }

        // 创建时间范围查询
        if (pagination.getCreateTimeStart() != null) {
            lambda.ge(ProjectBaseEntity::getCreatedAt, pagination.getCreateTimeStart());
        }
        if (pagination.getCreateTimeEnd() != null) {
            lambda.le(ProjectBaseEntity::getCreatedAt, pagination.getCreateTimeEnd());
        }

        // 是否归档查询
        if (pagination.getArchived() != null) {
            // 这里假设归档状态通过status字段的特定值来判断
            if (pagination.getArchived()) {
                lambda.eq(ProjectBaseEntity::getStatus, "archived");
            } else {
                lambda.ne(ProjectBaseEntity::getStatus, "archived");
            }
        }

        // 根据关键字搜索项目名称或编码
        String keyword = pagination.getKeyword();
        if (StrXhUtil.isNotEmpty(keyword)) {
            lambda.and(wrapper -> wrapper
                    .like(ProjectBaseEntity::getFullName, keyword)
                    .or()
                    .like(ProjectBaseEntity::getCode, keyword)
            );
        }
    }

    /**
     * 获取当前用户ID
     */
    private String getCurrentUserId(ProjectPagination pagination) {
        // 优先使用传入的用户ID
        if (StrXhUtil.isNotEmpty(pagination.getUserId())) {
            return pagination.getUserId();
        }

        // 从当前登录用户获取
        try {
            return UserProvider.getUser().getUserId();
        } catch (Exception e) {
            log.warn("获取当前用户ID失败", e);
            return null;
        }
    }

    @Override
    public List<ProjectBaseEntity> getListByProjectType(String projectType) {
        if (StrXhUtil.isEmpty(projectType)) {
            return new ArrayList<>();
        }

        QueryWrapper<ProjectBaseEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                   .eq(ProjectBaseEntity::getTypeId, projectType)
                   .orderByDesc(ProjectBaseEntity::getCreatedAt);

        return this.list(queryWrapper);
    }

    @Override
    public List<ProjectBaseEntity> getListByStatus(String status) {
        if (StrXhUtil.isEmpty(status)) {
            return new ArrayList<>();
        }

        QueryWrapper<ProjectBaseEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                   .eq(ProjectBaseEntity::getStatus, status)
                   .orderByDesc(ProjectBaseEntity::getCreatedAt);

        return this.list(queryWrapper);
    }

    @Override
    public List<ProjectBaseEntity> getListByManagerId(String managerId) {
        if (StrXhUtil.isEmpty(managerId)) {
            return new ArrayList<>();
        }

        QueryWrapper<ProjectBaseEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                   .eq(ProjectBaseEntity::getManagerId, managerId)
                   .orderByDesc(ProjectBaseEntity::getCreatedAt);

        return this.list(queryWrapper);
    }

    @Override
    public List<ProjectBaseEntity> getListByDeptId(String deptId) {
        if (StrXhUtil.isEmpty(deptId)) {
            return new ArrayList<>();
        }

        QueryWrapper<ProjectBaseEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                   .eq(ProjectBaseEntity::getDepartmentId, deptId)
                   .orderByDesc(ProjectBaseEntity::getCreatedAt);

        return this.list(queryWrapper);
    }

    @Override
    public ProjectBaseEntity getInfo(String id) {
        if (StrXhUtil.isEmpty(id)) {
            log.warn("查询项目信息ID为空");
            return null;
        }
        return this.getById(id);
    }

    @Override
    public String create(ProjectBaseEntity entity) {
        if (entity == null) {
            log.warn("创建项目信息为空");
            throw new RuntimeException("项目信息不能为空");
        }

        // 验证必填字段
        if (StrXhUtil.isEmpty(entity.getFullName())) {
            log.warn("项目名称不能为空");
            throw new RuntimeException("项目名称不能为空");
        }

        // 检查项目编码是否重复
        if (StrXhUtil.isNotEmpty(entity.getCode())) {
            boolean exists = isExistByCode(entity.getCode(), null);
            if (exists) {
                log.warn("项目编码已存在: {}", entity.getCode());
                throw new RuntimeException("项目编码已存在");
            }
        } else {
            // 如果没有提供编码，自动生成
            entity.setCode(generateProjectCode());
        }

        // 设置ID
        String id = RandomUtil.snowId();
        entity.setId(id);

        // 设置默认状态
        if (entity.getStatus()==null) {
            entity.setStatus(ProjectStatusEnum.NOT_STARTED.getCode()); // 默认为规划中
        }

        // 设置默认健康度
        if (StrXhUtil.isEmpty(entity.getHealth())) {
            entity.setHealth("normal"); // 默认为正常
        }

        // 设置默认优先级
        if (StrXhUtil.isEmpty(entity.getPriority())) {
            entity.setPriority("medium"); // 默认为中等优先级
        }

        this.save(entity);
        log.info("创建项目成功, ID: {}, 名称: {}", id, entity.getFullName());
        return id;
    }

    @Override
    public void update(String id, ProjectBaseEntity entity) {
        if (StrXhUtil.isEmpty(id) || entity == null) {
            log.warn("更新项目参数无效, ID: {}", id);
            throw new RuntimeException("更新参数无效");
        }

        // 查询原记录是否存在
        ProjectBaseEntity dbEntity = this.getById(id);
        if (dbEntity == null) {
            log.warn("更新的项目不存在, ID: {}", id);
            throw new RuntimeException("项目不存在");
        }

        // 验证必填字段
        if (StrXhUtil.isEmpty(entity.getFullName())) {
            log.warn("项目名称不能为空");
            throw new RuntimeException("项目名称不能为空");
        }

        // 检查项目编码是否重复（排除自身）
        if (StrXhUtil.isNotEmpty(entity.getCode())) {
            boolean exists = isExistByCode(entity.getCode(), id);
            if (exists) {
                log.warn("项目编码已存在: {}", entity.getCode());
                throw new RuntimeException("项目编码已存在");
            }
        }

        entity.setId(id);
        this.updateById(entity);
        log.info("更新项目成功, ID: {}, 名称: {}", id, entity.getFullName());
    }

    @Override
    public void delete(String id) {
        if (StrXhUtil.isEmpty(id)) {
            log.warn("删除项目ID为空");
            throw new RuntimeException("项目ID不能为空");
        }

        // 查询项目是否存在
        ProjectBaseEntity entity = this.getById(id);
        if (entity == null) {
            log.warn("删除的项目不存在, ID: {}", id);
            throw new RuntimeException("项目不存在");
        }

        // 检查项目状态，进行中的项目不能删除
        // 假设执行中状态的数值为2（根据前端配置推断）
        if (entity.getStatus() != null && entity.getStatus().equals(ProjectStatusEnum.IN_PROGRESS.getCode())) {
            log.warn("进行中的项目不能删除, ID: {}", id);
            throw new RuntimeException("进行中的项目不能删除");
        }

        this.removeById(id);
        log.info("删除项目成功, ID: {}, 名称: {}", id, entity.getFullName());
    }

    @Override
    public void updateStatus(String id, String status) {
        if (StrXhUtil.isEmpty(id) || status !=null) {
            log.warn("更新项目状态参数无效, ID: {}, status: {}", id, status);
            throw new RuntimeException("参数无效");
        }

        ProjectBaseEntity entity = new ProjectBaseEntity();
        entity.setId(id);
        entity.setStatus(status);
        this.updateById(entity);
        log.info("更新项目状态成功, ID: {}, status: {}", id, status);
    }

    @Override
    public void updateHealth(String id, String health) {
        if (StrXhUtil.isEmpty(id) || StrXhUtil.isEmpty(health)) {
            log.warn("更新项目健康度参数无效, ID: {}, health: {}", id, health);
            throw new RuntimeException("参数无效");
        }

        ProjectBaseEntity entity = new ProjectBaseEntity();
        entity.setId(id);
        entity.setHealth(health);
        this.updateById(entity);
        log.info("更新项目健康度成功, ID: {}, health: {}", id, health);
    }

    @Override
    public boolean isExistByCode(String code, String excludeId) {
        if (StrXhUtil.isEmpty(code)) {
            return false;
        }

        QueryWrapper<ProjectBaseEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ProjectBaseEntity::getCode, code);

        // 如果有排除ID，则添加排除条件
        if (StrXhUtil.isNotEmpty(excludeId)) {
            queryWrapper.lambda().ne(ProjectBaseEntity::getId, excludeId);
        }

        long count = this.count(queryWrapper);
        return count > 0;
    }

    @Override
    public List<Map<String, Object>> getProjectStatistics(Map<String, Object> params) {
        // 使用QueryWrapper实现项目统计
        QueryWrapper<ProjectBaseEntity> queryWrapper = new QueryWrapper<>();

        // 根据参数添加查询条件
        if (params != null) {
            String status = (String) params.get("status");
            String typeId = (String) params.get("typeId");
            String managerId = (String) params.get("managerId");
            String departmentId = (String) params.get("departmentId");
            String startDate = (String) params.get("startDate");
            String endDate = (String) params.get("endDate");

            if (StrXhUtil.isNotEmpty(status)) {
                queryWrapper.lambda().eq(ProjectBaseEntity::getStatus, status);
            }

            if (StrXhUtil.isNotEmpty(typeId)) {
                queryWrapper.lambda().eq(ProjectBaseEntity::getTypeId, typeId);
            }

            if (StrXhUtil.isNotEmpty(managerId)) {
                queryWrapper.lambda().eq(ProjectBaseEntity::getManagerId, managerId);
            }

            if (StrXhUtil.isNotEmpty(departmentId)) {
                queryWrapper.lambda().eq(ProjectBaseEntity::getDepartmentId, departmentId);
            }

            if (StrXhUtil.isNotEmpty(startDate)) {
                queryWrapper.lambda().ge(ProjectBaseEntity::getPlannedStartDate, startDate);
            }

            if (StrXhUtil.isNotEmpty(endDate)) {
                queryWrapper.lambda().le(ProjectBaseEntity::getPlannedEndDate, endDate);
            }
        }

        // 按状态分组统计
        queryWrapper.select("status, COUNT(*) as count, SUM(investment_budget) as totalBudget")
                   .groupBy("status");

        return this.listMaps(queryWrapper);
    }

    @Override
    public List<Map<String, Object>> getProjectHealthStatistics(Map<String, Object> params) {
        // 使用QueryWrapper实现项目健康度统计
        QueryWrapper<ProjectBaseEntity> queryWrapper = new QueryWrapper<>();

        // 根据参数添加查询条件
        if (params != null) {
            String departmentId = (String) params.get("departmentId");
            String managerId = (String) params.get("managerId");
            String year = (String) params.get("year");

            if (StrXhUtil.isNotEmpty(departmentId)) {
                queryWrapper.lambda().eq(ProjectBaseEntity::getDepartmentId, departmentId);
            }

            if (StrXhUtil.isNotEmpty(managerId)) {
                queryWrapper.lambda().eq(ProjectBaseEntity::getManagerId, managerId);
            }

            if (StrXhUtil.isNotEmpty(year)) {
                queryWrapper.lambda().like(ProjectBaseEntity::getPlannedStartDate, year);
            }
        }

        // 按健康度分组统计
        queryWrapper.select("health, COUNT(*) as count")
                   .groupBy("health");

        return this.listMaps(queryWrapper);
    }

    @Override
    public void archiveProject(String id) {
        updateStatus(id, ProjectStatusEnum.CLOSED.getCode());
        log.info("项目归档成功, ID: {}", id);
    }

    @Override
    public void activateProject(String id) {
        updateStatus(id, ProjectStatusEnum.IN_PROGRESS.getName());
        log.info("项目激活成功, ID: {}", id);
    }

    @Override
    public SimpleProjectInfoDTO getSimpleProjectInfo(String projectId) {
        if (StrXhUtil.isEmpty(projectId)) {
            log.warn("查询项目简要信息ID为空");
            return null;
        }

        // 使用QueryWrapper查询项目基本信息
        ProjectBaseEntity project = this.getById(projectId);
        if (project == null) {
            return null;
        }

        // 转换为SimpleProjectInfoDTO
        SimpleProjectInfoDTO dto = new SimpleProjectInfoDTO();
        dto.setId(project.getId());
        dto.setFullName(project.getFullName());
        dto.setCode(project.getCode());
        dto.setStatus(project.getStatus());
        dto.setManagerUserId(project.getManagerId());
        dto.setDeptId(project.getDepartmentId());
        dto.setExpectEndTime(project.getPlannedEndDate());
        dto.setCreateTime(project.getCreatedAt());
        dto.setDisplayName(project.getFullName() + "(" + project.getCode() + ")");

        return dto;
    }

    @Override
    public ProjectExtendedDTO getExtendedProjectInfo(String projectId) {
        if (StrXhUtil.isEmpty(projectId)) {
            log.warn("查询项目扩展信息ID为空");
            return null;
        }

        // 使用QueryWrapper查询项目基本信息
        ProjectBaseEntity project = this.getById(projectId);
        if (project == null) {
            return null;
        }

        // 转换为ProjectExtendedDTO
        ProjectExtendedDTO dto = new ProjectExtendedDTO();
        // 复制基本属性
        org.springframework.beans.BeanUtils.copyProperties(project, dto);

        // 设置扩展属性
        dto.setDisplayName(project.getFullName() + "(" + project.getCode() + ")");

        // 这里可以添加更多扩展信息的查询和设置
        // 比如团队信息、统计信息等

        return dto;
    }

    @Override
    public List<ProjectExtendedDTO> searchProjects(Map<String, Object> searchParams) {
        // 使用QueryWrapper实现项目搜索
        QueryWrapper<ProjectBaseEntity> queryWrapper = new QueryWrapper<>();

        if (searchParams != null) {
            String keyword = (String) searchParams.get("keyword");
            String status = (String) searchParams.get("status");
            String managerId = (String) searchParams.get("managerId");
            String departmentId = (String) searchParams.get("departmentId");
            List<String> projectIds = (List<String>) searchParams.get("projectIds");

            if (StrXhUtil.isNotEmpty(keyword)) {
                queryWrapper.lambda().and(wrapper ->
                    wrapper.like(ProjectBaseEntity::getFullName, keyword)
                           .or()
                           .like(ProjectBaseEntity::getCode, keyword)
                           .or()
                           .like(ProjectBaseEntity::getDescription, keyword)
                );
            }

            if (StrXhUtil.isNotEmpty(status)) {
                queryWrapper.lambda().eq(ProjectBaseEntity::getStatus, status);
            }

            if (StrXhUtil.isNotEmpty(managerId)) {
                queryWrapper.lambda().eq(ProjectBaseEntity::getManagerId, managerId);
            }

            if (StrXhUtil.isNotEmpty(departmentId)) {
                queryWrapper.lambda().eq(ProjectBaseEntity::getDepartmentId, departmentId);
            }

            if (projectIds != null && !projectIds.isEmpty()) {
                queryWrapper.lambda().in(ProjectBaseEntity::getId, projectIds);
            }
        }

        queryWrapper.lambda().orderByDesc(ProjectBaseEntity::getCreatedAt);

        List<ProjectBaseEntity> projects = this.list(queryWrapper);

        // 转换为ProjectExtendedDTO
        return projects.stream().map(project -> {
            ProjectExtendedDTO dto = new ProjectExtendedDTO();
            org.springframework.beans.BeanUtils.copyProperties(project, dto);
            dto.setDisplayName(project.getFullName() + "(" + project.getCode() + ")");
            return dto;
        }).collect(java.util.stream.Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveFastNew(ProjectBaseEntity projectInfo, List<Map<String, Object>> teamList, Map<String, Object> createOptions) {
        // 创建项目基本信息
        String projectId = create(projectInfo);

        // 创建项目团队
        if (teamList != null && !teamList.isEmpty()) {
            createProjectTeam(projectId, teamList);
        }

        // 处理创建选项
        if (createOptions != null) {
            processCreateOptions(projectId, createOptions);
        }

        log.info("快速创建项目成功, ID: {}, 名称: {}", projectId, projectInfo.getFullName());
        return projectId;
    }

    @Override
    public List<ProjectExtendedDTO> getMyParticipatedProjects(String userId, Map<String, Object> params) {
        if (StrXhUtil.isEmpty(userId)) {
            return new ArrayList<>();
        }

        // 先查询用户参与的项目ID列表
        QueryWrapper<ProjectTeamEntity> teamWrapper = new QueryWrapper<>();
        teamWrapper.lambda()
                   .eq(ProjectTeamEntity::getUserId, userId)
                   .eq(ProjectTeamEntity::getTeamStatus, 1); // 活跃状态

        List<ProjectTeamEntity> teamList = projectTeamMapper.selectList(teamWrapper);
        if (teamList.isEmpty()) {
            return new ArrayList<>();
        }

        List<String> projectIds = teamList.stream()
                                         .map(ProjectTeamEntity::getProjectId)
                                         .distinct()
                                         .collect(java.util.stream.Collectors.toList());

        // 根据项目ID列表查询项目详情
        params.put("projectIds", projectIds);
        return searchProjects(params);
    }

    @Override
    public List<ProjectExtendedDTO> getMyManagedProjects(String userId, Map<String, Object> params) {
        if (StrXhUtil.isEmpty(userId)) {
            return new ArrayList<>();
        }

        // 查询用户管理的项目
        QueryWrapper<ProjectBaseEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ProjectBaseEntity::getManagerId, userId);

        // 添加其他查询条件
        if (params != null) {
            String status = (String) params.get("status");
            if (StrXhUtil.isNotEmpty(status)) {
                queryWrapper.lambda().eq(ProjectBaseEntity::getStatus, status);
            }
        }

        queryWrapper.lambda().orderByDesc(ProjectBaseEntity::getCreatedAt);

        List<ProjectBaseEntity> projects = this.list(queryWrapper);

        // 转换为ProjectExtendedDTO
        return projects.stream().map(project -> {
            ProjectExtendedDTO dto = new ProjectExtendedDTO();
            org.springframework.beans.BeanUtils.copyProperties(project, dto);
            dto.setDisplayName(project.getFullName() + "(" + project.getCode() + ")");
            return dto;
        }).collect(java.util.stream.Collectors.toList());
    }

    @Override
    public List<ProjectExtendedDTO> getMyFollowedProjects(String userId, Map<String, Object> params) {
        if (StrXhUtil.isEmpty(userId)) {
            return new ArrayList<>();
        }

        // 先获取关注的项目ID列表
        QueryWrapper<ProjectUserInteractionEntity> interactionWrapper = new QueryWrapper<>();
        interactionWrapper.lambda()
                         .eq(ProjectUserInteractionEntity::getUserId, userId)
                         .eq(ProjectUserInteractionEntity::getInteractionType, "follow")
                         .eq(ProjectUserInteractionEntity::getInteractionStatus, 1);

        List<ProjectUserInteractionEntity> interactions = projectUserInteractionMapper.selectList(interactionWrapper);
        if (interactions.isEmpty()) {
            return new ArrayList<>();
        }

        List<String> projectIds = interactions.stream()
                                            .map(ProjectUserInteractionEntity::getProjectId)
                                            .collect(java.util.stream.Collectors.toList());

        // 根据项目ID列表查询项目详情
        params.put("projectIds", projectIds);
        return searchProjects(params);
    }

    @Override
    public List<ProjectExtendedDTO> getRecentlyVisitedProjects(String userId, Map<String, Object> params) {
        if (StrXhUtil.isEmpty(userId)) {
            return new ArrayList<>();
        }

        Integer limit = (Integer) params.getOrDefault("limit", 10);
        Integer days = (Integer) params.getOrDefault("days", 30);

        // 获取最近访问的项目ID列表
        QueryWrapper<ProjectUserInteractionEntity> interactionWrapper = new QueryWrapper<>();
        interactionWrapper.lambda()
                         .eq(ProjectUserInteractionEntity::getUserId, userId)
                         .eq(ProjectUserInteractionEntity::getInteractionType, "visit")
                         .ge(ProjectUserInteractionEntity::getLastInteractionTime,
                             new java.util.Date(System.currentTimeMillis() - days * 24 * 60 * 60 * 1000L))
                         .orderByDesc(ProjectUserInteractionEntity::getLastInteractionTime)
                         .last("LIMIT " + limit);

        List<ProjectUserInteractionEntity> interactions = projectUserInteractionMapper.selectList(interactionWrapper);
        if (interactions.isEmpty()) {
            return new ArrayList<>();
        }

        List<String> projectIds = interactions.stream()
                                            .map(ProjectUserInteractionEntity::getProjectId)
                                            .collect(java.util.stream.Collectors.toList());

        // 根据项目ID列表查询项目详情
        params.put("projectIds", projectIds);
        return searchProjects(params);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProjectVisitRecord(String projectId, String userId) {
        if (StrXhUtil.isEmpty(projectId) || StrXhUtil.isEmpty(userId)) {
            log.warn("更新项目访问记录参数无效");
            return;
        }

        // 查询是否已有访问记录
        QueryWrapper<ProjectUserInteractionEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                   .eq(ProjectUserInteractionEntity::getProjectId, projectId)
                   .eq(ProjectUserInteractionEntity::getUserId, userId)
                   .eq(ProjectUserInteractionEntity::getInteractionType, "visit");

        ProjectUserInteractionEntity interaction = projectUserInteractionMapper.selectOne(queryWrapper);

        if (interaction == null) {
            // 创建新的访问记录
            interaction = new ProjectUserInteractionEntity();
            interaction.setId(RandomUtil.snowId());
            interaction.setProjectId(projectId);
            interaction.setUserId(userId);
            interaction.setInteractionType("visit");
            interaction.setInteractionStatus(1);
            interaction.setInteractionCount(1);
            interaction.setLastInteractionTime(new java.util.Date());
            projectUserInteractionMapper.insert(interaction);
        } else {
            // 更新访问记录
            interaction.setInteractionCount(interaction.getInteractionCount() + 1);
            interaction.setLastInteractionTime(new java.util.Date());
            projectUserInteractionMapper.updateById(interaction);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void followProject(String projectId, String userId, boolean follow) {
        if (StrXhUtil.isEmpty(projectId) || StrXhUtil.isEmpty(userId)) {
            log.warn("关注项目参数无效");
            throw new RuntimeException("参数无效");
        }

        // 查询是否已有关注记录
        QueryWrapper<ProjectUserInteractionEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                   .eq(ProjectUserInteractionEntity::getProjectId, projectId)
                   .eq(ProjectUserInteractionEntity::getUserId, userId)
                   .eq(ProjectUserInteractionEntity::getInteractionType, "follow");

        ProjectUserInteractionEntity interaction = projectUserInteractionMapper.selectOne(queryWrapper);

        if (interaction == null && follow) {
            // 创建关注记录
            interaction = new ProjectUserInteractionEntity();
            interaction.setId(RandomUtil.snowId());
            interaction.setProjectId(projectId);
            interaction.setUserId(userId);
            interaction.setInteractionType("follow");
            interaction.setInteractionStatus(1);
            interaction.setInteractionCount(1);
            interaction.setLastInteractionTime(new java.util.Date());
            projectUserInteractionMapper.insert(interaction);
        } else if (interaction != null) {
            // 更新关注状态
            interaction.setInteractionStatus(follow ? 1 : 0);
            interaction.setLastInteractionTime(new java.util.Date());
            projectUserInteractionMapper.updateById(interaction);
        }

        log.info("{}项目成功, 项目ID: {}, 用户ID: {}", follow ? "关注" : "取消关注", projectId, userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void favoriteProject(String projectId, String userId, boolean favorite) {
        if (StrXhUtil.isEmpty(projectId) || StrXhUtil.isEmpty(userId)) {
            log.warn("收藏项目参数无效");
            throw new RuntimeException("参数无效");
        }

        // 查询是否已有收藏记录
        QueryWrapper<ProjectUserInteractionEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                   .eq(ProjectUserInteractionEntity::getProjectId, projectId)
                   .eq(ProjectUserInteractionEntity::getUserId, userId)
                   .eq(ProjectUserInteractionEntity::getInteractionType, "favorite");

        ProjectUserInteractionEntity interaction = projectUserInteractionMapper.selectOne(queryWrapper);

        if (interaction == null && favorite) {
            // 创建收藏记录
            interaction = new ProjectUserInteractionEntity();
            interaction.setId(RandomUtil.snowId());
            interaction.setProjectId(projectId);
            interaction.setUserId(userId);
            interaction.setInteractionType("favorite");
            interaction.setInteractionStatus(1);
            interaction.setInteractionCount(1);
            interaction.setLastInteractionTime(new java.util.Date());
            projectUserInteractionMapper.insert(interaction);
        } else if (interaction != null) {
            // 更新收藏状态
            interaction.setInteractionStatus(favorite ? 1 : 0);
            interaction.setLastInteractionTime(new java.util.Date());
            projectUserInteractionMapper.updateById(interaction);
        }

        log.info("{}项目成功, 项目ID: {}, 用户ID: {}", favorite ? "收藏" : "取消收藏", projectId, userId);
    }

    /**
     * 创建项目团队
     */
    private void createProjectTeam(String projectId, List<Map<String, Object>> teamList) {
        for (Map<String, Object> teamData : teamList) {
            String roleId = (String) teamData.get("id");
            String roleName = (String) teamData.get("name");
            List<Map<String, Object>> userList = (List<Map<String, Object>>) teamData.get("userList");

            if (userList != null) {
                for (Map<String, Object> userData : userList) {
                    ProjectTeamEntity teamEntity = new ProjectTeamEntity();
                    teamEntity.setId(RandomUtil.snowId());
                    teamEntity.setProjectId(projectId);
                    teamEntity.setUserId((String) userData.get("id"));
                    teamEntity.setRoleId(roleId);
                    teamEntity.setRoleName(roleName);
                    teamEntity.setIsMainResponsible(false);
                    teamEntity.setJoinTime(new java.util.Date());
                    teamEntity.setTeamStatus(1); // 活跃状态
                    teamEntity.setWorkloadPercent(100.0);

                    projectTeamMapper.insert(teamEntity);
                }
            }
        }
    }

    /**
     * 处理创建选项
     */
    private void processCreateOptions(String projectId, Map<String, Object> createOptions) {
        // 这里可以根据创建选项进行相应的处理
        // 比如创建项目计划、预算、风险登记册等
        log.info("处理项目创建选项, 项目ID: {}", projectId);
    }

    /**
     * 生成项目编码
     *
     * @return 项目编码
     */
    private String generateProjectCode() {
        // 简单的编码生成规则：PROJ + 时间戳后6位 + 随机数
        String timestamp = String.valueOf(System.currentTimeMillis());
        String suffix = timestamp.substring(timestamp.length() - 6);
        String random = String.valueOf((int)(Math.random() * 1000));
        return "PROJ" + suffix + String.format("%03d", Integer.parseInt(random));
    }

    @Override
    public ProjectBaseInfoVO getProjectBasicInfo(String projectId) {
        if (StrXhUtil.isEmpty(projectId)) {
            throw new IllegalArgumentException("项目ID不能为空");
        }

        // 查询项目基本信息
        ProjectBaseEntity entity = this.getById(projectId);
        if (entity == null) {
            return null;
        }

        // 转换为VO对象
        ProjectBaseInfoVO vo = BeanCopierUtils.copy(entity, ProjectBaseInfoVO.class);

        // 补充关联字段的查询
        try {
            // 项目经理名称
            if (StrXhUtil.isNotEmpty(entity.getManagerId())) {
                UserEntity manager = userService.getInfo(entity.getManagerId());
                if (manager != null) {
                    vo.setManagerName(manager.getRealName());
                }
            }

            // 项目发起人名称
            if (StrXhUtil.isNotEmpty(entity.getSponsorId())) {
                UserEntity sponsor = userService.getInfo(entity.getSponsorId());
                if (sponsor != null) {
                    vo.setSponsorName(sponsor.getRealName());
                }
            }

            // 部门名称
            if (StrXhUtil.isNotEmpty(entity.getDepartmentId())) {
                OrganizeEntity department = organizeService.getInfo(entity.getDepartmentId());
                if (department != null) {
                    vo.setDepartmentName(department.getFullName());
                }
            }

            // 创建人名称
            if (StrXhUtil.isNotEmpty(entity.getCreatedBy())) {
                UserEntity creator = userService.getInfo(entity.getCreatedBy());
                if (creator != null) {
                    vo.setCreatedByName(creator.getRealName());
                }
            }

            // 最后修改人名称
            if (StrXhUtil.isNotEmpty(entity.getLastUpdatedBy())) {
                UserEntity updater = userService.getInfo(entity.getLastUpdatedBy());
                if (updater != null) {
                    vo.setLastUpdatedByName(updater.getRealName());
                }
            }

            // TODO: 其他关联字段的查询可以在后续需要时补充：
            // - 项目类型名称 (需要项目类型字典服务)
            // - 项目群名称 (需要项目群服务)
            // - 客户名称 (需要客户服务)
            // - 合同名称 (需要合同服务)
            // - 战略目标名称 (需要字典服务)
            // - 优先级名称 (需要字典服务)
            // - 风险等级名称 (需要字典服务)

        } catch (Exception e) {
            log.warn("查询项目关联信息失败: projectId={}", projectId, e);
            // 不影响主要业务流程，仅记录警告日志
        }

        return vo;
    }

    @Override
    @Transactional
    public void updateProjectBasicInfo(String projectId, ProjectBaseInfoForm form) {
        if (StrXhUtil.isEmpty(projectId)) {
            throw new IllegalArgumentException("项目ID不能为空");
        }

        if (form == null) {
            throw new IllegalArgumentException("表单数据不能为空");
        }

        // 查询现有项目
        ProjectBaseEntity existingEntity = this.getById(projectId);
        if (existingEntity == null) {
            throw new IllegalArgumentException("项目不存在");
        }

        // 将表单数据复制到现有实体对象（保留数据库管理的字段）
        existingEntity = BeanCopierUtils.copy(form, ProjectBaseEntity.class);

        // 确保ID保持不变
        existingEntity.setId(projectId);

        // 更新项目信息
        boolean updated = this.updateById(existingEntity);
        if (!updated) {
            throw new RuntimeException("更新项目信息失败");
        }

        log.info("项目基本信息更新成功, 项目ID: {}", projectId);
    }
}
