# Vue 组件开发标准

## 组件设计原则

### 核心原则
1. **单一职责**：每个组件只做一件事，避免过于复杂的组件
2. **高内聚低耦合**：相关功能集中在一个组件，组件间依赖最小化
3. **可复用性**：设计组件时考虑复用场景，使用合理的接口设计
4. **可测试性**：组件应该易于被单元测试
5. **可维护性**：代码结构清晰易懂，便于后续维护
6. **一致性**：遵循项目统一的设计风格和交互模式

### 组件分类
- **基础组件**：不包含业务逻辑的通用UI组件（按钮、输入框等）
- **业务组件**：包含特定业务逻辑的组件（用户卡片、项目列表等）
- **容器组件**：负责数据获取和状态管理，较少UI元素
- **展示组件**：主要负责UI展示，通过props接收数据
- **布局组件**：负责页面布局结构（Header、Sidebar等）

## Composition API 标准模式

### 组件文件结构模板

```vue
<script lang="ts" setup>
// 1. 导入分组（按类型排序）
import { ref, computed, watch, onMounted } from 'vue';
import type { PropType } from 'vue';
import { useRouter } from 'vue-router';
import { BasicTable } from '/@/components/Table';

// 2. 组件选项
defineOptions({ 
  name: "ComponentName",
  inheritAttrs: false
});

// 3. Props 定义（类型安全）
const props = defineProps({
  title: { type: String, required: true },
  items: { 
    type: Array as PropType<Item[]>, 
    default: () => [],
    validator: (value: Item[]) => value.every(item => 'id' in item)
  },
  status: {
    type: String as PropType<'active' | 'inactive' | 'pending'>,
    default: 'active'
  }
});

// 4. Emits 定义（类型安全）
const emit = defineEmits<{
  (event: 'close'): void;
  (event: 'change', value: string): void;
  (event: 'update:modelValue', value: string): void;
}>();

// 5. 组件状态
const loading = ref(false);
const userData = reactive<UserData>({
  name: '',
  age: 0,
  items: []
});

// 6. 计算属性
const hasContent = computed(() => props.items.length > 0);
const fullName = computed(() => 
  `${userData.firstName} ${userData.lastName}`
);

// 7. 方法
function handleSubmit() {
  loading.value = true;
  // 实现逻辑
  emit('change', 'newValue');
}

// 8. 监听器
watch(() => props.items, (newItems) => {
  // 处理数据变化
}, { immediate: true, deep: true });

// 9. 生命周期钩子
onMounted(() => {
  fetchData();
});

// 10. 暴露公共接口
defineExpose({
  reset: () => userData = { name: '', age: 0, items: [] },
  loading: readonly(loading)
});
</script>

<template>
  <!-- 模板内容 -->
</template>

<style lang="less" scoped>
/* 组件样式 */
</style>
```

### 响应式数据管理

```typescript
// ✅ 基础类型使用 ref
const count = ref<number>(0);
const isVisible = ref<boolean>(false);

// ✅ 对象类型使用 reactive
const state = reactive<UserState>({
  user: { name: "", age: 0 },
  loading: false,
  items: []
});

// ✅ 计算属性用于派生状态
const filteredItems = computed(() => 
  state.items.filter(item => item.status === 'active')
);

// ✅ 大型不可变数据使用 shallowRef
const largeDataSet = shallowRef<LargeData[]>([]);
```

### Composable 函数模式

```typescript
// 定义清晰的返回类型
interface UseCounterReturn {
  count: Ref<number>;
  increment: () => void;
  decrement: () => void;
  reset: (value?: number) => void;
}

// ✅ 标准 Composable 函数
export function useCounter(initialValue = 0): UseCounterReturn {
  const count = ref(initialValue);
  
  const increment = () => count.value++;
  const decrement = () => count.value--;
  const reset = (value = 0) => count.value = value;
  
  return {
    count: readonly(count),
    increment,
    decrement,
    reset
  };
}

// ✅ 在组件中使用
const { count, increment, decrement, reset } = useCounter(10);
```

## Props 和 Emits 规范

### Props 最佳实践

```typescript
// ✅ 完整的 Props 定义
const props = defineProps({
  // 必需的字符串
  title: { 
    type: String, 
    required: true 
  },
  
  // 带默认值和验证的数组
  data: {
    type: Array as PropType<DataItem[]>,
    default: () => [],
    validator: (value: DataItem[]) => 
      value.every(item => typeof item === 'object' && 'id' in item)
  },
  
  // 联合类型（枚举值）
  size: {
    type: String as PropType<'small' | 'medium' | 'large'>,
    default: 'medium',
    validator: (value: string) => 
      ['small', 'medium', 'large'].includes(value)
  },
  
  // 函数类型
  formatter: {
    type: Function as PropType<(value: any) => string>,
    default: (value: any) => String(value)
  },
  
  // v-model 支持
  modelValue: {
    type: [String, Number, Boolean],
    default: ''
  }
});
```

### Emits 最佳实践

```typescript
// ✅ 类型安全的 Emits 定义
const emit = defineEmits<{
  // 无参数事件
  (event: 'close'): void;
  (event: 'reset'): void;
  
  // 单参数事件
  (event: 'change', value: string): void;
  (event: 'error', error: Error): void;
  
  // 多参数事件
  (event: 'select', item: Item, index: number): void;
  
  // v-model 更新事件
  (event: 'update:modelValue', value: string): void;
}>();

// ✅ 事件处理函数命名
function handleItemClick(item: Item) {
  emit('select', item, item.index);
}

function handleValueChange(newValue: string) {
  emit('update:modelValue', newValue);
}
```

## 生命周期与监听器

### 生命周期钩子使用

```typescript
// ✅ 初始化和清理
onMounted(() => {
  // 初始化操作
  fetchData();
  initializeChart();
  addEventListener('resize', handleResize);
});

onBeforeUnmount(() => {
  // 清理操作（关键！避免内存泄漏）
  clearInterval(timer);
  removeEventListener('resize', handleResize);
  disposeChart();
});

// ✅ Keep-alive 组件
onActivated(() => {
  refreshData();
});

onDeactivated(() => {
  pauseVideoPlayback();
});
```

### 监听器模式

```typescript
// ✅ 单源监听
watch(searchQuery, async (newValue, oldValue) => {
  if (newValue === oldValue) return;
  
  if (newValue.length > 2) {
    searchResults.value = await fetchSearchResults(newValue);
  }
}, { 
  immediate: true, 
  deep: true,
  flush: 'post' // DOM更新后执行
});

// ✅ 多源监听
watch([firstName, lastName], ([newFirst, newLast]) => {
  fullName.value = `${newFirst} ${newLast}`;
});

// ✅ 自动追踪监听
watchEffect(async () => {
  if (searchQuery.value.length > 2) {
    searchResults.value = await fetchSearchResults(searchQuery.value);
  }
});

// ✅ 停止监听
const stopWatcher = watch(source, callback);
onBeforeUnmount(() => {
  stopWatcher(); // 手动停止
});
```

## 组件导入路径规范

### 项目组件导入

```typescript
// ✅ 基础组件
import { BasicTable, useTable, TableAction } from "/@/components/Table";
import { BasicForm, useForm } from "/@/components/Form";
import { BasicModal, useModalInner } from "/@/components/Modal";
import { BasicDrawer, useDrawerInner } from "/@/components/Drawer";

// ✅ 业务组件
import UserSelect from "/@/components/Xh/Organize/src/UserSelect.vue";
import DepSelect from "/@/components/Xh/Organize/src/DepSelect.vue";
import RoleSelect from "/@/components/Xh/Organize/src/RoleSelect.vue";

// ✅ 图表组件
import { DynamicChart } from "/@/components/Charts";
import { ChartCard } from "/@/components/Charts";
```

### 导入分组顺序

```typescript
// 1. Vue 核心
import { ref, computed, watch, onMounted } from 'vue';
import type { PropType } from 'vue';

// 2. 类型导入
import type { UserInfo, ApiResponse } from '/@/types';

// 3. 第三方组件
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';

// 4. 项目基础组件
import { BasicTable, useTable } from "/@/components/Table";

// 5. 项目业务组件
import UserSelect from "/@/components/Xh/UserSelect.vue";

// 6. API 和工具
import { getUserList } from '/@/api/modules/user';
import { formatDate } from '/@/utils/date';
```

## XACE 项目特定标准

### 表格组件规范

```typescript
// ✅ 正确：在 useTable 中定义 actionColumn
const [registerTable] = useTable({
  title: '用户列表',
  columns: [
    { title: '姓名', dataIndex: 'fullName', width: 120 },
    { title: '账号', dataIndex: 'account', width: 120 }
  ],
  actionColumn: {
    width: 200,
    title: '操作',
    dataIndex: 'action',
    slots: { customRender: 'action' }
  },
  api: getUserList,
  clickToRowSelect: false,
  useSearchForm: true
});

// ❌ 错误：不要在 columns 数组中定义操作列
```

### 数据格式标准

```typescript
// ✅ 正确：使用 fullName/id 格式
const statusOptions = [
  { fullName: '全部', id: '' },
  { fullName: '启用', id: '1' },
  { fullName: '禁用', id: '0' }
];

// ❌ 错误：不要使用 label/value 格式
const wrongOptions = [
  { label: '全部', value: '' }, // 错误！
  { label: '启用', value: '1' } // 错误！
];
```

### 组件通信模式

```typescript
// ✅ 父子组件通信：Props + Emits
// 父组件
const handleUserSelect = (user: UserInfo) => {
  selectedUser.value = user;
};

// 子组件
emit('select', user);

// ✅ 兄弟组件通信：共享父组件状态或 Pinia Store
const userStore = useUserStore();

// ✅ 跨层级通信：provide/inject
// 祖先组件
provide('theme', { theme, toggleTheme });

// 后代组件
const { theme, toggleTheme } = inject('theme', defaultTheme);
```

## 常用自定义组件

### XACE 业务组件库

| 组件名称 | 功能描述 | 使用场景 |
|---------|---------|----------|
| `XhUserSelect` | 用户选择器 | 选择单个或多个用户 |
| `XhDepSelect` | 部门选择器 | 选择组织架构中的部门 |
| `XhRoleSelect` | 角色选择器 | 选择系统角色 |
| `XhDatePicker` | 日期选择器 | 业务日期选择 |
| `XhSelect` | 通用选择器 | 业务数据选择 |
| `XhTreeSelect` | 树形选择器 | 层级数据选择 |

### 基础UI组件

| 组件名称 | 功能描述 | 核心特性 |
|---------|---------|----------|
| `BasicTable` | 增强表格 | 内置分页、搜索、操作列 |
| `BasicForm` | 增强表单 | Schema驱动、自动校验 |
| `BasicModal` | 增强弹窗 | 标准化操作、状态管理 |
| `BasicDrawer` | 增强抽屉 | 统一样式、操作规范 |

## 性能优化最佳实践

### 组件优化

```typescript
// ✅ 懒加载大型组件
const HeavyComponent = defineAsyncComponent({
  loader: () => import('./HeavyComponent.vue'),
  loadingComponent: LoadingSpinner,
  errorComponent: ErrorComponent,
  delay: 200,
  timeout: 3000
});

// ✅ 虚拟滚动处理大数据
<VirtualList
  :data="largeDataSet"
  :item-height="50"
  :visible-count="20"
>
  <template #item="{ item }">
    <ListItem :data="item" />
  </template>
</VirtualList>

// ✅ 条件更新优化
<div v-memo="[user.id, user.updatedAt]">
  <UserCard :user="user" />
</div>

// ✅ 防抖事件处理
import { debounce } from 'lodash-es';

const handleSearch = debounce((value: string) => {
  search(value);
}, 300);
```

### 状态管理优化

```typescript
// ✅ 大型数据使用 shallowRef
const largeList = shallowRef<Item[]>([]);

// ✅ 计算属性缓存
const expensiveValue = computed(() => {
  return heavyCalculation(props.data);
});

// ✅ 使用 shallowReactive 减少深度响应
const config = shallowReactive({
  theme: 'light',
  language: 'zh-CN',
  features: new Set(['feature1', 'feature2'])
});
```

## 开发检查清单

### 组件开发
- [ ] 使用 Composition API + `<script setup>`
- [ ] 定义清晰的 TypeScript 类型
- [ ] Props 包含类型、默认值、验证
- [ ] Emits 使用类型安全定义
- [ ] 适当的生命周期钩子和清理逻辑
- [ ] 合理的组件拆分和复用设计

### 性能和质量
- [ ] 避免不必要的响应式数据
- [ ] 使用计算属性而不是方法
- [ ] 事件处理函数适当防抖/节流
- [ ] 组件卸载时清理资源
- [ ] 大型列表使用虚拟滚动
- [ ] 适当使用 v-memo 优化渲染