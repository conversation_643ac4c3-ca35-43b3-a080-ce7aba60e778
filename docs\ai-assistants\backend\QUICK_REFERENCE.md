# XACE Claude 快速参考卡

## 🚀 一键生成指令

### Entity 实体类
```
生成XACE Entity：表名：[table_name] 业务名：[Business] 字段：[field1(type,desc), field2(type,desc)]
```

### Form 表单对象  
```
生成XACE Form：业务名：[Business] 字段：[field1(type,验证), field2(type,验证)]
```

### VO 视图对象
```
生成XACE VO：业务名：[Business] 展示字段：[基础字段列表] 关联字段：[relatedName(type,desc)]
```

### 完整 CRUD
```
生成XACE完整CRUD：
表名：[table_name]
业务名：[Business]  
字段：[完整字段列表]
模块：com.xinghuo.[module]
```

### Controller 控制器
```
生成XACE Controller：业务名：[Business] API路径：/api/[path] 功能：[CRUD功能列表]
```

### Service 服务层
```
生成XACE Service：业务名：[Business] 实体类：[Business]Entity 特殊功能：[业务方法列表]
```

---

## 📋 核心规范速记

| 组件 | 继承/注解 | 关键点 |
|------|----------|-------|
| **Entity** | `BaseEntityV2.CUBaseEntityV2<String>` | @TableField, @Data, @EqualsAndHashCode |
| **Form** | 无继承 | 合并设计, Jakarta验证, @Schema |  
| **VO** | 无继承 | 扁平化, @JsonFormat, 状态转换 |
| **Pagination** | `extends Pagination` | 查询条件, 无验证注解 |
| **Mapper** | `XHBaseMapper<Entity>` | 仅@Mapper, 无自定义方法 |
| **Service** | `BaseService<Entity>` | 标准CRUD方法定义 |
| **ServiceImpl** | `BaseServiceImpl<Mapper,Entity>` | LambdaQueryWrapper, @Transactional |
| **Controller** | 无继承 | @Resource, ActionResult<T>, @Valid |

---

## ⚡ 最常用模板

### 标准业务模块（3分钟完成）
```
1. 生成XACE Entity：表名：product_info 业务名：Product 字段：productName(String,商品名称), price(BigDecimal,价格), status(Integer,状态)

2. 生成XACE完整CRUD：表名：product_info 业务名：Product 字段：同上 模块：com.xinghuo.product
```

### 快速检查代码规范
```
检查以下代码是否符合XACE规范：[粘贴代码]
```

### 添加业务方法
```
在现有[Business]Service基础上添加方法：[method1], [method2], [method3]
```

---

## 🔥 关键约定记忆

- **包导入**：`jakarta.*` ✅  `javax.*` ❌
- **基类字段**：`createdAt` ✅  `createTime` ❌  
- **查询方式**：`wrapper.lambda()` ✅  硬编码字段 ❌
- **依赖注入**：`@Resource` ✅  `@Autowired` ❌
- **返回格式**：`ActionResult<T>` ✅  直接返回 ❌

---

**💡 提示：直接复制上述指令到Claude对话框，替换[]中的参数即可快速生成符合XACE规范的代码！**