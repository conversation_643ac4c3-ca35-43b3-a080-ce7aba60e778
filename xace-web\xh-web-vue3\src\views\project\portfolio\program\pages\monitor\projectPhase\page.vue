<template>
  <div class="xh-common-layout">
    <a-card title="项目阶段分布" :bordered="false" class="mb-4">
      <div ref="chartRef" style="height: 300px"></div>
    </a-card>

    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'currentPhase'">
          <a-tag :color="getPhaseColor(record.currentPhase)">
            {{ record.currentPhase }}
          </a-tag>
        </template>
        <template v-if="column.key === 'phaseProgress'">
          <a-progress :percent="record.phaseProgress" :status="record.phaseProgress === 100 ? 'success' : 'active'" size="small" />
        </template>
        <template v-if="column.key === 'action'">
          <TableAction :actions="getTableActions(record)" />
        </template>
      </template>
    </BasicTable>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { getTableSchemas } from './tableSchema';
  import { useMessage } from '/@/hooks/web/useMessage';
  import * as echarts from 'echarts';

  defineOptions({ name: 'ProgramProjectPhase' });

  const { createMessage } = useMessage();

  const chartRef = ref<HTMLDivElement>();

  const projectPhases = [
    {
      id: '152068',
      projectName: '智能制造系统实施与优化项目',
      projectCode: '2025000026',
      currentPhase: '需求分析',
      phaseProgress: 30,
      phaseStartDate: '2025-02-15',
      phaseEndDate: '2025-04-30',
      nextPhase: '系统设计',
      phaseDeliverable: '需求规格说明书、业务流程分析报告',
      projectManager: '高峰',
      department: '智能制造事业部',
      stageIndex: 2,
      totalStages: 6,
      phaseStatus: '进行中',
      delayDays: 15,
    },
    {
      id: '151649',
      projectName: '人力资源管理系统（HRMS）咨询项目',
      projectCode: '2025000022',
      currentPhase: '需求调研阶段',
      phaseProgress: 7,
      phaseStartDate: '2025-03-06',
      phaseEndDate: '2025-05-15',
      nextPhase: '方案设计',
      phaseDeliverable: '现状调研报告、需求清单',
      projectManager: '王子豪,曹静',
      department: '数字化创新研究院',
      stageIndex: 1,
      totalStages: 5,
      phaseStatus: '刚启动',
      delayDays: 0,
    },
    {
      id: '150470',
      projectName: 'ERP系统优化项目',
      projectCode: '2025000015',
      currentPhase: '解决方案设计与规划',
      phaseProgress: 44,
      phaseStartDate: '2025-03-15',
      phaseEndDate: '2025-06-30',
      nextPhase: '系统开发',
      phaseDeliverable: '系统架构设计、详细设计文档',
      projectManager: '陈静,王子豪',
      department: '软件科技事业部',
      stageIndex: 3,
      totalStages: 6,
      phaseStatus: '正常进行',
      delayDays: 0,
    },
    {
      id: '150163',
      projectName: 'Demo-组织结构优化与流程再造项目',
      projectCode: '2025000012',
      currentPhase: '解决方案设计与规划',
      phaseProgress: 40,
      phaseStartDate: '2025-03-05',
      phaseEndDate: '2025-07-15',
      nextPhase: '试点实施',
      phaseDeliverable: '组织架构优化方案、流程再造设计',
      projectManager: '陈静,Demo演示用户,PM',
      department: '战略决策委员会',
      stageIndex: 2,
      totalStages: 5,
      phaseStatus: '正常进行',
      delayDays: 0,
    },
    {
      id: '151862',
      projectName: 'Demo-ERP系统实施与升级项目',
      projectCode: '2025000024',
      currentPhase: '需求分析',
      phaseProgress: 46,
      phaseStartDate: '2025-03-06',
      phaseEndDate: '2025-05-30',
      nextPhase: '系统配置',
      phaseDeliverable: '业务需求文档、功能规格说明书',
      projectManager: '陈涛,Demo演示用户,PM',
      department: '合规与审计中心',
      stageIndex: 2,
      totalStages: 7,
      phaseStatus: '延期风险',
      delayDays: 10,
    },
    {
      id: '153001',
      projectName: '数字化办公平台建设项目',
      projectCode: '2025000028',
      currentPhase: '项目启动',
      phaseProgress: 10,
      phaseStartDate: '2025-07-15',
      phaseEndDate: '2025-08-31',
      nextPhase: '需求分析',
      phaseDeliverable: '项目章程、项目计划',
      projectManager: '李明,张华',
      department: '信息技术部',
      stageIndex: 1,
      totalStages: 6,
      phaseStatus: '准备中',
      delayDays: 0,
    },
    {
      id: '153002',
      projectName: '客户关系管理系统升级项目',
      projectCode: '2025000029',
      currentPhase: '项目收尾',
      phaseProgress: 100,
      phaseStartDate: '2025-02-01',
      phaseEndDate: '2025-02-28',
      nextPhase: '已完成',
      phaseDeliverable: '项目总结报告、运维移交文档',
      projectManager: '刘强,赵敏',
      department: '销售事业部',
      stageIndex: 6,
      totalStages: 6,
      phaseStatus: '已完成',
      delayDays: 0,
    },
    {
      id: '153003',
      projectName: '供应链管理优化项目',
      projectCode: '2025000030',
      currentPhase: '需求分析',
      phaseProgress: 25,
      phaseStartDate: '2024-12-01',
      phaseEndDate: '2025-03-31',
      nextPhase: '流程设计',
      phaseDeliverable: '供应链现状分析、优化需求报告',
      projectManager: '孙伟,钱丽',
      department: '采购供应链部',
      stageIndex: 2,
      totalStages: 5,
      phaseStatus: '严重延期',
      delayDays: 45,
    },
    {
      id: '153004',
      projectName: '移动办公APP开发项目',
      projectCode: '2025000031',
      currentPhase: '系统开发',
      phaseProgress: 75,
      phaseStartDate: '2025-01-15',
      phaseEndDate: '2025-06-30',
      nextPhase: '系统测试',
      phaseDeliverable: 'APP原型、核心功能模块',
      projectManager: '周杰,陈美',
      department: '移动互联网事业部',
      stageIndex: 4,
      totalStages: 6,
      phaseStatus: '正常进行',
      delayDays: 0,
    },
    {
      id: '153005',
      projectName: '数据中心建设项目',
      projectCode: '2025000032',
      currentPhase: '基础设施建设',
      phaseProgress: 60,
      phaseStartDate: '2025-01-01',
      phaseEndDate: '2025-08-31',
      nextPhase: '设备安装调试',
      phaseDeliverable: '机房建设、网络架构部署',
      projectManager: '吴强,林晓',
      department: '基础设施部',
      stageIndex: 3,
      totalStages: 5,
      phaseStatus: '正常进行',
      delayDays: 0,
    },
  ];

  const [registerTable] = useTable({
    api: async () => {
      return {
        list: projectPhases,
        pagination: {
          total: projectPhases.length,
        },
      };
    },
    columns: getTableSchemas(),
    useSearchForm: true,
    formConfig: {
      labelWidth: 100,
      schemas: [
        {
          field: 'projectName',
          label: '项目名称',
          component: 'Input',
          colProps: { span: 6 },
        },
        {
          field: 'currentPhase',
          label: '当前阶段',
          component: 'Select',
          colProps: { span: 6 },
          componentProps: {
            options: [
              { label: '项目启动', value: '项目启动' },
              { label: '需求分析', value: '需求分析' },
              { label: '需求调研阶段', value: '需求调研阶段' },
              { label: '解决方案设计与规划', value: '解决方案设计与规划' },
              { label: '系统开发', value: '系统开发' },
              { label: '基础设施建设', value: '基础设施建设' },
              { label: '项目收尾', value: '项目收尾' },
            ],
          },
        },
        {
          field: 'phaseStatus',
          label: '阶段状态',
          component: 'Select',
          colProps: { span: 6 },
          componentProps: {
            options: [
              { label: '正常进行', value: '正常进行' },
              { label: '进行中', value: '进行中' },
              { label: '刚启动', value: '刚启动' },
              { label: '准备中', value: '准备中' },
              { label: '延期风险', value: '延期风险' },
              { label: '严重延期', value: '严重延期' },
              { label: '已完成', value: '已完成' },
            ],
          },
        },
      ],
    },
    actionColumn: {
      width: 150,
      title: '操作',
      dataIndex: 'action',
    },
  });

  function getPhaseColor(phase: string) {
    const colorMap = {
      启动阶段: 'blue',
      规划阶段: 'cyan',
      执行阶段: 'green',
      监控阶段: 'orange',
      收尾阶段: 'purple',
    };
    return colorMap[phase] || 'default';
  }

  function getTableActions(record) {
    return [
      {
        label: '查看详情',
        onClick: () => handleDetail(record),
      },
      {
        label: '阶段报告',
        onClick: () => handleReport(record),
      },
    ];
  }

  function handleDetail(record) {
    createMessage.info(`查看阶段详情: ${record.projectName} - ${record.currentPhase}`);
  }

  function handleReport(record) {
    createMessage.info(`生成阶段报告: ${record.projectName}`);
  }

  onMounted(() => {
    if (chartRef.value) {
      const chart = echarts.init(chartRef.value);

      const phaseCount = projectPhases.reduce((acc, project) => {
        acc[project.currentPhase] = (acc[project.currentPhase] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const option = {
        tooltip: {
          trigger: 'item',
        },
        legend: {
          orient: 'vertical',
          left: 'right',
        },
        series: [
          {
            name: '项目阶段',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2,
            },
            label: {
              show: true,
              position: 'center',
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 20,
                fontWeight: 'bold',
              },
            },
            labelLine: {
              show: false,
            },
            data: Object.entries(phaseCount).map(([phase, count]) => ({
              value: count,
              name: phase,
            })),
          },
        ],
      };

      chart.setOption(option);
    }
  });
</script>
