package com.xinghuo.performance.model.analysis;

import com.xinghuo.common.base.model.Pagination;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 绩效分析查询参数
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PerformanceAnalysisPagination extends Pagination {

    /**
     * 开始月份
     */
    private String startMonth;

    /**
     * 结束月份
     */
    private String endMonth;

    /**
     * 分部ID
     */
    private String fbId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 最低分数
     */
    private BigDecimal minScore;

    /**
     * 最高分数
     */
    private BigDecimal maxScore;

    /**
     * 绩效状态
     */
    private String status;

    /**
     * 排序字段
     */
    private String orderBy;

    /**
     * 排序方向
     */
    private String orderDirection;
}
