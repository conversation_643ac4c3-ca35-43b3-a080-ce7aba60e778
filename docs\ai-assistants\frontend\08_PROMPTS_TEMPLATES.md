# AI代码生成提示词模板

本文档提供使用AI工具（如GitHub Copilot或ChatGPT）生成XACE前端代码的提示词模板，帮助开发者快速生成高质量且符合项目规范的代码。

## 目录

- [AI代码生成提示词模板](#ai代码生成提示词模板)
  - [目录](#目录)
  - [通用模板](#通用模板)
    - [项目背景信息](#项目背景信息)
    - [代码风格说明](#代码风格说明)
    - [项目特定组件使用说明](#项目特定组件使用说明)
    - [项目特定布局规范](#项目特定布局规范)
    - [项目特定功能实现](#项目特定功能实现)
  - [组件生成模板](#组件生成模板)
    - [基础组件](#基础组件)
    - [表单组件](#表单组件)
    - [列表页面](#列表页面)
  - [API集成模板](#api集成模板)
    - [API定义](#api定义)
    - [API文件结构](#api文件结构)
    - [Hook封装](#hook封装)
  - [状态管理模板](#状态管理模板)
    - [Pinia Store](#pinia-store)
    - [模块化Store](#模块化store)
    - [全局状态与局部状态](#全局状态与局部状态)
    - [持久化策略](#持久化策略)
  - [工具函数模板](#工具函数模板)
    - [通用工具函数](#通用工具函数)
    - [日期处理函数](#日期处理函数)
    - [数据处理工具](#数据处理工具)
    - [业务工具函数](#业务工具函数)
    - [项目特定工具函数](#项目特定工具函数)
  - [项目特定模板](#项目特定模板)
    - [Allegro/Fruugo平台集成](#allegrofruugo平台集成)
    - [卖家分析功能](#卖家分析功能)
    - [产品监控功能](#产品监控功能)
    - [销售数据分析](#销售数据分析)
    - [多语言处理](#多语言处理)
    - [数据同步功能](#数据同步功能)
    - [店铺管理功能](#店铺管理功能)
    - [批量操作功能](#批量操作功能)
    - [报表和数据可视化](#报表和数据可视化)
    - [数据大屏](#数据大屏)
    - [数据导入导出](#数据导入导出)
    - [系统配置管理](#系统配置管理)
  - [最佳实践和案例](#最佳实践和案例)
    - [AI提示词最佳实践](#ai提示词最佳实践)
    - [项目特定注意事项](#项目特定注意事项)
    - [案例：完整页面开发流程](#案例完整页面开发流程)
    - [案例：图表组件开发流程](#案例图表组件开发流程)
    - [案例：数据同步功能开发流程](#案例数据同步功能开发流程)

## 通用模板

### 项目背景信息

在向AI提问前，先描述项目背景，有助于生成更匹配的代码：

```
我正在使用Vue 3.3.x + TypeScript 5.x + Ant Design Vue 3.2.x开发XACE系统前端。
该系统使用Vite 4.x构建，使用Pinia 2.x管理状态，使用Vue Router 4.x处理路由，使用Axios进行HTTP请求。
项目采用Composition API风格，使用<script lang="ts" setup>语法。
请根据以下需求生成符合项目规范的代码。
```

### 代码风格说明

```
请遵循以下编码风格：
1. 使用Vue 3 Composition API (<script lang="ts" setup>)
2. 组件使用defineOptions({name: 'ComponentName'})定义名称，便于路由缓存
3. 类型使用TypeScript接口或类型别名，确保类型完整性
4. 变量命名使用camelCase，组件使用PascalCase，常量使用UPPER_CASE
5. 样式使用Less，并使用scoped限制作用域
6. 使用ref/reactive进行响应式管理，复杂对象使用reactive，简单值使用ref
7. 导入顺序：外部库 -> 类型 -> API -> 组件 -> 工具/Hook -> 资源 -> 样式
8. HTTP请求使用/@/utils/http/axios中的defHttp工具，不使用@/utils/request
9. 组件通信使用props/emits，复杂状态使用Pinia
10. 表单和表格组件优先使用项目封装的BasicForm和BasicTable组件
```

### 项目特定组件使用说明

```
请注意以下项目特定组件的使用：
1. 表格组件使用BasicTable而非直接使用a-table，支持内置分页功能
2. 表单组件使用BasicForm而非直接使用a-form
3. 页面包装使用PageWrapper组件
4. 抽屉组件使用BasicDrawer，并通过useDrawer/useDrawerInner注册
5. 模态框使用BasicModal，并通过useModal/useModalInner注册
6. 图片预览使用ImgMagnifier组件
7. 消息提示使用useMessage钩子中的createMessage方法
8. 页面说明使用PageDescriptionDrawer和PageDescriptionSection组件
9. 表格操作列使用TableAction组件
```

### 项目特定布局规范

```
请遵循以下布局规范：
1. 页面高度应紧凑，特别是导航区域，减少不必要的高度
2. 查询条件表单应限制在一行内，字段标签尽量简短
3. 列表视图应添加序号列
4. 统计卡片应优化布局避免出现滚动条
5. 图表应确保正确初始化和响应窗口大小变化
6. 图片展示应使用ImgMagnifier组件，主要产品图片应较大
7. 页面说明内容应放在弹出框而非页面上，通过"查看页面说明"按钮切换
8. 页面说明应放在右上角，遵循seller/index.vue的模式
9. "返回"按钮应改为"关闭"按钮，并关闭当前标签页
```

### 项目特定功能实现

```
请遵循以下功能实现规范：
1. 路由配置应启用缓存，使用defineOptions({name: 'component-name'})
2. HTTP请求使用/@/utils/http/axios而非@/utils/request
3. 查询方法应放在ServiceImpl类中，而非controller方法中
4. 分页查询应参考FruugoPushOfferController的getList方法实现
5. 业务逻辑应从controllers移至service implementations
6. 删除数据时，应设置f_tenantId字段为'DELETE'而非实际删除记录
7. 正确导入和注册Ant Design Vue组件，避免'Failed to resolve component'警告
8. 将常用功能如打开商店URL和查看卖家详情封装为可复用的工具函数
```

## 组件生成模板

### 基础组件

```
请生成一个Vue 3组件，要求如下：
- 组件名称：{组件名}
- 功能描述：{功能描述}
- Props：{列出需要的props}
- 事件：{列出需要emit的事件}
- 特殊要求：{任何特殊要求}

组件应包含完整的TypeScript类型定义，使用Composition API，并使用defineOptions定义组件名称以支持路由缓存。
请确保组件结构遵循项目规范，包括导入顺序和样式使用Less。
```

示例：

```
请生成一个Vue 3组件，要求如下：
- 组件名称：StatusBadge
- 功能描述：展示状态标签，根据不同状态显示不同颜色
- Props：
  - status: 状态值，可选值为'success'|'warning'|'error'|'processing'|'default'
  - text: 显示的文本
  - showDot: 是否显示小圆点，默认true
- 事件：
  - click: 点击标签时触发

组件应包含完整的TypeScript类型定义，使用Composition API，并使用defineOptions定义组件名称以支持路由缓存。
请确保组件结构遵循项目规范，包括导入顺序和样式使用Less。
```

### 表单组件

```
请生成一个Vue 3表单组件，要求如下：
- 表单字段：{列出表单字段及其类型、验证规则}
- 布局：{表单布局描述}
- 提交操作：{表单提交后的处理}
- API调用：{需要调用的API}

组件应使用项目封装的BasicForm组件而非直接使用a-form，使用Composition API，并包含完整的TypeScript类型定义。
请确保表单验证规则完整，并处理提交、重置等操作。
```

示例：

```
请生成一个Vue 3表单组件，要求如下：
- 表单字段：
  - userName: 字符串，必填，3-20个字符
  - password: 字符串，必填，至少6个字符，包含数字和字母
  - email: 字符串，必填，有效的邮箱格式
  - role: 字符串，必选，从roleList中选择
- 布局：两列布局，label占6，表单项占18
- 提交操作：调用createUser API，成功后显示成功消息并关闭表单
- API调用：从/@/api/system/user导入createUser

组件应使用项目封装的BasicForm组件而非直接使用a-form，使用Composition API，并包含完整的TypeScript类型定义。
请确保表单验证规则完整，并处理提交、重置等操作。
```

### 列表页面

```
请生成一个Vue 3列表页面，要求如下：
- 页面功能：{页面功能描述}
- 查询条件：{查询条件字段}
- 表格列：{表格列定义}
- 操作按钮：{需要的操作按钮}
- API调用：{需要调用的API}

页面应使用项目封装的BasicTable组件而非直接使用a-table，使用PageWrapper包装页面，并包含完整的TypeScript类型定义。
请确保查询条件表单紧凑（限制在一行内），表格包含序号列，并处理加载、错误状态。
```

示例：

```
请生成一个Vue 3列表页面，要求如下：
- 页面功能：用户管理列表
- 查询条件：
  - 用户名：模糊查询
  - 状态：下拉选择（全部，启用，禁用）
  - 创建时间：日期范围
- 表格列：
  - 序号
  - 用户名
  - 邮箱
  - 角色
  - 状态（显示为标签）
  - 创建时间
  - 操作
- 操作按钮：
  - 表格上方：新增、批量删除
  - 行操作：编辑、删除、详情
- API调用：从/@/api/system/user导入getUserList, deleteUser, batchDeleteUsers

页面应使用项目封装的BasicTable组件而非直接使用a-table，使用PageWrapper包装页面，并包含完整的TypeScript类型定义。
请确保查询条件表单紧凑（限制在一行内），表格包含序号列，并处理加载、错误状态。
```

## API集成模板

### API定义

```
请根据以下后端API信息，生成对应的TypeScript API定义文件：
- API名称：{API名称}
- 请求方法：{GET/POST/PUT/DELETE}
- 请求路径：{请求路径}
- 请求参数：{请求参数及其类型}
- 响应数据：{响应数据及其类型}

使用项目中的defHttp工具（从/@/utils/http/axios导入）进行请求，不要使用@/utils/request。
请定义完整的TypeScript接口，包括请求参数和响应数据的类型。
```

示例：

```
请根据以下后端API信息，生成对应的TypeScript API定义文件：
- API名称：获取用户列表
- 请求方法：POST
- 请求路径：/api/system/user/getList
- 请求参数：
  - userName: 可选，字符串，用户名
  - status: 可选，数字，用户状态
  - pageSize: 必填，数字，每页条数
  - currentPage: 必填，数字，当前页码
- 响应数据：
  {
    "code": 200,
    "msg": "操作成功",
    "data": {
      "list": [
        {
          "id": "string",
          "userName": "string",
          "email": "string",
          "status": 1,
          "roleId": "string",
          "roleName": "string",
          "createTime": "string"
        }
      ],
      "pagination": {
        "currentPage": 1,
        "pageSize": 2,
        "total": 146
      }
    }
  }

使用项目中的defHttp工具（从/@/utils/http/axios导入）进行请求，不要使用@/utils/request。
请定义完整的TypeScript接口，包括请求参数和响应数据的类型。
```

### API文件结构

```
请为{模块名称}模块创建一个完整的API文件，包含以下API：
- {API1名称}：{简要描述}
- {API2名称}：{简要描述}
- {API3名称}：{简要描述}

API文件应包含：
1. API枚举定义
2. 请求和响应的TypeScript接口
3. 使用defHttp的API函数实现

请确保代码风格一致，使用项目规范的命名和注释。
```

示例：

```
请为商品管理模块创建一个完整的API文件，包含以下API：
- 获取商品列表：支持分页和筛选
- 获取商品详情：根据ID获取单个商品信息
- 创建商品：添加新商品
- 更新商品：更新现有商品信息
- 删除商品：删除单个商品
- 批量删除商品：删除多个商品

API文件应包含：
1. API枚举定义
2. 请求和响应的TypeScript接口
3. 使用defHttp的API函数实现

请确保代码风格一致，使用项目规范的命名和注释。
```

### Hook封装

```
请为以下API生成一个Vue 3 Hook封装：
- API功能：{API功能描述}
- API导入：{API导入路径}
- 参数类型：{参数类型}
- 响应类型：{响应类型}
- 特殊处理：{需要的特殊处理}

Hook应处理加载状态、错误状态，并支持重新请求。
请使用Composition API风格，确保类型定义完整，并处理可能的错误情况。
```

示例：

```
请为以下API生成一个Vue 3 Hook封装：
- API功能：获取用户列表
- API导入：import { getUserList } from '/@/api/system/user';
- 参数类型：{ userName?: string; status?: number; page: number; pageSize: number; }
- 响应类型：{ records: UserInfo[]; total: number; pages: number; size: number; current: number; }
- 特殊处理：
  1. 支持参数缓存
  2. 支持分页变化
  3. 支持条件过滤
  4. 使用useMessage提供操作反馈

Hook应处理加载状态、错误状态，并支持重新请求。
请使用Composition API风格，确保类型定义完整，并处理可能的错误情况。
```

## 状态管理模板

### Pinia Store

```
请生成一个Pinia Store，要求如下：
- Store名称：{Store名称}
- 状态定义：{状态及其类型}
- Actions：{需要的Actions}
- Getters：{需要的Getters}
- 持久化：{是否需要持久化，哪些字段需要持久化}

Store应包含完整的TypeScript类型定义，使用defineStore创建，并遵循项目的状态管理规范。
请确保状态更新逻辑清晰，异步操作处理得当，并提供必要的注释。
```

示例：

```
请生成一个Pinia Store，要求如下：
- Store名称：userStore
- 状态定义：
  - userInfo: 用户信息对象，包含id, name, avatar, permissions等字段
  - token: string，用户令牌
  - rememberMe: boolean，是否记住登录状态
  - lastUpdateTime: number，最后更新时间
- Actions：
  - login: 登录操作，调用API并设置用户信息
  - logout: 登出操作，清除用户信息
  - getUserInfoAction: 获取用户信息
  - updateUserInfo: 更新用户信息
- Getters：
  - hasPermission: 判断是否有某权限
  - isLoggedIn: 判断是否已登录
  - getUserInfo: 获取用户信息
- 持久化：token和rememberMe需要持久化到localStorage

Store应包含完整的TypeScript类型定义，使用defineStore创建，并遵循项目的状态管理规范。
请确保状态更新逻辑清晰，异步操作处理得当，并提供必要的注释。
```

### 模块化Store

```
请为{模块名称}创建一个模块化的Pinia Store，包含以下功能：
- 状态管理：{需要管理的状态}
- 数据获取：{需要从API获取的数据}
- 数据处理：{需要进行的数据处理}
- 状态同步：{需要同步的状态}
- 持久化：{需要持久化的数据}

Store应分为state、getters、actions三个部分，使用TypeScript接口定义状态类型，并确保类型安全。
请处理好异步操作和错误情况，提供必要的注释说明各部分功能。
```

示例：

```
请为商品管理模块创建一个模块化的Pinia Store，包含以下功能：
- 状态管理：
  - 商品列表
  - 当前选中的商品
  - 筛选条件
  - 分页信息
  - 加载状态
- 数据获取：
  - 获取商品列表
  - 获取商品详情
  - 获取商品分类
- 数据处理：
  - 商品数据格式化
  - 商品分类树构建
  - 价格计算
- 状态同步：
  - 更新筛选条件时重新加载数据
  - 分页变化时更新列表
- 持久化：
  - 筛选条件需要持久化

Store应分为state、getters、actions三个部分，使用TypeScript接口定义状态类型，并确保类型安全。
请处理好异步操作和错误情况，提供必要的注释说明各部分功能。
```

### 全局状态与局部状态

```
请为{功能描述}创建状态管理解决方案，包括：
- 全局状态：{需要全局管理的状态}
- 局部状态：{需要局部管理的状态}
- 状态交互：{状态间的交互}
- 组件访问：{组件如何访问状态}

请说明哪些状态应该使用Pinia全局管理，哪些应该使用组件局部状态（ref/reactive），以及如何处理它们之间的交互。
确保方案遵循Vue 3的最佳实践，避免不必要的全局状态。
```

示例：

```
请为电商系统的购物车功能创建状态管理解决方案，包括：
- 全局状态：
  - 购物车商品列表
  - 购物车总金额
  - 购物车商品数量
  - 优惠券信息
- 局部状态：
  - 商品数量编辑状态
  - 商品选中状态
  - 结算表单数据
  - UI展示状态（抽屉是否打开等）
- 状态交互：
  - 添加商品到购物车时更新全局状态
  - 修改商品数量时计算总金额
  - 应用优惠券时重新计算价格
- 组件访问：
  - 导航栏组件需要显示购物车数量
  - 购物车页面需要完整访问和修改购物车数据
  - 商品详情页需要添加商品到购物车

请说明哪些状态应该使用Pinia全局管理，哪些应该使用组件局部状态（ref/reactive），以及如何处理它们之间的交互。
确保方案遵循Vue 3的最佳实践，避免不必要的全局状态。
```

### 持久化策略

```
请为{应用场景}设计状态持久化策略，包括：
- 需要持久化的数据：{列出需要持久化的数据}
- 持久化方式：{localStorage/sessionStorage/cookie等}
- 数据安全性：{敏感数据处理方式}
- 过期策略：{数据过期处理}
- 同步策略：{多标签页/设备间同步}

请详细说明实现方式，包括使用的库或自定义实现，以及如何与Pinia集成。
确保方案考虑了性能、安全性和用户体验。
```

示例：

```
请为用户认证系统设计状态持久化策略，包括：
- 需要持久化的数据：
  - 用户令牌(token)
  - 用户基本信息(不含敏感数据)
  - 用户偏好设置
  - 记住登录状态选项
- 持久化方式：
  - token存储在localStorage（记住登录）或sessionStorage（不记住登录）
  - 用户基本信息存储在localStorage
  - 用户偏好设置存储在localStorage
- 数据安全性：
  - token使用有效期限制
  - 敏感信息（如密码）不进行持久化
  - 考虑对持久化数据进行加密
- 过期策略：
  - token过期自动跳转登录页
  - 本地数据与token同步过期
- 同步策略：
  - 多标签页通过localStorage事件监听实现数据同步
  - 登出操作在所有标签页同步执行

请详细说明实现方式，包括使用的库或自定义实现，以及如何与Pinia集成。
确保方案考虑了性能、安全性和用户体验。
```

## 工具函数模板

### 通用工具函数

```
请生成以下工具函数：
- 函数名称：{函数名称}
- 功能描述：{功能描述}
- 参数：{参数及其类型}
- 返回值：{返回值及其类型}
- 特殊要求：{特殊要求}

函数应包含完整的TypeScript类型定义和JSDoc文档注释。
请确保函数处理边缘情况和错误情况，并遵循函数式编程原则。
```

示例：

```
请生成以下工具函数：
- 函数名称：formatFileSize
- 功能描述：将字节大小格式化为人类可读的文件大小
- 参数：
  - size: number，文件大小（字节）
  - decimals: number，可选，小数点位数，默认2
- 返回值：string，格式化后的大小（如 "1.25 MB"）
- 特殊要求：
  1. 支持B, KB, MB, GB, TB, PB单位
  2. 当size为0时返回"0 B"
  3. 当size为负数时抛出错误
  4. 支持国际化单位显示

函数应包含完整的TypeScript类型定义和JSDoc文档注释。
请确保函数处理边缘情况和错误情况，并遵循函数式编程原则。
```

### 日期处理函数

```
请生成一个日期处理工具函数集，包含以下功能：
- 日期格式化：{格式化要求}
- 日期计算：{计算功能}
- 日期比较：{比较功能}
- 日期验证：{验证功能}
- 特殊处理：{特殊处理要求}

函数集应使用dayjs库实现，包含完整的TypeScript类型定义和JSDoc文档注释。
请确保函数处理各种边缘情况，并提供使用示例。
```

示例：

```
请生成一个日期处理工具函数集，包含以下功能：
- 日期格式化：
  - 格式化为YYYY-MM-DD
  - 格式化为YYYY-MM-DD HH:mm:ss
  - 格式化为相对时间（如"3小时前"）
  - 自定义格式化
- 日期计算：
  - 计算两个日期之间的天数
  - 添加/减少天数、月数、年数
  - 获取某月的第一天和最后一天
  - 获取某周的开始和结束
- 日期比较：
  - 判断日期大小
  - 判断是否同一天/周/月/年
  - 判断是否工作日/周末
- 日期验证：
  - 验证日期格式是否有效
  - 验证日期是否在指定范围内
- 特殊处理：
  - 处理时区问题
  - 支持国际化显示

函数集应使用dayjs库实现，包含完整的TypeScript类型定义和JSDoc文档注释。
请确保函数处理各种边缘情况，并提供使用示例。
```

### 数据处理工具

```
请生成一个数据处理工具函数集，用于处理{数据类型}，包含以下功能：
- 数据转换：{转换功能}
- 数据验证：{验证功能}
- 数据过滤：{过滤功能}
- 数据排序：{排序功能}
- 数据分组：{分组功能}

函数集应包含完整的TypeScript类型定义和JSDoc文档注释。
请确保函数高效、安全，并处理各种边缘情况。
```

示例：

```
请生成一个数据处理工具函数集，用于处理表格数据，包含以下功能：
- 数据转换：
  - 扁平数据转树形结构
  - 树形结构转扁平数据
  - 数据格式标准化
  - 字段映射转换
- 数据验证：
  - 检查必填字段
  - 验证数据类型
  - 验证数据范围
  - 验证唯一性
- 数据过滤：
  - 按条件过滤数据
  - 支持多条件组合过滤
  - 支持模糊匹配和精确匹配
  - 支持范围过滤
- 数据排序：
  - 单字段排序
  - 多字段排序
  - 自定义排序规则
  - 本地排序与远程排序
- 数据分组：
  - 按字段分组
  - 分组统计计算
  - 多级分组
  - 分组展开/折叠控制

函数集应包含完整的TypeScript类型定义和JSDoc文档注释。
请确保函数高效、安全，并处理各种边缘情况。
```

### 业务工具函数

```
请为{业务场景}创建一组业务工具函数，包含以下功能：
- {功能1}：{功能描述}
- {功能2}：{功能描述}
- {功能3}：{功能描述}
- {功能4}：{功能描述}

函数应封装常用的业务逻辑，提高代码复用性，并包含完整的TypeScript类型定义。
请确保函数命名清晰，参数设计合理，并提供详细的文档注释。
```

示例：

```
请为电商系统创建一组业务工具函数，包含以下功能：
- 价格计算：
  - 计算商品总价（含税、折扣等）
  - 计算运费（基于重量、距离等）
  - 计算优惠后价格（各种优惠规则）
  - 计算税费（不同税率和规则）
- 库存管理：
  - 检查商品是否有库存
  - 计算安全库存水平
  - 预测库存消耗时间
  - 生成库存预警
- 订单处理：
  - 验证订单有效性
  - 生成订单编号
  - 计算订单状态
  - 判断订单是否可取消/退款
- 用户行为分析：
  - 计算用户活跃度
  - 分析购买频率
  - 识别高价值用户
  - 计算用户留存率

函数应封装常用的业务逻辑，提高代码复用性，并包含完整的TypeScript类型定义。
请确保函数命名清晰，参数设计合理，并提供详细的文档注释。
```

### 项目特定工具函数

```
请为XACE系统创建以下项目特定工具函数：
- 函数名称：{函数名称}
- 功能描述：{功能描述}
- 参数：{参数及其类型}
- 返回值：{返回值及其类型}
- 使用场景：{使用场景描述}

函数应遵循项目的代码规范，包含完整的TypeScript类型定义和JSDoc文档注释。
请确保函数与项目现有功能集成，并考虑性能和可维护性。
```

示例：

```
请为XACE系统创建以下项目特定工具函数：
- 函数名称：openStoreUrl
- 功能描述：打开店铺URL，处理不同平台的URL格式
- 参数：
  - url: string，店铺URL
  - platform: string，可选，平台类型（'fruugo'|'allegro'|'other'）
  - newTab: boolean，可选，是否在新标签页打开，默认true
- 返回值：void
- 使用场景：在卖家列表、产品详情等页面中，需要打开店铺URL查看详情

函数应遵循项目的代码规范，包含完整的TypeScript类型定义和JSDoc文档注释。
请确保函数与项目现有功能集成，并考虑性能和可维护性。

特别要求：
1. 对于Fruugo平台，确保URL格式为https://www.fruugoschweiz.com/，并添加?language=en后缀
2. 处理URL为空的情况
3. 提供友好的用户反馈
```

## 项目特定模板

### Allegro/Fruugo平台集成

```
请为{Allegro/Fruugo}平台创建{功能描述}功能，包括：
- 数据模型：{数据模型描述}
- API接口：{API接口描述}
- 前端组件：{前端组件描述}
- 特殊处理：{特殊处理要求}

请确保代码符合项目规范，处理好多语言（波兰语/英语）转换，并支持数据编辑功能。
```

示例：

```
请为Allegro平台创建产品数据管理功能，包括：
- 数据模型：
  - 扩展ErpProductEntity，添加英文标题、参数、图片地址、描述字段
  - 添加Fruugo目录映射字段和路径
  - 支持价格设置和编辑
- API接口：
  - 创建AllegroProductController，支持查看、编辑、分析产品数据
  - 支持将产品认领到FruugoPushOfferEntity
  - 支持与FruugoShelfTemplateEntity集成
- 前端组件：
  - 产品列表页面，支持多条件筛选和批量操作
  - 产品编辑抽屉，支持编辑英文信息和Fruugo相关属性
  - 产品认领模态框，支持选择Fruugo店铺和模板
- 特殊处理：
  - 处理波兰语到英语的转换
  - 支持图片预览和编辑
  - 关联销售数据和店铺数据展示

请确保代码符合项目规范，处理好多语言（波兰语/英语）转换，并支持数据编辑功能。
```

### 卖家分析功能

```
请为{平台名称}创建卖家分析功能，包括：
- 统计指标：{需要展示的统计指标}
- 图表展示：{需要的图表类型和数据}
- 数据筛选：{筛选条件}
- 交互功能：{交互功能描述}
- 后端API：{需要的API}

请确保UI紧凑，图表正确初始化，并处理窗口大小变化。使用ECharts实现图表，确保导入正确。
```

示例：

```
请为Allegro平台创建卖家分析功能，包括：
- 统计指标：
  - 卖家总数、活跃卖家数、高级卖家数、新增卖家数
  - 活跃率、高级卖家比例、增长率
  - 中国卖家统计数据
- 图表展示：
  - 卖家趋势图（新增卖家、中国卖家）
  - 卖家分布图（按类型、按状态）
  - 高级卖家比例图
- 数据筛选：
  - 时间范围选择
  - 卖家类型筛选
  - 地区筛选
- 交互功能：
  - 图表类型切换
  - 数据刷新
  - 图表悬停显示详情
- 后端API：
  - 获取卖家统计数据
  - 获取卖家趋势数据
  - 获取卖家分布数据
  - 获取中国卖家数据

请确保UI紧凑，图表正确初始化，并处理窗口大小变化。使用ECharts实现图表，确保导入正确（import * as echarts from 'echarts'）而不是使用window.echarts。
```

### 产品监控功能

```
请为{平台名称}创建产品监控功能，包括：
- 监控指标：{需要监控的指标}
- 数据展示：{数据展示方式}
- 告警规则：{告警规则设置}
- 自动更新：{数据更新机制}
- 用户交互：{用户交互功能}

请确保界面简洁高效，支持自动数据刷新，并提供必要的用户操作反馈。
```

示例：

```
请为Fruugo平台创建产品监控功能，包括：
- 监控指标：
  - 产品价格变化
  - 库存状态变化
  - 销售排名变化
  - 竞争对手价格
  - 产品评分和评论
- 数据展示：
  - 产品监控列表，支持分页和筛选
  - 价格变化趋势图
  - 库存状态指示器
  - 销售排名变化指标
  - 竞争对手价格对比表
- 告警规则：
  - 价格低于/高于阈值告警
  - 库存不足告警
  - 排名大幅下降告警
  - 新评论/负面评论告警
- 自动更新：
  - 每30分钟自动刷新数据
  - 支持手动刷新
  - 后台任务定时更新
  - 更新时间显示
- 用户交互：
  - 添加/移除监控产品
  - 调整告警规则
  - 导出监控数据
  - 查看历史变化记录

请确保界面简洁高效，支持自动数据刷新，并提供必要的用户操作反馈。
使用BasicTable组件实现列表，使用ECharts实现趋势图，确保正确处理组件生命周期。
```

### 销售数据分析

```
请为{平台名称}创建销售数据分析功能，包括：
- 销售指标：{需要分析的销售指标}
- 数据维度：{数据分析维度}
- 图表展示：{图表类型和展示方式}
- 数据筛选：{筛选和过滤选项}
- 导出功能：{数据导出选项}

请确保数据展示清晰，图表加载高效，并支持多维度数据分析。
```

示例：

```
请为Allegro平台创建销售数据分析功能，包括：
- 销售指标：
  - 销售额（总额、日均、增长率）
  - 销售量（总量、日均、增长率）
  - 平均价格
  - 转化率
  - 利润率
- 数据维度：
  - 时间维度（日、周、月、季、年）
  - 产品维度（类别、品牌、价格区间）
  - 卖家维度（等级、地区、规模）
  - 地区维度（国家、城市）
- 图表展示：
  - 销售趋势折线图
  - 产品类别占比饼图
  - 卖家销售排行柱状图
  - 地区销售热力图
  - 多维度对比雷达图
- 数据筛选：
  - 时间范围选择器
  - 产品类别多选框
  - 卖家类型下拉选择
  - 价格区间滑块
  - 销量阈值输入框
- 导出功能：
  - Excel数据导出
  - PDF报表导出
  - 图表图片导出
  - 定制报表模板
  - 数据筛选条件保存

请确保数据展示清晰，图表加载高效，并支持多维度数据分析。
使用BasicTable展示数据表格，使用ECharts实现各类图表，确保正确处理组件生命周期和窗口大小变化。
```

### 多语言处理

```
请创建一个多语言处理工具，用于处理{语言A}和{语言B}之间的转换，包括：
- 文本翻译：{翻译功能描述}
- 数据转换：{数据转换描述}
- 格式处理：{格式处理描述}
- 特殊字符：{特殊字符处理}
- API集成：{API集成描述}

工具应支持批量处理，处理异常情况，并提供友好的用户界面。
```

示例：

```
请创建一个多语言处理工具，用于处理波兰语和英语之间的转换，包括：
- 文本翻译：
  - 产品标题翻译
  - 产品描述翻译
  - 产品参数翻译
  - 分类名称翻译
- 数据转换：
  - 波兰语产品数据转换为英语格式
  - 保留原始数据和翻译后数据
  - 支持手动编辑翻译结果
- 格式处理：
  - 保持HTML格式
  - 处理特殊格式（如列表、表格）
  - 保持换行和段落结构
- 特殊字符：
  - 处理波兰语特殊字符
  - 处理货币符号和单位转换
  - 处理日期格式差异
- API集成：
  - 集成Google翻译API
  - 支持批量翻译请求
  - 处理API限流和错误

工具应支持批量处理，处理异常情况，并提供友好的用户界面。
使用BasicForm组件实现表单，使用BasicTable展示数据，确保UI简洁高效。
```

### 数据同步功能

```
请为{数据类型}创建数据同步功能，包括：
- 数据源：{数据来源描述}
- 目标系统：{目标系统描述}
- 同步策略：{同步策略描述}
- 冲突处理：{冲突处理描述}
- 错误恢复：{错误恢复描述}

功能应包含前端界面和后端实现，支持手动和自动同步，并提供同步日志和状态监控。
```

示例：

```
请为Allegro产品数据创建数据同步功能，包括：
- 数据源：
  - Allegro API获取的产品数据
  - 本地数据库中的现有产品数据
  - 手动上传的产品数据文件
- 目标系统：
  - Fruugo平台
  - 本地数据库
  - 其他销售渠道
- 同步策略：
  - 增量同步（仅同步变更数据）
  - 全量同步（完整替换数据）
  - 定时自动同步（每天/每周/每月）
  - 条件触发同步（价格变化/库存变化）
- 冲突处理：
  - 检测数据冲突（如SKU冲突）
  - 提供冲突解决选项（保留源/目标/合并）
  - 支持手动处理复杂冲突
  - 记录冲突解决历史
- 错误恢复：
  - 同步事务支持回滚
  - 失败重试机制
  - 部分成功处理
  - 错误通知和报告

功能应包含前端界面和后端实现，支持手动和自动同步，并提供同步日志和状态监控。
使用BasicTable展示同步记录，使用BasicForm配置同步选项，确保UI简洁高效。
```

### 店铺管理功能

```
请为{平台名称}创建店铺管理功能，包括：
- 店铺信息：{店铺信息管理}
- 授权管理：{授权和认证管理}
- 性能监控：{店铺性能监控}
- 设置选项：{店铺设置选项}
- 数据统计：{数据统计和报表}

功能应提供完整的店铺生命周期管理，支持多店铺切换，并提供必要的数据可视化。
```

示例：

```
请为Fruugo平台创建店铺管理功能，包括：
- 店铺信息：
  - 店铺基本信息管理（名称、描述、logo等）
  - 联系信息设置（邮箱、电话、地址）
  - 店铺分类和标签管理
  - 店铺状态监控和切换
- 授权管理：
  - API密钥管理
  - 授权状态监控
  - 权限范围设置
  - 授权更新和续期
  - 授权历史记录
- 性能监控：
  - 店铺评分和评级
  - 订单处理时间
  - 客户满意度
  - 商品上架率
  - 违规和投诉记录
- 设置选项：
  - 运费模板设置
  - 退货政策配置
  - 自动回复设置
  - 通知偏好设置
  - 默认产品设置
- 数据统计：
  - 销售额和订单量趋势
  - 产品表现分析
  - 客户地域分布
  - 转化率和访问量
  - 收益和成本分析

功能应提供完整的店铺生命周期管理，支持多店铺切换，并提供必要的数据可视化。
使用BasicForm进行设置，使用BasicTable展示数据，使用ECharts实现数据可视化，确保UI简洁高效。
```

### 批量操作功能

```
请为{数据类型}创建批量操作功能，包括：
- 数据选择：{数据选择方式}
- 操作类型：{支持的操作类型}
- 执行策略：{执行策略选项}
- 结果处理：{结果处理方式}
- 错误处理：{错误处理机制}

功能应支持大数据量处理，提供操作进度反馈，并确保操作的可靠性和可恢复性。
```

示例：

```
请为Allegro产品数据创建批量操作功能，包括：
- 数据选择：
  - 表格多选（勾选行）
  - 条件筛选选择（符合条件的所有数据）
  - 手动输入ID列表
  - 文件导入（CSV/Excel）
  - 全选/反选/清除选择
- 操作类型：
  - 批量更新价格（固定值/百分比调整）
  - 批量更新状态（上架/下架/删除）
  - 批量分类调整
  - 批量导出数据
  - 批量认领到Fruugo
- 执行策略：
  - 立即执行
  - 定时执行
  - 分批执行（控制并发）
  - 条件执行（满足条件时执行）
- 结果处理：
  - 实时进度显示
  - 成功/失败统计
  - 操作日志记录
  - 结果导出
  - 操作撤销选项
- 错误处理：
  - 单项错误跳过继续执行
  - 错误重试机制
  - 错误详情记录
  - 批量操作事务支持
  - 手动干预选项

功能应支持大数据量处理，提供操作进度反馈，并确保操作的可靠性和可恢复性。
使用BasicTable实现数据选择，使用BasicForm配置操作参数，使用Modal进行确认，确保UI简洁高效。
```

### 报表和数据可视化

```
请为{业务领域}创建报表和数据可视化功能，包括：
- 报表类型：{需要的报表类型}
- 数据维度：{数据分析维度}
- 图表类型：{需要的图表类型}
- 交互功能：{交互功能描述}
- 导出选项：{导出格式和选项}

功能应使用ECharts实现图表，支持数据筛选和钻取，并提供导出和打印功能。
```

示例：

```
请为销售分析创建报表和数据可视化功能，包括：
- 报表类型：
  - 销售趋势报表（日/周/月/季/年）
  - 产品销售排行
  - 卖家销售业绩
  - 地区销售分布
  - 销售漏斗分析
- 数据维度：
  - 时间维度（日期范围、时间粒度）
  - 产品维度（类别、品牌、价格区间）
  - 卖家维度（等级、地区、规模）
  - 客户维度（新/老客户、购买频率）
- 图表类型：
  - 折线图（趋势分析）
  - 柱状图（对比分析）
  - 饼图（占比分析）
  - 地图（地区分布）
  - 热力图（相关性分析）
- 交互功能：
  - 数据筛选和过滤
  - 图表联动
  - 数据钻取（从总览到详情）
  - 自定义时间范围
  - 图表配置保存
- 导出选项：
  - Excel导出（原始数据）
  - PDF导出（报表格式）
  - 图片导出（PNG/JPG）
  - 定时发送报表
  - 批量导出

功能应使用ECharts实现图表，支持数据筛选和钻取，并提供导出和打印功能。
确保图表正确初始化，处理窗口大小变化，并在组件卸载时清理资源。
```

### 数据大屏

```
请为{业务场景}创建数据大屏展示功能，包括：
- 布局设计：{布局结构描述}
- 核心指标：{需要展示的核心指标}
- 图表组件：{需要的图表组件}
- 数据更新：{数据更新机制}
- 交互功能：{交互功能描述}

功能应使用ECharts实现图表，确保视觉效果美观，数据更新流畅，并支持必要的交互。
```

示例：

```
请为跨境电商运营中心创建数据大屏展示功能，包括：
- 布局设计：
  - 顶部：核心KPI指标展示区
  - 左侧：销售趋势和地区分布
  - 中间：实时订单流和热销产品
  - 右侧：卖家排行和客户分析
  - 底部：系统状态和告警信息
- 核心指标：
  - 今日销售额/订单量/新增客户
  - 环比/同比增长率
  - 平均订单价值
  - 转化率和退款率
  - 库存健康度
- 图表组件：
  - 销售趋势折线图（支持多平台对比）
  - 地区销售分布地图
  - 产品类别占比饼图
  - 卖家业绩排行柱状图
  - 实时订单流水瀑布图
- 数据更新：
  - 核心KPI实时更新（5秒间隔）
  - 趋势图表定时更新（1分钟间隔）
  - 排行榜定时更新（5分钟间隔）
  - 支持手动刷新
  - 显示数据最后更新时间
- 交互功能：
  - 图表悬停显示详情
  - 点击下钻查看明细
  - 时间范围切换
  - 平台/地区筛选
  - 全屏/窗口切换

功能应使用ECharts实现图表，确保视觉效果美观，数据更新流畅，并支持必要的交互。
注意处理图表初始化和窗口大小变化，确保在不同分辨率下都能正常显示。
```

### 数据导入导出

```
请为{数据类型}创建数据导入导出功能，包括：
- 导入功能：{导入功能描述}
- 导出功能：{导出功能描述}
- 数据验证：{数据验证规则}
- 错误处理：{错误处理机制}
- 模板管理：{模板管理功能}

功能应支持多种文件格式，提供数据预览和验证，并处理大数据量的导入导出。
```

示例：

```
请为产品数据创建数据导入导出功能，包括：
- 导入功能：
  - 支持Excel/CSV/XML/JSON格式导入
  - 文件上传界面（拖拽/选择文件）
  - 数据预览和映射配置
  - 批量导入进度显示
  - 导入历史记录
- 导出功能：
  - 支持多种格式导出（Excel/CSV/PDF/JSON）
  - 自定义导出字段选择
  - 导出数据筛选条件
  - 大数据量分批导出
  - 导出任务后台处理
- 数据验证：
  - 必填字段验证
  - 数据格式和类型验证
  - 业务规则验证（如SKU唯一性）
  - 数据关联性验证
  - 自定义验证规则配置
- 错误处理：
  - 错误数据标记和展示
  - 错误原因详细说明
  - 错误数据导出修正
  - 部分导入/全部回滚选项
  - 错误重试机制
- 模板管理：
  - 导入模板下载
  - 自定义模板保存
  - 模板版本管理
  - 默认模板设置
  - 模板字段映射配置

功能应支持多种文件格式，提供数据预览和验证，并处理大数据量的导入导出。
使用BasicTable展示数据预览，使用BasicForm配置导入导出选项，确保UI简洁高效。
```

### 系统配置管理

```
请为{系统模块}创建配置管理功能，包括：
- 配置项：{需要管理的配置项}
- 权限控制：{权限控制机制}
- 配置验证：{配置验证规则}
- 历史记录：{历史记录管理}
- 环境管理：{多环境配置管理}

功能应提供友好的配置界面，支持配置的版本控制和回滚，并确保配置的安全性。
```

示例：

```
请为跨境电商平台集成模块创建配置管理功能，包括：
- 配置项：
  - API连接设置（URL、密钥、超时时间）
  - 数据同步设置（频率、范围、策略）
  - 通知设置（邮件、短信、系统通知）
  - 默认值设置（币种、语言、税率）
  - 业务规则配置（价格计算、库存策略）
- 权限控制：
  - 基于角色的配置访问控制
  - 敏感配置项加密显示
  - 配置修改审批流程
  - 操作日志记录
  - IP限制访问
- 配置验证：
  - 格式和类型验证
  - 依赖项检查
  - 连接测试功能
  - 配置生效前预检
  - 配置冲突检测
- 历史记录：
  - 配置变更历史记录
  - 变更对比功能
  - 配置回滚功能
  - 变更原因记录
  - 变更影响分析
- 环境管理：
  - 开发/测试/生产环境配置分离
  - 环境配置复制功能
  - 环境切换功能
  - 环境特定配置标记
  - 配置部署流程

功能应提供友好的配置界面，支持配置的版本控制和回滚，并确保配置的安全性。
使用BasicForm实现配置表单，使用BasicTable展示配置历史，确保UI简洁高效。
```

## 最佳实践和案例

### AI提示词最佳实践

1. **提供清晰上下文**：
   - 在提问前先描述项目背景和技术栈
   - 明确说明是 XACE 系统，使用 Vue 3 + TypeScript + Ant Design Vue
   - 提及项目特定组件如 BasicTable、BasicForm 等

2. **指定具体需求**：
   - 明确功能、参数、返回值等
   - 列出具体的字段名称和类型
   - 说明业务规则和约束条件
   - 提供 UI/UX 要求（如紧凑布局、图表初始化等）

3. **提供示例**：
   - 尽可能提供数据结构示例或 API 响应示例
   - 提供类似功能的现有代码路径作为参考
   - 说明特定的命名约定或格式要求

4. **迭代优化**：
   - 对 AI 生成的代码进行审查，然后要求其改进
   - 指出具体的问题和改进方向
   - 分步骤验证和完善代码

5. **分步骤要求**：
   - 复杂功能可以分步骤要求 AI 生成，先框架后细节
   - 先生成数据模型和接口，再实现组件和逻辑
   - 最后添加样式和交互细节

### 项目特定注意事项

1. **路由和缓存**：
   - 确保使用 `defineOptions({name: 'ComponentName'})` 定义组件名称，支持路由缓存
   - 路由配置应遵循项目现有结构，放在正确的模块下

2. **HTTP 请求**：
   - 使用 `/@/utils/http/axios` 中的 `defHttp` 工具，不使用 `@/utils/request`
   - API 路径应遵循项目规范，如 `/api/module/action`

3. **UI 组件使用**：
   - 表格使用 `BasicTable` 而非直接使用 `a-table`
   - 表单使用 `BasicForm` 而非直接使用 `a-form`
   - 抽屉使用 `BasicDrawer` 并通过 `useDrawer/useDrawerInner` 注册
   - 模态框使用 `BasicModal` 并通过 `useModal/useModalInner` 注册

4. **布局规范**：
   - 页面高度应紧凑，特别是导航区域
   - 查询条件表单应限制在一行内，字段标签尽量简短
   - 列表视图应添加序号列
   - 统计卡片应优化布局避免出现滚动条

5. **图表处理**：
   - 使用 `import * as echarts from 'echarts'` 导入 ECharts
   - 确保图表正确初始化和响应窗口大小变化
   - 在组件卸载时清理资源和事件监听

### 案例：完整页面开发流程

```
我需要为XACE系统开发一个Allegro产品管理页面，使用Vue 3 + TypeScript + Ant Design Vue。请按照以下步骤帮我完成：

1. 首先，定义产品相关的TypeScript接口，包括：
   - 基本信息（ID、名称、描述等）
   - 英文信息（标题、参数、描述）
   - Fruugo相关属性（分类ID、路径、价格等）
   - 销售和店铺关联数据

2. 创建API调用函数，包括：
   - 获取产品列表（支持分页和筛选）
   - 获取产品详情
   - 更新产品信息
   - 删除产品
   - 认领产品到Fruugo

3. 开发产品列表页面，包括：
   - 查询表单（支持多条件筛选）
   - 数据表格（使用BasicTable，包含序号列）
   - 批量操作功能（认领到Fruugo、批量删除）
   - 单个产品操作（编辑、同步英文数据、删除）

4. 开发产品编辑抽屉组件，包括：
   - 基本信息编辑表单
   - 英文信息编辑（标题、参数、描述）
   - 图片预览和编辑
   - 销售数据和店铺数据展示

5. 开发产品认领模态框，包括：
   - 选择Fruugo店铺
   - 选择刊登模板
   - 批量认领功能

请逐步生成代码，确保符合项目规范，特别是使用项目特定组件和布局要求。
```

### 案例：图表组件开发流程

```
我需要为XACE系统开发一个Allegro卖家分析图表组件，使用Vue 3 + TypeScript + ECharts。请按照以下步骤帮我完成：

1. 首先，定义数据接口和API调用函数：
   - 卖家统计数据接口
   - 卖家趋势数据接口
   - 卖家分布数据接口
   - 中国卖家数据接口

2. 创建图表组件框架：
   - 组件基本结构（模板、脚本、样式）
   - 响应式数据定义
   - 生命周期钩子

3. 实现趋势图表：
   - 使用ECharts初始化图表
   - 处理数据加载和转换
   - 实现图表类型切换（新增卖家、中国卖家）
   - 处理窗口大小变化

4. 实现分布图表：
   - 使用ECharts初始化图表
   - 处理数据加载和转换
   - 实现图表类型切换（按类型、按状态）
   - 添加交互功能

5. 优化组件性能和用户体验：
   - 添加加载状态
   - 处理错误情况
   - 优化图表更新逻辑
   - 确保组件卸载时清理资源

请确保图表正确初始化，使用import * as echarts from 'echarts'导入ECharts，而不是使用window.echarts。
确保UI紧凑，避免出现滚动条，并正确处理窗口大小变化。
```

### 案例：数据同步功能开发流程

```
我需要为XACE系统开发一个Allegro到Fruugo的产品数据同步功能，使用Vue 3 + TypeScript + Ant Design Vue。请按照以下步骤帮我完成：

1. 首先，定义数据模型和接口：
   - 同步任务模型
   - 同步配置选项
   - 同步结果和日志

2. 创建API调用函数：
   - 创建同步任务
   - 获取同步状态
   - 获取同步历史
   - 取消同步任务

3. 开发同步配置界面：
   - 同步选项表单（使用BasicForm）
   - 数据映射配置
   - 同步策略设置
   - 冲突处理规则

4. 开发同步监控界面：
   - 同步进度显示
   - 实时日志展示
   - 错误和警告提示
   - 手动干预选项

5. 开发同步历史和报告：
   - 同步历史列表（使用BasicTable）
   - 同步结果统计
   - 详细同步报告
   - 导出同步报告

请确保界面简洁高效，提供清晰的用户反馈，并处理好异步操作和错误情况。
遵循项目的代码规范和UI设计风格，特别是使用项目特定组件。
```