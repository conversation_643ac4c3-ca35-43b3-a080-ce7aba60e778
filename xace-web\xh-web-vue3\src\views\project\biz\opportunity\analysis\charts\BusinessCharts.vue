<template>
  <div class="business-charts">
    <a-row :gutter="16">
      <a-col :xs="24" :md="12" :lg="6">
        <a-card :bordered="false" class="stat-card">
          <div class="stat-content">
            <div class="stat-icon status-icon">
              <FileSearchOutlined />
            </div>
            <div class="stat-info">
              <div class="stat-title">总商机数</div>
              <div class="stat-value">{{ statistics.totalCount }}</div>
              <div class="stat-desc">
                <span :class="statistics.totalGrowth >= 0 ? 'growth-up' : 'growth-down'">
                  <component :is="statistics.totalGrowth >= 0 ? 'RiseOutlined' : 'FallOutlined'" />
                  {{ Math.abs(statistics.totalGrowth) }}%
                </span>
                较上月
              </div>
            </div>
          </div>
        </a-card>
      </a-col>

      <a-col :xs="24" :md="12" :lg="6">
        <a-card :bordered="false" class="stat-card">
          <div class="stat-content">
            <div class="stat-icon amount-icon">
              <DollarOutlined />
            </div>
            <div class="stat-info">
              <div class="stat-title">商机总金额</div>
              <div class="stat-value">{{ formatAmount(statistics.totalAmount) }}</div>
              <div class="stat-desc">
                <span :class="statistics.amountGrowth >= 0 ? 'growth-up' : 'growth-down'">
                  <component :is="statistics.amountGrowth >= 0 ? 'RiseOutlined' : 'FallOutlined'" />
                  {{ Math.abs(statistics.amountGrowth) }}%
                </span>
                较上月
              </div>
            </div>
          </div>
        </a-card>
      </a-col>

      <a-col :xs="24" :md="12" :lg="6">
        <a-card :bordered="false" class="stat-card">
          <div class="stat-content">
            <div class="stat-icon signed-icon">
              <CheckCircleOutlined />
            </div>
            <div class="stat-info">
              <div class="stat-title">已签商机</div>
              <div class="stat-value">{{ statistics.signedCount }}</div>
              <div class="stat-desc">
                <span class="conversion-rate"> 转化率: {{ statistics.conversionRate }}% </span>
              </div>
            </div>
          </div>
        </a-card>
      </a-col>

      <a-col :xs="24" :md="12" :lg="6">
        <a-card :bordered="false" class="stat-card">
          <div class="stat-content">
            <div class="stat-icon tracking-icon">
              <ClockCircleOutlined />
            </div>
            <div class="stat-info">
              <div class="stat-title">跟踪中商机</div>
              <div class="stat-value">{{ statistics.trackingCount }}</div>
              <div class="stat-desc">
                <span> 平均跟踪时长: {{ statistics.avgTrackingDays }} 天 </span>
              </div>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <a-row :gutter="16" class="chart-row">
      <a-col :xs="24" :lg="12">
        <a-card :bordered="false" title="商机状态分布">
          <div ref="statusChartRef" class="chart-container"></div>
        </a-card>
      </a-col>

      <a-col :xs="24" :lg="12">
        <a-card :bordered="false" title="商机金额分布">
          <div ref="amountChartRef" class="chart-container"></div>
        </a-card>
      </a-col>
    </a-row>

    <a-row :gutter="16" class="chart-row">
      <a-col :span="24">
        <a-card :bordered="false" title="商机趋势">
          <a-radio-group v-model:value="trendType" button-style="solid" class="mb-4">
            <a-radio-button value="count">数量</a-radio-button>
            <a-radio-button value="amount">金额</a-radio-button>
          </a-radio-group>
          <div ref="trendChartRef" class="chart-container"></div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, onUnmounted, watch } from 'vue';
  import { FileSearchOutlined, DollarOutlined, CheckCircleOutlined, ClockCircleOutlined, RiseOutlined, FallOutlined } from '@ant-design/icons-vue';
  import * as echarts from 'echarts/core';
  import { BarChart, LineChart, PieChart } from 'echarts/charts';
  import { TitleComponent, TooltipComponent, LegendComponent, GridComponent } from 'echarts/components';
  import { CanvasRenderer } from 'echarts/renderers';

  // 注册 ECharts 组件
  echarts.use([BarChart, LineChart, PieChart, TitleComponent, TooltipComponent, LegendComponent, GridComponent, CanvasRenderer]);

  // 图表引用
  const statusChartRef = ref<HTMLElement | null>(null);
  const amountChartRef = ref<HTMLElement | null>(null);
  const trendChartRef = ref<HTMLElement | null>(null);

  // 图表实例
  let statusChart: echarts.ECharts | null = null;
  let amountChart: echarts.ECharts | null = null;
  let trendChart: echarts.ECharts | null = null;

  // 趋势类型
  const trendType = ref<'count' | 'amount'>('count');

  // 模拟统计数据
  const statistics = {
    totalCount: 156,
    totalGrowth: 12.5,
    totalAmount: 8560000,
    amountGrowth: -5.2,
    signedCount: 42,
    conversionRate: 26.9,
    trackingCount: 98,
    avgTrackingDays: 45,
  };

  // 模拟状态分布数据
  const statusData = [
    { value: 98, name: '跟踪中' },
    { value: 32, name: '方案报价中' },
    { value: 26, name: '商务谈判中' },
    { value: 42, name: '已签' },
    { value: 18, name: '已废弃' },
    { value: 12, name: '明年跟踪' },
  ];

  // 模拟金额分布数据
  const amountData = [
    { value: 35, name: '10万以下' },
    { value: 42, name: '10-50万' },
    { value: 28, name: '50-100万' },
    { value: 32, name: '100-500万' },
    { value: 19, name: '500万以上' },
  ];

  // 模拟趋势数据
  const trendData = {
    months: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
    count: [12, 15, 18, 22, 25, 30, 28, 32, 36, 40, 45, 48],
    amount: [120, 150, 180, 220, 250, 300, 280, 320, 360, 400, 450, 480],
  };

  // 格式化金额
  function formatAmount(amount: number) {
    if (amount >= 10000000) {
      return (amount / 10000000).toFixed(2) + '千万';
    } else if (amount >= 10000) {
      return (amount / 10000).toFixed(2) + '万';
    } else {
      return amount.toString();
    }
  }

  // 初始化状态分布图表
  function initStatusChart() {
    if (statusChartRef.value) {
      statusChart = echarts.init(statusChartRef.value);

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)',
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
          data: statusData.map(item => item.name),
        },
        series: [
          {
            name: '商机状态',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2,
            },
            label: {
              show: false,
              position: 'center',
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold',
              },
            },
            labelLine: {
              show: false,
            },
            data: statusData,
          },
        ],
      };

      statusChart.setOption(option);
    }
  }

  // 初始化金额分布图表
  function initAmountChart() {
    if (amountChartRef.value) {
      amountChart = echarts.init(amountChartRef.value);

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)',
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
          data: amountData.map(item => item.name),
        },
        series: [
          {
            name: '金额分布',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2,
            },
            label: {
              show: false,
              position: 'center',
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold',
              },
            },
            labelLine: {
              show: false,
            },
            data: amountData,
          },
        ],
      };

      amountChart.setOption(option);
    }
  }

  // 初始化趋势图表
  function initTrendChart() {
    if (trendChartRef.value) {
      trendChart = echarts.init(trendChartRef.value);
      updateTrendChart();
    }
  }

  // 更新趋势图表
  function updateTrendChart() {
    if (!trendChart) return;

    const data = trendType.value === 'count' ? trendData.count : trendData.amount;
    const title = trendType.value === 'count' ? '商机数量趋势' : '商机金额趋势(万元)';

    const option = {
      title: {
        text: title,
        left: 'center',
      },
      tooltip: {
        trigger: 'axis',
      },
      xAxis: {
        type: 'category',
        data: trendData.months,
      },
      yAxis: {
        type: 'value',
      },
      series: [
        {
          data: data,
          type: 'line',
          smooth: true,
          lineStyle: {
            width: 3,
            shadowColor: 'rgba(0,0,0,0.2)',
            shadowBlur: 10,
            shadowOffsetY: 10,
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(24, 144, 255, 0.6)',
                },
                {
                  offset: 1,
                  color: 'rgba(24, 144, 255, 0.1)',
                },
              ],
            },
          },
        },
      ],
    };

    trendChart.setOption(option);
  }

  // 监听窗口大小变化
  function handleResize() {
    statusChart?.resize();
    amountChart?.resize();
    trendChart?.resize();
  }

  // 监听趋势类型变化
  watch(trendType, () => {
    updateTrendChart();
  });

  onMounted(() => {
    // 初始化图表
    initStatusChart();
    initAmountChart();
    initTrendChart();

    // 添加窗口大小变化监听
    window.addEventListener('resize', handleResize);
  });

  onUnmounted(() => {
    // 销毁图表实例
    statusChart?.dispose();
    amountChart?.dispose();
    trendChart?.dispose();

    // 移除窗口大小变化监听
    window.removeEventListener('resize', handleResize);
  });
</script>

<style lang="less" scoped>
  .business-charts {
    .stat-card {
      margin-bottom: 16px;

      .stat-content {
        display: flex;
        align-items: center;

        .stat-icon {
          width: 64px;
          height: 64px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 28px;
          margin-right: 16px;

          &.status-icon {
            background-color: #e6f7ff;
            color: #1890ff;
          }

          &.amount-icon {
            background-color: #f6ffed;
            color: #52c41a;
          }

          &.signed-icon {
            background-color: #fff7e6;
            color: #fa8c16;
          }

          &.tracking-icon {
            background-color: #f9f0ff;
            color: #722ed1;
          }
        }

        .stat-info {
          flex: 1;

          .stat-title {
            color: #8c8c8c;
            font-size: 14px;
            margin-bottom: 4px;
          }

          .stat-value {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 4px;
          }

          .stat-desc {
            color: #8c8c8c;
            font-size: 12px;

            .growth-up {
              color: #52c41a;
              margin-right: 4px;
            }

            .growth-down {
              color: #ff4d4f;
              margin-right: 4px;
            }

            .conversion-rate {
              color: #1890ff;
            }
          }
        }
      }
    }

    .chart-row {
      margin-bottom: 16px;
    }

    .chart-container {
      height: 300px;
    }

    .mb-4 {
      margin-bottom: 16px;
    }
  }
</style>
