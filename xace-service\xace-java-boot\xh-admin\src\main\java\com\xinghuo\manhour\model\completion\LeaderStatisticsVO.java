package com.xinghuo.manhour.model.completion;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 负责人统计数据VO
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@Schema(description = "负责人统计数据")
public class LeaderStatisticsVO {

    @Schema(description = "负责人ID")
    private String leaderId;

    @Schema(description = "负责人姓名")
    private String leaderName;

    @Schema(description = "负责人类型")
    private String leaderType;

    @Schema(description = "负责人数")
    private Integer managedUsers;

    @Schema(description = "待审批数")
    private Integer pendingCount;

    @Schema(description = "已审批数")
    private Integer approvedCount;

    @Schema(description = "审批效率")
    private BigDecimal approvalEfficiency;

    @Schema(description = "平均审批时长")
    private BigDecimal avgApprovalDays;

    @Schema(description = "最长等待记录")
    private Integer maxWaitingDays;

    @Schema(description = "本月审批数")
    private Integer monthlyApprovalCount;

    @Schema(description = "逾期审批数")
    private Integer overdueApprovalCount;

    @Schema(description = "联系电话")
    private String phone;

    @Schema(description = "邮箱")
    private String email;
}
