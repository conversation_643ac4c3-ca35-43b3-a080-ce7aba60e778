import { defHttp } from '/@/utils/http/axios';

/**
 * 合同分析API
 */

// API URL前缀
const API_PREFIX = '/api/project/biz/report';

/**
 * 合同分析数据接口
 */
export interface ContractAnalysisData {
  // 合同基本信息
  contractInfo: {
    id: string;
    name: string;
    amount: number;
    contractStatus: string;
    cendDate: string;
    [key: string]: any;
  };

  // 收款统计
  paymentStats: {
    totalCount: number;
    totalAmount: number;
    receivedAmount: number;
    pendingAmount: number;
  };

  // 付款统计
  paymentContractStats: {
    totalCount: number;
    totalAmount: number;
    paidAmount: number;
    unpaidAmount: number;
  };
}

/**
 * API响应包装接口
 */
export interface ContractAnalysisResponse {
  code: number;
  msg: string;
  data: ContractAnalysisData;
}

/**
 * 获取合同分析数据
 * @param projectId 项目ID
 * @param contractId 合同ID
 * @param params 其他参数
 * @returns 合同分析数据
 */
export const getContractAnalysis = (projectId?: string, contractId?: string, params?: any) => {
  return defHttp.get<ContractAnalysisResponse>({
    url: `${API_PREFIX}/contract/analysis`,
    params: {
      projectId,
      contractId,
      ...params,
    },
  });
};

/**
 * 获取合同月度趋势数据
 * @param contractId 合同ID
 * @param params 其他参数
 * @returns 月度趋势数据
 */
export const getContractMonthlyTrend = (contractId: string, params?: any) => {
  return defHttp.get<{
    months: string[];
    receivedAmounts: number[];
    targetAmounts: number[];
  }>({
    url: `${API_PREFIX}/contract/monthly-trend`,
    params: {
      contractId,
      ...params,
    },
  });
};

/**
 * 获取合同执行状态数据
 * @param contractId 合同ID
 * @param params 其他参数
 * @returns 执行状态数据
 */
export const getContractExecutionStatus = (contractId: string, params?: any) => {
  return defHttp.get<
    Array<{
      key: string;
      label: string;
      percent: number;
      color: string;
    }>
  >({
    url: `${API_PREFIX}/contract/execution-status`,
    params: {
      contractId,
      ...params,
    },
  });
};

/**
 * 获取合同风险提醒数据
 * @param contractId 合同ID
 * @param params 其他参数
 * @returns 风险提醒数据
 */
export const getContractRiskAlerts = (contractId: string, params?: any) => {
  return defHttp.get<
    Array<{
      id: number;
      title: string;
      description: string;
      type: 'warning' | 'error' | 'info' | 'success';
    }>
  >({
    url: `${API_PREFIX}/contract/risk-alerts`,
    params: {
      contractId,
      ...params,
    },
  });
};
