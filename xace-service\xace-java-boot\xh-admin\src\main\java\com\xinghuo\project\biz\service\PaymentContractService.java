package com.xinghuo.project.biz.service;

import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.project.biz.entity.PaymentContractEntity;
import com.xinghuo.project.biz.model.paymentContract.PaymentContractPagination;
import com.xinghuo.project.biz.model.paymentContract.PaymentContractVO;

import java.util.Date;
import java.util.List;

/**
 * 付款合同服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
public interface PaymentContractService extends BaseService<PaymentContractEntity> {


    /**
     * 获取采购合同列表
     *
     * @param pagination 分页查询条件
     * @return 采购合同列表
     */
    List<PaymentContractEntity> getList(PaymentContractPagination pagination);

    /**
     * 根据收款合同ID获取采购合同列表
     *
     * @param contractId 收款合同ID
     * @return 采购合同列表
     */
    List<PaymentContractEntity> getListByContractId(String contractId);

    /**
     * 根据供应商ID获取采购合同列表
     *
     * @param supplierId 供应商ID
     * @return 采购合同列表
     */
    List<PaymentContractEntity> getListBySupplierId(String supplierId);

    /**
     * 获取采购合同详情
     *
     * @param id 采购合同ID
     * @return 采购合同详情
     */
    PaymentContractEntity getInfo(String id);

    /**
     * 创建采购合同
     *
     * @param entity 采购合同实体
     */
    void create(PaymentContractEntity entity);

    /**
     * 更新采购合同
     *
     * @param id     采购合同ID
     * @param entity 采购合同实体
     */
    void update(String id, PaymentContractEntity entity);

    /**
     * 删除采购合同
     *
     * @param id 采购合同ID
     */
    void delete(String id);

    /**
     * 检查采购合同编号是否已存在
     *
     * @param cNo 采购合同编号
     * @param id  采购合同ID（更新时使用，新增时为null）
     * @return 是否存在
     */
    boolean isExistByCNo(String cNo, String id);

    /**
     * 填充关联信息
     *
     * @param listVOs 采购合同视图对象列表
     */
    void fillRelatedInfo(List<PaymentContractVO> listVOs);

    /**
     * 更新采购合同状态
     *
     * @param id     采购合同ID
     * @param status 采购合同状态
     */
    void updateStatus(String id, String status);

    /**
     * 签订采购合同
     *
     * @param id       采购合同ID
     * @param cNo      采购合同编号
     * @param signDate 签订日期
     */
    void sign(String id, String cNo, Date signDate);

    /**
     * 检查采购合同是否可以删除
     *
     * @param id 采购合同ID
     * @return 是否可以删除
     */
    boolean canDelete(String id);
}
