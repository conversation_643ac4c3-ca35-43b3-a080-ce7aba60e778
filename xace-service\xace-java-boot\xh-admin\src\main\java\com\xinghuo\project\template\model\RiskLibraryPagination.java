package com.xinghuo.project.template.model;

import com.xinghuo.common.base.model.Pagination;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 标准项目风险库分页查询参数
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RiskLibraryPagination extends Pagination {

    /**
     * 风险编码
     */
    private String code;

    /**
     * 风险标题/名称
     */
    private String title;

    /**
     * 发布状态 (如: draft 草稿, published 已发布, archived 归档)
     */
    private String status;

    /**
     * 关键字搜索（编码或标题）
     */
    private String keyword;

    /**
     * 创建时间开始
     */
    private Date createTimeStart;

    /**
     * 创建时间结束
     */
    private Date createTimeEnd;

    /**
     * 风险类别ID (关联字典表, 如: 技术, 成本, 供应链, 法规)
     */
    private String riskCategoryId;

    /**
     * 默认概率等级ID (关联字典表, 如: 1-5级)
     */
    private String defaultProbabilityLevelId;

    /**
     * 默认影响等级ID (关联字典表, 如: 1-5级)
     */
    private String defaultImpactLevelId;

    /**
     * 创建用户ID
     */
    private String createdBy;

    /**
     * 风险描述关键字
     */
    private String descriptionKeyword;

    /**
     * 应对策略关键字
     */
    private String strategyKeyword;

    /**
     * 应对措施关键字
     */
    private String actionsKeyword;
}
