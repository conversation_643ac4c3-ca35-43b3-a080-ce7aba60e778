<template>
  <a-select
    v-model:value="currentValue"
    :placeholder="placeholder"
    :loading="loading"
    :allowClear="allowClear"
    :disabled="disabled"
    show-search
    :filter-option="false"
    @search="handleSearch"
    @change="handleChange"
    @clear="handleClear">
    <a-select-option v-for="item in options" :key="item.id" :value="item.id">
      {{ item.fullName }}
    </a-select-option>
  </a-select>
</template>

<script lang="ts" setup>
  import { ref, watch, onMounted } from 'vue';
  import { useDebounceFn } from '@vueuse/core';
  import { getSupplierSelector, type SupplierSelectorOption } from '/@/api/project/supplier';

  // 使用 API 中定义的类型
  type SupplierOption = SupplierSelectorOption;

  interface Props {
    value?: string;
    placeholder?: string;
    allowClear?: boolean;
    disabled?: boolean;
  }

  interface Emits {
    (e: 'update:value', value: string | undefined): void;
    (e: 'change', value: string | undefined, option?: SupplierOption): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    placeholder: '请选择供应商',
    allowClear: true,
    disabled: false,
  });

  const emit = defineEmits<Emits>();

  const currentValue = ref<string | undefined>(props.value);
  const options = ref<SupplierOption[]>([]);
  const loading = ref(false);

  // 加载选项数据
  const loadOptions = async (keyword?: string) => {
    if (loading.value) {
      return; // 防止重复请求
    }

    try {
      loading.value = true;
      const result = await getSupplierSelector(keyword);

      console.log('SupplierSelect: API 返回原始数据:', result);

      // 处理 API 返回的数据格式
      if (result && typeof result === 'object') {
        if (Array.isArray(result.data)) {
          // 如果 API 返回的是 { data: [...] } 格式
          options.value = result.data;
        } else if (Array.isArray(result)) {
          // 如果 API 直接返回数组
          options.value = result;
        } else {
          console.warn('SupplierSelect: API 返回的数据格式不正确', result);
          options.value = [];
        }
      } else if (Array.isArray(result)) {
        // 如果 result 直接是数组
        options.value = result;
      } else {
        options.value = [];
      }

      console.log('SupplierSelect: 加载选项完成', {
        keyword,
        optionsCount: options.value.length,
        currentValue: currentValue.value,
        processedOptions: options.value,
      });

      // 如果有当前值但在新加载的选项中找不到，打印调试信息
      if (currentValue.value && options.value.length > 0 && !options.value.find(item => item.id === currentValue.value)) {
        console.log('SupplierSelect: 当前值在选项中未找到', {
          currentValue: currentValue.value,
          options: options.value,
          keyword,
        });
      }
    } catch (error) {
      console.error('加载供应商选项失败:', error);
      options.value = [];
    } finally {
      loading.value = false;
    }
  };

  // 防抖搜索
  const debouncedSearch = useDebounceFn(loadOptions, 300);

  // 监听外部值变化
  watch(
    () => props.value,
    async (newVal, oldVal) => {
      currentValue.value = newVal;

      // 如果有新值且与旧值不同，需要确保选项列表中包含该值
      if (newVal && newVal !== oldVal) {
        // 如果选项列表中没有对应的选项，需要加载选项
        if (!options.value.find(item => item.id === newVal)) {
          console.log('SupplierSelect: 加载选项以包含新值', newVal);
          await loadOptions();
        }
      }
    },
    { immediate: true },
  );

  // 搜索处理
  function handleSearch(value: string) {
    if (value) {
      debouncedSearch(value);
    } else {
      loadOptions();
    }
  }

  // 值变化处理
  function handleChange(value: string | undefined) {
    currentValue.value = value;
    emit('update:value', value);

    const selectedOption = options.value.find(item => item.id === value);
    emit('change', value, selectedOption);
  }

  // 清空处理
  function handleClear() {
    currentValue.value = undefined;
    emit('update:value', undefined);
    emit('change', undefined);
  }

  // 组件挂载时加载初始数据
  onMounted(() => {
    loadOptions();
  });
</script>
