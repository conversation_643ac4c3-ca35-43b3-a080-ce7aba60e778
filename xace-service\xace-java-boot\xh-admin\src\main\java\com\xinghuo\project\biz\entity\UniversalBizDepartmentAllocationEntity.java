package com.xinghuo.project.biz.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 通用业务分部分配实体
 * 支持项目/商机/合同/收付款的统一分部分配管理
 * 
 * <AUTHOR>
 * @version V3.0
 * @copyright 易趋集团
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_department_allocation")
public class UniversalBizDepartmentAllocationEntity extends BaseEntityV2.CUDBaseEntityV2<String> {

    // ==================== 业务关联字段 ====================
    
    /**
     * 项目基础ID（如果有的话）
     */
    @TableField("F_PROJ_BASE_ID")
    private String projBaseId;
    
    /**
     * 商机ID
     */
    @TableField("F_OPPORTUNITY_ID")
    private String opportunityId;

    /**
     * 合同ID
     */
    @TableField("F_CONTRACT_ID")
    private String contractId;

    /**
     * 收款ID
     */
    @TableField("F_RECEIVABLE_ID")
    private String receivableId;

    /**
     * 付款ID
     */
    @TableField("F_PAYMENT_ID")
    private String paymentId;

    /**
     * 主业务ID（冗余字段，便于查询）
     */
    @TableField("F_BUSINESS_ID")
    private String businessId;

    /**
     * 业务类型（1-商机 2-合同 3-收款 4-付款）
     */
    @TableField("F_BUSINESS_TYPE")
    private Integer businessType;

    // ==================== 分部信息字段 ====================
    
    /**
     * 所属分部ID
     */
    @TableField("F_DEPT_ID")
    private String deptId;

    /**
     * 研发分部ID
     */
    @TableField("F_YF_DEPT_ID")
    private String yfDeptId;

    // ==================== 4个分部的营收分配 ====================
    
    /**
     * 一部营收分配
     */
    @TableField("F_YF_YB_AMOUNT")
    private BigDecimal yfYbAmount;

    /**
     * 二部营收分配
     */
    @TableField("F_YF_EB_AMOUNT")
    private BigDecimal yfEbAmount;

    /**
     * 交付营收分配
     */
    @TableField("F_YF_JF_AMOUNT")
    private BigDecimal yfJfAmount;

    /**
     * 综合营收分配
     */
    @TableField("F_YF_OTHER_AMOUNT")
    private BigDecimal yfOtherAmount;

    // ==================== 4个分部的外采分配 ====================
    
    /**
     * 一部外采分配
     */
    @TableField("F_OUT_YB_AMOUNT")
    private BigDecimal outYbAmount;

    /**
     * 二部外采分配
     */
    @TableField("F_OUT_EB_AMOUNT")
    private BigDecimal outEbAmount;

    /**
     * 交付外采分配
     */
    @TableField("F_OUT_JF_AMOUNT")
    private BigDecimal outJfAmount;

    /**
     * 综合外采分配
     */
    @TableField("F_OUT_OTHER_AMOUNT")
    private BigDecimal outOtherAmount;

    // ==================== 其他必要字段 ====================
    
    /**
     * 分配日期
     */
    @TableField("F_ALLOCATION_DATE")
    private LocalDate allocationDate;

    /**
     * 分配状态（1-有效 0-无效）
     */
    @TableField("F_ALLOCATION_STATUS")
    private Integer allocationStatus;

    /**
     * 分配比例（%）
     */
    @TableField("F_ALLOCATION_RATIO")
    private BigDecimal allocationRatio;

    /**
     * 分配备注
     */
    @TableField("F_REMARKS")
    private String remarks;

    // ==================== 业务方法 ====================

    /**
     * 获取分部营收分配总额
     */
    public BigDecimal getTotalYfAmount() {
        return (yfYbAmount != null ? yfYbAmount : BigDecimal.ZERO)
            .add(yfEbAmount != null ? yfEbAmount : BigDecimal.ZERO)
            .add(yfJfAmount != null ? yfJfAmount : BigDecimal.ZERO)
            .add(yfOtherAmount != null ? yfOtherAmount : BigDecimal.ZERO);
    }

    /**
     * 获取分部外采分配总额
     */
    public BigDecimal getTotalOutAmount() {
        return (outYbAmount != null ? outYbAmount : BigDecimal.ZERO)
            .add(outEbAmount != null ? outEbAmount : BigDecimal.ZERO)
            .add(outJfAmount != null ? outJfAmount : BigDecimal.ZERO)
            .add(outOtherAmount != null ? outOtherAmount : BigDecimal.ZERO);
    }

    /**
     * 获取指定分部的营收分配
     */
    public BigDecimal getYfAmountByDept(String deptCode) {
        switch (deptCode.toUpperCase()) {
            case "YB": return yfYbAmount != null ? yfYbAmount : BigDecimal.ZERO;
            case "EB": return yfEbAmount != null ? yfEbAmount : BigDecimal.ZERO;
            case "JF": return yfJfAmount != null ? yfJfAmount : BigDecimal.ZERO;
            case "OTHER": case "ZH": return yfOtherAmount != null ? yfOtherAmount : BigDecimal.ZERO;
            default: return BigDecimal.ZERO;
        }
    }

    /**
     * 获取指定分部的外采分配
     */
    public BigDecimal getOutAmountByDept(String deptCode) {
        switch (deptCode.toUpperCase()) {
            case "YB": return outYbAmount != null ? outYbAmount : BigDecimal.ZERO;
            case "EB": return outEbAmount != null ? outEbAmount : BigDecimal.ZERO;
            case "JF": return outJfAmount != null ? outJfAmount : BigDecimal.ZERO;
            case "OTHER": case "ZH": return outOtherAmount != null ? outOtherAmount : BigDecimal.ZERO;
            default: return BigDecimal.ZERO;
        }
    }

    /**
     * 获取业务类型名称
     */
    public String getBusinessTypeName() {
        switch (businessType) {
            case 1: return "商机";
            case 2: return "合同";
            case 3: return "收款";
            case 4: return "付款";
            default: return "未知";
        }
    }

    /**
     * 获取主要业务关联ID
     */
    public String getPrimaryBusinessId() {
        if (opportunityId != null) return opportunityId;
        if (contractId != null) return contractId;
        if (receivableId != null) return receivableId;
        if (paymentId != null) return paymentId;
        return businessId;
    }

    /**
     * 判断是否为商机分配
     */
    public boolean isOpportunityAllocation() {
        return businessType != null && businessType == 1;
    }

    /**
     * 判断是否为合同分配
     */
    public boolean isContractAllocation() {
        return businessType != null && businessType == 2;
    }

    /**
     * 判断是否为收款分配
     */
    public boolean isReceivableAllocation() {
        return businessType != null && businessType == 3;
    }

    /**
     * 判断是否为付款分配
     */
    public boolean isPaymentAllocation() {
        return businessType != null && businessType == 4;
    }

    /**
     * 获取交付分部的完整信息
     */
    public DeptAllocationInfo getJfDeptInfo() {
        DeptAllocationInfo info = new DeptAllocationInfo();
        info.setDeptCode("JF");
        info.setDeptName("交付");
        info.setYfAmount(this.yfJfAmount);
        info.setOutAmount(this.outJfAmount);
        info.setBusinessType(this.getBusinessTypeName());
        return info;
    }

    /**
     * 获取所有分部的分配信息
     */
    public java.util.List<DeptAllocationInfo> getAllDeptInfo() {
        java.util.List<DeptAllocationInfo> list = new java.util.ArrayList<>();
        
        list.add(createDeptInfo("YB", "一部", yfYbAmount, outYbAmount));
        list.add(createDeptInfo("EB", "二部", yfEbAmount, outEbAmount));
        list.add(createDeptInfo("JF", "交付", yfJfAmount, outJfAmount));
        list.add(createDeptInfo("OTHER", "综合", yfOtherAmount, outOtherAmount));
        
        return list;
    }

    private DeptAllocationInfo createDeptInfo(String code, String name, BigDecimal yf, BigDecimal out) {
        DeptAllocationInfo info = new DeptAllocationInfo();
        info.setDeptCode(code);
        info.setDeptName(name);
        info.setYfAmount(yf != null ? yf : BigDecimal.ZERO);
        info.setOutAmount(out != null ? out : BigDecimal.ZERO);
        info.setBusinessType(this.getBusinessTypeName());
        return info;
    }

    /**
     * 验证分配数据有效性
     */
    public boolean validate() {
        // 至少要有一个业务关联ID
        boolean hasBusinessId = opportunityId != null || contractId != null || 
                               receivableId != null || paymentId != null;
        
        // 业务类型必须有效
        boolean validBusinessType = businessType != null && businessType >= 1 && businessType <= 4;
        
        // 至少要有一个分配金额大于0
        boolean hasAllocation = getTotalYfAmount().compareTo(BigDecimal.ZERO) > 0 ||
                               getTotalOutAmount().compareTo(BigDecimal.ZERO) > 0;
        
        return hasBusinessId && validBusinessType && hasAllocation;
    }

    /**
     * 分部分配信息DTO
     */
    @Data
    public static class DeptAllocationInfo {
        private String deptCode;         // 分部编码
        private String deptName;         // 分部名称
        private BigDecimal yfAmount;     // 营收分配
        private BigDecimal outAmount;    // 外采分配
        private String businessType;     // 业务类型
        
        public BigDecimal getTotalAmount() {
            return (yfAmount != null ? yfAmount : BigDecimal.ZERO)
                .add(outAmount != null ? outAmount : BigDecimal.ZERO);
        }
    }
}