package com.xinghuo.project.biz.service;

import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.project.biz.entity.CustomerContactEntity;
import com.xinghuo.project.biz.model.CustomerLinkmanPagination;

import java.util.List;

/**
 * 客户联系人服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
public interface CustomerContactService extends BaseService<CustomerContactEntity> {

    /**
     * 分页查询客户联系人列表
     *
     * @param pagination 查询条件
     * @return 客户联系人列表
     */
    List<CustomerContactEntity> getList(CustomerLinkmanPagination pagination);

    /**
     * 根据客户ID查询联系人列表
     *
     * @param customerId 客户ID
     * @return 联系人列表
     */
    List<CustomerContactEntity> getListByCustomerId(String customerId);

    /**
     * 根据ID查询客户联系人信息
     *
     * @param id 联系人ID
     * @return 联系人信息
     */
    CustomerContactEntity getInfo(String id);

    /**
     * 创建客户联系人
     *
     * @param entity 联系人信息
     * @return 联系人ID
     */
    String create(CustomerContactEntity entity);

    /**
     * 更新客户联系人
     *
     * @param id 联系人ID
     * @param entity 更新信息
     */
    void update(String id, CustomerContactEntity entity);

    /**
     * 删除客户联系人
     *
     * @param id 联系人ID
     */
    void delete(String id);

    /**
     * 更新客户联系人状态
     *
     * @param id 联系人ID
     * @param status 状态
     */
    void updateStatus(String id, Integer status);

    /**
     * 获取客户联系人选择列表
     *
     * @param customerId 客户ID
     * @param keyword 关键字
     * @return 联系人列表
     */
    List<CustomerContactEntity> getSelectList(String customerId, String keyword);
}
