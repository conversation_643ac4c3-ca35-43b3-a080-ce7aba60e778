package com.xinghuo.project.biz.model;

import com.xinghuo.common.base.model.Pagination;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 客户联系人分页查询参数
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "客户联系人分页查询参数")
public class CustomerLinkmanPagination extends Pagination {

    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    private String customerId;

    /**
     * 联系人姓名
     */
    @Schema(description = "联系人姓名")
    private String name;

    /**
     * 联系电话
     */
    @Schema(description = "联系电话")
    private String phone;

    /**
     * 邮箱
     */
    @Schema(description = "邮箱")
    private String email;

    /**
     * 职位
     */
    @Schema(description = "职位")
    private String position;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private Integer status;

    /**
     * 创建时间开始
     */
    @Schema(description = "创建时间开始")
    private Date createTimeStart;

    /**
     * 创建时间结束
     */
    @Schema(description = "创建时间结束")
    private Date createTimeEnd;

    /**
     * 关键字搜索（姓名、电话或邮箱）
     */
    @Schema(description = "关键字搜索")
    private String keyword;
}
