package com.xinghuo.performance.service;

import com.xinghuo.performance.model.analysis.*;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;

/**
 * 绩效报表分析服务接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface PerformanceAnalysisService {

    /**
     * 获取绩效分析概览数据
     * 
     * @param pagination 查询参数
     * @return 概览数据
     */
    PerformanceAnalysisOverviewModel getOverview(PerformanceAnalysisPagination pagination);

    /**
     * 获取绩效分析图表数据
     * 
     * @param pagination 查询参数
     * @return 图表数据
     */
    PerformanceAnalysisChartModel getCharts(PerformanceAnalysisPagination pagination);

    /**
     * 获取个人绩效分析列表
     * 
     * @param pagination 查询参数
     * @return 个人绩效分析数据列表
     */
    List<PersonalPerformanceAnalysisVO> getPersonalAnalysisList(PerformanceAnalysisPagination pagination);

    /**
     * 获取分部绩效分析列表
     * 
     * @param pagination 查询参数
     * @return 分部绩效分析数据列表
     */
    List<DepartmentPerformanceAnalysisVO> getDepartmentAnalysisList(PerformanceAnalysisPagination pagination);

    /**
     * 获取绩效排名列表
     * 
     * @param pagination 查询参数
     * @return 绩效排名数据列表
     */
    List<PerformanceRankingVO> getRankingList(PerformanceAnalysisPagination pagination);

    /**
     * 获取个人绩效详情
     * 
     * @param userId 用户ID
     * @param pagination 查询参数
     * @return 个人绩效详情
     */
    PersonalPerformanceDetailVO getPersonalDetail(String userId, PerformanceAnalysisPagination pagination);

    /**
     * 获取分部绩效详情
     * 
     * @param fbId 分部ID
     * @param pagination 查询参数
     * @return 分部绩效详情
     */
    DepartmentPerformanceDetailVO getDepartmentDetail(String fbId, PerformanceAnalysisPagination pagination);

    /**
     * 获取绩效维度统计
     * 
     * @param pagination 查询参数
     * @return 维度统计数据
     */
    List<PerformanceDimensionStatsVO> getDimensionStats(PerformanceAnalysisPagination pagination);

    /**
     * 获取绩效趋势数据
     * 
     * @param pagination 查询参数
     * @return 趋势数据
     */
    List<PerformanceTrendVO> getTrend(PerformanceAnalysisPagination pagination);

    /**
     * 导出绩效分析报表
     * 
     * @param pagination 查询参数
     * @param response HTTP响应
     */
    void exportAnalysis(PerformanceAnalysisPagination pagination, HttpServletResponse response);
}
