package com.xinghuo.project.core.service;

import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.project.core.entity.ProjectBaseEntity;
import com.xinghuo.project.core.model.ProjectPagination;
import com.xinghuo.project.core.model.dto.ProjectExtendedDTO;
import com.xinghuo.project.core.model.dto.SimpleProjectInfoDTO;
import com.xinghuo.project.core.model.projectBase.ProjectBaseInfoVO;
import com.xinghuo.project.core.model.projectBase.ProjectBaseInfoForm;

import java.util.List;
import java.util.Map;

/**
 * 项目服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
public interface ProjectService extends BaseService<ProjectBaseEntity> {

    /**
     * 分页查询项目列表
     *
     * @param pagination 查询条件
     * @return 项目列表
     */
    List<ProjectBaseEntity> getList(ProjectPagination pagination);

    /**
     * 根据项目类型查询项目列表
     *
     * @param projectType 项目类型
     * @return 项目列表
     */
    List<ProjectBaseEntity> getListByProjectType(String projectType);

    /**
     * 根据项目状态查询项目列表
     *
     * @param status 项目状态
     * @return 项目列表
     */
    List<ProjectBaseEntity> getListByStatus(String status);

    /**
     * 根据项目经理ID查询项目列表
     *
     * @param managerId 项目经理ID
     * @return 项目列表
     */
    List<ProjectBaseEntity> getListByManagerId(String managerId);

    /**
     * 根据部门ID查询项目列表
     *
     * @param deptId 部门ID
     * @return 项目列表
     */
    List<ProjectBaseEntity> getListByDeptId(String deptId);

    /**
     * 根据ID查询项目信息
     *
     * @param id 项目ID
     * @return 项目信息
     */
    ProjectBaseEntity getInfo(String id);

    /**
     * 创建项目
     *
     * @param entity 项目信息
     * @return 项目ID
     */
    String create(ProjectBaseEntity entity);

    /**
     * 更新项目
     *
     * @param id 项目ID
     * @param entity 更新信息
     */
    void update(String id, ProjectBaseEntity entity);

    /**
     * 删除项目
     *
     * @param id 项目ID
     */
    void delete(String id);

    /**
     * 更新项目状态
     *
     * @param id 项目ID
     * @param status 项目状态
     */
    void updateStatus(String id, String status);

    /**
     * 更新项目健康度
     *
     * @param id 项目ID
     * @param health 项目健康度
     */
    void updateHealth(String id, String health);

    /**
     * 检查项目编码是否存在
     *
     * @param code 项目编码
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean isExistByCode(String code, String excludeId);

    /**
     * 获取项目统计数据
     *
     * @param params 查询参数
     * @return 统计数据
     */
    List<Map<String, Object>> getProjectStatistics(Map<String, Object> params);

    /**
     * 获取项目健康度统计
     *
     * @param params 查询参数
     * @return 健康度统计
     */
    List<Map<String, Object>> getProjectHealthStatistics(Map<String, Object> params);

    /**
     * 项目归档
     *
     * @param id 项目ID
     */
    void archiveProject(String id);

    /**
     * 项目激活
     *
     * @param id 项目ID
     */
    void activateProject(String id);

    /**
     * 获取项目简要信息
     *
     * @param projectId 项目ID
     * @return 项目简要信息
     */
    SimpleProjectInfoDTO getSimpleProjectInfo(String projectId);

    /**
     * 获取项目扩展信息
     *
     * @param projectId 项目ID
     * @return 项目扩展信息
     */
    ProjectExtendedDTO getExtendedProjectInfo(String projectId);

    /**
     * 项目搜索
     *
     * @param searchParams 搜索参数
     * @return 项目列表
     */
    List<ProjectExtendedDTO> searchProjects(Map<String, Object> searchParams);

    /**
     * 快速创建项目
     *
     * @param projectInfo 项目信息
     * @param teamList 团队信息
     * @param createOptions 创建选项
     * @return 项目ID
     */
    String saveFastNew(ProjectBaseEntity projectInfo, List<Map<String, Object>> teamList, Map<String, Object> createOptions);

    /**
     * 获取我参与的项目
     *
     * @param userId 用户ID
     * @param params 查询参数
     * @return 项目列表
     */
    List<ProjectExtendedDTO> getMyParticipatedProjects(String userId, Map<String, Object> params);

    /**
     * 获取我管理的项目
     *
     * @param userId 用户ID
     * @param params 查询参数
     * @return 项目列表
     */
    List<ProjectExtendedDTO> getMyManagedProjects(String userId, Map<String, Object> params);

    /**
     * 获取我关注的项目
     *
     * @param userId 用户ID
     * @param params 查询参数
     * @return 项目列表
     */
    List<ProjectExtendedDTO> getMyFollowedProjects(String userId, Map<String, Object> params);

    /**
     * 获取最近访问的项目
     *
     * @param userId 用户ID
     * @param params 查询参数
     * @return 项目列表
     */
    List<ProjectExtendedDTO> getRecentlyVisitedProjects(String userId, Map<String, Object> params);

    /**
     * 更新项目访问记录
     *
     * @param projectId 项目ID
     * @param userId 用户ID
     */
    void updateProjectVisitRecord(String projectId, String userId);

    /**
     * 关注/取消关注项目
     *
     * @param projectId 项目ID
     * @param userId 用户ID
     * @param follow 是否关注
     */
    void followProject(String projectId, String userId, boolean follow);

    /**
     * 收藏/取消收藏项目
     *
     * @param projectId 项目ID
     * @param userId 用户ID
     * @param favorite 是否收藏
     */
    void favoriteProject(String projectId, String userId, boolean favorite);

    /**
     * 获取项目基本信息 (用于项目信息页面)
     *
     * @param projectId 项目ID
     * @return 项目基本信息VO
     */
    ProjectBaseInfoVO getProjectBasicInfo(String projectId);

    /**
     * 更新项目基本信息 (用于项目信息页面)
     *
     * @param projectId 项目ID
     * @param form 项目基本信息表单
     */
    void updateProjectBasicInfo(String projectId, ProjectBaseInfoForm form);
}
