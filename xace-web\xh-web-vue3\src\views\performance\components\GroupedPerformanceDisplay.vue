<template>
  <div class="grouped-performance-display">
    <!-- 显示模式切换 -->
    <a-card size="small" class="display-mode-card">
      <template #title>
        <div class="mode-title">
          <Icon icon="ant-design:appstore-outlined" class="title-icon" />
          <span>显示模式</span>
        </div>
      </template>

      <template #extra>
        <a-space>
          <a-button @click="handleExportAll" :loading="exportLoading" size="small">
            <template #icon><Icon icon="ant-design:download-outlined" /></template>
            导出全部
          </a-button>
          <a-button @click="handleRefresh" :loading="loading" size="small">
            <template #icon><Icon icon="ant-design:reload-outlined" /></template>
            刷新
          </a-button>
        </a-space>
      </template>

      <a-radio-group v-model:value="displayMode" @change="handleDisplayModeChange">
        <a-radio-button value="list">列表模式</a-radio-button>
        <a-radio-button value="group">分组模式</a-radio-button>
        <a-radio-button value="department">部门模式</a-radio-button>
      </a-radio-group>

      <!-- 分组统计概览 -->
      <div class="group-stats" v-if="displayMode === 'group' && groupStats.length > 0">
        <a-divider orientation="left" orientation-margin="0">
          <span class="stats-title">分组统计</span>
        </a-divider>
        <a-row :gutter="16">
          <a-col :span="6" v-for="stat in groupStats" :key="stat.groupId">
            <a-statistic
              :title="stat.groupName || '未分组'"
              :value="stat.averageScore"
              :precision="1"
              suffix="分"
              :value-style="{ color: getScoreColor(stat.averageScore) }">
              <template #prefix>
                <Icon icon="ant-design:team-outlined" />
              </template>
              <template #suffix>
                <div class="stat-detail">
                  <span>分</span>
                  <div class="stat-count">({{ stat.count }}人)</div>
                </div>
              </template>
            </a-statistic>
          </a-col>
        </a-row>
      </div>
    </a-card>

    <!-- 列表模式 -->
    <a-card v-if="displayMode === 'list'" size="small" class="list-mode-card">
      <template #title>
        <span>绩效列表</span>
      </template>

      <BasicTable :columns="listColumns" :data-source="performanceData" :pagination="pagination" :loading="loading" @change="handleTableChange" />
    </a-card>

    <!-- 分组模式 -->
    <div v-if="displayMode === 'group'" class="group-mode">
      <a-collapse v-model:activeKey="activeGroupKeys" class="group-collapse">
        <a-collapse-panel v-for="group in groupedData" :key="group.groupId" class="group-panel">
          <template #header>
            <div class="group-header">
              <div class="group-info">
                <Icon icon="ant-design:team-outlined" class="group-icon" />
                <span class="group-name">{{ group.groupName || '未分组' }}</span>
                <a-tag color="blue" size="small">{{ group.members.length }}人</a-tag>
              </div>
              <div class="group-stats-inline">
                <a-statistic
                  title="平均分"
                  :value="group.averageScore"
                  :precision="1"
                  size="small"
                  :value-style="{ color: getScoreColor(group.averageScore), fontSize: '14px' }" />
              </div>
            </div>
          </template>

          <BasicTable :columns="groupColumns" :data-source="group.members" :pagination="false" size="small" :scroll="{ x: 800 }" />
        </a-collapse-panel>
      </a-collapse>
    </div>

    <!-- 部门模式 -->
    <div v-if="displayMode === 'department'" class="department-mode">
      <a-collapse v-model:activeKey="activeDeptKeys" class="department-collapse">
        <a-collapse-panel v-for="dept in departmentData" :key="dept.fbId" class="department-panel">
          <template #header>
            <div class="department-header">
              <div class="department-info">
                <Icon icon="ant-design:apartment-outlined" class="department-icon" />
                <span class="department-name">{{ dept.fbName }}</span>
                <a-tag color="green" size="small">{{ dept.members.length }}人</a-tag>
              </div>
              <div class="department-stats-inline">
                <a-statistic
                  title="部门平均分"
                  :value="dept.averageScore"
                  :precision="1"
                  size="small"
                  :value-style="{ color: getScoreColor(dept.averageScore), fontSize: '14px' }" />
              </div>
            </div>
          </template>

          <!-- 部门内分组显示 -->
          <div class="department-groups">
            <a-collapse size="small">
              <a-collapse-panel v-for="group in dept.groups" :key="`${dept.fbId}-${group.groupId}`" class="inner-group-panel">
                <template #header>
                  <div class="inner-group-header">
                    <Icon icon="ant-design:team-outlined" class="inner-group-icon" />
                    <span>{{ group.groupName || '未分组' }}</span>
                    <a-tag size="small">{{ group.members.length }}人</a-tag>
                    <span class="inner-group-score">平均: {{ group.averageScore.toFixed(1) }}分</span>
                  </div>
                </template>

                <BasicTable :columns="groupColumns" :data-source="group.members" :pagination="false" size="small" />
              </a-collapse-panel>
            </a-collapse>
          </div>
        </a-collapse-panel>
      </a-collapse>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed, onMounted, watch } from 'vue';
  import { Icon } from '/@/components/Icon';
  import { BasicTable } from '/@/components/Table';
  import { useMessage } from '/@/hooks/web/useMessage';

  interface PerformanceRecord {
    id: string;
    userId: string;
    userName: string;
    fbId: string;
    fbName: string;
    groupId?: string;
    groupName?: string;
    actScore: number;
    parentUserName: string;
    scoreNote: string;
    month: string;
  }

  interface GroupData {
    groupId: string;
    groupName: string;
    members: PerformanceRecord[];
    averageScore: number;
  }

  interface DepartmentData {
    fbId: string;
    fbName: string;
    members: PerformanceRecord[];
    groups: GroupData[];
    averageScore: number;
  }

  interface GroupStats {
    groupId: string;
    groupName: string;
    count: number;
    averageScore: number;
  }

  const props = defineProps({
    dataSource: {
      type: Array as PropType<PerformanceRecord[]>,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits(['refresh', 'export']);

  const { createMessage } = useMessage();

  // 响应式数据
  const displayMode = ref('list');
  const activeGroupKeys = ref<string[]>([]);
  const activeDeptKeys = ref<string[]>([]);
  const exportLoading = ref(false);

  const pagination = reactive({
    current: 1,
    pageSize: 20,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
  });

  // 表格列配置
  const listColumns = [
    { title: '所属部门', dataIndex: 'fbName', width: 120, align: 'center' },
    { title: '所在分组', dataIndex: 'groupName', width: 120, align: 'center' },
    { title: '员工姓名', dataIndex: 'userName', width: 120, align: 'center' },
    { title: '绩效得分', dataIndex: 'actScore', width: 120, align: 'center' },
    { title: '考核人', dataIndex: 'parentUserName', width: 180 },
    { title: '考核信息', dataIndex: 'scoreNote', ellipsis: true },
  ];

  const groupColumns = [
    { title: '员工姓名', dataIndex: 'userName', width: 120, align: 'center' },
    { title: '绩效得分', dataIndex: 'actScore', width: 120, align: 'center' },
    { title: '考核人', dataIndex: 'parentUserName', width: 180 },
    { title: '考核信息', dataIndex: 'scoreNote', ellipsis: true },
  ];

  // 计算属性
  const groupedData = computed<GroupData[]>(() => {
    const groups = new Map<string, PerformanceRecord[]>();

    props.dataSource.forEach(record => {
      const groupKey = record.groupId || 'ungrouped';
      if (!groups.has(groupKey)) {
        groups.set(groupKey, []);
      }
      groups.get(groupKey)!.push(record);
    });

    return Array.from(groups.entries())
      .map(([groupId, members]) => {
        const averageScore = members.reduce((sum, member) => sum + member.actScore, 0) / members.length;
        const groupName = members[0]?.groupName || '未分组';

        return {
          groupId,
          groupName,
          members: members.sort((a, b) => b.actScore - a.actScore),
          averageScore: Number(averageScore.toFixed(1)),
        };
      })
      .sort((a, b) => b.averageScore - a.averageScore);
  });

  const departmentData = computed<DepartmentData[]>(() => {
    const departments = new Map<string, PerformanceRecord[]>();

    props.dataSource.forEach(record => {
      const deptKey = record.fbId || 'unknown';
      if (!departments.has(deptKey)) {
        departments.set(deptKey, []);
      }
      departments.get(deptKey)!.push(record);
    });

    return Array.from(departments.entries())
      .map(([fbId, members]) => {
        const fbName = members[0]?.fbName || '未知部门';
        const averageScore = members.reduce((sum, member) => sum + member.actScore, 0) / members.length;

        // 按分组分类部门内成员
        const groups = new Map<string, PerformanceRecord[]>();
        members.forEach(member => {
          const groupKey = member.groupId || 'ungrouped';
          if (!groups.has(groupKey)) {
            groups.set(groupKey, []);
          }
          groups.get(groupKey)!.push(member);
        });

        const groupData = Array.from(groups.entries())
          .map(([groupId, groupMembers]) => {
            const groupAverageScore = groupMembers.reduce((sum, member) => sum + member.actScore, 0) / groupMembers.length;
            const groupName = groupMembers[0]?.groupName || '未分组';

            return {
              groupId,
              groupName,
              members: groupMembers.sort((a, b) => b.actScore - a.actScore),
              averageScore: Number(groupAverageScore.toFixed(1)),
            };
          })
          .sort((a, b) => b.averageScore - a.averageScore);

        return {
          fbId,
          fbName,
          members: members.sort((a, b) => b.actScore - a.actScore),
          groups: groupData,
          averageScore: Number(averageScore.toFixed(1)),
        };
      })
      .sort((a, b) => b.averageScore - a.averageScore);
  });

  const groupStats = computed<GroupStats[]>(() => {
    return groupedData.value.map(group => ({
      groupId: group.groupId,
      groupName: group.groupName,
      count: group.members.length,
      averageScore: group.averageScore,
    }));
  });

  const performanceData = computed(() => {
    // eslint-disable-next-line vue/no-mutating-props, vue/no-side-effects-in-computed-properties
    return props.dataSource.sort((a, b) => b.actScore - a.actScore);
  });

  // 生命周期
  onMounted(() => {
    // 默认展开前3个分组
    activeGroupKeys.value = groupedData.value.slice(0, 3).map(g => g.groupId);
    activeDeptKeys.value = departmentData.value.slice(0, 2).map(d => d.fbId);
  });

  // 监听器
  watch(
    () => props.dataSource,
    () => {
      pagination.total = props.dataSource.length;
    },
    { immediate: true },
  );

  // 方法
  const handleDisplayModeChange = () => {
    // 切换显示模式时的处理
  };

  const handleTableChange = (pag: any) => {
    pagination.current = pag.current;
    pagination.pageSize = pag.pageSize;
  };

  const handleRefresh = () => {
    emit('refresh');
  };

  const handleExportAll = async () => {
    exportLoading.value = true;
    try {
      emit('export', {
        mode: displayMode.value,
        data: props.dataSource,
      });
      createMessage.success('导出成功');
    } catch (error) {
      createMessage.error('导出失败');
    } finally {
      exportLoading.value = false;
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return '#52c41a';
    if (score >= 80) return '#1890ff';
    if (score >= 70) return '#faad14';
    return '#ff4d4f';
  };
</script>

<style lang="less" scoped>
  // 响应式设计
  @media (max-width: 768px) {
    .grouped-performance-display {
      .group-stats {
        .ant-col {
          margin-bottom: 16px;
        }
      }

      .group-header,
      .department-header {
        flex-direction: column;
        align-items: flex-start !important;
        gap: 8px;

        .group-stats-inline,
        .department-stats-inline {
          margin-right: 0 !important;
        }
      }
    }
  }
  .grouped-performance-display {
    .display-mode-card {
      margin-bottom: 16px;

      .mode-title {
        display: flex;
        align-items: center;
        gap: 8px;

        .title-icon {
          color: #1890ff;
          font-size: 16px;
        }
      }

      .group-stats {
        margin-top: 16px;

        .stats-title {
          font-size: 12px;
          color: #666;
          font-weight: 500;
        }

        .stat-detail {
          display: flex;
          flex-direction: column;
          align-items: center;

          .stat-count {
            font-size: 10px;
            color: #999;
            margin-top: 2px;
          }
        }
      }
    }

    .list-mode-card {
      margin-bottom: 16px;
    }

    .group-mode,
    .department-mode {
      .group-collapse,
      .department-collapse {
        .group-panel,
        .department-panel {
          margin-bottom: 12px;
          border: 1px solid #f0f0f0;
          border-radius: 6px;

          .group-header,
          .department-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;

            .group-info,
            .department-info {
              display: flex;
              align-items: center;
              gap: 8px;

              .group-icon,
              .department-icon {
                color: #1890ff;
                font-size: 16px;
              }

              .group-name,
              .department-name {
                font-weight: 500;
                color: #333;
              }
            }

            .group-stats-inline,
            .department-stats-inline {
              margin-right: 20px;
            }
          }
        }
      }

      .department-groups {
        .inner-group-panel {
          margin-bottom: 8px;

          .inner-group-header {
            display: flex;
            align-items: center;
            gap: 8px;

            .inner-group-icon {
              color: #52c41a;
              font-size: 14px;
            }

            .inner-group-score {
              margin-left: auto;
              font-size: 12px;
              color: #666;
            }
          }
        }
      }
    }
  }
</style>
