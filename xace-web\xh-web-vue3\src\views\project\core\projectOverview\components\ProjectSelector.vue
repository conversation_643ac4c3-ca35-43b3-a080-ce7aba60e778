<template>
  <a-popover overlayClassName="project-selector-popover" placement="bottomLeft" v-model:visible="showPicker" trigger="click" v-bind="$attrs">
    <template #content>
      <div class="!w-[960px] overflow-hidden !h-[calc(100vh-300px)]">
        <BasicTable @register="registerTable" @fetch-success="fetchSuccess" @row-click="onRowClick">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'selectStatus'">
              <a-radio @change="changeSelectStatus(record)" :checked="record.selectStatus" />
            </template>
            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'typeId'">
              <a-tag color="blue">{{ getTypeText(record.typeId) }}</a-tag>
            </template>
          </template>
        </BasicTable>
      </div>
    </template>
    <a-input class="!w-[400px]" readonly v-model:value="projectName" placeholder="请选择项目">
      <template #prefix>
        <i class="icon-ym icon-ym-project mr-1"></i>
      </template>
      <template #suffix>
        <UpOutlined v-if="showPicker" style="color: rgba(0, 0, 0, 0.45)" />
        <DownOutlined v-else style="color: rgba(0, 0, 0, 0.45)" />
      </template>
    </a-input>
  </a-popover>
</template>

<script setup lang="ts">
  import { ref, onMounted, watch } from 'vue';
  import { DownOutlined, UpOutlined } from '@ant-design/icons-vue';
  import { BasicTable, useTable, BasicColumn, FormProps } from '/@/components/Table';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { getProjectListByType } from '/@/api/project/projectBase';
  import { to } from '/@/utils/xh';
  import { useMessage } from '/@/hooks/web/useMessage';

  const { createMessage } = useMessage();
  const { t } = useI18n();

  const showPicker = ref(false);
  const projectIdInner = ref('');
  const projectName = ref('');

  const emit = defineEmits(['update:value', 'change']);
  const props = defineProps({
    value: {
      type: String,
      default: '',
    },
    queryType: {
      type: String,
      default: 'RECENT_VISITED', // RECENT_VISITED, MY_FAVORITE, MY_MANAGED, ALL
    },
  });

  // 监听外部值变化
  watch(
    () => props.value,
    newVal => {
      if (newVal !== projectIdInner.value) {
        projectIdInner.value = newVal;
        updateProjectName();
      }
    },
    { immediate: true },
  );

  onMounted(() => {
    init();
  });

  async function init() {
    // 如果有传入的项目ID，先设置它
    if (props.value) {
      projectIdInner.value = props.value;
      await updateProjectName();
    }
  }

  async function updateProjectName() {
    if (!projectIdInner.value) {
      projectName.value = '';
      return;
    }

    try {
      const [err, res] = await to(
        getProjectListByType({
          queryType: 'ALL',
          pageSize: 1000,
        }),
      );

      if (!err && res?.data?.list) {
        const project = res.data.list.find(item => item.id === projectIdInner.value);
        if (project) {
          projectName.value = project.fullName || project.name;
        }
      }
    } catch (error) {
      console.warn('获取项目名称失败:', error);
    }
  }

  const columns: BasicColumn[] = [
    { title: '', dataIndex: 'selectStatus', width: 40 },
    { title: '项目编码', dataIndex: 'code', width: 120 },
    { title: '项目名称', dataIndex: 'fullName', width: 250 },
    { title: '项目类型', dataIndex: 'typeId', width: 100 },
    { title: '项目状态', dataIndex: 'status', width: 100 },
    { title: '项目经理', dataIndex: 'managerName', width: 120 },
    { title: '计划开始', dataIndex: 'plannedStartDate', width: 120 },
    { title: '计划结束', dataIndex: 'plannedEndDate', width: 120 },
  ];

  const [registerTable, { getDataSource, reload }] = useTable({
    api: params => {
      // 调用真实的API
      return getProjectListByType({
        ...params,
        queryType: props.queryType,
      });
    },
    columns,
    showTableSetting: false,
    showIndexColumn: false,
    isCanResizeParent: true,
    immediate: true,
    canResize: true,
    useSearchForm: true,
    formConfig: getFormConfig(),
  });

  function fetchSuccess() {
    const dataSource = getDataSource();
    if (dataSource.length > 0) {
      // 如果有指定的项目ID，选中它
      if (props.value) {
        const itemIndex = dataSource.findIndex(item => item.id === props.value);
        if (itemIndex !== -1) {
          dataSource[itemIndex].selectStatus = true;
          projectName.value = dataSource[itemIndex].fullName || dataSource[itemIndex].name;
          return;
        }
      }

      // 否则默认选中第一个（最近访问的）
      dataSource[0].selectStatus = true;
      projectIdInner.value = dataSource[0].id;
      projectName.value = dataSource[0].fullName || dataSource[0].name;
      emit('update:value', dataSource[0].id);
      emit('change', dataSource[0]);
    }
  }

  function onRowClick(record, _index, e) {
    console.log(e, record);
    changeSelectStatus(record);
  }

  function changeSelectStatus(record) {
    getDataSource().forEach(item => {
      item.selectStatus = item.id === record.id;
    });
    projectIdInner.value = record.id;
    projectName.value = record.fullName || record.name;
    emit('update:value', record.id);
    emit('change', record);
    showPicker.value = false;
  }

  function getFormConfig(): Partial<FormProps> {
    return {
      schemas: [
        {
          field: 'keyword',
          label: t('common.keyword'),
          component: 'Input',
          colProps: {
            span: 12,
          },
          componentProps: {
            placeholder: '请输入项目名称或编码',
            submitOnPressEnter: true,
          },
        },
      ],
    };
  }

  function getStatusColor(status: string) {
    const statusMap = {
      PLANNING: 'blue',
      EXECUTING: 'green',
      TRACKING: 'cyan',
      SIGNED: 'purple',
      SUSPENDED: 'orange',
      COMPLETED: 'green',
      CANCELLED: 'red',
    };
    return statusMap[status] || 'default';
  }

  function getStatusText(status: string) {
    const statusMap = {
      PLANNING: '规划中',
      EXECUTING: '执行中',
      TRACKING: '跟踪中',
      SIGNED: '已签约',
      SUSPENDED: '暂停',
      COMPLETED: '已完成',
      CANCELLED: '已取消',
    };
    return statusMap[status] || status;
  }

  function getTypeText(typeId: string) {
    const typeMap = {
      S: '软件项目',
      M: '维护项目',
      I: '基础设施',
      C: '建设项目',
      R: '研发项目',
    };
    return typeMap[typeId] || typeId;
  }
</script>

<style lang="less">
  .project-selector-popover {
    .ant-popover-content {
      padding: 0;
    }

    .ant-popover-inner-content {
      padding: 12px;
    }
  }
</style>
