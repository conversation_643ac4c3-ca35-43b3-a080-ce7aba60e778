import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { h } from 'vue';
import { Tag } from 'ant-design-vue';

// 项目查询列表列配置
export const queryColumns: BasicColumn[] = [
  {
    title: '项目编码',
    dataIndex: 'code',
    width: 120,
    fixed: 'left',
  },
  {
    title: '项目名称',
    dataIndex: 'name',
    width: 200,
    fixed: 'left',
  },
  {
    title: '项目类型',
    dataIndex: 'typeId',
    width: 120,
    customRender: ({ record }) => {
      const typeMap = {
        software: '软件开发',
        integration: '系统集成',
        consulting: '咨询服务',
        maintenance: '运维服务',
      };
      return typeMap[record.typeId] || record.typeId;
    },
  },
  {
    title: '项目经理',
    dataIndex: 'managerId',
    width: 120,
  },
  {
    title: '项目状态',
    dataIndex: 'status',
    width: 100,
  },
  {
    title: '健康度',
    dataIndex: 'health',
    width: 100,
  },
  {
    title: '优先级',
    dataIndex: 'priority',
    width: 100,
  },
  {
    title: '计划开始日期',
    dataIndex: 'plannedStartDate',
    width: 120,
    format: 'date|YYYY-MM-DD',
  },
  {
    title: '计划结束日期',
    dataIndex: 'plannedEndDate',
    width: 120,
    format: 'date|YYYY-MM-DD',
  },
  {
    title: '预算金额',
    dataIndex: 'budgetAmount',
    width: 120,
    format: 'currency',
  },
  {
    title: '最后访问时间',
    dataIndex: 'lastVisitTime',
    width: 150,
    format: 'date|YYYY-MM-DD HH:mm:ss',
    ifShow: false, // 默认隐藏，在最近访问标签页显示
  },
  {
    title: '关注时间',
    dataIndex: 'favoriteTime',
    width: 150,
    format: 'date|YYYY-MM-DD HH:mm:ss',
    ifShow: false, // 默认隐藏，在我关注的标签页显示
  },
];

// 高级搜索表单配置
export const advancedSearchFormSchema: FormSchema[] = [
  {
    field: 'keyword',
    label: '关键字',
    component: 'Input',
    componentProps: {
      placeholder: '请输入项目名称或编码',
    },
    colProps: { span: 8 },
  },
  {
    field: 'status',
    label: '项目状态',
    component: 'Select',
    componentProps: {
      options: [
        { label: '规划中', value: 'planning' },
        { label: '执行中', value: 'executing' },
        { label: '已完成', value: 'completed' },
        { label: '已关闭', value: 'closed' },
        { label: '已取消', value: 'cancelled' },
        { label: '已归档', value: 'archived' },
      ],
      placeholder: '请选择项目状态',
    },
    colProps: { span: 8 },
  },
  {
    field: 'health',
    label: '健康度',
    component: 'Select',
    componentProps: {
      options: [
        { label: '正常', value: 'normal' },
        { label: '预警', value: 'warning' },
        { label: '风险', value: 'risk' },
      ],
      placeholder: '请选择健康度',
    },
    colProps: { span: 8 },
  },
  {
    field: 'priority',
    label: '优先级',
    component: 'Select',
    componentProps: {
      options: [
        { label: '高', value: 'high' },
        { label: '中', value: 'medium' },
        { label: '低', value: 'low' },
      ],
      placeholder: '请选择优先级',
    },
    colProps: { span: 8 },
  },
  {
    field: 'typeId',
    label: '项目类型',
    component: 'Select',
    componentProps: {
      options: [
        { label: '软件开发', value: 'software' },
        { label: '系统集成', value: 'integration' },
        { label: '咨询服务', value: 'consulting' },
        { label: '运维服务', value: 'maintenance' },
      ],
      placeholder: '请选择项目类型',
    },
    colProps: { span: 8 },
  },
  {
    field: 'managerId',
    label: '项目经理',
    component: 'Input',
    componentProps: {
      placeholder: '请输入项目经理ID',
    },
    colProps: { span: 8 },
  },
  {
    field: 'deptId',
    label: '部门',
    component: 'Input',
    componentProps: {
      placeholder: '请输入部门ID',
    },
    colProps: { span: 8 },
  },
  {
    field: '[startDateStart, startDateEnd]',
    label: '计划开始日期',
    component: 'RangePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      placeholder: ['开始日期', '结束日期'],
    },
    colProps: { span: 8 },
  },
  {
    field: '[endDateStart, endDateEnd]',
    label: '计划结束日期',
    component: 'RangePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      placeholder: ['开始日期', '结束日期'],
    },
    colProps: { span: 8 },
  },
  {
    field: '[budgetAmountMin, budgetAmountMax]',
    label: '预算金额范围',
    component: 'InputGroup',
    componentProps: {
      placeholder: ['最小金额', '最大金额'],
    },
    colProps: { span: 8 },
  },
];

// 查询类型配置
export const queryTypeConfig = {
  ALL: {
    title: '最近访问',
    icon: 'ant-design:clock-circle-outlined',
    description: '显示最近访问的项目',
    showColumns: ['lastVisitTime'],
    hideColumns: ['favoriteTime'],
  },
  MY_MANAGED: {
    title: '我管理的',
    icon: 'ant-design:user-outlined',
    description: '显示我作为项目经理的项目',
    showColumns: [],
    hideColumns: ['lastVisitTime', 'favoriteTime'],
  },
  MY_PARTICIPATED: {
    title: '我参与的',
    icon: 'ant-design:team-outlined',
    description: '显示我参与的项目团队',
    showColumns: [],
    hideColumns: ['lastVisitTime', 'favoriteTime'],
  },
  MY_FAVORITE: {
    title: '我关注的',
    icon: 'ant-design:star-outlined',
    description: '显示我关注的项目',
    showColumns: ['favoriteTime'],
    hideColumns: ['lastVisitTime'],
  },
  RECENT_VISITED: {
    title: '最近访问',
    icon: 'ant-design:clock-circle-outlined',
    description: '显示最近访问的项目',
    showColumns: ['lastVisitTime'],
    hideColumns: ['favoriteTime'],
  },
  ADVANCED: {
    title: '高级查询',
    icon: 'ant-design:search-outlined',
    description: '支持复杂查询条件',
    showColumns: [],
    hideColumns: ['lastVisitTime', 'favoriteTime'],
  },
};

// 获取指定查询类型的列配置
export function getColumnsForQueryType(queryType: string): BasicColumn[] {
  const config = queryTypeConfig[queryType];
  if (!config) return queryColumns;

  return queryColumns.map(column => {
    const shouldShow = config.showColumns.includes(column.dataIndex as string);
    const shouldHide = config.hideColumns.includes(column.dataIndex as string);

    return {
      ...column,
      ifShow: shouldShow ? true : shouldHide ? false : column.ifShow !== false,
    };
  });
}

// 项目状态映射
export const statusMap = {
  planning: { text: '规划中', color: 'blue' },
  executing: { text: '执行中', color: 'green' },
  completed: { text: '已完成', color: 'cyan' },
  closed: { text: '已关闭', color: 'gray' },
  cancelled: { text: '已取消', color: 'red' },
  archived: { text: '已归档', color: 'orange' },
};

// 健康度映射
export const healthMap = {
  normal: { text: '正常', color: 'green' },
  warning: { text: '预警', color: 'orange' },
  risk: { text: '风险', color: 'red' },
};

// 优先级映射
export const priorityMap = {
  high: { text: '高', color: 'red' },
  medium: { text: '中', color: 'orange' },
  low: { text: '低', color: 'green' },
};

// 项目类型映射
export const typeMap = {
  software: '软件开发',
  integration: '系统集成',
  consulting: '咨询服务',
  maintenance: '运维服务',
};
