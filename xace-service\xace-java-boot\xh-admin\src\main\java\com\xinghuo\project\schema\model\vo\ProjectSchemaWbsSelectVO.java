package com.xinghuo.project.schema.model.vo;

import lombok.Data;

/**
 * 项目模板WBS计划选择VO类
 * 用于下拉选择等场景
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
@Data
public class ProjectSchemaWbsSelectVO {

    /**
     * WBS计划ID
     */
    private String id;

    /**
     * WBS编码
     */
    private String wbsCode;

    /**
     * WBS名称
     */
    private String name;

    /**
     * 父级ID
     */
    private String parentId;

    /**
     * 父级名称
     */
    private String parentName;

    /**
     * 层级深度
     */
    private Integer level;

    /**
     * 显示顺序
     */
    private Integer seqNo;

    /**
     * 节点类型
     */
    private Integer nodeType;

    /**
     * 节点类型名称
     */
    private String nodeTypeName;

    /**
     * 是否里程碑
     */
    private Integer isMilestone;

    /**
     * 是否里程碑名称
     */
    private String isMilestoneName;

    /**
     * 计划工期
     */
    private String duration;

    /**
     * 责任角色ID
     */
    private String responseRoleId;

    /**
     * 责任角色名称
     */
    private String responseRoleName;

    /**
     * 是否有子节点
     */
    private Boolean hasChildren;

    /**
     * 子节点数量
     */
    private Integer childrenCount;

    /**
     * WBS路径（显示用）
     */
    private String wbsPath;

    /**
     * 层级路径（显示用）
     */
    private String levelPath;

    /**
     * 完整显示名称（包含层级缩进）
     */
    private String displayName;

    /**
     * 是否可选择（某些场景下可能不允许选择父节点）
     */
    private Boolean selectable;

    /**
     * 是否禁用
     */
    private Boolean disabled;

    /**
     * 状态
     */
    private String status;

    /**
     * 状态名称
     */
    private String statusName;

    /**
     * 排序权重
     */
    private Integer sortWeight;
}
