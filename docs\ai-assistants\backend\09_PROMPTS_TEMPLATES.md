# XACE 后端 AI 代码生成提示词模板

本文档提供符合XACE后端开发规范的AI辅助工具提示词模板，确保生成的代码遵循项目的架构标准、命名规范和最佳实践。

> **重要**：本模板基于XACE项目最新的后端开发规范制定，使用前请确保了解 [后端框架规范](../../framework-standards/backend/README.md)。

## 1. Entity 实体类生成模板

```
请根据以下表结构生成符合XACE规范的Entity实体类：
表名：{表名}
业务字段：
- {字段名1} {数据库类型} {Java类型} {描述}
- {字段名2} {数据库类型} {Java类型} {描述}
...

XACE规范要求：
1. 类名：{业务名}Entity，使用大驼峰命名
2. 继承：BaseEntityV2.CUBaseEntityV2<String>（推荐）或 BaseEntityV2.CUDBaseEntityV2<String>（需要逻辑删除）
3. 注解：
   - @Data
   - @EqualsAndHashCode(callSuper = true)
   - @TableName("{表名}")
4. 字段映射：
   - 系统字段（id、tenantId、createdAt等）由基类提供，无需定义
   - 业务字段使用 @TableField("{数据库字段名}") 注解
   - 数据库字段名使用大写下划线命名
5. 字段注释：每个字段添加完整的JavaDoc注释
6. 导入：使用 com.xinghuo.common.base.entity.BaseEntityV2
```

### 示例：生成商品实体类

```
请根据以下表结构生成符合XACE规范的Entity实体类：
表名：product_info
业务字段：
- product_name varchar(100) String 商品名称
- category_id varchar(32) String 分类ID
- price decimal(10,2) BigDecimal 商品价格
- stock int Integer 库存数量
- description text String 商品描述
- status tinyint(1) Integer 状态(1=上架,0=下架)
- image_url varchar(255) String 主图URL

XACE规范要求：
1. 类名：ProductEntity
2. 继承：BaseEntityV2.CUBaseEntityV2<String>
3. 注解：@Data、@EqualsAndHashCode(callSuper = true)、@TableName("product_info")
4. 字段映射：业务字段使用 @TableField 注解，系统字段由基类提供
5. 字段注释：每个字段添加完整的JavaDoc注释
6. 导入：com.xinghuo.common.base.entity.BaseEntityV2
```

## 2. Form 表单对象生成模板

```
请根据以下实体类生成符合XACE规范的Form表单对象：
实体类：{实体类名}
业务字段：
- {字段名1} {类型} {描述} {是否必填} {新增/更新}
- {字段名2} {类型} {描述} {是否必填} {新增/更新}
...

XACE规范要求：
1. 类名：{业务名}Form（推荐合并设计）
2. 设计原则：
   - 优先使用合并Form设计，同时支持创建和更新
   - id字段：新增时为空，更新时必填
   - 密码字段：新增时必填，更新时选填
3. 注解：
   - @Data
   - @Schema(description = "{业务对象}表单对象")
4. 验证注解：
   - @NotBlank(message = "字段不能为空")
   - @NotNull(message = "字段不能为null")
   - @Email(message = "邮箱格式不正确")
   - @Pattern 等验证注解
5. 包路径：com.xinghuo.[模块名].model.[业务功能].form
6. 导入：jakarta.validation.constraints.*（不是javax.*）
```

### 示例：根据实体生成Form

```
请根据以下实体类生成符合XACE规范的Form表单对象：
实体类：ProductEntity
业务字段：
- productName String 商品名称 必填 新增/更新
- categoryId String 分类ID 必填 新增/更新
- price BigDecimal 商品价格 必填 新增/更新
- stock Integer 库存数量 必填 新增/更新
- description String 商品描述 选填 新增/更新
- status Integer 状态(1=上架,0=下架) 必填 新增/更新
- imageUrl String 主图URL 选填 新增/更新

XACE规范要求：
1. 类名：ProductForm（合并设计）
2. id字段：新增时为空，更新时必填
3. 注解：@Data、@Schema(description = "商品表单对象")
4. 验证注解：必填字段使用@NotBlank，BigDecimal使用@NotNull
5. 包路径：com.xinghuo.product.model.product.form
```

## 3. VO 视图对象生成模板

```
请根据以下实体类生成符合XACE规范的VO视图对象：
实体类：{实体类名}
展示字段：
- {基础字段} {类型} {描述} {来源=实体}
- {关联字段} {类型} {描述} {来源=关联查询}
- {计算字段} {类型} {描述} {来源=业务计算}
...

XACE规范要求：
1. 类名：{业务名}VO
2. 包含内容：
   - 实体的所有业务字段
   - 系统字段（id、tenantId、createdAt等）
   - 关联对象的名称字段（如categoryName）
   - 状态转换字段（如statusName）
3. 注解：
   - @Data
   - @Schema(description = "{业务对象}视图对象")
4. 日期格式化：
   - @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
5. 设计原则：
   - 使用扁平化设计，避免复杂嵌套对象
   - 状态字段提供对应的名称字段
6. 包路径：com.xinghuo.[模块名].model.[业务功能].vo
```

### 示例：生成商品VO

```
请根据以下实体类生成符合XACE规范的VO视图对象：
实体类：ProductEntity
展示字段：
- id String 主键ID 来源=实体
- productName String 商品名称 来源=实体
- categoryId String 分类ID 来源=实体
- categoryName String 分类名称 来源=关联查询
- price BigDecimal 商品价格 来源=实体
- stock Integer 库存数量 来源=实体
- status Integer 状态代码 来源=实体
- statusName String 状态名称 来源=业务计算
- createdAt Date 创建时间 来源=实体

XACE规范要求：
1. 类名：ProductVO
2. 注解：@Data、@Schema(description = "商品视图对象")
3. 日期字段使用@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
4. 扁平化设计：使用categoryName而不是嵌套Category对象
5. 状态转换：提供statusName字段供前端直接显示
```

## 4. Pagination 分页对象生成模板

```
请根据以下实体类生成符合XACE规范的Pagination分页查询对象：
实体类：{实体类名}
查询条件：
- {条件字段1} {类型} {描述} {查询方式}
- {条件字段2} {类型} {描述} {查询方式}
...

XACE规范要求：
1. 类名：{业务名}Pagination
2. 继承：com.xinghuo.common.base.model.Pagination
3. 注解：
   - @Data
   - @EqualsAndHashCode(callSuper = true)
   - @Schema(description = "{业务对象}分页查询")
4. 查询字段：
   - 只包含查询条件字段，不包含业务逻辑字段
   - 所有查询字段都是可选的（不添加@NotBlank等验证注解）
   - 关键字搜索字段通常命名为keyword
5. 包路径：com.xinghuo.[模块名].model.[业务功能].pagination
```

### 示例：生成商品Pagination

```
请根据以下实体类生成符合XACE规范的Pagination分页查询对象：
实体类：ProductEntity
查询条件：
- productName String 商品名称 模糊查询
- categoryId String 分类ID 精确查询
- status Integer 状态 精确查询
- keyword String 关键字 模糊查询（商品名称、描述）
- priceMin BigDecimal 最低价格 范围查询
- priceMax BigDecimal 最高价格 范围查询

XACE规范要求：
1. 类名：ProductPagination
2. 继承：Pagination基类
3. 注解：@Data、@EqualsAndHashCode(callSuper = true)、@Schema
4. 查询字段可选：不添加验证注解
5. 关键字搜索：keyword字段用于多字段模糊查询
```

## 5. Mapper 数据访问生成模板

```
请根据以下实体类生成符合XACE规范的Mapper数据访问接口：
实体类：{实体类名}
特殊需求：{是否需要自定义SQL方法}

XACE规范要求：
1. 类名：{业务名}Mapper
2. 继承：XHBaseMapper<{实体类名}>（不是BaseMapper）
3. 注解：@Mapper
4. 设计原则：
   - 仅继承XHBaseMapper，不定义自定义方法
   - 所有业务查询逻辑在Service层使用LambdaQueryWrapper实现
   - 仅在极复杂的多表联查或特殊SQL函数时才考虑自定义SQL
5. 导入：com.xinghuo.common.base.dao.XHBaseMapper
6. 包路径：com.xinghuo.[模块名].mapper

注意：90%以上的查询都应在Service层实现，Mapper保持简洁！
```

### 示例：生成商品Mapper（标准情况）

```
请根据以下实体类生成符合XACE规范的Mapper数据访问接口：
实体类：ProductEntity
特殊需求：无自定义SQL方法

XACE规范要求：
1. 类名：ProductMapper
2. 继承：XHBaseMapper<ProductEntity>
3. 注解：@Mapper
4. 设计：仅继承基类，不定义任何自定义方法
5. 说明：所有查询操作通过Service层的LambdaQueryWrapper实现

期待输出：简洁的Mapper接口，仅包含继承和注解
```

### 示例：生成Mapper（特殊情况-需要自定义SQL）

```
请根据以下实体类生成符合XACE规范的Mapper数据访问接口：
实体类：ReportDataEntity  
特殊需求：需要复杂的多表统计查询（仅当LambdaQueryWrapper无法实现时）

自定义方法：
- selectComplexReport 复杂多表统计查询
- selectDataWithAggregation 带聚合函数的数据查询

XACE规范要求：
1. 类名：ReportDataMapper
2. 继承：XHBaseMapper<ReportDataEntity>
3. 注解：@Mapper
4. 自定义方法：仅在极特殊场景下添加
5. XML文件：自定义SQL写在对应的XML文件中
```

## 6. Service 接口生成模板

```
请根据以下业务需求生成符合XACE规范的Service业务接口：
业务实体：{实体名称}
实体类型：{实体类名}
基本功能：{是否包含CRUD}
特殊功能：
- {功能1} {描述}
- {功能2} {描述}
...

XACE规范要求：
1. 接口名：{实体名}Service
2. 继承：BaseService<{实体类名}>
3. 标准CRUD方法：
   - List<{实体类名}> getList({业务名}Pagination pagination)
   - {实体类名} getInfo(String id)
   - String create({实体类名} entity)
   - boolean update(String id, {实体类名} entity)
   - void delete(String id)
4. 业务方法：
   - 每个方法添加完整的JavaDoc注释
   - 明确参数类型和返回值
   - 说明可能抛出的异常
5. 包路径：com.xinghuo.[模块名].service
6. 导入：com.xinghuo.common.base.service.BaseService
```

### 示例：生成商品Service接口

```
请根据以下业务需求生成符合XACE规范的Service业务接口：
业务实体：Product
实体类型：ProductEntity
基本功能：包含完整CRUD
特殊功能：
- 检查商品名称是否重复
- 批量更新商品状态
- 按分类查询商品
- 更新商品库存

XACE规范要求：
1. 接口名：ProductService
2. 继承：BaseService<ProductEntity>
3. 标准CRUD方法：getList、getInfo、create、update、delete
4. 特殊业务方法：isExistByName、batchUpdateStatus、listByCategory、updateStock
5. 每个方法添加完整的JavaDoc注释
```

## 7. Service 实现类生成模板

```
请根据以下Service接口生成符合XACE规范的Service实现类：
接口：{接口名}
实体类：{实体类名}
Mapper类：{Mapper类名}
依赖服务：
- {依赖项1} {用途}
- {依赖项2} {用途}
...

XACE规范要求：
1. 类名：{接口名}Impl
2. 注解：
   - @Service
   - @Slf4j
3. 继承：BaseServiceImpl<{Mapper类名}, {实体类名}>（不是ExtendedBaseServiceImpl）
4. 依赖注入：
   - 使用@Resource注解（推荐Jakarta EE标准）
   - 每个依赖独占一行，按功能逻辑排序
5. 查询实现：
   - 90%查询使用LambdaQueryWrapper
   - 三元表达式进行null判断
   - 大表查询使用字段选择(.select())
6. 事务管理：
   - 使用@Transactional注解
   - 明确指定传播行为和隔离级别（如需要）
7. 业务逻辑：
   - 业务校验在Service层实现
   - 适当的异常处理
   - 日志记录关键操作
8. BaseEntityV2特性：
   - 充分利用自动填充（创建/更新时间等）
   - 正确使用字段名（getCreatedAt而不是getCreateTime）
```

### 示例：生成商品Service实现类

```
请根据以下Service接口生成符合XACE规范的Service实现类：
接口：ProductService
实体类：ProductEntity
Mapper类：ProductMapper
依赖服务：
- ProductMapper 数据访问
- CategoryService 分类服务
- UserProvider 用户上下文

XACE规范要求：
1. 类名：ProductServiceImpl
2. 注解：@Service、@Slf4j
3. 继承：BaseServiceImpl<ProductMapper, ProductEntity>
4. 依赖注入：使用@Resource，按功能排序
5. 查询实现：使用LambdaQueryWrapper，包含分页、条件查询等标准方法
6. 业务逻辑：名称重复检查、库存更新、状态管理等
7. 事务管理：修改操作添加@Transactional
```

## 8. Controller 生成模板

```
请根据以下Service生成符合XACE规范的RESTful Controller：
业务服务：{Service名称}
实体类型：{实体类名}
API路径：{基础路径}
功能：
- {功能1} {HTTP方法} {子路径}
- {功能2} {HTTP方法} {子路径}
...

XACE规范要求：
1. 类名：{实体名}Controller
2. 注解：
   - @RestController
   - @RequestMapping("/{基础路径}")
   - @Tag(name = "{业务模块}", description = "{业务描述}")
3. 依赖注入：
   - 使用@Resource注解（推荐Jakarta EE标准）
   - 每个依赖独占一行，按功能逻辑排序
4. API设计：
   - 统一返回ActionResult<T>对象
   - 使用Swagger注解进行API文档标注
   - 添加完整的参数校验（@Valid、@RequestBody等）
5. 方法模式：
   - 分页查询：ActionResult<PageListVO<{业务名}VO>>
   - 详情查询：ActionResult<{业务名}VO>
   - 创建操作：ActionResult<String>（返回新建ID）
   - 更新操作：ActionResult<String>（返回更新结果）
   - 删除操作：ActionResult<String>（返回删除结果）
6. 异常处理：使用统一异常处理机制
7. 包路径：com.xinghuo.[模块名].controller
8. 导入：com.xinghuo.common.base.ActionResult
```

### 示例：生成商品Controller

```
请根据以下Service生成符合XACE规范的RESTful Controller：
业务服务：ProductService
实体类型：ProductEntity
API路径：/api/product
功能：
- 分页查询商品列表 GET /
- 获取商品详情 GET /{id}
- 创建商品 POST /
- 更新商品 PUT /{id}
- 删除商品 DELETE /{id}
- 批量更新状态 PUT /status/batch

XACE规范要求：
1. 类名：ProductController
2. 注解：@RestController、@RequestMapping("/api/product")、@Tag
3. 依赖注入：使用@Resource注解注入ProductService
4. 返回类型：统一使用ActionResult包装返回值
5. 参数校验：使用@Valid验证Form对象
6. Swagger文档：每个方法添加@Operation注解
7. 分页查询返回：ActionResult<PageListVO<ProductVO>>
8. 详情查询返回：ActionResult<ProductVO>
```

## 9. 完整 CRUD 结构生成模板

```
请根据以下实体信息生成符合XACE规范的完整CRUD代码结构：
实体信息：
- 表名：{表名}
- 实体类名：{实体类名}
- 主要字段：
  - {字段名1} {类型} {描述}
  - {字段名2} {类型} {描述}
  ...
- 模块包名：{包名}

XACE规范要求生成：
1. Entity 实体类（继承BaseEntityV2.CUBaseEntityV2<String>）
2. Form 表单对象（合并设计，同时支持创建和更新）
3. VO 视图对象（包含所有展示字段）
4. Pagination 分页查询对象（继承Pagination基类）
5. Mapper 接口（仅继承XHBaseMapper，不定义自定义方法）
6. Service 接口（继承BaseService）
7. Service 实现类（继承BaseServiceImpl，使用LambdaQueryWrapper）
8. Controller 类（使用ActionResult，@Resource注入）
9. 遵循XACE项目各层规范和最佳实践
```

### 示例：生成完整商品模块

```
请根据以下实体信息生成符合XACE规范的完整CRUD代码结构：
实体信息：
- 表名：product_info
- 实体类名：ProductEntity
- 主要字段：
  - productName String 商品名称
  - categoryId String 分类ID
  - price BigDecimal 商品价格
  - stock Integer 库存数量
  - description String 商品描述
  - status Integer 状态(1-上架,0-下架)
  - imageUrl String 主图URL
- 模块包名：com.xinghuo.product

XACE规范要求生成：
1. ProductEntity（继承BaseEntityV2.CUBaseEntityV2<String>）
2. ProductForm（合并设计，id字段用于区分新增/更新）
3. ProductVO（包含业务字段和系统字段，支持状态名称转换）
4. ProductPagination（包含查询条件，继承Pagination）
5. ProductMapper（仅继承XHBaseMapper<ProductEntity>）
6. ProductService接口（标准CRUD方法定义）
7. ProductServiceImpl（使用LambdaQueryWrapper实现查询逻辑）
8. ProductController（RESTful API，统一ActionResult返回）
```

## 10. 单元测试生成模板

```
请为以下Service实现类生成符合XACE规范的单元测试：
类名：{Service实现类名}
继承体系：继承BaseServiceImpl<{Mapper类名}, {实体类名}>
需测试方法：
- {方法1} {简要描述}
- {方法2} {简要描述}
...

XACE规范要求：
1. 测试框架：JUnit 5 + Mockito + Spring Boot Test
2. 依赖模拟：使用@Mock注解模拟所有依赖
3. 测试数据：使用BaseEntityV2字段（createdAt、lastUpdatedAt等）
4. 测试场景：
   - 正常业务场景测试
   - 边界条件测试
   - 异常情况测试（DataException等）
   - LambdaQueryWrapper查询逻辑测试
5. 注解使用：
   - @ExtendWith(MockitoExtension.class)
   - @Mock、@InjectMocks
   - @Test、@DisplayName
6. 包路径：src/test/java/com.xinghuo.[模块名].service.impl
7. 导入：使用jakarta.*而不是javax.*
```

### 示例：生成商品服务单元测试

```
请为以下Service实现类生成符合XACE规范的单元测试：
类名：ProductServiceImpl
继承体系：继承BaseServiceImpl<ProductMapper, ProductEntity>
需测试方法：
- getList 分页查询商品列表
- create 创建商品
- update 更新商品
- isExistByName 检查商品名称重复

XACE规范要求：
1. 测试框架：JUnit 5 + Mockito
2. 依赖模拟：模拟ProductMapper和其他Service依赖
3. 测试数据：创建ProductEntity对象，使用BaseEntityV2字段
4. 测试场景：成功创建、名称重复异常、更新不存在的商品等
5. QueryWrapper测试：验证查询条件的正确性
6. 测试类名：ProductServiceImplTest
```

## 11. 根据数据库表生成代码模板

```
请根据以下数据库表结构生成符合XACE规范的Entity类：
CREATE TABLE `{表名}` (
  {表结构SQL}
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='{表注释}';

XACE规范要求：
1. 实体类名：{业务名}Entity，使用大驼峰命名
2. 继承：BaseEntityV2.CUBaseEntityV2<String>（推荐）或BaseEntityV2.CUDBaseEntityV2<String>（需要逻辑删除）
3. 注解：
   - @Data
   - @EqualsAndHashCode(callSuper = true)
   - @TableName("{数据库表名}")
4. 字段映射：
   - 系统字段（id、created_at、last_updated_at等）由BaseEntityV2提供，无需定义
   - 业务字段使用@TableField("{数据库字段名}")注解
   - 字段名使用小驼峰命名，数据库字段名使用下划线命名
5. 类型映射：
   - varchar → String
   - tinyint(1) → Integer（状态字段）
   - datetime → Date
   - decimal → BigDecimal
6. 导入：com.xinghuo.common.base.entity.BaseEntityV2
7. 包路径：com.xinghuo.[模块名].entity
```

### 示例：从建表SQL生成实体

```
请根据以下数据库表结构生成符合XACE规范的Entity类：
CREATE TABLE `user_address` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `user_id` varchar(32) NOT NULL COMMENT '用户ID',
  `recipient_name` varchar(50) NOT NULL COMMENT '收件人姓名',
  `phone` varchar(20) NOT NULL COMMENT '联系电话',
  `province` varchar(20) NOT NULL COMMENT '省份',
  `city` varchar(20) NOT NULL COMMENT '城市',
  `district` varchar(20) NOT NULL COMMENT '区县',
  `detail_address` varchar(200) NOT NULL COMMENT '详细地址',
  `is_default` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否默认地址',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `created_by` varchar(32) NOT NULL COMMENT '创建者',
  `last_updated_at` datetime NOT NULL COMMENT '最后更新时间',
  `last_updated_by` varchar(32) NOT NULL COMMENT '最后更新人',
  `delete_mark` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标记',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户地址表';

XACE规范要求：
1. 类名：UserAddressEntity
2. 继承：BaseEntityV2.CUDBaseEntityV2<String>（支持逻辑删除）
3. 系统字段自动继承：id、createdAt、lastUpdatedAt、createdBy、lastUpdatedBy、deleteMark
4. 业务字段映射：userId、recipientName、phone、province、city、district、detailAddress、isDefault
5. 注解：@TableField("{数据库字段名}")用于所有业务字段
6. 注释：每个字段添加完整的JavaDoc注释
```

## 12. 从Excel表格描述生成代码模板

```
请根据以下Excel表格描述生成符合XACE规范的Entity类：
表名称：{表名}
表描述：{表描述}

| 列名 | 数据类型 | 是否必填 | 描述 |
|------|----------|----------|------|
| {列名1} | {类型1} | {是/否} | {描述1} |
| {列名2} | {类型2} | {是/否} | {描述2} |
...

XACE规范要求：
1. 实体类名：根据表名称转换为驼峰命名 + Entity后缀
2. 继承：BaseEntityV2.CUBaseEntityV2<String>（推荐）或BaseEntityV2.CUDBaseEntityV2<String>（需要逻辑删除）
3. 系统字段自动继承：
   - id、created_at、created_by、last_updated_at、last_updated_by由基类提供
   - delete_mark、deleted_at、deleted_by（如果表支持逻辑删除）
4. 业务字段映射：
   - 将下划线命名转换为小驼峰命名
   - 使用@TableField("{数据库字段名}")注解
5. 类型映射：
   - varchar/text → String
   - tinyint → Integer
   - datetime → Date
   - decimal → BigDecimal
6. 注解：@Data、@EqualsAndHashCode(callSuper = true)、@TableName
7. 注释：每个字段添加完整的JavaDoc注释
```

### 示例：从Excel描述生成实体

```
请根据以下Excel表格描述生成符合XACE规范的Entity类：
表名称：customer_feedback
表描述：客户反馈表

| 列名 | 数据类型 | 是否必填 | 描述 |
|------|----------|----------|------|
| id | varchar(32) | 是 | 主键ID |
| customer_id | varchar(32) | 是 | 客户ID |
| content | text | 是 | 反馈内容 |
| feedback_type | tinyint | 是 | 反馈类型（1-建议，2-投诉，3-咨询） |
| status | tinyint | 是 | 处理状态（0-未处理，1-处理中，2-已处理） |
| created_at | datetime | 是 | 创建时间 |
| created_by | varchar(32) | 是 | 创建者 |
| last_updated_at | datetime | 是 | 最后更新时间 |
| last_updated_by | varchar(32) | 是 | 最后更新人 |
| handler_id | varchar(32) | 否 | 处理人ID |
| handle_time | datetime | 否 | 处理时间 |
| handle_result | text | 否 | 处理结果 |
| attachments | varchar(500) | 否 | 附件URL，多个用逗号分隔 |
| delete_mark | tinyint(1) | 是 | 删除标记 |

XACE规范要求：
1. 类名：CustomerFeedbackEntity
2. 继承：BaseEntityV2.CUDBaseEntityV2<String>（支持逻辑删除）
3. 系统字段自动继承：无需定义id、created_at、created_by等
4. 业务字段映射：customerId、content、feedbackType、status、handlerId、handleTime、handleResult、attachments
5. 注解：每个业务字段使用@TableField("{数据库字段名}")
6. 类型转换：tinyint→Integer、text→String、datetime→Date
```

## 13. 业务方法实现模板

```
请根据XACE规范实现以下业务方法：
方法签名：{方法签名}
业务需求：{详细描述业务逻辑}
涉及依赖：
- {依赖1} {用途}
- {依赖2} {用途}
...

XACE规范要求：
1. 业务逻辑实现：
   - 使用LambdaQueryWrapper进行数据查询
   - 充分利用BaseEntityV2的自动填充功能
   - 正确处理多租户和逻辑删除
2. 参数校验：
   - 使用Jakarta EE验证注解（@NotNull、@NotBlank等）
   - 业务规则校验在Service层实现
   - 抛出明确的业务异常（DataException等）
3. 事务管理：
   - 使用@Transactional注解控制事务边界
   - 明确指定rollbackFor = Exception.class
   - 复杂业务场景考虑事务传播行为
4. 异常处理：
   - 使用项目统一异常类型
   - 提供有意义的错误消息
   - 记录关键操作的错误日志
5. 日志记录：
   - 使用@Slf4j注解
   - 记录关键业务操作的开始和结果
   - 敏感信息不记录到日志中
6. 代码质量：
   - 使用BeanCopierUtils进行对象转换
   - 避免大量嵌套的if-else判断
   - 提取私有方法优化代码结构
```

### 示例：实现订单创建方法

```
请根据XACE规范实现以下业务方法：
方法签名：public String createOrder(OrderForm orderForm)
业务需求：
  1. 根据用户ID和商品列表创建订单
  2. 检查商品库存，不足则抛出DataException
  3. 计算订单总价
  4. 扣减商品库存
  5. 创建订单记录和订单明细记录
  6. 生成唯一订单号
  7. 如有优惠券，应用优惠券并更新优惠券状态
  8. 返回新创建的订单ID
  
涉及依赖：
- OrderMapper 订单数据访问（继承XHBaseMapper）
- OrderItemService 订单明细服务
- ProductService 商品服务
- CouponService 优惠券服务
- IdXhUtil ID生成工具

XACE规范要求：
1. 使用@Transactional(rollbackFor = Exception.class)
2. 使用LambdaQueryWrapper查询商品库存
3. 使用BaseEntityV2自动填充创建时间和创建人
4. 库存不足时抛出DataException("商品库存不足")
5. 使用BeanCopierUtils转换Form到Entity
6. 日志记录订单创建的关键步骤
7. 返回新建订单的ID（String类型）
```

