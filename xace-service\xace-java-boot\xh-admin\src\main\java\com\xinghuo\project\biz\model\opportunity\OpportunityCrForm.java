package com.xinghuo.project.biz.model.opportunity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 商机创建表单
 * 
 * <AUTHOR>
 * @date 2025-07-08
 */
@Data
@Schema(description = "商机创建表单")
public class OpportunityCrForm {

    /**
     * 项目类型 1-建设商机，2-维护商机，3-提前项目
     */
    @Schema(description = "项目类型", required = true)
    @NotNull(message = "项目类型不能为空")
    private Integer projType;

    /**
     * 项目编号
     */
    @Schema(description = "项目编号")
    private String businessNo;

    /**
     * 项目等级
     */
    @Schema(description = "项目等级")
    private String projectLevel;

    /**
     * 项目名称
     */
    @Schema(description = "项目名称", required = true)
    @NotBlank(message = "项目名称不能为空")
    private String projectName;

    /**
     * 项目简介
     */
    @Schema(description = "项目简介")
    private String projectContent;

    /**
     * 商机标签
     */
    @Schema(description = "商机标签")
    private String businessTag;

    /**
     * 客户单位ID
     */
    @Schema(description = "客户单位ID")
    private String custId;

    /**
     * 市场负责人
     */
    @Schema(description = "市场负责人")
    private String marketLinkman;

    /**
     * 售前负责人
     */
    @Schema(description = "售前负责人")
    private String presaleLinkman;

    /**
     * 项目负责人
     */
    @Schema(description = "项目负责人")
    private String projectLeader;

    /**
     * 项目类型
     */
    @Schema(description = "项目类型")
    private String projectType;

    /**
     * 所属分部
     */
    @Schema(description = "所属分部", required = true)
    @NotBlank(message = "所属分部不能为空")
    private String deptId;

    /**
     * 研发分部
     */
    @Schema(description = "研发分部")
    private String yfDeptId;

    /**
     * 启动日期
     */
    @Schema(description = "启动日期")
    private Date startDate;

    /**
     * 预计落地日期
     */
    @Schema(description = "预计落地日期")
    private Date evaSignMonth;

    /**
     * 预计首比回款时间
     */
    @Schema(description = "预计首比回款时间")
    private Date evaFirstMonth;

    /**
     * 预计首比回款金额(万元)
     */
    @Schema(description = "预计首比回款金额(万元)")
    private BigDecimal evaFirstAmount;

    /**
     * 第二笔回款时间
     */
    @Schema(description = "第二笔回款时间")
    private Date evaSecondMonth;

    /**
     * 二笔回款金额(万元)
     */
    @Schema(description = "二笔回款金额(万元)")
    private BigDecimal evaSecondAmount;

    /**
     * 首次外采月份
     */
    @Schema(description = "首次外采月份")
    private Date evaFirstexternalMonth;

    /**
     * 首次外采金额
     */
    @Schema(description = "首次外采金额")
    private BigDecimal evaFirstexternalAmount;

    /**
     * 二次外采月份
     */
    @Schema(description = "二次外采月份")
    private Date evaSecondexternalMonth;

    /**
     * 二次外采金额
     */
    @Schema(description = "二次外采金额")
    private BigDecimal evaSecondexternalAmount;

    /**
     * 合同审核日期
     */
    @Schema(description = "合同审核日期")
    private Date checkDate;

    /**
     * 交底日期
     */
    @Schema(description = "交底日期")
    private Date transDate;

    /**
     * 今年收款比例
     */
    @Schema(description = "今年收款比例")
    private String yearMoneyRatio;

    /**
     * 今年收款
     */
    @Schema(description = "今年收款")
    private BigDecimal yearMoney;

    /**
     * 软件部金额
     */
    @Schema(description = "软件部金额")
    private BigDecimal deptMoney;

    /**
     * 一部金额
     */
    @Schema(description = "一部金额")
    private BigDecimal yfYbAmount;

    /**
     * 二部金额
     */
    @Schema(description = "二部金额")
    private BigDecimal yfEbAmount;

    /**
     * 交付金额
     */
    @Schema(description = "交付金额")
    private BigDecimal yfJfAmount;

    /**
     * 综合金额
     */
    @Schema(description = "综合金额")
    private BigDecimal yfOtherAmount;

    /**
     * 一部外采
     */
    @Schema(description = "一部外采")
    private BigDecimal outYbAmount;

    /**
     * 二部外采
     */
    @Schema(description = "二部外采")
    private BigDecimal outEbAmount;

    /**
     * 交付外采
     */
    @Schema(description = "交付外采")
    private BigDecimal outJfAmount;

    /**
     * 综合外采
     */
    @Schema(description = "综合外采")
    private BigDecimal outOtherAmount;

    /**
     * 外采金额
     */
    @Schema(description = "外采金额")
    private BigDecimal purchaseMoney;

    /**
     * 毛利
     */
    @Schema(description = "毛利")
    private BigDecimal profitMoney;

    /**
     * 累计工时(自动计算)
     */
    @Schema(description = "累计工时(自动计算)")
    private BigDecimal autoManhours;

    /**
     * 是否部署系统
     */
    @Schema(description = "是否部署系统")
    private String deployStatus;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 最后跟踪记录
     */
    @Schema(description = "最后跟踪记录")
    private String lastNote;

    /**
     * 项目跟踪状态 1-跟踪中 2-已签，3-已废弃 4-24年跟踪
     */
    @Schema(description = "项目跟踪状态")
    private String status;

    /**
     * 签订合同编号
     */
    @Schema(description = "签订合同编号")
    private String projectNo;

    /**
     * 流程ID
     */
    @Schema(description = "流程ID")
    private String flowId;

    /**
     * 预计结束日期
     */
    @Schema(description = "预计结束日期")
    private Date yjEndDate;

    /**
     * 预计开始日期
     */
    @Schema(description = "预计开始日期")
    private Date yjStartDate;

    /**
     * 工时填写状态 1-可填写 0-已结束
     */
    @Schema(description = "工时填写状态")
    private Integer workStatus;
}