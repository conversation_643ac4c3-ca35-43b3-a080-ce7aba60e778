package com.xinghuo.project.biz.service;

import com.xinghuo.project.biz.entity.BizDepartmentAllocationEntity;
import com.xinghuo.common.base.service.BaseService;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 业务部门分配服务接口（固定字段版本）
 * 
 * <AUTHOR>
 * @version V2.0
 * @copyright 易趋集团
 * @date 2024-01-01
 */
public interface BizDepartmentAllocationService extends BaseService<BizDepartmentAllocationEntity> {

    /**
     * 根据业务ID和业务类型获取分配信息
     * 
     * @param businessId 业务ID
     * @param businessType 业务类型
     * @return 分配实体
     */
    BizDepartmentAllocationEntity getByBusinessId(String businessId, Integer businessType);

    /**
     * 保存或更新分配信息
     * 
     * @param businessId 业务ID
     * @param businessType 业务类型
     * @param allocation 分配数据
     */
    void saveOrUpdateAllocation(String businessId, Integer businessType, BizDepartmentAllocationEntity allocation);

    /**
     * 获取部门汇总数据
     * 
     * @param businessType 业务类型
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 部门汇总数据
     */
    Map<String, BigDecimal> getDepartmentSummary(Integer businessType, String startDate, String endDate);

    /**
     * 批量创建分配记录
     * 
     * @param businessIds 业务ID列表
     * @param businessType 业务类型
     */
    void batchCreateAllocations(String[] businessIds, Integer businessType);

    /**
     * 删除业务分配数据
     * 
     * @param businessId 业务ID
     * @param businessType 业务类型
     */
    void deleteByBusinessId(String businessId, Integer businessType);

    /**
     * 验证分配数据合理性
     * 
     * @param allocation 分配数据
     * @param totalAmount 总金额
     * @return 验证结果
     */
    boolean validateAllocation(BizDepartmentAllocationEntity allocation, BigDecimal totalAmount);

    /**
     * 获取交付部门相关数据（重点解决当前需求）
     * 
     * @param businessIds 业务ID列表
     * @param businessType 业务类型
     * @return 交付部门汇总
     */
    Map<String, BigDecimal> getJfDepartmentSummary(String[] businessIds, Integer businessType);

    /**
     * 从原有实体迁移分配数据
     * 
     * @param sourceEntity 原实体数据
     * @param businessId 业务ID
     * @param businessType 业务类型
     */
    void migrateFromSourceEntity(Object sourceEntity, String businessId, Integer businessType);
}