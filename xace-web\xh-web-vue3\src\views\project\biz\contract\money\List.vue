<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" @click="handleCreate">新增收款</a-button>
      </template>

      <!-- 表格单元格自定义渲染 -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'cmMoney'">
          {{ formatAmount(record.cmMoney) }}
        </template>
        <template v-if="column.key === 'payStatus'">
          <a-tag :color="getPayStatusColor(record.payStatus)">
            {{ getPayStatusText(record.payStatus) }}
          </a-tag>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'clarity:info-standard-line',
                label: '查看',
                onClick: handleView.bind(null, record),
              },
              {
                icon: 'clarity:note-edit-line',
                label: '编辑',
                onClick: handleEdit.bind(null, record),
              },
              {
                icon: 'ant-design:file-text-outlined',
                label: '开票',
                onClick: handleInvoice.bind(null, record),
                ifShow: String(record.payStatus) !== '1',
              },
              {
                icon: 'ant-design:dollar-circle-outlined',
                label: '收款',
                onClick: handlePayment.bind(null, record),
                ifShow: String(record.payStatus) !== '1',
              },
            ]" />
        </template>
      </template>
    </BasicTable>

    <!-- 新增/编辑抽屉 -->
    <ContractMoneyDrawer @register="registerDrawer" @success="handleSuccess" />

    <!-- 开票登记弹窗 -->
    <InvoiceModal @register="registerInvoiceModal" @success="handleSuccess" />

    <!-- 收款登记弹窗 -->
    <PaymentModal @register="registerPaymentModal" @success="handleSuccess" />
  </div>
</template>

<script lang="ts" setup>
  import { reactive, watch, computed } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import { useDrawer } from '/@/components/Drawer';
  import { getContractMoneyListByContractId } from '/@/api/project/contractMoney';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { formatToDate } from '/@/utils/dateUtil';

  import ContractMoneyDrawer from './ContractMoneyDrawer.vue';
  import InvoiceModal from './InvoiceModal.vue';
  import PaymentModal from './PaymentModal.vue';

  import type { ContractMoneyModel } from '/@/api/project/contractMoney';

  interface Props {
    contractId: string;
    contractAmount?: number;
  }

  const props = defineProps<Props>();
  const { createMessage } = useMessage();
  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerInvoiceModal, { openModal: openInvoiceModal }] = useModal();
  const [registerPaymentModal, { openModal: openPaymentModal }] = useModal();

  // 表格列定义
  const columns = [
    {
      title: '支付条件',
      dataIndex: 'fktj',
      width: 150,
    },
    {
      title: '收款比例',
      dataIndex: 'ratio',
      width: 100,
    },
    {
      title: '收款金额',
      key: 'cmMoney',
      dataIndex: 'cmMoney',
      width: 120,
      sorter: true,
    },
    {
      title: '收款状态',
      key: 'payStatus',
      dataIndex: 'payStatus',
      width: 100,
    },
    {
      title: '应收日期',
      dataIndex: 'yingshouDate',
      width: 120,
      customRender: ({ record }) => {
        return record.yingshouDate ? formatToDate(record.yingshouDate) : '-';
      },
    },
    {
      title: '预收日期',
      dataIndex: 'yushouDate',
      width: 120,
      customRender: ({ record }) => {
        return record.yushouDate ? formatToDate(record.yushouDate) : '-';
      },
    },
    {
      title: '开票日期',
      dataIndex: 'kaipiaoDate',
      width: 120,
      customRender: ({ record }) => {
        return record.kaipiaoDate ? formatToDate(record.kaipiaoDate) : '-';
      },
    },
    {
      title: '收款日期',
      dataIndex: 'shoukuanDate',
      width: 120,
      customRender: ({ record }) => {
        return record.shoukuanDate ? formatToDate(record.shoukuanDate) : '-';
      },
    },
  ];

  // 自定义API函数，根据合同ID获取收款列表
  const customApi = async () => {
    if (!props.contractId) {
      return { list: [], pagination: { total: 0 } };
    }

    try {
      console.log('正在获取收款列表，合同ID:', props.contractId);
      const data = await getContractMoneyListByContractId(props.contractId);
      console.log('获取到的收款列表数据:', data);
      return {
        list: data || [],
        pagination: { total: (data || []).length },
      };
    } catch (error) {
      console.error('获取收款列表失败:', error);
      return { list: [], pagination: { total: 0 } };
    }
  };

  // 表格配置
  const [registerTable, { reload }] = useTable({
    title: '收款管理列表',
    api: customApi,
    rowKey: 'cmId',
    columns,
    useSearchForm: false,
    showTableSetting: true,
    bordered: true,
    showSummary: true,
    summaryFunc: calculateSummary,
    actionColumn: {
      width: 200,
      title: '操作',
      dataIndex: 'action',
    },
  });

  // 计算合计行数据
  function calculateSummary(dataSource: ContractMoneyModel[]) {
    const summaryData = {
      fktj: '合计',
      ratio: '',
      cmMoney: 0,
      payStatus: '',
      yingshouDate: '',
      yushouDate: '',
      kaipiaoDate: '',
      shoukuanDate: '',
    };

    // 计算收款金额的合计
    dataSource.forEach(item => {
      summaryData.cmMoney += Number(item.cmMoney) || 0;
    });

    return [summaryData];
  }

  // 格式化金额
  function formatAmount(amount: number | undefined) {
    if (amount === undefined || amount === null) return '-';
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
    }).format(amount);
  }

  // 获取收款状态颜色
  function getPayStatusColor(status: string | number) {
    const statusStr = String(status);
    const colorMap = {
      '0': 'red', // 未收款
      '1': 'green', // 已收款
    };
    return colorMap[statusStr] || 'default';
  }

  // 获取收款状态文本
  function getPayStatusText(status: string | number) {
    const statusStr = String(status);
    const textMap = {
      '0': '未收款',
      '1': '已收款',
    };
    return textMap[statusStr] || status;
  }

  // 新增收款
  function handleCreate() {
    openDrawer(true, {
      isUpdate: false,
      contractId: props.contractId,
    });
  }

  // 查看详情
  function handleView(record: ContractMoneyModel) {
    openDrawer(true, {
      record,
      isUpdate: false,
      readonly: true,
    });
  }

  // 编辑收款
  function handleEdit(record: ContractMoneyModel) {
    openDrawer(true, {
      record,
      isUpdate: true,
    });
  }

  // 登记开票
  function handleInvoice(record: ContractMoneyModel) {
    openInvoiceModal(true, {
      record,
    });
  }

  // 登记收款
  function handlePayment(record: ContractMoneyModel) {
    openPaymentModal(true, {
      record,
    });
  }

  // 操作成功回调
  function handleSuccess() {
    reload();
  }

  // 监听合同ID变化，重新加载数据
  watch(
    () => props.contractId,
    newContractId => {
      if (newContractId) {
        reload();
      }
    },
    { immediate: true },
  );
</script>
