package com.xinghuo.project.template.model.dto;

import com.xinghuo.project.template.entity.WorkProductPlanTemplateDetailEntity;
import com.xinghuo.project.template.entity.WorkProductPlanTemplateEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 交付物计划模板DTO类
 * 包含主表信息和明细列表
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WorkProductPlanTemplateDTO extends WorkProductPlanTemplateEntity {

    /**
     * 交付物明细列表
     */
    private List<WorkProductPlanTemplateDetailEntity> workProductDetails;

    /**
     * 关联的项目模板ID列表
     */
    private List<String> projectTemplateIds;

    /**
     * 标签ID列表
     */
    private List<String> tagIds;

    /**
     * 知识状态名称（冗余字段，便于显示）
     */
    private String knStatusName;

    /**
     * 创建人姓名（冗余字段，便于显示）
     */
    private String creatorUserName;

    /**
     * 交付物总数
     */
    private Integer workProductCount;

    /**
     * 需要评审的交付物数量
     */
    private Integer reviewRequiredCount;

    /**
     * 最终交付成果数量
     */
    private Integer deliverableCount;

    /**
     * 关联项目模板数量
     */
    private Integer projectTemplateCount;
}
