package com.xinghuo.project.core.model.projectBase;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目基础信息表单
 *
 * <AUTHOR>
 * @date 2024-08-05
 */
@Data
@Schema(description = "项目基础信息表单")
public class ProjectBaseInfoForm {

    /**
     * 项目ID，新增时为空，更新时必填
     */
    @Schema(description = "项目ID，新增时为空，更新时必填")
    private String id;

    /**
     * 项目名称
     */
    @Schema(description = "项目名称")
    @NotBlank(message = "项目名称不能为空")
    private String fullName;

    /**
     * 项目描述
     */
    @Schema(description = "项目描述")
    private String description;

    /**
     * 项目经理ID
     */
    @Schema(description = "项目经理ID")
    private String managerId;

    /**
     * 项目发起人ID
     */
    @Schema(description = "项目发起人ID")
    private String sponsorId;

    /**
     * 计划开始日期
     */
    @Schema(description = "计划开始日期")
    private Date plannedStartDate;

    /**
     * 计划结束日期
     */
    @Schema(description = "计划结束日期")
    private Date plannedEndDate;

    /**
     * 投资预算
     */
    @Schema(description = "投资预算")
    private BigDecimal investmentBudget;

    /**
     * 战略目标
     */
    @Schema(description = "战略目标")
    private String strategicObjective;

    /**
     * 优先级
     */
    @Schema(description = "优先级")
    private String priority;

    /**
     * 工作量估算（人天）
     */
    @Schema(description = "工作量估算（人天）")
    private BigDecimal estimatedWorkload;

    /**
     * 风险等级
     */
    @Schema(description = "风险等级")
    private String riskLevel;

    /**
     * 所属部门ID
     */
    @Schema(description = "所属部门ID")
    private String departmentId;

    /**
     * 所属项目群ID
     */
    @Schema(description = "所属项目群ID")
    private String programId;

    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    private String customerId;

    /**
     * 合同ID
     */
    @Schema(description = "合同ID")
    private String contractId;

    /**
     * 项目范围
     */
    @Schema(description = "项目范围")
    private String projectScope;
}
