package com.xinghuo.manhour.model.analysis;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 个人分析数据VO
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@Schema(description = "个人分析数据")
public class PersonalAnalysisVO {

    @Schema(description = "用户ID")
    private String userId;

    @Schema(description = "员工姓名")
    private String userName;

    @Schema(description = "所属分部")
    private String fbName;

    @Schema(description = "分部ID")
    private String fbId;

    @Schema(description = "总工时")
    private BigDecimal totalWorkMonth;

    @Schema(description = "参与项目数")
    private Integer projectCount;

    @Schema(description = "主要项目类型")
    private String mainProjType;

    @Schema(description = "工时效率")
    private BigDecimal efficiency;

    @Schema(description = "技能标签")
    private String skillTags;

    @Schema(description = "平均月工时")
    private BigDecimal avgMonthWorkHour;

    @Schema(description = "项目参与度")
    private BigDecimal projectParticipation;

    @Schema(description = "工时完成率")
    private BigDecimal completionRate;

    @Schema(description = "最活跃项目")
    private String mostActiveProject;

    @Schema(description = "工时分布均匀度")
    private BigDecimal distributionBalance;
}
