<template>
  <div class="workhour-completion">
    <!-- 查询条件 -->
    <div class="search-section">
      <a-card size="small" class="search-card mb-4">
        <template #title>
          <div class="search-title">
            <SearchOutlined class="title-icon" />
            <span>查询条件</span>
          </div>
        </template>
        <template #extra>
          <a-space>
            <a-button type="primary" @click="handleExport" :loading="exportLoading" size="small">
              <template #icon><ExportOutlined /></template>
              导出报表
            </a-button>
            <a-button @click="handleRefresh" :loading="refreshLoading" size="small">
              <template #icon><ReloadOutlined /></template>
              刷新数据
            </a-button>
          </a-space>
        </template>
        <BasicForm @register="registerForm" @submit="handleSearch" @reset="handleReset" />
      </a-card>
    </div>

    <!-- 统计概览 -->
    <div class="overview-section mb-4">
      <a-row :gutter="16">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card size="small" class="stat-card total-users">
            <div class="stat-content">
              <div class="stat-icon">
                <UserOutlined />
              </div>
              <div class="stat-info">
                <a-statistic
                  title="应填写人数"
                  :value="overview.totalUsers"
                  suffix="人"
                  :value-style="{ color: '#1890ff', fontSize: '24px', fontWeight: '600' }" />
                <div class="stat-desc">本期应填写工时的总人数</div>
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card size="small" class="stat-card completed-users">
            <div class="stat-content">
              <div class="stat-icon">
                <TeamOutlined />
              </div>
              <div class="stat-info">
                <a-statistic
                  title="已填写人数"
                  :value="overview.completedUsers"
                  suffix="人"
                  :value-style="{ color: '#52c41a', fontSize: '24px', fontWeight: '600' }" />
                <div class="stat-desc">已完成工时填写的人数</div>
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card size="small" class="stat-card completion-rate">
            <div class="stat-content">
              <div class="stat-icon">
                <PieChartOutlined />
              </div>
              <div class="stat-info">
                <a-statistic
                  title="填写完成率"
                  :value="overview.completionRate"
                  suffix="%"
                  :precision="1"
                  :value-style="{
                    color: overview.completionRate >= 90 ? '#52c41a' : overview.completionRate >= 70 ? '#fa8c16' : '#ff4d4f',
                    fontSize: '24px',
                    fontWeight: '600',
                  }" />
                <div class="stat-desc">工时填写完成百分比</div>
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card size="small" class="stat-card pending-approval">
            <div class="stat-content">
              <div class="stat-icon">
                <BarChartOutlined />
              </div>
              <div class="stat-info">
                <a-statistic
                  title="待审批数量"
                  :value="overview.pendingApproval"
                  suffix="条"
                  :value-style="{ color: '#fa8c16', fontSize: '24px', fontWeight: '600' }" />
                <div class="stat-desc">等待审批的工时记录数</div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 填写状态分析 -->
    <div class="status-analysis mb-4">
      <a-row :gutter="16">
        <!-- 填写状态分布 -->
        <a-col :xs="24" :lg="8">
          <a-card size="small" class="chart-card">
            <template #title>
              <div class="chart-title">
                <PieChartOutlined class="title-icon" />
                <span>填写状态分布</span>
              </div>
            </template>
            <div ref="statusDistributionChart" class="chart-container"></div>
          </a-card>
        </a-col>

        <!-- 审批状态分布 -->
        <a-col :xs="24" :lg="8">
          <a-card size="small" class="chart-card">
            <template #title>
              <div class="chart-title">
                <PieChartOutlined class="title-icon" />
                <span>审批状态分布</span>
              </div>
            </template>
            <div ref="approvalStatusChart" class="chart-container"></div>
          </a-card>
        </a-col>

        <!-- 分部完成率对比 -->
        <a-col :xs="24" :lg="8">
          <a-card size="small" class="chart-card">
            <template #title>
              <div class="chart-title">
                <BarChartOutlined class="title-icon" />
                <span>分部完成率对比</span>
              </div>
            </template>
            <div ref="departmentCompletionChart" class="chart-container"></div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 趋势分析 -->
    <div class="trend-analysis mb-4">
      <a-row :gutter="16">
        <!-- 填写趋势 -->
        <a-col :xs="24" :lg="12">
          <a-card size="small" class="chart-card">
            <template #title>
              <div class="chart-title">
                <LineChartOutlined class="title-icon" />
                <span>填写完成趋势</span>
              </div>
            </template>
            <div ref="completionTrendChart" class="chart-container"></div>
          </a-card>
        </a-col>

        <!-- 审批效率趋势 -->
        <a-col :xs="24" :lg="12">
          <a-card size="small" class="chart-card">
            <template #title>
              <div class="chart-title">
                <LineChartOutlined class="title-icon" />
                <span>审批效率趋势</span>
              </div>
            </template>
            <div ref="approvalEfficiencyChart" class="chart-container"></div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 详细数据表格 -->
    <div class="table-section">
      <a-tabs v-model:activeKey="activeTab" @change="handleTabChange">
        <a-tab-pane key="unfilled" tab="未填写人员">
          <BasicTable @register="registerUnfilledTable">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'action'">
                <a-button type="link" size="small" @click="handleNotifyUser(record)"> 发送提醒 </a-button>
              </template>
            </template>
          </BasicTable>
        </a-tab-pane>

        <a-tab-pane key="pending" tab="待审批记录">
          <BasicTable @register="registerPendingTable">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'action'">
                <a-button type="link" size="small" @click="handleViewDetail(record)"> 查看详情 </a-button>
              </template>
            </template>
          </BasicTable>
        </a-tab-pane>

        <a-tab-pane key="department" tab="分部统计">
          <BasicTable @register="registerDepartmentTable" />
        </a-tab-pane>

        <a-tab-pane key="leader" tab="负责人统计">
          <BasicTable @register="registerLeaderTable">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'action'">
                <a-button type="link" size="small" @click="handleNotifyLeader(record)"> 发送提醒 </a-button>
              </template>
            </template>
          </BasicTable>
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 操作提示 -->
    <div class="action-tips">
      <a-alert
        message="操作提示"
        description="可以通过发送提醒功能通知相关人员及时填写或审批工时。系统会自动发送邮件和站内消息提醒。"
        type="info"
        show-icon
        closable />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted, nextTick } from 'vue';
  import { BasicForm, useForm, FormSchema } from '/@/components/Form';
  import { BasicTable, useTable } from '/@/components/Table';
  import {
    ExportOutlined,
    ReloadOutlined,
    SearchOutlined,
    BarChartOutlined,
    PieChartOutlined,
    LineChartOutlined,
    UserOutlined,
    TeamOutlined,
  } from '@ant-design/icons-vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import * as echarts from 'echarts';
  import { useComponentRegister } from '/@/components/Form/index';
  import FbSelect from '/@/views/performance/common/fbSelect.vue';
  import {
    getWorkhourCompletionOverview,
    getWorkhourCompletionCharts,
    getUnfilledUsersList,
    getPendingApprovalList,
    getDepartmentCompletionList,
    getLeaderStatisticsList,
    exportWorkhourCompletion,
    notifyUser,
    notifyLeader,
  } from './api';

  defineOptions({ name: 'WorkhourCompletion' });

  // @ts-expect-error
  useComponentRegister('FbSelect', FbSelect);

  const { createMessage } = useMessage();
  const activeTab = ref('unfilled');
  const exportLoading = ref(false);
  const refreshLoading = ref(false);

  // 统计概览数据
  const overview = reactive({
    totalUsers: 0,
    completedUsers: 0,
    completionRate: 0,
    pendingApproval: 0,
    unfilledUsers: 0,
    overdueUsers: 0,
  });

  // 图表实例
  const statusDistributionChart = ref();
  const approvalStatusChart = ref();
  const departmentCompletionChart = ref();
  const completionTrendChart = ref();
  const approvalEfficiencyChart = ref();

  let chartInstances: Record<string, echarts.ECharts> = {};

  // 查询表单配置
  const searchFormSchemas: FormSchema[] = [
    {
      field: 'timeType',
      label: '时间类型',
      component: 'Select',
      componentProps: {
        options: [
          { fullName: '月度', id: 'month' },
          { fullName: '季度', id: 'quarter' },
          { fullName: '年度', id: 'year' },
          { fullName: '总体', id: 'total' },
        ],
        placeholder: '请选择时间类型',
      },
      defaultValue: 'month',
      colProps: { span: 4 },
    },
    {
      field: 'startMonth',
      label: '开始月份',
      component: 'MonthPicker',
      componentProps: {
        format: 'YYYY-MM',
        valueFormat: 'YYYYMM',
        placeholder: '请选择开始月份',
      },
      colProps: { span: 3 },
      show: ({ values }) => values.timeType === 'month',
    },
    {
      field: 'endMonth',
      label: '结束月份',
      component: 'MonthPicker',
      componentProps: {
        format: 'YYYY-MM',
        valueFormat: 'YYYYMM',
        placeholder: '请选择结束月份',
      },
      colProps: { span: 3 },
      show: ({ values }) => values.timeType === 'month',
    },
    {
      field: 'quarter',
      label: '季度',
      component: 'Select',
      componentProps: {
        options: [
          { fullName: '第一季度', id: 'Q1' },
          { fullName: '第二季度', id: 'Q2' },
          { fullName: '第三季度', id: 'Q3' },
          { fullName: '第四季度', id: 'Q4' },
        ],
        placeholder: '请选择季度',
      },
      colProps: { span: 3 },
      show: ({ values }) => values.timeType === 'quarter',
    },
    {
      field: 'year',
      label: '年份',
      component: 'DatePicker',
      componentProps: {
        picker: 'year',
        format: 'YYYY',
        valueFormat: 'YYYY',
        placeholder: '请选择年份',
      },
      colProps: { span: 3 },
      show: ({ values }) => values.timeType === 'year',
    },
    {
      field: 'fbId',
      label: '分部',
      component: 'FbSelect',
      componentProps: {
        placeholder: '请选择分部',
      },
      colProps: { span: 4 },
    },
    {
      field: 'status',
      label: '填写状态',
      component: 'Select',
      componentProps: {
        options: [
          { fullName: '全部', id: '' },
          { fullName: '未填写', id: '0' },
          { fullName: '已填写', id: '1' },
          { fullName: '待审批', id: '2' },
          { fullName: '已审批', id: '3' },
        ],
        placeholder: '请选择状态',
      },
      colProps: { span: 3 },
    },
  ];

  const [registerForm, { getFieldsValue, setFieldsValue }] = useForm({
    schemas: searchFormSchemas,
    labelWidth: 80,
    showActionButtonGroup: true,
    actionColOptions: { span: 24 },
    autoSubmitOnEnter: true,
  });

  // 未填写人员表格
  const [registerUnfilledTable, { reload: reloadUnfilledTable, setTableData: setUnfilledTableData }] = useTable({
    api: getUnfilledUsersList,
    columns: [
      { title: '员工姓名', dataIndex: 'userName', width: 120 },
      { title: '所属分部', dataIndex: 'fbName', width: 120 },
      { title: '填写负责人', dataIndex: 'chargeName', width: 120 },
      { title: '应填写月份', dataIndex: 'month', width: 100 },
      { title: '逾期天数', dataIndex: 'overdueDays', width: 100, customRender: ({ text }) => (text > 0 ? `${text}天` : '-') },
      { title: '最后提醒时间', dataIndex: 'lastNotifyTime', width: 150 },
    ],
    useSearchForm: false,
    showTableSetting: true,
    bordered: true,
    immediate: false, // 不立即加载
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
  });

  // 待审批记录表格
  const [registerPendingTable, { reload: reloadPendingTable, setTableData: setPendingTableData }] = useTable({
    api: getPendingApprovalList,
    columns: [
      { title: '员工姓名', dataIndex: 'userName', width: 120 },
      { title: '所属分部', dataIndex: 'fbName', width: 120 },
      { title: '填写月份', dataIndex: 'month', width: 100 },
      { title: '工时人月', dataIndex: 'workMonth', width: 100, customRender: ({ text }) => `${text}人月` },
      { title: '当前状态', dataIndex: 'statusName', width: 100 },
      { title: '审批人', dataIndex: 'approverName', width: 120 },
      { title: '提交时间', dataIndex: 'submitTime', width: 150 },
      { title: '等待天数', dataIndex: 'waitingDays', width: 100, customRender: ({ text }) => `${text}天` },
    ],
    useSearchForm: false,
    showTableSetting: true,
    bordered: true,
    immediate: false, // 不立即加载
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
  });

  // 分部统计表格
  const [registerDepartmentTable, { reload: reloadDepartmentTable, setTableData: setDepartmentTableData }] = useTable({
    api: getDepartmentCompletionList,
    columns: [
      { title: '分部名称', dataIndex: 'fbName', width: 150 },
      { title: '应填写人数', dataIndex: 'totalUsers', width: 100 },
      { title: '已填写人数', dataIndex: 'completedUsers', width: 100 },
      { title: '未填写人数', dataIndex: 'unfilledUsers', width: 100 },
      { title: '完成率', dataIndex: 'completionRate', width: 100, customRender: ({ text }) => `${text}%` },
      { title: '待审批数', dataIndex: 'pendingCount', width: 100 },
      { title: '逾期人数', dataIndex: 'overdueUsers', width: 100 },
      { title: '分部负责人', dataIndex: 'fbLeaderName', width: 120 },
    ],
    useSearchForm: false,
    showTableSetting: true,
    bordered: true,
    immediate: false, // 不立即加载
  });

  // 负责人统计表格
  const [registerLeaderTable, { reload: reloadLeaderTable, setTableData: setLeaderTableData }] = useTable({
    api: getLeaderStatisticsList,
    columns: [
      { title: '负责人姓名', dataIndex: 'leaderName', width: 120 },
      { title: '负责人类型', dataIndex: 'leaderType', width: 100 },
      { title: '负责人数', dataIndex: 'managedUsers', width: 100 },
      { title: '待审批数', dataIndex: 'pendingCount', width: 100 },
      { title: '已审批数', dataIndex: 'approvedCount', width: 100 },
      { title: '审批效率', dataIndex: 'approvalEfficiency', width: 100, customRender: ({ text }) => `${text}%` },
      { title: '平均审批时长', dataIndex: 'avgApprovalDays', width: 120, customRender: ({ text }) => `${text}天` },
    ],
    useSearchForm: false,
    showTableSetting: true,
    bordered: true,
    immediate: false, // 不立即加载
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
  });

  // 搜索处理
  function handleSearch() {
    const values = getFieldsValue();
    // 处理查询参数
    const params = {
      ...values,
      startMonth: values.startMonth,
      endMonth: values.endMonth,
    };
    loadData(params);

    // 如果当前有激活的标签页，重新加载对应的表格数据
    if (activeTab.value) {
      setTimeout(() => {
        loadTableData(activeTab.value);
      }, 500);
    }
  }

  // 重置处理
  function handleReset() {
    // 设置默认查询条件（上个月）
    const now = new Date();
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const lastMonthStr = lastMonth.getFullYear() + String(lastMonth.getMonth() + 1).padStart(2, '0');

    const defaultParams = {
      timeType: 'month',
      startMonth: lastMonthStr,
      endMonth: lastMonthStr,
    };

    setFieldsValue(defaultParams);
    loadData(defaultParams);

    // 如果当前有激活的标签页，重新加载对应的表格数据
    if (activeTab.value) {
      setTimeout(() => {
        loadTableData(activeTab.value);
      }, 500);
    }
  }

  // 刷新数据
  function handleRefresh() {
    refreshLoading.value = true;
    const values = getFieldsValue();
    loadData(values).finally(() => {
      refreshLoading.value = false;
    });
  }

  // 标签页切换
  function handleTabChange(key: string) {
    activeTab.value = key;

    // 当切换到某个标签页时，手动加载对应的表格数据
    setTimeout(() => {
      try {
        switch (key) {
          case 'unfilled':
            loadTableData('unfilled');
            break;
          case 'pending':
            loadTableData('pending');
            break;
          case 'department':
            loadTableData('department');
            break;
          case 'leader':
            loadTableData('leader');
            break;
        }
      } catch (error) {
        console.warn(`切换到标签页 ${key} 时表格加载失败:`, error);
      }
    }, 100);
  }

  // 手动加载表格数据
  async function loadTableData(tableType: string) {
    try {
      const params = currentParams.value || {};

      switch (tableType) {
        case 'unfilled':
          // 手动调用API并设置表格数据
          const unfilledData = await getUnfilledUsersList(params);
          console.log('未填写人员数据:', unfilledData);
          if (unfilledData && unfilledData.data) {
            setUnfilledTableData(unfilledData.data.list || []);
          }
          break;
        case 'pending':
          const pendingData = await getPendingApprovalList(params);
          console.log('待审批数据:', pendingData);
          if (pendingData && pendingData.data) {
            setPendingTableData(pendingData.data.list || []);
          }
          break;
        case 'department':
          const departmentData = await getDepartmentCompletionList(params);
          console.log('分部统计数据:', departmentData);
          if (departmentData && departmentData.data) {
            setDepartmentTableData(departmentData.data.list || []);
          }
          break;
        case 'leader':
          const leaderData = await getLeaderStatisticsList(params);
          console.log('负责人统计数据:', leaderData);
          if (leaderData && leaderData.data) {
            setLeaderTableData(leaderData.data.list || []);
          }
          break;
      }
    } catch (error) {
      console.error(`加载${tableType}表格数据失败:`, error);
    }
  }

  // 导出报表
  async function handleExport() {
    try {
      exportLoading.value = true;
      const values = getFieldsValue();
      await exportWorkhourCompletion(values);
      createMessage.success('导出成功');
    } catch (error) {
      createMessage.error('导出失败');
    } finally {
      exportLoading.value = false;
    }
  }

  // 发送用户提醒
  async function handleNotifyUser(record: any) {
    try {
      await notifyUser(record.userId, record.month);
      createMessage.success('提醒发送成功');
      reloadUnfilledTable();
    } catch (error) {
      createMessage.error('提醒发送失败');
    }
  }

  // 发送负责人提醒
  async function handleNotifyLeader(record: any) {
    try {
      await notifyLeader(record.leaderId);
      createMessage.success('提醒发送成功');
      reloadLeaderTable();
    } catch (error) {
      createMessage.error('提醒发送失败');
    }
  }

  // 查看详情
  function handleViewDetail(record: any) {
    // TODO: 实现查看详情功能
    createMessage.info('查看详情功能开发中');
  }

  // 存储当前查询参数
  const currentParams = ref<any>({});

  // 加载数据
  async function loadData(params: any = {}) {
    try {
      // 保存当前查询参数
      currentParams.value = { ...params };

      // 加载概览数据
      const overviewResponse = await getWorkhourCompletionOverview(params);
      console.log('概览数据响应:', overviewResponse);

      // 检查响应结构并正确访问数据
      if (overviewResponse) {
        if (overviewResponse.data) {
          // 如果有 .data 属性，使用 .data
          Object.assign(overview, overviewResponse.data);
        } else {
          // 如果没有 .data 属性，直接使用响应数据
          Object.assign(overview, overviewResponse);
        }
      }

      // 加载图表数据
      const chartResponse = await getWorkhourCompletionCharts(params);
      console.log('图表数据响应:', chartResponse);

      // 检查响应结构并正确访问数据
      if (chartResponse) {
        if (chartResponse.data) {
          // 如果有 .data 属性，使用 .data
          renderCharts(chartResponse.data);
        } else {
          // 如果没有 .data 属性，直接使用响应数据
          renderCharts(chartResponse);
        }
      }

      // 不在这里重载表格，让表格按需加载
      console.log('概览和图表数据加载完成');
    } catch (error) {
      console.error('加载数据失败:', error);
      createMessage.error('加载数据失败');
    }
  }

  // 渲染图表
  function renderCharts(data: any) {
    if (!data || typeof data !== 'object') {
      console.warn('图表数据格式错误:', data);
      return;
    }

    nextTick(() => {
      renderStatusDistributionChart(data.statusDistribution || []);
      renderApprovalStatusChart(data.approvalStatus || []);
      renderDepartmentCompletionChart(data.departmentCompletion || []);
      renderCompletionTrendChart(data.completionTrend || []);
      renderApprovalEfficiencyChart(data.approvalEfficiency || []);
    });
  }

  // 渲染填写状态分布图表
  function renderStatusDistributionChart(data: any[]) {
    if (!statusDistributionChart.value || !Array.isArray(data)) return;

    if (chartInstances.statusDistribution) {
      chartInstances.statusDistribution.dispose();
    }

    chartInstances.statusDistribution = echarts.init(statusDistributionChart.value);

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c}人 ({d}%)',
      },
      legend: {
        bottom: '0%',
        left: 'center',
      },
      series: [
        {
          name: '填写状态',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['50%', '45%'],
          data: data.map(item => ({
            name: item.statusName || '未知状态',
            value: item.userCount || 0,
            itemStyle: {
              color: getStatusColor(item.status),
            },
          })),
        },
      ],
    };

    chartInstances.statusDistribution.setOption(option);
  }

  // 渲染审批状态分布图表
  function renderApprovalStatusChart(data: any[]) {
    if (!approvalStatusChart.value || !Array.isArray(data)) return;

    if (chartInstances.approvalStatus) {
      chartInstances.approvalStatus.dispose();
    }

    chartInstances.approvalStatus = echarts.init(approvalStatusChart.value);

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c}条 ({d}%)',
      },
      legend: {
        bottom: '0%',
        left: 'center',
      },
      series: [
        {
          name: '审批状态',
          type: 'pie',
          radius: '70%',
          center: ['50%', '45%'],
          data: data.map(item => ({
            name: item.statusName || '未知状态',
            value: item.recordCount || 0,
            itemStyle: {
              color: getApprovalStatusColor(item.status),
            },
          })),
        },
      ],
    };

    chartInstances.approvalStatus.setOption(option);
  }

  // 渲染分部完成率对比图表
  function renderDepartmentCompletionChart(data: any[]) {
    if (!departmentCompletionChart.value || !Array.isArray(data)) return;

    if (chartInstances.departmentCompletion) {
      chartInstances.departmentCompletion.dispose();
    }

    chartInstances.departmentCompletion = echarts.init(departmentCompletionChart.value);

    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'shadow' },
      },
      xAxis: {
        type: 'category',
        data: data.map(item => item.fbName || '未知分部'),
        axisLabel: {
          rotate: 45,
          interval: 0,
        },
      },
      yAxis: {
        type: 'value',
        name: '完成率(%)',
        max: 100,
      },
      series: [
        {
          name: '完成率',
          type: 'bar',
          data: data.map(item => ({
            value: item.completionRate || 0,
            itemStyle: {
              color: (item.completionRate || 0) >= 90 ? '#52c41a' : (item.completionRate || 0) >= 70 ? '#fa8c16' : '#ff4d4f',
            },
          })),
        },
      ],
    };

    chartInstances.departmentCompletion.setOption(option);
  }

  // 渲染填写完成趋势图表
  function renderCompletionTrendChart(data: any[]) {
    if (!completionTrendChart.value || !Array.isArray(data)) return;

    if (chartInstances.completionTrend) {
      chartInstances.completionTrend.dispose();
    }

    chartInstances.completionTrend = echarts.init(completionTrendChart.value);

    const option = {
      tooltip: {
        trigger: 'axis',
      },
      legend: {
        data: ['完成率', '应填写人数', '已填写人数'],
      },
      xAxis: {
        type: 'category',
        data: data.map(item => item.period || '未知期间'),
      },
      yAxis: [
        {
          type: 'value',
          name: '完成率(%)',
          position: 'left',
          max: 100,
        },
        {
          type: 'value',
          name: '人数',
          position: 'right',
        },
      ],
      series: [
        {
          name: '完成率',
          type: 'line',
          yAxisIndex: 0,
          data: data.map(item => item.completionRate || 0),
          itemStyle: { color: '#1890ff' },
        },
        {
          name: '应填写人数',
          type: 'bar',
          yAxisIndex: 1,
          data: data.map(item => item.totalUsers || 0),
          itemStyle: { color: '#52c41a' },
        },
        {
          name: '已填写人数',
          type: 'bar',
          yAxisIndex: 1,
          data: data.map(item => item.completedUsers || 0),
          itemStyle: { color: '#fa8c16' },
        },
      ],
    };

    chartInstances.completionTrend.setOption(option);
  }

  // 渲染审批效率趋势图表
  function renderApprovalEfficiencyChart(data: any[]) {
    if (!approvalEfficiencyChart.value || !Array.isArray(data)) return;

    if (chartInstances.approvalEfficiency) {
      chartInstances.approvalEfficiency.dispose();
    }

    chartInstances.approvalEfficiency = echarts.init(approvalEfficiencyChart.value);

    const option = {
      tooltip: {
        trigger: 'axis',
      },
      legend: {
        data: ['审批效率', '平均审批时长'],
      },
      xAxis: {
        type: 'category',
        data: data.map(item => item.period || '未知期间'),
      },
      yAxis: [
        {
          type: 'value',
          name: '审批效率(%)',
          position: 'left',
          max: 100,
        },
        {
          type: 'value',
          name: '审批时长(天)',
          position: 'right',
        },
      ],
      series: [
        {
          name: '审批效率',
          type: 'line',
          yAxisIndex: 0,
          data: data.map(item => item.approvalEfficiency || 0),
          itemStyle: { color: '#52c41a' },
        },
        {
          name: '平均审批时长',
          type: 'line',
          yAxisIndex: 1,
          data: data.map(item => item.avgApprovalDays || 0),
          itemStyle: { color: '#fa8c16' },
        },
      ],
    };

    chartInstances.approvalEfficiency.setOption(option);
  }

  // 获取状态颜色
  function getStatusColor(status: string | number) {
    const colorMap = {
      '0': '#ff4d4f', // 未填写 - 红色
      '1': '#52c41a', // 已填写 - 绿色
      '2': '#fa8c16', // 待审批 - 橙色
      '3': '#1890ff', // 已审批 - 蓝色
    };
    return colorMap[String(status)] || '#d9d9d9';
  }

  // 获取审批状态颜色
  function getApprovalStatusColor(status: string | number) {
    const colorMap = {
      '1': '#fa8c16', // 待提交 - 橙色
      '2': '#722ed1', // 待审批 - 紫色
      '3': '#52c41a', // 已审批 - 绿色
    };
    return colorMap[String(status)] || '#d9d9d9';
  }

  onMounted(async () => {
    // 设置默认查询条件（上个月）
    const now = new Date();
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const lastMonthStr = lastMonth.getFullYear() + String(lastMonth.getMonth() + 1).padStart(2, '0');

    // 等待表单完全初始化
    await nextTick();

    const defaultParams = {
      timeType: 'month',
      startMonth: lastMonthStr,
      endMonth: lastMonthStr,
    };

    await setFieldsValue(defaultParams);

    // 再次等待确保表单值已设置
    await nextTick();

    // 延迟加载数据，确保所有组件都已完全初始化
    setTimeout(() => {
      loadData(defaultParams);
    }, 200);
  });
</script>

<style lang="less" scoped>
  .workhour-completion {
    // 响应式设计
    @media (max-width: 768px) {
      .overview-section {
        .ant-col {
          margin-bottom: 16px;
        }

        .stat-card .stat-content {
          flex-direction: column;
          text-align: center;

          .stat-icon {
            margin-right: 0;
            margin-bottom: 12px;
          }
        }
      }

      .status-analysis,
      .trend-analysis {
        .ant-col {
          margin-bottom: 16px;
        }
      }

      .table-section {
        padding: 16px;
      }
    }
    .search-section {
      .search-card {
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

        .search-title {
          display: flex;
          align-items: center;
          font-weight: 600;
          color: #262626;

          .title-icon {
            margin-right: 8px;
            color: #1890ff;
          }
        }

        .ant-card-body {
          padding: 20px;
        }
      }
    }

    .overview-section {
      .stat-card {
        border-radius: 8px;
        transition: all 0.3s ease;
        overflow: hidden;
        position: relative;

        &:hover {
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
          transform: translateY(-4px);
        }

        &.total-users {
          background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
          border-left: 4px solid #1890ff;
        }

        &.completed-users {
          background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
          border-left: 4px solid #52c41a;
        }

        &.completion-rate {
          background: linear-gradient(135deg, #fff7e6 0%, #ffd591 100%);
          border-left: 4px solid #fa8c16;
        }

        &.pending-approval {
          background: linear-gradient(135deg, #fff1f0 0%, #ffccc7 100%);
          border-left: 4px solid #ff4d4f;
        }

        .stat-content {
          display: flex;
          align-items: center;
          padding: 16px;

          .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            margin-right: 16px;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
          }

          .stat-info {
            flex: 1;

            .ant-statistic {
              .ant-statistic-title {
                font-size: 14px;
                color: #666;
                margin-bottom: 4px;
                font-weight: 500;
              }

              .ant-statistic-content {
                margin-bottom: 4px;
              }
            }

            .stat-desc {
              font-size: 12px;
              color: #999;
              line-height: 1.4;
            }
          }
        }
      }
    }

    .status-analysis,
    .trend-analysis {
      .chart-card {
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }

        .chart-title {
          display: flex;
          align-items: center;
          font-weight: 600;
          color: #262626;

          .title-icon {
            margin-right: 8px;
            color: #1890ff;
          }
        }

        .ant-card-body {
          padding: 20px;
        }

        .chart-container {
          height: 300px;
          border-radius: 6px;
          background: linear-gradient(135deg, #fafafa 0%, #f0f0f0 100%);
          position: relative;

          &::before {
            content: '';
            position: absolute;
            inset: 0;
            background: linear-gradient(45deg, transparent 49%, rgba(255, 255, 255, 0.1) 50%, transparent 51%);
            pointer-events: none;
          }
        }
      }
    }

    .table-section {
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      padding: 20px;

      .ant-tabs {
        .ant-tabs-nav {
          margin-bottom: 16px;

          .ant-tabs-tab {
            font-weight: 500;
            padding: 12px 20px;
            border-radius: 6px 6px 0 0;
            transition: all 0.3s ease;

            &:hover {
              background: #f0f0f0;
            }

            &.ant-tabs-tab-active {
              font-weight: 600;
              background: #e6f7ff;
              color: #1890ff;
            }
          }

          .ant-tabs-ink-bar {
            background: #1890ff;
            height: 3px;
            border-radius: 2px;
          }
        }

        .ant-tabs-content-holder {
          padding-top: 0;
        }
      }
    }

    .action-tips {
      margin-top: 20px;

      .ant-alert {
        border-radius: 8px;
        border: none;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

        .ant-alert-message {
          font-weight: 600;
          color: #1890ff;
        }

        .ant-alert-description {
          margin-top: 8px;
          line-height: 1.6;
          color: #666;
        }

        .ant-alert-icon {
          color: #1890ff;
        }
      }
    }

    // 表格样式优化
    :deep(.ant-table) {
      border-radius: 6px;
      overflow: hidden;

      .ant-table-thead > tr > th {
        background: linear-gradient(135deg, #fafafa 0%, #f0f0f0 100%);
        font-weight: 600;
        color: #262626;
        border-bottom: 2px solid #e8e8e8;
      }

      .ant-table-tbody > tr {
        transition: all 0.3s ease;

        &:hover > td {
          background-color: #f0f7ff;
        }

        &:nth-child(even) {
          background-color: #fafafa;
        }
      }

      .ant-btn-link {
        padding: 4px 8px;
        height: auto;
        line-height: 1.4;
        border-radius: 4px;
        transition: all 0.3s ease;

        &:hover {
          background-color: #e6f7ff;
          color: #1890ff;
        }
      }
    }

    // 表单样式优化
    :deep(.ant-form) {
      .ant-form-item {
        margin-bottom: 16px;

        .ant-form-item-label {
          font-weight: 500;
        }
      }

      .ant-select,
      .ant-picker {
        border-radius: 6px;
        transition: all 0.3s ease;

        &:hover {
          border-color: #40a9ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
        }

        &.ant-select-focused,
        &.ant-picker-focused {
          border-color: #40a9ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
      }
    }

    // 按钮样式优化
    :deep(.ant-btn) {
      border-radius: 6px;
      font-weight: 500;
      transition: all 0.3s ease;

      &.ant-btn-primary {
        background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
        border: none;
        box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);

        &:hover {
          background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
          box-shadow: 0 4px 8px rgba(24, 144, 255, 0.4);
          transform: translateY(-1px);
        }
      }

      &.ant-btn-default {
        border-color: #d9d9d9;

        &:hover {
          border-color: #40a9ff;
          color: #40a9ff;
        }
      }
    }
  }
</style>
