package com.xinghuo.project.schema.service;

import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.project.schema.entity.ProjectTemplateEntity;
import com.xinghuo.project.schema.entity.ProjectSchemaWbsEntity;
import com.xinghuo.project.schema.entity.ProjectSchemaPhaseEntity;
import com.xinghuo.project.schema.model.ProjectTemplatePagination;
import com.xinghuo.project.schema.model.vo.ProjectTemplateVO;

import java.util.List;
import java.util.Map;

/**
 * 项目模板服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
public interface ProjectTemplateService extends BaseService<ProjectTemplateEntity> {

    /**
     * 获取项目模板列表
     *
     * @param pagination 分页参数
     * @return 模板列表
     */
    List<ProjectTemplateVO> getList(ProjectTemplatePagination pagination);

    /**
     * 根据状态获取模板列表
     *
     * @param status 状态
     * @return 模板列表
     */
    List<ProjectTemplateEntity> getListByStatus(Integer status);

    /**
     * 根据类型获取模板列表
     *
     * @param typeId 类型ID
     * @return 模板列表
     */
    List<ProjectTemplateEntity> getListByType(String typeId);

    /**
     * 获取模板详情（包含WBS和阶段配置）
     *
     * @param id 模板ID
     * @return 模板详情
     */
    ProjectTemplateVO getDetailInfo(String id);

    /**
     * 获取模板基本信息
     *
     * @param id 模板ID
     * @return 模板信息
     */
    ProjectTemplateEntity getInfo(String id);

    /**
     * 创建项目模板
     *
     * @param templateVO 模板信息（包含WBS和阶段配置）
     * @return 模板ID
     */
    String create(ProjectTemplateVO templateVO);

    /**
     * 更新项目模板
     *
     * @param id 模板ID
     * @param templateVO 模板信息（包含WBS和阶段配置）
     */
    void update(String id, ProjectTemplateVO templateVO);

    /**
     * 删除项目模板
     *
     * @param id 模板ID
     */
    void delete(String id);

    /**
     * 批量删除项目模板
     *
     * @param ids 模板ID列表
     */
    void batchDelete(List<String> ids);

    /**
     * 更新模板状态
     *
     * @param id 模板ID
     * @param status 状态
     */
    void updateStatus(String id, Integer status);

    /**
     * 批量更新状态
     *
     * @param ids 模板ID列表
     * @param status 状态
     */
    void batchUpdateStatus(List<String> ids, Integer status);

    /**
     * 启用模板
     *
     * @param id 模板ID
     */
    void enable(String id);

    /**
     * 禁用模板
     *
     * @param id 模板ID
     */
    void disable(String id);

    /**
     * 批量启用模板
     *
     * @param ids 模板ID列表
     */
    void batchEnable(List<String> ids);

    /**
     * 批量禁用模板
     *
     * @param ids 模板ID列表
     */
    void batchDisable(List<String> ids);

    /**
     * 检查模板名称是否存在
     *
     * @param name 模板名称
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean isExistByName(String name, String excludeId);

    /**
     * 检查模板编码是否存在
     *
     * @param code 模板编码
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean isExistByCode(String code, String excludeId);

    /**
     * 复制项目模板
     *
     * @param id 源模板ID
     * @param newName 新模板名称
     * @return 新模板ID
     */
    String copy(String id, String newName);

    /**
     * 获取模板选择列表
     *
     * @param keyword 关键字
     * @return 模板列表
     */
    List<ProjectTemplateEntity> getSelectList(String keyword);

    /**
     * 生成模板编码
     *
     * @return 模板编码
     */
    String generateCode();

    /**
     * 获取模板使用情况
     *
     * @param id 模板ID
     * @return 使用情况统计
     */
    Map<String, Object> getTemplateUsageInfo(String id);

    /**
     * 从WBS模板导入配置
     *
     * @param templateId 项目模板ID
     * @param wbsTemplateId WBS模板ID
     */
    void importFromWbsTemplate(String templateId, String wbsTemplateId);

    /**
     * 从阶段模板导入配置
     *
     * @param templateId 项目模板ID
     * @param phaseTemplateId 阶段模板ID
     */
    void importFromPhaseTemplate(String templateId, String phaseTemplateId);

    /**
     * 从标准阶段库导入阶段
     *
     * @param templateId 项目模板ID
     * @param phaseIds 阶段ID列表
     */
    void importFromPhaseLibrary(String templateId, List<String> phaseIds);

    /**
     * 获取WBS配置列表
     *
     * @param templateId 模板ID
     * @return WBS配置列表
     */
    List<ProjectSchemaWbsEntity> getWbsConfigs(String templateId);

    /**
     * 获取阶段配置列表
     *
     * @param templateId 模板ID
     * @return 阶段配置列表
     */
    List<ProjectSchemaPhaseEntity> getPhaseConfigs(String templateId);

    /**
     * 保存WBS配置
     *
     * @param templateId 模板ID
     * @param wbsConfigs WBS配置列表
     */
    void saveWbsConfigs(String templateId, List<ProjectSchemaWbsEntity> wbsConfigs);

    /**
     * 保存阶段配置
     *
     * @param templateId 模板ID
     * @param phaseConfigs 阶段配置列表
     */
    void savePhaseConfigs(String templateId, List<ProjectSchemaPhaseEntity> phaseConfigs);

    /**
     * 删除WBS配置
     *
     * @param wbsIds WBS配置ID列表
     */
    void deleteWbsConfigs(List<String> wbsIds);

    /**
     * 删除阶段配置
     *
     * @param phaseIds 阶段配置ID列表
     */
    void deletePhaseConfigs(List<String> phaseIds);

    /**
     * 应用到项目
     *
     * @param templateId 模板ID
     * @param projectIds 项目ID列表
     */
    void applyToProjects(String templateId, List<String> projectIds);
}
