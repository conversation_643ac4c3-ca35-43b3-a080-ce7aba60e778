package com.xinghuo.project.schema.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.project.schema.entity.ProjectSchemaWbsEntity;
import com.xinghuo.project.schema.model.ProjectSchemaWbsPagination;
import com.xinghuo.project.schema.service.ProjectSchemaWbsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 项目模板WBS计划管理控制器
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
@Slf4j
@Tag(name = "项目模板WBS计划管理", description = "项目模板WBS计划管理相关接口")
@RestController
@RequestMapping("/api/project/schema/wbs")
public class ProjectSchemaWbsController {

    @Resource
    private ProjectSchemaWbsService projectSchemaWbsService;

    /**
     * 获取WBS计划列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取WBS计划列表")
    public ActionResult<PageListVO<ProjectSchemaWbsEntity>> list(@RequestBody ProjectSchemaWbsPagination pagination) {
        try {
            List<ProjectSchemaWbsEntity> list = projectSchemaWbsService.getList(pagination);

            // 对结果进行数据转换和补充
            for (ProjectSchemaWbsEntity entity : list) {
                // 可以在这里添加其他关联数据的查询和设置
                // 例如：责任角色名称、约束类型名称等
            }

            PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
            return ActionResult.page(list, page);
        } catch (Exception e) {
            log.error("获取WBS计划列表失败", e);
            return ActionResult.fail("获取WBS计划列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据项目模板ID获取WBS计划列表
     */
    @GetMapping("/getListByTemplateId/{projectTemplateId}")
    @Operation(summary = "根据项目模板ID获取WBS计划列表")
    public ActionResult<List<ProjectSchemaWbsEntity>> getListByTemplateId(
            @Parameter(description = "项目模板ID") @PathVariable String projectTemplateId) {
        try {
            List<ProjectSchemaWbsEntity> list = projectSchemaWbsService.getListByTemplateId(projectTemplateId);
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("根据项目模板ID获取WBS计划列表失败", e);
            return ActionResult.fail("获取WBS计划列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取WBS计划详情
     */
    @GetMapping("/getInfo/{id}")
    @Operation(summary = "获取WBS计划详情")
    public ActionResult<ProjectSchemaWbsEntity> getInfo(
            @Parameter(description = "WBS计划ID") @PathVariable String id) {
        try {
            ProjectSchemaWbsEntity entity = projectSchemaWbsService.getInfo(id);
            if (entity == null) {
                return ActionResult.fail("WBS计划不存在");
            }
            return ActionResult.success(entity);
        } catch (Exception e) {
            log.error("获取WBS计划详情失败", e);
            return ActionResult.fail("获取WBS计划详情失败：" + e.getMessage());
        }
    }

    /**
     * 创建WBS计划
     */
    @PostMapping("/create")
    @Operation(summary = "创建WBS计划")
    public ActionResult<String> create(@RequestBody @Valid ProjectSchemaWbsEntity entity) {
        try {
            String id = projectSchemaWbsService.create(entity);
            return ActionResult.success("创建成功", id);
        } catch (Exception e) {
            log.error("创建WBS计划失败", e);
            return ActionResult.fail("创建WBS计划失败：" + e.getMessage());
        }
    }

    /**
     * 更新WBS计划
     */
    @PutMapping("/update/{id}")
    @Operation(summary = "更新WBS计划")
    public ActionResult<String> update(
            @Parameter(description = "WBS计划ID") @PathVariable String id,
            @RequestBody @Valid ProjectSchemaWbsEntity entity) {
        try {
            projectSchemaWbsService.update(id, entity);
            return ActionResult.success("更新成功");
        } catch (Exception e) {
            log.error("更新WBS计划失败", e);
            return ActionResult.fail("更新WBS计划失败：" + e.getMessage());
        }
    }

    /**
     * 删除WBS计划
     */
    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除WBS计划")
    public ActionResult<String> delete(
            @Parameter(description = "WBS计划ID") @PathVariable String id) {
        try {
            projectSchemaWbsService.delete(id);
            return ActionResult.success("删除成功");
        } catch (Exception e) {
            log.error("删除WBS计划失败", e);
            return ActionResult.fail("删除WBS计划失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除WBS计划
     */
    @DeleteMapping("/batchDelete")
    @Operation(summary = "批量删除WBS计划")
    public ActionResult<String> batchDelete(@RequestBody List<String> ids) {
        try {
            projectSchemaWbsService.batchDelete(ids);
            return ActionResult.success("批量删除成功");
        } catch (Exception e) {
            log.error("批量删除WBS计划失败", e);
            return ActionResult.fail("批量删除WBS计划失败：" + e.getMessage());
        }
    }

    /**
     * 从WBS模板导入WBS计划
     */
    @PostMapping("/importFromWbsTemplate/{projectTemplateId}")
    @Operation(summary = "从WBS模板导入WBS计划")
    public ActionResult<String> importFromWbsTemplate(
            @Parameter(description = "项目模板ID") @PathVariable String projectTemplateId,
            @RequestParam String wbsTemplateId,
            @RequestParam(required = false) String parentId) {
        try {
            projectSchemaWbsService.importFromWbsTemplate(projectTemplateId, wbsTemplateId, parentId);
            return ActionResult.success("导入成功");
        } catch (Exception e) {
            log.error("从WBS模板导入失败", e);
            return ActionResult.fail("导入失败：" + e.getMessage());
        }
    }

    /**
     * 从活动库添加活动到WBS计划
     */
    @PostMapping("/addActivitiesFromLibrary/{projectTemplateId}")
    @Operation(summary = "从活动库添加活动")
    public ActionResult<String> addActivitiesFromLibrary(
            @Parameter(description = "项目模板ID") @PathVariable String projectTemplateId,
            @RequestBody List<String> activityIds,
            @RequestParam(required = false) String parentId) {
        try {
            projectSchemaWbsService.addActivitiesFromLibrary(projectTemplateId, activityIds, parentId);
            return ActionResult.success("添加活动成功");
        } catch (Exception e) {
            log.error("从活动库添加活动失败", e);
            return ActionResult.fail("添加活动失败：" + e.getMessage());
        }
    }

    /**
     * 调整WBS计划序号
     */
    @PutMapping("/adjustSeqNo/{id}")
    @Operation(summary = "调整WBS计划序号")
    public ActionResult<String> adjustSeqNo(
            @Parameter(description = "WBS计划ID") @PathVariable String id,
            @RequestParam String direction) {
        try {
            projectSchemaWbsService.adjustSeqNo(id, direction);
            return ActionResult.success("调整序号成功");
        } catch (Exception e) {
            log.error("调整WBS序号失败", e);
            return ActionResult.fail("调整序号失败：" + e.getMessage());
        }
    }

    /**
     * 重新计算WBS编码
     */
    @PutMapping("/recalculateWbsCode/{projectTemplateId}")
    @Operation(summary = "重新计算WBS编码")
    public ActionResult<String> recalculateWbsCode(
            @Parameter(description = "项目模板ID") @PathVariable String projectTemplateId) {
        try {
            projectSchemaWbsService.recalculateWbsCode(projectTemplateId);
            return ActionResult.success("重新计算WBS编码成功");
        } catch (Exception e) {
            log.error("重新计算WBS编码失败", e);
            return ActionResult.fail("重新计算WBS编码失败：" + e.getMessage());
        }
    }

    /**
     * 重新计算层级深度
     */
    @PutMapping("/recalculateLevel/{projectTemplateId}")
    @Operation(summary = "重新计算层级深度")
    public ActionResult<String> recalculateLevel(
            @Parameter(description = "项目模板ID") @PathVariable String projectTemplateId) {
        try {
            projectSchemaWbsService.recalculateLevel(projectTemplateId);
            return ActionResult.success("重新计算层级深度成功");
        } catch (Exception e) {
            log.error("重新计算层级深度失败", e);
            return ActionResult.fail("重新计算层级深度失败：" + e.getMessage());
        }
    }

    /**
     * 移动WBS节点
     */
    @PutMapping("/moveNode/{id}")
    @Operation(summary = "移动WBS节点")
    public ActionResult<String> moveNode(
            @Parameter(description = "WBS计划ID") @PathVariable String id,
            @RequestParam(required = false) String newParentId,
            @RequestParam(required = false) Integer newSeqNo) {
        try {
            projectSchemaWbsService.moveNode(id, newParentId, newSeqNo);
            return ActionResult.success("移动节点成功");
        } catch (Exception e) {
            log.error("移动WBS节点失败", e);
            return ActionResult.fail("移动节点失败：" + e.getMessage());
        }
    }

    /**
     * 复制WBS节点
     */
    @PostMapping("/copyNode/{id}")
    @Operation(summary = "复制WBS节点")
    public ActionResult<String> copyNode(
            @Parameter(description = "WBS计划ID") @PathVariable String id,
            @RequestParam(required = false) String newParentId) {
        try {
            String newId = projectSchemaWbsService.copyNode(id, newParentId);
            return ActionResult.success("复制节点成功", newId);
        } catch (Exception e) {
            log.error("复制WBS节点失败", e);
            return ActionResult.fail("复制节点失败：" + e.getMessage());
        }
    }

    /**
     * 获取WBS树形结构
     */
    @GetMapping("/getWbsTree/{projectTemplateId}")
    @Operation(summary = "获取WBS树形结构")
    public ActionResult<List<ProjectSchemaWbsEntity>> getWbsTree(
            @Parameter(description = "项目模板ID") @PathVariable String projectTemplateId) {
        try {
            List<ProjectSchemaWbsEntity> tree = projectSchemaWbsService.getWbsTree(projectTemplateId);
            return ActionResult.success(tree);
        } catch (Exception e) {
            log.error("获取WBS树形结构失败", e);
            return ActionResult.fail("获取WBS树形结构失败：" + e.getMessage());
        }
    }

    /**
     * 获取子节点列表
     */
    @GetMapping("/getChildren/{parentId}")
    @Operation(summary = "获取子节点列表")
    public ActionResult<List<ProjectSchemaWbsEntity>> getChildren(
            @Parameter(description = "父级ID") @PathVariable String parentId) {
        try {
            List<ProjectSchemaWbsEntity> children = projectSchemaWbsService.getChildren(parentId);
            return ActionResult.success(children);
        } catch (Exception e) {
            log.error("获取子节点列表失败", e);
            return ActionResult.fail("获取子节点列表失败：" + e.getMessage());
        }
    }

    /**
     * 检查是否存在子节点
     */
    @GetMapping("/hasChildren/{parentId}")
    @Operation(summary = "检查是否存在子节点")
    public ActionResult<Boolean> hasChildren(
            @Parameter(description = "父级ID") @PathVariable String parentId) {
        try {
            boolean hasChildren = projectSchemaWbsService.hasChildren(parentId);
            return ActionResult.success(hasChildren);
        } catch (Exception e) {
            log.error("检查子节点失败", e);
            return ActionResult.fail("检查子节点失败：" + e.getMessage());
        }
    }

    /**
     * 获取WBS统计信息
     */
    @GetMapping("/getWbsStatistics/{projectTemplateId}")
    @Operation(summary = "获取WBS统计信息")
    public ActionResult<Map<String, Object>> getWbsStatistics(
            @Parameter(description = "项目模板ID") @PathVariable String projectTemplateId) {
        try {
            Map<String, Object> statistics = projectSchemaWbsService.getWbsStatistics(projectTemplateId);
            return ActionResult.success(statistics);
        } catch (Exception e) {
            log.error("获取WBS统计信息失败", e);
            return ActionResult.fail("获取WBS统计信息失败：" + e.getMessage());
        }
    }

    /**
     * 验证WBS结构完整性
     */
    @GetMapping("/validateWbsStructure/{projectTemplateId}")
    @Operation(summary = "验证WBS结构完整性")
    public ActionResult<Map<String, Object>> validateWbsStructure(
            @Parameter(description = "项目模板ID") @PathVariable String projectTemplateId) {
        try {
            Map<String, Object> result = projectSchemaWbsService.validateWbsStructure(projectTemplateId);
            return ActionResult.success(result);
        } catch (Exception e) {
            log.error("验证WBS结构失败", e);
            return ActionResult.fail("验证WBS结构失败：" + e.getMessage());
        }
    }

    /**
     * 批量保存WBS计划
     */
    @PostMapping("/batchSave/{projectTemplateId}")
    @Operation(summary = "批量保存WBS计划")
    public ActionResult<String> batchSave(
            @Parameter(description = "项目模板ID") @PathVariable String projectTemplateId,
            @RequestBody List<ProjectSchemaWbsEntity> wbsList) {
        try {
            projectSchemaWbsService.batchSave(projectTemplateId, wbsList);
            return ActionResult.success("批量保存成功");
        } catch (Exception e) {
            log.error("批量保存WBS计划失败", e);
            return ActionResult.fail("批量保存失败：" + e.getMessage());
        }
    }

    /**
     * 获取WBS路径
     */
    @GetMapping("/getWbsPath/{id}")
    @Operation(summary = "获取WBS路径")
    public ActionResult<List<ProjectSchemaWbsEntity>> getWbsPath(
            @Parameter(description = "WBS计划ID") @PathVariable String id) {
        try {
            List<ProjectSchemaWbsEntity> path = projectSchemaWbsService.getWbsPath(id);
            return ActionResult.success(path);
        } catch (Exception e) {
            log.error("获取WBS路径失败", e);
            return ActionResult.fail("获取WBS路径失败：" + e.getMessage());
        }
    }

    /**
     * 检查WBS名称是否存在
     */
    @GetMapping("/checkNameExists")
    @Operation(summary = "检查WBS名称是否存在")
    public ActionResult<Boolean> checkNameExists(
            @RequestParam String projectTemplateId,
            @RequestParam String name,
            @RequestParam(required = false) String excludeId) {
        try {
            boolean exists = projectSchemaWbsService.isExistByName(projectTemplateId, name, excludeId);
            return ActionResult.success(exists);
        } catch (Exception e) {
            log.error("检查WBS名称失败", e);
            return ActionResult.fail("检查WBS名称失败：" + e.getMessage());
        }
    }

    /**
     * 检查WBS编码是否存在
     */
    @GetMapping("/checkWbsCodeExists")
    @Operation(summary = "检查WBS编码是否存在")
    public ActionResult<Boolean> checkWbsCodeExists(
            @RequestParam String projectTemplateId,
            @RequestParam String wbsCode,
            @RequestParam(required = false) String excludeId) {
        try {
            boolean exists = projectSchemaWbsService.isExistByWbsCode(projectTemplateId, wbsCode, excludeId);
            return ActionResult.success(exists);
        } catch (Exception e) {
            log.error("检查WBS编码失败", e);
            return ActionResult.fail("检查WBS编码失败：" + e.getMessage());
        }
    }

    /**
     * 生成WBS编码
     */
    @GetMapping("/generateWbsCode")
    @Operation(summary = "生成WBS编码")
    public ActionResult<String> generateWbsCode(
            @RequestParam String projectTemplateId,
            @RequestParam(required = false) String parentId) {
        try {
            String wbsCode = projectSchemaWbsService.generateWbsCode(projectTemplateId, parentId);
            return ActionResult.success(wbsCode);
        } catch (Exception e) {
            log.error("生成WBS编码失败", e);
            return ActionResult.fail("生成WBS编码失败：" + e.getMessage());
        }
    }

    /**
     * 更新前置任务关系
     */
    @PutMapping("/updatePredecessors/{id}")
    @Operation(summary = "更新前置任务关系")
    public ActionResult<String> updatePredecessors(
            @Parameter(description = "WBS计划ID") @PathVariable String id,
            @RequestParam String predecessors) {
        try {
            projectSchemaWbsService.updatePredecessors(id, predecessors);
            return ActionResult.success("更新前置任务关系成功");
        } catch (Exception e) {
            log.error("更新前置任务关系失败", e);
            return ActionResult.fail("更新前置任务关系失败：" + e.getMessage());
        }
    }

    /**
     * 获取前置任务列表
     */
    @GetMapping("/getPredecessors/{id}")
    @Operation(summary = "获取前置任务列表")
    public ActionResult<List<Map<String, Object>>> getPredecessors(
            @Parameter(description = "WBS计划ID") @PathVariable String id) {
        try {
            List<Map<String, Object>> predecessors = projectSchemaWbsService.getPredecessors(id);
            return ActionResult.success(predecessors);
        } catch (Exception e) {
            log.error("获取前置任务列表失败", e);
            return ActionResult.fail("获取前置任务列表失败：" + e.getMessage());
        }
    }

    /**
     * 检查循环依赖
     */
    @GetMapping("/checkCircularDependency")
    @Operation(summary = "检查循环依赖")
    public ActionResult<Boolean> checkCircularDependency(
            @RequestParam String id,
            @RequestParam String predecessorId) {
        try {
            boolean hasCircular = projectSchemaWbsService.checkCircularDependency(id, predecessorId);
            return ActionResult.success(hasCircular);
        } catch (Exception e) {
            log.error("检查循环依赖失败", e);
            return ActionResult.fail("检查循环依赖失败：" + e.getMessage());
        }
    }

    /**
     * 获取下一个序号
     */
    @GetMapping("/getNextSeqNo")
    @Operation(summary = "获取下一个序号")
    public ActionResult<Integer> getNextSeqNo(
            @RequestParam String projectTemplateId,
            @RequestParam(required = false) String parentId) {
        try {
            Integer nextSeqNo = projectSchemaWbsService.getNextSeqNo(projectTemplateId, parentId);
            return ActionResult.success(nextSeqNo);
        } catch (Exception e) {
            log.error("获取下一个序号失败", e);
            return ActionResult.fail("获取下一个序号失败：" + e.getMessage());
        }
    }
}
