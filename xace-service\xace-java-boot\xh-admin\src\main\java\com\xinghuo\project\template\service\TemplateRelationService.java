package com.xinghuo.project.template.service;

import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.project.template.entity.TemplateRelationEntity;

import java.util.List;

/**
 * 通用模板关联服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
public interface TemplateRelationService extends BaseService<TemplateRelationEntity> {

    /**
     * 根据源模板查询关联的目标模板ID列表
     *
     * @param sourceTemplateId 源模板ID
     * @param sourceTemplateType 源模板类型
     * @param targetTemplateType 目标模板类型
     * @return 目标模板ID列表
     */
    List<String> getTargetTemplateIds(String sourceTemplateId, String sourceTemplateType, String targetTemplateType);

    /**
     * 根据目标模板查询关联的源模板ID列表
     *
     * @param targetTemplateId 目标模板ID
     * @param targetTemplateType 目标模板类型
     * @param sourceTemplateType 源模板类型
     * @return 源模板ID列表
     */
    List<String> getSourceTemplateIds(String targetTemplateId, String targetTemplateType, String sourceTemplateType);

    /**
     * 更新模板关联关系
     *
     * @param sourceTemplateId 源模板ID
     * @param sourceTemplateType 源模板类型
     * @param targetTemplateIds 目标模板ID列表
     * @param targetTemplateType 目标模板类型
     */
    void updateTemplateRelations(String sourceTemplateId, String sourceTemplateType, 
                                List<String> targetTemplateIds, String targetTemplateType);

    /**
     * 删除源模板的所有关联关系
     *
     * @param sourceTemplateId 源模板ID
     * @param sourceTemplateType 源模板类型
     */
    void deleteBySourceTemplate(String sourceTemplateId, String sourceTemplateType);

    /**
     * 删除目标模板的所有关联关系
     *
     * @param targetTemplateId 目标模板ID
     * @param targetTemplateType 目标模板类型
     */
    void deleteByTargetTemplate(String targetTemplateId, String targetTemplateType);

    /**
     * 检查关联关系是否存在
     *
     * @param sourceTemplateId 源模板ID
     * @param sourceTemplateType 源模板类型
     * @param targetTemplateId 目标模板ID
     * @param targetTemplateType 目标模板类型
     * @return 是否存在
     */
    boolean isRelationExists(String sourceTemplateId, String sourceTemplateType, 
                            String targetTemplateId, String targetTemplateType);

    /**
     * 添加模板关联关系
     *
     * @param sourceTemplateId 源模板ID
     * @param sourceTemplateType 源模板类型
     * @param targetTemplateId 目标模板ID
     * @param targetTemplateType 目标模板类型
     */
    void addTemplateRelation(String sourceTemplateId, String sourceTemplateType, 
                            String targetTemplateId, String targetTemplateType);

    /**
     * 删除指定的模板关联关系
     *
     * @param sourceTemplateId 源模板ID
     * @param sourceTemplateType 源模板类型
     * @param targetTemplateId 目标模板ID
     * @param targetTemplateType 目标模板类型
     */
    void removeTemplateRelation(String sourceTemplateId, String sourceTemplateType, 
                               String targetTemplateId, String targetTemplateType);

    /**
     * 批量删除源模板的关联关系
     *
     * @param sourceTemplateIds 源模板ID列表
     * @param sourceTemplateType 源模板类型
     */
    void batchDeleteBySourceTemplateIds(List<String> sourceTemplateIds, String sourceTemplateType);

    /**
     * 根据源模板类型查询所有关联关系
     *
     * @param sourceTemplateType 源模板类型
     * @return 关联关系列表
     */
    List<TemplateRelationEntity> getBySourceTemplateType(String sourceTemplateType);

    /**
     * 根据目标模板类型查询所有关联关系
     *
     * @param targetTemplateType 目标模板类型
     * @return 关联关系列表
     */
    List<TemplateRelationEntity> getByTargetTemplateType(String targetTemplateType);
}
