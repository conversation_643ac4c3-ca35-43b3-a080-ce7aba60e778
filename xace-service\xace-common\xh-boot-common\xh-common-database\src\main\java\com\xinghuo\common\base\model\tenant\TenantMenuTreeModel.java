package com.xinghuo.common.base.model.tenant;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.xinghuo.common.util.tree.SumTree;
import lombok.Data;

/**
 *
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TenantMenuTreeModel extends SumTree {
    private String fullName;
    private String icon;
    private Integer type;
    private Long sortCode;
    private String category;
    private boolean disabled;
    private String urlAddress;
}
