package com.xinghuo.project.core.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.project.core.model.projectBase.ProjectBaseInfoForm;
import com.xinghuo.project.core.model.projectBase.ProjectBaseInfoVO;
import com.xinghuo.project.core.service.ProjectBaseInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 项目基础信息控制器
 *
 * <AUTHOR>
 * @date 2024-08-05
 */
@Slf4j
@Tag(name = "项目基础信息管理", description = "项目基础信息管理相关接口")
@RestController
@RequestMapping("/api/project/core/project/base-info")
public class ProjectBaseInfoController {

    @Resource
    private ProjectBaseInfoService projectBaseInfoService;

    /**
     * 获取项目基础信息详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取项目基础信息详情")
    public ActionResult<ProjectBaseInfoVO> getInfo(
            @Parameter(description = "项目ID") @PathVariable String id) {
        ProjectBaseInfoVO vo = projectBaseInfoService.getInfo(id);
        if (vo == null) {
            return ActionResult.fail("项目不存在");
        }
        return ActionResult.success(vo);
    }

    /**
     * 更新项目基础信息
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新项目基础信息")
    public ActionResult<String> update(
            @Parameter(description = "项目ID") @PathVariable String id,
            @RequestBody @Valid ProjectBaseInfoForm form) {
        projectBaseInfoService.update(id, form);
        return ActionResult.success("更新成功");
    }
}
