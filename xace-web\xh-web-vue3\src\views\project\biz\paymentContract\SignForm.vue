<template>
  <BasicModal v-bind="$attrs" @register="registerModal" title="签订采购合同" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, unref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form';
  import { FormSchema } from '/@/components/Form/src/types/form';
  import { signPaycontract } from '/@/api/project/paycontract';
  import { useMessage } from '/@/hooks/web/useMessage';

  const { createMessage } = useMessage();
  const emit = defineEmits(['register', 'reload']);
  const paycontractId = ref('');

  // 表单配置
  const formSchemas: FormSchema[] = [
    {
      field: 'cNo',
      label: '采购合同编号',
      component: 'Input',
      componentProps: { placeholder: '请输入采购合同编号', maxlength: 50 },
      rules: [
        { required: true, trigger: 'blur', message: '请输入采购合同编号' },
        { max: 50, message: '采购合同编号最多为50个字符', trigger: 'blur' },
      ],
    },
    {
      field: 'signDate',
      label: '签订日期',
      component: 'DatePicker',
      componentProps: { placeholder: '请选择签订日期', style: 'width: 100%' },
      rules: [{ required: true, trigger: 'change', message: '请选择签订日期' }],
    },
  ];

  // 注册表单
  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 100,
    schemas: formSchemas,
    showActionButtonGroup: false,
  });

  // 注册模态框
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async data => {
    resetFields();
    setModalProps({ confirmLoading: false });

    paycontractId.value = data.paycontractId;

    // 如果有记录数据，则设置表单值
    if (data.record) {
      setFieldsValue({
        cNo: data.record.cNo,
      });
    }
  });

  // 提交表单
  async function handleSubmit() {
    try {
      const values = await validate();
      setModalProps({ confirmLoading: true });

      await signPaycontract(paycontractId.value, values.cNo, values.signDate);
      createMessage.success('签订成功');

      closeModal();
      emit('reload');
    } catch (error) {
      console.error('表单验证失败或提交出错:', error);
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
