package com.xinghuo.project.schema.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 项目模板阶段配置选择列表视图对象
 * 用于下拉选择框等场景，只包含必要的id和fullName字段
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
@Data
@Schema(description = "项目模板阶段配置选择列表视图对象")
public class ProjectSchemaPhaseSelectVO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private String id;

    /**
     * 完整名称 (格式: [序号] 阶段名称)
     * 用于option显示，符合fullName和id的规范要求
     */
    @Schema(description = "完整名称")
    private String fullName;

    /**
     * 项目模板ID
     */
    @Schema(description = "项目模板ID")
    private String projectTemplateId;

    /**
     * 阶段库ID
     */
    @Schema(description = "阶段库ID")
    private String phaseLibraryId;

    /**
     * 序号 (用于构建fullName)
     */
    @Schema(description = "序号")
    private Integer seqNo;

    /**
     * 阶段编码 (用于构建fullName)
     */
    @Schema(description = "阶段编码")
    private String phaseCode;

    /**
     * 阶段名称 (用于构建fullName)
     */
    @Schema(description = "阶段名称")
    private String phaseName;

    /**
     * 计划工期
     */
    @Schema(description = "计划工期（天）")
    private Integer duration;

    /**
     * 完成权重
     */
    @Schema(description = "完成权重（%）")
    private Integer completionWeight;

    /**
     * 是否可裁剪
     */
    @Schema(description = "是否可裁剪")
    private Integer canCut;
}
