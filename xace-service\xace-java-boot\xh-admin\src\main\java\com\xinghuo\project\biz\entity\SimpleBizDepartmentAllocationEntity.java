package com.xinghuo.project.biz.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 简化版业务分部分配实体
 * 核心原则：4个分部 × 2种类型 = 8个字段
 * 汇总数据保留在主表，这里只存分部明细
 * 
 * <AUTHOR>
 * @version V2.1
 * @copyright 易趋集团
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_department_allocation")
public class SimpleBizDepartmentAllocationEntity extends BaseEntityV2.CUDBaseEntityV2<String> {

    /**
     * 业务ID（关联的主业务记录ID）
     */
    @TableField("F_BUSINESS_ID")
    private String businessId;

    /**
     * 业务类型（1-商机 2-合同 3-收款 4-付款）
     */
    @TableField("F_BUSINESS_TYPE")
    private Integer businessType;

    // ==================== 4个分部的营收分配 ====================
    
    /**
     * 一部营收分配
     */
    @TableField("F_YF_YB_AMOUNT")
    private BigDecimal yfYbAmount;

    /**
     * 二部营收分配
     */
    @TableField("F_YF_EB_AMOUNT")
    private BigDecimal yfEbAmount;

    /**
     * 交付营收分配
     */
    @TableField("F_YF_JF_AMOUNT")
    private BigDecimal yfJfAmount;

    /**
     * 综合营收分配
     */
    @TableField("F_YF_OTHER_AMOUNT")
    private BigDecimal yfOtherAmount;

    // ==================== 4个分部的外采分配 ====================
    
    /**
     * 一部外采分配
     */
    @TableField("F_OUT_YB_AMOUNT")
    private BigDecimal outYbAmount;

    /**
     * 二部外采分配
     */
    @TableField("F_OUT_EB_AMOUNT")
    private BigDecimal outEbAmount;

    /**
     * 交付外采分配
     */
    @TableField("F_OUT_JF_AMOUNT")
    private BigDecimal outJfAmount;

    /**
     * 综合外采分配
     */
    @TableField("F_OUT_OTHER_AMOUNT")
    private BigDecimal outOtherAmount;

    // ==================== 业务方法 ====================

    /**
     * 获取分部营收分配总额
     */
    public BigDecimal getTotalYfAmount() {
        return (yfYbAmount != null ? yfYbAmount : BigDecimal.ZERO)
            .add(yfEbAmount != null ? yfEbAmount : BigDecimal.ZERO)
            .add(yfJfAmount != null ? yfJfAmount : BigDecimal.ZERO)
            .add(yfOtherAmount != null ? yfOtherAmount : BigDecimal.ZERO);
    }

    /**
     * 获取分部外采分配总额
     */
    public BigDecimal getTotalOutAmount() {
        return (outYbAmount != null ? outYbAmount : BigDecimal.ZERO)
            .add(outEbAmount != null ? outEbAmount : BigDecimal.ZERO)
            .add(outJfAmount != null ? outJfAmount : BigDecimal.ZERO)
            .add(outOtherAmount != null ? outOtherAmount : BigDecimal.ZERO);
    }

    /**
     * 获取指定分部的营收分配
     */
    public BigDecimal getYfAmountByDept(String deptCode) {
        switch (deptCode.toUpperCase()) {
            case "YB": return yfYbAmount != null ? yfYbAmount : BigDecimal.ZERO;
            case "EB": return yfEbAmount != null ? yfEbAmount : BigDecimal.ZERO;
            case "JF": return yfJfAmount != null ? yfJfAmount : BigDecimal.ZERO;
            case "OTHER": case "ZH": return yfOtherAmount != null ? yfOtherAmount : BigDecimal.ZERO;
            default: return BigDecimal.ZERO;
        }
    }

    /**
     * 获取指定分部的外采分配
     */
    public BigDecimal getOutAmountByDept(String deptCode) {
        switch (deptCode.toUpperCase()) {
            case "YB": return outYbAmount != null ? outYbAmount : BigDecimal.ZERO;
            case "EB": return outEbAmount != null ? outEbAmount : BigDecimal.ZERO;
            case "JF": return outJfAmount != null ? outJfAmount : BigDecimal.ZERO;
            case "OTHER": case "ZH": return outOtherAmount != null ? outOtherAmount : BigDecimal.ZERO;
            default: return BigDecimal.ZERO;
        }
    }

    /**
     * 设置指定分部的营收分配
     */
    public void setYfAmountByDept(String deptCode, BigDecimal amount) {
        switch (deptCode.toUpperCase()) {
            case "YB": this.yfYbAmount = amount; break;
            case "EB": this.yfEbAmount = amount; break;
            case "JF": this.yfJfAmount = amount; break;
            case "OTHER": case "ZH": this.yfOtherAmount = amount; break;
        }
    }

    /**
     * 设置指定分部的外采分配
     */
    public void setOutAmountByDept(String deptCode, BigDecimal amount) {
        switch (deptCode.toUpperCase()) {
            case "YB": this.outYbAmount = amount; break;
            case "EB": this.outEbAmount = amount; break;
            case "JF": this.outJfAmount = amount; break;
            case "OTHER": case "ZH": this.outOtherAmount = amount; break;
        }
    }

    /**
     * 获取交付分部的完整信息
     */
    public DeptAllocationInfo getJfDeptInfo() {
        DeptAllocationInfo info = new DeptAllocationInfo();
        info.setDeptCode("JF");
        info.setDeptName("交付");
        info.setYfAmount(this.yfJfAmount);
        info.setOutAmount(this.outJfAmount);
        return info;
    }

    /**
     * 获取所有分部的分配信息
     */
    public java.util.List<DeptAllocationInfo> getAllDeptInfo() {
        java.util.List<DeptAllocationInfo> list = new java.util.ArrayList<>();
        
        list.add(createDeptInfo("YB", "一部", yfYbAmount, outYbAmount));
        list.add(createDeptInfo("EB", "二部", yfEbAmount, outEbAmount));
        list.add(createDeptInfo("JF", "交付", yfJfAmount, outJfAmount));
        list.add(createDeptInfo("OTHER", "综合", yfOtherAmount, outOtherAmount));
        
        return list;
    }

    private DeptAllocationInfo createDeptInfo(String code, String name, BigDecimal yf, BigDecimal out) {
        DeptAllocationInfo info = new DeptAllocationInfo();
        info.setDeptCode(code);
        info.setDeptName(name);
        info.setYfAmount(yf != null ? yf : BigDecimal.ZERO);
        info.setOutAmount(out != null ? out : BigDecimal.ZERO);
        return info;
    }

    /**
     * 分部分配信息DTO
     */
    @Data
    public static class DeptAllocationInfo {
        private String deptCode;      // 分部编码
        private String deptName;      // 分部名称
        private BigDecimal yfAmount;  // 营收分配
        private BigDecimal outAmount; // 外采分配
        
        public BigDecimal getTotalAmount() {
            return (yfAmount != null ? yfAmount : BigDecimal.ZERO)
                .add(outAmount != null ? outAmount : BigDecimal.ZERO);
        }
    }
}