package com.xinghuo.project.template.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.project.template.entity.WbsTemplateMasterEntity;
import com.xinghuo.project.template.entity.WbsTemplateDetailEntity;
import com.xinghuo.project.template.model.WbsTemplateMasterPagination;
import com.xinghuo.project.template.model.dto.WbsTemplateVO;
import com.xinghuo.project.template.service.WbsTemplateMasterService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * WBS计划模板管理控制器
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Slf4j
@Tag(name = "WBS计划模板管理", description = "WBS计划模板管理相关接口")
@RestController
@RequestMapping("/api/project/template/wbsTemplate")
public class WbsTemplateMasterController {

    @Resource
    private WbsTemplateMasterService wbsTemplateMasterService;

    /**
     * 获取WBS模板列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取WBS模板列表")
    public ActionResult<PageListVO<WbsTemplateVO>> list(@RequestBody WbsTemplateMasterPagination pagination) {
        try {
            List<WbsTemplateVO> list = wbsTemplateMasterService.getList(pagination);

            // 对结果进行数据转换和补充
            for (WbsTemplateVO vo : list) {
                // 状态名称转换
                if (vo.getStatus() != null) {
                    switch (vo.getStatus()) {
                        case "draft":
                            vo.setStatusName("草稿");
                            break;
                        case "published":
                            vo.setStatusName("已发布");
                            break;
                        case "archived":
                            vo.setStatusName("归档");
                            break;
                        default:
                            vo.setStatusName(vo.getStatus());
                    }
                }

                // TODO: 可以在这里添加其他关联数据的查询和设置
                // 例如：创建用户名称等
            }

            PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
            return ActionResult.page(list, page);
        } catch (Exception e) {
            log.error("获取WBS模板列表失败", e);
            return ActionResult.fail("获取WBS模板列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据状态获取模板列表
     */
    @GetMapping("/getListByStatus/{status}")
    @Operation(summary = "根据状态获取模板列表")
    public ActionResult<List<WbsTemplateMasterEntity>> getListByStatus(
            @Parameter(description = "状态") @PathVariable String status) {
        try {
            List<WbsTemplateMasterEntity> list = wbsTemplateMasterService.getListByStatus(status);
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("根据状态获取模板列表失败", e);
            return ActionResult.fail("获取模板列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取模板详情（包含WBS明细）
     */
    @GetMapping("/getDetailInfo/{id}")
    @Operation(summary = "获取模板详情")
    public ActionResult<WbsTemplateVO> getDetailInfo(
            @Parameter(description = "模板ID") @PathVariable String id) {
        try {
            WbsTemplateVO templateVO = wbsTemplateMasterService.getDetailInfo(id);
            if (templateVO == null) {
                return ActionResult.fail("WBS模板不存在");
            }
            return ActionResult.success(templateVO);
        } catch (Exception e) {
            log.error("获取模板详情失败", e);
            return ActionResult.fail("获取模板详情失败：" + e.getMessage());
        }
    }

    /**
     * 获取模板基本信息
     */
    @GetMapping("/getInfo/{id}")
    @Operation(summary = "获取模板基本信息")
    public ActionResult<WbsTemplateMasterEntity> getInfo(
            @Parameter(description = "模板ID") @PathVariable String id) {
        try {
            WbsTemplateMasterEntity entity = wbsTemplateMasterService.getInfo(id);
            if (entity == null) {
                return ActionResult.fail("WBS模板不存在");
            }
            return ActionResult.success(entity);
        } catch (Exception e) {
            log.error("获取模板信息失败", e);
            return ActionResult.fail("获取模板信息失败：" + e.getMessage());
        }
    }

    /**
     * 创建WBS模板
     */
    @PostMapping("/create")
    @Operation(summary = "创建WBS模板")
    public ActionResult<String> create(@RequestBody @Valid WbsTemplateVO templateVO) {
        try {
            String id = wbsTemplateMasterService.create(templateVO);
            return ActionResult.success("创建成功", id);
        } catch (Exception e) {
            log.error("创建WBS模板失败", e);
            return ActionResult.fail("创建WBS模板失败：" + e.getMessage());
        }
    }

    /**
     * 更新WBS模板
     */
    @PutMapping("/update/{id}")
    @Operation(summary = "更新WBS模板")
    public ActionResult<String> update(
            @Parameter(description = "模板ID") @PathVariable String id,
            @RequestBody @Valid WbsTemplateVO templateVO) {
        try {
            wbsTemplateMasterService.update(id, templateVO);
            return ActionResult.success("更新成功");
        } catch (Exception e) {
            log.error("更新WBS模板失败", e);
            return ActionResult.fail("更新WBS模板失败：" + e.getMessage());
        }
    }

    /**
     * 删除WBS模板
     */
    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除WBS模板")
    public ActionResult<String> delete(
            @Parameter(description = "模板ID") @PathVariable String id) {
        try {
            wbsTemplateMasterService.delete(id);
            return ActionResult.success("删除成功");
        } catch (Exception e) {
            log.error("删除WBS模板失败", e);
            return ActionResult.fail("删除WBS模板失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除WBS模板
     */
    @DeleteMapping("/batchDelete")
    @Operation(summary = "批量删除WBS模板")
    public ActionResult<String> batchDelete(@RequestBody List<String> ids) {
        try {
            wbsTemplateMasterService.batchDelete(ids);
            return ActionResult.success("批量删除成功");
        } catch (Exception e) {
            log.error("批量删除WBS模板失败", e);
            return ActionResult.fail("批量删除WBS模板失败：" + e.getMessage());
        }
    }

    /**
     * 更新模板状态
     */
    @PutMapping("/updateStatus/{id}")
    @Operation(summary = "更新模板状态")
    public ActionResult<String> updateStatus(
            @Parameter(description = "模板ID") @PathVariable String id,
            @RequestParam String status) {
        try {
            wbsTemplateMasterService.updateStatus(id, status);
            return ActionResult.success("状态更新成功");
        } catch (Exception e) {
            log.error("更新模板状态失败", e);
            return ActionResult.fail("更新模板状态失败：" + e.getMessage());
        }
    }

    /**
     * 批量更新状态
     */
    @PutMapping("/batchUpdateStatus")
    @Operation(summary = "批量更新状态")
    public ActionResult<String> batchUpdateStatus(
            @RequestBody List<String> ids,
            @RequestParam String status) {
        try {
            wbsTemplateMasterService.batchUpdateStatus(ids, status);
            return ActionResult.success("批量更新状态成功");
        } catch (Exception e) {
            log.error("批量更新模板状态失败", e);
            return ActionResult.fail("批量更新状态失败：" + e.getMessage());
        }
    }

    /**
     * 发布模板
     */
    @PutMapping("/publish/{id}")
    @Operation(summary = "发布模板")
    public ActionResult<String> publish(
            @Parameter(description = "模板ID") @PathVariable String id) {
        try {
            wbsTemplateMasterService.publish(id);
            return ActionResult.success("发布成功");
        } catch (Exception e) {
            log.error("发布模板失败", e);
            return ActionResult.fail("发布模板失败：" + e.getMessage());
        }
    }

    /**
     * 归档模板
     */
    @PutMapping("/archive/{id}")
    @Operation(summary = "归档模板")
    public ActionResult<String> archive(
            @Parameter(description = "模板ID") @PathVariable String id) {
        try {
            wbsTemplateMasterService.archive(id);
            return ActionResult.success("归档成功");
        } catch (Exception e) {
            log.error("归档模板失败", e);
            return ActionResult.fail("归档模板失败：" + e.getMessage());
        }
    }

    /**
     * 批量发布模板
     */
    @PutMapping("/batchPublish")
    @Operation(summary = "批量发布模板")
    public ActionResult<String> batchPublish(@RequestBody List<String> ids) {
        try {
            wbsTemplateMasterService.batchPublish(ids);
            return ActionResult.success("批量发布成功");
        } catch (Exception e) {
            log.error("批量发布模板失败", e);
            return ActionResult.fail("批量发布模板失败：" + e.getMessage());
        }
    }

    /**
     * 批量归档模板
     */
    @PutMapping("/batchArchive")
    @Operation(summary = "批量归档模板")
    public ActionResult<String> batchArchive(@RequestBody List<String> ids) {
        try {
            wbsTemplateMasterService.batchArchive(ids);
            return ActionResult.success("批量归档成功");
        } catch (Exception e) {
            log.error("批量归档模板失败", e);
            return ActionResult.fail("批量归档模板失败：" + e.getMessage());
        }
    }

    /**
     * 复制WBS模板
     */
    @PostMapping("/copy/{id}")
    @Operation(summary = "复制WBS模板")
    public ActionResult<String> copy(
            @Parameter(description = "模板ID") @PathVariable String id,
            @RequestParam String newName) {
        try {
            String newId = wbsTemplateMasterService.copy(id, newName);
            return ActionResult.success("复制成功", newId);
        } catch (Exception e) {
            log.error("复制WBS模板失败", e);
            return ActionResult.fail("复制WBS模板失败：" + e.getMessage());
        }
    }

    /**
     * 检查模板名称是否存在
     */
    @GetMapping("/checkNameExists")
    @Operation(summary = "检查模板名称是否存在")
    public ActionResult<Boolean> checkNameExists(
            @RequestParam String name,
            @RequestParam(required = false) String excludeId) {
        try {
            boolean exists = wbsTemplateMasterService.isExistByName(name, excludeId);
            return ActionResult.success(exists);
        } catch (Exception e) {
            log.error("检查模板名称失败", e);
            return ActionResult.fail("检查模板名称失败：" + e.getMessage());
        }
    }

    /**
     * 检查模板编码是否存在
     */
    @GetMapping("/checkCodeExists")
    @Operation(summary = "检查模板编码是否存在")
    public ActionResult<Boolean> checkCodeExists(
            @RequestParam String code,
            @RequestParam(required = false) String excludeId) {
        try {
            boolean exists = wbsTemplateMasterService.isExistByCode(code, excludeId);
            return ActionResult.success(exists);
        } catch (Exception e) {
            log.error("检查模板编码失败", e);
            return ActionResult.fail("检查模板编码失败：" + e.getMessage());
        }
    }

    /**
     * 获取模板选择列表
     */
    @GetMapping("/getSelectList")
    @Operation(summary = "获取模板选择列表")
    public ActionResult<List<WbsTemplateMasterEntity>> getSelectList(
            @RequestParam(required = false) String keyword) {
        try {
            List<WbsTemplateMasterEntity> list = wbsTemplateMasterService.getSelectList(keyword);
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("获取模板选择列表失败", e);
            return ActionResult.fail("获取模板选择列表失败：" + e.getMessage());
        }
    }

    /**
     * 生成模板编码
     */
    @GetMapping("/generateCode")
    @Operation(summary = "生成模板编码")
    public ActionResult<String> generateCode() {
        try {
            String code = wbsTemplateMasterService.generateCode();
            return ActionResult.success(code);
        } catch (Exception e) {
            log.error("生成模板编码失败", e);
            return ActionResult.fail("生成模板编码失败：" + e.getMessage());
        }
    }

    /**
     * 获取模板使用情况
     */
    @GetMapping("/getTemplateUsageInfo/{id}")
    @Operation(summary = "获取模板使用情况")
    public ActionResult<Map<String, Object>> getTemplateUsageInfo(
            @Parameter(description = "模板ID") @PathVariable String id) {
        try {
            Map<String, Object> usageInfo = wbsTemplateMasterService.getTemplateUsageInfo(id);
            return ActionResult.success(usageInfo);
        } catch (Exception e) {
            log.error("获取模板使用情况失败", e);
            return ActionResult.fail("获取模板使用情况失败：" + e.getMessage());
        }
    }

    /**
     * 从活动库添加活动到WBS模板
     */
    @PostMapping("/addActivitiesFromLibrary/{templateId}")
    @Operation(summary = "从活动库添加活动")
    public ActionResult<String> addActivitiesFromLibrary(
            @Parameter(description = "模板ID") @PathVariable String templateId,
            @RequestBody List<String> activityIds,
            @RequestParam(required = false) String parentId) {
        try {
            wbsTemplateMasterService.addActivitiesFromLibrary(templateId, activityIds, parentId);
            return ActionResult.success("添加活动成功");
        } catch (Exception e) {
            log.error("从活动库添加活动失败", e);
            return ActionResult.fail("添加活动失败：" + e.getMessage());
        }
    }

    /**
     * 获取WBS模板明细列表
     */
    @GetMapping("/getWbsDetails/{templateId}")
    @Operation(summary = "获取WBS模板明细列表")
    public ActionResult<List<WbsTemplateDetailEntity>> getWbsDetails(
            @Parameter(description = "模板ID") @PathVariable String templateId) {
        try {
            List<WbsTemplateDetailEntity> details = wbsTemplateMasterService.getWbsDetails(templateId);
            return ActionResult.success(details);
        } catch (Exception e) {
            log.error("获取WBS明细失败", e);
            return ActionResult.fail("获取WBS明细失败：" + e.getMessage());
        }
    }

    /**
     * 保存WBS明细
     */
    @PostMapping("/saveWbsDetails/{templateId}")
    @Operation(summary = "保存WBS明细")
    public ActionResult<String> saveWbsDetails(
            @Parameter(description = "模板ID") @PathVariable String templateId,
            @RequestBody List<WbsTemplateDetailEntity> details) {
        try {
            wbsTemplateMasterService.saveWbsDetails(templateId, details);
            return ActionResult.success("保存成功");
        } catch (Exception e) {
            log.error("保存WBS明细失败", e);
            return ActionResult.fail("保存WBS明细失败：" + e.getMessage());
        }
    }

    /**
     * 删除WBS明细
     */
    @DeleteMapping("/deleteWbsDetails")
    @Operation(summary = "删除WBS明细")
    public ActionResult<String> deleteWbsDetails(@RequestBody List<String> detailIds) {
        try {
            wbsTemplateMasterService.deleteWbsDetails(detailIds);
            return ActionResult.success("删除成功");
        } catch (Exception e) {
            log.error("删除WBS明细失败", e);
            return ActionResult.fail("删除WBS明细失败：" + e.getMessage());
        }
    }

    /**
     * 应用到项目
     */
    @PostMapping("/applyToProjects/{templateId}")
    @Operation(summary = "应用到项目")
    public ActionResult<String> applyToProjects(
            @Parameter(description = "模板ID") @PathVariable String templateId,
            @RequestBody List<String> projectIds) {
        try {
            wbsTemplateMasterService.applyToProjects(templateId, projectIds);
            return ActionResult.success("应用成功");
        } catch (Exception e) {
            log.error("应用到项目失败", e);
            return ActionResult.fail("应用到项目失败：" + e.getMessage());
        }
    }
}
