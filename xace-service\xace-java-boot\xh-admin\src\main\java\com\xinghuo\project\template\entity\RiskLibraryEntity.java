package com.xinghuo.project.template.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 标准项目风险库实体类
 * 对应数据库表：zz_proj_risk_library
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("zz_proj_risk_library")
public class RiskLibraryEntity extends BaseEntityV2.CUBaseEntityV2<String> {

    /**
     * 标准风险编码
     */
    @TableField("code")
    private String code;

    /**
     * 标准风险标题/名称
     */
    @TableField("title")
    private String title;

    /**
     * 风险的详细描述 (可能的原因、后果等)
     */
    @TableField("description")
    private String description;

    /**
     * 发布状态 (如: draft 草稿, published 已发布, archived 归档)
     */
    @TableField("status")
    private String status;

    /**
     * 风险类别ID (关联字典表, 如: 技术, 成本, 供应链, 法规)
     */
    @TableField("risk_category_id")
    private String riskCategoryId;

    /**
     * 默认概率等级ID (关联字典表, 如: 1-5级)
     */
    @TableField("default_probability_level_id")
    private String defaultProbabilityLevelId;

    /**
     * 默认影响等级ID (关联字典表, 如: 1-5级)
     */
    @TableField("default_impact_level_id")
    private String defaultImpactLevelId;

    /**
     * 建议的应对策略 (如: 规避, 转移, 减轻, 接受)
     */
    @TableField("suggested_strategy")
    private String suggestedStrategy;

    /**
     * 建议的具体应对措施
     */
    @TableField("suggested_actions")
    private String suggestedActions;
}
