import { defHttp } from '/@/utils/http/axios';
import { ListResult, ActionResult } from '/@/api/model/baseModel';

/**
 * 商机管理API
 */

// API URL前缀 - 更新为新的架构路径
const API_PREFIX = '/api/project/biz/opportunity';

/**
 * 商机对象接口
 */
export interface BusinessModel {
  id: string;
  businessNo?: string;
  projectName: string;
  custId?: string;
  custName?: string;
  projectLevel: string;
  projectLeader: string;
  projectLeaderName?: string;
  status?: string;
  projType: string;
  projectType?: string;
  deptId?: string;
  deptName?: string;
  yfDeptId?: string;
  yfDeptName?: string;
  marketLinkman?: string;
  presaleLinkman?: string;
  projectContent?: string;
  startDate?: string;
  evaSignMonth?: string;
  businessTag?: string;
  workStatus?: string;
  projectNo?: string;
  lastNote?: string;
  evaFirstMonth?: string;
  evaFirstAmount?: number;
  evaSecondMonth?: string;
  evaSecondAmount?: number;
  evaFirstexternalMonth?: string;
  evaFirstexternalAmount?: number;
  evaSecondexternalMonth?: string;
  evaSecondexternalAmount?: number;
  yearMoneyRatio?: number;
  yearMoney?: number;
  deptMoney?: number;
  yfYbAmount?: number;
  yfEbAmount?: number;
  yfJfAmount?: number;
  yfOtherAmount?: number;
  outYbAmount?: number;
  outEbAmount?: number;
  outJfAmount?: number;
  outOtherAmount?: number;
  purchaseMoney?: number;
  profitMoney?: number;
  createUserId?: string;
  createUserName?: string;
  createTime?: string;
  lastModifyUserId?: string;
  lastModifyUserName?: string;
  lastModifyTime?: string;
  deleteMark?: number;
}

/**
 * 商机表单接口
 */
export interface BusinessFormModel {
  businessNo?: string;
  projectName: string;
  custId?: string;
  projectLevel: string;
  projectLeader: string;
  status?: string;
  projType: string;
  projectType?: string;
  deptId?: string;
  yfDeptId?: string;
  marketLinkman?: string;
  presaleLinkman?: string;
  projectContent?: string;
  startDate?: string;
  evaSignMonth?: string;
  businessTag?: string;
  workStatus?: string;
  projectNo?: string;
  lastNote?: string;
  evaFirstMonth?: string;
  evaFirstAmount?: number;
  evaSecondMonth?: string;
  evaSecondAmount?: number;
  evaFirstexternalMonth?: string;
  evaFirstexternalAmount?: number;
  evaSecondexternalMonth?: string;
  evaSecondexternalAmount?: number;
  yearMoneyRatio?: number;
  yearMoney?: number;
  deptMoney?: number;
  yfYbAmount?: number;
  yfEbAmount?: number;
  yfJfAmount?: number;
  yfOtherAmount?: number;
  outYbAmount?: number;
  outEbAmount?: number;
  outJfAmount?: number;
  outOtherAmount?: number;
  purchaseMoney?: number;
  profitMoney?: number;
}

/**
 * 商机查询参数接口
 */
export interface BusinessQueryParams {
  businessNo?: string;
  projectName?: string;
  custId?: string;
  projectLevel?: string;
  projectLeader?: string;
  status?: string;
  projType?: string;
  deptId?: string;
  yfDeptId?: string;
  evaSignMonthStart?: string;
  evaSignMonthEnd?: string;
  startDateStart?: string;
  startDateEnd?: string;
  createTimeStart?: string;
  createTimeEnd?: string;
  businessTag?: string;
  workStatus?: string;
  keyword?: string;
  pageSize?: number;
  currentPage?: number;
}

/**
 * 商机状态更新接口
 */
export interface BusinessStatusModel {
  status: string;
  lastNote?: string;
  projectNo?: string;
}

/**
 * 获取商机列表
 * @param params 查询参数
 * @returns 商机列表
 */
export const getBusinessList = (params?: BusinessQueryParams) => {
  return defHttp.post<ListResult<BusinessModel>>({
    url: `${API_PREFIX}/getList`,
    data: params,
  });
};

/**
 * 根据客户ID获取商机列表
 * @param custId 客户ID
 * @returns 商机列表
 */
export const getBusinessListByCustId = (custId: string) => {
  return defHttp.get<BusinessModel[]>({
    url: `${API_PREFIX}/getListByCustId/${custId}`,
  });
};

/**
 * 获取商机详情
 * @param id 商机ID
 * @returns 商机详情
 */
export const getBusinessInfo = (id: string) => {
  return defHttp.get<BusinessModel>({
    url: `${API_PREFIX}/${id}`,
  });
};

/**
 * 根据项目ID获取商机信息 (通过列表查询实现)
 * @param projectId 项目ID
 * @returns 商机信息
 */
export const getBusinessInfoByProjectId = (projectId: string) => {
  return defHttp.post<ListResult<BusinessModel>>({
    url: `${API_PREFIX}/getList`,
    data: {
      projBaseId: projectId,
      currentPage: 1,
      pageSize: 1,
    },
  });
};

// 商机阶段功能已移除

/**
 * 创建商机
 * @param params 商机创建参数
 * @returns 操作结果
 */
export const createBusiness = (params: BusinessFormModel) => {
  return defHttp.post<string>({
    url: `${API_PREFIX}/create`,
    data: params,
  });
};

/**
 * 更新商机
 * @param id 商机ID
 * @param params 商机更新参数
 * @returns 操作结果
 */
export const updateBusiness = (id: string, params: BusinessFormModel) => {
  return defHttp.put<void>({
    url: `${API_PREFIX}/update/${id}`,
    data: params,
  });
};

/**
 * 删除商机
 * @param id 商机ID
 * @returns 操作结果
 */
export const deleteBusiness = (id: string) => {
  return defHttp.delete<void>({
    url: `${API_PREFIX}/delete/${id}`,
  });
};

/**
 * 更新商机状态
 * @param id 商机ID
 * @param params 状态更新参数
 * @returns 操作结果
 */
export const updateBusinessStatus = (id: string, params: BusinessStatusModel) => {
  return defHttp.put<void>({
    url: `${API_PREFIX}/updateStatus/${id}`,
    data: params,
  });
};

/**
 * 更新最后跟踪记录
 * @param id 商机ID
 * @param lastNote 最后跟踪记录
 * @returns 操作结果
 */
export const updateBusinessLastNote = (id: string, lastNote: string) => {
  return defHttp.put<void>({
    url: `${API_PREFIX}/updateLastNote/${id}`,
    params: {
      lastNote,
    },
  });
};

/**
 * 更新工时填写状态
 * @param id 商机ID
 * @param workStatus 工时填写状态
 * @returns 操作结果
 */
export const updateBusinessWorkStatus = (id: string, workStatus: string) => {
  return defHttp.put<void>({
    url: `${API_PREFIX}/updateWorkStatus/${id}`,
    params: {
      workStatus,
    },
  });
};

// 商机转合同功能已移除

/**
 * 获取销售漏斗数据
 * @param params 查询参数
 * @returns 销售漏斗数据
 */
export const getSalesFunnelData = (params?: { year?: number; month?: number; deptId?: string; leaderId?: string }) => {
  return defHttp.post<any[]>({
    url: `${API_PREFIX}/getSalesFunnelData`,
    data: params,
  });
};

/**
 * 获取商机预测数据
 * @param params 查询参数
 * @returns 商机预测数据
 */
export const getBusinessForecastData = (params?: { year?: number; deptId?: string; leaderId?: string }) => {
  return defHttp.post<any[]>({
    url: `${API_PREFIX}/getBusinessForecastData`,
    data: params,
  });
};

/**
 * 获取赢单/输单分析数据
 * @param params 查询参数
 * @returns 赢单/输单分析数据
 */
export const getWinLoseAnalysisData = (params?: { year?: number; deptId?: string; leaderId?: string }) => {
  return defHttp.post<any[]>({
    url: `${API_PREFIX}/getWinLoseAnalysisData`,
    data: params,
  });
};
