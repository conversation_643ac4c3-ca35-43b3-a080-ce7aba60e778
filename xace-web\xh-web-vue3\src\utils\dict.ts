import { useBaseStore } from '/@/store/modules/base';

/**
 * 数据字典翻译工具
 */
export class DictTranslator {
  private static instance: DictTranslator;
  private baseStore = useBaseStore();
  private dictCache = new Map<string, Map<string, string>>();

  private constructor() {}

  public static getInstance(): DictTranslator {
    if (!DictTranslator.instance) {
      DictTranslator.instance = new DictTranslator();
    }
    return DictTranslator.instance;
  }

  /**
   * 获取字典翻译映射
   * @param dictType 字典类型编码
   * @returns Promise<Map<string, string>> key为字典值，value为显示名称
   */
  public async getDictMap(dictType: string): Promise<Map<string, string>> {
    // 检查缓存
    if (this.dictCache.has(dictType)) {
      return this.dictCache.get(dictType)!;
    }

    try {
      // 从store获取字典数据
      const dictData = await this.baseStore.getDictionaryData(dictType);
      const dictMap = new Map<string, string>();

      if (Array.isArray(dictData)) {
        dictData.forEach((item: any) => {
          // 支持通过id或enCode进行翻译
          if (item.id) {
            dictMap.set(item.id, item.fullName);
          }
          if (item.enCode && item.enCode !== item.id) {
            dictMap.set(item.enCode, item.fullName);
          }
        });
      }

      // 缓存结果
      this.dictCache.set(dictType, dictMap);
      return dictMap;
    } catch (error) {
      console.warn(`获取字典数据失败: ${dictType}`, error);
      return new Map();
    }
  }

  /**
   * 翻译单个字典值
   * @param value 字典值
   * @param dictType 字典类型编码
   * @returns Promise<string> 翻译后的显示名称
   */
  public async translate(value: string | null | undefined, dictType: string): Promise<string> {
    if (!value) return '';

    const dictMap = await this.getDictMap(dictType);
    return dictMap.get(value) || value;
  }

  /**
   * 批量翻译字典值
   * @param values 字典值数组
   * @param dictType 字典类型编码
   * @returns Promise<string[]> 翻译后的显示名称数组
   */
  public async translateBatch(values: (string | null | undefined)[], dictType: string): Promise<string[]> {
    const dictMap = await this.getDictMap(dictType);
    return values.map(value => {
      if (!value) return '';
      return dictMap.get(value) || value;
    });
  }

  /**
   * 翻译对象数组中的字典字段
   * @param data 数据数组
   * @param fieldMappings 字段映射配置 {字段名: {dictType: 字典类型, targetField?: 目标字段名}}
   * @returns Promise<any[]> 翻译后的数据数组
   */
  public async translateFields(data: any[], fieldMappings: Record<string, { dictType: string; targetField?: string }>): Promise<any[]> {
    if (!data || !Array.isArray(data) || data.length === 0) {
      return data;
    }

    // 预加载所有需要的字典数据
    const dictTypes = Object.values(fieldMappings).map(mapping => mapping.dictType);
    const dictMaps = new Map<string, Map<string, string>>();

    await Promise.all(
      dictTypes.map(async dictType => {
        const dictMap = await this.getDictMap(dictType);
        dictMaps.set(dictType, dictMap);
      }),
    );

    // 翻译数据
    return data.map(item => {
      const translatedItem = { ...item };

      Object.entries(fieldMappings).forEach(([field, config]) => {
        const value = item[field];
        if (value !== null && value !== undefined) {
          const dictMap = dictMaps.get(config.dictType);
          const translatedValue = dictMap?.get(value) || value;
          const targetField = config.targetField || `${field}Text`;
          translatedItem[targetField] = translatedValue;
        }
      });

      return translatedItem;
    });
  }

  /**
   * 清除字典缓存
   * @param dictType 可选，指定清除某个字典类型的缓存，不传则清除所有
   */
  public clearCache(dictType?: string): void {
    if (dictType) {
      this.dictCache.delete(dictType);
    } else {
      this.dictCache.clear();
    }
  }
}

// 导出单例实例
export const dictTranslator = DictTranslator.getInstance();

// 导出便捷函数
export const translateDict = (value: string | null | undefined, dictType: string) => dictTranslator.translate(value, dictType);

export const translateDictBatch = (values: (string | null | undefined)[], dictType: string) => dictTranslator.translateBatch(values, dictType);

export const translateDictFields = (data: any[], fieldMappings: Record<string, { dictType: string; targetField?: string }>) =>
  dictTranslator.translateFields(data, fieldMappings);
