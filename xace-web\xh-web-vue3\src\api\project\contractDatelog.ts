import { defHttp } from '/@/utils/http/axios';

/**
 * 合同日期日志管理API
 */

// API URL前缀
const API_PREFIX = '/api/project/contract/datelog';

/**
 * 合同日期日志对象接口
 */
export interface ContractDatelogModel {
  id: string;
  relateId: string;
  type: string;
  oldDate: string;
  newDate: string;
  note: string;
  createUserId: string;
  createUserName?: string;
  createTime: string;
}

/**
 * 根据合同ID获取日期变更日志列表
 * @param contractId 合同ID
 * @returns 日期变更日志列表
 */
export const getContractDatelogListByContractId = (contractId: string) => {
  return defHttp.get<ContractDatelogModel[]>({
    url: `${API_PREFIX}/contract/${contractId}`,
  });
};
