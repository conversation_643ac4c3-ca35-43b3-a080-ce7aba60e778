package com.xinghuo.project.template.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 标准项目问题库视图对象
 * 用于返回问题库列表和详情信息
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
@Schema(description = "标准项目问题库视图对象")
public class IssueLibraryVO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private String id;

    /**
     * 标准问题编码
     */
    @Schema(description = "标准问题编码")
    private String code;

    /**
     * 标准问题标题/名称
     */
    @Schema(description = "标准问题标题/名称")
    private String title;

    /**
     * 问题的详细描述模板 (现象, 可能原因, 潜在影响)
     */
    @Schema(description = "问题的详细描述模板")
    private String description;

    /**
     * 发布状态 (如: draft 草稿, published 已发布, archived 归档)
     */
    @Schema(description = "发布状态")
    private String status;

    /**
     * 发布状态名称 (草稿/已发布/归档)
     */
    @Schema(description = "发布状态名称")
    private String statusName;

    /**
     * 问题类别ID (关联字典表, 如: 过程不符合, 客户反馈, 事件)
     */
    @Schema(description = "问题类别ID")
    private String issueCategoryId;

    /**
     * 问题类别名称 (冗余字段，便于显示)
     */
    @Schema(description = "问题类别名称")
    private String issueCategoryName;

    /**
     * 默认优先级/严重性ID (关联字典表, 如: 严重, 一般, 较低)
     */
    @Schema(description = "默认优先级/严重性ID")
    private String defaultPriorityId;

    /**
     * 默认优先级/严重性名称 (冗余字段，便于显示)
     */
    @Schema(description = "默认优先级/严重性名称")
    private String defaultPriorityName;

    /**
     * 建议的通用解决方案或处理思路
     */
    @Schema(description = "建议的通用解决方案或处理思路")
    private String suggestedSolution;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private String tenantId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    /**
     * 创建用户ID
     */
    @Schema(description = "创建用户ID")
    private String createdBy;

    /**
     * 创建用户名称 (冗余字段，便于显示)
     */
    @Schema(description = "创建用户名称")
    private String createdByName;

    /**
     * 最后修改时间
     */
    @Schema(description = "最后修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdatedAt;

    /**
     * 最后修改用户ID
     */
    @Schema(description = "最后修改用户ID")
    private String lastUpdatedBy;

    /**
     * 最后修改用户名称 (冗余字段，便于显示)
     */
    @Schema(description = "最后修改用户名称")
    private String lastUpdatedByName;
}
