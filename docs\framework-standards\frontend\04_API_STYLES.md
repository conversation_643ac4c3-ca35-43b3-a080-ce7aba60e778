# API 响应处理与样式规范

## API 响应处理规范

### ActionResult 统一响应格式

XACE 项目后端统一使用 `ActionResult<T>` 格式返回数据：

```typescript
interface ActionResult<T> {
  code: number;     // 状态码（200=成功）
  msg: string;      // 返回信息
  data: T;          // 返回数据
}
```

### 标准响应示例

```typescript
// 成功响应
{
  code: 200,
  msg: "操作成功",
  data: {
    // 具体业务数据
  }
}

// 分页数据响应
{
  code: 200,
  msg: "查询成功",
  data: {
    list: [...],           // 数据列表
    pagination: {          // 分页信息
      current: 1,
      pageSize: 10,
      total: 100
    }
  }
}

// 错误响应
{
  code: 400,
  msg: "参数错误",
  data: null
}
```

### API 调用最佳实践

#### 正确的 API 定义

```typescript
// ✅ 正确：返回完整 ActionResult
export function getBusinessInfo(id: string) {
  return defHttp.get<ActionResult<BusinessModel>>({
    url: `/api/business/${id}`,
  });
}

// ❌ 错误：直接返回 data
export function getBusinessInfo(id: string) {
  return defHttp.get<BusinessModel>({
    url: `/api/business/${id}`,
  });
}
```

#### 组件中的正确处理

```typescript
// ✅ 正确：检查状态码并处理响应
async function loadBusinessData() {
  try {
    const response = await getBusinessInfo(businessId.value);
    businessInfo.value = response.data;
  } catch (error) {
    console.error('网络请求失败:', error);
    createMessage.error('网络请求失败');
  }
}

// ❌ 错误：直接使用 response 作为数据
async function loadBusinessData() {
  try {
    const businessDetail = await getBusinessInfo(businessId.value);
    businessInfo.value = businessDetail; // 错误！
  } catch (error) {
    console.error('错误:', error);
  }
}
```

### 分页数据处理

```typescript
// ✅ 正确的分页数据处理
async function loadPageData() {
  try {
    const response = await getBusinessList(params);
    const { list, pagination } = response.data;
    dataSource.value = list;
    setPagination({
      current: pagination.current,
      pageSize: pagination.pageSize,
      total: pagination.total,
    });
  } catch (error) {
    console.error('加载数据失败:', error);
  }
}
```

### 类型定义规范

```typescript
// types/api.ts
export interface ActionResult<T = any> {
  code: number;
  msg: string;
  data: T;
}

export interface PageListVO<T> {
  list: T[];
  pagination: PaginationVO;
}

export interface PaginationVO {
  current: number;
  pageSize: number;
  total: number;
}

// 常用类型别名
export type ApiResponse<T> = ActionResult<T>;
export type PageResponse<T> = ActionResult<PageListVO<T>>;
```

### 错误处理最佳实践

```typescript

// 在组件中使用
async function handleSubmit() {
  try {
    await updateBusiness(businessId.value, formData);
    createMessage.success('更新成功');
    closeModal();
    emit('success');
  } catch (error) {
    console.error('更新失败:', error);
  }
}
```

## CSS/LESS 样式规范

### 基本编码原则

1. **一致性**：遵循项目已有的样式命名和组织方式
2. **可维护性**：编写清晰易读的样式代码
3. **可重用性**：抽象通用样式，避免重复
4. **语义化命名**：类名应反映内容而非外观
5. **模块化组织**：样式按组件或功能划分
6. **性能优化**：避免过度复杂的选择器

### 样式命名规范

#### 基础命名规则

```css
/* ✅ 使用 kebab-case */
.user-profile {}
.nav-item {}
.btn-primary {}

/* ✅ 功能性命名 */
.nav-header {}
.product-list {}

/* ❌ 避免外观命名 */
.red-text {}     /* 错误 */
.big-margin {}   /* 错误 */
```

#### BEM 命名法则

```css
/* Block（块） */
.card {}

/* Element（元素） - 双下划线 */
.card__header {}
.card__body {}
.card__footer {}

/* Modifier（修饰符） - 双连字符 */
.card--featured {}
.card__header--large {}
```

#### 组件前缀约定

```css
.c-card {}       /* 组件 (Component) */
.l-header {}     /* 布局 (Layout) */
.u-clearfix {}   /* 工具 (Utility) */
.t-dark {}       /* 主题 (Theme) */

/* 状态类名 */
.is-active {}
.is-disabled {}
.has-error {}
.is-loading {}
```

### 文件组织结构

```
src/assets/styles/
├── variables.less          # 变量定义
├── mixins.less             # 混入函数
├── global.less             # 全局样式
├── components/             # 组件样式
│   ├── button.less
│   └── form.less
├── layout/                 # 布局样式
│   ├── header.less
│   └── sidebar.less
└── utilities/              # 工具类
    ├── spacing.less
    └── typography.less
```

### 变量和主题管理

#### LESS 变量定义

```less
// 颜色变量
@primary-color: #1890ff;
@text-color-primary: rgba(0, 0, 0, 0.85);
@bg-color-base: #f5f5f5;
@border-color-base: #d9d9d9;

// 尺寸变量
@font-size-base: 14px;
@spacing-unit: 8px;
@border-radius-base: 4px;

// Z-index 管理
@z-index-dropdown: 1000;
@z-index-modal: 1050;
@z-index-tooltip: 1070;
```

#### CSS 变量（主题切换）

```css
:root {
  --primary-color: #1890ff;
  --text-color: rgba(0, 0, 0, 0.85);
  --bg-color: #ffffff;
  --border-color: #d9d9d9;
}

/* 暗黑模式 */
html[data-theme='dark'] {
  --primary-color: #177ddc;
  --text-color: rgba(255, 255, 255, 0.85);
  --bg-color: #141414;
  --border-color: #434343;
}

/* 使用变量 */
.component {
  color: var(--text-color);
  background-color: var(--bg-color);
  border: 1px solid var(--border-color);
}
```

### 响应式设计

#### 断点定义

```less
@screen-xs: 480px;
@screen-sm: 576px;
@screen-md: 768px;
@screen-lg: 992px;
@screen-xl: 1200px;
@screen-xxl: 1600px;
```

#### 响应式布局

```vue
<template>
  <!-- 使用 Ant Design Vue 网格系统 -->
  <a-row :gutter="16">
    <a-col :xs="24" :sm="12" :md="8" :lg="6">
      <!-- 内容 -->
    </a-col>
  </a-row>
</template>

<style lang="less" scoped>
.responsive-container {
  // 基础样式（移动端优先）
  padding: 12px;
  
  // 平板端
  @media (min-width: @screen-md) {
    padding: 16px;
  }
  
  // 桌面端
  @media (min-width: @screen-lg) {
    padding: 24px;
  }
}
</style>
```

### 组件样式最佳实践

#### 作用域样式

```vue
<template>
  <div class="user-card">
    <div class="user-card__header">
      <h3 class="user-card__title">{{ user.name }}</h3>
    </div>
    <div class="user-card__content">
      <!-- 内容 -->
    </div>
  </div>
</template>

<style lang="less" scoped>
.user-card {
  border: 1px solid var(--border-color);
  border-radius: @border-radius-base;
  
  &__header {
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
  }
  
  &__title {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
  }
  
  &__content {
    padding: 16px;
  }
}
</style>
```

#### 深度选择器

```vue
<style lang="less" scoped>
.custom-form {
  // 修改子组件样式
  :deep(.ant-form-item-label) {
    font-weight: 500;
  }
  
  // 或使用 /deep/ (Vue 2 语法)
  /deep/ .ant-input {
    border-radius: 6px;
  }
}
</style>
```

### 实用工具类

#### 通用混入

```less
// 文本省略
.text-ellipsis() {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

// 多行文本省略
.multi-line-ellipsis(@lines: 2) {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: @lines;
  -webkit-box-orient: vertical;
}

// Flexbox 布局
.flex-center() {
  display: flex;
  align-items: center;
  justify-content: center;
}

// 清除浮动
.clearfix() {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}
```

#### 工具类样式

```css
/* 文本对齐 */
.u-text-left { text-align: left; }
.u-text-center { text-align: center; }
.u-text-right { text-align: right; }

/* 间距工具类 */
.u-margin-top-sm { margin-top: 8px; }
.u-margin-top-md { margin-top: 16px; }
.u-margin-top-lg { margin-top: 24px; }

/* 显示隐藏 */
.u-hidden { display: none; }
.u-visible { display: block; }

/* Flexbox 工具类 */
.u-flex { display: flex; }
.u-flex-center { 
  display: flex; 
  align-items: center; 
  justify-content: center; 
}
```

### 性能优化

#### 选择器优化

```css
/* ✅ 推荐：简洁选择器 */
.nav-link-active {}

/* ❌ 避免：过度嵌套 */
.header .navigation ul li a.active span {}

/* ✅ 推荐：使用类选择器 */
.container > .item {}

/* ❌ 避免：通用选择器 */
.container * {}
```

#### 属性书写顺序

```css
.component {
  /* 1. 定位属性 */
  position: relative;
  top: 0;
  right: 0;
  z-index: 1;
  
  /* 2. 盒模型 */
  display: flex;
  width: 100%;
  height: 40px;
  padding: 10px;
  margin: 0 auto;
  
  /* 3. 文本属性 */
  font-size: 14px;
  line-height: 1.5;
  text-align: center;
  
  /* 4. 视觉效果 */
  color: #333;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  
  /* 5. 其他属性 */
  opacity: 1;
  cursor: pointer;
  transition: all 0.3s ease;
}
```

## 常见问题与解决方案

### API 响应处理

```typescript
// ❌ 常见错误 1：直接访问 data
const businessDetail = await getBusinessInfo(id);
console.log(businessDetail.projectName); // 可能报错

// ✅ 正确做法
const response = await getBusinessInfo(id);
console.log(response?.data?.projectName);

// ✅ 正确做法
if (response.data) {
  // 处理数据
}

// ✅ 正确做法
dataSource.value = response.data.list;
setPagination(response?.data?.pagination);
```

### 样式问题解决

```css
/* 问题 1：全局样式污染 */
/* ✅ 解决：使用作用域样式 */
<style lang="less" scoped>
.component { /* 样式 */ }
</style>

/* 问题 2：样式优先级过高 */
/* ❌ 避免 */
.component .item .text { color: red !important; }

/* ✅ 推荐 */
.component-item-text { color: red; }

/* 问题 3：响应式问题 */
/* ❌ 固定像素 */
.container { width: 1200px; }

/* ✅ 相对单位 */
.container { 
  width: 100%;
  max-width: 1200px;
}
```

## 开发检查清单

### API 响应处理
- [ ] 正确访问 `response.data`
- [ ] 处理错误情况并显示 `response.msg`
- [ ] 分页数据使用 `response.data.list`
- [ ] 使用 TypeScript 类型定义

### 样式编写
- [ ] 使用语义化类名
- [ ] 遵循 BEM 命名法则
- [ ] 使用作用域样式
- [ ] 支持响应式设计
- [ ] 使用主题变量
- [ ] 避免过度嵌套（最多3层）