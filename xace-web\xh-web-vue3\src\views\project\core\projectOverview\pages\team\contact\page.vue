<template>
  <div class="contact-page p-4">
    <!-- 页面标题 -->
    <div class="page-header mb-4">
      <h2 class="text-xl font-semibold mb-2">项目通信录</h2>
      <p class="text-gray-600">查看项目团队成员的联系方式和详细信息</p>
    </div>

    <a-spin :spinning="loading">
      <!-- 工具栏 -->
      <div class="toolbar mb-4 flex justify-between items-center">
        <div class="filters flex items-center space-x-4">
          <a-input-search v-model:value="searchText" placeholder="搜索姓名或角色" style="width: 300px" @search="handleSearch" />
          <a-select v-model:value="departmentFilter" placeholder="筛选部门" style="width: 180px" allow-clear @change="handleDepartmentFilter">
            <a-select-option v-for="dept in departments" :key="dept.value" :value="dept.value">
              {{ dept.label }}
            </a-select-option>
          </a-select>
          <a-select v-model:value="roleFilter" placeholder="筛选角色" style="width: 180px" allow-clear @change="handleRoleFilter">
            <a-select-option v-for="role in roles" :key="role.value" :value="role.value">
              {{ role.label }}
            </a-select-option>
          </a-select>
        </div>
        <div class="actions">
          <a-space>
            <a-switch v-model:checked="showActiveOnly" checked-children="只显示活动用户" un-checked-children="显示所有用户" />
            <a-button @click="handleExport">
              <template #icon><ExportOutlined /></template>
              导出到Excel
            </a-button>
            <a-button @click="handleRefresh">
              <template #icon><ReloadOutlined /></template>
              刷新
            </a-button>
          </a-space>
        </div>
      </div>

      <!-- 联系人统计卡片 -->
      <div class="contact-stats grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="stat-card bg-blue-50 rounded-lg p-4">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-sm text-blue-600">总人数</div>
              <div class="text-2xl font-bold text-blue-600">{{ contactStats.totalUsers }}</div>
            </div>
            <UserOutlined class="text-3xl text-blue-600" />
          </div>
        </div>
        <div class="stat-card bg-green-50 rounded-lg p-4">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-sm text-green-600">活动用户</div>
              <div class="text-2xl font-bold text-green-600">{{ contactStats.activeUsers }}</div>
            </div>
            <CheckCircleOutlined class="text-3xl text-green-600" />
          </div>
        </div>
        <div class="stat-card bg-orange-50 rounded-lg p-4">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-sm text-orange-600">部门数</div>
              <div class="text-2xl font-bold text-orange-600">{{ contactStats.departments }}</div>
            </div>
            <ApartmentOutlined class="text-3xl text-orange-600" />
          </div>
        </div>
        <div class="stat-card bg-purple-50 rounded-lg p-4">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-sm text-purple-600">角色类型</div>
              <div class="text-2xl font-bold text-purple-600">{{ contactStats.roles }}</div>
            </div>
            <CrownOutlined class="text-3xl text-purple-600" />
          </div>
        </div>
      </div>

      <!-- 联系人表格 -->
      <div class="contact-table bg-white rounded-lg shadow-sm border">
        <a-table
          :columns="contactColumns"
          :data-source="filteredContacts"
          :pagination="pagination"
          :scroll="{ x: 1400 }"
          row-key="id"
          size="small"
          :row-selection="{
            selectedRowKeys: selectedRowKeys,
            onChange: onSelectChange,
          }">
          <!-- 显示名称列 -->
          <template #displayName="{ record }">
            <div class="flex items-center">
              <a-avatar :size="32" :src="record.avatar" class="mr-3">
                {{ record.displayName?.charAt(0) || '?' }}
              </a-avatar>
              <div>
                <div class="font-medium">{{ record.displayName }}</div>
                <div class="text-sm text-gray-500">{{ record.employeeId }}</div>
              </div>
            </div>
          </template>

          <!-- 角色列 -->
          <template #roleList="{ record }">
            <div class="role-tags">
              <a-tag v-for="role in record.roleList" :key="role" :color="getRoleColor(role)" class="mb-1">
                {{ role }}
              </a-tag>
            </div>
          </template>

          <!-- 联系方式列 -->
          <template #contact="{ record }">
            <div class="contact-info">
              <div v-if="record.email" class="text-sm mb-1">
                <MailOutlined class="mr-1" />
                <a :href="`mailto:${record.email}`" class="text-blue-600">{{ record.email }}</a>
              </div>
              <div v-if="record.mobilePhone" class="text-sm mb-1">
                <PhoneOutlined class="mr-1" />
                <a :href="`tel:${record.mobilePhone}`" class="text-blue-600">{{ record.mobilePhone }}</a>
              </div>
              <div v-if="record.telephone" class="text-sm">
                <PhoneOutlined class="mr-1" />
                <a :href="`tel:${record.telephone}`" class="text-blue-600">{{ record.telephone }}</a>
              </div>
            </div>
          </template>

          <!-- 资源类型列 -->
          <template #resourceType="{ record }">
            <a-tag v-if="record.resourceTypeName" :color="getResourceTypeColor(record.resourceTypeName)">
              {{ record.resourceTypeName }}
            </a-tag>
            <span v-else class="text-gray-400">-</span>
          </template>

          <!-- 雇佣类型列 -->
          <template #hireType="{ record }">
            <a-tag v-if="record.hireTypeName" :color="getHireTypeColor(record.hireTypeName)">
              {{ record.hireTypeName }}
            </a-tag>
            <span v-else class="text-gray-400">-</span>
          </template>

          <!-- 操作列 -->
          <template #action="{ record }">
            <a-space>
              <a-button type="link" size="small" @click="handleViewDetail(record)"> 查看详情 </a-button>
              <a-dropdown>
                <a-button type="link" size="small"> 更多 <DownOutlined /> </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="handleSendEmail(record)" :disabled="!record.email">
                      <MailOutlined />
                      发送邮件
                    </a-menu-item>
                    <a-menu-item @click="handleCall(record)" :disabled="!record.mobilePhone">
                      <PhoneOutlined />
                      拨打电话
                    </a-menu-item>
                    <a-menu-item @click="handleExportCard(record)">
                      <IdcardOutlined />
                      导出名片
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item @click="handleEditContact(record)">
                      <EditOutlined />
                      编辑信息
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </a-table>
      </div>
    </a-spin>

    <!-- 联系人详情模态框 -->
    <a-modal v-model:open="detailModalVisible" title="联系人详情" width="70%" :footer="null" :mask-closable="false">
      <div v-if="selectedContact" class="contact-detail">
        <div class="contact-header flex items-center mb-6">
          <a-avatar :size="80" :src="selectedContact.avatar" class="mr-4">
            {{ selectedContact.displayName?.charAt(0) || '?' }}
          </a-avatar>
          <div class="contact-basic-info">
            <h3 class="text-xl font-semibold">{{ selectedContact.displayName }}</h3>
            <p class="text-gray-600">{{ selectedContact.departmentName }}</p>
            <p class="text-sm text-gray-500">{{ selectedContact.employeeId }}</p>
          </div>
        </div>

        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="项目角色">
            <div class="role-tags">
              <a-tag v-for="role in selectedContact.roleList" :key="role" :color="getRoleColor(role)" class="mb-1">
                {{ role }}
              </a-tag>
            </div>
          </a-descriptions-item>
          <a-descriptions-item label="所属部门">
            {{ selectedContact.departmentName || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="电子邮箱">
            <a v-if="selectedContact.email" :href="`mailto:${selectedContact.email}`" class="text-blue-600">
              {{ selectedContact.email }}
            </a>
            <span v-else class="text-gray-400">-</span>
          </a-descriptions-item>
          <a-descriptions-item label="手机号码">
            <a v-if="selectedContact.mobilePhone" :href="`tel:${selectedContact.mobilePhone}`" class="text-blue-600">
              {{ selectedContact.mobilePhone }}
            </a>
            <span v-else class="text-gray-400">-</span>
          </a-descriptions-item>
          <a-descriptions-item label="办公电话">
            <a v-if="selectedContact.telephone" :href="`tel:${selectedContact.telephone}`" class="text-blue-600">
              {{ selectedContact.telephone }}
            </a>
            <span v-else class="text-gray-400">-</span>
          </a-descriptions-item>
          <a-descriptions-item label="资源类型">
            <a-tag v-if="selectedContact.resourceTypeName" :color="getResourceTypeColor(selectedContact.resourceTypeName)">
              {{ selectedContact.resourceTypeName }}
            </a-tag>
            <span v-else class="text-gray-400">-</span>
          </a-descriptions-item>
          <a-descriptions-item label="雇佣类型">
            <a-tag v-if="selectedContact.hireTypeName" :color="getHireTypeColor(selectedContact.hireTypeName)">
              {{ selectedContact.hireTypeName }}
            </a-tag>
            <span v-else class="text-gray-400">-</span>
          </a-descriptions-item>
          <a-descriptions-item label="入职时间">
            {{ formatDate(selectedContact.hireDate) }}
          </a-descriptions-item>
          <a-descriptions-item label="参与项目时间">
            {{ formatDate(selectedContact.joinProjectDate) }}
          </a-descriptions-item>
          <a-descriptions-item label="用户状态">
            <a-tag :color="selectedContact.isActive ? 'green' : 'red'">
              {{ selectedContact.isActive ? '活动' : '非活动' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="技能标签" :span="2">
            <a-tag v-for="skill in selectedContact.skills" :key="skill" class="mb-1">
              {{ skill }}
            </a-tag>
            <span v-if="!selectedContact.skills || selectedContact.skills.length === 0" class="text-gray-400"> 暂无技能标签 </span>
          </a-descriptions-item>
          <a-descriptions-item label="备注" :span="2">
            {{ selectedContact.remark || '暂无备注' }}
          </a-descriptions-item>
        </a-descriptions>

        <!-- 快速操作按钮 -->
        <div class="contact-actions mt-6 text-center">
          <a-space>
            <a-button v-if="selectedContact.email" type="primary" @click="handleSendEmail(selectedContact)">
              <MailOutlined />
              发送邮件
            </a-button>
            <a-button v-if="selectedContact.mobilePhone" @click="handleCall(selectedContact)">
              <PhoneOutlined />
              拨打电话
            </a-button>
            <a-button @click="handleExportCard(selectedContact)">
              <IdcardOutlined />
              导出名片
            </a-button>
          </a-space>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import dayjs from 'dayjs';
  import {
    UserOutlined,
    CheckCircleOutlined,
    ApartmentOutlined,
    CrownOutlined,
    ExportOutlined,
    ReloadOutlined,
    MailOutlined,
    PhoneOutlined,
    IdcardOutlined,
    EditOutlined,
    DownOutlined,
  } from '@ant-design/icons-vue';

  const { createMessage } = useMessage();

  // 响应式数据
  const loading = ref(false);
  const searchText = ref('');
  const departmentFilter = ref('');
  const roleFilter = ref('');
  const showActiveOnly = ref(true);
  const selectedRowKeys = ref([]);
  const detailModalVisible = ref(false);
  const selectedContact = ref(null);

  // 联系人统计数据
  const contactStats = ref({
    totalUsers: 25,
    activeUsers: 23,
    departments: 8,
    roles: 12,
  });

  // 部门选项
  const departments = ref([
    { value: '易趋集团', label: '易趋集团' },
    { value: '知识产权管理部', label: '知识产权管理部' },
    { value: '投资分析组', label: '投资分析组' },
    { value: 'DevOps平台组', label: 'DevOps平台组' },
    { value: '软件开发中心', label: '软件开发中心' },
    { value: '专业服务事业部', label: '专业服务事业部' },
    { value: '技术培训学院', label: '技术培训学院' },
    { value: '咨询服务中心', label: '咨询服务中心' },
  ]);

  // 角色选项
  const roles = ref([
    { value: '项目经理', label: '项目经理' },
    { value: '项目发起人', label: '项目发起人' },
    { value: '项目干系人', label: '项目干系人' },
    { value: '项目成员', label: '项目成员' },
    { value: '法律顾问', label: '法律顾问' },
    { value: '财务分析师', label: '财务分析师' },
    { value: '投资经理', label: '投资经理' },
    { value: '审计经理', label: '审计经理' },
    { value: '审计员', label: '审计员' },
    { value: '项目助理', label: '项目助理' },
    { value: '项目IT支持工程师', label: '项目IT支持工程师' },
    { value: '质量控制专员', label: '质量控制专员' },
  ]);

  // 分页配置
  const pagination = reactive({
    current: 1,
    pageSize: 20,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number) => `共 ${total} 条记录`,
  });

  // 联系人数据
  const contactList = ref([
    {
      id: '1',
      displayName: 'Demo演示用户',
      employeeId: 'E001',
      roleList: ['项目经理', '项目发起人'],
      departmentName: '易趋集团',
      email: '<EMAIL>',
      telephone: '',
      mobilePhone: '',
      resourceTypeName: '高级项目经理',
      hireTypeName: '',
      avatar: '',
      isActive: true,
      hireDate: '2020-01-15',
      joinProjectDate: '2025-01-01',
      skills: ['项目管理', 'PMP', '敏捷开发'],
      remark: '项目主要负责人',
    },
    {
      id: '2',
      displayName: 'DM',
      employeeId: 'E002',
      roleList: ['项目干系人'],
      departmentName: '易趋集团',
      email: '<EMAIL>',
      telephone: '',
      mobilePhone: '',
      resourceTypeName: '',
      hireTypeName: '',
      avatar: '',
      isActive: true,
      hireDate: '2021-03-10',
      joinProjectDate: '2025-01-01',
      skills: ['业务分析', '需求管理'],
      remark: '项目干系人',
    },
    {
      id: '3',
      displayName: 'PM',
      employeeId: 'E003',
      roleList: ['项目干系人'],
      departmentName: '易趋集团',
      email: '<EMAIL>',
      telephone: '',
      mobilePhone: '',
      resourceTypeName: '',
      hireTypeName: '',
      avatar: '',
      isActive: true,
      hireDate: '2020-08-20',
      joinProjectDate: '2025-01-01',
      skills: ['项目管理', '团队协作'],
      remark: '项目管理支持',
    },
    {
      id: '4',
      displayName: 'PMO',
      employeeId: 'E004',
      roleList: ['项目干系人'],
      departmentName: '易趋集团',
      email: '<EMAIL>',
      telephone: '',
      mobilePhone: '',
      resourceTypeName: '',
      hireTypeName: '',
      avatar: '',
      isActive: true,
      hireDate: '2019-11-05',
      joinProjectDate: '2025-01-01',
      skills: ['项目管理办公室', '流程管理'],
      remark: 'PMO支持',
    },
    {
      id: '5',
      displayName: 'TM',
      employeeId: 'E005',
      roleList: ['项目干系人'],
      departmentName: '易趋集团',
      email: '<EMAIL>',
      telephone: '',
      mobilePhone: '',
      resourceTypeName: '',
      hireTypeName: '',
      avatar: '',
      isActive: true,
      hireDate: '2022-02-15',
      joinProjectDate: '2025-01-01',
      skills: ['团队管理', '技术管理'],
      remark: '技术管理支持',
    },
    {
      id: '6',
      displayName: '陈涛',
      employeeId: 'E006',
      roleList: ['法律顾问'],
      departmentName: '知识产权管理部',
      email: '<EMAIL>',
      telephone: '',
      mobilePhone: '13209900092',
      resourceTypeName: '高级测试工程师',
      hireTypeName: '',
      avatar: '',
      isActive: true,
      hireDate: '2020-06-18',
      joinProjectDate: '2025-01-05',
      skills: ['法律咨询', '知识产权', '合规审查'],
      remark: '负责项目法律事务',
    },
    {
      id: '7',
      displayName: '陈涛国',
      employeeId: 'E007',
      roleList: ['法律顾问'],
      departmentName: '投资分析组',
      email: '<EMAIL>',
      telephone: '',
      mobilePhone: '13209900070',
      resourceTypeName: '投资经理',
      hireTypeName: '',
      avatar: '',
      isActive: true,
      hireDate: '2021-09-12',
      joinProjectDate: '2025-01-10',
      skills: ['投资分析', '风险评估', '财务建模'],
      remark: '投资分析支持',
    },
    {
      id: '8',
      displayName: '陈涛华',
      employeeId: 'E008',
      roleList: ['项目成员'],
      departmentName: 'DevOps平台组',
      email: '<EMAIL>',
      telephone: '',
      mobilePhone: '13209900054',
      resourceTypeName: '中级运维工程师',
      hireTypeName: '',
      avatar: '',
      isActive: true,
      hireDate: '2021-12-08',
      joinProjectDate: '2025-01-15',
      skills: ['DevOps', '自动化部署', '监控运维'],
      remark: '负责项目运维支持',
    },
    {
      id: '9',
      displayName: '陈涛龙',
      employeeId: 'E009',
      roleList: ['项目成员'],
      departmentName: '软件开发中心',
      email: '<EMAIL>',
      telephone: '',
      mobilePhone: '13209900032',
      resourceTypeName: '高级产品经理',
      hireTypeName: '',
      avatar: '',
      isActive: true,
      hireDate: '2019-07-25',
      joinProjectDate: '2025-01-08',
      skills: ['产品设计', '用户体验', '需求分析'],
      remark: '负责产品设计',
    },
    {
      id: '10',
      displayName: '陈涛中',
      employeeId: 'E010',
      roleList: ['项目经理', '质量控制专员'],
      departmentName: '专业服务事业部',
      email: '<EMAIL>',
      telephone: '',
      mobilePhone: '13209900085',
      resourceTypeName: '初级软件工程师',
      hireTypeName: '',
      avatar: '',
      isActive: true,
      hireDate: '2022-04-10',
      joinProjectDate: '2025-01-12',
      skills: ['软件开发', '质量控制', '测试管理'],
      remark: '负责质量控制',
    },
    {
      id: '11',
      displayName: '陈伟',
      employeeId: 'E011',
      roleList: ['财务分析师'],
      departmentName: '技术培训学院',
      email: '<EMAIL>',
      telephone: '',
      mobilePhone: '13209900063',
      resourceTypeName: '高级测试工程师',
      hireTypeName: '',
      avatar: '',
      isActive: true,
      hireDate: '2020-11-15',
      joinProjectDate: '2025-01-18',
      skills: ['财务分析', '成本控制', '预算管理'],
      remark: '负责财务分析',
    },
    {
      id: '12',
      displayName: '李伟',
      employeeId: 'E012',
      roleList: ['投资经理'],
      departmentName: '咨询服务中心',
      email: '<EMAIL>',
      telephone: '',
      mobilePhone: '13209900038',
      resourceTypeName: '审计经理',
      hireTypeName: '',
      avatar: '',
      isActive: true,
      hireDate: '2019-05-20',
      joinProjectDate: '2025-01-05',
      skills: ['投资管理', '风险控制', '项目评估'],
      remark: '负责投资管理',
    },
    {
      id: '13',
      displayName: '梁伟',
      employeeId: 'E013',
      roleList: ['投资经理'],
      departmentName: '咨询服务中心',
      email: '<EMAIL>',
      telephone: '',
      mobilePhone: '13209900014',
      resourceTypeName: '高级审计员',
      hireTypeName: '',
      avatar: '',
      isActive: true,
      hireDate: '2020-09-08',
      joinProjectDate: '2025-01-20',
      skills: ['审计管理', '内控审计', '风险评估'],
      remark: '负责审计工作',
    },
    {
      id: '14',
      displayName: '孙涛国',
      employeeId: 'E014',
      roleList: ['财务分析师', '审计员'],
      departmentName: '技术培训学院',
      email: '<EMAIL>',
      telephone: '',
      mobilePhone: '13209900015',
      resourceTypeName: '审计经理',
      hireTypeName: '',
      avatar: '',
      isActive: true,
      hireDate: '2021-01-12',
      joinProjectDate: '2025-01-25',
      skills: ['财务审计', '内控评估', '合规检查'],
      remark: '负责财务审计',
    },
    {
      id: '15',
      displayName: '王涛',
      employeeId: 'E015',
      roleList: ['项目助理'],
      departmentName: '技术培训学院',
      email: '<EMAIL>',
      telephone: '',
      mobilePhone: '13209900039',
      resourceTypeName: '行政助理',
      hireTypeName: '',
      avatar: '',
      isActive: true,
      hireDate: '2022-06-15',
      joinProjectDate: '2025-01-22',
      skills: ['项目协调', '文档管理', '会议组织'],
      remark: '负责项目协调',
    },
    {
      id: '16',
      displayName: '吴迪国',
      employeeId: 'E016',
      roleList: ['项目IT支持工程师', '审计员'],
      departmentName: '咨询服务中心',
      email: '<EMAIL>',
      telephone: '',
      mobilePhone: '13209900062',
      resourceTypeName: '高级数据分析师',
      hireTypeName: '',
      avatar: '',
      isActive: true,
      hireDate: '2019-12-10',
      joinProjectDate: '2025-01-08',
      skills: ['IT支持', '数据分析', '系统维护'],
      remark: '负责IT技术支持',
    },
    {
      id: '17',
      displayName: '张敏',
      employeeId: 'E017',
      roleList: ['项目IT支持工程师'],
      departmentName: '咨询服务中心',
      email: '<EMAIL>',
      telephone: '',
      mobilePhone: '13209900086',
      resourceTypeName: '财务分析师',
      hireTypeName: '',
      avatar: '',
      isActive: true,
      hireDate: '2020-03-15',
      joinProjectDate: '2025-01-15',
      skills: ['财务分析', 'IT支持', '数据处理'],
      remark: '负责财务IT支持',
    },
    {
      id: '18',
      displayName: '张强',
      employeeId: 'E018',
      roleList: ['项目经理', '质量控制专员'],
      departmentName: '专业服务事业部',
      email: '<EMAIL>',
      telephone: '',
      mobilePhone: '13209900037',
      resourceTypeName: '技术支持工程师',
      hireTypeName: '',
      avatar: '',
      isActive: true,
      hireDate: '2018-08-20',
      joinProjectDate: '2025-01-01',
      skills: ['项目管理', '质量控制', '技术支持'],
      remark: '项目主要负责人',
    },
    {
      id: '19',
      displayName: '赵敏',
      employeeId: 'E019',
      roleList: ['审计经理'],
      departmentName: '专业服务事业部',
      email: '<EMAIL>',
      telephone: '',
      mobilePhone: '13209900061',
      resourceTypeName: '中级实施工程师',
      hireTypeName: '',
      avatar: '',
      isActive: true,
      hireDate: '2020-10-12',
      joinProjectDate: '2025-01-10',
      skills: ['审计管理', '实施管理', '流程优化'],
      remark: '负责审计实施',
    },
    {
      id: '20',
      displayName: '赵伟',
      employeeId: 'E020',
      roleList: ['审计经理'],
      departmentName: '专业服务事业部',
      email: '<EMAIL>',
      telephone: '',
      mobilePhone: '13209900013',
      resourceTypeName: '高级项目经理',
      hireTypeName: '',
      avatar: '',
      isActive: true,
      hireDate: '2019-04-18',
      joinProjectDate: '2025-01-05',
      skills: ['审计管理', '项目管理', '团队领导'],
      remark: '审计项目负责人',
    },
    {
      id: '21',
      displayName: '赵伟国',
      employeeId: 'E021',
      roleList: ['项目助理'],
      departmentName: '技术培训学院',
      email: '<EMAIL>',
      telephone: '',
      mobilePhone: '13209900087',
      resourceTypeName: 'IT支持专员',
      hireTypeName: '',
      avatar: '',
      isActive: true,
      hireDate: '2021-07-22',
      joinProjectDate: '2025-01-28',
      skills: ['IT支持', '项目协调', '用户培训'],
      remark: '负责IT支持协调',
    },
  ]);

  // 表格列配置
  const contactColumns = [
    {
      title: '显示名',
      dataIndex: 'displayName',
      key: 'displayName',
      width: 200,
      sorter: (a, b) => a.displayName.localeCompare(b.displayName),
      slots: { customRender: 'displayName' },
    },
    {
      title: '角色',
      dataIndex: 'roleList',
      key: 'roleList',
      width: 200,
      slots: { customRender: 'roleList' },
    },
    {
      title: '所属部门',
      dataIndex: 'departmentName',
      key: 'departmentName',
      width: 150,
      sorter: (a, b) => a.departmentName.localeCompare(b.departmentName),
    },
    {
      title: '联系方式',
      dataIndex: 'contact',
      key: 'contact',
      width: 200,
      slots: { customRender: 'contact' },
    },
    {
      title: '资源类型',
      dataIndex: 'resourceTypeName',
      key: 'resourceTypeName',
      width: 150,
      slots: { customRender: 'resourceType' },
    },
    {
      title: '雇佣类型',
      dataIndex: 'hireTypeName',
      key: 'hireTypeName',
      width: 120,
      slots: { customRender: 'hireType' },
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right',
      slots: { customRender: 'action' },
    },
  ];

  // 计算属性
  const filteredContacts = computed(() => {
    let result = contactList.value;

    // 活动用户过滤
    if (showActiveOnly.value) {
      result = result.filter(contact => contact.isActive);
    }

    // 搜索过滤
    if (searchText.value) {
      const searchLower = searchText.value.toLowerCase();
      result = result.filter(
        contact =>
          contact.displayName.toLowerCase().includes(searchLower) ||
          contact.roleList.some(role => role.toLowerCase().includes(searchLower)) ||
          contact.departmentName.toLowerCase().includes(searchLower),
      );
    }

    // 部门过滤
    if (departmentFilter.value) {
      result = result.filter(contact => contact.departmentName === departmentFilter.value);
    }

    // 角色过滤
    if (roleFilter.value) {
      result = result.filter(contact => contact.roleList.includes(roleFilter.value));
    }

    return result;
  });

  onMounted(() => {
    loadContactData();
  });

  // 加载联系人数据
  const loadContactData = async () => {
    loading.value = true;
    try {
      // 这里调用实际的API
      // const result = await getContactList();
      // contactList.value = result.data;

      pagination.total = filteredContacts.value.length;
    } catch (error) {
      console.error('加载联系人数据失败:', error);
      createMessage.error('加载联系人数据失败');
    } finally {
      loading.value = false;
    }
  };

  // 工具函数
  const getRoleColor = (role: string) => {
    const colorMap = {
      项目经理: 'blue',
      项目发起人: 'green',
      项目干系人: 'orange',
      项目成员: 'cyan',
      法律顾问: 'purple',
      财务分析师: 'magenta',
      投资经理: 'red',
      审计经理: 'volcano',
      审计员: 'gold',
      项目助理: 'lime',
      项目IT支持工程师: 'geekblue',
      质量控制专员: 'pink',
    };
    return colorMap[role] || 'default';
  };

  const getResourceTypeColor = (resourceType: string) => {
    const colorMap = {
      高级项目经理: 'blue',
      高级测试工程师: 'green',
      投资经理: 'red',
      中级运维工程师: 'orange',
      高级产品经理: 'purple',
      初级软件工程师: 'cyan',
      审计经理: 'magenta',
      高级审计员: 'volcano',
      行政助理: 'lime',
      高级数据分析师: 'geekblue',
      财务分析师: 'pink',
      技术支持工程师: 'gold',
      中级实施工程师: 'orange',
      IT支持专员: 'cyan',
    };
    return colorMap[resourceType] || 'default';
  };

  const getHireTypeColor = (hireType: string) => {
    const colorMap = {
      正式员工: 'green',
      合同工: 'orange',
      临时工: 'red',
      实习生: 'blue',
      外包: 'purple',
    };
    return colorMap[hireType] || 'default';
  };

  const formatDate = (date: string) => {
    return date ? dayjs(date).format('YYYY-MM-DD') : '-';
  };

  // 事件处理函数
  const handleSearch = () => {
    // 触发搜索
  };

  const handleDepartmentFilter = () => {
    // 触发部门过滤
  };

  const handleRoleFilter = () => {
    // 触发角色过滤
  };

  const handleExport = () => {
    createMessage.info('导出功能开发中...');
  };

  const handleRefresh = () => {
    loadContactData();
  };

  const handleViewDetail = (record: any) => {
    selectedContact.value = record;
    detailModalVisible.value = true;
  };

  const handleSendEmail = (record: any) => {
    if (record.email) {
      window.location.href = `mailto:${record.email}`;
    } else {
      createMessage.warning('该联系人没有邮箱地址');
    }
  };

  const handleCall = (record: any) => {
    if (record.mobilePhone) {
      window.location.href = `tel:${record.mobilePhone}`;
    } else {
      createMessage.warning('该联系人没有手机号码');
    }
  };

  const handleExportCard = (record: any) => {
    createMessage.info('导出名片功能开发中...');
  };

  const handleEditContact = (record: any) => {
    createMessage.info('编辑联系人功能开发中...');
  };

  const onSelectChange = (selectedKeys: string[]) => {
    selectedRowKeys.value = selectedKeys;
  };
</script>

<style scoped>
  .contact-page {
    background: var(--section-bg-color);
  }

  .stat-card {
    transition: all 0.3s ease;
  }

  .stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .contact-table {
    min-height: 400px;
  }

  .role-tags .ant-tag {
    margin-bottom: 4px;
  }

  .contact-info {
    line-height: 1.4;
  }

  .contact-header {
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 20px;
  }

  .contact-detail {
    max-height: 70vh;
    overflow-y: auto;
  }

  /* 表格样式 */
  :deep(.ant-table-tbody > tr > td) {
    vertical-align: top;
    padding: 12px 8px;
  }

  :deep(.ant-table-thead > tr > th) {
    background-color: #fafafa;
    font-weight: 600;
  }

  :deep(.ant-table-tbody > tr:hover > td) {
    background-color: var(--section-bg-color);
  }
</style>
