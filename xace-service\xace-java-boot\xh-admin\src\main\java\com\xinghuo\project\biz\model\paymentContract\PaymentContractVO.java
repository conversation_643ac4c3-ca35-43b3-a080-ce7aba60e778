package com.xinghuo.project.biz.model.paymentContract;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Schema(description = "采购合同视图对象")
public class PaymentContractVO {

    /**
     * 采购合同ID
     */
    @Schema(description = "采购合同ID")
    private String id;

    /**
     * 采购合同名称
     */
    @Schema(description = "采购合同名称")
    private String name;

    /**
     * 采购合同编号
     */
    @Schema(description = "采购合同编号")
    private String cno;

    /**
     * 收款合同ID
     */
    @Schema(description = "收款合同ID")
    private String contractId;

    /**
     * 收款合同名称（非数据库字段，需要关联查询）
     */
    @Schema(description = "收款合同名称")
    private String contractName;

    /**
     * 收款合同编号（非数据库字段，需要关联查询）
     */
    @Schema(description = "收款合同编号")
    private String contractNo;

    /**
     * 供应商ID
     */
    @Schema(description = "供应商ID")
    private String suppilerId;

    /**
     * 供应商名称（非数据库字段，需要关联查询）
     */
    @Schema(description = "供应商名称")
    private String supplierName;

    /**
     * 采购负责人ID
     */
    @Schema(description = "采购负责人ID")
    private String ownId;

    /**
     * 采购负责人名称（非数据库字段，需要关联查询）
     */
    @Schema(description = "采购负责人名称")
    private String ownName;

    /**
     * 采购合同金额
     */
    @Schema(description = "采购合同金额")
    private BigDecimal amount;

    /**
     * 已付金额
     */
    @Schema(description = "已付金额")
    private BigDecimal yfAmount;

    /**
     * 付款状态
     */
    @Schema(description = "付款状态")
    private String moneyStatus;

    /**
     * 采购合同状态
     */
    @Schema(description = "采购合同状态")
    private String status;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 预计签订日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "预计签订日期")
    private Date estSignDate;

    /**
     * 实际签订日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "实际签订日期")
    private Date signDate;

    /**
     * 签订年份
     */
    @Schema(description = "签订年份")
    private Integer signYear;

    /**
     * 开发一部金额
     */
    @Schema(description = "开发一部金额")
    private BigDecimal kfybAmount;

    /**
     * 开发二部金额
     */
    @Schema(description = "开发二部金额")
    private BigDecimal kfebAmount;

    /**
     * 综合金额
     */
    @Schema(description = "综合金额")
    private BigDecimal otherAmount;

    /**
     * 创建用户
     */
    @Schema(description = "创建用户")
    private String createUserId;

    /**
     * 创建用户名称（非数据库字段，需要关联查询）
     */
    @Schema(description = "创建用户名称")
    private String createUserName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 最后修改人
     */
    @Schema(description = "最后修改人")
    private String lastModifiedUserId;

    /**
     * 最后修改人名称（非数据库字段，需要关联查询）
     */
    @Schema(description = "最后修改人名称")
    private String lastModifiedUserName;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 删除标记
     */
    @Schema(description = "删除标记")
    private Integer deleteMark;
}
