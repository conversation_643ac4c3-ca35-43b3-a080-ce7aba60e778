package com.xinghuo.project.template.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 标准交付物库选择列表视图对象
 * 用于下拉选择框等场景，只包含必要的id和fullName字段
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@Data
@Schema(description = "标准交付物库选择列表视图对象")
public class WorkProductLibrarySelectVO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private String id;

    /**
     * 完整名称 (格式: [编码] 名称)
     * 用于option显示，符合fullName和id的规范要求
     */
    @Schema(description = "完整名称")
    private String fullName;

    /**
     * 交付物编码 (用于构建fullName)
     */
    @Schema(description = "交付物编码")
    private String code;

    /**
     * 交付物名称 (用于构建fullName)
     */
    @Schema(description = "交付物名称")
    private String name;

    /**
     * 交付物类型ID
     */
    @Schema(description = "交付物类型ID")
    private String typeId;

    /**
     * 交付物类型名称
     */
    @Schema(description = "交付物类型名称")
    private String typeName;

    /**
     * 状态ID
     */
    @Schema(description = "状态ID")
    private String statusId;

    /**
     * 状态名称
     */
    @Schema(description = "状态名称")
    private String statusName;

    /**
     * 是否需要评审 (1:是, 0:否)
     */
    @Schema(description = "是否需要评审")
    private Integer needReview;

    /**
     * 是否是项目最终交付成果 (1:是, 0:否)
     */
    @Schema(description = "是否是项目最终交付成果")
    private Integer isDeliverable;

    /**
     * 是否可裁剪 (1:可, 0:不可)
     */
    @Schema(description = "是否可裁剪")
    private Integer canCut;
}
