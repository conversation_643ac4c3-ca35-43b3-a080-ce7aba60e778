import { defHttp } from '/@/utils/http/axios';
import { ListResult } from '/@/api/model/baseModel';

/**
 * 商机标签记录API
 */

// API URL前缀
const API_PREFIX = '/api/project/business-tag';

/**
 * 商机标签记录对象接口
 */
export interface BusinessTagModel {
  id: string;
  businessId: string;
  tagType: string;
  tagTitle: string;
  tagContent: string;
  createUser: string;
  createTime?: string;
  updateTime?: string;
}

/**
 * 商机标签记录查询参数接口
 */
export interface BusinessTagQueryParams {
  businessId: string;
  page?: number;
  pageSize?: number;
}

/**
 * 获取商机标签记录列表
 * @param params 查询参数
 * @returns 商机标签记录列表
 */
export const getBusinessTagList = (params: BusinessTagQueryParams) => {
  return defHttp.get<ListResult<BusinessTagModel>>({
    url: API_PREFIX,
    params,
  });
};

/**
 * 获取商机标签记录详情
 * @param id 标签记录ID
 * @returns 商机标签记录详情
 */
export const getBusinessTag = (id: string) => {
  return defHttp.get<BusinessTagModel>({
    url: `${API_PREFIX}/${id}`,
  });
};

/**
 * 添加商机标签记录
 * @param params 标签记录参数
 * @returns 操作结果
 */
export const addBusinessTag = (params: Omit<BusinessTagModel, 'id' | 'createTime' | 'updateTime'>) => {
  return defHttp.post<string>({
    url: API_PREFIX,
    data: params,
  });
};

/**
 * 更新商机标签记录
 * @param id 标签记录ID
 * @param params 标签记录参数
 * @returns 操作结果
 */
export const updateBusinessTag = (id: string, params: Omit<BusinessTagModel, 'id' | 'createTime' | 'updateTime'>) => {
  return defHttp.put<void>({
    url: `${API_PREFIX}/${id}`,
    data: params,
  });
};

/**
 * 删除商机标签记录
 * @param id 标签记录ID
 * @returns 操作结果
 */
export const deleteBusinessTag = (id: string) => {
  return defHttp.delete<void>({
    url: `${API_PREFIX}/${id}`,
  });
};
