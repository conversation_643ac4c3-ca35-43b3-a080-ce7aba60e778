package com.xinghuo.project.template.service;

import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.project.template.entity.IssueLibraryEntity;
import com.xinghuo.project.template.model.IssueLibraryPagination;

import java.util.List;
import java.util.Map;

/**
 * 标准项目问题库服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
public interface IssueLibraryService extends BaseService<IssueLibraryEntity> {

    /**
     * 获取问题库列表
     *
     * @param pagination 分页参数
     * @return 问题库列表
     */
    List<IssueLibraryEntity> getList(IssueLibraryPagination pagination);

    /**
     * 根据状态获取问题库列表
     *
     * @param status 状态
     * @return 问题库列表
     */
    List<IssueLibraryEntity> getListByStatus(String status);

    /**
     * 获取问题库详情
     *
     * @param id 问题库ID
     * @return 问题库信息
     */
    IssueLibraryEntity getInfo(String id);

    /**
     * 创建问题库
     *
     * @param entity 问题库信息
     * @return 问题库ID
     */
    String create(IssueLibraryEntity entity);

    /**
     * 更新问题库
     *
     * @param id 问题库ID
     * @param entity 问题库信息
     */
    void update(String id, IssueLibraryEntity entity);

    /**
     * 删除问题库
     *
     * @param id 问题库ID
     */
    void delete(String id);

    /**
     * 批量删除问题库
     *
     * @param ids 问题库ID列表
     */
    void batchDelete(List<String> ids);

    /**
     * 更新问题库状态
     *
     * @param id 问题库ID
     * @param status 状态
     */
    void updateStatus(String id, String status);

    /**
     * 批量更新状态
     *
     * @param ids 问题库ID列表
     * @param status 状态
     */
    void batchUpdateStatus(List<String> ids, String status);

    /**
     * 检查问题编码是否存在
     *
     * @param code 问题编码
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean isExistByCode(String code, String excludeId);

    /**
     * 根据编码获取问题库
     *
     * @param code 问题编码
     * @return 问题库信息
     */
    IssueLibraryEntity getByCode(String code);

    /**
     * 获取问题库选择列表
     *
     * @param keyword 关键字
     * @return 问题库列表
     */
    List<IssueLibraryEntity> getSelectList(String keyword);

    /**
     * 发布问题库
     *
     * @param id 问题库ID
     */
    void publish(String id);

    /**
     * 归档问题库
     *
     * @param id 问题库ID
     */
    void archive(String id);

    /**
     * 复制问题库
     *
     * @param id 源问题库ID
     * @param newTitle 新标题
     * @return 新问题库ID
     */
    String copy(String id, String newTitle);

    /**
     * 生成问题编码
     *
     * @return 问题编码
     */
    String generateCode();

    /**
     * 获取问题库使用情况
     *
     * @param id 问题库ID
     * @return 使用情况统计
     */
    Map<String, Object> getIssueLibraryUsageInfo(String id);

    /**
     * 根据问题类别获取问题库列表
     *
     * @param issueCategoryId 问题类别ID
     * @return 问题库列表
     */
    List<IssueLibraryEntity> getListByIssueCategory(String issueCategoryId);

    /**
     * 根据优先级获取问题库列表
     *
     * @param priorityId 优先级ID
     * @return 问题库列表
     */
    List<IssueLibraryEntity> getListByPriority(String priorityId);

    /**
     * 检查问题标题是否存在
     *
     * @param title 问题标题
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean isExistByTitle(String title, String excludeId);

    /**
     * 批量发布问题库
     *
     * @param ids 问题库ID列表
     */
    void batchPublish(List<String> ids);

    /**
     * 批量归档问题库
     *
     * @param ids 问题库ID列表
     */
    void batchArchive(List<String> ids);
}
