package com.xinghuo.project.biz.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 业务分配详情实体
 * 用于记录各业务实体的部门金额分配明细，替代硬编码字段
 * 
 * <AUTHOR>
 * @version V2.0
 * @copyright 易趋集团
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_allocation_detail")
public class BizAllocationDetailEntity extends BaseEntityV2.CUDBaseEntityV2<String> {

    /**
     * 业务ID（关联的主业务记录ID）
     */
    @TableField("F_BUSINESS_ID")
    private String businessId;

    /**
     * 业务类型（1-商机 2-合同 3-收款 4-付款）
     */
    @TableField("F_BUSINESS_TYPE")
    private Integer businessType;

    /**
     * 部门ID（关联biz_department表）
     */
    @TableField("F_DEPARTMENT_ID")
    private String departmentId;

    /**
     * 部门编码（冗余字段，便于查询）
     */
    @TableField("F_DEPARTMENT_CODE")
    private String departmentCode;

    /**
     * 分配类型（1-营收 2-外采 3-待签外采 4-外采已付）
     */
    @TableField("F_ALLOCATION_TYPE")
    private Integer allocationType;

    /**
     * 分配金额
     */
    @TableField("F_ALLOCATION_AMOUNT")
    private BigDecimal allocationAmount;

    /**
     * 分配比例（百分比）
     */
    @TableField("F_ALLOCATION_RATIO")
    private BigDecimal allocationRatio;

    /**
     * 备注
     */
    @TableField("F_REMARKS")
    private String remarks;

    /**
     * 状态（0-无效 1-有效）
     */
    @TableField("F_STATUS")
    private Integer status;
}