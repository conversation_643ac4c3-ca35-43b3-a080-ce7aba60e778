# 供应商管理模块

## 📁 目录结构

```
supplier/
├── index.vue              # 供应商管理主页面
├── FormDrawer.vue         # 新增/编辑供应商表单
├── DetailDrawer.vue       # 供应商详情页面
├── route.ts              # 路由配置
├── 用户手册.md            # 详细用户操作手册
├── 快速操作指南.md        # 快速上手指南
└── README.md             # 本文件
```

## 🎯 功能概述

供应商管理模块是项目管理系统的重要组成部分，主要用于管理项目中的供应商信息。

### 核心功能
- ✅ 供应商信息的增删改查
- ✅ 供应商列表的搜索和筛选
- ✅ 供应商详情查看
- ✅ 关联采购合同查看和统计
- ✅ 数据导出和打印
- ✅ 权限控制和操作日志

### 技术特性
- 🚀 基于 Vue 3 + TypeScript 开发
- 🎨 使用 Ant Design Vue 组件库
- 📱 响应式设计，支持多设备访问
- ⚡ 高性能表格组件，支持虚拟滚动
- 🔍 强大的搜索和筛选功能
- 📊 实时数据统计和图表展示

## 🚀 快速开始

### 新用户指南
1. **首次使用**：阅读 [用户手册.md](./用户手册.md) 了解详细功能
2. **快速上手**：查看 [快速操作指南.md](./快速操作指南.md) 学习常用操作
3. **实际操作**：在系统中尝试新增、编辑、查看供应商

### 常用操作
```bash
# 新增供应商
点击"新增供应商" → 填写表单 → 保存

# 查看详情
选择供应商 → 点击"查看" → 查看基本信息和采购合同

# 编辑信息
选择供应商 → 点击"编辑" → 修改信息 → 保存

# 删除供应商
选择供应商 → 点击"删除" → 确认删除
```

## 📋 页面组件说明

### index.vue - 主页面
**功能**：供应商列表展示和管理
**组件**：
- BasicTable：数据表格组件
- TableAction：操作按钮组件
- 搜索表单：支持多条件搜索

**主要功能**：
- 供应商列表展示
- 搜索和筛选
- 新增、编辑、删除、查看操作
- 分页和排序

### FormDrawer.vue - 表单页面
**功能**：新增和编辑供应商信息
**组件**：
- BasicDrawer：抽屉组件
- BasicForm：表单组件

**表单字段**：
- 供应商名称（必填）
- 联系人
- 联系电话
- 排序码
- 备注

### DetailDrawer.vue - 详情页面
**功能**：查看供应商详细信息和关联数据
**组件**：
- BasicDrawer：抽屉组件
- Description：描述列表组件
- BasicTable：采购合同表格
- Tabs：标签页组件

**标签页**：
- 基本信息：供应商基本信息展示
- 采购合同：关联采购合同列表和统计

## 🔧 开发说明

### 技术栈
- **前端框架**：Vue 3.x
- **开发语言**：TypeScript
- **UI组件库**：Ant Design Vue
- **状态管理**：Pinia
- **路由管理**：Vue Router
- **HTTP客户端**：Axios
- **构建工具**：Vite

### 代码结构
```typescript
// 组件导入
import { BasicTable, useTable } from '/@/components/Table';
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
import { BasicForm, useForm } from '/@/components/Form';

// API接口
import { 
  getSupplierList, 
  createSupplier, 
  updateSupplier, 
  deleteSupplier,
  getSupplierInfo 
} from '/@/api/project/supplier';

// 类型定义
interface SupplierModel {
  id: string;
  name: string;
  linkman?: string;
  telephone?: string;
  sortCode?: number;
  remark?: string;
}
```

### API接口说明
| 接口 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 获取供应商列表 | POST | /supplier/getList | 分页查询供应商列表 |
| 创建供应商 | POST | /supplier | 新增供应商 |
| 更新供应商 | PUT | /supplier/{id} | 更新供应商信息 |
| 删除供应商 | DELETE | /supplier/{id} | 删除供应商 |
| 获取供应商详情 | GET | /supplier/{id} | 获取供应商详细信息 |
| 获取关联合同 | POST | /supplier/{id}/paycontracts | 获取供应商关联的采购合同 |

## 📖 文档说明

### 用户手册.md
**适用对象**：终端用户、业务人员
**内容包含**：
- 详细的功能说明
- 逐步操作指导
- 界面元素说明
- 常见问题解答
- 最佳实践建议

### 快速操作指南.md
**适用对象**：新用户、临时用户
**内容包含**：
- 5分钟快速上手
- 常用操作清单
- 快捷键和技巧
- 故障排除方法

## 🔒 权限说明

### 操作权限
| 操作 | 权限要求 | 说明 |
|------|----------|------|
| 查看列表 | supplier:list | 查看供应商列表 |
| 查看详情 | supplier:view | 查看供应商详细信息 |
| 新增供应商 | supplier:create | 创建新的供应商 |
| 编辑供应商 | supplier:update | 修改供应商信息 |
| 删除供应商 | supplier:delete | 删除供应商记录 |

### 数据权限
- 用户只能查看和操作有权限的供应商数据
- 删除操作受业务规则限制（如已关联采购合同的供应商不能删除）

## 🐛 问题反馈

### 常见问题
1. **无法删除供应商**：检查是否有关联的采购合同
2. **搜索无结果**：检查搜索条件和数据权限
3. **页面加载缓慢**：检查网络连接和数据量

### 反馈渠道
- **Bug报告**：通过系统内置的反馈功能
- **功能建议**：联系产品经理或项目负责人
- **技术支持**：联系开发团队或系统管理员

## 📈 更新日志

### v1.0.0 (2024-XX-XX)
- ✅ 基础的供应商管理功能
- ✅ 采购合同关联查看
- ✅ 搜索和筛选功能
- ✅ 权限控制

### 计划功能
- 📋 批量导入供应商
- 📊 供应商数据分析
- 🔔 供应商信息变更通知
- 📱 移动端适配优化

## 🤝 贡献指南

### 开发规范
1. 遵循项目的代码规范和命名约定
2. 添加适当的注释和文档
3. 编写单元测试
4. 提交前进行代码审查

### 提交流程
1. Fork 项目仓库
2. 创建功能分支
3. 提交代码变更
4. 创建 Pull Request
5. 等待代码审查和合并

---

**维护团队**：前端开发组
**最后更新**：2024年
**版本**：v1.0.0
