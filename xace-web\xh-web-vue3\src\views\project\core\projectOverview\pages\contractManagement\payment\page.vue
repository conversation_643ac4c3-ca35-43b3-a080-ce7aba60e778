<template>
  <div class="contract-payment-management">
    <div class="page-header">
      <div class="flex items-center">
        <i class="icon-ym icon-ym-money mr-2 text-lg"></i>
        <span class="text-base font-medium">收款信息</span>
      </div>
      <div class="flex items-center space-x-2">
        <a-button type="primary" @click="handleAdd" v-if="contractId">
          <template #icon><PlusOutlined /></template>
          新增收款
        </a-button>
        <a-button @click="handleRefresh">
          <template #icon><ReloadOutlined /></template>
          刷新
        </a-button>
      </div>
    </div>

    <div class="page-content">
      <Spin :spinning="loading">
        <!-- 收款统计卡片 -->
        <div class="payment-summary mb-4" v-if="contractInfo">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-card>
                <a-statistic title="合同金额" :value="getContractAmount()" :precision="2" suffix="元" :value-style="{ color: '#1890ff' }" />
              </a-card>
            </a-col>
            <a-col :span="6">
              <a-card>
                <a-statistic title="已收金额" :value="getReceivedAmount()" :precision="2" suffix="元" :value-style="{ color: '#52c41a' }" />
              </a-card>
            </a-col>
            <a-col :span="6">
              <a-card>
                <a-statistic
                  title="未收金额"
                  :value="getContractAmount() - getReceivedAmount()"
                  :precision="2"
                  suffix="元"
                  :value-style="{ color: '#f5222d' }" />
              </a-card>
            </a-col>
            <a-col :span="6">
              <a-card>
                <a-statistic title="收款进度" :value="getPaymentProgress()" suffix="%" :precision="1" :value-style="{ color: getProgressColor() }" />
              </a-card>
            </a-col>
          </a-row>
        </div>

        <!-- 收款记录列表 -->
        <div class="payment-list">
          <a-card title="收款记录">
            <a-table :columns="paymentColumns" :data-source="paymentList" :pagination="paymentPagination" :loading="loading" row-key="id">
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'cmMoney'">
                  <span class="text-green-600 font-medium"> ¥{{ record.cmMoney?.toLocaleString() || 0 }} </span>
                </template>
                <template v-else-if="column.key === 'shoukuanDate'">
                  {{ formatDate(record.shoukuanDate) }}
                </template>
                <template v-else-if="column.key === 'yingshouDate'">
                  {{ formatDate(record.yingshouDate) }}
                </template>
                <template v-else-if="column.key === 'kaipiaoDate'">
                  {{ formatDate(record.kaipiaoDate) }}
                </template>
                <template v-else-if="column.key === 'payStatus'">
                  <a-tag :color="getPaymentStatusColor(record.payStatus)">
                    {{ getPaymentStatusText(record.payStatus) }}
                  </a-tag>
                </template>
                <template v-else-if="column.key === 'action'">
                  <a-space>
                    <a-button type="link" size="small" @click="handleViewPayment(record)"> 查看 </a-button>
                    <a-button type="link" size="small" @click="handleEditPayment(record)"> 编辑 </a-button>
                  </a-space>
                </template>
              </template>
            </a-table>
          </a-card>
        </div>
      </Spin>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, onMounted, onActivated, inject, watch } from 'vue';
  import { Spin } from 'ant-design-vue';
  import { PlusOutlined, ReloadOutlined } from '@ant-design/icons-vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { getContractMoneyListByContractId, type ContractMoneyModel } from '/@/api/project/contractMoney';
  import { formatToDate } from '/@/utils/dateUtil';

  // 格式化日期函数 - 处理时间戳和日期字符串
  const formatDate = (dateValue: any) => {
    if (!dateValue) return '-';

    try {
      // 如果是数字（时间戳）
      if (typeof dateValue === 'number') {
        // 检查是否是毫秒时间戳（13位）还是秒时间戳（10位）
        const timestamp = dateValue.toString().length === 10 ? dateValue * 1000 : dateValue;
        return formatToDate(new Date(timestamp));
      }

      // 如果是字符串
      if (typeof dateValue === 'string') {
        // 尝试解析日期字符串
        const date = new Date(dateValue);
        if (!isNaN(date.getTime())) {
          return formatToDate(date);
        }
      }

      // 如果是Date对象
      if (dateValue instanceof Date) {
        return formatToDate(dateValue);
      }

      return '-';
    } catch (error) {
      console.warn('日期格式化失败:', dateValue, error);
      return '-';
    }
  };

  // 从父组件注入项目和合同信息
  const projectId = inject('projectId', ref(''));
  const contractId = inject('contractId', ref(''));
  const contractInfo = inject('contractInfo', ref(null));

  const { createMessage } = useMessage();

  const loading = ref(false);
  const paymentList = ref<ContractMoneyModel[]>([]);
  const paymentPagination = ref({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
  });

  // 收款记录表格列
  const paymentColumns = [
    { title: '收款条件', dataIndex: 'fktj', key: 'fktj', width: 150 },
    { title: '收款比例', dataIndex: 'ratio', key: 'ratio', width: 100 },
    { title: '收款金额', dataIndex: 'cmMoney', key: 'cmMoney', width: 120, customRender: ({ text }) => `¥${text?.toLocaleString() || 0}` },
    { title: '应收日期', dataIndex: 'yingshouDate', key: 'yingshouDate', width: 120, customRender: ({ text }) => formatToDate(text) },
    { title: '收款日期', dataIndex: 'shoukuanDate', key: 'shoukuanDate', width: 120, customRender: ({ text }) => formatToDate(text) },
    { title: '开票日期', dataIndex: 'kaipiaoDate', key: 'kaipiaoDate', width: 120, customRender: ({ text }) => formatToDate(text) },
    { title: '收款状态', dataIndex: 'payStatus', key: 'payStatus', width: 100, customRender: ({ text }) => getPaymentStatusText(text) },
    { title: '负责人', dataIndex: 'ownName', key: 'ownName', width: 100 },
    { title: '备注', dataIndex: 'note', key: 'note', ellipsis: true },
    { title: '操作', key: 'action', width: 120, fixed: 'right' },
  ];

  // 计算统计数据 - 使用响应式计算属性确保实时更新
  const contractAmount = computed(() => {
    const contract = contractInfo.value as any;
    return contract?.amount || 0;
  });

  const getContractAmount = () => contractAmount.value;

  // 响应式计算已收金额
  const receivedAmount = computed(() => {
    const receivedItems = paymentList.value.filter(item => {
      // 兼容数字和字符串类型的状态值
      return String(item.payStatus) === '1'; // 已收款状态
    });
    const total = receivedItems.reduce((total, item) => total + (item.cmMoney || 0), 0);
    return total;
  });

  const getReceivedAmount = () => receivedAmount.value;

  // 响应式计算未收金额
  const unreceivedAmount = computed(() => {
    return contractAmount.value - receivedAmount.value;
  });

  // 响应式计算收款进度
  const paymentProgress = computed(() => {
    const total = contractAmount.value;
    if (!total) return 0;
    const received = receivedAmount.value;
    const progress = (received / total) * 100;
    return Math.min(progress, 100);
  });

  // 响应式计算进度颜色
  const progressColor = computed(() => {
    const progress = paymentProgress.value;
    if (progress >= 100) return '#52c41a';
    if (progress >= 80) return '#1890ff';
    if (progress >= 50) return '#faad14';
    return '#f5222d';
  });

  // 兼容性函数（保持向后兼容）
  const getUnreceivedAmount = () => unreceivedAmount.value;
  const getPaymentProgress = () => paymentProgress.value;
  const getProgressColor = () => progressColor.value;

  // 监听统计数据变化，输出调试信息
  watch(
    [contractAmount, receivedAmount, paymentProgress],
    ([contract, received, progress]) => {
      console.log('📊 [统计卡片] 数据更新:', {
        合同金额: contract,
        已收金额: received,
        未收金额: contract - received,
        收款进度: progress.toFixed(1) + '%',
      });
    },
    { immediate: false },
  );

  // 获取收款状态文本
  const getPaymentStatusText = (status: string | number) => {
    const textMap: Record<string, string> = {
      '0': '未收款',
      '1': '已收款',
      '2': '部分收款',
      '3': '逾期',
    };
    return textMap[String(status)] || '未知';
  };

  // 获取收款状态颜色
  const getPaymentStatusColor = (status: string | number) => {
    const colorMap: Record<string, string> = {
      '0': 'orange',
      '1': 'green',
      '2': 'blue',
      '3': 'red',
    };
    return colorMap[String(status)] || 'default';
  };

  // 加载收款记录
  const loadPaymentList = async () => {
    if (!contractId.value) {
      console.log('⚠️ 合同ID为空，无法加载收款记录');
      paymentList.value = [];
      paymentPagination.value.total = 0;
      return;
    }

    loading.value = true;
    try {
      console.log('🚀 开始加载合同收款记录');
      console.log('🏗️ 当前项目ID:', projectId.value);
      console.log('🆔 当前合同ID:', contractId.value);
      console.log('📄 当前合同信息:', contractInfo.value);

      // 调用真实API获取合同的收款记录
      const response = await getContractMoneyListByContractId(contractId.value);
      console.log('🔍 API响应数据:', response);
      console.log('🔍 响应数据类型:', typeof response);
      console.log('🔍 是否为数组:', Array.isArray(response));

      // 处理API响应数据
      let dataList: ContractMoneyModel[] = [];
      if (response) {
        // 检查是否是ActionResult格式 {code: 200, msg: "Success", data: [...]}
        const responseAny = response as any;
        if (responseAny.code === 200 && responseAny.data) {
          dataList = Array.isArray(responseAny.data) ? responseAny.data : [];
          console.log('📦 从ActionResult中提取数据:', dataList.length, '条记录');
        }
        // 检查是否直接是数组格式
        else if (Array.isArray(response)) {
          dataList = response;
          console.log('📦 直接使用数组数据:', dataList.length, '条记录');
        }
      }

      if (dataList.length > 0) {
        paymentList.value = dataList;
        paymentPagination.value.total = dataList.length;
        console.log('✅ 收款记录加载成功:', paymentList.value.length, '条记录');
        console.log('📊 统计数据更新 - 已收金额:', getReceivedAmount(), '收款进度:', getPaymentProgress().toFixed(1) + '%');
      } else {
        paymentList.value = [];
        paymentPagination.value.total = 0;
        console.log('⚠️ 该合同暂无收款记录');
      }
    } catch (error) {
      console.error('加载收款记录失败:', error);
      createMessage.error('加载收款记录失败');
      paymentList.value = [];
      paymentPagination.value.total = 0;
    } finally {
      loading.value = false;
    }
  };

  // 初始化数据加载
  const loadContractInfo = () => {
    console.log('📋 ===== 初始化加载合同信息 =====');
    console.log('🏗️ 当前项目ID:', projectId.value);
    console.log('🆔 当前合同ID:', contractId.value);
    console.log('📄 合同信息:', contractInfo.value);

    // 检查合同信息是否与项目匹配
    if (contractInfo.value) {
      const contract = contractInfo.value as any;
      console.log('🔍 合同所属项目ID:', contract.projBaseId || contract.projectId);
      console.log('🔍 项目ID匹配检查:', contract.projBaseId === projectId.value || contract.projectId === projectId.value);
    }

    // 如果有合同信息，加载收款记录
    if (contractId.value) {
      console.log('✅ 合同ID存在，开始加载收款记录');
      loadPaymentList();
    } else {
      console.log('⚠️ 没有合同ID，清空收款记录');
      paymentList.value = [];
      paymentPagination.value.total = 0;
    }
    console.log('📋 ===== 合同信息加载完成 =====');
  };

  // 新增收款
  const handleAdd = () => {
    createMessage.info('新增收款记录功能');
  };

  // 查看收款记录
  const handleViewPayment = (record: any) => {
    createMessage.info(`查看收款记录: ${record.id}`);
  };

  // 编辑收款记录
  const handleEditPayment = (record: any) => {
    createMessage.info(`编辑收款记录: ${record.id}`);
  };

  // 刷新数据
  const handleRefresh = () => {
    console.log('🔄 手动刷新数据');
    loadContractInfo();
  };

  // 强制重新加载收款记录（用于外部调用）
  const reloadPaymentData = () => {
    console.log('🔄 强制重新加载收款记录');
    loadPaymentList();
  };

  // 暴露给父组件的方法
  defineExpose({
    reloadPaymentData,
  });

  // 监听项目ID变化 - 最高优先级的数据刷新触发器
  watch(
    projectId,
    async (newProjectId, oldProjectId) => {
      console.log('🏗️ 项目ID发生变化:', oldProjectId, '->', newProjectId);
      if (newProjectId && newProjectId !== oldProjectId) {
        console.log('🔄 项目切换，清空所有数据并等待合同信息更新...');

        // 立即清空当前数据和缓存
        paymentList.value = [];
        paymentPagination.value.total = 0;
        loading.value = false;

        // 强制触发统计数据重新计算（清除可能的缓存）
        console.log('🧹 清空数据后强制重新计算统计数据');
        console.log('💰 合同金额:', getContractAmount());
        console.log('💰 已收金额:', getReceivedAmount());
        console.log('💰 未收金额:', getUnreceivedAmount());
        console.log('📊 收款进度:', getPaymentProgress());

        // 等待一小段时间，确保父组件的合同信息已经更新
        await new Promise(resolve => setTimeout(resolve, 200));

        // 重新初始化数据加载
        console.log('⏰ 延迟后重新加载合同信息...');
        console.log('🔍 延迟后检查 - 项目ID:', projectId.value, '合同ID:', contractId.value);

        // 如果延迟后合同ID还是空的，说明新项目可能没有合同
        if (!contractId.value) {
          console.log('⚠️ 延迟后合同ID仍为空，新项目可能没有合同');
          paymentList.value = [];
          paymentPagination.value.total = 0;
        } else {
          loadContractInfo();
        }
      }
    },
    { immediate: false },
  );

  // 监听合同ID变化，自动重新加载数据
  watch(
    contractId,
    (newContractId, oldContractId) => {
      console.log('📋 合同ID发生变化:', oldContractId, '->', newContractId);
      if (newContractId && newContractId !== oldContractId) {
        console.log('📡 重新加载收款记录...');
        loadPaymentList();
      }
    },
    { immediate: false },
  );

  // 监听合同信息变化 - 处理项目切换后的合同信息更新
  watch(
    contractInfo,
    (newContractInfo, oldContractInfo) => {
      console.log('📄 合同信息发生变化:', oldContractInfo, '->', newContractInfo);

      // 检查是否是项目切换导致的合同信息变化
      const isContractChanged =
        (!oldContractInfo && newContractInfo) ||
        (oldContractInfo && newContractInfo && (oldContractInfo as any)?.id !== (newContractInfo as any)?.id) ||
        (oldContractInfo && !newContractInfo);

      if (isContractChanged) {
        console.log('🔄 合同信息已更新，重新加载收款记录...');
        if (newContractInfo && contractId.value) {
          loadPaymentList();
        } else {
          // 没有合同信息，清空收款记录
          paymentList.value = [];
          paymentPagination.value.total = 0;
          console.log('⚠️ 新项目没有合同，清空收款记录');
        }
      }
    },
    { immediate: false, deep: true },
  );

  // 组件激活时（keep-alive缓存组件重新激活）
  onActivated(async () => {
    console.log('🔄 组件激活，检查是否需要刷新数据');
    console.log('🏗️ 当前项目ID:', projectId.value);
    console.log('🆔 当前合同ID:', contractId.value);

    // 等待一小段时间，确保父组件数据已更新
    await new Promise(resolve => setTimeout(resolve, 100));

    console.log('🔄 延迟后再次检查:');
    console.log('🏗️ 项目ID:', projectId.value);
    console.log('🆔 合同ID:', contractId.value);
    console.log('📄 合同信息:', contractInfo.value);

    // 组件激活时重新加载数据，确保数据是最新的
    loadContractInfo();
  });

  onMounted(() => {
    console.log('🚀 组件挂载，初始化加载数据');
    loadContractInfo();
  });
</script>

<style lang="less" scoped>
  .contract-payment-management {
    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding: 16px;
      background: #fff;
      border-radius: 6px;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    }

    .page-content {
      .payment-summary {
        .ant-card {
          text-align: center;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
        }
      }

      .payment-list {
        .ant-card {
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
        }
      }
    }
  }
</style>
