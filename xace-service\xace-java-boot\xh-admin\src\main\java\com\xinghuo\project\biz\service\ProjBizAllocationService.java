package com.xinghuo.project.biz.service;

import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.project.biz.entity.ProjBizAllocationEntity;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 项目业务分部分配 服务类接口
 * 
 * <AUTHOR>
 * @version V3.0
 * @copyright 易趋集团
 * @date 2024-01-01
 */
public interface ProjBizAllocationService extends BaseService<ProjBizAllocationEntity> {

    /**
     * 根据业务ID和业务类型查询分配信息
     */
    ProjBizAllocationEntity getByBusinessIdAndType(String businessId, Integer businessType);

    /**
     * 根据商机ID查询分配信息
     */
    ProjBizAllocationEntity getByOpportunityId(String opportunityId);

    /**
     * 根据合同ID查询分配信息
     */
    ProjBizAllocationEntity getByContractId(String contractId);

    /**
     * 根据项目基础ID查询所有相关分配
     */
    List<ProjBizAllocationEntity> getByProjBaseId(String projBaseId);

    /**
     * 保存或更新分配信息
     */
    boolean saveOrUpdateAllocation(ProjBizAllocationEntity allocation);

    /**
     * 创建商机分配记录
     */
    boolean createOpportunityAllocation(String opportunityId, String projBaseId, 
                                       BigDecimal ybAmount, BigDecimal ebAmount, 
                                       BigDecimal jfAmount, BigDecimal otherAmount,
                                       BigDecimal outYbAmount, BigDecimal outEbAmount,
                                       BigDecimal outJfAmount, BigDecimal outOtherAmount);

    /**
     * 创建合同分配记录
     */
    boolean createContractAllocation(String contractId, String opportunityId, String projBaseId,
                                   BigDecimal ybAmount, BigDecimal ebAmount, 
                                   BigDecimal jfAmount, BigDecimal otherAmount,
                                   BigDecimal outYbAmount, BigDecimal outEbAmount,
                                   BigDecimal outJfAmount, BigDecimal outOtherAmount);

    /**
     * 创建收款分配记录
     */
    boolean createReceivableAllocation(String receivableId, String contractId,
                                     BigDecimal ybAmount, BigDecimal ebAmount,
                                     BigDecimal jfAmount, BigDecimal otherAmount);

    /**
     * 创建付款分配记录
     */
    boolean createPaymentAllocation(String paymentId, String contractId,
                                  BigDecimal outYbAmount, BigDecimal outEbAmount,
                                  BigDecimal outJfAmount, BigDecimal outOtherAmount);

    /**
     * 更新分部分配金额
     */
    boolean updateDeptAllocation(String allocationId, String deptCode, 
                               BigDecimal amount, BigDecimal outAmount);

    /**
     * 查询项目全链路分部分配汇总
     */
    List<Map<String, Object>> getAllocationSummaryByProjId(String projBaseId);

    /**
     * 查询分部营收汇总（按分部统计）
     */
    List<Map<String, Object>> getDeptSummaryByBusinessType(Integer businessType);

    /**
     * 查询交付部门的分配汇总
     */
    List<Map<String, Object>> getJfDeptSummary();

    /**
     * 查询指定分部的总分配金额
     */
    Map<String, BigDecimal> getDeptTotalAmount(String deptCode, Integer businessType);

    /**
     * 检查业务是否已存在分配记录
     */
    boolean existsByBusinessIdAndType(String businessId, Integer businessType);

    /**
     * 批量查询多个业务的分配信息
     */
    List<ProjBizAllocationEntity> getByBusinessIds(List<String> businessIds, Integer businessType);

    /**
     * 复制商机分配到合同
     */
    boolean copyOpportunityToContract(String opportunityId, String contractId);

    /**
     * 验证分配数据的一致性
     */
    boolean validateAllocationConsistency(String businessId, Integer businessType);

    /**
     * 删除指定业务的分配记录
     */
    boolean deleteByBusinessIdAndType(String businessId, Integer businessType);

    /**
     * 根据分部编码和业务类型统计金额
     */
    BigDecimal sumAmountByDeptAndType(String deptCode, Integer businessType, boolean isOutAmount);

    /**
     * 获取交付部门在所有业务类型中的分配情况
     */
    List<ProjBizAllocationEntity.DeptAllocationInfo> getJfAllocationAcrossAllTypes();

    /**
     * 批量更新分配记录
     */
    boolean batchUpdateAllocations(List<ProjBizAllocationEntity> allocations);

    /**
     * 重新计算并同步分配金额到主业务表
     */
    boolean syncAllocationToMainBusiness(String businessId, Integer businessType);
}