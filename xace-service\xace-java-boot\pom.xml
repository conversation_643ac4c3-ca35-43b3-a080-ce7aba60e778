<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.xinghuo.xace</groupId>
        <artifactId>xh-common</artifactId>
        <version>2.1.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>xh-java-boot</artifactId>
    <packaging>pom</packaging>

    <modules>
        <module>xh-admin</module>
        <module>xh-oauth</module>
        <module>xh-file</module>
        <module>xh-system</module>
        <module>xh-visualdev</module>
        <module>xh-workflow-engine</module>
        <module>xh-ext-demo</module>
        <module>xh-ext-magicapi</module>
        <module>xh-ext-visualdata</module>
        <module>xh-ext-app</module>
<!---->
        <module>xh-ext-scheduletask</module>

    </modules>

    <properties>
    </properties>

    <dependencyManagement>
        <dependencies>

        </dependencies>
    </dependencyManagement>
<build>
    <plugins>
    <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
            <source>${maven.compiler.source}</source> <!-- 或者更高的版本 -->
            <target>${maven.compiler.target}</target> <!-- 或者更高的版本 -->
            <compilerArgs>
                <arg>-parameters</arg>
            </compilerArgs>
        </configuration>
    </plugin>
    </plugins>
</build>
</project>
