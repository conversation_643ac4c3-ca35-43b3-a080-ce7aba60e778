package com.xinghuo.project.biz.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.biz.entity.BizCustomerEntity;
import com.xinghuo.project.biz.entity.CustomerContactEntity;
import com.xinghuo.project.biz.model.CustomerLinkmanPagination;
import com.xinghuo.project.biz.model.customerContact.CustomerContactForm;
import com.xinghuo.project.biz.model.customerContact.CustomerContactVO;
import com.xinghuo.project.biz.service.BizCustomerService;
import com.xinghuo.project.biz.service.CustomerContactService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 客户联系人管理控制器
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Tag(name = "客户联系人管理", description = "客户联系人管理相关接口")
@RestController
@RequestMapping("/api/project/biz/customer/linkman")
public class CustomerLinkmanController {

    @Resource
    private CustomerContactService customerContactService;

    @Resource
    private BizCustomerService bizCustomerService;

    /**
     * 获取客户联系人列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取客户联系人列表")
    public ActionResult<PageListVO<CustomerContactVO>> list(@RequestBody CustomerLinkmanPagination pagination) {
        List<CustomerContactEntity> list = customerContactService.getList(pagination);

        // 转换为VO对象
        List<CustomerContactVO> listVOs = BeanCopierUtils.copyList(list, CustomerContactVO.class);

        // 批量获取客户单位名称
        List<String> customerIds = listVOs.stream()
                .map(CustomerContactVO::getCuId)
                .filter(StrXhUtil::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());

        if (!customerIds.isEmpty()) {
            // 批量查询客户信息
            Map<String, String> customerNameMap = customerIds.stream()
                    .collect(Collectors.toMap(
                            id -> id,
                            id -> {
                                try {
                                    BizCustomerEntity customer = bizCustomerService.getInfo(id);
                                    return customer != null ? customer.getName() : "";
                                } catch (Exception e) {
                                    log.warn("获取客户单位名称失败，客户ID: {}", id, e);
                                    return "";
                                }
                            }
                    ));

            // 设置客户单位名称和状态名称
            for (CustomerContactVO vo : listVOs) {
                if (StrXhUtil.isNotEmpty(vo.getCuId())) {
                    vo.setCustomerName(customerNameMap.get(vo.getCuId()));
                }
                // 设置状态名称
                vo.setStatusName(vo.getStatus() != null && vo.getStatus() == 1 ? "有效" : "无效");
            }
        }

        PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
        return ActionResult.page(listVOs, page);
    }

    /**
     * 根据客户ID获取联系人列表
     */
    @GetMapping("/getListByCustomerId/{customerId}")
    @Operation(summary = "根据客户ID获取联系人列表")
    public ActionResult<List<CustomerContactVO>> getListByCustomerId(
            @Parameter(description = "客户ID") @PathVariable String customerId) {
        List<CustomerContactEntity> list = customerContactService.getListByCustomerId(customerId);

        // 转换为VO对象
        List<CustomerContactVO> listVOs = BeanCopierUtils.copyList(list, CustomerContactVO.class);

        // 获取客户单位名称
        if (StrXhUtil.isNotEmpty(customerId)) {
            try {
                BizCustomerEntity customer = bizCustomerService.getInfo(customerId);
                String customerName = customer != null ? customer.getName() : "";

                // 设置客户单位名称和状态名称
                for (CustomerContactVO vo : listVOs) {
                    vo.setCustomerName(customerName);
                    vo.setStatusName(vo.getStatus() != null && vo.getStatus() == 1 ? "有效" : "无效");
                }
            } catch (Exception e) {
                log.warn("获取客户单位名称失败，客户ID: {}", customerId, e);
            }
        }

        return ActionResult.success(listVOs);
    }

    /**
     * 获取客户联系人详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取客户联系人详情")
    public ActionResult<CustomerContactVO> getInfo(
            @Parameter(description = "联系人ID") @PathVariable String id) {
        CustomerContactEntity entity = customerContactService.getInfo(id);

        // 转换为VO对象
        CustomerContactVO vo = BeanCopierUtils.copy(entity, CustomerContactVO.class);

        // 获取客户单位名称
        if (StrXhUtil.isNotEmpty(vo.getCuId())) {
            try {
                BizCustomerEntity customer = bizCustomerService.getInfo(vo.getCuId());
                vo.setCustomerName(customer != null ? customer.getName() : "");
            } catch (Exception e) {
                log.warn("获取客户单位名称失败，客户ID: {}", vo.getCuId(), e);
            }
        }

        // 设置状态名称
        vo.setStatusName(vo.getStatus() != null && vo.getStatus() == 1 ? "有效" : "无效");

        return ActionResult.success(vo);
    }

    /**
     * 创建客户联系人
     */
    @PostMapping("")
    @Operation(summary = "创建客户联系人")
    public ActionResult<String> create(@RequestBody @Valid CustomerContactForm form) {
        // 转换为实体对象
        CustomerContactEntity entity = BeanCopierUtils.copy(form, CustomerContactEntity.class);
        String id = customerContactService.create(entity);
        return ActionResult.success("创建成功", id);
    }

    /**
     * 更新客户联系人
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新客户联系人")
    public ActionResult<String> update(
            @Parameter(description = "联系人ID") @PathVariable String id,
            @RequestBody @Valid CustomerContactForm form) {
        // 转换为实体对象
        CustomerContactEntity entity = BeanCopierUtils.copy(form, CustomerContactEntity.class);
        customerContactService.update(id, entity);
        return ActionResult.success("更新成功");
    }

    /**
     * 删除客户联系人
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除客户联系人")
    public ActionResult<String> delete(
            @Parameter(description = "联系人ID") @PathVariable String id) {
        customerContactService.delete(id);
        return ActionResult.success("删除成功");
    }

    /**
     * 更新客户联系人状态
     */
    @PutMapping("/updateStatus/{id}")
    @Operation(summary = "更新客户联系人状态")
    public ActionResult<String> updateStatus(
            @Parameter(description = "联系人ID") @PathVariable String id,
            @RequestParam Integer status) {
        customerContactService.updateStatus(id, status);
        return ActionResult.success("状态更新成功");
    }

    /**
     * 获取客户联系人选择列表
     */
    @GetMapping("/getSelectList")
    @Operation(summary = "获取客户联系人选择列表")
    public ActionResult<List<CustomerContactEntity>> getSelectList(
            @RequestParam(required = false) String customerId,
            @RequestParam(required = false) String keyword) {
        List<CustomerContactEntity> list = customerContactService.getSelectList(customerId, keyword);
        return ActionResult.success(list);
    }
}
