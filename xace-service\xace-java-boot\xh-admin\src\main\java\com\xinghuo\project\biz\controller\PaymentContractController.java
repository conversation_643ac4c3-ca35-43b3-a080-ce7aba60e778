package com.xinghuo.project.biz.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.constant.MsgCode;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.common.util.json.JsonXhUtil;
import com.xinghuo.project.biz.entity.PaymentContractEntity;
import com.xinghuo.project.biz.model.paymentContract.PaymentContractForm;
import com.xinghuo.project.biz.model.paymentContract.PaymentContractPagination;
import com.xinghuo.project.biz.model.paymentContract.PaymentContractVO;
import com.xinghuo.project.biz.service.PaymentContractService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 付款合同管理控制器
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Tag(name = "付款合同管理", description = "付款合同管理相关接口")
@RestController
@RequestMapping("/api/project/biz/paymentContract")
public class PaymentContractController {

    @Resource
    private PaymentContractService paymentContractService;


    /**
     * 获取采购合同列表
     *
     * @param pagination 分页查询参数
     * @return 采购合同列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取采购合同列表")
    public ActionResult list(@RequestBody PaymentContractPagination pagination) {
        List<PaymentContractEntity> list = paymentContractService.getList(pagination);
        List<PaymentContractVO> listVOs = BeanCopierUtils.copyList(list, PaymentContractVO.class);

        // 填充关联信息
        paymentContractService.fillRelatedInfo(listVOs);

        PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
        return ActionResult.page(listVOs,page);
    }

    /**
     * 根据收款合同ID获取采购合同列表
     *
     * @param contractId 收款合同ID
     * @return 采购合同列表
     */
    @GetMapping("/contract/{contractId}")
    @Operation(summary = "根据收款合同ID获取采购合同列表")
    @Parameters({
            @Parameter(name = "contractId", description = "收款合同ID", required = true),
    })
    public ActionResult<List<PaymentContractVO>> listByContractId(@PathVariable("contractId") String contractId) {
        List<PaymentContractEntity> list = paymentContractService.getListByContractId(contractId);
        List<PaymentContractVO> listVOs = BeanCopierUtils.copyList(list, PaymentContractVO.class);

        // 填充关联信息
        paymentContractService.fillRelatedInfo(listVOs);

        return ActionResult.success(listVOs);
    }

    /**
     * 根据供应商ID获取采购合同列表
     *
     * @param supplierId 供应商ID
     * @return 采购合同列表
     */
    @GetMapping("/supplier/{supplierId}")
    @Operation(summary = "根据供应商ID获取采购合同列表")
    @Parameters({
            @Parameter(name = "supplierId", description = "供应商ID", required = true),
    })
    public ActionResult<List<PaymentContractVO>> listBySupplierId(@PathVariable("supplierId") String supplierId) {
        List<PaymentContractEntity> list = paymentContractService.getListBySupplierId(supplierId);
        List<PaymentContractVO> listVOs = JsonXhUtil.jsonToList(list, PaymentContractVO.class);
        return ActionResult.success(listVOs);
    }

    /**
     * 获取采购合同详情
     *
     * @param id 采购合同ID
     * @return 采购合同详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取采购合同详情")
    @Parameters({
            @Parameter(name = "id", description = "采购合同ID", required = true),
    })
    public ActionResult<PaymentContractVO> info(@PathVariable("id") String id) {
        PaymentContractEntity entity = paymentContractService.getInfo(id);
        PaymentContractVO vo = BeanCopierUtils.copy(entity, PaymentContractVO.class);
        return ActionResult.success(vo);
    }

    /**
     * 创建采购合同
     *
     * @param paycontractForm 采购合同表单
     * @return 操作结果
     */
    @PostMapping
    @Operation(summary = "创建采购合同")
    @Parameters({
            @Parameter(name = "paycontractForm", description = "采购合同表单", required = true),
    })
    public ActionResult create(@RequestBody @Valid PaymentContractForm paycontractForm) {
        // 检查采购合同编号是否已存在
        if (paymentContractService.isExistByCNo(paycontractForm.getCno(), null)) {
            return ActionResult.fail("采购合同编号已存在");
        }

        PaymentContractEntity entity = JsonXhUtil.jsonDeepCopy(paycontractForm, PaymentContractEntity.class);
        paymentContractService.create(entity);
        return ActionResult.success(MsgCode.SU001.get());
    }

    /**
     * 更新采购合同
     *
     * @param id             采购合同ID
     * @param paycontractForm 采购合同表单
     * @return 操作结果
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新采购合同")
    @Parameters({
            @Parameter(name = "id", description = "采购合同ID", required = true),
            @Parameter(name = "paycontractForm", description = "采购合同表单", required = true),
    })
    public ActionResult update(@PathVariable("id") String id, @RequestBody @Valid PaymentContractForm paycontractForm) {
        // 检查采购合同编号是否已存在
        if (paymentContractService.isExistByCNo(paycontractForm.getCno(), id)) {
            return ActionResult.fail("采购合同编号已存在");
        }

        PaymentContractEntity entity = JsonXhUtil.jsonDeepCopy(paycontractForm, PaymentContractEntity.class);
        paymentContractService.update(id, entity);
        return ActionResult.success(MsgCode.SU002.get());
    }

    /**
     * 删除采购合同
     *
     * @param id 采购合同ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除采购合同")
    @Parameters({
            @Parameter(name = "id", description = "采购合同ID", required = true),
    })
    public ActionResult delete(@PathVariable("id") String id) {
        // 检查采购合同是否可以删除
        if (!paymentContractService.canDelete(id)) {
            return ActionResult.fail("该采购合同已有付款记录，无法删除");
        }

        paymentContractService.delete(id);
        return ActionResult.success(MsgCode.SU003.get());
    }

    /**
     * 更新采购合同状态
     *
     * @param id     采购合同ID
     * @param status 采购合同状态
     * @return 操作结果
     */
    @PutMapping("/{id}/status/{status}")
    @Operation(summary = "更新采购合同状态")
    @Parameters({
            @Parameter(name = "id", description = "采购合同ID", required = true),
            @Parameter(name = "status", description = "采购合同状态", required = true),
    })
    public ActionResult updateStatus(@PathVariable("id") String id, @PathVariable("status") String status) {
        paymentContractService.updateStatus(id, status);
        return ActionResult.success(MsgCode.SU002.get());
    }

    /**
     * 签订采购合同
     *
     * @param id       采购合同ID
     * @param cNo      采购合同编号
     * @param signDate 签订日期
     * @return 操作结果
     */
    @PutMapping("/{id}/sign")
    @Operation(summary = "签订采购合同")
    @Parameters({
            @Parameter(name = "id", description = "采购合同ID", required = true),
            @Parameter(name = "cNo", description = "采购合同编号", required = true),
            @Parameter(name = "signDate", description = "签订日期", required = true),
    })
    public ActionResult sign(
            @PathVariable("id") String id,
            @RequestParam("cNo") String cNo,
            @RequestParam("signDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date signDate) {
        try {
            paymentContractService.sign(id, cNo, signDate);
            return ActionResult.success(MsgCode.SU002.get());
        } catch (Exception e) {
            return ActionResult.fail(e.getMessage());
        }
    }

    /**
     * 检查采购合同编号是否已存在
     *
     * @param cNo 采购合同编号
     * @param id  采购合同ID（更新时使用，新增时为null）
     * @return 是否存在
     */
    @GetMapping("/check-cno")
    @Operation(summary = "检查采购合同编号是否已存在")
    @Parameters({
            @Parameter(name = "cNo", description = "采购合同编号", required = true),
            @Parameter(name = "id", description = "采购合同ID（更新时使用，新增时为null）"),
    })
    public ActionResult<Boolean> checkCNo(@RequestParam("cNo") String cNo, @RequestParam(value = "id", required = false) String id) {
        boolean exists = paymentContractService.isExistByCNo(cNo, id);
        return ActionResult.success(exists);
    }

    /**
     * 获取采购合同选择器列表
     *
     * @param keyword 关键字搜索
     * @return 采购合同选择器列表
     */
    @GetMapping("/selector")
    @Operation(summary = "获取采购合同选择器列表")
    public ActionResult<?> selector(@RequestParam(value = "keyword", required = false) String keyword) {
        PaymentContractPagination pagination = new PaymentContractPagination();
        pagination.setKeyword(keyword);
        pagination.setPageSize(3000); // 限制返回数量

        List<PaymentContractEntity> list = paymentContractService.getList(pagination);
        List<Map<String, Object>> selectorList = new ArrayList<>();

        for (PaymentContractEntity paycontract : list) {
            Map<String, Object> option = new HashMap<>();
            option.put("id", paycontract.getId());
            option.put("fullName", paycontract.getCno() + " - " + paycontract.getName());
            option.put("cNo", paycontract.getCno());
            option.put("name", paycontract.getName());
            selectorList.add(option);
        }

        return ActionResult.success(selectorList);
    }
}
