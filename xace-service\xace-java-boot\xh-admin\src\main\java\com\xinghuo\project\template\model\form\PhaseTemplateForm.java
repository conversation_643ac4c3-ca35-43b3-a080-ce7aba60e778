package com.xinghuo.project.template.model.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Min;
import lombok.Data;

/**
 * 阶段模板表单对象
 * 
 * <AUTHOR>
 * @date 2025-07-07
 */
@Data
@Schema(description = "阶段模板表单对象")
public class PhaseTemplateForm {

    /**
     * 阶段编码
     */
//    @NotBlank(message = "阶段编码不能为空")
//    @Size(max = 50, message = "阶段编码长度不能超过50个字符")
    @Schema(description = "阶段编码")
    private String code;

    /**
     * 阶段名称
     */
    @NotBlank(message = "阶段名称不能为空")
    @Size(max = 100, message = "阶段名称长度不能超过100个字符")
    @Schema(description = "阶段名称")
    private String name;

    /**
     * 阶段描述
     */
    @Size(max = 500, message = "阶段描述长度不能超过500个字符")
    @Schema(description = "阶段描述")
    private String description;

    /**
     * 标准工期(天)
     */
    @Min(value = 1, message = "标准工期必须大于0")
    @Schema(description = "标准工期(天)")
    private Integer stdDuration;

    /**
     * 默认阶段完成审批流程ID
     */
    @Size(max = 50, message = "审批流程ID长度不能超过50个字符")
    @Schema(description = "默认阶段完成审批流程ID")
    private String defaultApprovalId;

    /**
     * 默认检查单模板ID
     */
    @Size(max = 50, message = "检查单模板ID长度不能超过50个字符")
    @Schema(description = "默认检查单模板ID")
    private String defaultChecklistId;

    /**
     * 状态 (1:启用, 0:禁用)
     */
    @NotNull(message = "状态不能为空")
    @Schema(description = "状态 (1:启用, 0:禁用)")
    private Integer status;
}