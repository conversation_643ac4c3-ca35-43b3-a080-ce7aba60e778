<template>
  <div class="project-setting p-4">
    <!-- 页面标题 -->
    <div class="page-header mb-4">
      <h2 class="text-xl font-semibold mb-2">项目设置</h2>
      <p class="text-gray-600">配置项目的各项参数和系统设置</p>
    </div>

    <a-spin :spinning="loading">
      <div class="setting-content">
        <!-- 基础设置 -->
        <a-card title="基础设置" class="mb-6">
          <a-form :model="basicSettings" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item label="项目可见性">
                  <a-radio-group v-model:value="basicSettings.visibility">
                    <a-radio :value="1">公开</a-radio>
                    <a-radio :value="2">内部</a-radio>
                    <a-radio :value="3">私有</a-radio>
                  </a-radio-group>
                  <div class="text-xs text-gray-500 mt-1"> 公开：所有人可见 | 内部：组织内可见 | 私有：仅团队成员可见 </div>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="项目状态">
                  <a-select v-model:value="basicSettings.status" style="width: 100%">
                    <a-select-option value="1">活跃</a-select-option>
                    <a-select-option value="2">暂停</a-select-option>
                    <a-select-option value="3">归档</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item label="时区设置">
                  <a-select v-model:value="basicSettings.timezone" style="width: 100%">
                    <a-select-option value="Asia/Shanghai">Asia/Shanghai (UTC+8)</a-select-option>
                    <a-select-option value="America/New_York">America/New_York (UTC-5)</a-select-option>
                    <a-select-option value="Europe/London">Europe/London (UTC+0)</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="工作日设置">
                  <a-checkbox-group v-model:value="basicSettings.workdays" :options="workdayOptions" />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item label="自动归档">
                  <a-switch v-model:checked="basicSettings.autoArchive" />
                  <span class="ml-2 text-sm text-gray-500">项目完成后自动归档</span>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="数据备份">
                  <a-switch v-model:checked="basicSettings.dataBackup" />
                  <span class="ml-2 text-sm text-gray-500">定期备份项目数据</span>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-card>

        <!-- 通知设置 -->
        <a-card title="通知设置" class="mb-6">
          <a-form :model="notificationSettings" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item label="邮件通知">
                  <a-switch v-model:checked="notificationSettings.email" />
                  <span class="ml-2 text-sm text-gray-500">启用邮件通知功能</span>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="短信通知">
                  <a-switch v-model:checked="notificationSettings.sms" />
                  <span class="ml-2 text-sm text-gray-500">启用短信通知功能</span>
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item label="浏览器推送">
                  <a-switch v-model:checked="notificationSettings.browser" />
                  <span class="ml-2 text-sm text-gray-500">启用浏览器推送通知</span>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="移动端推送">
                  <a-switch v-model:checked="notificationSettings.mobile" />
                  <span class="ml-2 text-sm text-gray-500">启用移动端推送通知</span>
                </a-form-item>
              </a-col>
            </a-row>

            <a-divider>通知事件配置</a-divider>

            <div class="notification-events">
              <div v-for="(event, index) in notificationEvents" :key="index" class="event-item mb-4">
                <div class="event-header flex items-center justify-between mb-2">
                  <span class="font-medium">{{ event.name }}</span>
                  <a-switch v-model:checked="event.enabled" />
                </div>
                <div v-if="event.enabled" class="event-channels ml-4">
                  <a-checkbox-group v-model:value="event.channels" :options="channelOptions" />
                </div>
              </div>
            </div>
          </a-form>
        </a-card>

        <!-- 权限设置 -->
        <a-card title="权限设置" class="mb-6">
          <a-form :model="permissionSettings" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item label="默认成员权限">
                  <a-select v-model:value="permissionSettings.defaultMemberRole" style="width: 100%">
                    <a-select-option value="1">只读</a-select-option>
                    <a-select-option value="2">编辑</a-select-option>
                    <a-select-option value="3">管理</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="访客权限">
                  <a-select v-model:value="permissionSettings.guestPermission" style="width: 100%">
                    <a-select-option value="0">禁止访问</a-select-option>
                    <a-select-option value="1">只读</a-select-option>
                    <a-select-option value="2">受限编辑</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item label="文档权限控制">
                  <a-switch v-model:checked="permissionSettings.documentControl" />
                  <span class="ml-2 text-sm text-gray-500">启用文档权限控制</span>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="任务权限控制">
                  <a-switch v-model:checked="permissionSettings.taskControl" />
                  <span class="ml-2 text-sm text-gray-500">启用任务权限控制</span>
                </a-form-item>
              </a-col>
            </a-row>

            <a-divider>功能权限配置</a-divider>

            <div class="permission-matrix">
              <a-table :columns="permissionColumns" :data-source="permissionMatrix" :pagination="false" size="small" bordered>
                <template #role="{ record }">
                  <span class="font-medium">{{ record.role }}</span>
                </template>

                <template #view="{ record }">
                  <a-checkbox v-model:checked="record.permissions.view" />
                </template>

                <template #edit="{ record }">
                  <a-checkbox v-model:checked="record.permissions.edit" />
                </template>

                <template #delete="{ record }">
                  <a-checkbox v-model:checked="record.permissions.delete" />
                </template>

                <template #manage="{ record }">
                  <a-checkbox v-model:checked="record.permissions.manage" />
                </template>
              </a-table>
            </div>
          </a-form>
        </a-card>

        <!-- 集成设置 -->
        <a-card title="集成设置" class="mb-6">
          <a-form :model="integrationSettings" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item label="Git集成">
                  <a-switch v-model:checked="integrationSettings.git" />
                  <span class="ml-2 text-sm text-gray-500">启用Git代码仓库集成</span>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="CI/CD集成">
                  <a-switch v-model:checked="integrationSettings.cicd" />
                  <span class="ml-2 text-sm text-gray-500">启用持续集成部署</span>
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item label="第三方API">
                  <a-switch v-model:checked="integrationSettings.thirdPartyApi" />
                  <span class="ml-2 text-sm text-gray-500">允许第三方API访问</span>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="Webhook">
                  <a-switch v-model:checked="integrationSettings.webhook" />
                  <span class="ml-2 text-sm text-gray-500">启用Webhook通知</span>
                </a-form-item>
              </a-col>
            </a-row>

            <a-divider>API配置</a-divider>

            <div class="api-config">
              <div class="config-item mb-4">
                <label class="block text-sm font-medium mb-2">API密钥</label>
                <a-input-group compact>
                  <a-input
                    v-model:value="integrationSettings.apiKey"
                    placeholder="API密钥"
                    style="width: calc(100% - 100px)"
                    :type="showApiKey ? 'text' : 'password'" />
                  <a-button @click="showApiKey = !showApiKey">
                    {{ showApiKey ? '隐藏' : '显示' }}
                  </a-button>
                </a-input-group>
              </div>

              <div class="config-item mb-4">
                <label class="block text-sm font-medium mb-2">Webhook URL</label>
                <a-input v-model:value="integrationSettings.webhookUrl" placeholder="https://your-webhook-url.com" />
              </div>
            </div>
          </a-form>
        </a-card>

        <!-- 高级设置 -->
        <a-card title="高级设置" class="mb-6">
          <a-form :model="advancedSettings" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item label="数据保留期">
                  <a-select v-model:value="advancedSettings.dataRetention" style="width: 100%">
                    <a-select-option value="30">30天</a-select-option>
                    <a-select-option value="90">90天</a-select-option>
                    <a-select-option value="180">180天</a-select-option>
                    <a-select-option value="365">1年</a-select-option>
                    <a-select-option value="-1">永久保留</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="日志级别">
                  <a-select v-model:value="advancedSettings.logLevel" style="width: 100%">
                    <a-select-option value="1">错误</a-select-option>
                    <a-select-option value="2">警告</a-select-option>
                    <a-select-option value="3">信息</a-select-option>
                    <a-select-option value="4">调试</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item label="性能监控">
                  <a-switch v-model:checked="advancedSettings.performanceMonitoring" />
                  <span class="ml-2 text-sm text-gray-500">启用性能监控</span>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="错误追踪">
                  <a-switch v-model:checked="advancedSettings.errorTracking" />
                  <span class="ml-2 text-sm text-gray-500">启用错误追踪</span>
                </a-form-item>
              </a-col>
            </a-row>

            <a-divider>危险操作</a-divider>

            <div class="danger-zone bg-red-50 border border-red-200 rounded p-4">
              <h4 class="text-red-600 font-medium mb-3">危险操作区域</h4>
              <div class="danger-actions space-y-3">
                <div class="action-item flex items-center justify-between">
                  <div>
                    <div class="font-medium">重置项目设置</div>
                    <div class="text-sm text-gray-600">将所有设置恢复到默认值</div>
                  </div>
                  <a-button danger @click="handleResetSettings"> 重置设置 </a-button>
                </div>

                <div class="action-item flex items-center justify-between">
                  <div>
                    <div class="font-medium">清空项目数据</div>
                    <div class="text-sm text-gray-600">删除所有项目数据（不可恢复）</div>
                  </div>
                  <a-button danger @click="handleClearData"> 清空数据 </a-button>
                </div>

                <div class="action-item flex items-center justify-between">
                  <div>
                    <div class="font-medium">删除项目</div>
                    <div class="text-sm text-gray-600">永久删除整个项目（不可恢复）</div>
                  </div>
                  <a-button danger type="primary" @click="handleDeleteProject"> 删除项目 </a-button>
                </div>
              </div>
            </div>
          </a-form>
        </a-card>

        <!-- 保存按钮 -->
        <div class="save-actions text-center">
          <a-space>
            <a-button type="primary" size="large" @click="handleSaveSettings" :loading="saveLoading"> 保存所有设置 </a-button>
            <a-button size="large" @click="handleReloadSettings"> 重新加载 </a-button>
          </a-space>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import { Card, Form, Row, Col, Radio, Select, Switch, Checkbox, Table, Input, Button, Space, Divider } from 'ant-design-vue';
  import { useMessage } from '/@/hooks/web/useMessage';

  const props = defineProps({
    projectId: {
      type: String,
      default: '',
    },
  });

  const { createMessage } = useMessage();

  // 响应式数据
  const loading = ref(false);
  const saveLoading = ref(false);
  const showApiKey = ref(false);

  // 基础设置
  const basicSettings = reactive({
    visibility: 2,
    status: '1',
    timezone: 'Asia/Shanghai',
    workdays: ['1', '2', '3', '4', '5'],
    autoArchive: true,
    dataBackup: true,
  });

  // 通知设置
  const notificationSettings = reactive({
    email: true,
    sms: false,
    browser: true,
    mobile: false,
  });

  // 权限设置
  const permissionSettings = reactive({
    defaultMemberRole: '2',
    guestPermission: '1',
    documentControl: true,
    taskControl: true,
  });

  // 集成设置
  const integrationSettings = reactive({
    git: true,
    cicd: false,
    thirdPartyApi: false,
    webhook: true,
    apiKey: 'sk-1234567890abcdef',
    webhookUrl: '',
  });

  // 高级设置
  const advancedSettings = reactive({
    dataRetention: '365',
    logLevel: '3',
    performanceMonitoring: true,
    errorTracking: true,
  });

  // 工作日选项
  const workdayOptions = [
    { label: '周一', value: '1' },
    { label: '周二', value: '2' },
    { label: '周三', value: '3' },
    { label: '周四', value: '4' },
    { label: '周五', value: '5' },
    { label: '周六', value: '6' },
    { label: '周日', value: '7' },
  ];

  // 通知渠道选项
  const channelOptions = [
    { label: '邮件', value: 'email' },
    { label: '短信', value: 'sms' },
    { label: '浏览器', value: 'browser' },
    { label: '移动端', value: 'mobile' },
  ];

  // 通知事件
  const notificationEvents = ref([
    {
      name: '任务分配',
      enabled: true,
      channels: ['email', 'browser'],
    },
    {
      name: '任务完成',
      enabled: true,
      channels: ['email'],
    },
    {
      name: '项目里程碑',
      enabled: true,
      channels: ['email', 'sms', 'browser'],
    },
    {
      name: '风险预警',
      enabled: true,
      channels: ['email', 'sms'],
    },
    {
      name: '进度延期',
      enabled: true,
      channels: ['email', 'browser'],
    },
  ]);

  // 权限矩阵表格列
  const permissionColumns = [
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      slots: { customRender: 'role' },
    },
    {
      title: '查看',
      dataIndex: 'view',
      key: 'view',
      align: 'center',
      slots: { customRender: 'view' },
    },
    {
      title: '编辑',
      dataIndex: 'edit',
      key: 'edit',
      align: 'center',
      slots: { customRender: 'edit' },
    },
    {
      title: '删除',
      dataIndex: 'delete',
      key: 'delete',
      align: 'center',
      slots: { customRender: 'delete' },
    },
    {
      title: '管理',
      dataIndex: 'manage',
      key: 'manage',
      align: 'center',
      slots: { customRender: 'manage' },
    },
  ];

  // 权限矩阵数据
  const permissionMatrix = ref([
    {
      role: '项目经理',
      permissions: { view: true, edit: true, delete: true, manage: true },
    },
    {
      role: '开发工程师',
      permissions: { view: true, edit: true, delete: false, manage: false },
    },
    {
      role: '测试工程师',
      permissions: { view: true, edit: true, delete: false, manage: false },
    },
    {
      role: '访客',
      permissions: { view: true, edit: false, delete: false, manage: false },
    },
  ]);

  onMounted(() => {
    loadProjectSettings();
  });

  // 加载项目设置
  const loadProjectSettings = async () => {
    loading.value = true;
    try {
      // 这里调用实际的API
      // const result = await getProjectSettings(props.projectId);
      // 更新各个设置对象

      console.log('项目设置加载完成');
    } catch (error) {
      console.error('加载项目设置失败:', error);
      createMessage.error('加载项目设置失败');
    } finally {
      loading.value = false;
    }
  };

  // 保存所有设置
  const handleSaveSettings = async () => {
    saveLoading.value = true;
    try {
      // 这里调用保存API
      // await saveProjectSettings({
      //   projectId: props.projectId,
      //   basicSettings,
      //   notificationSettings,
      //   permissionSettings,
      //   integrationSettings,
      //   advancedSettings,
      //   notificationEvents: notificationEvents.value,
      //   permissionMatrix: permissionMatrix.value
      // });

      createMessage.success('设置保存成功');
    } catch (error) {
      console.error('保存设置失败:', error);
      createMessage.error('保存设置失败');
    } finally {
      saveLoading.value = false;
    }
  };

  // 重新加载设置
  const handleReloadSettings = () => {
    loadProjectSettings();
  };

  // 重置设置
  const handleResetSettings = () => {
    createMessage.info('重置设置功能开发中...');
  };

  // 清空数据
  const handleClearData = () => {
    createMessage.info('清空数据功能开发中...');
  };

  // 删除项目
  const handleDeleteProject = () => {
    createMessage.info('删除项目功能开发中...');
  };
</script>

<style lang="less" scoped>
  .project-setting {
    .danger-zone {
      .action-item {
        padding: 12px 0;
        border-bottom: 1px solid #fecaca;

        &:last-child {
          border-bottom: none;
        }
      }
    }

    .save-actions {
      margin-top: 32px;
      padding-top: 24px;
      border-top: 1px solid #f0f0f0;
    }
  }
</style>
