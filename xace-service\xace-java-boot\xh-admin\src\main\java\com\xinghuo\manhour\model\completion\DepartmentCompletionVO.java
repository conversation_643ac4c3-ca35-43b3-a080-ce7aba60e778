package com.xinghuo.manhour.model.completion;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 分部完成情况数据VO
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@Schema(description = "分部完成情况数据")
public class DepartmentCompletionVO {

    @Schema(description = "分部ID")
    private String fbId;

    @Schema(description = "分部名称")
    private String fbName;

    @Schema(description = "应填写人数")
    private Integer totalUsers;

    @Schema(description = "已填写人数")
    private Integer completedUsers;

    @Schema(description = "未填写人数")
    private Integer unfilledUsers;

    @Schema(description = "完成率")
    private BigDecimal completionRate;

    @Schema(description = "待审批数")
    private Integer pendingCount;

    @Schema(description = "逾期人数")
    private Integer overdueUsers;

    @Schema(description = "分部负责人ID")
    private String fbLeaderId;

    @Schema(description = "分部负责人")
    private String fbLeaderName;

    @Schema(description = "平均填写时长")
    private BigDecimal avgCompletionDays;

    @Schema(description = "最早逾期天数")
    private Integer maxOverdueDays;

    @Schema(description = "分部排名")
    private Integer ranking;
}
