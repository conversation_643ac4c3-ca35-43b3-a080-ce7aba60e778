package com.xinghuo.project.biz.model.bizBusinessWeeklog;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;

/**
 * 商机周报更新表单
 * 
 * <AUTHOR>
 * @date 2025-07-08
 */
@Data
@Schema(description = "商机周报更新表单")
public class BizBusinessWeeklogUpForm {

    /**
     * 所属分部
     */
    @Schema(description = "所属分部")
    private String fbId;

    /**
     * 项目类型
     */
    @Schema(description = "项目类型")
    private String projType;

    /**
     * 项目ID
     */
    @NotBlank(message = "项目ID不能为空")
    @Schema(description = "项目ID", required = true)
    private String projId;

    /**
     * 项目名称
     */
    @NotBlank(message = "项目名称不能为空")
    @Schema(description = "项目名称", required = true)
    private String projName;

    /**
     * 项目备注
     */
    @Schema(description = "项目备注")
    private String projNote;

    /**
     * 项目级别
     */
    @Schema(description = "项目级别")
    private String projectLevel;

    /**
     * 开始日期
     */
    @NotNull(message = "开始日期不能为空")
    @Schema(description = "开始日期", required = true)
    private Date startDate;

    /**
     * 结束日期
     */
    @NotNull(message = "结束日期不能为空")
    @Schema(description = "结束日期", required = true)
    private Date endDate;

    /**
     * 录入日期
     */
    @Schema(description = "录入日期")
    private Date inputDate;

    /**
     * 负责人ID
     */
    @NotBlank(message = "负责人不能为空")
    @Schema(description = "负责人ID", required = true)
    private String ownId;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 计划
     */
    @Schema(description = "计划")
    private String plan;

    /**
     * 风险
     */
    @Schema(description = "风险")
    private String risk;

    /**
     * 状态 (1-表示已填写，0-未填写，2-提交审核，3-已发布，-1-已驳回)
     */
    @Schema(description = "状态")
    private Integer status;

    /**
     * 显示状态 (0-表示未显示，1-表示显示)
     */
    @Schema(description = "显示状态")
    private Integer showStatus;
}