import { defHttp } from '/@/utils/http/axios';

/**
 * 客户联系人管理API
 */

// API URL前缀
const API_PREFIX = '/api/project/biz/customer/linkman';

/**
 * 客户联系人对象接口
 */
export interface CustomerLinkmanModel {
  id: string;
  cuId: string;
  customerName?: string; // 客户单位名称
  linkman: string;
  telephone: string;
  status: number;
  topic: string;
  content: string;
  note: string;
  creatorUserId: string;
  creatorTime: string;
  lastModifyUserId: string;
  lastModifyTime: string;
}

/**
 * 客户联系人表单接口
 */
export interface CustomerLinkmanFormModel {
  cuId: string;
  linkman: string;
  telephone?: string;
  status?: number;
  topic?: string;
  content?: string;
  note?: string;
}

/**
 * 获取客户联系人列表
 * @param customerId 客户ID
 * @returns 客户联系人列表
 */
export const getCustomerLinkmanList = (customerId: string) => {
  return defHttp.get<CustomerLinkmanModel[]>({
    url: `${API_PREFIX}/getListByCustomerId/${customerId}`,
  });
};

/**
 * 获取客户联系人详情
 * @param id 客户联系人ID
 * @returns 客户联系人详情
 */
export const getCustomerLinkmanInfo = (id: string) => {
  return defHttp.get<CustomerLinkmanModel>({
    url: `${API_PREFIX}/${id}`,
  });
};

/**
 * 创建客户联系人
 * @param params 客户联系人创建参数
 * @returns 操作结果
 */
export const createCustomerLinkman = (params: CustomerLinkmanFormModel) => {
  return defHttp.post<void>({
    url: `${API_PREFIX}`,
    data: params,
  });
};

/**
 * 更新客户联系人
 * @param id 客户联系人ID
 * @param params 客户联系人更新参数
 * @returns 操作结果
 */
export const updateCustomerLinkman = (id: string, params: CustomerLinkmanFormModel) => {
  return defHttp.put<void>({
    url: `${API_PREFIX}/${id}`,
    data: params,
  });
};

/**
 * 删除客户联系人
 * @param id 客户联系人ID
 * @returns 操作结果
 */
export const deleteCustomerLinkman = (id: string) => {
  return defHttp.delete<void>({
    url: `${API_PREFIX}/${id}`,
  });
};

/**
 * 更新客户联系人状态
 * @param id 客户联系人ID
 * @param status 状态 1-有效，0-无效
 * @returns 操作结果
 */
export const updateCustomerLinkmanStatus = (id: string, status: number) => {
  return defHttp.put<void>({
    url: `${API_PREFIX}/updateStatus/${id}?status=${status}`,
  });
};

/**
 * 获取联系人列表（分页查询）
 * @param params 查询参数
 * @returns 联系人列表
 */
export const getLinkmanList = (params: any) => {
  return defHttp.post<any>({
    url: `${API_PREFIX}/getList`,
    data: params,
  });
};
