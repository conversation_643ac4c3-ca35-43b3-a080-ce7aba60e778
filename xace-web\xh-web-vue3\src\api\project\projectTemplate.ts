import { defHttp } from '/@/utils/http/axios';

enum Api {
  GetList = '/api/project/schema/projectTemplate/getList',
  GetListByStatus = '/api/project/schema/projectTemplate/getListByStatus',
  GetListByType = '/api/project/schema/projectTemplate/getListByType',
  GetDetailInfo = '/api/project/schema/projectTemplate/getDetailInfo',
  GetInfo = '/api/project/schema/projectTemplate/getInfo',
  Create = '/api/project/schema/projectTemplate/create',
  Update = '/api/project/schema/projectTemplate/update',
  Delete = '/api/project/schema/projectTemplate/delete',
  BatchDelete = '/api/project/schema/projectTemplate/batchDelete',
  UpdateStatus = '/api/project/schema/projectTemplate/updateStatus',
  BatchUpdateStatus = '/api/project/schema/projectTemplate/batchUpdateStatus',
  Enable = '/api/project/schema/projectTemplate/enable',
  Disable = '/api/project/schema/projectTemplate/disable',
  BatchEnable = '/api/project/schema/projectTemplate/batchEnable',
  BatchDisable = '/api/project/schema/projectTemplate/batchDisable',
  Copy = '/api/project/schema/projectTemplate/copy',
  CheckNameExists = '/api/project/schema/projectTemplate/checkNameExists',
  CheckCodeExists = '/api/project/schema/projectTemplate/checkCodeExists',
  GetSelectList = '/api/project/schema/projectTemplate/getSelectList',
  GenerateCode = '/api/project/schema/projectTemplate/generateCode',
  GetTemplateUsageInfo = '/api/project/schema/projectTemplate/getTemplateUsageInfo',
  ImportFromWbsTemplate = '/api/project/schema/projectTemplate/importFromWbsTemplate',
  ImportFromPhaseTemplate = '/api/project/schema/projectTemplate/importFromPhaseTemplate',
  ImportFromPhaseLibrary = '/api/project/schema/projectTemplate/importFromPhaseLibrary',
  GetWbsConfigs = '/api/project/schema/projectTemplate/getWbsConfigs',
  GetPhaseConfigs = '/api/project/schema/projectTemplate/getPhaseConfigs',
  SaveWbsConfigs = '/api/project/schema/projectTemplate/saveWbsConfigs',
  SavePhaseConfigs = '/api/project/schema/projectTemplate/savePhaseConfigs',
  DeleteWbsConfigs = '/api/project/schema/projectTemplate/deleteWbsConfigs',
  DeletePhaseConfigs = '/api/project/schema/projectTemplate/deletePhaseConfigs',
  ApplyToProjects = '/api/project/schema/projectTemplate/applyToProjects',
}

/**
 * 项目模板接口
 */

// 获取项目模板列表
export function getProjectTemplateList(params: any) {
  return defHttp.post({
    url: Api.GetList,
    data: params,
  });
}

// 根据状态获取模板列表
export function getProjectTemplateListByStatus(status: number) {
  return defHttp.get({
    url: `${Api.GetListByStatus}/${status}`,
  });
}

// 根据类型获取模板列表
export function getProjectTemplateListByType(typeId: string) {
  return defHttp.get({
    url: `${Api.GetListByType}/${typeId}`,
  });
}

// 获取模板详情（包含WBS和阶段配置）
export function getProjectTemplateDetailInfo(id: string) {
  return defHttp.get({
    url: `${Api.GetDetailInfo}/${id}`,
  });
}

// 获取模板基本信息
export function getProjectTemplateInfo(id: string) {
  return defHttp.get({
    url: `${Api.GetInfo}/${id}`,
  });
}

// 创建项目模板
export function createProjectTemplate(params: any) {
  return defHttp.post({
    url: Api.Create,
    data: params,
  });
}

// 更新项目模板
export function updateProjectTemplate(id: string, params: any) {
  return defHttp.put({
    url: `${Api.Update}/${id}`,
    data: params,
  });
}

// 删除项目模板
export function deleteProjectTemplate(id: string) {
  return defHttp.delete({
    url: `${Api.Delete}/${id}`,
  });
}

// 批量删除项目模板
export function batchDeleteProjectTemplate(ids: string[]) {
  return defHttp.delete({
    url: Api.BatchDelete,
    data: ids,
  });
}

// 更新模板状态
export function updateProjectTemplateStatus(id: string, status: number) {
  return defHttp.put({
    url: `${Api.UpdateStatus}/${id}?status=${status}`,
  });
}

// 批量更新状态
export function batchUpdateProjectTemplateStatus(ids: string[], status: number) {
  return defHttp.put({
    url: `${Api.BatchUpdateStatus}?status=${status}`,
    data: ids,
  });
}

// 启用模板
export function enableProjectTemplate(id: string) {
  return defHttp.put({
    url: `${Api.Enable}/${id}`,
  });
}

// 禁用模板
export function disableProjectTemplate(id: string) {
  return defHttp.put({
    url: `${Api.Disable}/${id}`,
  });
}

// 批量启用模板
export function batchEnableProjectTemplate(ids: string[]) {
  return defHttp.put({
    url: Api.BatchEnable,
    data: ids,
  });
}

// 批量禁用模板
export function batchDisableProjectTemplate(ids: string[]) {
  return defHttp.put({
    url: Api.BatchDisable,
    data: ids,
  });
}

// 复制项目模板
export function copyProjectTemplate(id: string, newName: string) {
  return defHttp.post({
    url: `${Api.Copy}/${id}?newName=${encodeURIComponent(newName)}`,
  });
}

// 检查模板名称是否存在
export function checkProjectTemplateNameExists(name: string, excludeId?: string) {
  return defHttp.get({
    url: Api.CheckNameExists,
    params: { name, excludeId },
  });
}

// 检查模板编码是否存在
export function checkProjectTemplateCodeExists(code: string, excludeId?: string) {
  return defHttp.get({
    url: Api.CheckCodeExists,
    params: { code, excludeId },
  });
}

// 获取模板选择列表
export function getProjectTemplateSelectList(keyword?: string) {
  return defHttp.get({
    url: Api.GetSelectList,
    params: { keyword },
  });
}

// 生成模板编码
export function generateProjectTemplateCode() {
  return defHttp.get({
    url: Api.GenerateCode,
  });
}

// 获取模板使用情况
export function getProjectTemplateUsageInfo(id: string) {
  return defHttp.get({
    url: `${Api.GetTemplateUsageInfo}/${id}`,
  });
}

// 从WBS模板导入配置
export function importFromWbsTemplate(templateId: string, wbsTemplateId: string) {
  return defHttp.post({
    url: `${Api.ImportFromWbsTemplate}/${templateId}?wbsTemplateId=${wbsTemplateId}`,
  });
}

// 从阶段模板导入配置
export function importFromPhaseTemplate(templateId: string, phaseTemplateId: string) {
  return defHttp.post({
    url: `${Api.ImportFromPhaseTemplate}/${templateId}?phaseTemplateId=${phaseTemplateId}`,
  });
}

// 从标准阶段库导入阶段
export function importFromPhaseLibrary(templateId: string, phaseIds: string[]) {
  return defHttp.post({
    url: `${Api.ImportFromPhaseLibrary}/${templateId}`,
    data: phaseIds,
  });
}

// 获取WBS配置列表
export function getProjectTemplateWbsConfigs(templateId: string) {
  return defHttp.get({
    url: `${Api.GetWbsConfigs}/${templateId}`,
  });
}

// 获取阶段配置列表
export function getProjectTemplatePhaseConfigs(templateId: string) {
  return defHttp.get({
    url: `${Api.GetPhaseConfigs}/${templateId}`,
  });
}

// 保存WBS配置
export function saveProjectTemplateWbsConfigs(templateId: string, wbsConfigs: any[]) {
  return defHttp.post({
    url: `${Api.SaveWbsConfigs}/${templateId}`,
    data: wbsConfigs,
  });
}

// 保存阶段配置
export function saveProjectTemplatePhaseConfigs(templateId: string, phaseConfigs: any[]) {
  return defHttp.post({
    url: `${Api.SavePhaseConfigs}/${templateId}`,
    data: phaseConfigs,
  });
}

// 删除WBS配置
export function deleteProjectTemplateWbsConfigs(wbsIds: string[]) {
  return defHttp.delete({
    url: Api.DeleteWbsConfigs,
    data: wbsIds,
  });
}

// 删除阶段配置
export function deleteProjectTemplatePhaseConfigs(phaseIds: string[]) {
  return defHttp.delete({
    url: Api.DeletePhaseConfigs,
    data: phaseIds,
  });
}

// 应用到项目
export function applyProjectTemplateToProjects(templateId: string, projectIds: string[]) {
  return defHttp.post({
    url: `${Api.ApplyToProjects}/${templateId}`,
    data: projectIds,
  });
}
