package com.xinghuo.project.template.model.dto;

import com.xinghuo.project.template.entity.WbsTemplateMasterEntity;
import com.xinghuo.project.template.entity.WbsTemplateDetailEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * WBS计划模板DTO类
 * 包含主表信息和明细列表
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WbsTemplateVO extends WbsTemplateMasterEntity {

    /**
     * WBS活动明细列表
     */
    private List<WbsTemplateDetailEntity> wbsDetails;

    /**
     * 关联的项目模板ID列表
     */
    private List<String> projectTemplateIds;

    /**
     * 标签ID列表
     */
    private List<String> tagIds;

    /**
     * 状态名称（冗余字段，便于显示）
     */
    private String statusName;

    /**
     * 创建人姓名（冗余字段，便于显示）
     */
    private String creatorUserName;

    /**
     * 活动总数
     */
    private Integer activityCount;

    /**
     * 总工期（所有活动工期之和）
     */
    private BigDecimal totalDuration;

    /**
     * 里程碑数量
     */
    private Integer milestoneCount;

    /**
     * 最大层级深度
     */
    private Integer maxLevel;
}
