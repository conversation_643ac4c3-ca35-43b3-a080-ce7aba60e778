<template>
  <div class="opportunity-weekly">
    <!-- 商机不存在提示 -->
    <a-result v-if="!hasOpportunity" status="info" title="该项目暂无关联商机" sub-title="只有商机类型的项目才会有商机周报功能">
      <template #extra>
        <a-button type="primary" @click="goBack"> 返回概览 </a-button>
      </template>
    </a-result>

    <!-- 商机周报内容 -->
    <div v-else>
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-content">
          <div class="header-left">
            <h1>{{ opportunityInfo.projectName }}</h1>
            <a-space size="large">
              <span>商机编号：{{ opportunityInfo.businessNo }}</span>
              <a-tag :color="getStatusColor(opportunityInfo.status)">
                {{ getStatusName(opportunityInfo.status) }}
              </a-tag>
            </a-space>
          </div>
          <div class="header-right">
            <a-space>
              <a-button type="primary" @click="showGenerateModal">
                <file-add-outlined />
                生成周报
              </a-button>
              <a-button @click="handleExport">
                <download-outlined />
                导出周报
              </a-button>
            </a-space>
          </div>
        </div>
      </div>

      <!-- 周报统计 -->
      <div class="weekly-stats">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-card>
              <a-statistic title="总周报数" :value="weeklyReports.length" :value-style="{ color: '#3f8600' }">
                <template #prefix>
                  <calendar-outlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card>
              <a-statistic title="本月周报" :value="getMonthlyReportCount()" :value-style="{ color: '#1890ff' }">
                <template #prefix>
                  <file-text-outlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card>
              <a-statistic title="最近更新" :value="getLastUpdateDays()" suffix="天前" :value-style="{ color: '#722ed1' }">
                <template #prefix>
                  <clock-circle-outlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card>
              <a-statistic title="平均工时" :value="getAverageWorkHours()" suffix="小时" :value-style="{ color: '#eb2f96' }">
                <template #prefix>
                  <hourglass-outlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 周报列表 -->
      <div class="weekly-list">
        <a-card title="周报列表" :bordered="false">
          <template #extra>
            <a-space>
              <a-range-picker v-model:value="dateRange" :placeholder="['开始日期', '结束日期']" @change="handleDateRangeChange" />
              <a-select v-model:value="filterStatus" style="width: 120px" @change="handleStatusFilterChange">
                <a-select-option value="all">全部状态</a-select-option>
                <a-select-option value="draft">草稿</a-select-option>
                <a-select-option value="submitted">已提交</a-select-option>
                <a-select-option value="approved">已审批</a-select-option>
              </a-select>
            </a-space>
          </template>

          <a-table :columns="columns" :data-source="filteredReports" :pagination="pagination" :loading="loading" row-key="id">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'weekRange'">
                <span>{{ formatWeekRange(record.weekStart, record.weekEnd) }}</span>
              </template>
              <template v-else-if="column.key === 'status'">
                <a-tag :color="getReportStatusColor(record.status)">
                  {{ getReportStatusName(record.status) }}
                </a-tag>
              </template>
              <template v-else-if="column.key === 'workHours'">
                <span>{{ record.workHours }}小时</span>
              </template>
              <template v-else-if="column.key === 'action'">
                <a-space>
                  <a-button type="link" size="small" @click="viewReport(record)"> 查看 </a-button>
                  <a-button type="link" size="small" @click="editReport(record)"> 编辑 </a-button>
                  <a-button type="link" size="small" danger @click="deleteReport(record)"> 删除 </a-button>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-card>
      </div>
    </div>

    <!-- 生成周报模态框 -->
    <a-modal v-model:open="generateModalVisible" title="生成周报" :width="900" @ok="handleGenerateReport" @cancel="cancelGenerate">
      <a-form ref="reportFormRef" :model="reportForm" :rules="reportFormRules" layout="vertical">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item name="weekStart" label="周报开始日期">
              <a-date-picker v-model:value="reportForm.weekStart" style="width: 100%" placeholder="选择开始日期" @change="handleWeekStartChange" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="weekEnd" label="周报结束日期">
              <a-date-picker v-model:value="reportForm.weekEnd" style="width: 100%" placeholder="选择结束日期" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item name="workContent" label="本周工作内容">
          <a-textarea v-model:value="reportForm.workContent" :rows="4" placeholder="请描述本周的主要工作内容..." show-count :maxlength="1000" />
        </a-form-item>

        <a-form-item name="workHours" label="工作时长">
          <a-input-number
            v-model:value="reportForm.workHours"
            :min="0"
            :max="168"
            :precision="1"
            style="width: 100%"
            placeholder="请输入工作时长"
            suffix="小时" />
        </a-form-item>

        <a-form-item name="achievements" label="主要成果">
          <a-textarea v-model:value="reportForm.achievements" :rows="3" placeholder="请描述本周取得的主要成果..." show-count :maxlength="800" />
        </a-form-item>

        <a-form-item name="problems" label="遇到的问题">
          <a-textarea v-model:value="reportForm.problems" :rows="3" placeholder="请描述本周遇到的问题和困难..." show-count :maxlength="800" />
        </a-form-item>

        <a-form-item name="nextWeekPlan" label="下周计划">
          <a-textarea v-model:value="reportForm.nextWeekPlan" :rows="3" placeholder="请描述下周的工作计划..." show-count :maxlength="800" />
        </a-form-item>

        <a-form-item name="remark" label="备注">
          <a-textarea v-model:value="reportForm.remark" :rows="2" placeholder="其他需要说明的事项..." show-count :maxlength="500" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 查看周报模态框 -->
    <a-modal v-model:open="viewModalVisible" title="查看周报" :width="800" :footer="null">
      <div v-if="currentReport" class="report-view">
        <a-descriptions title="周报信息" :column="2" bordered>
          <a-descriptions-item label="周报时间">
            {{ formatWeekRange(currentReport.weekStart, currentReport.weekEnd) }}
          </a-descriptions-item>
          <a-descriptions-item label="工作时长"> {{ currentReport.workHours }}小时 </a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getReportStatusColor(currentReport.status)">
              {{ getReportStatusName(currentReport.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="提交时间">
            {{ formatDateTime(currentReport.submitTime) }}
          </a-descriptions-item>
        </a-descriptions>

        <a-divider />

        <div class="report-content">
          <h4>本周工作内容</h4>
          <p>{{ currentReport.workContent }}</p>

          <h4>主要成果</h4>
          <p>{{ currentReport.achievements }}</p>

          <h4>遇到的问题</h4>
          <p>{{ currentReport.problems }}</p>

          <h4>下周计划</h4>
          <p>{{ currentReport.nextWeekPlan }}</p>

          <h4 v-if="currentReport.remark">备注</h4>
          <p v-if="currentReport.remark">{{ currentReport.remark }}</p>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, computed } from 'vue';
  import { message } from 'ant-design-vue';
  import { FileAddOutlined, DownloadOutlined, CalendarOutlined, FileTextOutlined, ClockCircleOutlined, HourglassOutlined } from '@ant-design/icons-vue';
  import { formatToDate, formatToDateTime } from '/@/utils/dateUtil';
  import dayjs from 'dayjs';

  const props = defineProps<{
    projectId?: string;
  }>();

  const hasOpportunity = ref(true);
  const opportunityInfo = ref<any>({});
  const weeklyReports = ref<any[]>([]);
  const filteredReports = ref<any[]>([]);
  const loading = ref(false);
  const generateModalVisible = ref(false);
  const viewModalVisible = ref(false);
  const currentReport = ref<any>(null);
  const reportFormRef = ref();
  const dateRange = ref<any[]>([]);
  const filterStatus = ref('all');

  // 分页配置
  const pagination = ref({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number) => `共 ${total} 条`,
  });

  // 表格列配置
  const columns = [
    {
      title: '周报时间',
      key: 'weekRange',
      width: 200,
    },
    {
      title: '工作时长',
      key: 'workHours',
      width: 100,
    },
    {
      title: '状态',
      key: 'status',
      width: 100,
    },
    {
      title: '提交时间',
      dataIndex: 'submitTime',
      key: 'submitTime',
      width: 150,
      customRender: ({ text }) => formatDateTime(text),
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
    },
  ];

  // 周报表单数据
  const reportForm = ref({
    weekStart: null,
    weekEnd: null,
    workContent: '',
    workHours: 0,
    achievements: '',
    problems: '',
    nextWeekPlan: '',
    remark: '',
  });

  // 表单验证规则
  const reportFormRules = {
    weekStart: [{ required: true, message: '请选择开始日期', trigger: 'change' }],
    weekEnd: [{ required: true, message: '请选择结束日期', trigger: 'change' }],
    workContent: [{ required: true, message: '请填写工作内容', trigger: 'blur' }],
    workHours: [{ required: true, message: '请填写工作时长', trigger: 'blur' }],
  };

  // 获取状态名称
  const getStatusName = (status: string) => {
    const statusMap = {
      '1': '跟踪中',
      '2': '方案报价中',
      '3': '商务谈判中',
      '4': '已签',
      '5': '已废弃',
      '6': '明年跟踪',
    };
    return statusMap[status] || '未知状态';
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    const colorMap = {
      '1': 'processing',
      '2': 'warning',
      '3': 'cyan',
      '4': 'success',
      '5': 'error',
      '6': 'purple',
    };
    return colorMap[status] || 'default';
  };

  // 获取周报状态名称
  const getReportStatusName = (status: string) => {
    const statusMap = {
      draft: '草稿',
      submitted: '已提交',
      approved: '已审批',
    };
    return statusMap[status] || '未知状态';
  };

  // 获取周报状态颜色
  const getReportStatusColor = (status: string) => {
    const colorMap = {
      draft: 'default',
      submitted: 'processing',
      approved: 'success',
    };
    return colorMap[status] || 'default';
  };

  // 格式化周报时间范围
  const formatWeekRange = (start: string, end: string) => {
    return `${formatToDate(start)} ~ ${formatToDate(end)}`;
  };

  // 格式化日期时间
  const formatDateTime = (date: string) => {
    return date ? formatToDateTime(date) : '-';
  };

  // 获取本月周报数量
  const getMonthlyReportCount = () => {
    const now = dayjs();
    return weeklyReports.value.filter(report => dayjs(report.submitTime).isSame(now, 'month')).length;
  };

  // 获取最近更新天数
  const getLastUpdateDays = () => {
    if (weeklyReports.value.length === 0) return 0;
    const lastReport = weeklyReports.value[0];
    return dayjs().diff(dayjs(lastReport.submitTime), 'day');
  };

  // 获取平均工时
  const getAverageWorkHours = () => {
    if (weeklyReports.value.length === 0) return 0;
    const totalHours = weeklyReports.value.reduce((sum, report) => sum + report.workHours, 0);
    return Math.round((totalHours / weeklyReports.value.length) * 10) / 10;
  };

  // 显示生成周报模态框
  const showGenerateModal = () => {
    generateModalVisible.value = true;
    // 默认设置为本周
    const now = dayjs();
    const weekStart = now.startOf('week');
    const weekEnd = now.endOf('week');
    reportForm.value.weekStart = weekStart;
    reportForm.value.weekEnd = weekEnd;
  };

  // 取消生成周报
  const cancelGenerate = () => {
    generateModalVisible.value = false;
    reportFormRef.value?.resetFields();
  };

  // 处理周开始日期变化
  const handleWeekStartChange = (date: any) => {
    if (date) {
      reportForm.value.weekEnd = date.add(6, 'day');
    }
  };

  // 生成周报
  const handleGenerateReport = async () => {
    try {
      await reportFormRef.value.validate();
      const newReport = {
        id: Date.now().toString(),
        weekStart: reportForm.value.weekStart.format('YYYY-MM-DD'),
        weekEnd: reportForm.value.weekEnd.format('YYYY-MM-DD'),
        workContent: reportForm.value.workContent,
        workHours: reportForm.value.workHours,
        achievements: reportForm.value.achievements,
        problems: reportForm.value.problems,
        nextWeekPlan: reportForm.value.nextWeekPlan,
        remark: reportForm.value.remark,
        status: 'draft',
        submitTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        submitUser: '当前用户',
      };

      weeklyReports.value.unshift(newReport);
      filteredReports.value = [...weeklyReports.value];
      message.success('周报生成成功');
      generateModalVisible.value = false;
      reportFormRef.value?.resetFields();
    } catch (error) {
      console.error('生成周报失败:', error);
    }
  };

  // 查看周报
  const viewReport = (record: any) => {
    currentReport.value = record;
    viewModalVisible.value = true;
  };

  // 编辑周报
  const editReport = (record: any) => {
    message.info('编辑周报功能');
  };

  // 删除周报
  const deleteReport = (record: any) => {
    message.info('删除周报功能');
  };

  // 导出周报
  const handleExport = () => {
    message.info('导出周报功能');
  };

  // 处理日期范围变化
  const handleDateRangeChange = (dates: any[]) => {
    filterReports();
  };

  // 处理状态过滤变化
  const handleStatusFilterChange = (status: string) => {
    filterReports();
  };

  // 过滤周报
  const filterReports = () => {
    let filtered = [...weeklyReports.value];

    // 日期过滤
    if (dateRange.value && dateRange.value.length === 2) {
      const [start, end] = dateRange.value;
      filtered = filtered.filter(report => {
        const reportDate = dayjs(report.submitTime);
        return reportDate.isAfter(start) && reportDate.isBefore(end);
      });
    }

    // 状态过滤
    if (filterStatus.value !== 'all') {
      filtered = filtered.filter(report => report.status === filterStatus.value);
    }

    filteredReports.value = filtered;
    pagination.value.total = filtered.length;
  };

  // 返回
  const goBack = () => {
    console.log('返回概览');
  };

  // 加载数据
  const loadData = async () => {
    loading.value = true;

    // 加载商机信息
    opportunityInfo.value = {
      businessNo: 'OPP-2024-0045',
      projectName: 'XX公司智慧园区建设项目',
      status: '1',
    };

    // 加载周报数据
    weeklyReports.value = [
      {
        id: '1',
        weekStart: '2024-01-15',
        weekEnd: '2024-01-21',
        workContent: '本周主要完成了系统架构设计和技术方案评审，与客户进行了两次技术交流会议，确认了核心功能模块的实现方案。',
        workHours: 42,
        achievements: '1. 完成系统架构设计文档 2. 通过技术方案评审 3. 获得客户技术认可',
        problems: '在数据库设计方面遇到性能优化问题，需要进一步调研解决方案。',
        nextWeekPlan: '1. 完成数据库设计优化 2. 开始详细设计文档编写 3. 准备技术演示',
        remark: '客户对当前进度表示满意',
        status: 'approved',
        submitTime: '2024-01-22 18:00:00',
        submitUser: '张三',
      },
      {
        id: '2',
        weekStart: '2024-01-08',
        weekEnd: '2024-01-14',
        workContent: '本周进行了项目需求调研和分析，完成了初步的技术方案设计，与产品经理确认了功能需求清单。',
        workHours: 38,
        achievements: '1. 完成需求调研报告 2. 制定技术方案框架 3. 确认功能需求清单',
        problems: '部分业务需求存在不明确之处，需要与客户进一步沟通确认。',
        nextWeekPlan: '1. 完善技术方案设计 2. 准备技术评审会议 3. 与客户沟通需求细节',
        remark: '整体进展顺利',
        status: 'submitted',
        submitTime: '2024-01-15 17:30:00',
        submitUser: '张三',
      },
      {
        id: '3',
        weekStart: '2024-01-01',
        weekEnd: '2024-01-07',
        workContent: '本周主要进行了项目启动准备工作，组建项目团队，制定项目计划，与客户进行了项目启动会议。',
        workHours: 35,
        achievements: '1. 完成项目团队组建 2. 制定项目计划 3. 召开项目启动会议',
        problems: '项目资源分配需要进一步优化，部分关键岗位人员配置不足。',
        nextWeekPlan: '1. 完善项目资源配置 2. 开始需求调研工作 3. 制定详细工作计划',
        remark: '项目正式启动',
        status: 'draft',
        submitTime: '2024-01-08 16:45:00',
        submitUser: '张三',
      },
    ];

    filteredReports.value = [...weeklyReports.value];
    pagination.value.total = weeklyReports.value.length;
    hasOpportunity.value = true;
    loading.value = false;
  };

  onMounted(() => {
    loadData();
  });
</script>

<style lang="less" scoped>
  .opportunity-weekly {
    padding: 20px;

    .page-header {
      margin-bottom: 24px;

      .header-content {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 20px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        .header-left {
          h1 {
            margin: 0 0 8px 0;
            font-size: 24px;
            font-weight: 500;
          }
        }
      }
    }

    .weekly-stats {
      margin-bottom: 24px;
    }

    .weekly-list {
      .ant-table {
        background: #fff;
        border-radius: 8px;
      }
    }

    .report-view {
      .report-content {
        h4 {
          margin-top: 16px;
          margin-bottom: 8px;
          color: #262626;
          font-size: 14px;
          font-weight: 500;
        }

        p {
          margin-bottom: 12px;
          line-height: 1.6;
          color: #666;
        }
      }
    }
  }
</style>
