<template>
  <div>
    <a-card :bordered="false" title="待填写商机跟踪记录">
      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleBatchSubmit" :disabled="!hasSelectedItems"> 批量提交 </a-button>
          <a-button @click="handleRefresh">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
        </a-space>
      </template>

      <a-spin :spinning="loading">
        <a-empty v-if="pendingList.length === 0" description="暂无待填写的跟踪记录" />

        <div v-else>
          <a-alert type="info" show-icon class="mb-4" message="系统每周自动生成待填写的商机跟踪记录，请及时填写。" />

          <a-table
            :dataSource="pendingList"
            :columns="columns"
            :pagination="{ pageSize: 10 }"
            :rowKey="record => record.id"
            :rowSelection="{
              selectedRowKeys: selectedRowKeys,
              onChange: onSelectChange,
              getCheckboxProps: getCheckboxProps,
            }">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'status'">
                <a-tag :color="record.status === '待填写' ? 'orange' : 'green'">
                  {{ record.status }}
                </a-tag>
              </template>

              <template v-if="column.key === 'content'">
                <a-input v-if="editableMap[record.id]" v-model:value="record.content" placeholder="请输入跟踪内容" :disabled="submittingMap[record.id]" />
                <span v-else>{{ record.content || '暂无内容' }}</span>
              </template>

              <template v-if="column.key === 'action'">
                <a-space>
                  <template v-if="editableMap[record.id]">
                    <a-button type="primary" size="small" @click="handleSaveItem(record)" :loading="submittingMap[record.id]"> 保存 </a-button>
                    <a-button size="small" @click="handleCancelEdit(record)" :disabled="submittingMap[record.id]"> 取消 </a-button>
                  </template>
                  <template v-else>
                    <a-button type="link" size="small" @click="handleEditItem(record)"> 编辑 </a-button>
                    <a-button type="link" size="small" @click="handleViewBusiness(record)"> 查看商机 </a-button>
                  </template>
                </a-space>
              </template>
            </template>
          </a-table>
        </div>
      </a-spin>
    </a-card>

    <a-modal v-model:visible="batchModalVisible" title="批量填写跟踪记录" @ok="confirmBatchSubmit" :confirmLoading="batchSubmitting" width="700px">
      <a-form :model="batchForm" layout="vertical">
        <a-form-item label="跟踪内容模板" name="contentTemplate" :rules="[{ required: true, message: '请输入跟踪内容模板' }]">
          <a-textarea
            v-model:value="batchForm.contentTemplate"
            placeholder="请输入跟踪内容模板，将应用于所有选中的记录"
            :rows="4"
            :disabled="batchSubmitting" />
        </a-form-item>

        <a-divider>选中的商机</a-divider>

        <a-table :dataSource="selectedRecords" :columns="batchColumns" :pagination="false" size="small" :rowKey="record => record.id">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'content'">
              <a-input v-model:value="record.content" placeholder="请输入跟踪内容" :disabled="batchSubmitting" />
            </template>
          </template>
        </a-table>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, reactive, onMounted } from 'vue';
  import { getPendingBusinessWeeklogs, updateBusinessWeeklog, batchUpdateBusinessWeeklogs, BusinessWeeklogModel } from '/@/api/project/businessWeeklog';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useRouter } from 'vue-router';
  import { ReloadOutlined } from '@ant-design/icons-vue';
  import { cloneDeep } from 'lodash-es';

  const { createMessage } = useMessage();
  const router = useRouter();
  const loading = ref(false);
  const pendingList = ref<BusinessWeeklogModel[]>([]);
  const originalData = ref<Record<string, BusinessWeeklogModel>>({});
  const editableMap = ref<Record<string, boolean>>({});
  const submittingMap = ref<Record<string, boolean>>({});
  const selectedRowKeys = ref<string[]>([]);
  const batchModalVisible = ref(false);
  const batchSubmitting = ref(false);
  const batchForm = reactive({
    contentTemplate: '',
  });

  // 表格列定义
  const columns = [
    {
      title: '商机名称',
      dataIndex: 'businessName',
      width: 200,
    },
    {
      title: '周次',
      key: 'week',
      customRender: ({ record }) => {
        return record.year && record.weekNo ? `${record.year}年第${record.weekNo}周` : '-';
      },
      width: 120,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
    },
    {
      title: '跟踪内容',
      dataIndex: 'content',
      key: 'content',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150,
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
    },
  ];

  // 批量表格列定义
  const batchColumns = [
    {
      title: '商机名称',
      dataIndex: 'businessName',
      width: 200,
    },
    {
      title: '周次',
      key: 'week',
      customRender: ({ record }) => {
        return record.year && record.weekNo ? `${record.year}年第${record.weekNo}周` : '-';
      },
      width: 120,
    },
    {
      title: '跟踪内容',
      dataIndex: 'content',
      key: 'content',
    },
  ];

  // 计算属性：是否有选中项
  const hasSelectedItems = computed(() => selectedRowKeys.value.length > 0);

  // 计算属性：选中的记录
  const selectedRecords = computed(() => {
    return pendingList.value.filter(item => selectedRowKeys.value.includes(item.id));
  });

  // 加载待填写的跟踪记录
  async function loadPendingRecords() {
    try {
      loading.value = true;
      const data = await getPendingBusinessWeeklogs();
      pendingList.value = data;

      // 保存原始数据，用于取消编辑
      originalData.value = {};
      data.forEach(item => {
        originalData.value[item.id] = cloneDeep(item);
      });

      // 重置编辑状态
      editableMap.value = {};
      submittingMap.value = {};
    } catch (error) {
      console.error('获取待填写跟踪记录失败:', error);
      createMessage.error('获取待填写跟踪记录失败');
    } finally {
      loading.value = false;
    }
  }

  // 选择变更处理
  function onSelectChange(keys: string[]) {
    selectedRowKeys.value = keys;
  }

  // 获取复选框属性
  function getCheckboxProps(record: BusinessWeeklogModel) {
    return {
      disabled: submittingMap.value[record.id] || record.status === '已填写',
    };
  }

  // 编辑单条记录
  function handleEditItem(record: BusinessWeeklogModel) {
    editableMap.value[record.id] = true;
  }

  // 取消编辑
  function handleCancelEdit(record: BusinessWeeklogModel) {
    // 恢复原始数据
    const index = pendingList.value.findIndex(item => item.id === record.id);
    if (index !== -1 && originalData.value[record.id]) {
      pendingList.value[index] = cloneDeep(originalData.value[record.id]);
    }

    // 取消编辑状态
    editableMap.value[record.id] = false;
  }

  // 保存单条记录
  async function handleSaveItem(record: BusinessWeeklogModel) {
    if (!record.content || record.content.trim() === '') {
      createMessage.warning('跟踪内容不能为空');
      return;
    }

    try {
      submittingMap.value[record.id] = true;

      await updateBusinessWeeklog(record.id, {
        businessId: record.businessId,
        content: record.content,
        weekNo: record.weekNo,
        year: record.year,
        month: record.month,
        day: record.day,
        status: '已填写',
      });

      // 更新状态
      record.status = '已填写';

      // 更新原始数据
      originalData.value[record.id] = cloneDeep(record);

      // 取消编辑状态
      editableMap.value[record.id] = false;

      createMessage.success('保存成功');
    } catch (error) {
      console.error('保存跟踪记录失败:', error);
      createMessage.error('保存跟踪记录失败');
    } finally {
      submittingMap.value[record.id] = false;
    }
  }

  // 查看商机详情
  function handleViewBusiness(record: BusinessWeeklogModel) {
    router.push({
      path: `/project/business/detail/${record.businessId}`,
    });
  }

  // 刷新列表
  function handleRefresh() {
    loadPendingRecords();
  }

  // 批量提交
  function handleBatchSubmit() {
    if (selectedRowKeys.value.length === 0) {
      createMessage.warning('请选择要批量填写的记录');
      return;
    }

    // 打开批量填写模态框
    batchForm.contentTemplate = '';
    batchModalVisible.value = true;
  }

  // 确认批量提交
  async function confirmBatchSubmit() {
    if (!batchForm.contentTemplate || batchForm.contentTemplate.trim() === '') {
      createMessage.warning('跟踪内容模板不能为空');
      return;
    }

    try {
      batchSubmitting.value = true;

      // 准备批量更新数据
      const updateParams = selectedRecords.value.map(record => ({
        id: record.id,
        businessId: record.businessId,
        content: record.content || batchForm.contentTemplate,
        weekNo: record.weekNo,
        year: record.year,
        month: record.month,
        day: record.day,
        status: '已填写',
      }));

      await batchUpdateBusinessWeeklogs(updateParams);

      // 更新本地数据状态
      selectedRecords.value.forEach(record => {
        record.status = '已填写';
        record.content = record.content || batchForm.contentTemplate;

        // 更新原始数据
        originalData.value[record.id] = cloneDeep(record);
      });

      // 清空选择
      selectedRowKeys.value = [];

      // 关闭模态框
      batchModalVisible.value = false;

      createMessage.success('批量提交成功');
    } catch (error) {
      console.error('批量提交跟踪记录失败:', error);
      createMessage.error('批量提交跟踪记录失败');
    } finally {
      batchSubmitting.value = false;
    }
  }

  onMounted(() => {
    loadPendingRecords();
  });
</script>

<style lang="less" scoped>
  .mb-4 {
    margin-bottom: 16px;
  }
</style>
