package com.xinghuo.project.template.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.template.dao.TemplateRelationMapper;
import com.xinghuo.project.template.entity.TemplateRelationEntity;
import com.xinghuo.project.template.service.TemplateRelationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 通用模板关联服务实现类
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Service
public class TemplateRelationServiceImpl extends BaseServiceImpl<TemplateRelationMapper, TemplateRelationEntity> implements TemplateRelationService {

    @Override
    public List<String> getTargetTemplateIds(String sourceTemplateId, String sourceTemplateType, String targetTemplateType) {
        if (StrXhUtil.isEmpty(sourceTemplateId) || StrXhUtil.isEmpty(sourceTemplateType) || StrXhUtil.isEmpty(targetTemplateType)) {
            return new ArrayList<>();
        }

        QueryWrapper<TemplateRelationEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                   .select(TemplateRelationEntity::getTargetTemplateId)
                   .eq(TemplateRelationEntity::getSourceTemplateId, sourceTemplateId)
                   .eq(TemplateRelationEntity::getSourceTemplateType, sourceTemplateType)
                   .eq(TemplateRelationEntity::getTargetTemplateType, targetTemplateType);

        List<TemplateRelationEntity> relations = this.list(queryWrapper);
        return relations.stream()
                       .map(TemplateRelationEntity::getTargetTemplateId)
                       .collect(Collectors.toList());
    }

    @Override
    public List<String> getSourceTemplateIds(String targetTemplateId, String targetTemplateType, String sourceTemplateType) {
        if (StrXhUtil.isEmpty(targetTemplateId) || StrXhUtil.isEmpty(targetTemplateType) || StrXhUtil.isEmpty(sourceTemplateType)) {
            return new ArrayList<>();
        }

        QueryWrapper<TemplateRelationEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                   .select(TemplateRelationEntity::getSourceTemplateId)
                   .eq(TemplateRelationEntity::getTargetTemplateId, targetTemplateId)
                   .eq(TemplateRelationEntity::getTargetTemplateType, targetTemplateType)
                   .eq(TemplateRelationEntity::getSourceTemplateType, sourceTemplateType);

        List<TemplateRelationEntity> relations = this.list(queryWrapper);
        return relations.stream()
                       .map(TemplateRelationEntity::getSourceTemplateId)
                       .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTemplateRelations(String sourceTemplateId, String sourceTemplateType, 
                                       List<String> targetTemplateIds, String targetTemplateType) {
        if (StrXhUtil.isEmpty(sourceTemplateId) || StrXhUtil.isEmpty(sourceTemplateType) || 
            StrXhUtil.isEmpty(targetTemplateType)) {
            log.warn("更新模板关联关系参数无效");
            throw new RuntimeException("参数无效");
        }

        // 删除原有关联关系
        deleteBySourceTemplate(sourceTemplateId, sourceTemplateType);

        // 保存新的关联关系
        if (targetTemplateIds != null && !targetTemplateIds.isEmpty()) {
            List<TemplateRelationEntity> relations = new ArrayList<>();
            for (String targetTemplateId : targetTemplateIds) {
                TemplateRelationEntity relation = new TemplateRelationEntity();
                relation.setId(RandomUtil.snowId());
                relation.setSourceTemplateId(sourceTemplateId);
                relation.setSourceTemplateType(sourceTemplateType);
                relation.setTargetTemplateId(targetTemplateId);
                relation.setTargetTemplateType(targetTemplateType);
                relations.add(relation);
            }
            this.saveBatch(relations);
        }

        log.info("更新模板关联关系成功, 源模板ID: {}, 目标模板数量: {}", 
                sourceTemplateId, targetTemplateIds != null ? targetTemplateIds.size() : 0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBySourceTemplate(String sourceTemplateId, String sourceTemplateType) {
        if (StrXhUtil.isEmpty(sourceTemplateId) || StrXhUtil.isEmpty(sourceTemplateType)) {
            log.warn("删除源模板关联关系参数无效");
            return;
        }

        QueryWrapper<TemplateRelationEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                   .eq(TemplateRelationEntity::getSourceTemplateId, sourceTemplateId)
                   .eq(TemplateRelationEntity::getSourceTemplateType, sourceTemplateType);

        long count = this.count(queryWrapper);
        this.remove(queryWrapper);

        log.info("删除源模板关联关系成功, 源模板ID: {}, 源模板类型: {}, 删除数量: {}",
                sourceTemplateId, sourceTemplateType, count);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByTargetTemplate(String targetTemplateId, String targetTemplateType) {
        if (StrXhUtil.isEmpty(targetTemplateId) || StrXhUtil.isEmpty(targetTemplateType)) {
            log.warn("删除目标模板关联关系参数无效");
            return;
        }

        QueryWrapper<TemplateRelationEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                   .eq(TemplateRelationEntity::getTargetTemplateId, targetTemplateId)
                   .eq(TemplateRelationEntity::getTargetTemplateType, targetTemplateType);

        long count = this.count(queryWrapper);
        this.remove(queryWrapper);

        log.info("删除目标模板关联关系成功, 目标模板ID: {}, 目标模板类型: {}, 删除数量: {}",
                targetTemplateId, targetTemplateType, count);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addTemplateRelation(String sourceTemplateId, String sourceTemplateType, 
                                   String targetTemplateId, String targetTemplateType) {
        if (StrXhUtil.isEmpty(sourceTemplateId) || StrXhUtil.isEmpty(sourceTemplateType) ||
            StrXhUtil.isEmpty(targetTemplateId) || StrXhUtil.isEmpty(targetTemplateType)) {
            log.warn("添加模板关联关系参数无效");
            throw new RuntimeException("参数无效");
        }

        // 检查关联关系是否已存在
        if (isRelationExists(sourceTemplateId, sourceTemplateType, targetTemplateId, targetTemplateType)) {
            log.warn("模板关联关系已存在");
            return;
        }

        TemplateRelationEntity relation = new TemplateRelationEntity();
        relation.setId(RandomUtil.snowId());
        relation.setSourceTemplateId(sourceTemplateId);
        relation.setSourceTemplateType(sourceTemplateType);
        relation.setTargetTemplateId(targetTemplateId);
        relation.setTargetTemplateType(targetTemplateType);

        this.save(relation);
        log.info("添加模板关联关系成功, 源模板ID: {}, 目标模板ID: {}", sourceTemplateId, targetTemplateId);
    }

    @Override
    public boolean isRelationExists(String sourceTemplateId, String sourceTemplateType,
                                   String targetTemplateId, String targetTemplateType) {
        if (StrXhUtil.isEmpty(sourceTemplateId) || StrXhUtil.isEmpty(sourceTemplateType) ||
            StrXhUtil.isEmpty(targetTemplateId) || StrXhUtil.isEmpty(targetTemplateType)) {
            return false;
        }

        QueryWrapper<TemplateRelationEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                   .eq(TemplateRelationEntity::getSourceTemplateId, sourceTemplateId)
                   .eq(TemplateRelationEntity::getSourceTemplateType, sourceTemplateType)
                   .eq(TemplateRelationEntity::getTargetTemplateId, targetTemplateId)
                   .eq(TemplateRelationEntity::getTargetTemplateType, targetTemplateType);

        long count = this.count(queryWrapper);
        return count > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeTemplateRelation(String sourceTemplateId, String sourceTemplateType,
                                      String targetTemplateId, String targetTemplateType) {
        if (StrXhUtil.isEmpty(sourceTemplateId) || StrXhUtil.isEmpty(sourceTemplateType) ||
            StrXhUtil.isEmpty(targetTemplateId) || StrXhUtil.isEmpty(targetTemplateType)) {
            log.warn("删除模板关联关系参数无效");
            throw new RuntimeException("参数无效");
        }

        QueryWrapper<TemplateRelationEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                   .eq(TemplateRelationEntity::getSourceTemplateId, sourceTemplateId)
                   .eq(TemplateRelationEntity::getSourceTemplateType, sourceTemplateType)
                   .eq(TemplateRelationEntity::getTargetTemplateId, targetTemplateId)
                   .eq(TemplateRelationEntity::getTargetTemplateType, targetTemplateType);

        long count = this.count(queryWrapper);
        this.remove(queryWrapper);

        log.info("删除模板关联关系成功, 源模板ID: {}, 目标模板ID: {}, 删除数量: {}",
                sourceTemplateId, targetTemplateId, count);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteBySourceTemplateIds(List<String> sourceTemplateIds, String sourceTemplateType) {
        if (sourceTemplateIds == null || sourceTemplateIds.isEmpty() || StrXhUtil.isEmpty(sourceTemplateType)) {
            log.warn("批量删除源模板关联关系参数无效");
            return;
        }

        QueryWrapper<TemplateRelationEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                   .in(TemplateRelationEntity::getSourceTemplateId, sourceTemplateIds)
                   .eq(TemplateRelationEntity::getSourceTemplateType, sourceTemplateType);

        long count = this.count(queryWrapper);
        this.remove(queryWrapper);

        log.info("批量删除源模板关联关系成功, 源模板类型: {}, 删除数量: {}", sourceTemplateType, count);
    }

    @Override
    public List<TemplateRelationEntity> getBySourceTemplateType(String sourceTemplateType) {
        if (StrXhUtil.isEmpty(sourceTemplateType)) {
            return new ArrayList<>();
        }

        QueryWrapper<TemplateRelationEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(TemplateRelationEntity::getSourceTemplateType, sourceTemplateType);

        return this.list(queryWrapper);
    }

    @Override
    public List<TemplateRelationEntity> getByTargetTemplateType(String targetTemplateType) {
        if (StrXhUtil.isEmpty(targetTemplateType)) {
            return new ArrayList<>();
        }

        QueryWrapper<TemplateRelationEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(TemplateRelationEntity::getTargetTemplateType, targetTemplateType);

        return this.list(queryWrapper);
    }
}
