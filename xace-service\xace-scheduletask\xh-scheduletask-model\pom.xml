<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>xh-scheduletask-starter</artifactId>
        <groupId>com.xinghuo.xace</groupId>
        <version>1.2-RELEASE</version>
    </parent>
    <artifactId>xh-scheduletask-model</artifactId>
    <packaging>jar</packaging>
    <description>共同模型</description>

    <properties>
        <!--		<mybatis-plus.version>*******</mybatis-plus.version>-->
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-annotation</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.32</version>
        </dependency>

         <dependency>
             <groupId>com.xinghuo.xace</groupId>
            <artifactId>xh-common-database</artifactId>
             <version>2.1.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
            <version>8.0.1.Final</version>
        </dependency>


    </dependencies>

</project>
