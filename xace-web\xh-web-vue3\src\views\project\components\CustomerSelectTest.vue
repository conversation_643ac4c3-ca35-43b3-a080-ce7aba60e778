<template>
  <div style="padding: 20px">
    <h3>客户单位选择组件测试</h3>

    <div style="margin-bottom: 20px">
      <p>测试组件已加载成功！</p>
      <p>当前时间: {{ currentTime }}</p>
    </div>

    <div style="margin-bottom: 20px">
      <h4>甲方单位选择 (custType="JIAFANG")</h4>
      <CustomerSelect v-model:value="selectedCustomer1" custType="JIAFANG" placeholder="请选择甲方单位" @change="handleCustomerChange" />
      <p>选中的值: {{ selectedCustomer1 }}</p>
    </div>

    <div style="margin-bottom: 20px">
      <h4>监理单位选择 (custType="JIANLI")</h4>
      <CustomerSelect v-model:value="selectedCustomer2" custType="JIANLI" placeholder="请选择监理单位" @change="handleCustomerChange" />
      <p>选中的值: {{ selectedCustomer2 }}</p>
    </div>

    <div style="margin-bottom: 20px">
      <h4>三方测评单位选择 (custType="CEPING")</h4>
      <CustomerSelect v-model:value="selectedCustomer3" custType="CEPING" placeholder="请选择三方测评单位" @change="handleCustomerChange" />
      <p>选中的值: {{ selectedCustomer3 }}</p>
    </div>

    <div style="margin-bottom: 20px">
      <h4>客户单位联系人选择 (甲方)</h4>
      <CustomerLinkmanSelect
        v-model:value="selectedLinkman1"
        custType="JIAFANG"
        customerPlaceholder="请选择甲方单位"
        linkmanPlaceholder="请选择联系人"
        @change="handleLinkmanChange" />
      <p>选中的值: {{ JSON.stringify(selectedLinkman1) }}</p>
    </div>

    <div style="margin-bottom: 20px">
      <h4>监理单位联系人选择</h4>
      <CustomerLinkmanSelect
        v-model:value="selectedLinkman2"
        custType="JIANLI"
        customerPlaceholder="请选择监理单位"
        linkmanPlaceholder="请选择联系人"
        @change="handleLinkmanChange" />
      <p>选中的值: {{ JSON.stringify(selectedLinkman2) }}</p>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { CustomerSelect, CustomerLinkmanSelect } from './index';

  const currentTime = ref(new Date().toLocaleString());
  const selectedCustomer1 = ref('');
  const selectedCustomer2 = ref('');
  const selectedCustomer3 = ref('');
  const selectedLinkman1 = ref({});
  const selectedLinkman2 = ref({});

  function handleCustomerChange(value: any, selectedData: any) {
    console.log('客户单位选择变更:', value, selectedData);
  }

  function handleLinkmanChange(value: any) {
    console.log('联系人选择变更:', value);
  }
</script>
