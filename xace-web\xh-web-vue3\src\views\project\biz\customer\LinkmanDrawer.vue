<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" :title="getTitle" width="500px" @ok="handleSubmit" showFooter>
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>

<script lang="ts" setup>
  import { computed, ref, unref } from 'vue';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { BasicForm, useForm } from '/@/components/Form';
  import { FormSchema } from '/@/components/Form/src/types/form';
  import { createCustomerLinkman, updateCustomerLinkman, ProjCustomerLinkmanModel } from '/@/api/project/customerLinkman';
  import { useMessage } from '/@/hooks/web/useMessage';

  const emit = defineEmits(['register', 'success']);
  const { createMessage } = useMessage();
  const isUpdate = ref(false);
  const linkmanId = ref('');
  const customerId = ref('');
  const customerName = ref('');

  // 表单配置
  const formSchema: FormSchema[] = [
    {
      field: 'customerInfo',
      label: '所属单位',
      component: 'Input',
      componentProps: {
        disabled: true,
      },
      show: ({ values }) => !!customerName.value,
    },
    {
      field: 'cuId',
      label: '客户ID',
      component: 'Input',
      show: false, // 隐藏字段，但保留值
    },
    {
      field: 'linkman',
      label: '联系人姓名',
      component: 'Input',
      required: true,
      componentProps: {
        placeholder: '请输入联系人姓名',
        maxlength: 50,
      },
      rules: [{ required: true, message: '请输入联系人姓名' }],
    },
    {
      field: 'telephone',
      label: '联系电话',
      component: 'Input',
      required: true,
      componentProps: {
        placeholder: '请输入联系电话',
        maxlength: 20,
      },
      rules: [{ required: true, message: '请输入联系电话' }],
    },
    {
      field: 'topic',
      label: '岗位',
      component: 'Input',
      componentProps: {
        placeholder: '请输入岗位',
        maxlength: 50,
      },
    },
    {
      field: 'status',
      label: '状态',
      component: 'RadioGroup',
      defaultValue: 1,
      required: true,
      componentProps: {
        options: [
          { label: '有效', value: 1 },
          { label: '无效', value: 0 },
        ],
      },
      rules: [{ required: true, message: '请选择状态' }],
    },
    {
      field: 'content',
      label: '内容',
      component: 'InputTextArea',
      componentProps: {
        placeholder: '请输入内容',
        rows: 3,
        maxlength: 500,
        showCount: true,
      },
    },
    {
      field: 'note',
      label: '备注',
      component: 'InputTextArea',
      componentProps: {
        placeholder: '请输入备注',
        rows: 3,
        maxlength: 500,
        showCount: true,
      },
    },
  ];

  // 注册表单
  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 100,
    schemas: formSchema,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });

  // 注册抽屉
  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async data => {
    resetFields();
    setDrawerProps({ confirmLoading: false });

    isUpdate.value = !!data?.isUpdate;
    customerId.value = data.customerId || '';
    customerName.value = data.customerName || '';

    if (unref(isUpdate)) {
      linkmanId.value = data.record.id;
      setFieldsValue({
        ...data.record,
        customerInfo: customerName.value,
      });
    } else {
      // 新增时设置默认值
      setFieldsValue({
        cuId: customerId.value,
        customerInfo: customerName.value,
        status: 1, // 默认状态为有效
      });
    }
  });

  // 获取标题
  const getTitle = computed(() => {
    return unref(isUpdate) ? '编辑联系人' : '新增联系人';
  });

  // 提交表单
  async function handleSubmit() {
    try {
      const values = await validate();
      setDrawerProps({ confirmLoading: true });

      // 确保设置了客户ID
      if (!values.cuId) {
        values.cuId = customerId.value;
      }

      // 移除显示字段，不提交到后端
      const submitData = { ...values };
      delete submitData.customerInfo;

      console.log('提交数据:', submitData);

      if (unref(isUpdate)) {
        await updateCustomerLinkman(linkmanId.value, submitData);
        createMessage.success('更新成功');
      } else {
        await createCustomerLinkman(submitData);
        createMessage.success('新增成功');
      }

      closeDrawer();
      emit('success');
    } catch (error) {
      console.error('表单验证失败或提交出错:', error);
      createMessage.error('操作失败，请检查输入信息');
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }

  // 注意：移除了动态更新customerInfo字段的watch，
  // 因为该字段已经在formSchema中通过props.customerName进行了初始化
</script>
