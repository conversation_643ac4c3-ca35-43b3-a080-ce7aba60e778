<template>
  <a-modal v-model:visible="modalVisible" :width="1200" title="合同详情" :footer="null" :mask-closable="false" @cancel="handleClose">
    <div class="contract-detail-modal">
      <a-spin :spinning="loading">
        <a-tabs v-model:active-key="activeTab" type="card">
          <!-- 基本信息 -->
          <a-tab-pane key="basic" tab="基本信息">
            <a-descriptions :column="2" bordered>
              <a-descriptions-item label="合同名称" :span="2">
                <span class="font-medium">{{ contractData?.name || '-' }}</span>
              </a-descriptions-item>
              <a-descriptions-item label="合同编号">
                <a-tag color="blue">{{ contractData?.cno || '-' }}</a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="合同金额">
                <span class="text-blue-600 font-medium text-lg">
                  {{ formatMoney(contractData?.amount) }}
                </span>
              </a-descriptions-item>
              <a-descriptions-item label="客户单位">
                {{ contractData?.custName || '-' }}
              </a-descriptions-item>
              <a-descriptions-item label="负责人">
                {{ contractData?.ownName || '-' }}
              </a-descriptions-item>
              <a-descriptions-item label="所属部门">
                {{ contractData?.deptName || '-' }}
              </a-descriptions-item>
              <a-descriptions-item label="终端用户">
                {{ contractData?.finalUserName || '-' }}
              </a-descriptions-item>
              <a-descriptions-item label="合同状态">
                <a-tag :color="getStatusColor(contractData?.contractStatus)">
                  {{ getStatusText(contractData?.contractStatus) }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="收款状态">
                <a-tag :color="getMoneyStatusColor(contractData?.moneyStatus)">
                  {{ getMoneyStatusText(contractData?.moneyStatus) }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="已收金额">
                <span class="text-green-600 font-medium">
                  {{ formatMoney(contractData?.ysAmount) }}
                </span>
              </a-descriptions-item>
              <a-descriptions-item label="收款进度">
                <div class="flex items-center space-x-2">
                  <a-progress
                    :percent="getReceivedRate()"
                    :size="['small', 'default']"
                    :stroke-color="getProgressColor(getReceivedRate())"
                    style="width: 120px" />
                  <span class="text-sm text-gray-500">{{ getReceivedRate() }}%</span>
                </div>
              </a-descriptions-item>
              <a-descriptions-item label="外协金额">
                <span class="text-orange-600 font-medium">
                  {{ formatMoney(contractData?.externalAmount) }}
                </span>
              </a-descriptions-item>
              <a-descriptions-item label="备注" :span="2">
                {{ contractData?.note || '-' }}
              </a-descriptions-item>
            </a-descriptions>
          </a-tab-pane>

          <!-- 时间管理 -->
          <a-tab-pane key="dates" tab="时间管理">
            <a-descriptions :column="2" bordered>
              <a-descriptions-item label="签订日期">
                {{ formatDate(contractData?.signDate) }}
              </a-descriptions-item>
              <a-descriptions-item label="签订年份">
                {{ contractData?.signYear || '-' }}
              </a-descriptions-item>
              <a-descriptions-item label="招标日期">
                {{ formatDate(contractData?.bidDate) }}
              </a-descriptions-item>
              <a-descriptions-item label="开工日期">
                {{ formatDate(contractData?.commencementDate) }}
              </a-descriptions-item>
              <a-descriptions-item label="初检日期">
                {{ formatDate(contractData?.initialCheckDate) }}
              </a-descriptions-item>
              <a-descriptions-item label="终检日期">
                {{ formatDate(contractData?.finalCheckDate) }}
              </a-descriptions-item>
              <a-descriptions-item label="审计日期">
                {{ formatDate(contractData?.auditDate) }}
              </a-descriptions-item>
              <a-descriptions-item label="合同开始日期">
                {{ formatDate(contractData?.cstartDate) }}
              </a-descriptions-item>
              <a-descriptions-item label="合同结束日期">
                {{ formatDate(contractData?.cendDate) }}
              </a-descriptions-item>
              <a-descriptions-item label="监理开始日期">
                {{ formatDate(contractData?.mstartDate) }}
              </a-descriptions-item>
              <a-descriptions-item label="监理结束日期">
                {{ formatDate(contractData?.mendDate) }}
              </a-descriptions-item>
              <a-descriptions-item label="合同时长">
                {{ calculateContractDuration() }}
              </a-descriptions-item>
            </a-descriptions>
          </a-tab-pane>

          <!-- 联系信息 -->
          <a-tab-pane key="contact" tab="联系信息">
            <a-descriptions :column="2" bordered>
              <a-descriptions-item label="主要联系人">
                {{ contractData?.linkman || '-' }}
              </a-descriptions-item>
              <a-descriptions-item label="联系电话">
                {{ contractData?.linkTelephone || '-' }}
              </a-descriptions-item>
            </a-descriptions>

            <a-divider>合作单位信息</a-divider>

            <a-descriptions :column="1" bordered>
              <a-descriptions-item label="勘察部门">
                <div class="cooperation-info">
                  <div><strong>部门:</strong> {{ contractData?.svDeptName || '-' }}</div>
                  <div><strong>联系人:</strong> {{ contractData?.svLinkman || '-' }}</div>
                  <div><strong>电话:</strong> {{ contractData?.svTelephone || '-' }}</div>
                </div>
              </a-descriptions-item>
              <a-descriptions-item label="审查部门">
                <div class="cooperation-info">
                  <div><strong>部门:</strong> {{ contractData?.reviewDeptName || '-' }}</div>
                  <div><strong>联系人:</strong> {{ contractData?.reviewLinkman || '-' }}</div>
                  <div><strong>电话:</strong> {{ contractData?.reviewTelephone || '-' }}</div>
                </div>
              </a-descriptions-item>
              <a-descriptions-item label="监理部门">
                <div class="cooperation-info">
                  <div><strong>部门:</strong> {{ contractData?.jsDeptName || '-' }}</div>
                  <div><strong>联系人:</strong> {{ contractData?.jsLinkman || '-' }}</div>
                  <div><strong>电话:</strong> {{ contractData?.jsTelephone || '-' }}</div>
                </div>
              </a-descriptions-item>
            </a-descriptions>
          </a-tab-pane>

          <!-- 成本分析 -->
          <a-tab-pane key="cost" tab="成本分析">
            <a-row :gutter="24">
              <a-col :span="12">
                <a-card title="毛利分析" size="small">
                  <a-descriptions :column="1" size="small">
                    <a-descriptions-item label="预估毛利">
                      <span class="text-blue-600 font-medium">
                        {{ formatMoney(contractData?.estProbit) }}
                      </span>
                    </a-descriptions-item>
                    <a-descriptions-item label="预估毛利率">
                      <span class="text-blue-600 font-medium">
                        {{ formatPercent(contractData?.estProbitRatio) }}
                      </span>
                    </a-descriptions-item>
                    <a-descriptions-item label="实际毛利">
                      <span class="text-green-600 font-medium">
                        {{ formatMoney(contractData?.actProbit) }}
                      </span>
                    </a-descriptions-item>
                    <a-descriptions-item label="实际毛利率">
                      <span class="text-green-600 font-medium">
                        {{ formatPercent(contractData?.actProbitRatio) }}
                      </span>
                    </a-descriptions-item>
                  </a-descriptions>
                </a-card>
              </a-col>
              <a-col :span="12">
                <a-card title="成本构成" size="small">
                  <a-descriptions :column="1" size="small">
                    <a-descriptions-item label="预估外协金额">
                      <span class="text-orange-600 font-medium">
                        {{ formatMoney(contractData?.evaExternalAmount) }}
                      </span>
                    </a-descriptions-item>
                    <a-descriptions-item label="预估成本金额">
                      <span class="text-red-600 font-medium">
                        {{ formatMoney(contractData?.evaCostAmount) }}
                      </span>
                    </a-descriptions-item>
                    <a-descriptions-item label="成本合计">
                      <span class="text-red-600 font-medium">
                        {{ formatMoney(getTotalCost()) }}
                      </span>
                    </a-descriptions-item>
                    <a-descriptions-item label="成本率">
                      <span class="text-red-600 font-medium">
                        {{ formatPercent(getCostRatio()) }}
                      </span>
                    </a-descriptions-item>
                  </a-descriptions>
                </a-card>
              </a-col>
            </a-row>

            <a-divider>部门分配金额</a-divider>

            <a-descriptions :column="2" bordered>
              <a-descriptions-item label="一部分配金额">
                <span class="text-blue-600 font-medium">
                  {{ formatMoney(contractData?.yfYbAmount) }}
                </span>
              </a-descriptions-item>
              <a-descriptions-item label="二部分配金额">
                <span class="text-blue-600 font-medium">
                  {{ formatMoney(contractData?.yfEbAmount) }}
                </span>
              </a-descriptions-item>
              <a-descriptions-item label="监理分配金额">
                <span class="text-blue-600 font-medium">
                  {{ formatMoney(contractData?.yfJfAmount) }}
                </span>
              </a-descriptions-item>
              <a-descriptions-item label="其他分配金额">
                <span class="text-blue-600 font-medium">
                  {{ formatMoney(contractData?.yfOtherAmount) }}
                </span>
              </a-descriptions-item>
              <a-descriptions-item label="分配金额合计" :span="2">
                <span class="text-blue-600 font-medium text-lg">
                  {{ formatMoney(getTotalAllocated()) }}
                </span>
              </a-descriptions-item>
            </a-descriptions>
          </a-tab-pane>

          <!-- 操作日志 -->
          <a-tab-pane key="logs" tab="操作日志">
            <a-descriptions :column="2" bordered>
              <a-descriptions-item label="创建时间">
                {{ formatDateTime(contractData?.createTime) }}
              </a-descriptions-item>
              <a-descriptions-item label="更新时间">
                {{ formatDateTime(contractData?.updateTime) }}
              </a-descriptions-item>
              <a-descriptions-item label="是否继续" :span="2">
                <a-tag :color="contractData?.isContinue === 1 ? 'success' : 'default'">
                  {{ contractData?.isContinueText || (contractData?.isContinue === 1 ? '是' : '否') }}
                </a-tag>
              </a-descriptions-item>
            </a-descriptions>

            <!-- 这里可以后续扩展显示详细的操作日志记录 -->
            <div class="mt-4">
              <a-empty description="暂无操作日志记录" />
            </div>
          </a-tab-pane>
        </a-tabs>
      </a-spin>
    </div>

    <template #footer>
      <div class="flex justify-between">
        <a-space>
          <a-button @click="handleEdit" type="primary">编辑合同</a-button>
          <a-button @click="handleViewMoney" type="default">查看收款</a-button>
        </a-space>
        <a-button @click="handleClose">关闭</a-button>
      </div>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, computed } from 'vue';
  import { formatToDate, formatToDateTime } from '/@/utils/dateUtil';
  import type { ContractVO } from '/@/api/project/contract';
  import dayjs from 'dayjs';

  // Props & Emits
  const props = defineProps<{
    visible: boolean;
    contractData: ContractVO | null;
  }>();

  const emit = defineEmits<{
    'update:visible': [value: boolean];
    close: [];
    edit: [data: ContractVO];
    viewMoney: [contractId: string];
  }>();

  // Reactive state
  const loading = ref(false);
  const activeTab = ref('basic');

  const modalVisible = computed({
    get: () => props.visible,
    set: value => emit('update:visible', value),
  });

  // Utility methods
  const formatMoney = (amount: number | string | null | undefined) => {
    if (!amount || amount === 0) return '-';
    return (
      '¥ ' +
      Number(amount).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      })
    );
  };

  const formatPercent = (ratio: number | string | null | undefined) => {
    if (ratio === null || ratio === undefined || ratio === '') return '-';
    return Number(ratio).toFixed(2) + '%';
  };

  const formatDate = (date: string | Date | null | undefined) => {
    return date ? formatToDate(date) : '-';
  };

  const formatDateTime = (dateTime: string | Date | null | undefined) => {
    return dateTime ? formatToDateTime(dateTime) : '-';
  };

  // Status related methods
  const getStatusColor = (status: string | undefined) => {
    const colorMap: Record<string, string> = {
      draft: 'default',
      signed: 'blue',
      executing: 'processing',
      completed: 'success',
      terminated: 'error',
    };
    return colorMap[status || ''] || 'default';
  };

  const getStatusText = (status: string | undefined) => {
    const textMap: Record<string, string> = {
      draft: '草稿',
      signed: '已签约',
      executing: '执行中',
      completed: '已完成',
      terminated: '已终止',
    };
    return textMap[status || ''] || status || '-';
  };

  const getMoneyStatusColor = (status: string | undefined) => {
    const colorMap: Record<string, string> = {
      unpaid: 'default',
      partial: 'warning',
      paid: 'success',
    };
    return colorMap[status || ''] || 'default';
  };

  const getMoneyStatusText = (status: string | undefined) => {
    const textMap: Record<string, string> = {
      unpaid: '未收款',
      partial: '部分收款',
      paid: '已收款',
    };
    return textMap[status || ''] || status || '-';
  };

  // Progress calculation
  const getReceivedRate = () => {
    const { contractData } = props;
    if (!contractData?.amount || contractData.amount === 0) return 0;
    return Math.round(((contractData.ysAmount || 0) / contractData.amount) * 100);
  };

  const getProgressColor = (percent: number) => {
    if (percent >= 100) return '#52c41a';
    if (percent >= 80) return '#1890ff';
    if (percent >= 50) return '#faad14';
    return '#f5222d';
  };

  // Cost analysis calculations
  const getTotalCost = () => {
    const { contractData } = props;
    if (!contractData) return 0;

    const externalAmount = contractData.evaExternalAmount || 0;
    const costAmount = contractData.evaCostAmount || 0;
    return externalAmount + costAmount;
  };

  const getCostRatio = () => {
    const { contractData } = props;
    if (!contractData?.amount || contractData.amount === 0) return 0;

    const totalCost = getTotalCost();
    return (totalCost / contractData.amount) * 100;
  };

  const getTotalAllocated = () => {
    const { contractData } = props;
    if (!contractData) return 0;

    const ybAmount = contractData.yfYbAmount || 0;
    const ebAmount = contractData.yfEbAmount || 0;
    const jfAmount = contractData.yfJfAmount || 0;
    const otherAmount = contractData.yfOtherAmount || 0;

    return ybAmount + ebAmount + jfAmount + otherAmount;
  };

  // Duration calculation
  const calculateContractDuration = () => {
    const { contractData } = props;
    if (!contractData?.cstartDate || !contractData?.cendDate) return '-';

    const startDate = dayjs(contractData.cstartDate);
    const endDate = dayjs(contractData.cendDate);
    const duration = endDate.diff(startDate, 'days');

    if (duration > 365) {
      const years = Math.floor(duration / 365);
      const months = Math.floor((duration % 365) / 30);
      return `${years}年${months}个月`;
    } else if (duration > 30) {
      const months = Math.floor(duration / 30);
      const days = duration % 30;
      return `${months}个月${days}天`;
    } else {
      return `${duration}天`;
    }
  };

  // Event handlers
  const handleClose = () => {
    activeTab.value = 'basic';
    emit('close');
  };

  const handleEdit = () => {
    if (props.contractData) {
      emit('edit', props.contractData);
    }
  };

  const handleViewMoney = () => {
    if (props.contractData?.id) {
      emit('viewMoney', props.contractData.id);
    }
  };
</script>

<style lang="less" scoped>
  .contract-detail-modal {
    .ant-tabs-content-holder {
      max-height: 600px;
      overflow-y: auto;
    }

    .cooperation-info {
      div {
        margin-bottom: 4px;

        strong {
          display: inline-block;
          width: 60px;
          color: #666;
        }
      }
    }

    // 金额颜色
    .text-blue-600 {
      color: #1890ff;
    }

    .text-green-600 {
      color: #52c41a;
    }

    .text-orange-600 {
      color: #fa8c16;
    }

    .text-red-600 {
      color: #ff4d4f;
    }

    .text-gray-500 {
      color: #8c8c8c;
    }

    // 字体样式
    .font-medium {
      font-weight: 500;
    }

    .text-lg {
      font-size: 16px;
    }

    .text-sm {
      font-size: 12px;
    }

    // 布局样式
    .flex {
      display: flex;
    }

    .items-center {
      align-items: center;
    }

    .justify-between {
      justify-content: space-between;
    }

    .space-x-2 > * + * {
      margin-left: 8px;
    }

    .mt-4 {
      margin-top: 16px;
    }

    // 描述列表样式
    :deep(.ant-descriptions-item-label) {
      font-weight: 500;
      background-color: #fafafa;
    }

    :deep(.ant-descriptions-item-content) {
      background-color: #fff;
    }

    // 卡片样式
    .ant-card {
      .ant-card-head {
        min-height: 40px;

        .ant-card-head-title {
          font-size: 14px;
          font-weight: 500;
        }
      }
    }

    // 进度条样式
    :deep(.ant-progress) {
      .ant-progress-text {
        font-size: 12px;
      }
    }

    // 标签样式
    :deep(.ant-tag) {
      border-radius: 4px;
      font-size: 12px;
    }
  }
</style>
