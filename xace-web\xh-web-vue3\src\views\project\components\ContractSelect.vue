<template>
  <a-select
    v-model:value="currentValue"
    :placeholder="placeholder"
    :allowClear="allowClear"
    :disabled="disabled"
    :loading="loading"
    show-search
    :filter-option="false"
    :not-found-content="loading ? undefined : '暂无数据'"
    @search="handleSearch"
    @change="handleChange"
    @clear="handleClear">
    <a-select-option v-for="item in options" :key="item.id" :value="item.id" :title="item.fullName">
      {{ item.fullName }}
    </a-select-option>
  </a-select>
</template>

<script lang="ts" setup>
  import { ref, watch, onMounted } from 'vue';
  import { getContractSelector } from '/@/api/project/contract';
  import { useDebounceFn } from '@vueuse/core';

  interface ContractOption {
    id: string;
    fullName: string;
    cNo: string;
    name: string;
  }

  interface Props {
    value?: string;
    placeholder?: string;
    allowClear?: boolean;
    disabled?: boolean;
  }

  interface Emits {
    (e: 'update:value', value: string | undefined): void;
    (e: 'change', value: string | undefined, option?: ContractOption): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    placeholder: '请选择合同',
    allowClear: true,
    disabled: false,
  });

  const emit = defineEmits<Emits>();

  const currentValue = ref<string | undefined>(props.value);
  const options = ref<ContractOption[]>([]);
  const loading = ref(false);

  // 监听外部值变化
  watch(
    () => props.value,
    async (newVal, oldVal) => {
      currentValue.value = newVal;

      // 如果有新值且与旧值不同，需要确保选项列表中包含该值
      if (newVal && newVal !== oldVal) {
        // 如果选项列表中没有对应的选项，需要加载选项
        if (!options.value.find(item => item.id === newVal)) {
          console.log('ContractSelect: 加载选项以包含新值', newVal);
          await loadOptions();
        }
      }
    },
    { immediate: true },
  );

  // 加载选项数据
  const loadOptions = async (keyword?: string) => {
    try {
      loading.value = true;
      const result = await getContractSelector(keyword);

      console.log('ContractSelect: API 返回原始数据:', result);

      // 处理 API 返回的数据格式
      if (result && typeof result === 'object') {
        if (Array.isArray(result.data)) {
          // 如果 API 返回的是 { data: [...] } 格式
          options.value = result.data;
        } else if (Array.isArray(result)) {
          // 如果 API 直接返回数组
          options.value = result;
        } else {
          console.warn('ContractSelect: API 返回的数据格式不正确', result);
          options.value = [];
        }
      } else if (Array.isArray(result)) {
        // 如果 result 直接是数组
        options.value = result;
      } else {
        options.value = [];
      }

      console.log('ContractSelect: 加载选项完成', {
        keyword,
        optionsCount: options.value.length,
        currentValue: currentValue.value,
        processedOptions: options.value,
      });

      // 如果有当前值但在新加载的选项中找不到，打印调试信息
      if (currentValue.value && !options.value.find(item => item.id === currentValue.value)) {
        console.log('ContractSelect: 当前值在选项中未找到', {
          currentValue: currentValue.value,
          options: options.value,
          keyword,
        });
      }
    } catch (error) {
      console.error('加载合同选项失败:', error);
      options.value = [];
    } finally {
      loading.value = false;
    }
  };

  // 防抖搜索
  const handleSearch = useDebounceFn((value: string) => {
    loadOptions(value);
  }, 300);

  // 值变化处理
  const handleChange = (value: string | undefined) => {
    currentValue.value = value;
    emit('update:value', value);

    const selectedOption = options.value.find(item => item.id === value);
    emit('change', value, selectedOption);
  };

  // 清空处理
  const handleClear = () => {
    currentValue.value = undefined;
    emit('update:value', undefined);
    emit('change', undefined);
  };

  // 组件挂载时加载初始数据
  onMounted(() => {
    loadOptions();
  });
</script>
