package com.xinghuo.manhour.model.analysis;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 个人工时效率详情
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@Schema(description = "个人工时效率详情")
public class PersonalEfficiencyDetail {

    @Schema(description = "用户ID")
    private String userId;

    @Schema(description = "员工姓名")
    private String userName;

    @Schema(description = "总工时效率")
    private BigDecimal totalEfficiency;

    @Schema(description = "月度效率趋势")
    private List<MonthlyEfficiencyData> monthlyTrend;

    @Schema(description = "项目效率分布")
    private List<ProjectEfficiencyData> projectEfficiency;

    @Schema(description = "工时类型分布")
    private List<WorkTypeDistributionData> workTypeDistribution;

    @Schema(description = "技能评估")
    private List<SkillAssessmentData> skillAssessment;

    /**
     * 月度效率数据
     */
    @Data
    @Schema(description = "月度效率数据")
    public static class MonthlyEfficiencyData {
        @Schema(description = "月份")
        private String month;

        @Schema(description = "效率值")
        private BigDecimal efficiency;

        @Schema(description = "工时")
        private BigDecimal workMonth;
    }

    /**
     * 项目效率数据
     */
    @Data
    @Schema(description = "项目效率数据")
    public static class ProjectEfficiencyData {
        @Schema(description = "项目名称")
        private String projectName;

        @Schema(description = "效率值")
        private BigDecimal efficiency;

        @Schema(description = "投入工时")
        private BigDecimal workMonth;
    }

    /**
     * 工时类型分布数据
     */
    @Data
    @Schema(description = "工时类型分布数据")
    public static class WorkTypeDistributionData {
        @Schema(description = "工时类型")
        private String workType;

        @Schema(description = "工时占比")
        private BigDecimal percentage;

        @Schema(description = "工时")
        private BigDecimal workMonth;
    }

    /**
     * 技能评估数据
     */
    @Data
    @Schema(description = "技能评估数据")
    public static class SkillAssessmentData {
        @Schema(description = "技能标签")
        private String skillTag;

        @Schema(description = "熟练度")
        private BigDecimal proficiency;

        @Schema(description = "相关项目数")
        private Integer projectCount;
    }
}
