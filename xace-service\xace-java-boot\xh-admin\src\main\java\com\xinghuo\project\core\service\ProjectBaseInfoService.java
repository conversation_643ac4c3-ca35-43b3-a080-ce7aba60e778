package com.xinghuo.project.core.service;

import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.project.core.entity.ProjectBaseEntity;
import com.xinghuo.project.core.model.projectBase.ProjectBaseInfoForm;
import com.xinghuo.project.core.model.projectBase.ProjectBaseInfoVO;

/**
 * 项目基础信息服务接口
 *
 * <AUTHOR>
 * @date 2024-08-05
 */
public interface ProjectBaseInfoService extends BaseService<ProjectBaseEntity> {

    /**
     * 根据ID查询项目基础信息详情
     */
    ProjectBaseInfoVO getInfo(String id);

    /**
     * 更新项目基础信息
     */
    boolean update(String id, ProjectBaseInfoForm form);
}
