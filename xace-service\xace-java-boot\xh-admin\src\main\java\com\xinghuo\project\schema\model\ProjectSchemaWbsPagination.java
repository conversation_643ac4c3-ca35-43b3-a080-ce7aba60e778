package com.xinghuo.project.schema.model;

import com.xinghuo.common.base.model.Pagination;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目模板WBS计划分页查询参数
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectSchemaWbsPagination extends Pagination {

    /**
     * 项目模板ID
     */
    private String projectTemplateId;

    /**
     * 父级ID
     */
    private String parentId;

    /**
     * WBS名称（模糊查询）
     */
    private String name;

    /**
     * WBS编码（模糊查询）
     */
    private String wbsCode;

    /**
     * 节点类型
     */
    private Integer nodeType;

    /**
     * 是否里程碑
     */
    private Integer isMilestone;

    /**
     * 层级
     */
    private Integer level;

    /**
     * 最小工期
     */
    private BigDecimal durationMin;

    /**
     * 最大工期
     */
    private BigDecimal durationMax;

    /**
     * 责任角色ID
     */
    private String responseRoleId;

    /**
     * 约束类型ID
     */
    private String constraintTypeId;

    /**
     * 源WBS模板明细ID
     */
    private String sourceWbsDetailId;

    /**
     * 源活动库ID
     */
    private String sourceLibraryActivityId;

    /**
     * 创建时间开始
     */
    private Date createTimeStart;

    /**
     * 创建时间结束
     */
    private Date createTimeEnd;

    /**
     * 关键字搜索（名称或编码）
     */
    private String keyword;

    /**
     * 是否只查询根节点
     */
    private Boolean rootOnly;

    /**
     * 是否包含子节点统计
     */
    private Boolean includeChildrenCount;

    /**
     * 排序字段
     */
    private String sortField;

    /**
     * 排序方向（asc/desc）
     */
    private String sortDirection;
}
