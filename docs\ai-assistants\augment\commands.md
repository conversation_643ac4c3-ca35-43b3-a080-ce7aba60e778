# Augment 自定义命令

基于XACE框架的快速代码生成命令。

## 🚀 后端命令

### /xace-entity
生成XACE标准实体类
```
/xace-entity UserEntity
```
自动生成：
- 继承 `BaseEntityV2.CUDBaseEntityV2<String>`
- 标准字段注解 `@TableField("F_FIELD")`
- Lombok注解

### /xace-controller
生成XACE标准控制器
```
/xace-controller UserController
```
自动生成：
- 标准CRUD接口
- ActionResult响应格式
- 分页查询支持
- 异常处理（依赖全局处理器）

### /xace-service
生成XACE标准服务类
```
/xace-service UserService
```
自动生成：
- Service接口和实现类
- 继承BaseService
- 标准CRUD方法
- 事务注解

### /xace-mapper
生成XACE标准Mapper
```
/xace-mapper UserMapper
```
自动生成：
- 继承XHBaseMapper
- 自定义查询方法
- XML映射文件

## 🎨 前端命令

### /xace-vue-page
生成完整CRUD页面
```
/xace-vue-page UserManagement
```
自动生成：
- BasicTable表格组件
- BasicForm表单组件
- API调用处理
- 分页逻辑

### /xace-vue-component
生成Vue组件
```
/xace-vue-component UserSelector
```
自动生成：
- Composition API结构
- TypeScript类型定义
- Props/Events规范
- XACE样式

### /xace-api
生成前端API服务
```
/xace-api userApi
```
自动生成：
- TypeScript接口定义
- ActionResult类型处理
- 错误处理封装

## 📝 模型命令

### /xace-vo
生成VO视图对象
```
/xace-vo UserVO
```
自动生成：
- 视图对象结构 (返回给前端)
- 包结构：com.xinghuo.[模块名].model.[业务功能]
- 标准字段和注解

### /xace-form
生成Form表单对象
```
/xace-form UserForm          # 推荐：合并设计
/xace-form UserCrForm        # 分离设计：创建表单
/xace-form UserUpForm        # 分离设计：更新表单
```
自动生成：
- Form结构 (接收前端表单数据)
- 验证注解 (@NotBlank, @Valid等)
- 完整import路径 (jakarta.*, lombok.Data等)
- 包结构：com.xinghuo.[模块名].model.[业务功能]
- 推荐使用合并设计，仅在验证差异大时分离

### /xace-pagination
生成分页查询对象
```
/xace-pagination UserPagination
```
自动生成：
- 继承PaginationTime
- 查询条件字段
- 包结构：com.xinghuo.[模块名].model.[业务功能]

## ⚙️ 命令配置

每个命令都会：
1. 检查XACE核心规则
2. 应用标准模板
3. 生成符合规范的代码
4. 包含必要的导入和注解

### 全局变量
- `{{projectPath}}` - 项目根路径
- `{{packageName}}` - 包名
- `{{moduleName}}` - 模块名
- `{{entityName}}` - 实体名