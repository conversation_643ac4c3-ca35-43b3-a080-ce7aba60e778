package com.xinghuo.project.portfolio.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.project.portfolio.entity.ProgramEntity;
import com.xinghuo.project.portfolio.model.ProgramPagination;
import com.xinghuo.project.portfolio.service.ProgramService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 项目群管理控制器
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Tag(name = "项目群管理", description = "项目群管理相关接口")
@RestController
@RequestMapping("/api/project/portfolio/program")
public class ProgramController {

    @Resource
    private ProgramService programService;

    /**
     * 获取项目群列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取项目群列表")
    public ActionResult<PageListVO<ProgramEntity>> list(@RequestBody ProgramPagination pagination) {
        List<ProgramEntity> list = programService.getList(pagination);
        PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
        return ActionResult.page(list, page);
    }

    /**
     * 根据项目群经理ID获取项目群列表
     */
    @GetMapping("/getListByManagerId/{managerId}")
    @Operation(summary = "根据项目群经理ID获取项目群列表")
    public ActionResult<List<ProgramEntity>> getListByManagerId(
            @Parameter(description = "项目群经理ID") @PathVariable String managerId) {
        List<ProgramEntity> list = programService.getListByManagerId(managerId);
        return ActionResult.success(list);
    }

    /**
     * 根据项目群类型ID获取项目群列表
     */
    @GetMapping("/getListByTypeId/{typeId}")
    @Operation(summary = "根据项目群类型ID获取项目群列表")
    public ActionResult<List<ProgramEntity>> getListByTypeId(
            @Parameter(description = "项目群类型ID") @PathVariable String typeId) {
        List<ProgramEntity> list = programService.getListByTypeId(typeId);
        return ActionResult.success(list);
    }

    /**
     * 根据状态获取项目群列表
     */
    @GetMapping("/getListByStatus/{status}")
    @Operation(summary = "根据状态获取项目群列表")
    public ActionResult<List<ProgramEntity>> getListByStatus(
            @Parameter(description = "状态") @PathVariable String status) {
        List<ProgramEntity> list = programService.getListByStatus(status);
        return ActionResult.success(list);
    }

    /**
     * 获取项目群详情
     */
    @GetMapping("/getInfo/{id}")
    @Operation(summary = "获取项目群详情")
    public ActionResult<ProgramEntity> getInfo(
            @Parameter(description = "项目群ID") @PathVariable String id) {
        ProgramEntity entity = programService.getInfo(id);
        return ActionResult.success(entity);
    }

    /**
     * 创建项目群
     */
    @PostMapping("/create")
    @Operation(summary = "创建项目群")
    public ActionResult<String> create(@RequestBody @Valid ProgramEntity entity) {
        boolean result = programService.saveInfo(entity);
        if (result) {
            return ActionResult.success("创建成功", entity.getId());
        } else {
            return ActionResult.fail("创建失败");
        }
    }

    /**
     * 更新项目群
     */
    @PutMapping("/update/{id}")
    @Operation(summary = "更新项目群")
    public ActionResult<String> update(
            @Parameter(description = "项目群ID") @PathVariable String id,
            @RequestBody @Valid ProgramEntity entity) {
        entity.setId(id);
        boolean result = programService.updateInfo(entity);
        if (result) {
            return ActionResult.success("更新成功");
        } else {
            return ActionResult.fail("更新失败");
        }
    }

    /**
     * 删除项目群
     */
    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除项目群")
    public ActionResult<String> delete(
            @Parameter(description = "项目群ID") @PathVariable String id) {
        boolean result = programService.deleteById(id);
        if (result) {
            return ActionResult.success("删除成功");
        } else {
            return ActionResult.fail("删除失败");
        }
    }

    /**
     * 检查项目群编码是否存在
     */
    @GetMapping("/checkCodeExists")
    @Operation(summary = "检查项目群编码是否存在")
    public ActionResult<Boolean> checkCodeExists(
            @RequestParam String code,
            @RequestParam(required = false) String excludeId) {
        boolean exists = programService.isExistByCode(code, excludeId);
        return ActionResult.success(exists);
    }
}
