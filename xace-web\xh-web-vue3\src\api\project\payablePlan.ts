import { http } from '/@/utils/http';

/**
 * 付款计划管理相关类型定义
 */

// 付款计划VO (基于PayablePlanEntity)
export interface PayablePlanVO {
  id: string;
  cmId: string; // 付款ID
  pcId: string; // 采购合同ID
  paymentContractName?: string; // 关联付款合同名称
  paymentContractCno?: string; // 关联付款合同编号
  fktj?: string; // 付款条件
  ratio?: string; // 比例
  cmMoney?: number; // 付款金额
  ownId?: string; // 付款负责人
  ownName?: string; // 付款负责人名称
  payStatus: string; // 支付状态
  payStatusText?: string; // 支付状态文本

  // 日期字段
  yufuDate?: string; // 预付日期
  fukuanDate?: string; // 付款日期

  // 备注信息
  lastNote?: string; // 最后备注
  note?: string; // 备注

  // 部门分配
  deptId?: string; // 部门ID
  deptName?: string; // 部门名称
  ybAmount?: number; // 一部金额
  ebAmount?: number; // 二部金额
  otherAmount?: number; // 其他金额

  // 系统字段
  createUserId?: string;
  createTime?: string;
  lastModifiedUserId?: string;
  updateTime?: string;
  tenantId?: string;
  flowId?: string;
}

// 付款计划表单对象
export interface PayablePlanForm {
  id?: string;
  cmId?: string;
  pcId: string; // 采购合同ID (必填)
  fktj?: string; // 付款条件
  ratio?: string; // 比例
  cmMoney?: number; // 付款金额
  ownId?: string; // 付款负责人
  payStatus?: string; // 支付状态

  // 日期字段
  yufuDate?: string; // 预付日期
  fukuanDate?: string; // 付款日期

  // 备注信息
  lastNote?: string; // 最后备注
  note?: string; // 备注

  // 部门分配
  deptId?: string; // 部门ID
  ybAmount?: number; // 一部金额
  ebAmount?: number; // 二部金额
  otherAmount?: number; // 其他金额
}

// 付款计划分页查询参数
export interface PayablePlanPagination {
  currentPage: number;
  pageSize: number;
  keyword?: string; // 关键字搜索
  pcId?: string; // 采购合同ID
  payStatus?: string; // 支付状态
  ownId?: string; // 付款负责人
  deptId?: string; // 部门ID

  // 时间范围查询
  yufuDateStart?: string; // 预付日期开始
  yufuDateEnd?: string; // 预付日期结束
  fukuanDateStart?: string; // 付款日期开始
  fukuanDateEnd?: string; // 付款日期结束

  // 金额范围查询
  minCmMoney?: number; // 最小付款金额
  maxCmMoney?: number; // 最大付款金额
}

// 分页结果
export interface PageResult<T> {
  list: T[];
  total: number;
  currentPage: number;
  pageSize: number;
}

// 付款计划统计信息
export interface PayablePlanStats {
  totalRecords: number; // 付款计划总数
  totalAmount: number; // 付款总金额
  paidAmount: number; // 已付金额
  unpaidAmount: number; // 未付金额
  plannedAmount: number; // 计划付款金额

  // 按状态统计
  statusStats: Array<{
    status: string;
    statusText: string;
    count: number;
    amount: number;
  }>;

  // 按部门统计
  deptStats: Array<{
    deptType: string; // 部门类型：yb(一部)/eb(二部)/other(其他)
    deptName: string;
    count: number;
    amount: number;
    percentage: number;
  }>;

  // 按付款合同统计
  contractStats: Array<{
    pcId: string;
    contractName: string;
    totalAmount: number;
    paidAmount: number;
    unpaidAmount: number;
    paymentRate: number;
  }>;

  // 按负责人统计
  ownerStats: Array<{
    ownId: string;
    ownName: string;
    count: number;
    amount: number;
    paidAmount: number;
  }>;
}

// 付款状态更新表单
export interface PaymentStatusUpdateForm {
  payStatus: string;
  note?: string;
  fukuanDate?: string; // 付款日期
}

/**
 * 付款计划管理API接口
 */
export const payablePlanApi = {
  /**
   * 获取付款计划列表 (分页)
   */
  getList: (params: PayablePlanPagination) => {
    return http.post<PageResult<PayablePlanVO>>('/api/project/biz/payable-plan/getList', params);
  },

  /**
   * 根据付款合同ID获取付款计划列表
   */
  getByPaymentContractId: (paymentContractId: string) => {
    return http.get<PayablePlanVO[]>(`/api/project/biz/payable-plan/payment-contract/${paymentContractId}`);
  },

  /**
   * 获取付款计划详情
   */
  getInfo: (id: string) => {
    return http.get<PayablePlanVO>(`/api/project/biz/payable-plan/${id}`);
  },

  /**
   * 创建付款计划
   */
  create: (data: PayablePlanForm) => {
    return http.post<string>('/api/project/biz/payable-plan', data);
  },

  /**
   * 更新付款计划
   */
  update: (id: string, data: PayablePlanForm) => {
    return http.put<string>(`/api/project/biz/payable-plan/${id}`, data);
  },

  /**
   * 删除付款计划
   */
  delete: (id: string) => {
    return http.delete<string>(`/api/project/biz/payable-plan/${id}`);
  },

  /**
   * 批量删除付款计划
   */
  batchDelete: (ids: string[]) => {
    return http.post<string>('/api/project/biz/payable-plan/batchDelete', { ids });
  },

  /**
   * 更新付款状态
   */
  updatePayStatus: (id: string, data: PaymentStatusUpdateForm) => {
    return http.put<string>(`/api/project/biz/payable-plan/${id}/status`, data);
  },

  /**
   * 批量更新付款状态
   */
  batchUpdatePayStatus: (ids: string[], data: PaymentStatusUpdateForm) => {
    return http.put<string>('/api/project/biz/payable-plan/batchUpdateStatus', { ids, ...data });
  },

  /**
   * 获取付款计划统计信息
   */
  getStats: (params?: {
    pcId?: string; // 付款合同ID
    ownId?: string; // 负责人ID
    deptId?: string; // 部门ID
    startDate?: string; // 开始日期
    endDate?: string; // 结束日期
    payStatus?: string; // 支付状态
  }) => {
    return http.post<PayablePlanStats>('/api/project/biz/payable-plan/stats', params || {});
  },

  /**
   * 导出付款计划数据
   */
  export: (params: PayablePlanPagination) => {
    return http.post('/api/project/biz/payable-plan/export', params, {
      responseType: 'blob',
    });
  },

  /**
   * 获取付款计划选择器数据
   */
  getSelector: (paymentContractId?: string, keyword?: string) => {
    return http.get<Array<{ id: string; fullName: string }>>('/api/project/biz/payable-plan/selector', {
      params: { paymentContractId, keyword },
    });
  },

  /**
   * 自动计算付款金额（基于比例）
   */
  calculateAmount: (paymentContractId: string, ratio: string) => {
    return http.get<{ amount: number }>('/api/project/biz/payable-plan/calculateAmount', {
      params: { paymentContractId, ratio },
    });
  },

  /**
   * 验证付款计划唯一性
   */
  checkUnique: (paymentContractId: string, fktj: string, excludeId?: string) => {
    return http.get<boolean>('/api/project/biz/payable-plan/checkUnique', {
      params: { paymentContractId, fktj, excludeId },
    });
  },

  /**
   * 根据付款合同自动计算部门分配金额
   */
  calculateDeptAmount: (paymentContractId: string, totalAmount: number) => {
    return http.get<{
      ybAmount: number;
      ebAmount: number;
      otherAmount: number;
    }>('/api/project/biz/payable-plan/calculateDeptAmount', {
      params: { paymentContractId, totalAmount },
    });
  },

  /**
   * 批量标记为已付
   */
  batchMarkAsPaid: (ids: string[], fukuanDate?: string, note?: string) => {
    return http.put<string>('/api/project/biz/payable-plan/batchMarkAsPaid', {
      ids,
      fukuanDate,
      note,
    });
  },

  /**
   * 获取付款提醒列表（即将到期或逾期）
   */
  getPaymentReminders: (days = 7) => {
    return http.get<PayablePlanVO[]>('/api/project/biz/payable-plan/reminders', {
      params: { days },
    });
  },

  /**
   * 生成付款申请单
   */
  generatePaymentRequest: (ids: string[]) => {
    return http.post('/api/project/biz/payable-plan/generatePaymentRequest', { ids }, { responseType: 'blob' });
  },
};

// 默认导出
export default payablePlanApi;
