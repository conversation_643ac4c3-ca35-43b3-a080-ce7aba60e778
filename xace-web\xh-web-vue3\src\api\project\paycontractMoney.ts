import { defHttp } from '/@/utils/http/axios';
import { ListResult } from '/@/api/model/baseModel';

/**
 * 采购付款计划管理API
 */

// API URL前缀
const API_PREFIX = '/api/project/biz/paymentContract/money';

/**
 * 采购付款计划对象接口
 */
export interface PaycontractMoneyModel {
  cmId: string;
  pcId: string;
  paycontractName?: string;
  paycontractNo?: string;
  fktj: string;
  ratio: number;
  cmMoney: number;
  yufuDate: string;
  fukuanDate: string;
  payStatus: string;
  note: string;
  lastNote: string;
  ownId: string;
  ownName?: string;
  ybAmount: number;
  ebAmount: number;
  otherAmount: number;
  createUserId: string;
  createUserName?: string;
  createTime: string;
  lastModifiedUserId: string;
  lastModifiedUserName?: string;
  updateTime: string;
}

/**
 * 采购付款计划表单接口
 */
export interface PaycontractMoneyFormModel {
  pcId: string;
  fktj: string;
  ratio?: number;
  cmMoney: number;
  yufuDate?: string;
  fukuanDate?: string;
  payStatus?: string;
  note?: string;
  ownId?: string;
  ybAmount?: number;
  ebAmount?: number;
  otherAmount?: number;
}

/**
 * 采购付款计划查询参数接口
 */
export interface PaycontractMoneyQueryParams {
  pcId?: string;
  payStatus?: string;
  yufuDateStart?: string;
  yufuDateEnd?: string;
  ownId?: string;
  pageSize?: number;
  currentPage?: number;
}

/**
 * 采购付款计划状态更新接口
 */
export interface PaycontractMoneyStatusModel {
  payStatus: string;
  fukuanDate?: string;
  lastNote?: string;
}

/**
 * 获取采购付款计划列表
 * @param params 查询参数
 * @returns 采购付款计划列表
 */
export const getPaycontractMoneyList = (params?: PaycontractMoneyQueryParams) => {
  return defHttp.post<ListResult<PaycontractMoneyModel>>({
    url: `${API_PREFIX}/getList`,
    data: params,
  });
};

/**
 * 根据采购合同ID获取付款计划列表
 * @param paycontractId 采购合同ID
 * @returns 付款计划列表
 */
export const getPaycontractMoneyListByPaycontractId = (paycontractId: string) => {
  return defHttp.get<PaycontractMoneyModel[]>({
    url: `${API_PREFIX}/paycontract/${paycontractId}`,
  });
};

/**
 * 获取采购付款计划详情
 * @param id 采购付款计划ID
 * @returns 采购付款计划详情
 */
export const getPaycontractMoneyInfo = (id: string) => {
  return defHttp.get<PaycontractMoneyModel>({
    url: `${API_PREFIX}/${id}`,
  });
};

/**
 * 创建采购付款计划
 * @param params 采购付款计划创建参数
 * @returns 操作结果
 */
export const createPaycontractMoney = (params: PaycontractMoneyFormModel) => {
  return defHttp.post<void>({
    url: API_PREFIX,
    data: params,
  });
};

/**
 * 更新采购付款计划
 * @param id 采购付款计划ID
 * @param params 采购付款计划更新参数
 * @returns 操作结果
 */
export const updatePaycontractMoney = (id: string, params: PaycontractMoneyFormModel) => {
  return defHttp.put<void>({
    url: `${API_PREFIX}/${id}`,
    data: params,
  });
};

/**
 * 删除采购付款计划
 * @param id 采购付款计划ID
 * @returns 操作结果
 */
export const deletePaycontractMoney = (id: string) => {
  return defHttp.delete<void>({
    url: `${API_PREFIX}/${id}`,
  });
};

/**
 * 更新付款状态
 * @param id 采购付款计划ID
 * @param params 状态更新参数
 * @returns 操作结果
 */
export const updatePaycontractMoneyStatus = (id: string, params: PaycontractMoneyStatusModel) => {
  return defHttp.put<void>({
    url: `${API_PREFIX}/${id}/status`,
    data: params,
  });
};

/**
 * 登记付款
 * @param id 采购付款计划ID
 * @param fukuanDate 付款日期
 * @param lastNote 备注
 * @returns 操作结果
 */
export const registerPaycontractMoneyPayment = (id: string, fukuanDate: string, lastNote?: string) => {
  return defHttp.put<void>({
    url: `${API_PREFIX}/${id}/payment`,
    params: {
      fukuanDate,
      lastNote,
    },
  });
};
