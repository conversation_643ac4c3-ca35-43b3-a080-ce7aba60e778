<template>
  <a-tree-select
    v-if="isTree"
    v-bind="getBindValue"
    placeholder="请选择"
    :tree-data="treeData"
    :field-names="fieldNames"
    v-model:value="innerValue"
    style="width: 100%"
    :multiple="mode === 'multiple'"
    :tree-default-expand-all="treeDefaultExpandAll"
    :show-search="showSearch"
    :filter-tree-node="filterTreeNode"
    @change="onChange" />
  <a-select
    v-else
    v-bind="getBindValue"
    placeholder="请选择"
    :mode="mode"
    :field-names="fieldNames"
    v-model:value="innerValue"
    style="width: 100%"
    show-search
    :options="options"
    :filter-option="filterOption"
    @change="onChange" />
</template>
<script lang="ts">
  import type { PropType } from 'vue';
  import { get, omit } from 'lodash-es';
  import { defineComponent, ref, unref, computed, watch, reactive, onMounted } from 'vue';
  import { useAttrs } from '/@/hooks/core/useAttrs';
  import { TreeSelect as ATreeSelect } from 'ant-design-vue';

  type SearchType = Nullable<'frontValue' | 'serverValue'>;
  const attrs = useAttrs({ excludeDefaultKeys: false });
  const getBindValue = computed(() => omit(unref(attrs), ['onChange']));

  export default defineComponent({
    name: 'XhApiSelect',
    components: { ATreeSelect },
    props: {
      isTree: {
        type: Boolean,
        default: false,
      },
      value: {
        type: [String, Number, Array] as PropType<string | number | (string | number)[]>,
        default: '',
      },
      input: {
        type: Boolean,
        default: true,
      },
      api: {
        required: true,
        type: Function as PropType<() => Promise<Recordable[]>>,
      },
      labelField: {
        type: String,
        default: 'fullName',
      },
      valueField: {
        type: String,
        default: 'id',
      },
      childrenField: {
        type: String,
        default: 'children',
      },
      immediate: {
        type: Boolean,
        default: true,
      },
      resultField: {
        type: String,
        default: 'data.list',
      },
      mode: {
        type: String as PropType<'multiple' | 'tags' | 'combobox'>,
        default: '',
      },
      showSearch: {
        type: Boolean,
        default: true,
      },
      treeDefaultExpandAll: {
        type: Boolean,
        default: true,
      },
    },
    setup(props, { emit }) {
      const innerValue = ref<string | undefined | number | string[] | number[]>('');
      const searchType = ref<SearchType>(null);
      const options = ref([]);
      const treeData = ref([]);

      const fieldNames = reactive({
        label: props.labelField,
        value: props.valueField,
        children: props.childrenField,
      });

      const onChange = (value: string | string[]) => {
        emit('update:value', value);
        emit('change', value);
      };

      watch(
        () => props.value,
        val => {
          if (val === '') {
            innerValue.value = undefined;
          } else {
            innerValue.value = val;
          }
        },
        { immediate: true },
      );

      onMounted(() => {
        props.api?.().then(res => {
          const data = get(res, props.resultField, []);
          if (props.isTree) {
            treeData.value = data;
          } else {
            options.value = data;
          }
        });
      });

      const filterOption = (input: string, option: any) => {
        return option[props.labelField].toLowerCase().indexOf(input.toLowerCase()) >= 0;
      };

      const filterTreeNode = (searchValue: string, treeNode: any) => {
        return treeNode[props.labelField].toLowerCase().indexOf(searchValue.toLowerCase()) >= 0;
      };

      return {
        searchType,
        innerValue,
        onChange,
        getBindValue,
        fieldNames,
        filterOption,
        filterTreeNode,
        options,
        treeData,
      };
    },
  });
</script>
