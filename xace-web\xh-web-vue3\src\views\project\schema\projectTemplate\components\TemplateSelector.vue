<template>
  <div class="template-selector">
    <span class="selector-label">项目模板：</span>
    <a-select
      v-model:value="selectedValue"
      :placeholder="placeholder"
      show-search
      allow-clear
      :loading="loading"
      :filter-option="false"
      @search="handleSearch"
      @change="handleChange"
      style="min-width: 250px">
      <a-select-option v-for="template in templateOptions" :key="template.id" :value="template.id" :label="template.fullName">
        <div class="template-option">
          <div class="template-name">{{ template.fullName }}</div>
          <div class="template-desc" v-if="template.description">
            {{ template.description }}
          </div>
        </div>
      </a-select-option>
    </a-select>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, watch } from 'vue';
  import { getProjectTemplateSelectList } from '/@/api/project/projectTemplate';

  interface TemplateOption {
    id: string;
    fullName: string;
    name: string;
    code: string;
    description?: string;
  }

  const props = defineProps({
    value: {
      type: String,
      default: '',
    },
    placeholder: {
      type: String,
      default: '请选择项目模板',
    },
  });

  const emit = defineEmits(['update:value', 'change']);

  const selectedValue = ref(props.value);
  const templateOptions = ref<TemplateOption[]>([]);
  const loading = ref(false);
  const searchKeyword = ref('');

  // 监听外部值变化
  watch(
    () => props.value,
    newValue => {
      selectedValue.value = newValue;
    },
  );

  // 监听内部值变化
  watch(selectedValue, newValue => {
    emit('update:value', newValue);
  });

  // 加载模板选择列表
  const loadTemplateOptions = async (keyword = '') => {
    loading.value = true;
    try {
      const response = await getProjectTemplateSelectList(keyword);
      if (response.code === 200) {
        // 确保数据格式符合 XACE 标准 {id, fullName}
        const formattedData = (response.data || []).map(item => ({
          id: item.id,
          fullName: item.fullName || item.name,
          name: item.name,
          code: item.code,
          description: item.description,
        }));
        templateOptions.value = formattedData;
      } else {
        console.error('加载模板选择列表失败:', response.msg);
        templateOptions.value = [];
      }
    } catch (error) {
      console.error('加载项目模板选择列表失败:', error);
      templateOptions.value = [];
    } finally {
      loading.value = false;
    }
  };

  // 搜索处理 - 添加防抖优化
  let searchTimer: NodeJS.Timeout | null = null;
  const handleSearch = (value: string) => {
    searchKeyword.value = value;

    // 清除之前的定时器
    if (searchTimer) {
      clearTimeout(searchTimer);
    }

    // 设置新的防抖定时器
    searchTimer = setTimeout(() => {
      loadTemplateOptions(value);
    }, 300);
  };

  // 选择变化处理
  const handleChange = (value: string) => {
    const selectedTemplate = templateOptions.value.find(item => item.id === value);
    if (selectedTemplate) {
      emit('change', selectedTemplate);
    } else {
      emit('change', null);
    }
  };

  onMounted(() => {
    loadTemplateOptions();
  });
</script>

<style lang="less" scoped>
  .template-selector {
    display: flex;
    align-items: center;
    gap: 8px;

    .selector-label {
      color: rgba(0, 0, 0, 0.85);
      font-weight: 500;
      white-space: nowrap;
    }

    .template-option {
      .template-name {
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
      }

      .template-desc {
        font-size: 12px;
        color: rgba(0, 0, 0, 0.45);
        margin-top: 2px;
      }
    }
  }
</style>
