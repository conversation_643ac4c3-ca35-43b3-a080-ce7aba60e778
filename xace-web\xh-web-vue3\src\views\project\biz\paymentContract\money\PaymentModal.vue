<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="getTitle" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script lang="ts" setup>
  import { computed, ref, unref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { registerPaycontractMoneyPayment } from '/@/api/project/paycontractMoney';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { formatToDate } from '/@/utils/dateUtil';

  import type { FormSchema } from '/@/components/Table';
  import type { PaycontractMoneyModel } from '/@/api/project/paycontractMoney';

  const emit = defineEmits(['success', 'register']);
  const { createMessage } = useMessage();

  const rowId = ref('');
  const record = ref<PaycontractMoneyModel>();

  const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
    labelWidth: 100,
    baseColProps: { span: 24 },
    schemas: getFormSchema(),
    showActionButtonGroup: false,
  });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async data => {
    resetFields();
    setModalProps({ confirmLoading: false });

    rowId.value = data.record.id;
    record.value = data.record;

    // 设置表单初始值
    await setFieldsValue({
      fukuanDate: formatToDate(new Date()), // 默认今天
      lastNote: '',
    });
  });

  const getTitle = computed(() => '登记付款');

  function getFormSchema(): FormSchema[] {
    return [
      {
        field: 'fukuanDate',
        label: '付款日期',
        component: 'DatePicker',
        componentProps: {
          placeholder: '请选择付款日期',
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
        },
        required: true,
        colProps: { span: 24 },
      },
      {
        field: 'lastNote',
        label: '备注',
        component: 'Input',
        componentProps: {
          type: 'textarea',
          placeholder: '请输入备注',
          rows: 4,
        },
        colProps: { span: 24 },
      },
    ];
  }

  async function handleSubmit() {
    try {
      const values = await validate();
      setModalProps({ confirmLoading: true });

      await registerPaycontractMoneyPayment(rowId.value, values.fukuanDate, values.lastNote);

      createMessage.success('付款登记成功');
      closeModal();
      emit('success');
    } catch (error) {
      createMessage.error('付款登记失败');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
