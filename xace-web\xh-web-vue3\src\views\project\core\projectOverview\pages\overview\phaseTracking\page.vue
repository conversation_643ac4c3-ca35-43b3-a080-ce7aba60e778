<template>
  <div class="phase-tracking-page p-4">
    <!-- 页面标题 -->
    <div class="page-header mb-4">
      <h2 class="text-xl font-semibold mb-2">阶段跟踪</h2>
      <p class="text-gray-600">项目各阶段的详细进展跟踪和关键指标监控</p>
    </div>

    <a-spin :spinning="loading">
      <!-- 工具栏 -->
      <div class="toolbar mb-4 flex justify-between items-center">
        <div class="filters flex items-center space-x-4">
          <a-select v-model:value="selectedPhase" placeholder="筛选阶段" style="width: 150px" allow-clear @change="handlePhaseFilter">
            <a-select-option v-for="phase in phases" :key="phase.id" :value="phase.id">
              {{ phase.name }}
            </a-select-option>
          </a-select>
          <a-select v-model:value="selectedMetric" placeholder="筛选指标" style="width: 150px" allow-clear @change="handleMetricFilter">
            <a-select-option value="all">全部指标</a-select-option>
            <a-select-option value="schedule">进度指标</a-select-option>
            <a-select-option value="quality">质量指标</a-select-option>
            <a-select-option value="cost">成本指标</a-select-option>
          </a-select>
        </div>
        <div class="actions">
          <a-space>
            <a-button @click="handleExport">
              <template #icon><ExportOutlined /></template>
              导出报告
            </a-button>
            <a-button @click="handleRefresh">
              <template #icon><ReloadOutlined /></template>
              刷新
            </a-button>
          </a-space>
        </div>
      </div>

      <!-- 阶段概览卡片 -->
      <div class="phase-overview grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6 p-4 rounded-lg">
        <div
          v-for="phase in phases"
          :key="phase.id"
          :class="[
            'phase-card p-4 rounded-lg border-2 transition-all cursor-pointer',
            phase.status === 'completed'
              ? 'bg-green-50 border-green-200'
              : phase.status === 'current'
              ? 'bg-blue-50 border-blue-200'
              : 'bg-gray-50 border-gray-200',
          ]"
          @click="selectPhase(phase.id)">
          <div class="text-center">
            <div
              :class="[
                'w-12 h-12 rounded-full mx-auto mb-2 flex items-center justify-center text-lg font-bold',
                phase.status === 'completed' ? 'bg-green-500 text-white' : phase.status === 'current' ? 'bg-blue-500 text-white' : 'bg-gray-400 text-white',
              ]">
              {{ phase.seqNo }}
            </div>
            <div class="font-medium text-sm">{{ phase.name }}</div>
            <div class="text-xs text-gray-500 mt-1">
              {{ phase.status === 'completed' ? '已完成' : phase.status === 'current' ? '进行中' : '计划中' }}
            </div>
          </div>
        </div>
      </div>

      <!-- 阶段跟踪表格 -->
      <div class="tracking-table bg-white rounded-lg shadow-sm border">
        <div class="table-header p-4 border-b">
          <h3 class="text-lg font-semibold">阶段跟踪矩阵</h3>
          <p class="text-sm text-gray-600 mt-1">各阶段关键指标的详细跟踪情况</p>
        </div>

        <div class="table-content overflow-x-auto">
          <table class="w-full">
            <thead class="bg-gray-50">
              <tr>
                <th class="text-left p-4 font-medium text-gray-900 border-r border-gray-200" style="min-width: 120px"> 指标类型 </th>
                <th
                  v-for="phase in phases"
                  :key="phase.id"
                  :class="[
                    'text-center p-4 font-medium border-r border-gray-200',
                    phase.status === 'completed' ? 'text-green-600 bg-green-50' : phase.status === 'current' ? 'text-blue-600 bg-blue-50' : 'text-gray-600',
                  ]"
                  style="min-width: 150px">
                  {{ phase.name }}
                </th>
              </tr>
            </thead>
            <tbody>
              <!-- 计划完成/实际完成 -->
              <tr class="border-b border-gray-100 hover:bg-gray-50">
                <td class="p-4 font-medium text-gray-900 border-r border-gray-200">
                  <div class="flex items-center">
                    <CalendarOutlined class="mr-2 text-blue-500" />
                    计划完成/实际完成
                  </div>
                </td>
                <td v-for="phase in phases" :key="`date-${phase.id}`" class="p-4 text-center border-r border-gray-200">
                  <div class="space-y-1">
                    <div class="text-sm">{{ getPhaseData(phase.id, 'stageDate').planned }}</div>
                    <div class="text-sm text-gray-500">{{ getPhaseData(phase.id, 'stageDate').actual || '-' }}</div>
                  </div>
                </td>
              </tr>

              <!-- 活动 -->
              <tr class="border-b border-gray-100 hover:bg-gray-50">
                <td class="p-4 font-medium text-gray-900 border-r border-gray-200">
                  <div class="flex items-center">
                    <CheckSquareOutlined class="mr-2 text-green-500" />
                    活动
                  </div>
                </td>
                <td v-for="phase in phases" :key="`activity-${phase.id}`" class="p-4 text-center border-r border-gray-200">
                  <div class="space-y-1">
                    <div class="text-sm font-medium">{{ getPhaseData(phase.id, 'activity').count }}</div>
                    <div class="text-xs">
                      <a-progress
                        :percent="getPhaseData(phase.id, 'activity').percentage"
                        size="small"
                        :show-info="false"
                        :stroke-color="getPhaseProgressColor(phase.status)" />
                      <span class="text-gray-500 ml-1">{{ getPhaseData(phase.id, 'activity').percentage }}%</span>
                    </div>
                  </div>
                </td>
              </tr>

              <!-- 交付物 -->
              <tr class="border-b border-gray-100 hover:bg-gray-50">
                <td class="p-4 font-medium text-gray-900 border-r border-gray-200">
                  <div class="flex items-center">
                    <FileTextOutlined class="mr-2 text-orange-500" />
                    交付物
                  </div>
                </td>
                <td v-for="phase in phases" :key="`deliverable-${phase.id}`" class="p-4 text-center border-r border-gray-200">
                  <div class="space-y-1">
                    <div class="text-sm font-medium">{{ getPhaseData(phase.id, 'workProduct').count }}</div>
                    <div class="flex justify-center space-x-1">
                      <a-tag
                        v-for="(value, status) in getPhaseData(phase.id, 'workProduct').status"
                        :key="status"
                        :color="getDeliverableStatusColor(status)"
                        size="small">
                        {{ value }}
                      </a-tag>
                    </div>
                  </div>
                </td>
              </tr>

              <!-- 质量检查 -->
              <tr class="border-b border-gray-100 hover:bg-gray-50">
                <td class="p-4 font-medium text-gray-900 border-r border-gray-200">
                  <div class="flex items-center">
                    <SafetyCertificateOutlined class="mr-2 text-purple-500" />
                    质量检查
                  </div>
                </td>
                <td v-for="phase in phases" :key="`qa-${phase.id}`" class="p-4 text-center border-r border-gray-200">
                  <div class="space-y-1">
                    <div class="text-sm font-medium">{{ getPhaseData(phase.id, 'qaPlan').count }}</div>
                    <div class="text-xs">
                      <a-tag :color="getQualityStatusColor(getPhaseData(phase.id, 'qaPlan').status)" size="small">
                        {{ getPhaseData(phase.id, 'qaPlan').status }}
                      </a-tag>
                    </div>
                  </div>
                </td>
              </tr>

              <!-- 成本累计 -->
              <tr class="border-b border-gray-100 hover:bg-gray-50">
                <td class="p-4 font-medium text-gray-900 border-r border-gray-200">
                  <div class="flex items-center">
                    <DollarCircleOutlined class="mr-2 text-red-500" />
                    成本累计
                  </div>
                </td>
                <td v-for="phase in phases" :key="`cost-${phase.id}`" class="p-4 text-center border-r border-gray-200">
                  <div class="space-y-1">
                    <div class="text-sm font-medium">¥{{ formatCurrency(getPhaseData(phase.id, 'cost').amount) }}</div>
                    <div class="text-xs text-gray-500"> {{ getPhaseData(phase.id, 'cost').percentage }}% 预算 </div>
                  </div>
                </td>
              </tr>

              <!-- 人工累计 -->
              <tr class="hover:bg-gray-50">
                <td class="p-4 font-medium text-gray-900 border-r border-gray-200">
                  <div class="flex items-center">
                    <TeamOutlined class="mr-2 text-indigo-500" />
                    人工累计
                  </div>
                </td>
                <td v-for="phase in phases" :key="`workhour-${phase.id}`" class="p-4 text-center border-r border-gray-200">
                  <div class="space-y-1">
                    <div class="text-sm font-medium">{{ getPhaseData(phase.id, 'workhour').days }}人天</div>
                    <div class="text-xs text-gray-500"> {{ getPhaseData(phase.id, 'workhour').hours }}小时 </div>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 阶段详情 -->
      <div v-if="selectedPhaseDetails" class="phase-details mt-6 bg-white rounded-lg shadow-sm border p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold">{{ selectedPhaseDetails.name }} - 详细信息</h3>
          <a-button @click="selectedPhaseDetails = null" type="link">
            <template #icon><CloseOutlined /></template>
            关闭
          </a-button>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <!-- 进度信息 -->
          <div class="detail-section">
            <h4 class="font-medium mb-3 flex items-center">
              <PercentageOutlined class="mr-2 text-blue-500" />
              进度信息
            </h4>
            <div class="space-y-2 text-sm">
              <div class="flex justify-between">
                <span class="text-gray-600">状态:</span>
                <a-tag :color="getPhaseStatusColor(selectedPhaseDetails.status)">
                  {{ getPhaseStatusText(selectedPhaseDetails.status) }}
                </a-tag>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">计划开始:</span>
                <span>{{ selectedPhaseDetails.plannedStartDate }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">计划结束:</span>
                <span>{{ selectedPhaseDetails.plannedEndDate }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">实际开始:</span>
                <span>{{ selectedPhaseDetails.actualStartDate || '-' }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">实际结束:</span>
                <span>{{ selectedPhaseDetails.actualEndDate || '-' }}</span>
              </div>
            </div>
          </div>

          <!-- 资源信息 -->
          <div class="detail-section">
            <h4 class="font-medium mb-3 flex items-center">
              <TeamOutlined class="mr-2 text-green-500" />
              资源信息
            </h4>
            <div class="space-y-2 text-sm">
              <div class="flex justify-between">
                <span class="text-gray-600">参与人员:</span>
                <span>{{ selectedPhaseDetails.teamSize }}人</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">预算分配:</span>
                <span>¥{{ formatCurrency(selectedPhaseDetails.budget) }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">已用成本:</span>
                <span>¥{{ formatCurrency(selectedPhaseDetails.spentCost) }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">成本使用率:</span>
                <span>{{ Math.round((selectedPhaseDetails.spentCost / selectedPhaseDetails.budget) * 100) }}%</span>
              </div>
            </div>
          </div>

          <!-- 质量信息 -->
          <div class="detail-section">
            <h4 class="font-medium mb-3 flex items-center">
              <SafetyCertificateOutlined class="mr-2 text-purple-500" />
              质量信息
            </h4>
            <div class="space-y-2 text-sm">
              <div class="flex justify-between">
                <span class="text-gray-600">质量评级:</span>
                <a-tag :color="getQualityRatingColor(selectedPhaseDetails.qualityRating)">
                  {{ selectedPhaseDetails.qualityRating }}
                </a-tag>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">发现问题:</span>
                <span>{{ selectedPhaseDetails.issuesFound }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">已解决:</span>
                <span>{{ selectedPhaseDetails.issuesResolved }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">解决率:</span>
                <span>{{ Math.round((selectedPhaseDetails.issuesResolved / selectedPhaseDetails.issuesFound) * 100) }}%</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 关键活动列表 -->
        <div class="key-activities mt-6">
          <h4 class="font-medium mb-3 flex items-center">
            <CheckSquareOutlined class="mr-2 text-orange-500" />
            关键活动
          </h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div v-for="activity in selectedPhaseDetails.keyActivities" :key="activity.id" class="activity-item p-3 bg-gray-50 rounded-lg">
              <div class="flex items-center justify-between mb-2">
                <span class="font-medium">{{ activity.name }}</span>
                <a-tag :color="getActivityStatusColor(activity.status)" size="small">
                  {{ activity.status }}
                </a-tag>
              </div>
              <div class="text-sm text-gray-600 mb-2">{{ activity.description }}</div>
              <div class="flex items-center justify-between text-xs text-gray-500">
                <span>负责人: {{ activity.assignee }}</span>
                <span>{{ activity.dueDate }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import {
    ExportOutlined,
    ReloadOutlined,
    CalendarOutlined,
    CheckSquareOutlined,
    FileTextOutlined,
    SafetyCertificateOutlined,
    DollarCircleOutlined,
    TeamOutlined,
    PercentageOutlined,
    CloseOutlined,
  } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';

  // 响应式数据
  const loading = ref(false);
  const selectedPhase = ref(undefined);
  const selectedMetric = ref(undefined);
  const selectedPhaseDetails = ref<any>(null);

  // 阶段数据
  const phases = ref([
    { id: '1', name: '需求分析', seqNo: 1, status: 'completed', color: '#52c41a' },
    { id: '2', name: '系统设计', seqNo: 2, status: 'completed', color: '#52c41a' },
    { id: '3', name: '开发实施', seqNo: 3, status: 'current', color: '#1890ff' },
    { id: '4', name: '系统测试', seqNo: 4, status: 'pending', color: '#d9d9d9' },
    { id: '5', name: '用户验收', seqNo: 5, status: 'pending', color: '#d9d9d9' },
    { id: '6', name: '项目收尾', seqNo: 6, status: 'pending', color: '#d9d9d9' },
  ]);

  // 跟踪数据
  const trackingData = reactive({
    stageDate: {
      '1': { planned: '2024-02-28', actual: '2024-02-25' },
      '2': { planned: '2024-04-15', actual: '2024-04-12' },
      '3': { planned: '2024-07-31', actual: null },
      '4': { planned: '2024-09-30', actual: null },
      '5': { planned: '2024-11-15', actual: null },
      '6': { planned: '2024-12-31', actual: null },
    },
    activity: {
      '1': { count: 8, percentage: 100 },
      '2': { count: 12, percentage: 100 },
      '3': { count: 15, percentage: 67 },
      '4': { count: 10, percentage: 0 },
      '5': { count: 6, percentage: 0 },
      '6': { count: 4, percentage: 0 },
    },
    workProduct: {
      '1': { count: 3, status: { completed: 3, pending: 0, review: 0 } },
      '2': { count: 5, status: { completed: 5, pending: 0, review: 0 } },
      '3': { count: 8, status: { completed: 3, pending: 4, review: 1 } },
      '4': { count: 4, status: { completed: 0, pending: 4, review: 0 } },
      '5': { count: 2, status: { completed: 0, pending: 2, review: 0 } },
      '6': { count: 1, status: { completed: 0, pending: 1, review: 0 } },
    },
    qaPlan: {
      '1': { count: 2, status: '已通过' },
      '2': { count: 3, status: '已通过' },
      '3': { count: 4, status: '进行中' },
      '4': { count: 3, status: '计划中' },
      '5': { count: 2, status: '计划中' },
      '6': { count: 1, status: '计划中' },
    },
    cost: {
      '1': { amount: 85000, percentage: 95 },
      '2': { amount: 120000, percentage: 88 },
      '3': { amount: 180000, percentage: 72 },
      '4': { amount: 0, percentage: 0 },
      '5': { amount: 0, percentage: 0 },
      '6': { amount: 0, percentage: 0 },
    },
    workhour: {
      '1': { days: 45, hours: 360 },
      '2': { days: 68, hours: 544 },
      '3': { days: 89, hours: 712 },
      '4': { days: 0, hours: 0 },
      '5': { days: 0, hours: 0 },
      '6': { days: 0, hours: 0 },
    },
  });

  // 获取阶段数据
  const getPhaseData = (phaseId: string, type: string) => {
    return trackingData[type as keyof typeof trackingData][phaseId] || {};
  };

  // 获取阶段进度颜色
  const getPhaseProgressColor = (status: string) => {
    const colorMap: Record<string, string> = {
      completed: '#52c41a',
      current: '#1890ff',
      pending: '#d9d9d9',
    };
    return colorMap[status] || '#d9d9d9';
  };

  // 获取交付物状态颜色
  const getDeliverableStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      completed: 'green',
      pending: 'orange',
      review: 'blue',
    };
    return colorMap[status] || 'default';
  };

  // 获取质量状态颜色
  const getQualityStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      已通过: 'green',
      进行中: 'blue',
      计划中: 'default',
      未通过: 'red',
    };
    return colorMap[status] || 'default';
  };

  // 获取阶段状态颜色
  const getPhaseStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      completed: 'green',
      current: 'blue',
      pending: 'default',
    };
    return colorMap[status] || 'default';
  };

  // 获取阶段状态文本
  const getPhaseStatusText = (status: string) => {
    const textMap: Record<string, string> = {
      completed: '已完成',
      current: '进行中',
      pending: '计划中',
    };
    return textMap[status] || '未知';
  };

  // 获取质量评级颜色
  const getQualityRatingColor = (rating: string) => {
    const colorMap: Record<string, string> = {
      优秀: 'green',
      良好: 'blue',
      一般: 'orange',
      待改进: 'red',
    };
    return colorMap[rating] || 'default';
  };

  // 获取活动状态颜色
  const getActivityStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      已完成: 'green',
      进行中: 'blue',
      计划中: 'default',
      逾期: 'red',
    };
    return colorMap[status] || 'default';
  };

  // 格式化货币
  const formatCurrency = (amount: number) => {
    return amount.toLocaleString();
  };

  // 选择阶段
  const selectPhase = (phaseId: string) => {
    const phase = phases.value.find(p => p.id === phaseId);
    if (phase) {
      selectedPhaseDetails.value = {
        ...phase,
        plannedStartDate: '2024-03-01',
        plannedEndDate: '2024-07-31',
        actualStartDate: '2024-03-05',
        actualEndDate: phase.status === 'completed' ? '2024-07-28' : null,
        teamSize: 12,
        budget: 200000,
        spentCost: 145000,
        qualityRating: '良好',
        issuesFound: 8,
        issuesResolved: 6,
        keyActivities: [
          {
            id: '1',
            name: '核心模块开发',
            description: '开发用户管理和权限控制核心功能',
            assignee: '张三',
            dueDate: '2024-06-15',
            status: '进行中',
          },
          {
            id: '2',
            name: '数据库设计',
            description: '完成数据库表结构设计和优化',
            assignee: '李四',
            dueDate: '2024-05-30',
            status: '已完成',
          },
          {
            id: '3',
            name: 'API接口开发',
            description: '开发RESTful API接口',
            assignee: '王五',
            dueDate: '2024-06-30',
            status: '进行中',
          },
          {
            id: '4',
            name: '单元测试',
            description: '编写并执行单元测试用例',
            assignee: '赵六',
            dueDate: '2024-07-15',
            status: '计划中',
          },
        ],
      };
    }
  };

  // 阶段筛选
  const handlePhaseFilter = () => {
    console.log('阶段筛选:', selectedPhase.value);
  };

  // 指标筛选
  const handleMetricFilter = () => {
    console.log('指标筛选:', selectedMetric.value);
  };

  // 导出报告
  const handleExport = () => {
    message.info('导出功能开发中...');
  };

  // 刷新数据
  const handleRefresh = () => {
    loadData();
    message.success('刷新成功');
  };

  // 加载数据
  const loadData = async () => {
    loading.value = true;
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 800));

      console.log('阶段跟踪数据加载完成');
    } catch (error) {
      console.error('加载失败:', error);
      message.error('加载失败，请刷新页面重试');
    } finally {
      loading.value = false;
    }
  };

  // 页面初始化
  onMounted(() => {
    loadData();
  });
</script>

<style scoped>
  .phase-tracking-page {
    min-height: 100vh;
    background-color: var(--section-bg-color);
  }

  .phase-card {
    transition: all 0.3s ease;
  }

  .phase-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .tracking-table table {
    border-collapse: collapse;
  }

  .tracking-table th,
  .tracking-table td {
    border-right: 1px solid #f0f0f0;
  }

  .tracking-table th:last-child,
  .tracking-table td:last-child {
    border-right: none;
  }

  .tracking-table tbody tr:hover {
    background-color: #fafafa;
  }

  .detail-section {
    padding: 16px;
    background-color: #fafafa;
    border-radius: 8px;
  }

  .activity-item {
    transition: all 0.3s ease;
  }

  .activity-item:hover {
    background-color: #ffffff !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  :deep(.ant-progress-line) {
    margin: 0;
  }

  :deep(.ant-tag) {
    margin: 0;
  }
  .phase-overview {
    background: var(--content-bg-color);
  }
</style>
