<template>
  <div class="template-plan-page p-6">
    <div class="max-w-7xl mx-auto">
      <!-- 页面标题 -->
      <div class="header-section mb-6">
        <div class="flex justify-between items-center">
          <div>
            <h1 class="text-2xl font-bold mb-2">计划配置</h1>
            <p class="text-gray-600">管理项目模板的WBS结构和阶段配置</p>
          </div>
          <a-space>
            <a-button @click="handleImportWbs">
              <template #icon><ImportOutlined /></template>
              导入WBS
            </a-button>
            <a-button @click="handleImportPhase">
              <template #icon><ImportOutlined /></template>
              导入阶段
            </a-button>
            <a-button type="primary" @click="handleSaveAll" :loading="saving">
              <template #icon><SaveOutlined /></template>
              保存全部
            </a-button>
          </a-space>
        </div>
      </div>

      <!-- 标签页 -->
      <a-tabs v-model:activeKey="activeTab" type="card" class="plan-tabs">
        <!-- WBS结构标签页 -->
        <a-tab-pane key="wbs" tab="WBS结构">
          <div class="wbs-section">
            <div class="section-header mb-4">
              <div class="flex justify-between items-center">
                <h3 class="text-lg font-semibold">WBS活动结构</h3>
                <a-space>
                  <a-button @click="handleAddWbs">
                    <template #icon><PlusOutlined /></template>
                    添加活动
                  </a-button>
                  <a-button color="error" :disabled="!selectedWbsRows.length" @click="handleBatchDeleteWbs">
                    <template #icon><DeleteOutlined /></template>
                    批量删除
                  </a-button>
                </a-space>
              </div>
            </div>

            <a-table
              :columns="wbsColumns"
              :data-source="wbsList"
              :loading="wbsLoading"
              :pagination="false"
              row-key="id"
              :row-selection="{
                selectedRowKeys: selectedWbsRows,
                onChange: onWbsSelectionChange,
                type: 'checkbox',
              }"
              :scroll="{ x: 1200 }"
              size="small">
              <template #bodyCell="{ column, record, index }">
                <template v-if="column.key === 'seqNo'">
                  {{ index + 1 }}
                </template>
                <template v-else-if="column.key === 'nodeType'">
                  <a-tag :color="record.nodeType === 1 ? 'blue' : 'green'">
                    {{ record.nodeType === 1 ? '活动' : '工作包' }}
                  </a-tag>
                </template>
                <template v-else-if="column.key === 'isMilestone'">
                  <a-tag :color="record.isMilestone === 1 ? 'orange' : 'default'">
                    {{ record.isMilestone === 1 ? '是' : '否' }}
                  </a-tag>
                </template>
                <template v-else-if="column.key === 'action'">
                  <a-space>
                    <a-button type="link" size="small" @click="handleEditWbs(record)"> 编辑 </a-button>
                    <a-button type="link" size="small" danger @click="handleDeleteWbs(record)"> 删除 </a-button>
                  </a-space>
                </template>
              </template>
            </a-table>
          </div>
        </a-tab-pane>

        <!-- 阶段配置标签页 -->
        <a-tab-pane key="phase" tab="阶段配置">
          <div class="phase-section">
            <div class="section-header mb-4">
              <div class="flex justify-between items-center">
                <h3 class="text-lg font-semibold">项目阶段配置</h3>
                <a-space>
                  <a-button @click="handleAddPhase">
                    <template #icon><PlusOutlined /></template>
                    添加阶段
                  </a-button>
                  <a-button color="error" :disabled="!selectedPhaseRows.length" @click="handleBatchDeletePhase">
                    <template #icon><DeleteOutlined /></template>
                    批量删除
                  </a-button>
                </a-space>
              </div>
            </div>

            <a-table
              :columns="phaseColumns"
              :data-source="phaseList"
              :loading="phaseLoading"
              :pagination="false"
              row-key="id"
              :row-selection="{
                selectedRowKeys: selectedPhaseRows,
                onChange: onPhaseSelectionChange,
                type: 'checkbox',
              }"
              :scroll="{ x: 1000 }"
              size="small">
              <template #bodyCell="{ column, record, index }">
                <template v-if="column.key === 'seqNo'">
                  {{ index + 1 }}
                </template>
                <template v-else-if="column.key === 'canCut'">
                  <a-tag :color="record.canCut === 1 ? 'green' : 'red'">
                    {{ record.canCut === 1 ? '是' : '否' }}
                  </a-tag>
                </template>
                <template v-else-if="column.key === 'action'">
                  <a-space>
                    <a-button type="link" size="small" @click="handleEditPhase(record)"> 编辑 </a-button>
                    <a-button type="link" size="small" danger @click="handleDeletePhase(record)"> 删除 </a-button>
                  </a-space>
                </template>
              </template>
            </a-table>
          </div>
        </a-tab-pane>

        <!-- 里程碑标签页 -->
        <a-tab-pane key="milestone" tab="里程碑">
          <div class="milestone-section">
            <div class="section-header mb-4">
              <div class="flex justify-between items-center">
                <h3 class="text-lg font-semibold">项目里程碑</h3>
                <a-button @click="handleAddMilestone">
                  <template #icon><PlusOutlined /></template>
                  添加里程碑
                </a-button>
              </div>
            </div>

            <div class="milestone-timeline">
              <a-timeline mode="left">
                <a-timeline-item v-for="milestone in milestoneList" :key="milestone.id" :color="milestone.isCompleted ? 'green' : 'blue'">
                  <template #label>
                    <span class="timeline-date">{{ milestone.plannedDate || '未设置' }}</span>
                  </template>
                  <div class="milestone-content">
                    <h4 class="milestone-title">{{ milestone.name }}</h4>
                    <p class="milestone-description">{{ milestone.description || '无描述' }}</p>
                    <div class="milestone-actions mt-2">
                      <a-space>
                        <a-button size="small" @click="handleEditMilestone(milestone)"> 编辑 </a-button>
                        <a-button size="small" danger @click="handleDeleteMilestone(milestone)"> 删除 </a-button>
                      </a-space>
                    </div>
                  </div>
                </a-timeline-item>
              </a-timeline>

              <div v-if="milestoneList.length === 0" class="empty-milestone text-center py-8">
                <Empty description="暂无里程碑配置">
                  <a-button type="primary" @click="handleAddMilestone"> 添加第一个里程碑 </a-button>
                </Empty>
              </div>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- WBS编辑弹窗 -->
    <a-modal
      v-model:open="wbsModalVisible"
      :title="wbsEditMode === 'add' ? '添加WBS活动' : '编辑WBS活动'"
      width="800px"
      @ok="handleSaveWbs"
      @cancel="handleCancelWbs">
      <a-form :model="wbsForm" :rules="wbsRules" layout="vertical">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="WBS编码" name="wbsCode">
              <a-input v-model:value="wbsForm.wbsCode" placeholder="请输入WBS编码" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="活动名称" name="name" required>
              <a-input v-model:value="wbsForm.name" placeholder="请输入活动名称" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="节点类型" name="nodeType">
              <a-select v-model:value="wbsForm.nodeType" placeholder="请选择节点类型">
                <a-select-option :value="1">活动</a-select-option>
                <a-select-option :value="3">工作包</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="工期(天)" name="duration">
              <a-input-number v-model:value="wbsForm.duration" :min="0" class="w-full" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="层级" name="level">
              <a-input-number v-model:value="wbsForm.level" :min="1" class="w-full" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="是否里程碑" name="isMilestone">
              <a-radio-group v-model:value="wbsForm.isMilestone">
                <a-radio :value="0">否</a-radio>
                <a-radio :value="1">是</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="责任角色" name="responseRoleId">
              <a-select v-model:value="wbsForm.responseRoleId" placeholder="请选择责任角色">
                <!-- TODO: 从角色API加载 -->
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item label="活动描述" name="description">
          <a-textarea v-model:value="wbsForm.description" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 阶段编辑弹窗 -->
    <a-modal
      v-model:open="phaseModalVisible"
      :title="phaseEditMode === 'add' ? '添加项目阶段' : '编辑项目阶段'"
      width="600px"
      @ok="handleSavePhase"
      @cancel="handleCancelPhase">
      <a-form :model="phaseForm" :rules="phaseRules" layout="vertical">
        <a-form-item label="阶段名称" name="phaseName" required>
          <a-input v-model:value="phaseForm.phaseName" placeholder="请输入阶段名称" />
        </a-form-item>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="工期(天)" name="duration">
              <a-input-number v-model:value="phaseForm.duration" :min="0" class="w-full" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="完成权重(%)" name="completionWeight">
              <a-input-number v-model:value="phaseForm.completionWeight" :min="0" :max="100" class="w-full" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item label="可裁剪" name="canCut">
          <a-radio-group v-model:value="phaseForm.canCut">
            <a-radio :value="0">否</a-radio>
            <a-radio :value="1">是</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="阶段描述" name="description">
          <a-textarea v-model:value="phaseForm.description" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import {
    Tabs as ATabs,
    TabPane as ATabPane,
    Table as ATable,
    Button as AButton,
    Space as ASpace,
    Tag as ATag,
    Modal as AModal,
    Form as AForm,
    FormItem as AFormItem,
    Input as AInput,
    InputNumber as AInputNumber,
    Select as ASelect,
    SelectOption as ASelectOption,
    Radio as ARadio,
    RadioGroup as ARadioGroup,
    Textarea as ATextarea,
    Timeline as ATimeline,
    TimelineItem as ATimelineItem,
    Row as ARow,
    Col as ACol,
    Empty,
  } from 'ant-design-vue';
  import { PlusOutlined, DeleteOutlined, SaveOutlined, ImportOutlined } from '@ant-design/icons-vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import {
    getProjectTemplateWbsConfigs,
    getProjectTemplatePhaseConfigs,
    saveProjectTemplateWbsConfigs,
    saveProjectTemplatePhaseConfigs,
    deleteProjectTemplateWbsConfigs,
    deleteProjectTemplatePhaseConfigs,
  } from '/@/api/project/projectTemplate';

  interface Props {
    templateId: string;
  }

  const props = defineProps<Props>();
  const { createMessage, createConfirm } = useMessage();

  // 响应式数据
  const activeTab = ref('wbs');
  const saving = ref(false);

  // WBS相关
  const wbsList = ref([]);
  const wbsLoading = ref(false);
  const selectedWbsRows = ref([]);
  const wbsModalVisible = ref(false);
  const wbsEditMode = ref('add');
  const wbsForm = reactive({
    id: '',
    wbsCode: '',
    name: '',
    nodeType: 1,
    duration: 0,
    level: 1,
    isMilestone: 0,
    responseRoleId: '',
    description: '',
  });

  // 阶段相关
  const phaseList = ref([]);
  const phaseLoading = ref(false);
  const selectedPhaseRows = ref([]);
  const phaseModalVisible = ref(false);
  const phaseEditMode = ref('add');
  const phaseForm = reactive({
    id: '',
    phaseName: '',
    duration: 0,
    completionWeight: 0,
    canCut: 0,
    description: '',
  });

  // 里程碑相关
  const milestoneList = ref([]);

  // 表格列定义
  const wbsColumns = [
    { title: '序号', key: 'seqNo', width: 60, align: 'center' },
    { title: 'WBS编码', dataIndex: 'wbsCode', width: 120 },
    { title: '活动名称', dataIndex: 'name', width: 200 },
    { title: '节点类型', key: 'nodeType', width: 100, align: 'center' },
    { title: '是否里程碑', key: 'isMilestone', width: 100, align: 'center' },
    { title: '工期(天)', dataIndex: 'duration', width: 100, align: 'center' },
    { title: '层级', dataIndex: 'level', width: 80, align: 'center' },
    { title: '责任角色', dataIndex: 'responseRoleName', width: 120 },
    { title: '操作', key: 'action', width: 120, align: 'center', fixed: 'right' },
  ];

  const phaseColumns = [
    { title: '序号', key: 'seqNo', width: 60, align: 'center' },
    { title: '阶段名称', dataIndex: 'phaseName', width: 200 },
    { title: '工期(天)', dataIndex: 'duration', width: 100, align: 'center' },
    { title: '完成权重(%)', dataIndex: 'completionWeight', width: 120, align: 'center' },
    { title: '可裁剪', key: 'canCut', width: 100, align: 'center' },
    { title: '操作', key: 'action', width: 120, align: 'center', fixed: 'right' },
  ];

  // 表单验证规则
  const wbsRules = {
    name: [{ required: true, message: '请输入活动名称', trigger: 'blur' }],
  };

  const phaseRules = {
    phaseName: [{ required: true, message: '请输入阶段名称', trigger: 'blur' }],
  };

  // 方法
  function onWbsSelectionChange(selectedRowKeys: string[]) {
    selectedWbsRows.value = selectedRowKeys;
  }

  function onPhaseSelectionChange(selectedRowKeys: string[]) {
    selectedPhaseRows.value = selectedRowKeys;
  }

  // WBS操作
  function handleAddWbs() {
    resetWbsForm();
    wbsEditMode.value = 'add';
    wbsModalVisible.value = true;
  }

  function handleEditWbs(record: any) {
    Object.assign(wbsForm, record);
    wbsEditMode.value = 'edit';
    wbsModalVisible.value = true;
  }

  function handleDeleteWbs(record: any) {
    createConfirm({
      title: '确认删除',
      content: `确定要删除WBS活动"${record.name}"吗？`,
      onOk: async () => {
        try {
          await deleteProjectTemplateWbsConfigs([record.id]);
          createMessage.success('删除成功');
          await loadWbsConfigs();
        } catch (error) {
          createMessage.error('删除失败');
        }
      },
    });
  }

  function handleBatchDeleteWbs() {
    if (selectedWbsRows.value.length === 0) return;

    createConfirm({
      title: '确认删除',
      content: `确定要删除选中的${selectedWbsRows.value.length}个WBS活动吗？`,
      onOk: async () => {
        try {
          await deleteProjectTemplateWbsConfigs(selectedWbsRows.value);
          createMessage.success('批量删除成功');
          selectedWbsRows.value = [];
          await loadWbsConfigs();
        } catch (error) {
          createMessage.error('批量删除失败');
        }
      },
    });
  }

  async function handleSaveWbs() {
    try {
      const wbsData = { ...wbsForm, templateId: props.templateId };

      if (wbsEditMode.value === 'add') {
        wbsList.value.push(wbsData);
      } else {
        const index = wbsList.value.findIndex(item => item.id === wbsForm.id);
        if (index > -1) {
          wbsList.value[index] = wbsData;
        }
      }

      await saveProjectTemplateWbsConfigs(props.templateId, wbsList.value);
      createMessage.success('保存成功');
      wbsModalVisible.value = false;
      await loadWbsConfigs();
    } catch (error) {
      createMessage.error('保存失败');
    }
  }

  function handleCancelWbs() {
    wbsModalVisible.value = false;
    resetWbsForm();
  }

  function resetWbsForm() {
    Object.assign(wbsForm, {
      id: '',
      wbsCode: '',
      name: '',
      nodeType: 1,
      duration: 0,
      level: 1,
      isMilestone: 0,
      responseRoleId: '',
      description: '',
    });
  }

  // 阶段操作
  function handleAddPhase() {
    resetPhaseForm();
    phaseEditMode.value = 'add';
    phaseModalVisible.value = true;
  }

  function handleEditPhase(record: any) {
    Object.assign(phaseForm, record);
    phaseEditMode.value = 'edit';
    phaseModalVisible.value = true;
  }

  function handleDeletePhase(record: any) {
    createConfirm({
      title: '确认删除',
      content: `确定要删除阶段"${record.phaseName}"吗？`,
      onOk: async () => {
        try {
          await deleteProjectTemplatePhaseConfigs([record.id]);
          createMessage.success('删除成功');
          await loadPhaseConfigs();
        } catch (error) {
          createMessage.error('删除失败');
        }
      },
    });
  }

  function handleBatchDeletePhase() {
    if (selectedPhaseRows.value.length === 0) return;

    createConfirm({
      title: '确认删除',
      content: `确定要删除选中的${selectedPhaseRows.value.length}个阶段吗？`,
      onOk: async () => {
        try {
          await deleteProjectTemplatePhaseConfigs(selectedPhaseRows.value);
          createMessage.success('批量删除成功');
          selectedPhaseRows.value = [];
          await loadPhaseConfigs();
        } catch (error) {
          createMessage.error('批量删除失败');
        }
      },
    });
  }

  async function handleSavePhase() {
    try {
      const phaseData = { ...phaseForm, templateId: props.templateId };

      if (phaseEditMode.value === 'add') {
        phaseList.value.push(phaseData);
      } else {
        const index = phaseList.value.findIndex(item => item.id === phaseForm.id);
        if (index > -1) {
          phaseList.value[index] = phaseData;
        }
      }

      await saveProjectTemplatePhaseConfigs(props.templateId, phaseList.value);
      createMessage.success('保存成功');
      phaseModalVisible.value = false;
      await loadPhaseConfigs();
    } catch (error) {
      createMessage.error('保存失败');
    }
  }

  function handleCancelPhase() {
    phaseModalVisible.value = false;
    resetPhaseForm();
  }

  function resetPhaseForm() {
    Object.assign(phaseForm, {
      id: '',
      phaseName: '',
      duration: 0,
      completionWeight: 0,
      canCut: 0,
      description: '',
    });
  }

  // 其他操作
  function handleImportWbs() {
    createMessage.info('WBS导入功能开发中...');
  }

  function handleImportPhase() {
    createMessage.info('阶段导入功能开发中...');
  }

  async function handleSaveAll() {
    try {
      saving.value = true;
      await Promise.all([saveProjectTemplateWbsConfigs(props.templateId, wbsList.value), saveProjectTemplatePhaseConfigs(props.templateId, phaseList.value)]);
      createMessage.success('保存全部成功');
    } catch (error) {
      createMessage.error('保存失败');
    } finally {
      saving.value = false;
    }
  }

  function handleAddMilestone() {
    createMessage.info('里程碑功能开发中...');
  }

  function handleEditMilestone(milestone: any) {
    createMessage.info('里程碑编辑功能开发中...');
  }

  function handleDeleteMilestone(milestone: any) {
    createMessage.info('里程碑删除功能开发中...');
  }

  // 数据加载
  async function loadWbsConfigs() {
    if (!props.templateId) return;

    try {
      wbsLoading.value = true;
      const response = await getProjectTemplateWbsConfigs(props.templateId);

      if (response.code === 200) {
        wbsList.value = response.data || [];
      } else {
        createMessage.error(response.msg || '加载WBS配置失败');
      }
    } catch (error) {
      console.error('加载WBS配置失败:', error);
      createMessage.error('加载WBS配置失败');
    } finally {
      wbsLoading.value = false;
    }
  }

  async function loadPhaseConfigs() {
    if (!props.templateId) return;

    try {
      phaseLoading.value = true;
      const response = await getProjectTemplatePhaseConfigs(props.templateId);

      if (response.code === 200) {
        phaseList.value = response.data || [];
      } else {
        createMessage.error(response.msg || '加载阶段配置失败');
      }
    } catch (error) {
      console.error('加载阶段配置失败:', error);
      createMessage.error('加载阶段配置失败');
    } finally {
      phaseLoading.value = false;
    }
  }

  function loadMilestones() {
    // TODO: 加载里程碑数据
    milestoneList.value = [];
  }

  // 初始化
  onMounted(async () => {
    if (props.templateId) {
      await Promise.all([loadWbsConfigs(), loadPhaseConfigs(), loadMilestones()]);
    }
  });
</script>

<style scoped>
  .template-plan-page {
    background: #f5f5f5;
    min-height: 100vh;
  }

  .plan-tabs {
    background: white;
    border-radius: 8px;
    padding: 16px;
  }

  .section-header {
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 12px;
    margin-bottom: 16px;
  }

  .milestone-timeline {
    padding: 20px 0;
  }

  .timeline-date {
    font-size: 12px;
    color: #666;
    white-space: nowrap;
  }

  .milestone-content {
    background: white;
    padding: 16px;
    border-radius: 8px;
    border: 1px solid #f0f0f0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }

  .milestone-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #333;
  }

  .milestone-description {
    color: #666;
    font-size: 14px;
    margin-bottom: 0;
  }

  .empty-milestone {
    background: white;
    border-radius: 8px;
    border: 2px dashed #d9d9d9;
  }

  :deep(.ant-tabs-content-holder) {
    padding: 0;
  }

  :deep(.ant-table-thead > tr > th) {
    background: #fafafa;
    font-weight: 600;
  }

  :deep(.ant-timeline-item-content) {
    min-height: auto;
  }
</style>
