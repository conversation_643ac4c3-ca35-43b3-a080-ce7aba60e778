<template>
  <div>
    <BasicTable @register="registerTable" :searchInfo="searchInfo">
      <template #toolbar>
        <a-button type="primary" @click="handleCreate">新增付款</a-button>
      </template>

      <!-- 日期查询插槽 -->
      <template #form-external2="{ model }">
        <a-input-group compact>
          <a-form-item-rest>
            <a-select allow-clear v-model:value="model.dateType" class="form-external2-select" placeholder="请选择" style="width: 130px; text-align: center">
              <a-select-option v-for="item in dataTypeOption" :value="item.id" :key="item.id">{{ item.fullName }}</a-select-option>
            </a-select>
            <RangePicker style="width: calc(100% - 130px)" v-model:value="model.timeRange" />
          </a-form-item-rest>
        </a-input-group>
      </template>

      <!-- 表格单元格自定义渲染 -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'paycontractInfo'">
          <div>
            <div v-if="record.paycontractNo && record.paycontractName"> {{ record.paycontractNo }} - {{ record.paycontractName }} </div>
            <div v-else-if="record.paycontractName">
              {{ record.paycontractName }}
            </div>
            <div v-else-if="record.paycontractNo">
              {{ record.paycontractNo }}
            </div>
            <div v-else>-</div>
          </div>
        </template>
        <template v-if="column.key === 'cmMoney'">
          {{ formatAmount(record.cmMoney) }}
        </template>
        <template v-if="column.key === 'ybAmount'">
          {{ formatAmount(record.ybAmount) }}
        </template>
        <template v-if="column.key === 'ebAmount'">
          {{ formatAmount(record.ebAmount) }}
        </template>
        <template v-if="column.key === 'otherAmount'">
          {{ formatAmount(record.otherAmount) }}
        </template>
        <template v-if="column.key === 'ratio'">
          {{ record.ratio ? `${record.ratio}%` : '-' }}
        </template>
        <template v-if="column.key === 'yufuDate'">
          {{ record.yufuDate ? formatToDate(record.yufuDate) : '-' }}
        </template>
        <template v-if="column.key === 'fukuanDate'">
          {{ record.fukuanDate ? formatToDate(record.fukuanDate) : '-' }}
        </template>
        <template v-if="column.key === 'payStatus'">
          <a-tag :color="getPayStatusColor(record.payStatus)">
            {{ getPayStatusText(record.payStatus) }}
          </a-tag>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'clarity:info-standard-line',
                label: '查看',
                onClick: handleView.bind(null, record),
              },
              {
                icon: 'clarity:note-edit-line',
                label: '编辑',
                onClick: handleEdit.bind(null, record),
              },
              {
                icon: 'ant-design:dollar-circle-outlined',
                label: '登记付款',
                onClick: handlePayment.bind(null, record),
                ifShow: String(record.payStatus) !== '1' && String(record.payStatus) !== 'paid',
              },
              {
                icon: 'ant-design:delete-outlined',
                label: '删除',
                color: 'error',
                popConfirm: {
                  title: '是否确认删除',
                  confirm: handleDelete.bind(null, record),
                },
              },
            ]" />
        </template>
      </template>
    </BasicTable>

    <!-- 新增/编辑抽屉 -->
    <PaycontractMoneyDrawer @register="registerDrawer" @success="handleSuccess" />

    <!-- 付款登记弹窗 -->
    <PaymentModal @register="registerPaymentModal" @success="handleSuccess" />
  </div>
</template>

<script lang="ts" setup>
  import { reactive } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import { useDrawer } from '/@/components/Drawer';
  import { getPaycontractMoneyList, deletePaycontractMoney } from '/@/api/project/paycontractMoney';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { formatToDate } from '/@/utils/dateUtil';
  import { RangePicker } from 'ant-design-vue';

  import PaycontractSelect from '/@/views/project/components/PaycontractSelect.vue';
  import PaycontractMoneyDrawer from './PaycontractMoneyDrawer.vue';
  import PaymentModal from './PaymentModal.vue';

  import type { FormSchema } from '/@/components/Table';
  import type { PaycontractMoneyModel } from '/@/api/project/paycontractMoney';

  const { createMessage } = useMessage();
  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerPaymentModal, { openModal: openPaymentModal }] = useModal();

  const searchInfo = reactive<Recordable>({});
  defineOptions({ name: 'project-biz-payContract-money' });

  // 表格列定义
  const columns = [
    {
      title: '采购合同',
      key: 'paycontractInfo',
      dataIndex: 'paycontractInfo',
      width: 200,
    },
    {
      title: '付款条件',
      dataIndex: 'fktj',
      width: 150,
    },
    {
      title: '付款比例',
      key: 'ratio',
      dataIndex: 'ratio',
      width: 100,
    },
    {
      title: '付款金额',
      key: 'cmMoney',
      dataIndex: 'cmMoney',
      width: 120,
      sorter: true,
    },
    {
      title: '付款状态',
      key: 'payStatus',
      dataIndex: 'payStatus',
      width: 100,
    },
    {
      title: '预付日期',
      key: 'yufuDate',
      dataIndex: 'yufuDate',
      width: 120,
    },
    {
      title: '付款日期',
      key: 'fukuanDate',
      dataIndex: 'fukuanDate',
      width: 120,
    },
    {
      title: '开发一部金额',
      key: 'ybAmount',
      dataIndex: 'ybAmount',
      width: 120,
    },
    {
      title: '开发二部金额',
      key: 'ebAmount',
      dataIndex: 'ebAmount',
      width: 120,
    },
    {
      title: '综合金额',
      key: 'otherAmount',
      dataIndex: 'otherAmount',
      width: 120,
    },
    {
      title: '备注',
      dataIndex: 'note',
      width: 150,
      ellipsis: true,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      width: 150,
    },
  ];

  // 搜索表单配置
  const searchFormSchema: FormSchema[] = [
    {
      field: 'dateType',
      label: '',
      component: 'Input',
      colProps: { span: 4 },
      componentProps: { placeholder: '请输入' },
      show: false,
    },
    {
      field: 'paycontractName',
      label: '采购合同',
      component: 'Input',
      colProps: { span: 4 },
    },
    {
      field: 'payStatus',
      label: '付款状态',
      component: 'Select',
      componentProps: {
        options: [
          { fullName: '全部', id: '' },
          { fullName: '未付款', id: '0' },
          { fullName: '已付款', id: '1' },
        ],
        placeholder: '请选择付款状态',
        allowClear: true,
      },
      colProps: { span: 3 },
    },

    {
      field: 'external2',
      label: '',
      component: 'Input',
      slot: 'external2',
      colProps: { span: 6 },
      componentProps: { placeholder: '' },
    },
  ];

  // 日期类型选项
  const dataTypeOption = [
    { fullName: '预付日期', id: 'yufu_date' },
    { fullName: '付款日期', id: 'fukuan_date' },
    { fullName: '创建时间', id: 'create_time' },
    { fullName: '更新时间', id: 'update_time' },
  ];

  // 表格配置
  const [registerTable, { reload, getForm }] = useTable({
    title: '付款管理列表',
    api: getPaycontractMoneyList,
    rowKey: 'id',
    columns,
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      fieldMapToTime: [['timeRange', ['startTime', 'endTime'], 'YYYY-MM-DD']],
      autoAdvancedLine: 2,
      showAdvancedButton: false,
    },
    useSearchForm: true,
    showTableSetting: true,
    bordered: true,
    actionColumn: {
      width: 200,
      title: '操作',
      dataIndex: 'action',
    },
  });

  // 格式化金额
  function formatAmount(amount: number | undefined) {
    if (amount === undefined || amount === null) return '-';
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
    }).format(amount);
  }

  // 获取付款状态颜色
  function getPayStatusColor(status: string | number) {
    const statusStr = String(status);
    const colorMap = {
      '0': 'red', // 未付款
      '1': 'green', // 已付款
      unpaid: 'red',
      paid: 'green',
    };
    return colorMap[statusStr] || 'default';
  }

  // 获取付款状态文本
  function getPayStatusText(status: string | number) {
    const statusStr = String(status);
    const textMap = {
      '0': '未付款',
      '1': '已付款',
      unpaid: '未付款',
      paid: '已付款',
    };
    return textMap[statusStr] || status;
  }

  // 新增付款
  function handleCreate() {
    const drawerData = {
      isUpdate: false,
      readonly: false, // 明确设置为非只读
    };
    console.log('新增付款，传递给抽屉的数据:', drawerData);
    openDrawer(true, drawerData);
  }

  // 查看详情
  function handleView(record: PaycontractMoneyModel) {
    console.log('查看付款记录:', record);
    openDrawer(true, {
      record: { id: record.id }, // 只传递ID，让抽屉组件自己获取详情
      isUpdate: false,
      readonly: true,
    });
  }

  // 编辑付款
  function handleEdit(record: PaycontractMoneyModel) {
    console.log('编辑付款记录:', record);
    const drawerData = {
      record: { id: record.id }, // 只传递ID，让抽屉组件自己获取详情
      isUpdate: true,
      readonly: false, // 明确设置为非只读
    };
    console.log('传递给抽屉的数据:', drawerData);
    openDrawer(true, drawerData);
  }

  // 登记付款
  function handlePayment(record: PaycontractMoneyModel) {
    openPaymentModal(true, {
      record,
    });
  }

  // 删除付款
  async function handleDelete(record: PaycontractMoneyModel) {
    try {
      await deletePaycontractMoney(record.id);
      createMessage.success('删除成功');
      reload();
    } catch (error) {
      createMessage.error('删除失败');
    }
  }

  // 操作成功回调
  function handleSuccess() {
    reload();
  }
</script>

<style scoped>
  :deep(.form-external2-select .ant-select-selector) {
    background-color: #fafafa;
  }
</style>
