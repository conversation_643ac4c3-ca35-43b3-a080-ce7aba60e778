package com.xinghuo.manhour.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 员工填写工时项目Entity
 *
 * <AUTHOR>
 * @date 2023-12-13
 */

@Data
@TableName("zz_proj_manhour")
public class ManhourEntity  {
    @TableId(value ="F_ID"  )
    private String id;
    @TableField(value = "MT_ID" )
    private String mtId;
    @TableField(value = "USER_ID" )
    private String userId;
    @TableField(value = "PROJ_TYPE" )
    private String projType;
    @TableField(value = "PROJECT_ID" )
    private String projectId;
    @TableField(value = "PROJECT_NAME" )
    private String projectName;
    @TableField(value = "MODULE_ID" )
    private String moduleId;
    @TableField(value = "MODULE_NAME" )
    private String moduleName;
    @TableField(value = "WORK_TYPE" )
    private String workType;
    @TableField(value = "MONTH" )
    private String month;
    @TableField(value = "WORK_MONTH" )
    private BigDecimal workMonth;
    @TableField(value = "WORK_NOTE" )
    private String workNote;
    @TableField(value = "CREATE_USER_ID" )
    private String createUserId;
    @TableField(value = "CREATE_TIME")
    private Date createTime;
    @TableField(value = "LAST_MODIFIED_USER_ID" )
    private String lastModifiedUserId;
    @TableField(value = "UPDATE_TIME" )
    private Date updateTime;
    @TableField(value = "F_DELETEMARK" )
    private Integer deletemark;
    @TableField(value = "F_FLOWID" )
    private String flowid;
    @TableField(value = "F_TENANTID" )
    private String tenantid;

    // 扩展字段，用于分析查询
    @TableField(exist = false)
    private String userName;
    @TableField(exist = false)
    private String fbId;
    @TableField(exist = false)
    private String fbName;
    @TableField(exist = false)
    private String projTypeName;
    @TableField(exist = false)
    private Integer status;
}