<template>
  <div>
    <a-row :gutter="8">
      <a-col :span="customerColSpan">
        <CustomerSelect
          v-model:value="selectedCustomerId"
          :placeholder="customerPlaceholder"
          :disabled="disabled"
          :custType="custType"
          @change="handleCustomerChange" />
      </a-col>
      <a-col :span="linkmanColSpan">
        <a-select
          v-model:value="selectedLinkmanId"
          :placeholder="linkmanPlaceholder"
          :options="linkmanOptions"
          :showSearch="true"
          :disabled="disabled || !selectedCustomerId"
          :filterOption="filterOption"
          @change="handleLinkmanChange"
          style="width: 100%">
          <template v-if="loading" #notFoundContent>
            <a-spin size="small" />
          </template>
        </a-select>
      </a-col>
    </a-row>
  </div>
</template>

<script lang="ts" setup>
  import { ref, watch } from 'vue';
  import { getLinkmanList } from '/@/api/project/customerLinkman/index';
  import { CustomerSelect } from './index';
  import { Spin } from 'ant-design-vue';
  const ASpin = Spin;

  const props = defineProps({
    value: {
      type: Object,
      default: () => ({}),
    },
    custType: {
      type: String,
      default: 'JIAFANG', // 默认为甲方单位
    },
    customerPlaceholder: {
      type: String,
      default: '请选择单位',
    },
    linkmanPlaceholder: {
      type: String,
      default: '请选择联系人',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    customerColSpan: {
      type: Number,
      default: 12,
    },
    linkmanColSpan: {
      type: Number,
      default: 12,
    },
  });

  const emit = defineEmits(['update:value', 'change']);

  // 选中的客户ID
  const selectedCustomerId = ref('');
  // 选中的联系人ID
  const selectedLinkmanId = ref('');
  // 联系人选项
  const linkmanOptions = ref<any[]>([]);
  // 联系人列表
  const linkmanList = ref<any[]>([]);
  // 加载状态
  const loading = ref(false);

  // 监听value变化
  watch(
    () => props.value,
    val => {
      if (val) {
        selectedCustomerId.value = val.customerId || '';
        selectedLinkmanId.value = val.linkmanId || '';

        // 如果有客户ID但没有联系人列表，则加载联系人列表
        if (selectedCustomerId.value && linkmanOptions.value.length === 0) {
          loadLinkmanOptions(selectedCustomerId.value);
        }
      }
    },
    { immediate: true, deep: true },
  );

  // 监听客户ID变化
  watch(
    () => selectedCustomerId.value,
    val => {
      if (val) {
        loadLinkmanOptions(val);
      } else {
        linkmanOptions.value = [];
        linkmanList.value = [];
        selectedLinkmanId.value = '';
        updateValue();
      }
    },
  );

  // 过滤选项
  function filterOption(input: string, option: any) {
    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  }

  // 加载客户联系人列表
  async function loadLinkmanOptions(customerId: string) {
    if (!customerId) {
      linkmanOptions.value = [];
      linkmanList.value = [];
      return;
    }

    try {
      loading.value = true;
      console.log('开始获取联系人列表:', { customerId });

      const result = await getLinkmanList({ cuId: customerId, status: 1 });
      console.log('联系人API返回结果:', result);

      // 处理不同的返回数据结构
      let items: any[] = [];
      if (result && result.code === 200 && result.data) {
        items = result.data.list || [];
      } else if (result && result.items) {
        items = result.items || [];
      } else if (result && Array.isArray(result)) {
        items = result;
      } else {
        console.warn('联系人API返回格式不正确:', result);
        items = [];
      }

      console.log('处理后的联系人items:', items);

      linkmanList.value = items;
      linkmanOptions.value = items.map((item: any) => ({
        label: `${item.linkman}${item.telephone ? ' - ' + item.telephone : ''}`,
        value: item.id,
        fullName: `${item.linkman}${item.telephone ? ' - ' + item.telephone : ''}`,
        id: item.id,
        linkman: item.linkman,
        telephone: item.telephone,
        ...item,
      }));

      console.log('转换后的联系人选项:', linkmanOptions.value);
    } catch (error) {
      console.error('获取客户联系人列表失败:', error);
    } finally {
      loading.value = false;
    }
  }

  // 处理客户选择变化
  function handleCustomerChange(value: string, option: any) {
    selectedCustomerId.value = value;
    selectedLinkmanId.value = '';
    updateValue();
  }

  // 处理联系人选择变化
  function handleLinkmanChange(value: string, option: any) {
    selectedLinkmanId.value = value;
    updateValue();
  }

  // 更新值
  function updateValue() {
    const selectedLinkman = linkmanList.value.find(item => item.id === selectedLinkmanId.value);

    const value = {
      customerId: selectedCustomerId.value,
      linkmanId: selectedLinkmanId.value,
      linkman: selectedLinkman?.linkman || '',
      telephone: selectedLinkman?.telephone || '',
    };

    emit('update:value', value);
    emit('change', value);
  }
</script>
