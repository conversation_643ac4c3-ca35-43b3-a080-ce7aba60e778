package com.xinghuo.project.biz.service;

import com.xinghuo.project.biz.entity.BizDepartmentEntity;
import com.xinghuo.common.base.service.BaseService;

import java.util.List;

/**
 * 业务部门服务接口
 * 
 * <AUTHOR>
 * @version V2.0
 * @copyright 易趋集团
 * @date 2024-01-01
 */
public interface BizDepartmentService extends BaseService<BizDepartmentEntity> {

    /**
     * 获取所有启用的部门
     * 
     * @return 部门列表
     */
    List<BizDepartmentEntity> getEnabledDepartments();

    /**
     * 根据部门类型获取部门
     * 
     * @param departmentType 部门类型
     * @return 部门列表
     */
    List<BizDepartmentEntity> getByType(Integer departmentType);

    /**
     * 根据部门编码获取部门
     * 
     * @param departmentCode 部门编码
     * @return 部门实体
     */
    BizDepartmentEntity getByCode(String departmentCode);

    /**
     * 获取部门选择器数据
     * 
     * @return 部门选择器数据
     */
    List<BizDepartmentEntity> getDepartmentSelector();

    /**
     * 验证部门编码是否唯一
     * 
     * @param departmentCode 部门编码
     * @param excludeId 排除的ID
     * @return 是否唯一
     */
    boolean isCodeUnique(String departmentCode, String excludeId);

    /**
     * 获取部门树形结构
     * 
     * @return 树形部门列表
     */
    List<BizDepartmentEntity> getDepartmentTree();

    /**
     * 更新部门状态
     * 
     * @param id 部门ID
     * @param enabledMark 启用状态
     */
    void updateStatus(String id, Integer enabledMark);
}