package com.xinghuo.manhour.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.manhour.model.analysis.*;
import com.xinghuo.manhour.service.ManhourAnalysisService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 工时分析控制器
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Slf4j
@RestController
@RequestMapping("/api/manhour/analysis")
@Tag(name = "工时分析管理", description = "工时分析相关接口")
public class ManhourAnalysisController {

    @Autowired
    private ManhourAnalysisService manhourAnalysisService;

    /**
     * 获取工时分析概览数据
     *
     * @param params 查询参数
     * @return 概览数据
     */
    @GetMapping("/overview")
    @Operation(summary = "获取工时分析概览数据")
    public ActionResult<WorkhourAnalysisOverview> getOverview(WorkhourAnalysisParams params) {
        WorkhourAnalysisOverview overview = manhourAnalysisService.getOverview(params);
        return ActionResult.success(overview);
    }

    /**
     * 获取工时分析图表数据
     *
     * @param params 查询参数
     * @return 图表数据
     */
    @GetMapping("/charts")
    @Operation(summary = "获取工时分析图表数据")
    public ActionResult<ChartDataModel> getCharts(WorkhourAnalysisParams params) {
        ChartDataModel chartData = manhourAnalysisService.getCharts(params);
        return ActionResult.success(chartData);
    }

    /**
     * 获取个人分析列表
     *
     * @param pagination 分页参数
     * @return 个人分析数据列表
     */
    @PostMapping("/personal/list")
    @Operation(summary = "获取个人分析列表")
    public ActionResult getPersonalAnalysisList(@RequestBody WorkhourAnalysisPagination pagination) {
        List<PersonalAnalysisVO> list = manhourAnalysisService.getPersonalAnalysisList(pagination);

        PaginationVO paginationVO = new PaginationVO();
        paginationVO.setCurrentPage(pagination.getCurrentPage());
        paginationVO.setPageSize(pagination.getPageSize());
        paginationVO.setTotal(pagination.getTotal());

        return ActionResult.page(list, paginationVO);
    }

    /**
     * 获取项目分析列表
     *
     * @param pagination 分页参数
     * @return 项目分析数据列表
     */
    @PostMapping("/project/list")
    @Operation(summary = "获取项目分析列表")
    public ActionResult getProjectAnalysisList(@RequestBody WorkhourAnalysisPagination pagination) {
        List<ProjectAnalysisVO> list = manhourAnalysisService.getProjectAnalysisList(pagination);

        PaginationVO paginationVO = new PaginationVO();
        paginationVO.setCurrentPage(pagination.getCurrentPage());
        paginationVO.setPageSize(pagination.getPageSize());
        paginationVO.setTotal(pagination.getTotal());

        return ActionResult.page(list, paginationVO);
    }

    /**
     * 获取分部分析列表
     *
     * @param pagination 分页参数
     * @return 分部分析数据列表
     */
    @PostMapping("/department/list")
    @Operation(summary = "获取分部分析列表")
    public ActionResult getDepartmentAnalysisList(@RequestBody WorkhourAnalysisPagination pagination) {
        List<DepartmentAnalysisVO> list = manhourAnalysisService.getDepartmentAnalysisList(pagination);

        PaginationVO paginationVO = new PaginationVO();
        paginationVO.setCurrentPage(pagination.getCurrentPage());
        paginationVO.setPageSize(pagination.getPageSize());
        paginationVO.setTotal(pagination.getTotal());

        return ActionResult.page(list, paginationVO);
    }

    /**
     * 获取工时明细列表
     *
     * @param pagination 分页参数
     * @return 工时明细数据列表
     */
    @PostMapping("/detail/list")
    @Operation(summary = "获取工时明细列表")
    public ActionResult getWorkhourDetailList(@RequestBody WorkhourAnalysisPagination pagination) {
        List<WorkhourDetailVO> list = manhourAnalysisService.getWorkhourDetailList(pagination);

        PaginationVO paginationVO = new PaginationVO();
        paginationVO.setCurrentPage(pagination.getCurrentPage());
        paginationVO.setPageSize(pagination.getPageSize());
        paginationVO.setTotal(pagination.getTotal());

        return ActionResult.page(list, paginationVO);
    }

    /**
     * 导出工时分析报表
     *
     * @param params   查询参数
     * @param response HTTP响应
     */
    @PostMapping("/export")
    @Operation(summary = "导出工时分析报表")
    public void exportWorkhourAnalysis(@RequestBody WorkhourAnalysisParams params, HttpServletResponse response) {
        manhourAnalysisService.exportWorkhourAnalysis(params, response);
    }

    /**
     * 获取项目选择器数据
     *
     * @return 项目列表
     */
    @GetMapping("/selector/project")
    @Operation(summary = "获取项目选择器数据")
    public ActionResult<List<Map<String, Object>>> getProjectSelector() {
        List<Map<String, Object>> projects = manhourAnalysisService.getProjectSelector();
        return ActionResult.success(projects);
    }

    /**
     * 获取分部选择器数据
     *
     * @return 分部列表
     */
    @GetMapping("/selector/department")
    @Operation(summary = "获取分部选择器数据")
    public ActionResult<List<Map<String, Object>>> getDepartmentSelector() {
        List<Map<String, Object>> departments = manhourAnalysisService.getDepartmentSelector();
        return ActionResult.success(departments);
    }

    /**
     * 获取个人工时效率详情
     *
     * @param userId 用户ID
     * @param params 查询参数
     * @return 个人效率详情
     */
    @GetMapping("/personal/{userId}/efficiency")
    @Operation(summary = "获取个人工时效率详情")
    public ActionResult<PersonalEfficiencyDetail> getPersonalEfficiencyDetail(
            @PathVariable String userId, WorkhourAnalysisParams params) {
        PersonalEfficiencyDetail detail = manhourAnalysisService.getPersonalEfficiencyDetail(userId, params);
        return ActionResult.success(detail);
    }

    /**
     * 获取项目健康度详情
     *
     * @param projectId 项目ID
     * @param params    查询参数
     * @return 项目健康度详情
     */
    @GetMapping("/project/{projectId}/health")
    @Operation(summary = "获取项目健康度详情")
    public ActionResult<ProjectHealthDetail> getProjectHealthDetail(
            @PathVariable String projectId, WorkhourAnalysisParams params) {
        ProjectHealthDetail detail = manhourAnalysisService.getProjectHealthDetail(projectId, params);
        return ActionResult.success(detail);
    }

    /**
     * 获取分部资源利用率详情
     *
     * @param fbId   分部ID
     * @param params 查询参数
     * @return 分部资源利用率详情
     */
    @GetMapping("/department/{fbId}/utilization")
    @Operation(summary = "获取分部资源利用率详情")
    public ActionResult<DepartmentUtilizationDetail> getDepartmentUtilizationDetail(
            @PathVariable String fbId, WorkhourAnalysisParams params) {
        DepartmentUtilizationDetail detail = manhourAnalysisService.getDepartmentUtilizationDetail(fbId, params);
        return ActionResult.success(detail);
    }

    /**
     * 获取技能标签统计
     *
     * @param params 查询参数
     * @return 技能标签统计
     */
    @GetMapping("/skill-tags/statistics")
    @Operation(summary = "获取技能标签统计")
    public ActionResult<List<Map<String, Object>>> getSkillTagsStatistics(WorkhourAnalysisParams params) {
        List<Map<String, Object>> statistics = manhourAnalysisService.getSkillTagsStatistics(params);
        return ActionResult.success(statistics);
    }

    /**
     * 获取工时类型统计
     *
     * @param params 查询参数
     * @return 工时类型统计
     */
    @GetMapping("/work-type/statistics")
    @Operation(summary = "获取工时类型统计")
    public ActionResult<List<Map<String, Object>>> getWorkTypeStatistics(WorkhourAnalysisParams params) {
        List<Map<String, Object>> statistics = manhourAnalysisService.getWorkTypeStatistics(params);
        return ActionResult.success(statistics);
    }
}
