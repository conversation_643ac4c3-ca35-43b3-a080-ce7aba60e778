package com.xinghuo.project.schema.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.project.schema.entity.ProjectSchemaPhaseEntity;
import com.xinghuo.project.schema.model.ProjectSchemaPhasePagination;
import com.xinghuo.project.schema.model.vo.ProjectSchemaPhaseVO;
import com.xinghuo.project.schema.model.vo.ProjectSchemaPhaseSelectVO;
import com.xinghuo.project.schema.service.ProjectSchemaPhaseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 项目模板阶段配置管理控制器
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
@Slf4j
@Tag(name = "项目模板阶段配置管理", description = "项目模板阶段配置管理相关接口")
@RestController
@RequestMapping("/api/project/schema/projectSchemaPhase")
public class ProjectSchemaPhaseController {

    @Resource
    private ProjectSchemaPhaseService projectSchemaPhaseService;

    /**
     * 获取阶段配置列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取阶段配置列表")
    public ActionResult<PageListVO<ProjectSchemaPhaseVO>> list(@RequestBody ProjectSchemaPhasePagination pagination) {
        try {
            List<ProjectSchemaPhaseEntity> list = projectSchemaPhaseService.getList(pagination);
            List<ProjectSchemaPhaseVO> listVO = BeanCopierUtils.copyList(list, ProjectSchemaPhaseVO.class);

            // 对结果进行数据转换和补充
            for (ProjectSchemaPhaseVO vo : listVO) {
                // 是否可裁剪名称转换
                if (vo.getCanCut() != null) {
                    vo.setCanCutName(vo.getCanCut() == 1 ? "是" : "否");
                }

                // TODO: 可以在这里添加其他关联数据的查询和设置
                // 例如：项目模板名称、阶段库信息、审批流程名称、检查单模板名称等
            }

            PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
            return ActionResult.page(listVO, page);
        } catch (Exception e) {
            log.error("获取阶段配置列表失败", e);
            return ActionResult.fail("获取阶段配置列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据项目模板ID获取阶段配置列表
     */
    @GetMapping("/getListByTemplateId/{projectTemplateId}")
    @Operation(summary = "根据项目模板ID获取阶段配置列表")
    public ActionResult<List<ProjectSchemaPhaseEntity>> getListByTemplateId(
            @Parameter(description = "项目模板ID") @PathVariable String projectTemplateId) {
        try {
            List<ProjectSchemaPhaseEntity> list = projectSchemaPhaseService.getListByTemplateId(projectTemplateId);
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("根据项目模板ID获取阶段配置列表失败", e);
            return ActionResult.fail("获取阶段配置列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取阶段配置详情
     */
    @GetMapping("/getInfo/{id}")
    @Operation(summary = "获取阶段配置详情")
    public ActionResult<ProjectSchemaPhaseEntity> getInfo(
            @Parameter(description = "阶段配置ID") @PathVariable String id) {
        try {
            ProjectSchemaPhaseEntity entity = projectSchemaPhaseService.getInfo(id);
            if (entity == null) {
                return ActionResult.fail("阶段配置不存在");
            }
            return ActionResult.success(entity);
        } catch (Exception e) {
            log.error("获取阶段配置详情失败", e);
            return ActionResult.fail("获取阶段配置详情失败：" + e.getMessage());
        }
    }

    /**
     * 创建阶段配置
     */
    @PostMapping("/create")
    @Operation(summary = "创建阶段配置")
    public ActionResult<String> create(@Valid @RequestBody ProjectSchemaPhaseEntity entity) {
        try {
            String id = projectSchemaPhaseService.create(entity);
            return ActionResult.success(id, "创建成功");
        } catch (Exception e) {
            log.error("创建阶段配置失败", e);
            return ActionResult.fail("创建阶段配置失败：" + e.getMessage());
        }
    }

    /**
     * 更新阶段配置
     */
    @PostMapping("/update/{id}")
    @Operation(summary = "更新阶段配置")
    public ActionResult<String> update(
            @Parameter(description = "阶段配置ID") @PathVariable String id,
            @Valid @RequestBody ProjectSchemaPhaseEntity entity) {
        try {
            projectSchemaPhaseService.update(id, entity);
            return ActionResult.success("更新成功");
        } catch (Exception e) {
            log.error("更新阶段配置失败", e);
            return ActionResult.fail("更新阶段配置失败：" + e.getMessage());
        }
    }

    /**
     * 删除阶段配置
     */
    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除阶段配置")
    public ActionResult<String> delete(
            @Parameter(description = "阶段配置ID") @PathVariable String id) {
        try {
            projectSchemaPhaseService.delete(id);
            return ActionResult.success("删除成功");
        } catch (Exception e) {
            log.error("删除阶段配置失败", e);
            return ActionResult.fail("删除阶段配置失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除阶段配置
     */
    @DeleteMapping("/batchDelete")
    @Operation(summary = "批量删除阶段配置")
    public ActionResult<String> batchDelete(@RequestBody List<String> ids) {
        try {
            projectSchemaPhaseService.batchDelete(ids);
            return ActionResult.success("批量删除成功");
        } catch (Exception e) {
            log.error("批量删除阶段配置失败", e);
            return ActionResult.fail("批量删除阶段配置失败：" + e.getMessage());
        }
    }

    /**
     * 根据项目模板ID删除所有阶段配置
     */
    @DeleteMapping("/deleteByTemplateId/{projectTemplateId}")
    @Operation(summary = "根据项目模板ID删除所有阶段配置")
    public ActionResult<String> deleteByTemplateId(
            @Parameter(description = "项目模板ID") @PathVariable String projectTemplateId) {
        try {
            projectSchemaPhaseService.deleteByTemplateId(projectTemplateId);
            return ActionResult.success("删除成功");
        } catch (Exception e) {
            log.error("根据项目模板ID删除阶段配置失败", e);
            return ActionResult.fail("删除阶段配置失败：" + e.getMessage());
        }
    }

    /**
     * 获取阶段配置选择列表
     */
    @GetMapping("/getSelectList")
    @Operation(summary = "获取阶段配置选择列表")
    public ActionResult<List<ProjectSchemaPhaseSelectVO>> getSelectList(
            @Parameter(description = "项目模板ID") @RequestParam(required = false) String projectTemplateId,
            @Parameter(description = "关键字") @RequestParam(required = false) String keyword) {
        try {
            List<ProjectSchemaPhaseEntity> list = projectSchemaPhaseService.getSelectList(projectTemplateId, keyword);
            List<ProjectSchemaPhaseSelectVO> selectList = BeanCopierUtils.copyList(list, ProjectSchemaPhaseSelectVO.class);

            // 构建fullName
            for (ProjectSchemaPhaseSelectVO vo : selectList) {
                String fullName = "";
                if (vo.getSeqNo() != null) {
                    fullName += "[" + vo.getSeqNo() + "] ";
                }
                if (vo.getPhaseName() != null) {
                    fullName += vo.getPhaseName();
                }
                vo.setFullName(fullName);
            }

            return ActionResult.success(selectList);
        } catch (Exception e) {
            log.error("获取阶段配置选择列表失败", e);
            return ActionResult.fail("获取阶段配置选择列表失败：" + e.getMessage());
        }
    }

    /**
     * 复制阶段配置
     */
    @PostMapping("/copyPhaseConfigs")
    @Operation(summary = "复制阶段配置")
    public ActionResult<String> copyPhaseConfigs(
            @Parameter(description = "源模板ID") @RequestParam String sourceTemplateId,
            @Parameter(description = "目标模板ID") @RequestParam String targetTemplateId) {
        try {
            int count = projectSchemaPhaseService.copyPhaseConfigs(sourceTemplateId, targetTemplateId);
            return ActionResult.success("复制成功，共复制 " + count + " 个阶段配置");
        } catch (Exception e) {
            log.error("复制阶段配置失败", e);
            return ActionResult.fail("复制阶段配置失败：" + e.getMessage());
        }
    }

    /**
     * 批量保存阶段配置
     */
    @PostMapping("/batchSave/{projectTemplateId}")
    @Operation(summary = "批量保存阶段配置")
    public ActionResult<String> batchSave(
            @Parameter(description = "项目模板ID") @PathVariable String projectTemplateId,
            @RequestBody List<ProjectSchemaPhaseEntity> phaseConfigs) {
        try {
            projectSchemaPhaseService.batchSave(projectTemplateId, phaseConfigs);
            return ActionResult.success("批量保存成功");
        } catch (Exception e) {
            log.error("批量保存阶段配置失败", e);
            return ActionResult.fail("批量保存阶段配置失败：" + e.getMessage());
        }
    }

    /**
     * 更新序号
     */
    @PostMapping("/updateSeqNo/{id}")
    @Operation(summary = "更新序号")
    public ActionResult<String> updateSeqNo(
            @Parameter(description = "阶段配置ID") @PathVariable String id,
            @Parameter(description = "序号") @RequestParam Integer seqNo) {
        try {
            projectSchemaPhaseService.updateSeqNo(id, seqNo);
            return ActionResult.success("更新序号成功");
        } catch (Exception e) {
            log.error("更新序号失败", e);
            return ActionResult.fail("更新序号失败：" + e.getMessage());
        }
    }

    /**
     * 批量更新序号
     */
    @PostMapping("/batchUpdateSeqNo")
    @Operation(summary = "批量更新序号")
    public ActionResult<String> batchUpdateSeqNo(@RequestBody Map<String, Integer> seqNoMap) {
        try {
            projectSchemaPhaseService.batchUpdateSeqNo(seqNoMap);
            return ActionResult.success("批量更新序号成功");
        } catch (Exception e) {
            log.error("批量更新序号失败", e);
            return ActionResult.fail("批量更新序号失败：" + e.getMessage());
        }
    }

    /**
     * 调整序号（上移/下移）
     */
    @PostMapping("/adjustSeqNo/{id}")
    @Operation(summary = "调整序号")
    public ActionResult<String> adjustSeqNo(
            @Parameter(description = "阶段配置ID") @PathVariable String id,
            @Parameter(description = "方向（up/down）") @RequestParam String direction) {
        try {
            projectSchemaPhaseService.adjustSeqNo(id, direction);
            return ActionResult.success("调整序号成功");
        } catch (Exception e) {
            log.error("调整序号失败", e);
            return ActionResult.fail("调整序号失败：" + e.getMessage());
        }
    }

    /**
     * 获取下一个序号
     */
    @GetMapping("/getNextSeqNo/{projectTemplateId}")
    @Operation(summary = "获取下一个序号")
    public ActionResult<Integer> getNextSeqNo(
            @Parameter(description = "项目模板ID") @PathVariable String projectTemplateId) {
        try {
            Integer nextSeqNo = projectSchemaPhaseService.getNextSeqNo(projectTemplateId);
            return ActionResult.success(nextSeqNo);
        } catch (Exception e) {
            log.error("获取下一个序号失败", e);
            return ActionResult.fail("获取下一个序号失败：" + e.getMessage());
        }
    }

    /**
     * 获取阶段配置使用情况
     */
    @GetMapping("/getUsageInfo/{id}")
    @Operation(summary = "获取阶段配置使用情况")
    public ActionResult<Map<String, Object>> getUsageInfo(
            @Parameter(description = "阶段配置ID") @PathVariable String id) {
        try {
            Map<String, Object> usageInfo = projectSchemaPhaseService.getPhaseConfigUsageInfo(id);
            return ActionResult.success(usageInfo);
        } catch (Exception e) {
            log.error("获取阶段配置使用情况失败", e);
            return ActionResult.fail("获取阶段配置使用情况失败：" + e.getMessage());
        }
    }

    /**
     * 检查阶段库ID在指定模板中是否已存在
     */
    @GetMapping("/checkExist")
    @Operation(summary = "检查阶段库ID在指定模板中是否已存在")
    public ActionResult<Boolean> checkExist(
            @Parameter(description = "项目模板ID") @RequestParam String projectTemplateId,
            @Parameter(description = "阶段库ID") @RequestParam String phaseLibraryId,
            @Parameter(description = "排除的ID") @RequestParam(required = false) String excludeId) {
        try {
            boolean exists = projectSchemaPhaseService.isExistByPhaseLibraryId(projectTemplateId, phaseLibraryId, excludeId);
            return ActionResult.success(exists);
        } catch (Exception e) {
            log.error("检查阶段库ID是否存在失败", e);
            return ActionResult.fail("检查阶段库ID是否存在失败：" + e.getMessage());
        }
    }

    /**
     * 根据阶段库ID获取阶段配置
     */
    @GetMapping("/getByPhaseLibraryId")
    @Operation(summary = "根据阶段库ID获取阶段配置")
    public ActionResult<ProjectSchemaPhaseEntity> getByPhaseLibraryId(
            @Parameter(description = "项目模板ID") @RequestParam String projectTemplateId,
            @Parameter(description = "阶段库ID") @RequestParam String phaseLibraryId) {
        try {
            ProjectSchemaPhaseEntity entity = projectSchemaPhaseService.getByPhaseLibraryId(projectTemplateId, phaseLibraryId);
            return ActionResult.success(entity);
        } catch (Exception e) {
            log.error("根据阶段库ID获取阶段配置失败", e);
            return ActionResult.fail("根据阶段库ID获取阶段配置失败：" + e.getMessage());
        }
    }
}
