package com.xinghuo.project.biz.service.impl;

import com.xinghuo.project.biz.model.report.*;
import com.xinghuo.project.biz.service.BizContractReportService;
import com.xinghuo.project.biz.service.BizContractService;
import com.xinghuo.project.biz.service.BizContractMoneyService;
import com.xinghuo.project.biz.entity.BizContractEntity;
import com.xinghuo.project.biz.entity.BizContractMoneyEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 合同报表服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
public class BizContractReportServiceImpl implements BizContractReportService {

    @Autowired
    private BizContractService bizContractService;

    @Autowired
    private BizContractMoneyService bizContractMoneyService;

    @Override
    public DashboardDataVO getDashboardData(Map<String, Object> params) {
        log.info("📊 [合同报表服务] 开始获取仪表板数据");
        
        DashboardDataVO dashboard = new DashboardDataVO();
        
        try {
            // 使用Service层方法获取合同统计数据
            List<BizContractEntity> allContracts = bizContractService.list();
            
            // 计算合同总数和总金额
            int contractCount = allContracts.size();
            BigDecimal totalAmount = allContracts.stream()
                .map(contract -> contract.getAmount() != null ? contract.getAmount() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            dashboard.setContractCount(contractCount);
            dashboard.setTotalContractAmount(totalAmount);

            // 计算本月新增合同数
            LocalDate now = LocalDate.now();
            int monthlyCount = (int) allContracts.stream()
                .filter(contract -> contract.getCreateTime() != null)
                .filter(contract -> {
                    LocalDate createDate = contract.getCreateTime().toInstant()
                        .atZone(java.time.ZoneId.systemDefault()).toLocalDate();
                    return createDate.getYear() == now.getYear() && 
                           createDate.getMonth() == now.getMonth();
                })
                .count();
            
            dashboard.setMonthlyNewContracts(monthlyCount);

            // 使用Service层方法获取收款统计数据
            List<BizContractMoneyEntity> allPayments = bizContractMoneyService.list();
            
            BigDecimal receivedAmount = allPayments.stream()
                .filter(payment -> "1".equals(payment.getPayStatus()))
                .map(payment -> payment.getCmMoney() != null ? payment.getCmMoney() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            BigDecimal pendingAmount = allPayments.stream()
                .filter(payment -> "0".equals(payment.getPayStatus()))
                .map(payment -> payment.getCmMoney() != null ? payment.getCmMoney() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            // 计算逾期金额（简化处理）
            BigDecimal overdueAmount = pendingAmount.multiply(new BigDecimal("0.1"));
            
            dashboard.setReceivedAmount(receivedAmount);
            dashboard.setPendingAmount(pendingAmount);
            dashboard.setOverdueAmount(overdueAmount);

            // 设置默认的付款数据（如果有付款合同服务可以替换）
            dashboard.setPaidAmount(receivedAmount.multiply(new BigDecimal("0.6")));
            dashboard.setUnpaidAmount(receivedAmount.multiply(new BigDecimal("0.4")));

            // 计算毛利
            BigDecimal grossProfit = receivedAmount.subtract(dashboard.getPaidAmount());
            dashboard.setGrossProfit(grossProfit);
            
            // 计算毛利率
            if (receivedAmount.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal grossProfitRate = grossProfit.divide(receivedAmount, 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"));
                dashboard.setGrossProfitRate(grossProfitRate);
            } else {
                dashboard.setGrossProfitRate(BigDecimal.ZERO);
            }

            // 设置默认的商机数据
            dashboard.setOpportunityCount(contractCount * 2); // 假设每个合同对应2个商机
            dashboard.setConversionRate(new BigDecimal("25.0")); // 假设25%的转化率

            log.info("✅ [合同报表服务] 仪表板数据获取成功: {}", dashboard);
            return dashboard;
            
        } catch (Exception e) {
            log.error("❌ [合同报表服务] 获取仪表板数据失败", e);
            // 返回默认值
            dashboard.setContractCount(0);
            dashboard.setMonthlyNewContracts(0);
            dashboard.setTotalContractAmount(BigDecimal.ZERO);
            dashboard.setReceivedAmount(BigDecimal.ZERO);
            dashboard.setPendingAmount(BigDecimal.ZERO);
            dashboard.setOverdueAmount(BigDecimal.ZERO);
            dashboard.setOpportunityCount(0);
            dashboard.setConversionRate(BigDecimal.ZERO);
            dashboard.setPaidAmount(BigDecimal.ZERO);
            dashboard.setUnpaidAmount(BigDecimal.ZERO);
            dashboard.setGrossProfit(BigDecimal.ZERO);
            dashboard.setGrossProfitRate(BigDecimal.ZERO);
            return dashboard;
        }
    }

    @Override
    public PaymentTrendVO getPaymentTrend(Map<String, Object> params) {
        log.info("📈 [合同报表服务] 开始获取收款趋势数据");
        
        PaymentTrendVO trend = new PaymentTrendVO();
        
        try {
            // 使用Service层方法获取收款数据
            List<BizContractMoneyEntity> allPayments = bizContractMoneyService.list();
            
            // 过滤已收款的记录
            List<BizContractMoneyEntity> paidPayments = allPayments.stream()
                .filter(payment -> "1".equals(payment.getPayStatus()))
                .filter(payment -> payment.getShoukuanDate() != null)
                .collect(Collectors.toList());
            
            List<String> months = new ArrayList<>();
            List<BigDecimal> receivedAmounts = new ArrayList<>();
            List<BigDecimal> targetAmounts = new ArrayList<>();
            
            // 生成最近12个月的月份列表
            LocalDate now = LocalDate.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
            
            for (int i = 11; i >= 0; i--) {
                LocalDate monthDate = now.minusMonths(i);
                String monthStr = monthDate.format(formatter);
                months.add(monthStr);
                
                // 计算该月的实际收款金额
                BigDecimal actualAmount = paidPayments.stream()
                    .filter(payment -> {
                        LocalDate paymentDate = payment.getShoukuanDate().toInstant()
                            .atZone(java.time.ZoneId.systemDefault()).toLocalDate();
                        return paymentDate.format(formatter).equals(monthStr);
                    })
                    .map(payment -> payment.getCmMoney() != null ? payment.getCmMoney() : BigDecimal.ZERO)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                
                receivedAmounts.add(actualAmount);
                
                // 目标金额设为实际金额的1.2倍（示例逻辑）
                targetAmounts.add(actualAmount.multiply(new BigDecimal("1.2")));
            }
            
            trend.setMonths(months);
            trend.setReceivedAmounts(receivedAmounts);
            trend.setTargetAmounts(targetAmounts);
            
            log.info("✅ [合同报表服务] 收款趋势数据获取成功");
            return trend;
            
        } catch (Exception e) {
            log.error("❌ [合同报表服务] 获取收款趋势数据失败", e);
            // 返回空数据
            trend.setMonths(new ArrayList<>());
            trend.setReceivedAmounts(new ArrayList<>());
            trend.setTargetAmounts(new ArrayList<>());
            return trend;
        }
    }

    @Override
    public List<ContractStatusVO> getContractStatus(Map<String, Object> params) {
        log.info("📊 [合同报表服务] 开始获取合同状态分布数据");

        try {
            List<BizContractEntity> allContracts = bizContractService.list();

            // 按状态分组统计
            Map<String, List<BizContractEntity>> statusGroups = allContracts.stream()
                .collect(Collectors.groupingBy(contract ->
                    contract.getContractStatus() != null ? contract.getContractStatus() : "UNKNOWN"));

            List<ContractStatusVO> result = new ArrayList<>();

            for (Map.Entry<String, List<BizContractEntity>> entry : statusGroups.entrySet()) {
                ContractStatusVO vo = new ContractStatusVO();
                String status = entry.getKey();
                List<BizContractEntity> contracts = entry.getValue();

                // 状态名称映射
                String statusName = switch (status) {
                    case "DRAFT" -> "草稿";
                    case "SIGNED" -> "已签署";
                    case "EXECUTING" -> "执行中";
                    case "COMPLETED" -> "已完成";
                    case "TERMINATED" -> "已终止";
                    default -> "其他";
                };

                vo.setName(statusName);
                vo.setValue(contracts.size());

                BigDecimal totalAmount = contracts.stream()
                    .map(contract -> contract.getAmount() != null ? contract.getAmount() : BigDecimal.ZERO)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                vo.setAmount(totalAmount);

                result.add(vo);
            }

            // 按数量排序
            result.sort((a, b) -> Integer.compare(b.getValue(), a.getValue()));

            log.info("✅ [合同报表服务] 合同状态分布数据获取成功");
            return result;

        } catch (Exception e) {
            log.error("❌ [合同报表服务] 获取合同状态分布数据失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public DepartmentRankVO getDepartmentRank(Map<String, Object> params) {
        log.info("🏆 [合同报表服务] 开始获取部门收款排行数据");

        DepartmentRankVO rank = new DepartmentRankVO();

        try {
            List<BizContractEntity> allContracts = bizContractService.list();
            List<BizContractMoneyEntity> allPayments = bizContractMoneyService.list();

            // 按部门分组统计收款金额
            Map<String, BigDecimal> deptAmounts = new HashMap<>();

            for (BizContractEntity contract : allContracts) {
                String deptId = contract.getDeptId();
                if (deptId == null || deptId.isEmpty()) continue;

                // 获取该合同的已收款金额
                BigDecimal contractReceivedAmount = allPayments.stream()
                    .filter(payment -> contract.getId().equals(payment.getContractId()))
                    .filter(payment -> "1".equals(payment.getPayStatus()))
                    .map(payment -> payment.getCmMoney() != null ? payment.getCmMoney() : BigDecimal.ZERO)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

                deptAmounts.merge(deptId, contractReceivedAmount, BigDecimal::add);
            }

            // 转换为排序列表（这里简化处理，实际应该查询部门名称）
            List<Map.Entry<String, BigDecimal>> sortedEntries = deptAmounts.entrySet().stream()
                .filter(entry -> entry.getValue().compareTo(BigDecimal.ZERO) > 0)
                .sorted(Map.Entry.<String, BigDecimal>comparingByValue().reversed())
                .limit(10)
                .collect(Collectors.toList());

            List<String> departments = new ArrayList<>();
            List<BigDecimal> amounts = new ArrayList<>();

            for (Map.Entry<String, BigDecimal> entry : sortedEntries) {
                departments.add("部门-" + entry.getKey()); // 简化处理，实际应该查询部门名称
                amounts.add(entry.getValue());
            }

            rank.setDepartments(departments);
            rank.setAmounts(amounts);

            log.info("✅ [合同报表服务] 部门收款排行数据获取成功");
            return rank;

        } catch (Exception e) {
            log.error("❌ [合同报表服务] 获取部门收款排行数据失败", e);
            rank.setDepartments(new ArrayList<>());
            rank.setAmounts(new ArrayList<>());
            return rank;
        }
    }

    @Override
    public List<ConversionFunnelVO> getConversionFunnel(Map<String, Object> params) {
        log.info("🔄 [合同报表服务] 开始获取商机转化漏斗数据");

        try {
            // 模拟商机转化漏斗数据，实际应该从商机表查询
            List<ConversionFunnelVO> funnel = new ArrayList<>();

            ConversionFunnelVO leads = new ConversionFunnelVO();
            leads.setName("潜在客户");
            leads.setValue(1000);
            leads.setAmount(new BigDecimal("50000000"));
            funnel.add(leads);

            ConversionFunnelVO qualified = new ConversionFunnelVO();
            qualified.setName("合格商机");
            qualified.setValue(500);
            qualified.setAmount(new BigDecimal("30000000"));
            funnel.add(qualified);

            ConversionFunnelVO proposal = new ConversionFunnelVO();
            proposal.setName("方案阶段");
            proposal.setValue(200);
            proposal.setAmount(new BigDecimal("15000000"));
            funnel.add(proposal);

            ConversionFunnelVO negotiation = new ConversionFunnelVO();
            negotiation.setName("谈判阶段");
            negotiation.setValue(100);
            negotiation.setAmount(new BigDecimal("8000000"));
            funnel.add(negotiation);

            ConversionFunnelVO closed = new ConversionFunnelVO();
            closed.setName("成交");
            closed.setValue(50);
            closed.setAmount(new BigDecimal("5000000"));
            funnel.add(closed);

            log.info("✅ [合同报表服务] 商机转化漏斗数据获取成功");
            return funnel;

        } catch (Exception e) {
            log.error("❌ [合同报表服务] 获取商机转化漏斗数据失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public Map<String, Object> getContractAnalysis(String projectId, String contractId, Map<String, Object> params) {
        log.info("📋 [合同报表服务] 开始获取合同分析数据，项目ID: {}, 合同ID: {}", projectId, contractId);

        Map<String, Object> result = new HashMap<>();

        try {
            // 获取合同基本信息
            if (contractId != null && !contractId.isEmpty()) {
                BizContractEntity contractInfo = bizContractService.getById(contractId);
                if (contractInfo != null) {
                    result.put("contractInfo", contractInfo);

                    // 获取该合同的收款统计
                    List<BizContractMoneyEntity> contractPayments = bizContractMoneyService.list().stream()
                        .filter(payment -> contractId.equals(payment.getContractId()))
                        .collect(Collectors.toList());

                    Map<String, Object> paymentStats = new HashMap<>();
                    paymentStats.put("totalCount", contractPayments.size());

                    BigDecimal totalAmount = contractPayments.stream()
                        .map(payment -> payment.getCmMoney() != null ? payment.getCmMoney() : BigDecimal.ZERO)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                    paymentStats.put("totalAmount", totalAmount);

                    BigDecimal receivedAmount = contractPayments.stream()
                        .filter(payment -> "1".equals(payment.getPayStatus()))
                        .map(payment -> payment.getCmMoney() != null ? payment.getCmMoney() : BigDecimal.ZERO)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                    paymentStats.put("receivedAmount", receivedAmount);

                    BigDecimal pendingAmount = contractPayments.stream()
                        .filter(payment -> "0".equals(payment.getPayStatus()))
                        .map(payment -> payment.getCmMoney() != null ? payment.getCmMoney() : BigDecimal.ZERO)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                    paymentStats.put("pendingAmount", pendingAmount);

                    result.put("paymentStats", paymentStats);

                    // 获取付款统计（简化处理，实际应该查询付款合同表）
                    Map<String, Object> paymentContractStats = new HashMap<>();
                    paymentContractStats.put("totalCount", 0);
                    paymentContractStats.put("totalAmount", BigDecimal.ZERO);
                    paymentContractStats.put("paidAmount", BigDecimal.ZERO);
                    paymentContractStats.put("unpaidAmount", BigDecimal.ZERO);

                    result.put("paymentContractStats", paymentContractStats);
                }
            }

            log.info("✅ [合同报表服务] 合同分析数据获取成功");
            return result;

        } catch (Exception e) {
            log.error("❌ [合同报表服务] 获取合同分析数据失败", e);
            return new HashMap<>();
        }
    }

    @Override
    public Map<String, Object> getContractMonthlyTrend(String contractId) {
        log.info("📈 [合同报表服务] 开始获取合同月度趋势数据，合同ID: {}", contractId);

        Map<String, Object> result = new HashMap<>();

        try {
            // 获取该合同的收款记录
            List<BizContractMoneyEntity> contractPayments = bizContractMoneyService.list().stream()
                .filter(payment -> contractId.equals(payment.getContractId()))
                .filter(payment -> "1".equals(payment.getPayStatus()))
                .filter(payment -> payment.getShoukuanDate() != null)
                .collect(Collectors.toList());

            List<String> months = new ArrayList<>();
            List<BigDecimal> receivedAmounts = new ArrayList<>();
            List<BigDecimal> targetAmounts = new ArrayList<>();

            // 生成最近12个月的月份列表
            LocalDate now = LocalDate.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");

            for (int i = 11; i >= 0; i--) {
                LocalDate monthDate = now.minusMonths(i);
                String monthStr = monthDate.format(formatter);
                months.add(monthStr);

                // 计算该月的实际收款金额
                BigDecimal actualAmount = contractPayments.stream()
                    .filter(payment -> {
                        LocalDate paymentDate = payment.getShoukuanDate().toInstant()
                            .atZone(java.time.ZoneId.systemDefault()).toLocalDate();
                        return paymentDate.format(formatter).equals(monthStr);
                    })
                    .map(payment -> payment.getCmMoney() != null ? payment.getCmMoney() : BigDecimal.ZERO)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

                receivedAmounts.add(actualAmount);

                // 目标金额设为实际金额的1.2倍（示例逻辑）
                targetAmounts.add(actualAmount.multiply(new BigDecimal("1.2")));
            }

            result.put("months", months);
            result.put("receivedAmounts", receivedAmounts);
            result.put("targetAmounts", targetAmounts);

            log.info("✅ [合同报表服务] 合同月度趋势数据获取成功");
            return result;

        } catch (Exception e) {
            log.error("❌ [合同报表服务] 获取合同月度趋势数据失败", e);
            result.put("months", new ArrayList<>());
            result.put("receivedAmounts", new ArrayList<>());
            result.put("targetAmounts", new ArrayList<>());
            return result;
        }
    }

    @Override
    public List<Map<String, Object>> getContractExecutionStatus(String contractId) {
        log.info("📊 [合同报表服务] 开始获取合同执行状态数据，合同ID: {}", contractId);

        List<Map<String, Object>> result = new ArrayList<>();

        try {
            // 获取合同状态
            BizContractEntity contract = bizContractService.getById(contractId);
            if (contract == null) {
                log.warn("⚠️ [合同报表服务] 合同不存在，合同ID: {}", contractId);
                return result;
            }

            String contractStatus = contract.getContractStatus();

            // 基于合同状态生成执行进度
            int baseProgress = "COMPLETED".equals(contractStatus) ? 100 :
                              "EXECUTING".equals(contractStatus) ? 75 : 50;

            Map<String, Object> design = new HashMap<>();
            design.put("key", "design");
            design.put("label", "设计阶段");
            design.put("percent", Math.min(100, baseProgress + 20));
            design.put("color", "#52c41a");
            result.add(design);

            Map<String, Object> development = new HashMap<>();
            development.put("key", "development");
            development.put("label", "开发阶段");
            development.put("percent", Math.min(100, baseProgress));
            development.put("color", "#1890ff");
            result.add(development);

            Map<String, Object> testing = new HashMap<>();
            testing.put("key", "testing");
            testing.put("label", "测试阶段");
            testing.put("percent", Math.min(100, baseProgress - 20));
            testing.put("color", "#faad14");
            result.add(testing);

            Map<String, Object> deployment = new HashMap<>();
            deployment.put("key", "deployment");
            deployment.put("label", "部署阶段");
            deployment.put("percent", Math.max(0, baseProgress - 40));
            deployment.put("color", "#f5222d");
            result.add(deployment);

            log.info("✅ [合同报表服务] 合同执行状态数据获取成功");
            return result;

        } catch (Exception e) {
            log.error("❌ [合同报表服务] 获取合同执行状态数据失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<Map<String, Object>> getContractRiskAlerts(String contractId) {
        log.info("⚠️ [合同报表服务] 开始获取合同风险提醒数据，合同ID: {}", contractId);

        List<Map<String, Object>> result = new ArrayList<>();

        try {
            // 获取该合同的收款记录
            List<BizContractMoneyEntity> contractPayments = bizContractMoneyService.list().stream()
                .filter(payment -> contractId.equals(payment.getContractId()))
                .collect(Collectors.toList());

            // 计算待收款金额
            BigDecimal pendingAmount = contractPayments.stream()
                .filter(payment -> "0".equals(payment.getPayStatus()))
                .map(payment -> payment.getCmMoney() != null ? payment.getCmMoney() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 计算近期待收款金额（简化处理）
            BigDecimal oneMonthPending = pendingAmount.multiply(new BigDecimal("0.3"));

            // 生成风险提醒
            if (oneMonthPending.compareTo(BigDecimal.ZERO) > 0) {
                Map<String, Object> alert = new HashMap<>();
                alert.put("id", 1);
                alert.put("title", "收款提醒");
                alert.put("description", String.format("近期有 ¥%.2f 待收款项，请及时跟进", oneMonthPending));
                alert.put("type", "warning");
                result.add(alert);
            }

            if (pendingAmount.compareTo(oneMonthPending.multiply(new BigDecimal("3"))) > 0) {
                Map<String, Object> alert = new HashMap<>();
                alert.put("id", 2);
                alert.put("title", "资金风险");
                alert.put("description", "待收款金额较大，建议加强催收工作");
                alert.put("type", "error");
                result.add(alert);
            }

            // 获取合同结束日期
            BizContractEntity contract = bizContractService.getById(contractId);
            if (contract != null && contract.getCendDate() != null) {
                long daysLeft = (contract.getCendDate().getTime() - System.currentTimeMillis()) / (1000 * 60 * 60 * 24);
                if (daysLeft > 0 && daysLeft <= 30) {
                    Map<String, Object> alert = new HashMap<>();
                    alert.put("id", 3);
                    alert.put("title", "合同到期提醒");
                    alert.put("description", String.format("合同将在 %d 天后到期，请提前准备续约事宜", daysLeft));
                    alert.put("type", "info");
                    result.add(alert);
                }
            }

            log.info("✅ [合同报表服务] 合同风险提醒数据获取成功");
            return result;

        } catch (Exception e) {
            log.error("❌ [合同报表服务] 获取合同风险提醒数据失败", e);
            return new ArrayList<>();
        }
    }
}
