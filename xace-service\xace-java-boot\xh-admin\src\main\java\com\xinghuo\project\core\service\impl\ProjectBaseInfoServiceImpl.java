package com.xinghuo.project.core.service.impl;

import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.exception.DataException;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.permission.entity.OrganizeEntity;
import com.xinghuo.permission.entity.UserEntity;
import com.xinghuo.permission.service.OrganizeService;
import com.xinghuo.permission.service.UserService;
import com.xinghuo.project.core.entity.ProjectBaseEntity;
import com.xinghuo.project.core.dao.ProjectBaseMapper;
import com.xinghuo.project.core.model.projectBase.ProjectBaseInfoForm;
import com.xinghuo.project.core.model.projectBase.ProjectBaseInfoVO;
import com.xinghuo.project.core.service.ProjectBaseInfoService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 项目基础信息服务实现类
 *
 * <AUTHOR>
 * @date 2024-08-05
 */
@Slf4j
@Service
public class ProjectBaseInfoServiceImpl extends BaseServiceImpl<ProjectBaseMapper, ProjectBaseEntity>
        implements ProjectBaseInfoService {

    @Resource
    private UserService userService;

    @Resource
    private OrganizeService organizeService;

    @Override
    public ProjectBaseInfoVO getInfo(String id) {
        if (StrXhUtil.isEmpty(id)) {
            throw new DataException("项目ID不能为空");
        }

        // 查询项目基本信息
        ProjectBaseEntity entity = this.getById(id);
        if (entity == null) {
            return null;
        }

        // 转换为VO对象
        ProjectBaseInfoVO vo = BeanCopierUtils.copy(entity, ProjectBaseInfoVO.class);

        // 补充关联字段的查询
        enrichProjectInfo(vo, entity);

        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(String id, ProjectBaseInfoForm form) {
        if (StrXhUtil.isEmpty(id)) {
            throw new DataException("项目ID不能为空");
        }

        if (form == null) {
            throw new DataException("项目信息表单不能为空");
        }

        // 查询现有项目信息
        ProjectBaseEntity existingEntity = this.getById(id);
        if (existingEntity == null) {
            throw new DataException("项目不存在");
        }

        // 将表单数据复制到现有实体对象（保留数据库管理的字段）
        existingEntity =  BeanCopierUtils.copy(form, ProjectBaseEntity.class);

        // 确保ID保持不变
        existingEntity.setId(id);

        // 更新项目信息
        boolean result = this.updateById(existingEntity);
        if (!result) {
            throw new DataException("更新项目信息失败");
        }

        log.info("项目基础信息更新成功: projectId={}", id);
        return true;
    }

    /**
     * 补充项目关联信息
     * 
     * @param vo 项目信息VO
     * @param entity 项目实体
     */
    private void enrichProjectInfo(ProjectBaseInfoVO vo, ProjectBaseEntity entity) {
        try {
            // 项目经理名称
            if (StrXhUtil.isNotEmpty(entity.getManagerId())) {
                UserEntity manager = userService.getInfo(entity.getManagerId());
                if (manager != null) {
                    vo.setManagerName(manager.getRealName());
                }
            }

            // 项目发起人名称
            if (StrXhUtil.isNotEmpty(entity.getSponsorId())) {
                UserEntity sponsor = userService.getInfo(entity.getSponsorId());
                if (sponsor != null) {
                    vo.setSponsorName(sponsor.getRealName());
                }
            }

            // 部门名称
            if (StrXhUtil.isNotEmpty(entity.getDepartmentId())) {
                OrganizeEntity department = organizeService.getInfo(entity.getDepartmentId());
                if (department != null) {
                    vo.setDepartmentName(department.getFullName());
                }
            }

            // 创建人名称
            if (StrXhUtil.isNotEmpty(entity.getCreatedBy())) {
                UserEntity creator = userService.getInfo(entity.getCreatedBy());
                if (creator != null) {
                    vo.setCreatedByName(creator.getRealName());
                }
            }

            // 最后修改人名称
            if (StrXhUtil.isNotEmpty(entity.getLastUpdatedBy())) {
                UserEntity updater = userService.getInfo(entity.getLastUpdatedBy());
                if (updater != null) {
                    vo.setLastUpdatedByName(updater.getRealName());
                }
            }

            // TODO: 其他关联字段的查询可以在后续需要时补充：
            // - 项目类型名称 (需要项目类型字典服务)
            // - 项目群名称 (需要项目群服务)
            // - 客户名称 (需要客户服务)
            // - 合同名称 (需要合同服务)
            // - 战略目标名称 (需要字典服务)
            // - 优先级名称 (需要字典服务)
            // - 风险等级名称 (需要字典服务)

        } catch (Exception e) {
            log.warn("查询项目关联信息失败: projectId={}", entity.getId(), e);
            // 不影响主要业务流程，仅记录警告日志
        }
    }
}
