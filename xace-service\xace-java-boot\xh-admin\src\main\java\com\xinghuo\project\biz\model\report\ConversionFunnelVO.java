package com.xinghuo.project.biz.model.report;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 商机转化漏斗数据VO
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Schema(description = "商机转化漏斗数据VO")
public class ConversionFunnelVO {

    /**
     * 阶段名称
     */
    @Schema(description = "阶段名称")
    private String name;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer value;

    /**
     * 金额
     */
    @Schema(description = "金额")
    private BigDecimal amount;
}
