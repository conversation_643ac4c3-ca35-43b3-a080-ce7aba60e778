package com.xinghuo.project.template.model.dto;

import lombok.Data;

/**
 * 阶段模板选择器VO
 * 用于下拉选择、API选择等场景
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
@Data
public class PhaseTemplateSelectVO {

    /**
     * 阶段模板ID
     */
    private String id;

    /**
     * 阶段编码
     */
    private String code;

    /**
     * 阶段名称
     */
    private String name;

    private String fullName;

    /**
     * 阶段描述
     */
    private String description;

    /**
     * 标准工期(天)
     */
    private Integer stdDuration;

    /**
     * 状态 (1:启用 0:禁用)
     */
    private Integer status;

    /**
     * 状态名称（冗余字段，便于显示）
     */
    private String statusName;
}
