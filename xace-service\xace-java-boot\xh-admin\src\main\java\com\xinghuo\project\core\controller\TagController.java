package com.xinghuo.project.core.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.constant.MsgCode;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.common.util.json.JsonXhUtil;
import com.xinghuo.project.core.entity.TagEntity;
import com.xinghuo.project.core.model.tag.*;
import com.xinghuo.project.core.service.TagService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 标签管理控制器
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Tag(name = "标签管理", description = "标签管理相关接口")
@RestController
@RequestMapping("/api/project/core/tag")
public class TagController {

    @Autowired
    private TagService tagService;

    /**
     * 获取标签列表
     *
     * @param pagination 分页查询参数
     * @return 标签列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取标签列表")
    public ActionResult<PageListVO<TagListVO>> list(@RequestBody TagPagination pagination){
        List<TagEntity> list = tagService.getList(pagination);
        List<TagListVO> listVOs = JsonXhUtil.jsonToList(list, TagListVO.class);
        PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
        return ActionResult.page(listVOs,page);
    }

    /**
     * 获取标签详情
     *
     * @param id 标签ID
     * @return 标签详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取标签详情")
    @Parameters({
            @Parameter(name = "id", description = "标签ID", required = true),
    })
    public ActionResult<TagInfoVO> info(@PathVariable("id") String id) {
        TagEntity entity = tagService.getInfo(id);
        TagInfoVO vo = JsonXhUtil.jsonDeepCopy(entity, TagInfoVO.class);
        return ActionResult.success(vo);
    }

    /**
     * 创建标签
     *
     * @param tagCrForm 标签创建表单
     * @return 操作结果
     */
    @PostMapping
    @Operation(summary = "创建标签")
    @Parameters({
            @Parameter(name = "tagCrForm", description = "标签创建表单", required = true),
    })
    public ActionResult create(@RequestBody @Valid TagCrForm tagCrForm) {
        // 检查标签名称是否已存在
        if (tagService.isExistByTagName(tagCrForm.getTagName(), null)) {
            return ActionResult.fail("标签名称已存在");
        }

        TagEntity entity = JsonXhUtil.jsonDeepCopy(tagCrForm, TagEntity.class);
        tagService.create(entity);
        return ActionResult.success(MsgCode.SU001.get());
    }

    /**
     * 更新标签
     *
     * @param id        标签ID
     * @param tagUpForm 标签更新表单
     * @return 操作结果
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新标签")
    @Parameters({
            @Parameter(name = "id", description = "标签ID", required = true),
            @Parameter(name = "tagUpForm", description = "标签更新表单", required = true),
    })
    public ActionResult update(@PathVariable("id") String id, @RequestBody @Valid TagUpForm tagUpForm) {
        // 检查标签名称是否已存在
        if (tagService.isExistByTagName(tagUpForm.getTagName(), id)) {
            return ActionResult.fail("标签名称已存在");
        }

        TagEntity entity = JsonXhUtil.jsonDeepCopy(tagUpForm, TagEntity.class);
        tagService.update(id, entity);
        return ActionResult.success(MsgCode.SU002.get());
    }

    /**
     * 删除标签
     *
     * @param id 标签ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除标签")
    @Parameters({
            @Parameter(name = "id", description = "标签ID", required = true),
    })
    public ActionResult delete(@PathVariable("id") String id) {
        tagService.delete(id);
        return ActionResult.success(MsgCode.SU003.get());
    }
}
