package com.xinghuo.project.template.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 标准项目活动库实体类
 * 对应数据库表：zz_proj_activity_library
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("zz_proj_activity_library")
public class ActivityLibraryEntity extends BaseEntityV2.CUBaseEntityV2<String> {

    /**
     * 标准活动名称 (如: 项目章程编写)
     */
    @TableField("name")
    private String name;

    /**
     * 标准活动编码 (可用于WBS编码)
     */
    @TableField("code")
    private String code;

    /**
     * 活动描述与指南
     */
    @TableField("description")
    private String description;

    /**
     * 状态 (0:启用, 1:禁用)
     */
    @TableField("status")
    private Integer status;

    /**
     * 是否是里程碑 (1:是, 0:否)
     */
    @TableField("is_milestone")
    private Integer isMilestone;

    /**
     * 标准工期 (天)
     */
    @TableField("duration")
    private BigDecimal duration;

    /**
     * 标准工时 (小时)
     */
    @TableField("standard_hour")
    private BigDecimal standardHour;

    /**
     * 默认责任角色ID (关联角色字典表)
     */
    @TableField("response_role_id")
    private String responseRoleId;

    /**
     * 默认确认角色ID (当完成方式为人工确认时)
     */
    @TableField("confirm_role_id")
    private String confirmRoleId;

    /**
     * 默认完成方式ID (关联字典表, 如: 0:无需确认, 2:审批确认, 3:人工确认)
     */
    @TableField("complete_type_id")
    private String completeTypeId;

    /**
     * 关联的审批流程模板ID (当完成方式为审批确认时)
     */
    @TableField("approve_schema_id")
    private String approveSchemaId;

    /**
     * 活动大类ID (关联字典表, 如: 项目管理, 质量管理)
     */
    @TableField("activity_type_id")
    private String activityTypeId;

    /**
     * 活动子类ID (关联字典表)
     */
    @TableField("activity_sub_type_id")
    private String activitySubTypeId;
}
