import { defHttp } from '/@/utils/http/axios';

enum Api {
  GetList = '/api/project/template/phasePlanTemplate/getList',
  GetListByKnStatus = '/api/project/template/phasePlanTemplate/getListByKnStatus',
  GetDetailInfo = '/api/project/template/phasePlanTemplate/getDetailInfo',
  GetInfo = '/api/project/template/phasePlanTemplate/getInfo',
  Create = '/api/project/template/phasePlanTemplate/create',
  Update = '/api/project/template/phasePlanTemplate/update',
  Delete = '/api/project/template/phasePlanTemplate/delete',
  BatchDelete = '/api/project/template/phasePlanTemplate/batchDelete',
  UpdateKnStatus = '/api/project/template/phasePlanTemplate/updateKnStatus',
  BatchUpdateKnStatus = '/api/project/template/phasePlanTemplate/batchUpdateKnStatus',
  Publish = '/api/project/template/phasePlanTemplate/publish',
  Archive = '/api/project/template/phasePlanTemplate/archive',
  Copy = '/api/project/template/phasePlanTemplate/copy',
  CheckNameExists = '/api/project/template/phasePlanTemplate/checkNameExists',
  GetSelectList = '/api/project/template/phasePlanTemplate/getSelectList',
  AddPhasesFromStandardLibrary = '/api/project/template/phasePlanTemplate/addPhasesFromStandardLibrary',
  UpdatePhaseOrder = '/api/project/template/phasePlanTemplate/updatePhaseOrder',
  GetTemplateStatistics = '/api/project/template/phasePlanTemplate/getTemplateStatistics',
  GetByProjectTemplateId = '/api/project/template/phasePlanTemplate/getByProjectTemplateId',
  UpdateProjectTemplateRelations = '/api/project/template/phasePlanTemplate/updateProjectTemplateRelations',
  CreateFromProject = '/api/project/template/phasePlanTemplate/createFromProject',
  ApplyToProject = '/api/project/template/phasePlanTemplate/applyToProject',
  GetTemplateUsageInfo = '/api/project/template/phasePlanTemplate/getTemplateUsageInfo',
}

/**
 * 阶段计划模板接口
 */

// 获取阶段计划模板列表
export function getPhasePlanTemplateList(params: any) {
  return defHttp.post({
    url: Api.GetList,
    data: params,
  });
}

// 根据知识状态获取模板列表
export function getPhasePlanTemplateListByKnStatus(knStatusId: string) {
  return defHttp.get({
    url: `${Api.GetListByKnStatus}/${knStatusId}`,
  });
}

// 获取模板详情（包含阶段明细）
export function getPhasePlanTemplateDetailInfo(id: string) {
  return defHttp.get({
    url: `${Api.GetDetailInfo}/${id}`,
  });
}

// 获取模板基本信息
export function getPhasePlanTemplateInfo(id: string) {
  return defHttp.get({
    url: `${Api.GetInfo}/${id}`,
  });
}

// 创建阶段计划模板
export function createPhasePlanTemplate(params: any) {
  return defHttp.post({
    url: Api.Create,
    data: params,
  });
}

// 更新阶段计划模板
export function updatePhasePlanTemplate(id: string, params: any) {
  return defHttp.put({
    url: `${Api.Update}/${id}`,
    data: params,
  });
}

// 删除阶段计划模板
export function deletePhasePlanTemplate(id: string) {
  return defHttp.delete({
    url: `${Api.Delete}/${id}`,
  });
}

// 批量删除阶段计划模板
export function batchDeletePhasePlanTemplate(ids: string[]) {
  return defHttp.delete({
    url: Api.BatchDelete,
    data: ids,
  });
}

// 更新模板知识状态
export function updatePhasePlanTemplateKnStatus(id: string, knStatusId: string) {
  return defHttp.put({
    url: `${Api.UpdateKnStatus}/${id}?knStatusId=${encodeURIComponent(knStatusId)}`,
  });
}

// 批量更新知识状态
export function batchUpdatePhasePlanTemplateKnStatus(ids: string[], knStatusId: string) {
  return defHttp.put({
    url: `${Api.BatchUpdateKnStatus}?knStatusId=${encodeURIComponent(knStatusId)}`,
    data: ids,
  });
}

// 发布模板
export function publishPhasePlanTemplate(id: string) {
  return defHttp.put({
    url: `${Api.Publish}/${id}`,
  });
}

// 归档模板
export function archivePhasePlanTemplate(id: string) {
  return defHttp.put({
    url: `${Api.Archive}/${id}`,
  });
}

// 复制模板
export function copyPhasePlanTemplate(id: string, newName: string) {
  return defHttp.post({
    url: `${Api.Copy}/${id}?newName=${encodeURIComponent(newName)}`,
  });
}

// 检查模板名称是否存在
export function checkPhasePlanTemplateNameExists(name: string, excludeId?: string) {
  return defHttp.get({
    url: Api.CheckNameExists,
    params: { name, excludeId },
  });
}

// 获取模板选择列表
export function getPhasePlanTemplateSelectList(keyword?: string, knStatusId?: string) {
  return defHttp.get({
    url: Api.GetSelectList,
    params: { keyword, knStatusId },
  });
}

// 从标准阶段库添加阶段
export function addPhasesFromStandardLibrary(templateId: string, phaseCodes: string[]) {
  return defHttp.post({
    url: `${Api.AddPhasesFromStandardLibrary}/${templateId}`,
    data: phaseCodes,
  });
}

// 更新阶段顺序
export function updatePhaseOrder(templateId: string, phaseOrders: any[]) {
  return defHttp.put({
    url: `${Api.UpdatePhaseOrder}/${templateId}`,
    data: phaseOrders,
  });
}

// 获取模板统计信息
export function getPhasePlanTemplateStatistics(params: any) {
  return defHttp.post({
    url: Api.GetTemplateStatistics,
    data: params,
  });
}

// 根据项目模板ID查询关联的阶段计划模板
export function getPhasePlanTemplateByProjectTemplateId(projectTplId: string) {
  return defHttp.get({
    url: `${Api.GetByProjectTemplateId}/${projectTplId}`,
  });
}

// 更新模板与项目模板的关联关系
export function updateProjectTemplateRelations(templateId: string, projectTemplateIds: string[]) {
  return defHttp.put({
    url: `${Api.UpdateProjectTemplateRelations}/${templateId}`,
    data: projectTemplateIds,
  });
}

// 从项目阶段计划创建模板
export function createPhasePlanTemplateFromProject(projectId: string, templateName: string, description?: string) {
  return defHttp.post({
    url: `${Api.CreateFromProject}/${projectId}`,
    params: { templateName, description },
  });
}

// 应用模板到项目
export function applyPhasePlanTemplateToProject(templateId: string, projectId: string) {
  return defHttp.post({
    url: `${Api.ApplyToProject}/${templateId}`,
    params: { projectId },
  });
}

// 获取模板使用情况
export function getPhasePlanTemplateUsageInfo(id: string) {
  return defHttp.get({
    url: `${Api.GetTemplateUsageInfo}/${id}`,
  });
}
