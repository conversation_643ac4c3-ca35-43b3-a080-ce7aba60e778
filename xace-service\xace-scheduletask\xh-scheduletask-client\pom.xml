<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>xh-scheduletask-starter</artifactId>
        <groupId>com.xinghuo.xace</groupId>
        <version>1.2-RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>xh-scheduletask-client</artifactId>
    <description>xxl-job客户端</description>

    <properties>
        <spring-boot.version>2.7.0</spring-boot.version>
    </properties>

    <dependencies>
        <!--xxl-job任务调度核心-->
        <dependency>
            <groupId>com.xinghuo.xace</groupId>
            <artifactId>xxl-job-core</artifactId>
            <version>1.2-RELEASE</version>
        </dependency>

        <!--自动装配-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
            <version>${spring-boot.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <version>${spring-boot.version}</version>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.32</version>
        </dependency>
    </dependencies>


</project>
