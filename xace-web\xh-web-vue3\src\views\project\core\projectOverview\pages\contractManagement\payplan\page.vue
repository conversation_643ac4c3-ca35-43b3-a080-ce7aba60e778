<template>
  <div class="contract-payplan-management">
    <div class="page-header">
      <div class="flex items-center">
        <i class="icon-ym icon-ym-plan mr-2 text-lg"></i>
        <span class="text-base font-medium">付款计划</span>
      </div>
      <div class="flex items-center space-x-2">
        <!-- 合同选择器 -->
        <a-select
          v-model:value="selectedPaycontractId"
          placeholder="选择付款合同"
          style="width: 240px"
          show-search
          :options="paycontractOptions"
          option-label-prop="fullName"
          option-filter-prop="fullName"
          @change="handlePaycontractChange"
          allow-clear>
          <a-select-option v-for="option in paycontractOptions" :key="option.id" :value="option.id" :label="option.fullName">
            {{ option.fullName }}
          </a-select-option>
        </a-select>
        <a-button type="primary" @click="handleAdd" :disabled="!selectedPaycontractId">
          <template #icon><PlusOutlined /></template>
          新增计划
        </a-button>
        <a-button @click="handleRefresh">
          <template #icon><ReloadOutlined /></template>
          刷新
        </a-button>
      </div>
    </div>

    <div class="page-content">
      <a-spin :spinning="loading">
        <!-- 付款计划统计 -->
        <div class="payplan-summary mb-4">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-card>
                <a-statistic title="计划总金额" :value="stats.totalAmount || 0" :precision="2" suffix="元" :value-style="{ color: '#1890ff' }" />
              </a-card>
            </a-col>
            <a-col :span="6">
              <a-card>
                <a-statistic title="已付金额" :value="stats.paidAmount || 0" :precision="2" suffix="元" :value-style="{ color: '#52c41a' }" />
              </a-card>
            </a-col>
            <a-col :span="6">
              <a-card>
                <a-statistic title="待付金额" :value="stats.unpaidAmount || 0" :precision="2" suffix="元" :value-style="{ color: '#faad14' }" />
              </a-card>
            </a-col>
            <a-col :span="6">
              <a-card>
                <a-statistic title="执行进度" :value="stats.progressRate || 0" suffix="%" :precision="1" :value-style="{ color: '#faad14' }" />
              </a-card>
            </a-col>
          </a-row>
        </div>

        <!-- 付款计划时间线 -->
        <div class="payplan-timeline mb-4" v-if="payplanList.length > 0">
          <a-card title="付款计划时间线">
            <a-timeline>
              <a-timeline-item v-for="(plan, index) in payplanList" :key="plan.id" :color="getTimelineColor(plan.payStatus)">
                <template #dot>
                  <CheckCircleOutlined v-if="plan.payStatus === '1'" style="color: #52c41a" />
                  <ClockCircleOutlined v-else-if="plan.payStatus === '0'" style="color: #faad14" />
                  <ExclamationCircleOutlined v-else style="color: #f5222d" />
                </template>
                <div class="timeline-content">
                  <div class="flex justify-between items-start">
                    <div>
                      <h4 class="text-base font-medium mb-1">{{ plan.fktj }}</h4>
                      <p class="text-gray-600 mb-2">{{ plan.note }}</p>
                      <div class="flex items-center space-x-4 text-sm text-gray-500">
                        <span>预付日期: {{ formatDate(plan.yufuDate) }}</span>
                        <span>金额: ¥{{ formatMoney(plan.cmMoney) }}</span>
                        <a-tag :color="getStatusColor(plan.payStatus)">
                          {{ getStatusText(plan.payStatus) }}
                        </a-tag>
                      </div>
                    </div>
                    <div class="flex space-x-2">
                      <a-button size="small" @click="handleEdit(plan)">编辑</a-button>
                      <a-popconfirm title="确定要删除这个付款计划吗？" @confirm="handleDelete(plan)">
                        <a-button size="small" danger>删除</a-button>
                      </a-popconfirm>
                    </div>
                  </div>
                </div>
              </a-timeline-item>
            </a-timeline>
          </a-card>
        </div>

        <!-- 付款计划表格 -->
        <div class="payplan-table">
          <a-card title="付款计划详情">
            <a-table :columns="columns" :data-source="payplanList" :pagination="false" :loading="loading" row-key="id" :scroll="{ x: 1200 }">
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'cmMoney'">
                  <span class="text-blue-600 font-medium"> ¥{{ formatMoney(record.cmMoney) }} </span>
                </template>
                <template v-else-if="column.key === 'payStatus'">
                  <a-tag :color="getStatusColor(record.payStatus)">
                    {{ getStatusText(record.payStatus) }}
                  </a-tag>
                </template>
                <template v-else-if="column.key === 'yufuDate'">
                  {{ formatDate(record.yufuDate) }}
                </template>
                <template v-else-if="column.key === 'fukuanDate'">
                  {{ formatDate(record.fukuanDate) }}
                </template>
                <template v-else-if="column.key === 'ratio'">
                  {{ record.ratio ? `${record.ratio}%` : '-' }}
                </template>
                <template v-else-if="column.key === 'action'">
                  <a-space>
                    <a-button type="link" size="small" @click="handleView(record)"> 查看 </a-button>
                    <a-button type="link" size="small" @click="handleEdit(record)"> 编辑 </a-button>
                    <a-button type="link" size="small" @click="handleMarkPaid(record)" v-if="record.payStatus === '0'"> 登记付款 </a-button>
                    <a-popconfirm title="确定要删除这个付款计划吗？" @confirm="handleDelete(record)">
                      <a-button type="link" size="small" danger> 删除 </a-button>
                    </a-popconfirm>
                  </a-space>
                </template>
              </template>
            </a-table>
          </a-card>
        </div>
      </a-spin>
    </div>

    <!-- 新增/编辑付款计划抽屉 -->
    <a-drawer v-model:open="drawerVisible" :title="drawerTitle" :width="600" :destroy-on-close="true" @close="handleDrawerClose">
      <a-form ref="formRef" :model="formData" :rules="formRules" layout="vertical">
        <a-form-item label="付款条件" name="fktj">
          <a-input v-model:value="formData.fktj" placeholder="请输入付款条件" />
        </a-form-item>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="付款比例(%)" name="ratio">
              <a-input v-model:value="formData.ratio" style="width: 100%" placeholder="请输入付款比例" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="付款金额(元)" name="cmMoney">
              <a-input-number v-model:value="formData.cmMoney" :min="0" :precision="2" style="width: 100%" placeholder="请输入付款金额" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="预付日期" name="yufuDate">
              <a-date-picker v-model:value="formData.yufuDate" style="width: 100%" placeholder="请选择预付日期" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="付款状态" name="payStatus">
              <a-select v-model:value="formData.payStatus" placeholder="请选择付款状态">
                <a-select-option value="0">待付款</a-select-option>
                <a-select-option value="1">已付款</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="付款日期" name="fukuanDate" v-if="formData.payStatus === '1'">
          <a-date-picker v-model:value="formData.fukuanDate" style="width: 100%" placeholder="请选择付款日期" />
        </a-form-item>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="负责人" name="ownId">
              <a-select
                v-model:value="formData.ownId"
                placeholder="请选择负责人"
                show-search
                :options="userOptions"
                :field-names="{ label: 'fullName', value: 'id' }"
                allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="所属部门" name="deptId">
              <XhDepSelect v-model:value="formData.deptId" modalTitle="选择部门" :multiple="false" placeholder="请选择部门" allowClear />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="部门金额分配">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item label="一部金额(元)" name="ybAmount">
                <a-input-number v-model:value="formData.ybAmount" :min="0" :precision="2" style="width: 100%" placeholder="一部金额" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="二部金额(元)" name="ebAmount">
                <a-input-number v-model:value="formData.ebAmount" :min="0" :precision="2" style="width: 100%" placeholder="二部金额" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="其他金额(元)" name="otherAmount">
                <a-input-number v-model:value="formData.otherAmount" :min="0" :precision="2" style="width: 100%" placeholder="其他金额" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form-item>

        <a-form-item label="备注" name="note">
          <a-textarea v-model:value="formData.note" :rows="3" placeholder="请输入备注信息" />
        </a-form-item>
      </a-form>

      <template #footer>
        <a-space>
          <a-button @click="handleDrawerClose">取消</a-button>
          <a-button type="primary" @click="handleSubmit" :loading="submitLoading">
            {{ isEdit ? '更新' : '创建' }}
          </a-button>
        </a-space>
      </template>
    </a-drawer>

    <!-- 登记付款对话框 -->
    <a-modal v-model:open="paymentModalVisible" title="登记付款" :width="400" @ok="handleConfirmPayment" :confirm-loading="submitLoading">
      <a-form layout="vertical">
        <a-form-item label="付款日期">
          <a-date-picker v-model:value="paymentData.fukuanDate" style="width: 100%" placeholder="请选择付款日期" />
        </a-form-item>
        <a-form-item label="备注">
          <a-textarea v-model:value="paymentData.lastNote" :rows="3" placeholder="请输入付款备注" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, inject, computed, watch } from 'vue';
  import { PlusOutlined, ReloadOutlined, CheckCircleOutlined, ClockCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { formatToDate } from '/@/utils/dateUtil';
  import { FormInstance } from 'ant-design-vue';
  import dayjs, { Dayjs } from 'dayjs';
  import {
    paymentContractMoneyApi,
    type PaymentContractMoneyVO,
    type PaymentContractMoneyForm,
    type PaymentContractMoneyStats,
  } from '/@/api/project/paymentContractMoney';
  import { paymentContractApi } from '/@/api/project/paymentContract';
  import { XhDepSelect } from '/@/components/Xh/index';

  // 从父组件注入项目ID和合同信息
  const projectId = inject('projectId', ref(''));
  const contractId = inject('contractId', ref(''));
  const contractInfo = inject('contractInfo', ref(null));
  const hasContract = inject('hasContract', ref(false));

  const { createMessage } = useMessage();

  const loading = ref(false);
  const payplanList = ref<PaymentContractMoneyVO[]>([]);
  const stats = ref<PaymentContractMoneyStats>({
    totalAmount: 0,
    paidAmount: 0,
    unpaidAmount: 0,
    completedCount: 0,
    pendingCount: 0,
    progressRate: 0,
  });

  // 付款合同选择
  const selectedPaycontractId = ref<string>('');
  const paycontractOptions = ref<Array<{ id: string; fullName: string }>>([]);

  // 抽屉相关
  const drawerVisible = ref(false);
  const drawerTitle = ref('');
  const isEdit = ref(false);
  const currentRecord = ref<PaymentContractMoneyVO | null>(null);
  const submitLoading = ref(false);
  const formRef = ref<FormInstance>();

  // 登记付款相关
  const paymentModalVisible = ref(false);
  const currentPaymentRecord = ref<PaymentContractMoneyVO | null>(null);
  const paymentData = ref({
    fukuanDate: undefined as Dayjs | undefined,
    lastNote: '',
  });

  // 用户选项
  const userOptions = ref<Array<{ id: string; fullName: string }>>([]);

  // 表单数据
  const formData = ref<
    PaymentContractMoneyForm & {
      yufuDate?: Dayjs;
      fukuanDate?: Dayjs;
    }
  >({
    pcId: '',
    fktj: '',
    ratio: undefined,
    cmMoney: 0,
    yufuDate: undefined,
    fukuanDate: undefined,
    payStatus: '0',
    note: '',
    ownId: '',
    deptId: '',
    ybAmount: undefined,
    ebAmount: undefined,
    otherAmount: undefined,
  });

  // 表单验证规则
  const formRules = {
    fktj: [{ required: true, message: '请输入付款条件', trigger: 'blur' }],
    cmMoney: [{ required: true, message: '请输入付款金额', trigger: 'blur' }],
  };

  // 表格列定义
  const columns = [
    {
      title: '付款条件',
      dataIndex: 'fktj',
      key: 'fktj',
      width: 200,
      ellipsis: true,
    },
    {
      title: '付款金额',
      dataIndex: 'cmMoney',
      key: 'cmMoney',
      width: 120,
    },
    {
      title: '付款比例',
      dataIndex: 'ratio',
      key: 'ratio',
      width: 100,
    },
    {
      title: '预付日期',
      dataIndex: 'yufuDate',
      key: 'yufuDate',
      width: 120,
    },
    {
      title: '付款日期',
      dataIndex: 'fukuanDate',
      key: 'fukuanDate',
      width: 120,
    },
    {
      title: '付款状态',
      dataIndex: 'payStatus',
      key: 'payStatus',
      width: 100,
    },
    {
      title: '负责人',
      dataIndex: 'ownName',
      key: 'ownName',
      width: 100,
    },
    {
      title: '所属部门',
      dataIndex: 'deptName',
      key: 'deptName',
      width: 100,
    },
    {
      title: '备注',
      dataIndex: 'note',
      key: 'note',
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
    },
  ];

  // 格式化金额
  const formatMoney = (amount: number | string) => {
    if (!amount) return '0.00';
    return Number(amount).toLocaleString('zh-CN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  };

  // 格式化日期
  const formatDate = (date: string) => {
    return date ? formatToDate(date) : '-';
  };

  // 获取时间线颜色
  const getTimelineColor = (status: string) => {
    const colorMap: Record<string, string> = {
      '1': 'green',
      '0': 'blue',
    };
    return colorMap[status] || 'gray';
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      '1': 'green',
      '0': 'blue',
    };
    return colorMap[status] || 'default';
  };

  // 获取状态文本
  const getStatusText = (status: string) => {
    const textMap: Record<string, string> = {
      '1': '已付款',
      '0': '待付款',
    };
    return textMap[status] || '未知';
  };

  // 加载付款合同列表
  const loadPaycontractOptions = async () => {
    try {
      let response;
      
      // 如果有收款合同ID，使用专门的API获取关联的付款合同
      if (contractId.value && hasContract.value) {
        console.log('根据收款合同ID加载付款合同:', contractId.value);
        response = await paymentContractApi.getByContractId(contractId.value);
        
        // getByContractId 通常直接返回数组
        if (Array.isArray(response)) {
          paycontractOptions.value = response.map(item => ({
            id: item.id || item.pcId,
            fullName: item.name || item.cno || '未命名合同'
          }));
        } else if (response && response.code === 200 && response.data) {
          const data = Array.isArray(response.data) ? response.data : [];
          paycontractOptions.value = data.map(item => ({
            id: item.id || item.pcId,
            fullName: item.name || item.cno || '未命名合同'
          }));
        } else {
          paycontractOptions.value = [];
        }
      } else {
        // 否则获取所有付款合同选项
        response = await paymentContractApi.getSelector();
        console.log('付款合同选择器API响应:', response);
        
        // 检查响应格式并正确处理数据
        if (response && response.code === 200) {
          // 如果是标准API响应格式
          const data = response.data;
          if (Array.isArray(data)) {
            paycontractOptions.value = data;
          } else if (data && Array.isArray(data.list)) {
            // 如果数据被包装在 list 字段中
            paycontractOptions.value = data.list;
          } else {
            paycontractOptions.value = [];
          }
        } else if (Array.isArray(response)) {
          // 如果直接返回数组
          paycontractOptions.value = response;
        } else {
          // 默认设为空数组
          paycontractOptions.value = [];
          console.warn('未知的API响应格式:', response);
        }
      }

      console.log('设置付款合同选项:', paycontractOptions.value);
      
      // 如果只有一个付款合同，自动选中
      if (paycontractOptions.value.length === 1) {
        selectedPaycontractId.value = paycontractOptions.value[0].id;
        console.log('自动选中唯一的付款合同:', selectedPaycontractId.value);
        // 自动加载付款计划
        await loadPayplanList();
      } else if (paycontractOptions.value.length > 1 && !selectedPaycontractId.value) {
        // 如果有多个付款合同且未选中，提示用户选择
        console.log('有多个付款合同，请选择一个查看付款计划');
      }
    } catch (error) {
      console.error('加载付款合同列表失败:', error);
      paycontractOptions.value = [];
      createMessage.error('加载付款合同列表失败');
    }
  };

  // 加载付款计划列表
  const loadPayplanList = async () => {
    if (!selectedPaycontractId.value) {
      payplanList.value = [];
      stats.value = {
        totalAmount: 0,
        paidAmount: 0,
        unpaidAmount: 0,
        completedCount: 0,
        pendingCount: 0,
        progressRate: 0,
      };
      return;
    }

    loading.value = true;
    try {
      console.log('开始加载付款计划列表, paycontractId:', selectedPaycontractId.value);
      const response = await paymentContractMoneyApi.getByPaycontractId(selectedPaycontractId.value);
      console.log('付款计划API响应:', response);

      if (response && response.code === 200) {
        // 确保 response.data 是数组类型
        const data = response.data;
        if (Array.isArray(data)) {
          payplanList.value = data;
        } else if (data && Array.isArray(data.list)) {
          // 如果数据被包装在 list 字段中
          payplanList.value = data.list;
        } else {
          payplanList.value = [];
        }
        console.log('设置付款计划列表:', payplanList.value);
        // 加载统计信息
        await loadStats();
      } else {
        payplanList.value = [];
        const errorMsg = response?.msg || '加载付款计划列表失败';
        console.error('加载付款计划列表失败:', errorMsg);
        createMessage.error(errorMsg);
      }
    } catch (error) {
      console.error('加载付款计划列表失败:', error);
      payplanList.value = [];
      createMessage.error('加载付款计划列表失败');
    } finally {
      loading.value = false;
    }
  };

  // 加载统计信息
  const loadStats = async () => {
    if (!selectedPaycontractId.value) {
      stats.value = {
        totalAmount: 0,
        paidAmount: 0,
        unpaidAmount: 0,
        completedCount: 0,
        pendingCount: 0,
        progressRate: 0,
      };
      return;
    }

    try {
      console.log('开始加载统计信息, paycontractId:', selectedPaycontractId.value);
      const response = await paymentContractMoneyApi.getStats(selectedPaycontractId.value);
      console.log('统计信息API响应:', response);
      
      if (response && response.code === 200) {
        stats.value = response.data || {
          totalAmount: 0,
          paidAmount: 0,
          unpaidAmount: 0,
          completedCount: 0,
          pendingCount: 0,
          progressRate: 0,
        };
        console.log('设置统计信息:', stats.value);
      } else {
        console.warn('统计信息API返回异常:', response);
      }
    } catch (error) {
      console.error('加载统计信息失败:', error);
    }
  };

  // 付款合同变更
  const handlePaycontractChange = () => {
    loadPayplanList();
  };

  // 新增付款计划
  const handleAdd = () => {
    isEdit.value = false;
    drawerTitle.value = '新增付款计划';
    currentRecord.value = null;
    resetFormData();
    formData.value.pcId = selectedPaycontractId.value;
    drawerVisible.value = true;
  };

  // 查看付款计划
  const handleView = async (record: PaymentContractMoneyVO) => {
    try {
      const response = await paymentContractMoneyApi.getInfo(record.id);
      if (response.code === 200) {
        createMessage.info(`付款条件: ${response.data.fktj}, 金额: ¥${formatMoney(response.data.cmMoney)}`);
      }
    } catch (error) {
      console.error('查看付款计划失败:', error);
    }
  };

  // 编辑付款计划
  const handleEdit = (record: PaymentContractMoneyVO) => {
    isEdit.value = true;
    drawerTitle.value = '编辑付款计划';
    currentRecord.value = record;

    // 填充表单数据
    formData.value = {
      pcId: record.pcId,
      fktj: record.fktj,
      ratio: record.ratio,
      cmMoney: record.cmMoney,
      yufuDate: record.yufuDate ? dayjs(record.yufuDate) : undefined,
      fukuanDate: record.fukuanDate ? dayjs(record.fukuanDate) : undefined,
      payStatus: record.payStatus || '0',
      note: record.note || '',
      ownId: record.ownId || '',
      deptId: record.deptId || '',
      ybAmount: record.ybAmount,
      ebAmount: record.ebAmount,
      otherAmount: record.otherAmount,
    };

    drawerVisible.value = true;
  };

  // 登记付款
  const handleMarkPaid = (record: PaymentContractMoneyVO) => {
    currentPaymentRecord.value = record;
    paymentData.value = {
      fukuanDate: dayjs(),
      lastNote: '',
    };
    paymentModalVisible.value = true;
  };

  // 确认登记付款
  const handleConfirmPayment = async () => {
    if (!currentPaymentRecord.value || !paymentData.value.fukuanDate) {
      createMessage.warning('请选择付款日期');
      return;
    }

    submitLoading.value = true;
    try {
      const response = await paymentContractMoneyApi.registerPayment(
        currentPaymentRecord.value.id,
        paymentData.value.fukuanDate.format('YYYY-MM-DD'),
        paymentData.value.lastNote,
      );

      if (response.code === 200) {
        createMessage.success('登记付款成功');
        paymentModalVisible.value = false;
        loadPayplanList();
      } else {
        createMessage.error(response.msg || '登记付款失败');
      }
    } catch (error) {
      console.error('登记付款失败:', error);
      createMessage.error('登记付款失败');
    } finally {
      submitLoading.value = false;
    }
  };

  // 删除付款计划
  const handleDelete = async (record: PaymentContractMoneyVO) => {
    try {
      const response = await paymentContractMoneyApi.delete(record.id);
      if (response.code === 200) {
        createMessage.success('删除成功');
        loadPayplanList();
      } else {
        createMessage.error(response.msg || '删除失败');
      }
    } catch (error) {
      console.error('删除付款计划失败:', error);
      createMessage.error('删除失败');
    }
  };

  // 刷新
  const handleRefresh = () => {
    loadPaycontractOptions();
    loadPayplanList();
  };

  // 重置表单数据
  const resetFormData = () => {
    formData.value = {
      pcId: '',
      fktj: '',
      ratio: undefined,
      cmMoney: 0,
      yufuDate: undefined,
      fukuanDate: undefined,
      payStatus: '0',
      note: '',
      ownId: '',
      deptId: '',
      ybAmount: undefined,
      ebAmount: undefined,
      otherAmount: undefined,
    };
  };

  // 关闭抽屉
  const handleDrawerClose = () => {
    drawerVisible.value = false;
    formRef.value?.resetFields();
  };

  // 提交表单
  const handleSubmit = async () => {
    if (!formRef.value) return;

    try {
      await formRef.value.validate();

      submitLoading.value = true;

      // 处理日期格式
      const submitData: PaymentContractMoneyForm = {
        ...formData.value,
        yufuDate: formData.value.yufuDate ? formData.value.yufuDate.format('YYYY-MM-DD') : undefined,
        fukuanDate: formData.value.fukuanDate ? formData.value.fukuanDate.format('YYYY-MM-DD') : undefined,
      };

      let response;
      if (isEdit.value && currentRecord.value) {
        response = await paymentContractMoneyApi.update(currentRecord.value.id, submitData);
      } else {
        response = await paymentContractMoneyApi.create(submitData);
      }

      if (response.code === 200) {
        createMessage.success(isEdit.value ? '更新成功' : '创建成功');
        handleDrawerClose();
        loadPayplanList();
      } else {
        createMessage.error(response.msg || '操作失败');
      }
    } catch (error) {
      console.error('提交表单失败:', error);
      createMessage.error('操作失败');
    } finally {
      submitLoading.value = false;
    }
  };

  // 监听合同ID变化，重新加载付款合同选项
  watch(contractId, (newVal) => {
    if (newVal) {
      console.log('合同ID变化，重新加载付款合同选项:', newVal);
      loadPaycontractOptions();
    }
  });

  onMounted(() => {
    console.log('付款计划页面挂载，当前合同ID:', contractId.value);
    loadPaycontractOptions();
  });
</script>

<style lang="less" scoped>
  .contract-payplan-management {
    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding: 16px;
      background: #fff;
      border-radius: 6px;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    }

    .page-content {
      .payplan-summary {
        .ant-card {
          text-align: center;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
        }
      }

      .payplan-timeline,
      .payplan-table {
        .ant-card {
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
        }
      }

      .timeline-content {
        width: 100%;

        h4 {
          margin: 0;
        }

        p {
          margin: 0;
        }
      }
    }
  }
</style>
