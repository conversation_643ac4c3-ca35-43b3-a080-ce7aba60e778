<template>
  <div class="business-weeklog-history">
    <div class="mb-4">
      <a-alert type="info" show-icon message="历史记录参考" description="点击历史记录可以快速引用其内容到当前编辑表单中，便于参考和延续之前的工作内容。" />
    </div>

    <div v-if="loading" class="text-center py-8">
      <a-spin size="large" />
    </div>

    <div v-else-if="historyList.length === 0" class="text-center py-8 text-gray-500">
      <a-empty description="暂无历史记录" />
    </div>

    <div v-else class="history-timeline">
      <a-timeline>
        <a-timeline-item v-for="(item, index) in historyList" :key="item.id" :color="getTimelineColor(item.status)">
          <template #dot>
            <a-tag :color="getStatusColor(item.status)">
              {{ getStatusText(item.status) }}
            </a-tag>
          </template>

          <div class="history-item">
            <div class="history-header">
              <div class="flex justify-between items-start">
                <div>
                  <h4 class="text-base font-medium mb-1"> 第{{ historyList.length - index }}次填写 </h4>
                  <div class="text-sm text-gray-500 space-y-1">
                    <div>时间范围：{{ formatToDate(item.startDate) }} ~ {{ formatToDate(item.endDate) }}</div>
                    <div>填写人：{{ item.ownName || '未知' }}</div>
                    <div>创建时间：{{ formatToDateTime(item.createdAt) }}</div>
                    <div v-if="item.lastUpdatedAt && item.lastUpdatedAt !== item.createdAt"> 更新时间：{{ formatToDateTime(item.lastUpdatedAt) }} </div>
                  </div>
                </div>

                <a-button type="primary" size="small" @click="handleSelect(item)" :disabled="!item.note && !item.plan && !item.risk"> 引用内容 </a-button>
              </div>
            </div>

            <div class="history-content mt-3 space-y-3">
              <!-- 进度内容 -->
              <div v-if="item.note" class="content-section">
                <div class="content-label">本次进度：</div>
                <div class="content-text">{{ item.note }}</div>
              </div>

              <!-- 计划内容 -->
              <div v-if="item.plan" class="content-section">
                <div class="content-label">下一步计划：</div>
                <div class="content-text">{{ item.plan }}</div>
              </div>

              <!-- 风险内容 -->
              <div v-if="item.risk" class="content-section">
                <div class="content-label">存在风险/问题：</div>
                <div class="content-text risk-text">{{ item.risk }}</div>
              </div>

              <!-- 无内容提示 -->
              <div v-if="!item.note && !item.plan && !item.risk" class="text-gray-400 italic"> 暂无详细内容 </div>
            </div>
          </div>
        </a-timeline-item>
      </a-timeline>
    </div>

    <!-- 加载更多 -->
    <div v-if="hasMore" class="text-center mt-4">
      <a-button @click="loadMore" :loading="loadingMore"> 加载更多 </a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, watch, onMounted } from 'vue';
  import { getBusinessWeeklogHistoryByProjId } from '/@/api/project/biz/businessWeeklog';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { formatToDate, formatToDateTime } from '/@/utils/dateUtil';

  interface Props {
    projId: string;
  }

  const props = defineProps<Props>();
  const emit = defineEmits(['select']);

  const { createMessage } = useMessage();

  // 状态管理
  const loading = ref(false);
  const loadingMore = ref(false);
  const historyList = ref([]);
  const hasMore = ref(false);
  const currentPage = ref(1);
  const pageSize = ref(10);

  // 加载历史记录
  async function loadHistoryData(page = 1, append = false) {
    if (!props.projId) return;

    try {
      if (page === 1) {
        loading.value = true;
      } else {
        loadingMore.value = true;
      }

      const response = await getBusinessWeeklogHistoryByProjId(props.projId);

      if (response.code === 200) {
        const newData = response.data || [];

        if (append) {
          historyList.value = [...historyList.value, ...newData];
        } else {
          historyList.value = newData;
        }

        // 检查是否还有更多数据
        hasMore.value = newData.length === pageSize.value;
        currentPage.value = page;
      }
    } catch (error) {
      console.error('加载历史记录失败:', error);
      createMessage.error('加载历史记录失败');
    } finally {
      loading.value = false;
      loadingMore.value = false;
    }
  }

  // 加载更多
  function loadMore() {
    loadHistoryData(currentPage.value + 1, true);
  }

  // 处理选择历史记录
  function handleSelect(item: any) {
    emit('select', item);
    createMessage.success('已选择历史记录，内容已填充到编辑表单');
  }

  // 获取状态颜色
  function getStatusColor(status: number) {
    const colorMap = {
      0: 'default', // 未填写
      1: 'blue', // 已填写
      2: 'orange', // 提交审核
      3: 'green', // 已发布
      '-1': 'red', // 已驳回
    };
    return colorMap[status] || 'default';
  }

  // 获取状态文字
  function getStatusText(status: number) {
    const textMap = {
      0: '未填写',
      1: '已填写',
      2: '审核中',
      3: '已发布',
      '-1': '已驳回',
    };
    return textMap[status] || '未知';
  }

  // 获取时间线颜色
  function getTimelineColor(status: number) {
    const colorMap = {
      0: 'gray',
      1: 'blue',
      2: 'orange',
      3: 'green',
      '-1': 'red',
    };
    return colorMap[status] || 'gray';
  }

  // 监听projId变化
  watch(
    () => props.projId,
    newProjId => {
      if (newProjId) {
        currentPage.value = 1;
        loadHistoryData(1);
      } else {
        historyList.value = [];
      }
    },
    { immediate: true },
  );

  onMounted(() => {
    if (props.projId) {
      loadHistoryData(1);
    }
  });
</script>

<style lang="less" scoped>
  .business-weeklog-history {
    .history-timeline {
      max-height: 600px;
      overflow-y: auto;

      :deep(.ant-timeline) {
        .ant-timeline-item {
          .ant-timeline-item-content {
            min-height: auto;
          }
        }
      }
    }

    .history-item {
      background: #fafafa;
      border: 1px solid #e8e8e8;
      border-radius: 6px;
      padding: 16px;
      margin-bottom: 12px;

      .history-header {
        h4 {
          color: #262626;
          margin: 0;
        }
      }

      .history-content {
        .content-section {
          .content-label {
            font-weight: 500;
            color: #595959;
            margin-bottom: 4px;
            font-size: 13px;
          }

          .content-text {
            background: white;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            padding: 8px 12px;
            line-height: 1.6;
            white-space: pre-wrap;
            word-break: break-word;
          }

          .risk-text {
            border-color: #ff7875;
            background: #fff2f0;
          }
        }
      }

      &:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
      }
    }
  }
</style>
