import cryptoJs from 'crypto-js';
import decimal from 'decimal.js';
import type { Decimal } from 'decimal.js';

decimal.config({
  precision: 10, // 设置全局精度为10位有效数字
  rounding: decimal.ROUND_HALF_UP, // 设置舍入模式为四舍五入
});

export async function to<T = any>(fn: Promise<T>): Promise<[Error] | [null, T]> {
  try {
    return [null, await fn];
  } catch (e: any) {
    return [e || new Error('unknown error')];
  }
}

export async function tos(tasks) {
  return await Promise.all(tasks.map(task => to(task)));
}

export function toDecimal(num = 0) {
  const sign = num == (num = Math.abs(num));
  num = Math.floor(num * 100 + 0.50000000001);
  const cents = num % 100;
  let value: string = Math.floor(num / 100).toString();
  const centsStr: string = cents < 10 ? '0' + cents : cents.toString();
  for (let i = 0; i < Math.floor((value.length - (1 + i)) / 3); i++)
    value = value.substring(0, value.length - (4 * i + 3)) + '' + value.substring(value.length - (4 * i + 3));
  return (sign ? '' : '-') + value + '.' + centsStr;
}
export function toFileSize(size) {
  if (size == null || size == '') return '';
  if (size < 1024) return toDecimal(size) + ' 字节';
  else if (size >= 1024 && size < 1048576) return toDecimal(size / 1024) + ' KB';
  else if (size >= 1048576 && size < 1073741824) return toDecimal(size / 1024 / 1024) + ' MB';
  else if (size >= 1073741824) return toDecimal(size / 1024 / 1024 / 1024) + ' GB';
}
export function toDateText(dateTimeStamp) {
  if (!dateTimeStamp) return '';
  let result = '';
  let minute = 1000 * 60; //把分，时，天，周，半个月，一个月用毫秒表示
  let hour = minute * 60;
  let day = hour * 24;
  let week = day * 7;
  // let halfAMonth = day * 15;
  let month = day * 30;
  let now = new Date().getTime(); //获取当前时间毫秒
  let diffValue = now - dateTimeStamp; //时间差
  if (diffValue < 0) return '刚刚';
  let minC = diffValue / minute; //计算时间差的分，时，天，周，月
  let hourC = diffValue / hour;
  let dayC = diffValue / day;
  let weekC = diffValue / week;
  let monthC = diffValue / month;
  if (monthC >= 1 && monthC <= 3) {
    result = ' ' + parseInt(monthC) + '月前';
  } else if (weekC >= 1 && weekC <= 3) {
    result = ' ' + parseInt(weekC) + '周前';
  } else if (dayC >= 1 && dayC <= 6) {
    result = ' ' + parseInt(dayC) + '天前';
  } else if (hourC >= 1 && hourC <= 23) {
    result = ' ' + parseInt(hourC) + '小时前';
  } else if (minC >= 1 && minC <= 59) {
    result = ' ' + parseInt(minC) + '分钟前';
  } else if (diffValue >= 0 && diffValue <= minute) {
    result = '刚刚';
  } else {
    let datetime = new Date();
    datetime.setTime(dateTimeStamp);
    const nYear = datetime.getFullYear();
    const nMonth = datetime.getMonth() + 1 < 10 ? '0' + (datetime.getMonth() + 1) : datetime.getMonth() + 1;
    const nDate = datetime.getDate() < 10 ? '0' + datetime.getDate() : datetime.getDate();
    // const nHour = datetime.getHours() < 10 ? '0' + datetime.getHours() : datetime.getHours();
    // const nMinute = datetime.getMinutes() < 10 ? '0' + datetime.getMinutes() : datetime.getMinutes();
    // const nSecond = datetime.getSeconds() < 10 ? '0' + datetime.getSeconds() : datetime.getSeconds();
    result = nYear + '-' + nMonth + '-' + nDate;
  }
  return result;
}
export function toDateValue(dateTimeStamp) {
  if (!dateTimeStamp) return '';
  let result = '';
  let datetime = new Date();
  let nowYear = datetime.getFullYear();
  datetime.setTime(dateTimeStamp);
  let nYear = datetime.getFullYear();
  let nMonth = datetime.getMonth() + 1 < 10 ? '0' + (datetime.getMonth() + 1) : datetime.getMonth() + 1;
  let nDate = datetime.getDate() < 10 ? '0' + datetime.getDate() : datetime.getDate();
  let nHour = datetime.getHours() < 10 ? '0' + datetime.getHours() : datetime.getHours();
  let nMinute = datetime.getMinutes() < 10 ? '0' + datetime.getMinutes() : datetime.getMinutes();
  if (nYear == nowYear) {
    result = nMonth + '-' + nDate + ' ' + nHour + ':' + nMinute;
  } else {
    result = nYear + '-' + nMonth + '-' + nDate + ' ' + nHour + ':' + nMinute;
  }
  return result;
}
export function toDate(v, format?) {
  format = format || 'yyyy-MM-dd HH:mm';
  if (!v) return '';
  let d = v;
  if (typeof v === 'string') {
    if (v.indexOf('/Date(') > -1) {
      d = new Date(parseInt(v.replace('/Date(', '').replace(')/', ''), 10));
    } else if (/^\d+$/.test(v)) {
      d = new Date(Number(v));
    } else {
      d = new Date(Date.parse(v.replace(/-/g, '/').replace('T', ' ').split('.')[0]));
    }
  } else {
    d = new Date(v);
  }
  let o = {
    'M+': d.getMonth() + 1,
    'd+': d.getDate(),
    'h+': d.getHours(),
    'H+': d.getHours(),
    'm+': d.getMinutes(),
    's+': d.getSeconds(),
    'q+': Math.floor((d.getMonth() + 3) / 3),
    S: d.getMilliseconds(),
  };
  if (/(y+)/.test(format)) {
    format = format.replace(RegExp.$1, (d.getFullYear() + '').substr(4 - RegExp.$1.length));
  }
  for (let k in o) {
    if (new RegExp('(' + k + ')').test(format)) {
      format = format.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length));
    }
  }
  return format;
}
// 数据类型转换
export function getDataTypeText(val) {
  let text = val;
  switch (val) {
    case 'varchar':
      text = '字符串';
      break;
    case 'int':
      text = '整型';
      break;
    case 'datetime':
      text = '日期时间';
      break;
    case 'decimal':
      text = '浮点';
      break;
    case 'bigint':
      text = '长整型';
      break;
    case 'text':
      text = '文本';
      break;
    default:
      text = val;
      break;
  }
  return text;
}
export function getScriptFunc(str) {
  let func = null;
  try {
    func = eval(str);
    if (Object.prototype.toString.call(func) !== '[object Function]') return false;
    return func;
  } catch (_) {
    return false;
  }
}
export function interfaceDataHandler(data): any {
  if (!data.dataProcessing) return data.data;
  const dataHandler: any = getScriptFunc(data.dataProcessing);
  if (!dataHandler) return data.data;
  return dataHandler(data.data);
}
// 在线开发外链解密
export function decryptForShortLink(str) {
  const key = 'xhlinkxhlink';
  const realStr = cryptoJs.enc.Base64.stringify(cryptoJs.enc.Hex.parse(str));
  const decryptedData = cryptoJs.AES.decrypt(realStr, cryptoJs.enc.Utf8.parse(key), {
    mode: cryptoJs.mode.ECB,
    padding: cryptoJs.pad.Pkcs7,
  }).toString(cryptoJs.enc.Utf8);
  return decryptedData;
}
// 代码生成器数据匹配
export function dynamicText(value, options) {
  if (!value) return '';
  if (Array.isArray(value)) {
    if (!options || !Array.isArray(options)) return value.join();
    let textList: any[] = [];
    for (let i = 0; i < value.length; i++) {
      let item = options.filter(o => o.id == value[i])[0];
      if (!item || !item.fullName) {
        textList.push(value[i]);
      } else {
        textList.push(item.fullName);
      }
    }
    return textList.join();
  }
  if (!options || !Array.isArray(options)) return value;
  let item = options.filter(o => o.id == value)[0];
  if (!item || !item.fullName) return value;
  return item.fullName;
}

export function dynamicTreeText(value, options) {
  if (!value) return '';

  function transfer(data) {
    let textList: any[] = [];

    function loop(data, id) {
      for (let i = 0; i < data.length; i++) {
        if (data[i].id === id) {
          textList.push(data[i].fullName);
          break;
        }
        if (data[i].children) loop(data[i].children, id);
      }
    }
    for (let i = 0; i < data.length; i++) {
      loop(options, data[i]);
    }
    return textList.join();
  }
  if (!options || !Array.isArray(options)) return value.join();
  if (Array.isArray(value)) {
    let text = transfer(value);
    return text;
  } else {
    if (!options || !Array.isArray(options)) return value;
    let list = value.split();
    let text = transfer(list);
    return text;
  }
}
/**
 * 金额转中文
 * 思路：
 *                              个
 *      十     百      千       万
 *      十万   百万    千万     亿
 *      十亿   百亿    千亿
 *
 *                              1
 *      2      3       4        5
 *      6      7       8        9
 *      10
 *
 * 计算步骤
 * 1. 获取当前数值大小
 * 2. 排除个位后 数值按个，十，百，千有规律的重复 所以计算其和4的余数 pos % 4
 * 3. pos = 0 ~ 3 没有最大单位
 *    pos = 4 ~ 7 最大单位是万
 *    pos = 8 ~ 11 最大单位是亿
 * pos / 4 的整数就是最大单位
 *
 */
export function getAmountChinese(val) {
  if (!val && val !== 0) return '';
  if (val === 0) return '零元整';
  const regExp = /[a-zA-Z]/;
  if (regExp.test(val)) return '数字较大溢出';
  let amount = +val;
  if (isNaN(amount)) return '';
  if (amount < 0) amount = Number(amount.toString().split('-')[1]);
  const NUMBER = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
  const N_UNIT1 = ['', '拾', '佰', '仟'];
  const N_UNIT2 = ['', '万', '亿', '兆'];
  const D_UNIT = ['角', '分', '厘', '毫'];
  let [integer, decimal] = amount.toString().split('.');
  if (integer && (integer.length > 15 || integer.indexOf('e') > -1)) return '数字较大溢出';
  let res = '';
  // 整数部分
  if (integer) {
    let zeroCount = 0;
    for (let i = 0, len = integer.length; i < len; i++) {
      const num = integer.charAt(i);
      const pos = len - i - 1; // 排除个位后 所处的索引位置
      const q = pos / 4;
      const m = pos % 4;
      if (num === '0') {
        zeroCount++;
      } else {
        if (zeroCount > 0 && m !== 3) res += NUMBER[0];
        zeroCount = 0;
        res += NUMBER[parseInt(num)] + N_UNIT1[m];
      }
      if (m == 0 && zeroCount < 4) res += N_UNIT2[Math.floor(q)];
    }
  }
  res += '元';
  // 小数部分
  if (parseInt(decimal)) {
    for (let i = 0; i < 4; i++) {
      const num = decimal.charAt(i);
      if (parseInt(num)) res += NUMBER[num] + D_UNIT[i];
    }
  } else {
    res += '整';
  }
  if (val < 0) res = '负数' + res;
  return res;
}
// 转千位分隔
export function thousandsFormat(num) {
  if (num === 0) return '0';
  if (!num) return '';
  const numArr = num.toString().split('.');
  numArr[0] = numArr[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  return numArr.join('.');
}
export function getDateTimeUnit(format) {
  if (format == 'YYYY') return 'year';
  if (format == 'YYYY-MM') return 'month';
  if (format == 'YYYY-MM-DD') return 'day';
  if (format == 'YYYY-MM-DD HH:mm') return 'minute';
  if (format == 'YYYY-MM-DD HH:mm:ss') return 'second';
  return 'day';
}
export function getTimeUnit(key) {
  if (key == 1) return 'year';
  if (key == 2) return 'month';
  if (key == 3) return 'day';
  if (key == 4) return 'hour';
  if (key == 5) return 'minute';
  if (key == 6) return 'second';
  return 'day';
}

function calcDecimalPlaces(v1, v2) {
  const v1Str = v1.toString();
  const v2Str = v2.toString();
  const curDecimalPlaces1 = (v1Str.split('.')[1] || '').length;
  const curDecimalPlaces2 = (v2Str.split('.')[1] || '').length;
  return Math.max(curDecimalPlaces1, curDecimalPlaces2);
}

/**
 * JSON数组树形求和，处理小数不准确纹理
 * @param arrayList JSON数组
 * @param props 求和属性
 * @param iteratee 迭代器，可以获取JSON每项数据
 * @returns 和
 */
export function sumBy(arrayList: Recordable[], props: string, iteratee?: Fn): number {
  // let total = 0;
  // const isIterateeFunc = typeof iteratee === 'function';
  // if (!props) {
  //   console.warn('props不能为空！');
  //   return total;
  // }
  // for (let i = 0; i < arrayList.length; i++) {
  //   let num = arrayList[i][props];
  //   if (isIterateeFunc) {
  //     (iteratee as Fn)(arrayList[i]);
  //   }
  //   if (typeof num === 'undefined' || num === null) {
  //     num = 0;
  //   }
  //   const decimalPlaces = calcDecimalPlaces(total, num);
  //   const powers = Math.pow(10, decimalPlaces + 1);
  //   total = total * powers + num * powers;
  //   total /= powers;
  // }
  // return total;
  let total = new decimal(0);
  const isIterateeFunc = typeof iteratee === 'function';
  if (!props) {
    console.warn('props不能为空！');
    return total.toNumber();
  }
  for (let i = 0; i < arrayList.length; i++) {
    let num = arrayList[i][props];
    if (isIterateeFunc) {
      (iteratee as Fn)(arrayList[i]);
    }
    if (typeof num === 'undefined' || num === null) {
      num = 0;
    }
    total = total.add(new decimal(num));
  }
  return total.toNumber();
}

type DecimalOrNumber = Decimal | number;

// 两个数字加法
export function add(n1: DecimalOrNumber, n2?: DecimalOrNumber) {
  return new decimal(n1 || 0).add(new decimal(n2 || 0));
}

/**
 * 两个数字求差值，处理浮点数不准确问题
 * @param n1 数字
 * @param n2 被减数
 */
export function sub(n1: DecimalOrNumber, n2: DecimalOrNumber): Decimal {
  // const decimalPlaces = calcDecimalPlaces(n1, n2);
  // const powers = Math.pow(10, decimalPlaces + 1);
  // const subs = n1 * powers - n2 * powers;
  // return subs / powers;
  return new decimal(n1 || 0).sub(new decimal(n2 || 0));
}

// 两个数字乘法
export function mul(n1: DecimalOrNumber, n2: DecimalOrNumber): Decimal {
  return new decimal(n1 || 0).mul(new decimal(n2 || 0));
}

// 多个数字乘法
export function mulAll(data: DecimalOrNumber[]): Decimal {
  let rtData = new decimal(1);
  data.forEach(t => {
    rtData = rtData.mul(t || 0);
  });
  return rtData;
}

// 两个数字除法
export function div(n1: DecimalOrNumber, n2: DecimalOrNumber): Decimal {
  return new decimal(n1).div(new decimal(n2));
}

/**
 * 比较2个中文排序
 * @param cstr1 中文字符1
 * @param cstr2 中文字符2
 * @returns 1 -1
 */
export function sortChineseStr(cstr1: string, cstr2: string) {
  cstr1 = typeof cstr1 === 'string' ? cstr1 : '';
  cstr2 = typeof cstr2 === 'string' ? cstr2 : '';
  return cstr1.localeCompare(cstr2, 'zh-Hans-CN');
}
