package com.xinghuo.project.schema.service;

import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.project.schema.entity.ProjectSchemaWbsEntity;
import com.xinghuo.project.schema.model.ProjectSchemaWbsPagination;

import java.util.List;
import java.util.Map;

/**
 * 项目模板WBS计划服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
public interface ProjectSchemaWbsService extends BaseService<ProjectSchemaWbsEntity> {

    /**
     * 获取WBS计划列表
     *
     * @param pagination 分页参数
     * @return WBS计划列表
     */
    List<ProjectSchemaWbsEntity> getList(ProjectSchemaWbsPagination pagination);

    /**
     * 根据项目模板ID获取WBS计划列表
     *
     * @param projectTemplateId 项目模板ID
     * @return WBS计划列表
     */
    List<ProjectSchemaWbsEntity> getListByTemplateId(String projectTemplateId);

    /**
     * 获取WBS计划详情
     *
     * @param id WBS计划ID
     * @return WBS计划详情
     */
    ProjectSchemaWbsEntity getInfo(String id);

    /**
     * 创建WBS计划
     *
     * @param entity WBS计划信息
     * @return WBS计划ID
     */
    String create(ProjectSchemaWbsEntity entity);

    /**
     * 更新WBS计划
     *
     * @param id WBS计划ID
     * @param entity WBS计划信息
     */
    void update(String id, ProjectSchemaWbsEntity entity);

    /**
     * 删除WBS计划
     *
     * @param id WBS计划ID
     */
    void delete(String id);

    /**
     * 批量删除WBS计划
     *
     * @param ids WBS计划ID列表
     */
    void batchDelete(List<String> ids);

    /**
     * 根据项目模板ID删除所有WBS计划
     *
     * @param projectTemplateId 项目模板ID
     */
    void deleteByTemplateId(String projectTemplateId);

    /**
     * 从WBS模板导入WBS计划
     *
     * @param projectTemplateId 项目模板ID
     * @param wbsTemplateId WBS模板ID
     * @param parentId 父级ID（可选）
     */
    void importFromWbsTemplate(String projectTemplateId, String wbsTemplateId, String parentId);

    /**
     * 从活动库添加活动到WBS计划
     *
     * @param projectTemplateId 项目模板ID
     * @param activityIds 活动库ID列表
     * @param parentId 父级ID（可选）
     */
    void addActivitiesFromLibrary(String projectTemplateId, List<String> activityIds, String parentId);

    /**
     * 调整WBS计划序号
     *
     * @param id WBS计划ID
     * @param direction 调整方向（up/down）
     */
    void adjustSeqNo(String id, String direction);

    /**
     * 获取下一个序号
     *
     * @param projectTemplateId 项目模板ID
     * @param parentId 父级ID（可选）
     * @return 下一个序号
     */
    Integer getNextSeqNo(String projectTemplateId, String parentId);

    /**
     * 重新计算WBS编码
     *
     * @param projectTemplateId 项目模板ID
     */
    void recalculateWbsCode(String projectTemplateId);

    /**
     * 重新计算层级深度
     *
     * @param projectTemplateId 项目模板ID
     */
    void recalculateLevel(String projectTemplateId);

    /**
     * 移动WBS节点
     *
     * @param id WBS计划ID
     * @param newParentId 新父级ID
     * @param newSeqNo 新序号
     */
    void moveNode(String id, String newParentId, Integer newSeqNo);

    /**
     * 复制WBS节点（包含子节点）
     *
     * @param id 源WBS计划ID
     * @param newParentId 新父级ID
     * @return 新WBS计划ID
     */
    String copyNode(String id, String newParentId);

    /**
     * 获取WBS树形结构
     *
     * @param projectTemplateId 项目模板ID
     * @return WBS树形结构
     */
    List<ProjectSchemaWbsEntity> getWbsTree(String projectTemplateId);

    /**
     * 获取子节点列表
     *
     * @param parentId 父级ID
     * @return 子节点列表
     */
    List<ProjectSchemaWbsEntity> getChildren(String parentId);

    /**
     * 检查是否存在子节点
     *
     * @param parentId 父级ID
     * @return 是否存在子节点
     */
    boolean hasChildren(String parentId);

    /**
     * 获取WBS统计信息
     *
     * @param projectTemplateId 项目模板ID
     * @return 统计信息
     */
    Map<String, Object> getWbsStatistics(String projectTemplateId);

    /**
     * 验证WBS结构完整性
     *
     * @param projectTemplateId 项目模板ID
     * @return 验证结果
     */
    Map<String, Object> validateWbsStructure(String projectTemplateId);

    /**
     * 批量保存WBS计划
     *
     * @param projectTemplateId 项目模板ID
     * @param wbsList WBS计划列表
     */
    void batchSave(String projectTemplateId, List<ProjectSchemaWbsEntity> wbsList);

    /**
     * 获取WBS路径（从根节点到当前节点）
     *
     * @param id WBS计划ID
     * @return WBS路径
     */
    List<ProjectSchemaWbsEntity> getWbsPath(String id);

    /**
     * 检查WBS名称是否存在
     *
     * @param projectTemplateId 项目模板ID
     * @param name WBS名称
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean isExistByName(String projectTemplateId, String name, String excludeId);

    /**
     * 检查WBS编码是否存在
     *
     * @param projectTemplateId 项目模板ID
     * @param wbsCode WBS编码
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean isExistByWbsCode(String projectTemplateId, String wbsCode, String excludeId);

    /**
     * 生成WBS编码
     *
     * @param projectTemplateId 项目模板ID
     * @param parentId 父级ID
     * @return WBS编码
     */
    String generateWbsCode(String projectTemplateId, String parentId);

    /**
     * 更新前置任务关系
     *
     * @param id WBS计划ID
     * @param predecessors 前置任务JSON字符串
     */
    void updatePredecessors(String id, String predecessors);

    /**
     * 获取前置任务列表
     *
     * @param id WBS计划ID
     * @return 前置任务列表
     */
    List<Map<String, Object>> getPredecessors(String id);

    /**
     * 检查循环依赖
     *
     * @param id WBS计划ID
     * @param predecessorId 前置任务ID
     * @return 是否存在循环依赖
     */
    boolean checkCircularDependency(String id, String predecessorId);
}
