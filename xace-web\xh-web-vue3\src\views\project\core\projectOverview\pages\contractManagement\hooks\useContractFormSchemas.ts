import type { FormSchema } from '/@/components/Form';
import { computed, type Ref } from 'vue';
import {
  CONTRACT_STATUS_MAP,
  MONEY_STATUS_MAP,
  ACCDOC_STATUS_MAP,
  CONTINUE_STATUS_MAP,
  ARCHIVE_STATUS_MAP,
  WORK_STATUS_MAP,
  REPORT_FREQUENCY_OPTIONS,
} from '../constants';

/**
 * 合同表单配置 Hook
 * 统一管理表单配置，减少代码重复
 */
export function useContractFormSchemas(editMode: Ref<boolean>) {
  // 基础信息表单配置
  const basicFormSchemas = computed<FormSchema[]>(() => {
    const isEdit = editMode.value;
    return [
      {
        field: 'name',
        label: '合同名称',
        component: 'Input',
        colProps: { span: 12 },
        required: isEdit,
        componentProps: {
          disabled: !isEdit,
          placeholder: isEdit ? '请输入合同名称' : '',
        },
      },
      {
        field: 'cno',
        label: '合同编号',
        component: 'Input',
        colProps: { span: 12 },
        required: isEdit,
        componentProps: {
          disabled: !isEdit,
          placeholder: isEdit ? '请输入合同编号' : '',
        },
      },
      {
        field: 'contractStatus',
        label: '合同状态',
        component: isEdit ? 'Select' : 'Input',
        colProps: { span: 12 },
        componentProps: isEdit
          ? {
              placeholder: '请选择合同状态',
              options: Object.entries(CONTRACT_STATUS_MAP).map(([key, value]) => ({
                id: key,
                fullName: value,
              })),
            }
          : {
              disabled: true,
            },
        render: !isEdit ? ({ model }) => CONTRACT_STATUS_MAP[model.contractStatus] || '未知' : undefined,
      },
      {
        field: 'custId',
        label: '客户单位',
        component: 'PopupSelect',
        colProps: { span: 12 },
        componentProps: {
          disabled: !isEdit,
          placeholder: isEdit ? '请选择客户单位' : '',
          interfaceId: 'customer',
          modalTitle: '选择客户单位',
          selectType: 'all',
          multiple: false,
          allowClear: true,
        },
      },
      {
        field: 'finalUserId',
        label: '最终用户',
        component: 'PopupSelect',
        colProps: { span: 12 },
        componentProps: {
          disabled: !isEdit,
          placeholder: isEdit ? '请选择最终用户' : '',
          interfaceId: 'customer',
          modalTitle: '选择最终用户',
          selectType: 'all',
          multiple: false,
          allowClear: true,
        },
      },
      {
        field: 'ownId',
        label: '负责人',
        component: 'UserSelect',
        colProps: { span: 12 },
        required: isEdit,
        componentProps: {
          disabled: !isEdit,
          placeholder: isEdit ? '请选择负责人' : '',
        },
      },
      {
        field: 'deptId',
        label: '所属部门',
        component: 'DepSelect',
        colProps: { span: 12 },
        componentProps: {
          disabled: !isEdit,
          placeholder: isEdit ? '请选择部门' : '',
        },
      },
      {
        field: 'reportFrequency',
        label: '汇报频率',
        component: 'Select',
        colProps: { span: 12 },
        componentProps: {
          disabled: !isEdit,
          placeholder: isEdit ? '请选择汇报频率' : '',
          options: REPORT_FREQUENCY_OPTIONS,
        },
      },
      {
        field: 'signYear',
        label: '合同年度',
        component: 'InputNumber',
        colProps: { span: 12 },
        componentProps: {
          disabled: !isEdit,
          placeholder: isEdit ? '请输入合同年度' : '',
          precision: 0,
          min: 2000,
          max: 2100,
        },
      },
    ];
  });

  // 金额信息表单配置
  const amountFormSchemas = computed<FormSchema[]>(() => {
    const isEdit = editMode.value;
    return [
      {
        field: 'amount',
        label: '合同金额',
        component: 'InputNumber',
        colProps: { span: 12 },
        componentProps: {
          disabled: !isEdit,
          precision: 2,
          formatter: (value: number) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
        },
      },
      {
        field: 'ysAmount',
        label: '已收金额',
        component: 'InputNumber',
        colProps: { span: 12 },
        componentProps: {
          disabled: true, // 自动计算字段，始终只读
          precision: 2,
          formatter: (value: number) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
        },
      },
      {
        field: 'yearYsAmount',
        label: '本年度收款金额',
        component: 'InputNumber',
        colProps: { span: 12 },
        componentProps: {
          disabled: true, // 自动计算字段，始终只读
          precision: 2,
          formatter: (value: number) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
        },
      },
      {
        field: 'externalAmount',
        label: '外采金额',
        component: 'InputNumber',
        colProps: { span: 12 },
        componentProps: {
          disabled: !isEdit,
          precision: 2,
          formatter: (value: number) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
        },
      },
      {
        field: 'moneyStatus',
        label: '收款状态',
        component: 'Input',
        colProps: { span: 12 },
        componentProps: { disabled: true },
        render: ({ model }) => MONEY_STATUS_MAP[model.moneyStatus] || '未知',
      },
    ];
  });

  // 日期信息表单配置生成器
  const createDateSchema = (field: string, label: string, isEdit: boolean): FormSchema => ({
    field,
    label,
    component: 'DatePicker',
    colProps: { span: 12 },
    componentProps: {
      disabled: !isEdit,
      format: 'YYYY-MM-DD',
      placeholder: isEdit ? `请选择${label}` : label,
    },
  });

  // 日期信息表单配置
  const dateFormSchemas = computed<FormSchema[]>(() => {
    const isEdit = editMode.value;
    return [
      createDateSchema('signDate', '签订日期', isEdit),
      createDateSchema('bidDate', '中标日期', isEdit),
      createDateSchema('commencementDate', '开工日期', isEdit),
      createDateSchema('initialCheckDate', '初验日期', isEdit),
      createDateSchema('finalCheckDate', '终验日期', isEdit),
      createDateSchema('auditDate', '审计日期', isEdit),
    ];
  });

  // 合同周期表单配置
  const cycleFormSchemas = computed<FormSchema[]>(() => {
    const isEdit = editMode.value;
    return [
      createDateSchema('cstartDate', '合同开始日期', isEdit),
      createDateSchema('cendDate', '合同结束日期', isEdit),
      createDateSchema('mstartDate', '维保开始日期', isEdit),
      createDateSchema('mendDate', '维保结束日期', isEdit),
    ];
  });

  // 联系人信息表单配置生成器
  const createContactSchema = (prefix: string, label: string, isEdit: boolean): FormSchema[] => [
    {
      field: `${prefix}DeptId`,
      label: `${label}单位`,
      component: 'PopupSelect',
      colProps: { span: 12 },
      componentProps: {
        disabled: !isEdit,
        placeholder: isEdit ? `请选择${label}单位` : '',
      },
    },
    {
      field: `${prefix}Linkman`,
      label: `${label}联系人`,
      component: 'Input',
      colProps: { span: 12 },
      componentProps: {
        disabled: !isEdit,
        placeholder: isEdit ? `请输入${label}联系人` : '',
      },
    },
    {
      field: `${prefix}Telephone`,
      label: `${label}联系电话`,
      component: 'Input',
      colProps: { span: 12 },
      componentProps: {
        disabled: !isEdit,
        placeholder: isEdit ? `请输入${label}联系电话` : '',
      },
    },
  ];

  // 联系人信息表单配置
  const contactFormSchemas = computed<FormSchema[]>(() => {
    const isEdit = editMode.value;
    return [
      {
        field: 'linkman',
        label: '合同联系人',
        component: 'Input',
        colProps: { span: 12 },
        componentProps: {
          disabled: !isEdit,
          placeholder: isEdit ? '请输入合同联系人' : '',
        },
      },
      {
        field: 'linkTelephone',
        label: '联系电话',
        component: 'Input',
        colProps: { span: 12 },
        componentProps: {
          disabled: !isEdit,
          placeholder: isEdit ? '请输入联系电话' : '',
        },
      },
      ...createContactSchema('sv', '监理', isEdit),
      ...createContactSchema('review', '测评', isEdit),
      ...createContactSchema('db', '等保', isEdit),
      ...createContactSchema('sm', '商密', isEdit),
      ...createContactSchema('js', '结算', isEdit),
    ];
  });

  // 状态管理表单配置
  const statusFormSchemas = computed<FormSchema[]>(() => {
    const isEdit = editMode.value;
    return [
      {
        field: 'contractStatus',
        label: '合同状态',
        component: isEdit ? 'Select' : 'Input',
        colProps: { span: 12 },
        componentProps: isEdit
          ? {
              placeholder: '请选择合同状态',
              options: Object.entries(CONTRACT_STATUS_MAP).map(([key, value]) => ({
                id: key,
                fullName: value,
              })),
            }
          : { disabled: true },
        render: !isEdit ? ({ model }) => CONTRACT_STATUS_MAP[model.contractStatus] || '未知' : undefined,
      },
      {
        field: 'moneyStatus',
        label: '收款状态',
        component: 'Input',
        colProps: { span: 12 },
        componentProps: { disabled: true },
        render: ({ model }) => MONEY_STATUS_MAP[model.moneyStatus] || '未知',
      },
      {
        field: 'accdocStatus',
        label: '验收状态',
        component: 'Select',
        colProps: { span: 12 },
        componentProps: {
          disabled: !isEdit,
          options: Object.entries(ACCDOC_STATUS_MAP).map(([key, value]) => ({
            id: key,
            fullName: value,
          })),
        },
      },
      {
        field: 'isContinue',
        label: '续签状态',
        component: 'Select',
        colProps: { span: 12 },
        componentProps: {
          disabled: !isEdit,
          options: CONTINUE_STATUS_MAP,
        },
      },
      {
        field: 'isArchive',
        label: '归档状态',
        component: 'Select',
        colProps: { span: 12 },
        componentProps: {
          disabled: !isEdit,
          options: ARCHIVE_STATUS_MAP,
        },
      },
      {
        field: 'workStatus',
        label: '工时状态',
        component: 'Select',
        colProps: { span: 12 },
        componentProps: {
          disabled: !isEdit,
          options: WORK_STATUS_MAP,
        },
      },
    ];
  });

  // 毛利分析表单配置
  const profitFormSchemas = computed<FormSchema[]>(() => {
    const isEdit = editMode.value;
    const createAmountField = (field: string, label: string, isReadonly = false) => ({
      field,
      label,
      component: 'InputNumber',
      colProps: { span: 12 },
      componentProps: {
        disabled: !isEdit || isReadonly,
        precision: 2,
        formatter: (value: number) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
        placeholder: isEdit && !isReadonly ? `请输入${label}` : '',
      },
    });

    const createPercentField = (field: string, label: string, isReadonly = false) => ({
      field,
      label,
      component: 'InputNumber',
      colProps: { span: 12 },
      componentProps: {
        disabled: !isEdit || isReadonly,
        precision: 2,
        max: 100,
        formatter: (value: number) => `${value}%`,
        placeholder: isEdit && !isReadonly ? `请输入${label}` : '',
      },
    });

    return [
      createAmountField('estProbit', '预估毛利'),
      createAmountField('actProbit', '实际毛利', true),
      createPercentField('estProbitRatio', '预估毛利率'),
      createPercentField('actProbitRatio', '实际毛利率', true),
      createAmountField('evaExternalAmount', '采购费用预测'),
      createAmountField('actExternalAmount', '实际采购金额', true),
      createAmountField('evaCostAmount', '费用预测'),
      createAmountField('actCostAmount', '实际费用', true),
      {
        field: 'autoManhours',
        label: '累计工时',
        component: 'InputNumber',
        colProps: { span: 12 },
        componentProps: {
          disabled: true,
          precision: 1,
          formatter: (value: number) => `${value}小时`,
        },
      },
      {
        field: 'autoRatioLevel',
        label: '毛利率区间',
        component: 'Input',
        colProps: { span: 12 },
        componentProps: { disabled: true },
      },
    ];
  });

  // 备注信息表单配置
  const noteFormSchemas = computed<FormSchema[]>(() => {
    const isEdit = editMode.value;
    return [
      {
        field: 'note',
        label: '备注',
        component: 'InputTextArea',
        colProps: { span: 24 },
        componentProps: {
          disabled: !isEdit,
          rows: 4,
          placeholder: isEdit ? '请输入备注信息' : '',
        },
      },
      {
        field: 'continueDesc',
        label: '续签说明',
        component: 'InputTextArea',
        colProps: { span: 24 },
        componentProps: {
          disabled: !isEdit,
          rows: 3,
          placeholder: isEdit ? '请输入续签说明' : '',
        },
      },
      {
        field: 'accdocPath',
        label: '验收文档路径',
        component: 'Input',
        colProps: { span: 24 },
        componentProps: {
          disabled: !isEdit,
          placeholder: isEdit ? '请输入验收文档路径' : '',
        },
      },
      {
        field: 'accdocNote',
        label: '验收文档说明',
        component: 'InputTextArea',
        colProps: { span: 24 },
        componentProps: {
          disabled: !isEdit,
          rows: 3,
          placeholder: isEdit ? '请输入验收文档说明' : '',
        },
      },
    ];
  });

  return {
    basicFormSchemas,
    amountFormSchemas,
    dateFormSchemas,
    cycleFormSchemas,
    contactFormSchemas,
    statusFormSchemas,
    profitFormSchemas,
    noteFormSchemas,
  };
}
