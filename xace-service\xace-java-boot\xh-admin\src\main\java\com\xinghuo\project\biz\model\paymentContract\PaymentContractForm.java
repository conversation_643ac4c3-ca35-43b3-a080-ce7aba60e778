package com.xinghuo.project.biz.model.paymentContract;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 采购合同表单对象
 */
@Data
@Schema(description = "采购合同表单对象")
public class PaymentContractForm {

    /**
     * 采购合同名称
     */
    @NotBlank(message = "采购合同名称不能为空")
    @Size(max = 500, message = "采购合同名称长度不能超过500个字符")
    @Schema(description = "采购合同名称")
    private String name;

    /**
     * 采购合同编号
     */
    @Size(max = 50, message = "采购合同编号长度不能超过50个字符")
    @Schema(description = "采购合同编号")
    private String cno;

    /**
     * 收款合同ID
     */
    @NotBlank(message = "收款合同不能为空")
    @Schema(description = "收款合同ID")
    private String contractId;

    /**
     * 供应商ID
     */
    @NotBlank(message = "供应商不能为空")
    @Schema(description = "供应商ID")
    private String suppilerId;

    /**
     * 采购负责人ID
     */
    @Schema(description = "采购负责人ID")
    private String ownId;

    /**
     * 采购合同金额
     */
    @NotNull(message = "采购合同金额不能为空")
    @Schema(description = "采购合同金额")
    private BigDecimal amount;

    /**
     * 付款状态
     */
    @Schema(description = "付款状态")
    private String moneyStatus;

    /**
     * 采购合同状态
     */
    @Schema(description = "采购合同状态")
    private String status;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @Schema(description = "备注")
    private String note;

    /**
     * 预计签订日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "预计签订日期")
    private Date estSignDate;

    /**
     * 实际签订日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "实际签订日期")
    private Date signDate;

    /**
     * 开发一部金额
     */
    @Schema(description = "开发一部金额")
    private BigDecimal kfybAmount;

    /**
     * 开发二部金额
     */
    @Schema(description = "开发二部金额")
    private BigDecimal kfebAmount;

    /**
     * 综合金额
     */
    @Schema(description = "综合金额")
    private BigDecimal otherAmount;
}
