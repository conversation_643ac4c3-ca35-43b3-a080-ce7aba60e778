package com.xinghuo.project.biz.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.controller.BaseController;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.constant.MsgCode;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.common.util.json.JsonXhUtil;
import com.xinghuo.project.biz.entity.BizContractEntity;
import com.xinghuo.project.biz.entity.BizContractMoneyEntity;
import com.xinghuo.project.biz.model.bizContractMoney.BizContractMoneyForm;
import com.xinghuo.project.biz.model.bizContractMoney.BizContractMoneyPagination;
import com.xinghuo.project.biz.model.bizContractMoney.BizContractMoneyVO;
import com.xinghuo.project.biz.service.BizContractMoneyService;
import com.xinghuo.project.biz.service.BizContractService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 合同收款管理
 */
@Slf4j
@RestController
@Tag(name = "合同收款管理", description = "BizContractEntity")
@RequestMapping("/api/project/biz/contract/money")
public class BizContractMoneyController extends BaseController<BizContractMoneyService, BizContractMoneyEntity> {

    @Resource
    private BizContractMoneyService moneyService;

    @Resource
    private BizContractService contractService;

    /**
     * 获取合同收款列表
     *
     * @param pagination 分页查询参数
     * @return 合同收款列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取合同收款列表")
    public ActionResult list(@RequestBody BizContractMoneyPagination pagination) {
        List<BizContractMoneyEntity> list = moneyService.getList(pagination);
        List<BizContractMoneyVO> listVOs = BeanCopierUtils.copyList(list, BizContractMoneyVO.class);

        // 填充合同信息
        for (BizContractMoneyVO vo : listVOs) {
            if (StrXhUtil.isNotEmpty(vo.getContractId())) {
                BizContractEntity contract = contractService.getInfo(vo.getContractId());
                if (contract != null) {
                    vo.setContractName(contract.getName());
                    vo.setContractNo(contract.getCno());
                }
            }
        }

        PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
        return ActionResult.page(listVOs,page);
    }

    /**
     * 根据合同ID获取收款列表
     *
     * @param contractId 合同ID
     * @return 收款列表
     */
    @GetMapping("/contract/{contractId}")
    @Operation(summary = "根据合同ID获取收款列表")
    @Parameters({
            @Parameter(name = "contractId", description = "合同ID", required = true),
    })
    public ActionResult<List<BizContractMoneyVO>> listByContractId(@PathVariable("contractId") String contractId) {
        List<BizContractMoneyEntity> list = moneyService.getListByContractId(contractId);
        List<BizContractMoneyVO> listVOs = BeanCopierUtils.copyList(list, BizContractMoneyVO.class);

        // 填充合同信息
        for (BizContractMoneyVO vo : listVOs) {
            if (StrXhUtil.isNotEmpty(vo.getContractId())) {
                BizContractEntity contract = contractService.getInfo(vo.getContractId());
                if (contract != null) {
                    vo.setContractName(contract.getName());
                    vo.setContractNo(contract.getCno());
                }
            }
        }

        return ActionResult.success(listVOs);
    }

    /**
     * 获取合同收款详情
     *
     * @param id 合同收款ID
     * @return 合同收款详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取合同收款详情")
    @Parameters({
            @Parameter(name = "id", description = "合同收款ID", required = true),
    })
    public ActionResult<BizContractMoneyVO> info(@PathVariable("id") String id) {
        BizContractMoneyEntity entity = moneyService.getInfo(id);
        BizContractMoneyVO vo = JsonXhUtil.jsonDeepCopy(entity, BizContractMoneyVO.class);
        return ActionResult.success(vo);
    }

    /**
     * 创建合同收款
     *
     * @param moneyForm 合同收款表单
     * @return 操作结果
     */
    @PostMapping
    @Operation(summary = "创建合同收款")
    @Parameters({
            @Parameter(name = "moneyForm", description = "合同收款表单", required = true),
    })
    public ActionResult create(@RequestBody @Valid BizContractMoneyForm moneyForm) {
        BizContractMoneyEntity entity = JsonXhUtil.jsonDeepCopy(moneyForm, BizContractMoneyEntity.class);
        moneyService.create(entity);
        return ActionResult.success(MsgCode.SU001.get());
    }

    /**
     * 更新合同收款
     *
     * @param id        合同收款ID
     * @param moneyForm 合同收款表单
     * @return 操作结果
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新合同收款")
    @Parameters({
            @Parameter(name = "id", description = "合同收款ID", required = true),
            @Parameter(name = "moneyForm", description = "合同收款表单", required = true),
    })
    public ActionResult update(@PathVariable("id") String id, @RequestBody @Valid BizContractMoneyForm moneyForm) {
        BizContractMoneyEntity entity = JsonXhUtil.jsonDeepCopy(moneyForm, BizContractMoneyEntity.class);
        moneyService.update(id, entity);
        return ActionResult.success(MsgCode.SU002.get());
    }

    /**
     * 删除合同收款
     *
     * @param id 合同收款ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除合同收款")
    @Parameters({
            @Parameter(name = "id", description = "合同收款ID", required = true),
    })
    public ActionResult delete(@PathVariable("id") String id) {
        moneyService.delete(id);
        return ActionResult.success(MsgCode.SU003.get());
    }

    /**
     * 更新收款状态
     *
     * @param id         合同收款ID
     * @param statusForm 状态更新表单
     * @return 操作结果
     */
    @PutMapping("/{id}/status")
    @Operation(summary = "更新收款状态")
    @Parameters({
            @Parameter(name = "id", description = "合同收款ID", required = true),
            @Parameter(name = "statusForm", description = "状态更新表单", required = true),
    })
    public ActionResult updateStatus(@PathVariable("id") String id, @RequestBody @Valid BizContractMoneyForm statusForm) {
        moneyService.updateStatus(id, statusForm);
        return ActionResult.success(MsgCode.SU002.get());
    }

    /**
     * 登记开票
     *
     * @param id          合同收款ID
     * @param kaipiaoDate 开票日期
     * @param lastNote    备注
     * @return 操作结果
     */
    @PutMapping("/{id}/invoice")
    @Operation(summary = "登记开票")
    @Parameters({
            @Parameter(name = "id", description = "合同收款ID", required = true),
            @Parameter(name = "kaipiaoDate", description = "开票日期", required = true),
            @Parameter(name = "lastNote", description = "备注"),
    })
    public ActionResult registerInvoice(
            @PathVariable("id") String id,
            @RequestParam("kaipiaoDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date kaipiaoDate,
            @RequestParam(value = "lastNote", required = false) String lastNote) {
        moneyService.registerInvoice(id, kaipiaoDate, lastNote);
        return ActionResult.success(MsgCode.SU002.get());
    }

    /**
     * 登记收款
     *
     * @param id           合同收款ID
     * @param shoukuanDate 收款日期
     * @param lastNote     备注
     * @return 操作结果
     */
    @PutMapping("/{id}/payment")
    @Operation(summary = "登记收款")
    @Parameters({
            @Parameter(name = "id", description = "合同收款ID", required = true),
            @Parameter(name = "shoukuanDate", description = "收款日期", required = true),
            @Parameter(name = "lastNote", description = "备注"),
    })
    public ActionResult registerPayment(
            @PathVariable("id") String id,
            @RequestParam("shoukuanDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date shoukuanDate,
            @RequestParam(value = "lastNote", required = false) String lastNote) {
        moneyService.registerPayment(id, shoukuanDate, lastNote);
        return ActionResult.success(MsgCode.SU002.get());
    }

    /**
     * 获取收款统计数据
     *
     * @return 收款统计数据
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取收款统计数据")
    public ActionResult<Map<String, Object>> getStatistics() {
        Map<String, Object> statistics = moneyService.getStatistics();
        return ActionResult.success(statistics);
    }
}
