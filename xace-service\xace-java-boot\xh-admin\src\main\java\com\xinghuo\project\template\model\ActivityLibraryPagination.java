package com.xinghuo.project.template.model;

import com.xinghuo.common.base.model.Pagination;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 标准项目活动库分页查询参数
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ActivityLibraryPagination extends Pagination {

    /**
     * 活动编码
     */
    private String code;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 状态 (0:启用, 1:禁用)
     */
    private Integer status;

    /**
     * 关键字搜索（编码或名称）
     */
    private String keyword;

    /**
     * 创建时间开始
     */
    private Date createTimeStart;

    /**
     * 创建时间结束
     */
    private Date createTimeEnd;

    /**
     * 是否是里程碑 (1:是, 0:否)
     */
    private Integer isMilestone;

    /**
     * 活动大类ID
     */
    private String activityTypeId;

    /**
     * 活动子类ID
     */
    private String activitySubTypeId;

    /**
     * 标准工期最小值 (天)
     */
    private BigDecimal durationMin;

    /**
     * 标准工期最大值 (天)
     */
    private BigDecimal durationMax;

    /**
     * 标准工时最小值 (小时)
     */
    private BigDecimal standardHourMin;

    /**
     * 标准工时最大值 (小时)
     */
    private BigDecimal standardHourMax;

    /**
     * 责任角色ID
     */
    private String responseRoleId;

    /**
     * 完成方式ID
     */
    private String completeTypeId;

    /**
     * 创建用户ID
     */
    private String createdBy;
}
