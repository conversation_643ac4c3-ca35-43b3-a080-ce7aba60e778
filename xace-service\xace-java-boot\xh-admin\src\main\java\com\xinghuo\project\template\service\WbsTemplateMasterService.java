package com.xinghuo.project.template.service;

import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.project.template.entity.WbsTemplateMasterEntity;
import com.xinghuo.project.template.entity.WbsTemplateDetailEntity;
import com.xinghuo.project.template.model.WbsTemplateMasterPagination;
import com.xinghuo.project.template.model.dto.WbsTemplateVO;

import java.util.List;
import java.util.Map;

/**
 * WBS计划模板主表服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
public interface WbsTemplateMasterService extends BaseService<WbsTemplateMasterEntity> {

    /**
     * 获取WBS模板列表
     *
     * @param pagination 分页参数
     * @return 模板列表
     */
    List<WbsTemplateVO> getList(WbsTemplateMasterPagination pagination);

    /**
     * 根据状态获取模板列表
     *
     * @param status 状态
     * @return 模板列表
     */
    List<WbsTemplateMasterEntity> getListByStatus(String status);

    /**
     * 获取模板详情（包含WBS明细）
     *
     * @param id 模板ID
     * @return 模板详情
     */
    WbsTemplateVO getDetailInfo(String id);

    /**
     * 获取模板基本信息
     *
     * @param id 模板ID
     * @return 模板信息
     */
    WbsTemplateMasterEntity getInfo(String id);

    /**
     * 创建WBS模板
     *
     * @param templateVO 模板信息（包含WBS明细）
     * @return 模板ID
     */
    String create(WbsTemplateVO templateVO);

    /**
     * 更新WBS模板
     *
     * @param id 模板ID
     * @param templateVO 模板信息（包含WBS明细）
     */
    void update(String id, WbsTemplateVO templateVO);

    /**
     * 删除WBS模板
     *
     * @param id 模板ID
     */
    void delete(String id);

    /**
     * 批量删除WBS模板
     *
     * @param ids 模板ID列表
     */
    void batchDelete(List<String> ids);

    /**
     * 更新模板状态
     *
     * @param id 模板ID
     * @param status 状态
     */
    void updateStatus(String id, String status);

    /**
     * 批量更新状态
     *
     * @param ids 模板ID列表
     * @param status 状态
     */
    void batchUpdateStatus(List<String> ids, String status);

    /**
     * 发布模板
     *
     * @param id 模板ID
     */
    void publish(String id);

    /**
     * 归档模板
     *
     * @param id 模板ID
     */
    void archive(String id);

    /**
     * 检查模板名称是否存在
     *
     * @param name 模板名称
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean isExistByName(String name, String excludeId);

    /**
     * 检查模板编码是否存在
     *
     * @param code 模板编码
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean isExistByCode(String code, String excludeId);

    /**
     * 复制WBS模板
     *
     * @param id 源模板ID
     * @param newName 新模板名称
     * @return 新模板ID
     */
    String copy(String id, String newName);

    /**
     * 获取模板选择列表
     *
     * @param keyword 关键字
     * @return 模板列表
     */
    List<WbsTemplateMasterEntity> getSelectList(String keyword);

    /**
     * 生成模板编码
     *
     * @return 模板编码
     */
    String generateCode();

    /**
     * 获取模板使用情况
     *
     * @param id 模板ID
     * @return 使用情况统计
     */
    Map<String, Object> getTemplateUsageInfo(String id);

    /**
     * 从活动库添加活动到WBS模板
     *
     * @param templateId 模板ID
     * @param activityIds 活动库ID列表
     * @param parentId 父级ID
     */
    void addActivitiesFromLibrary(String templateId, List<String> activityIds, String parentId);

    /**
     * 获取WBS模板明细列表
     *
     * @param templateId 模板ID
     * @return WBS明细列表
     */
    List<WbsTemplateDetailEntity> getWbsDetails(String templateId);

    /**
     * 保存WBS明细
     *
     * @param templateId 模板ID
     * @param details WBS明细列表
     */
    void saveWbsDetails(String templateId, List<WbsTemplateDetailEntity> details);

    /**
     * 删除WBS明细
     *
     * @param detailIds 明细ID列表
     */
    void deleteWbsDetails(List<String> detailIds);

    /**
     * 批量发布模板
     *
     * @param ids 模板ID列表
     */
    void batchPublish(List<String> ids);

    /**
     * 批量归档模板
     *
     * @param ids 模板ID列表
     */
    void batchArchive(List<String> ids);

    /**
     * 应用到项目
     *
     * @param templateId 模板ID
     * @param projectIds 项目ID列表
     */
    void applyToProjects(String templateId, List<String> projectIds);
}
