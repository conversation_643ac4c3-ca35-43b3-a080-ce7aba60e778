<template>
  <div class="quick-access-cards">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <a-card
        v-for="card in quickAccessCards"
        :key="card.key"
        :class="['quick-access-card', { active: activeCard === card.key }]"
        hoverable
        @click="selectCard(card)">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div :class="['icon-wrapper', card.color]">
              <component :is="card.icon" class="text-xl" />
            </div>
          </div>
          <div class="ml-4 flex-1">
            <p class="text-sm text-gray-600 mb-1">{{ card.title }}</p>
            <p class="text-2xl font-bold text-gray-900">{{ card.count }}</p>
          </div>
        </div>
        <div class="mt-3 text-xs text-gray-500">
          {{ card.description }}
        </div>
      </a-card>
    </div>

    <!-- 最近访问项目列表 -->
    <a-card v-if="showRecentProjects" title="最近访问的项目" class="recent-projects-card">
      <template #extra>
        <a-button type="text" size="small" @click="refreshRecentProjects">
          <template #icon><ReloadOutlined /></template>
          刷新
        </a-button>
      </template>

      <a-list :loading="recentProjectsLoading" :data-source="recentProjects" size="small">
        <template #renderItem="{ item }">
          <a-list-item class="recent-project-item" @click="selectProject(item)">
            <template #actions>
              <a-tooltip title="进入项目">
                <a-button type="text" size="small" @click.stop="enterProject(item)">
                  <template #icon><RightOutlined /></template>
                </a-button>
              </a-tooltip>
            </template>

            <a-list-item-meta>
              <template #title>
                <div class="flex items-center">
                  <span class="font-medium">{{ item.fullName }}</span>
                  <a-tag :color="getStatusColor(item.status)" size="small" class="ml-2">
                    {{ getStatusText(item.status) }}
                  </a-tag>
                </div>
              </template>
              <template #description>
                <div class="text-xs text-gray-500">
                  <span>{{ item.code }}</span>
                  <span class="mx-2">|</span>
                  <span>经理ID: {{ item.managerId }}</span>
                  <span class="mx-2">|</span>
                  <span>最后访问: {{ formatToDate(item.lastVisitTime, 'MM-DD HH:mm') }}</span>
                </div>
              </template>
            </a-list-item-meta>
          </a-list-item>
        </template>
      </a-list>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, computed } from 'vue';
  import { List, Card, Button, Tooltip, Tag } from 'ant-design-vue';
  import { ClockCircleOutlined, StarOutlined, UserOutlined, TeamOutlined, AppstoreOutlined, ReloadOutlined, RightOutlined } from '@ant-design/icons-vue';
  import { getProjectListByType, getProjectStatistics } from '/@/api/project/projectBase';
  import { to } from '/@/utils/xh';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { formatToDate } from '/@/utils/dateUtil';

  const { createMessage } = useMessage();

  const activeCard = ref('RECENT_VISITED');
  const recentProjects = ref([]);
  const recentProjectsLoading = ref(false);

  const emit = defineEmits(['query-type-change', 'project-select', 'enter-project']);

  const quickAccessCards = ref([
    {
      key: 'RECENT_VISITED',
      title: '最近访问',
      count: 0,
      description: '最近访问的项目',
      icon: ClockCircleOutlined,
      color: 'bg-blue-500',
    },
    {
      key: 'MY_FAVORITE',
      title: '我的收藏',
      count: 0,
      description: '收藏的项目',
      icon: StarOutlined,
      color: 'bg-yellow-500',
    },
    {
      key: 'MY_MANAGED',
      title: '我管理的',
      count: 0,
      description: '我担任项目经理的项目',
      icon: UserOutlined,
      color: 'bg-green-500',
    },
    {
      key: 'MY_PARTICIPATED',
      title: '我参与的',
      count: 0,
      description: '我参与的项目',
      icon: TeamOutlined,
      color: 'bg-purple-500',
    },
  ]);

  const showRecentProjects = computed(() => {
    return activeCard.value === 'RECENT_VISITED' && recentProjects.value.length > 0;
  });

  onMounted(() => {
    loadStatistics();
    loadRecentProjects();
  });

  async function loadStatistics() {
    try {
      const [err, res] = await to(getProjectStatistics({}));
      if (!err && res?.data) {
        const statistics = res.data;
        quickAccessCards.value.forEach(card => {
          switch (card.key) {
            case 'RECENT_VISITED':
              card.count = statistics.recentVisitedCount || 0;
              break;
            case 'MY_FAVORITE':
              card.count = statistics.myFavoriteCount || 0;
              break;
            case 'MY_MANAGED':
              card.count = statistics.myManagedCount || 0;
              break;
            case 'MY_PARTICIPATED':
              card.count = statistics.myParticipatedCount || 0;
              break;
          }
        });
      }
    } catch (error) {
      console.warn('加载项目统计数据失败:', error);
    }
  }

  async function loadRecentProjects() {
    if (activeCard.value !== 'RECENT_VISITED') return;

    recentProjectsLoading.value = true;
    try {
      const [err, res] = await to(
        getProjectListByType({
          queryType: 'RECENT_VISITED',
          pageSize: 10,
        }),
      );

      if (!err && res?.data?.list) {
        recentProjects.value = res.data.list;
      }
    } catch (error) {
      console.error('加载最近访问项目失败:', error);
    } finally {
      recentProjectsLoading.value = false;
    }
  }

  async function refreshRecentProjects() {
    await loadRecentProjects();
    createMessage.success('最近访问项目已刷新');
  }

  function selectCard(card) {
    activeCard.value = card.key;
    emit('query-type-change', card.key);

    // 如果选择的是最近访问，加载最近访问的项目列表
    if (card.key === 'RECENT_VISITED') {
      loadRecentProjects();
    }
  }

  function selectProject(project) {
    emit('project-select', project);
  }

  function enterProject(project) {
    emit('enter-project', project);
  }

  function getStatusColor(status: string) {
    const statusMap = {
      PLANNING: 'blue',
      EXECUTING: 'green',
      TRACKING: 'cyan',
      SIGNED: 'purple',
      SUSPENDED: 'orange',
      COMPLETED: 'green',
      CANCELLED: 'red',
    };
    return statusMap[status] || 'default';
  }

  function getStatusText(status: string) {
    const statusMap = {
      PLANNING: '规划中',
      EXECUTING: '执行中',
      TRACKING: '跟踪中',
      SIGNED: '已签约',
      SUSPENDED: '暂停',
      COMPLETED: '已完成',
      CANCELLED: '已取消',
    };
    return statusMap[status] || status;
  }
</script>

<style lang="less" scoped>
  .quick-access-cards {
    .quick-access-card {
      cursor: pointer;
      transition: all 0.3s ease;
      border: 2px solid transparent;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      &.active {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }

      .icon-wrapper {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;

        &.bg-blue-500 {
          background-color: #3b82f6;
        }

        &.bg-yellow-500 {
          background-color: #eab308;
        }

        &.bg-green-500 {
          background-color: #22c55e;
        }

        &.bg-purple-500 {
          background-color: #a855f7;
        }
      }
    }

    .recent-projects-card {
      .recent-project-item {
        cursor: pointer;
        padding: 8px 0;
        border-radius: 6px;
        transition: background-color 0.2s ease;

        &:hover {
          background-color: var(--section-bg-color);
        }
      }
    }
  }
</style>
