# XACE 后端基本编码规范

## 命名约定

### 通用命名规则

* **类名:** 大驼峰命名法 (PascalCase)，如 `SysUserService`, `ProductOrderController`
* **接口名:** 大驼峰命名法 (PascalCase)，可考虑 `Service`/`Mapper` 结尾，如 `SysUserService` 或 `SysUserService` / `SysUserMapper`
* **方法名:** 小驼峰命名法 (camelCase)，如 `getUserById`, `saveProductOrder`
* **变量名:** 小驼峰命名法 (camelCase)，如 `userName`, `orderDetailList`
* **常量名:** 全大写，单词间用下划线分隔 (UPPER_SNAKE_CASE)，如 `MAX_RETRY_COUNT`
* **数据库表/列名:** 全小写，单词间用下划线分隔 (snake_case)，如 `sys_user`, `product_id`
* **数据库字段:** 使用大写下划线命名，如 `USER_NAME`, `ORDER_STATUS`

### 严格命名规则

1. **强制:** 代码中任何命名均不能以下划线或美元符号开始以及结束
2. **强制:** 严禁使用拼音与英文混合的方式命名，更不允许直接使用中文命名
3. **强制:** 类名必须使用 UpperCamelCase 风格，严格遵守驼峰形式
4. **强制:** 方法名、参数名、成员变量、局部变量统一使用 lowerCamelCase 风格，必须遵守驼峰形式
5. **强制:** 常量命名全部大写，单词间用下划线隔开，如 `MAX_STOCK_COUNT`
6. **强制:** Model 中布尔类型的变量，不要以 `is` 开头，数据库字段也是如此
7. **强制:** 包名统一使用小写，点分隔符之间有且仅有一个英语单词
8. **强制:** 实体类必须以 `Entity` 后缀，如 `UserEntity`, `DepartmentEntity`
9. **强制:**  VO 类必须以 `VO` 后缀
10. **强制:** 表单类使用 `Form` 后缀，推荐合并设计，如需分开，创建表单用 `CrForm`，更新表单用 `UpForm`

### 命名含义规范

* 方法名应该明确表示其功能和意图
* 获取单个对象的方法应使用 `get` 前缀，如 `getUser`
* 获取集合或数组的方法应使用复数名词，如 `getUsers`
* 布尔类型的字段或方法应使用 `is`, `has`, `can` 等前缀，如 `isEnabled`, `hasPermission`

## 常量定义

* **强制:** 不允许任何魔法值(未经定义的常量)直接出现在代码中
* 代码中应避免硬编码，将可能变化的值定义为静态常量
* 相同领域的常量应归类到同一个常量类中

```java
// 正确示例：常量定义
public class OrderConstants {
    // 订单状态
    public static final int STATUS_PENDING = 1;
    public static final int STATUS_PAID = 2;
    public static final int STATUS_SHIPPED = 3;
    public static final int STATUS_COMPLETED = 4;

    // 支付方式
    public static final String PAYMENT_ALIPAY = "alipay";
    public static final String PAYMENT_WECHAT = "wechat";
}

// 使用常量
if (order.getStatus() == OrderConstants.STATUS_PAID) {
    // 处理已支付订单
}
```

## 导入规范

### 导入顺序

1. Java 标准库
2. Jakarta EE 库
3. Spring 框架库
4. 第三方库
5. 项目内部包

### 导入规则

* 不使用通配符导入（除非导入超过5个同包类）
* 删除未使用的导入
* 按字母顺序排列
* **强制：** 使用 `jakarta.*` 包，不使用 `javax.*` 包（详见 [README](./README.md) 的Jakarta EE规范说明）

## 代码格式化

* 使用统一的代码格式化配置，建议使用 IntelliJ IDEA 默认格式
* **强制:** 缩进使用4个空格，禁止使用制表符(Tab)
* **强制:** 一行代码不应过长，单行字符数限制不超过120个
* **强制:** 大括号使用约定：
  * 左大括号前不换行，后换行
  * 右大括号前换行，后根据情况换行
* **强制:** 左右小括号和字符之间不允许有空格
* **强制:** 任何二元、三元运算符的左右两边都需要加空格
* **强制:** 方法参数在定义和传入时，多个参数的逗号后必须加空格
* 方法间应有一个空行分隔
* 相关代码应分组放置在一起
* 避免无意义的空行

```java
// 正确示例：格式化规范
public void someMethod(String param1, int param2, boolean flag) {
    // 二元运算符两侧加空格
    int result = param2 * 2 + 10;

    // 条件判断，括号内侧无空格，操作符两侧有空格
    if (param1.length() > 10 && flag) {
        // 做一些处理
        doSomething();
    } else {
        // 其他处理
        doOthers();
    }

    // 调用方法，逗号后加空格
    callAnotherMethod("value", 42, true);
}
```

## 注释规范

### 类注释

使用标准的 Javadoc 类注释，至少包含以下信息：

```java
/**
 * [类功能简要描述]
 *
 * @author： [你的名字或标识]
 * date： [创建或修改日期 YYYY-MM-DD]
 */
```

### 方法注释

重要的公共方法应添加 Javadoc 注释，说明方法用途：

```java
/**
 * [方法功能简述]
 *
 * @param paramName 参数描述
 * @return 返回值描述
 * @throws ExceptionType 异常情况描述
 */
```

### 行内注释

对复杂逻辑添加必要的行内注释：

```java
// 这是一个行内注释示例，说明下面代码的功能
```

## 常用库使用规范

### Lombok

推荐使用的注解：

* `@Data` - 生成所有字段的getter、setter、toString、equals、hashCode
* `@Getter`/`@Setter` - 只需getter或setter时
* `@Builder` - 构建者模式
* `@RequiredArgsConstructor` - 使用final字段的构造器
* `@Slf4j` - 日志字段

### MybatisPlus

* 实体类注解：
  * `@TableName` - 指定表名
  * `@TableId` - 指定主键及生成规则
  * `@TableField` - 指定字段映射

* 查询构建：
  * 优先使用 `LambdaQueryWrapper` 保证类型安全
  * 复杂查询使用 XML 映射文件

### Spring Boot

* 组件注解：
  * `@RestController` - REST控制器
  * `@Service` - 服务组件
  * `@Mapper` - 数据库访问组件（推荐）
  * `@Component` - 通用组件

* 依赖注入：
  * `@Resource` - 推荐使用（Jakarta EE标准）
  * `@Autowired` - Spring标准注解

* 配置相关：
  * `@Configuration` - 配置类
  * `@Value` - 注入配置值
  * `@ConfigurationProperties` - 配置属性绑定

## 异常处理

### 异常处理最佳实践

1. **Controller 层**：不使用 try-catch，依赖全局异常处理器
2. **Service 层**：使用自定义异常类封装业务异常
3. **全局处理**：所有异常由 `@ControllerAdvice` 统一处理和记录
4. **业务判断**：使用 `ActionResult.fail()` 返回业务错误信息

### 自定义异常使用

* 使用项目提供的自定义异常类：`DataException`、`LoginException` 等
* 明确区分系统异常和业务异常
* Service 层抛出具体的业务异常，由全局处理器统一处理

```java
// Service 层正确的异常处理
public void updateUser(String id, UserForm form) {
    UserEntity user = this.getById(id);
    if (user == null) {
        throw new DataException("用户不存在");
    }
    // 业务逻辑...
}
```

## XACE 特有规范

### 数据库设计要点

- **字段映射**：详细的实体字段映射规范参见 [实体层规范](./03_ENTITY_LAYER.md)
- **命名约定**：数据库表和列使用小写下划线命名法
- **索引设计**：合理创建索引，避免过度索引影响性能


### API 响应规范

**统一使用 ActionResult 返回：**
```java
@RestController
@RequestMapping("/api/user")
public class UserController {
    
    @GetMapping("/{id}")
    public ActionResult<UserVO> getInfo(@PathVariable String id) {
        UserEntity entity = userService.getById(id);
        UserVO userVO = BeanCopierUtils.copy(entity, UserVO.class);
        return ActionResult.success(userVO);
    }
    
    @GetMapping
    public ActionResult<PageListVO<UserVO>> getList(UserListForm form) {
        return ActionResult.success(userService.getList(form));
    }
}
```

### 分页查询规范

**使用 Pagination 进行分页：**
```java
// Service 层
public PageListVO<UserVO> getList(UserListForm form) {
    QueryWrapper<UserEntity> queryWrapper = new QueryWrapper<>();
    // 构建查询条件...
    
    // 分页查询
    IPage<UserEntity> page = new Page<>(form.getCurrentPage(), form.getPageSize());
    IPage<UserEntity> result = baseMapper.selectPage(page, queryWrapper);
    
    // 转换为 VO
    List<UserVO> list = result.getRecords().stream()
        .map(entity -> BeanCopierUtils.copy(entity, UserVO.class))
        .collect(Collectors.toList());
    
    return PageListVO.create(list, result.getTotal());
}
```

### 权限控制规范

**使用多层权限注解：**
```java
@RestController
public class UserController {
    
    @GetMapping("/list")
    @SaCheckPermission("user.view")  // Sa-Token 权限检查
    @UserPermission                   // 用户权限验证
    @OrganizePermission              // 组织权限验证
    public ActionResult<PageListVO<UserVO>> getList() {
        // 控制器逻辑
    }
}
```

### 多租户支持规范

**自动租户隔离：**
```java
// 实体类继承支持多租户的基类
public class UserEntity extends BaseEntityV2.CUDBaseEntityV2<String> {
    // 会自动包含 tenantId 字段
    // 查询时自动添加租户条件
    // 保存时自动设置租户ID
}

// 需要跨租户查询时使用特殊方法
@Service
public class UserServiceImpl {
    
    public List<UserEntity> getAllUsersIgnoreTenant() {
        // 使用 TenantHolder 临时忽略租户过滤
        return TenantHolder.ignoreTenant(() -> {
            return baseMapper.selectList(null);
        });
    }
}
```

### 数据验证规范

**使用验证注解：**
```java
public class UserCrForm {
    
    @NotBlank(message = "账户不能为空")
    @Schema(description = "账户")
    private String account;
    
    @NotBlank(message = "真实姓名不能为空")
    @Length(max = 50, message = "真实姓名长度不能超过50")
    @Schema(description = "真实姓名")
    private String realName;
    
    @Email(message = "邮箱格式不正确")
    @Schema(description = "邮箱")
    private String email;
}
```

## 其他规范

* 避免编写过长的方法，单个方法建议不超过50行
* 避免过度设计或提前优化
* 保持代码的可测试性
* 遵循单一职责原则
* **强制:** 所有公共方法必须添加 @Schema 注解用于 API 文档生成
* **强制:** 控制器方法必须使用 ActionResult 包装返回值
* **推荐:** 使用 BeanCopierUtils 进行对象拷贝，避免手动赋值
