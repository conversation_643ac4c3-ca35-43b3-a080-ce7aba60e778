package com.xinghuo.project.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.biz.dao.PaymentContractMapper;
import com.xinghuo.project.biz.dao.SupplierMapper;
import com.xinghuo.project.biz.entity.PaymentContractEntity;
import com.xinghuo.project.biz.entity.SupplierEntity;
import com.xinghuo.project.biz.model.supplier.SupplierPagination;
import com.xinghuo.project.biz.service.SupplierService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 供应商服务实现类
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Service
public class SupplierServiceImpl extends BaseServiceImpl<SupplierMapper, SupplierEntity> implements SupplierService {

    @Resource
    private PaymentContractMapper paycontractMapper;

    @Override
    public List<SupplierEntity> getList(SupplierPagination pagination) {
        QueryWrapper<SupplierEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<SupplierEntity> lambda = queryWrapper.lambda();

        // 根据供应商名称模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getName())) {
            lambda.like(SupplierEntity::getName, pagination.getName());
        }

        // 根据联系人模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getLinkman())) {
            lambda.like(SupplierEntity::getLinkman, pagination.getLinkman());
        }

        // 根据联系电话模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getTelephone())) {
            lambda.like(SupplierEntity::getTelephone, pagination.getTelephone());
        }

        // 根据关键字搜索供应商名称或联系人
        String keyword = pagination.getKeyword();
        if (StrXhUtil.isNotEmpty(keyword)) {
            lambda.and(wrapper -> wrapper
                    .like(SupplierEntity::getName, keyword)
                    .or()
                    .like(SupplierEntity::getLinkman, keyword)
            );
        }

        // 排序
        lambda.orderByAsc(SupplierEntity::getSortCode);
        lambda.orderByDesc(SupplierEntity::getCreatedAt);
        
        return processDataType(queryWrapper, pagination);
    }

    @Override
    public SupplierEntity getInfo(String id) {
        return this.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String create(SupplierEntity entity) {
        // 生成供应商ID
        String id = RandomUtil.snowId();
        entity.setId(id);

        // 设置排序码
        if (entity.getSortCode() == null) {
            entity.setSortCode((int) System.currentTimeMillis());
        }

        // 保存供应商
        this.save(entity);

        return id;
    }

    @Override
    public void update(String id, SupplierEntity entity) {
        SupplierEntity oldEntity = this.getById(id);
        if (oldEntity == null) {
            throw new RuntimeException("供应商不存在");
        }

        // 设置ID
        entity.setId(id);

        // 更新供应商
        this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        SupplierEntity entity = this.getById(id);
        if (entity == null) {
            throw new RuntimeException("供应商不存在");
        }

        // 物理删除
        this.removeById(id);
    }

    @Override
    public boolean isExistByName(String name, String excludeId) {
        QueryWrapper<SupplierEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<SupplierEntity> lambda = queryWrapper.lambda();
        lambda.eq(SupplierEntity::getName, name);
        if (StrXhUtil.isNotEmpty(excludeId)) {
            lambda.ne(SupplierEntity::getId, excludeId);
        }
        long count =  this.count(queryWrapper);
        return count > 0;
    }

    @Override
    public List<SupplierEntity> getSelectList() {
        LambdaQueryWrapper<SupplierEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByAsc(SupplierEntity::getSortCode);
        queryWrapper.orderByAsc(SupplierEntity::getName);
        return this.list(queryWrapper);
    }

    @Override
    public boolean canDelete(String id) {
        // 检查是否有关联的采购合同
        LambdaQueryWrapper<PaymentContractEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PaymentContractEntity::getSuppilerId, id);
        // 如果有关联的采购合同，则不能删除
        return paycontractMapper.selectCount(queryWrapper) == 0;
    }
}
