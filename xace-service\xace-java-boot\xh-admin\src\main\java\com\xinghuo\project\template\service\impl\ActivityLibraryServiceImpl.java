package com.xinghuo.project.template.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.template.dao.ActivityLibraryMapper;
import com.xinghuo.project.template.entity.ActivityLibraryEntity;
import com.xinghuo.project.template.model.ActivityLibraryPagination;
import com.xinghuo.project.template.service.ActivityLibraryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 标准项目活动库服务实现类
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Slf4j
@Service
public class ActivityLibraryServiceImpl extends BaseServiceImpl<ActivityLibraryMapper, ActivityLibraryEntity> implements ActivityLibraryService {

    @Override
    public List<ActivityLibraryEntity> getList(ActivityLibraryPagination pagination) {
        QueryWrapper<ActivityLibraryEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<ActivityLibraryEntity> lambda = queryWrapper.lambda();

        // 根据活动编码模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getCode())) {
            lambda.like(ActivityLibraryEntity::getCode, pagination.getCode());
        }

        // 根据活动名称模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getName())) {
            lambda.like(ActivityLibraryEntity::getName, pagination.getName());
        }

        // 根据状态精确查询
        if (pagination.getStatus() != null) {
            lambda.eq(ActivityLibraryEntity::getStatus, pagination.getStatus());
        }

        // 根据里程碑标识查询
        if (pagination.getIsMilestone() != null) {
            lambda.eq(ActivityLibraryEntity::getIsMilestone, pagination.getIsMilestone());
        }

        // 根据活动大类查询
        if (StrXhUtil.isNotEmpty(pagination.getActivityTypeId())) {
            lambda.eq(ActivityLibraryEntity::getActivityTypeId, pagination.getActivityTypeId());
        }

        // 根据活动子类查询
        if (StrXhUtil.isNotEmpty(pagination.getActivitySubTypeId())) {
            lambda.eq(ActivityLibraryEntity::getActivitySubTypeId, pagination.getActivitySubTypeId());
        }

        // 标准工期范围查询
        if (pagination.getDurationMin() != null) {
            lambda.ge(ActivityLibraryEntity::getDuration, pagination.getDurationMin());
        }
        if (pagination.getDurationMax() != null) {
            lambda.le(ActivityLibraryEntity::getDuration, pagination.getDurationMax());
        }

        // 标准工时范围查询
        if (pagination.getStandardHourMin() != null) {
            lambda.ge(ActivityLibraryEntity::getStandardHour, pagination.getStandardHourMin());
        }
        if (pagination.getStandardHourMax() != null) {
            lambda.le(ActivityLibraryEntity::getStandardHour, pagination.getStandardHourMax());
        }

        // 根据责任角色查询
        if (StrXhUtil.isNotEmpty(pagination.getResponseRoleId())) {
            lambda.eq(ActivityLibraryEntity::getResponseRoleId, pagination.getResponseRoleId());
        }

        // 根据完成方式查询
        if (StrXhUtil.isNotEmpty(pagination.getCompleteTypeId())) {
            lambda.eq(ActivityLibraryEntity::getCompleteTypeId, pagination.getCompleteTypeId());
        }

        // 创建时间范围查询
        if (pagination.getCreateTimeStart() != null) {
            lambda.ge(ActivityLibraryEntity::getCreatedAt, pagination.getCreateTimeStart());
        }
        if (pagination.getCreateTimeEnd() != null) {
            lambda.le(ActivityLibraryEntity::getCreatedAt, pagination.getCreateTimeEnd());
        }

        // 根据创建用户查询
        if (StrXhUtil.isNotEmpty(pagination.getCreatedBy())) {
            lambda.eq(ActivityLibraryEntity::getCreatedBy, pagination.getCreatedBy());
        }

        // 根据关键字搜索编码或名称
        String keyword = pagination.getKeyword();
        if (StrXhUtil.isNotEmpty(keyword)) {
            lambda.and(wrapper -> wrapper
                    .like(ActivityLibraryEntity::getCode, keyword)
                    .or()
                    .like(ActivityLibraryEntity::getName, keyword)
            );
        }

        // 排序：按创建时间倒序
        lambda.orderByDesc(ActivityLibraryEntity::getCreatedAt);
        
        return processDataType(queryWrapper, pagination);
    }

    @Override
    public List<ActivityLibraryEntity> getListByStatus(Integer status) {
        if (status == null) {
            return new ArrayList<>();
        }

        QueryWrapper<ActivityLibraryEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ActivityLibraryEntity::getStatus, status)
                .orderByDesc(ActivityLibraryEntity::getCreatedAt);
        
        return list(queryWrapper);
    }

    @Override
    public ActivityLibraryEntity getInfo(String id) {
        if (StrXhUtil.isEmpty(id)) {
            return null;
        }
        return getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String create(ActivityLibraryEntity entity) {
        if (entity == null) {
            throw new RuntimeException("活动库信息不能为空");
        }

        // 验证必填字段
        if (StrXhUtil.isEmpty(entity.getName())) {
            throw new RuntimeException("活动名称不能为空");
        }

        // 检查编码是否重复
        if (StrXhUtil.isNotEmpty(entity.getCode()) && isExistByCode(entity.getCode(), null)) {
            throw new RuntimeException("活动编码已存在");
        }

        // 检查名称是否重复
        if (isExistByName(entity.getName(), null)) {
            throw new RuntimeException("活动名称已存在");
        }

        // 如果没有提供编码，自动生成
        if (StrXhUtil.isEmpty(entity.getCode())) {
            entity.setCode(generateCode());
        }

        // 设置默认状态
        if (entity.getStatus() == null) {
            entity.setStatus(0); // 默认启用
        }

        // 设置默认里程碑标识
        if (entity.getIsMilestone() == null) {
            entity.setIsMilestone(0); // 默认不是里程碑
        }

        save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(String id, ActivityLibraryEntity entity) {
        if (StrXhUtil.isEmpty(id) || entity == null) {
            throw new RuntimeException("参数不能为空");
        }

        ActivityLibraryEntity existEntity = getById(id);
        if (existEntity == null) {
            throw new RuntimeException("活动库不存在");
        }

        // 验证必填字段
        if (StrXhUtil.isEmpty(entity.getName())) {
            throw new RuntimeException("活动名称不能为空");
        }

        // 检查编码是否重复
        if (StrXhUtil.isNotEmpty(entity.getCode()) && isExistByCode(entity.getCode(), id)) {
            throw new RuntimeException("活动编码已存在");
        }

        // 检查名称是否重复
        if (isExistByName(entity.getName(), id)) {
            throw new RuntimeException("活动名称已存在");
        }

        entity.setId(id);
        updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        if (StrXhUtil.isEmpty(id)) {
            throw new RuntimeException("活动库ID不能为空");
        }

        ActivityLibraryEntity entity = getById(id);
        if (entity == null) {
            throw new RuntimeException("活动库不存在");
        }

        // TODO: 检查是否被其他模块引用，如果被引用则不允许删除
        // 例如：检查是否被WBS模板、项目计划等引用

        removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new RuntimeException("活动库ID列表不能为空");
        }

        for (String id : ids) {
            delete(id);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(String id, Integer status) {
        if (StrXhUtil.isEmpty(id) || status == null) {
            throw new RuntimeException("参数不能为空");
        }

        ActivityLibraryEntity entity = getById(id);
        if (entity == null) {
            throw new RuntimeException("活动库不存在");
        }

        UpdateWrapper<ActivityLibraryEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .eq(ActivityLibraryEntity::getId, id)
                .set(ActivityLibraryEntity::getStatus, status);
        
        update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateStatus(List<String> ids, Integer status) {
        if (ids == null || ids.isEmpty() || status == null) {
            throw new RuntimeException("参数不能为空");
        }

        UpdateWrapper<ActivityLibraryEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .in(ActivityLibraryEntity::getId, ids)
                .set(ActivityLibraryEntity::getStatus, status);
        
        update(updateWrapper);
    }

    @Override
    public boolean isExistByCode(String code, String excludeId) {
        if (StrXhUtil.isEmpty(code)) {
            return false;
        }

        QueryWrapper<ActivityLibraryEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ActivityLibraryEntity::getCode, code);
        
        if (StrXhUtil.isNotEmpty(excludeId)) {
            queryWrapper.lambda().ne(ActivityLibraryEntity::getId, excludeId);
        }

        return count(queryWrapper) > 0;
    }

    @Override
    public boolean isExistByName(String name, String excludeId) {
        if (StrXhUtil.isEmpty(name)) {
            return false;
        }

        QueryWrapper<ActivityLibraryEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ActivityLibraryEntity::getName, name);
        
        if (StrXhUtil.isNotEmpty(excludeId)) {
            queryWrapper.lambda().ne(ActivityLibraryEntity::getId, excludeId);
        }

        return count(queryWrapper) > 0;
    }

    @Override
    public ActivityLibraryEntity getByCode(String code) {
        if (StrXhUtil.isEmpty(code)) {
            return null;
        }

        QueryWrapper<ActivityLibraryEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ActivityLibraryEntity::getCode, code);
        
        return getOne(queryWrapper);
    }

    @Override
    public List<ActivityLibraryEntity> getSelectList(String keyword) {
        QueryWrapper<ActivityLibraryEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<ActivityLibraryEntity> lambda = queryWrapper.lambda();

        // 只查询启用状态的记录
        lambda.eq(ActivityLibraryEntity::getStatus, 0);

        // 根据关键字搜索
        if (StrXhUtil.isNotEmpty(keyword)) {
            lambda.and(wrapper -> wrapper
                    .like(ActivityLibraryEntity::getCode, keyword)
                    .or()
                    .like(ActivityLibraryEntity::getName, keyword)
            );
        }

        // 排序：按名称升序
        lambda.orderByAsc(ActivityLibraryEntity::getName);
        
        return list(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enable(String id) {
        updateStatus(id, 0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void disable(String id) {
        updateStatus(id, 1);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String copy(String id, String newName) {
        if (StrXhUtil.isEmpty(id) || StrXhUtil.isEmpty(newName)) {
            throw new RuntimeException("参数不能为空");
        }

        ActivityLibraryEntity sourceEntity = getById(id);
        if (sourceEntity == null) {
            throw new RuntimeException("源活动库不存在");
        }

        // 检查新名称是否重复
        if (isExistByName(newName, null)) {
            throw new RuntimeException("活动名称已存在");
        }

        // 创建新的活动库
        ActivityLibraryEntity newEntity = new ActivityLibraryEntity();
        newEntity.setName(newName);
        newEntity.setCode(generateCode()); // 生成新的编码
        newEntity.setDescription(sourceEntity.getDescription());
        newEntity.setStatus(sourceEntity.getStatus());
        newEntity.setIsMilestone(sourceEntity.getIsMilestone());
        newEntity.setDuration(sourceEntity.getDuration());
        newEntity.setStandardHour(sourceEntity.getStandardHour());
        newEntity.setResponseRoleId(sourceEntity.getResponseRoleId());
        newEntity.setConfirmRoleId(sourceEntity.getConfirmRoleId());
        newEntity.setCompleteTypeId(sourceEntity.getCompleteTypeId());
        newEntity.setApproveSchemaId(sourceEntity.getApproveSchemaId());
        newEntity.setActivityTypeId(sourceEntity.getActivityTypeId());
        newEntity.setActivitySubTypeId(sourceEntity.getActivitySubTypeId());

        save(newEntity);
        return newEntity.getId();
    }

    @Override
    public String generateCode() {
        String prefix = "ACT";
        String randomStr = RandomUtil.enUuId().substring(0, 8).toUpperCase();
        String code = prefix + randomStr;

        // 确保编码不重复
        while (isExistByCode(code, null)) {
            randomStr = RandomUtil.enUuId().substring(0, 8).toUpperCase();
            code = prefix + randomStr;
        }

        return code;
    }

    @Override
    public Map<String, Object> getActivityLibraryUsageInfo(String id) {
        Map<String, Object> result = new HashMap<>();
        
        if (StrXhUtil.isEmpty(id)) {
            result.put("total", 0);
            result.put("details", new ArrayList<>());
            return result;
        }

        // TODO: 实现活动库使用情况统计
        // 统计在以下模块中的使用情况：
        // 1. WBS模板明细表
        // 2. 项目计划
        // 3. 其他相关模块

        result.put("total", 0);
        result.put("details", new ArrayList<>());
        
        return result;
    }

    @Override
    public List<ActivityLibraryEntity> getListByActivityType(String activityTypeId) {
        if (StrXhUtil.isEmpty(activityTypeId)) {
            return new ArrayList<>();
        }

        QueryWrapper<ActivityLibraryEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(ActivityLibraryEntity::getActivityTypeId, activityTypeId)
                .eq(ActivityLibraryEntity::getStatus, 0) // 只查询启用状态
                .orderByAsc(ActivityLibraryEntity::getName);
        
        return list(queryWrapper);
    }

    @Override
    public List<ActivityLibraryEntity> getListByMilestone(Integer isMilestone) {
        if (isMilestone == null) {
            return new ArrayList<>();
        }

        QueryWrapper<ActivityLibraryEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(ActivityLibraryEntity::getIsMilestone, isMilestone)
                .eq(ActivityLibraryEntity::getStatus, 0) // 只查询启用状态
                .orderByAsc(ActivityLibraryEntity::getName);
        
        return list(queryWrapper);
    }
}
