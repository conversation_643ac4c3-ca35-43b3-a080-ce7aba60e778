package com.xinghuo.project.schema.service;

import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.project.schema.entity.ProjectSchemaPhaseEntity;
import com.xinghuo.project.schema.model.ProjectSchemaPhasePagination;

import java.util.List;
import java.util.Map;

/**
 * 项目模板阶段配置服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
public interface ProjectSchemaPhaseService extends BaseService<ProjectSchemaPhaseEntity> {

    /**
     * 获取项目模板阶段配置列表
     *
     * @param pagination 分页参数
     * @return 阶段配置列表
     */
    List<ProjectSchemaPhaseEntity> getList(ProjectSchemaPhasePagination pagination);

    /**
     * 根据项目模板ID获取阶段配置列表
     *
     * @param projectTemplateId 项目模板ID
     * @return 阶段配置列表
     */
    List<ProjectSchemaPhaseEntity> getListByTemplateId(String projectTemplateId);

    /**
     * 获取阶段配置详情
     *
     * @param id 阶段配置ID
     * @return 阶段配置信息
     */
    ProjectSchemaPhaseEntity getInfo(String id);

    /**
     * 创建阶段配置
     *
     * @param entity 阶段配置信息
     * @return 阶段配置ID
     */
    String create(ProjectSchemaPhaseEntity entity);

    /**
     * 更新阶段配置
     *
     * @param id 阶段配置ID
     * @param entity 阶段配置信息
     */
    void update(String id, ProjectSchemaPhaseEntity entity);

    /**
     * 删除阶段配置
     *
     * @param id 阶段配置ID
     */
    void delete(String id);

    /**
     * 批量删除阶段配置
     *
     * @param ids 阶段配置ID列表
     */
    void batchDelete(List<String> ids);

    /**
     * 根据项目模板ID删除所有阶段配置
     *
     * @param projectTemplateId 项目模板ID
     */
    void deleteByTemplateId(String projectTemplateId);

    /**
     * 检查阶段库ID在指定模板中是否已存在
     *
     * @param projectTemplateId 项目模板ID
     * @param phaseLibraryId 阶段库ID
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean isExistByPhaseLibraryId(String projectTemplateId, String phaseLibraryId, String excludeId);

    /**
     * 根据阶段库ID获取阶段配置
     *
     * @param projectTemplateId 项目模板ID
     * @param phaseLibraryId 阶段库ID
     * @return 阶段配置信息
     */
    ProjectSchemaPhaseEntity getByPhaseLibraryId(String projectTemplateId, String phaseLibraryId);

    /**
     * 获取阶段配置选择列表
     *
     * @param projectTemplateId 项目模板ID
     * @param keyword 关键字
     * @return 阶段配置列表
     */
    List<ProjectSchemaPhaseEntity> getSelectList(String projectTemplateId, String keyword);

    /**
     * 复制阶段配置
     *
     * @param sourceTemplateId 源模板ID
     * @param targetTemplateId 目标模板ID
     * @return 复制的配置数量
     */
    int copyPhaseConfigs(String sourceTemplateId, String targetTemplateId);

    /**
     * 批量保存阶段配置
     *
     * @param projectTemplateId 项目模板ID
     * @param phaseConfigs 阶段配置列表
     */
    void batchSave(String projectTemplateId, List<ProjectSchemaPhaseEntity> phaseConfigs);

    /**
     * 更新序号
     *
     * @param id 阶段配置ID
     * @param seqNo 序号
     */
    void updateSeqNo(String id, Integer seqNo);

    /**
     * 批量更新序号
     *
     * @param seqNoMap 序号映射 (ID -> 序号)
     */
    void batchUpdateSeqNo(Map<String, Integer> seqNoMap);

    /**
     * 获取阶段配置使用情况
     *
     * @param id 阶段配置ID
     * @return 使用情况统计
     */
    Map<String, Object> getPhaseConfigUsageInfo(String id);

    /**
     * 获取下一个序号
     *
     * @param projectTemplateId 项目模板ID
     * @return 下一个序号
     */
    Integer getNextSeqNo(String projectTemplateId);

    /**
     * 调整序号（上移/下移）
     *
     * @param id 阶段配置ID
     * @param direction 方向（up/down）
     */
    void adjustSeqNo(String id, String direction);
}
