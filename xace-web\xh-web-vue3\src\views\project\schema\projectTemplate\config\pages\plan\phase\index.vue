<template>
  <div class="phase-manager">
    <a-card title="阶段管理" :bordered="false">
      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleAdd">
            <Icon icon="ant-design:plus-outlined" />
            新增阶段
          </a-button>
          <a-button @click="handleImportFromLibrary">
            <Icon icon="ant-design:import-outlined" />
            从阶段库导入
          </a-button>
        </a-space>
      </template>

      <BasicTable @register="registerTable" :searchInfo="searchInfo">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <TableAction
              :actions="[
                {
                  icon: 'clarity:note-edit-line',
                  label: '编辑',
                  onClick: handleEdit.bind(null, record),
                },
                {
                  icon: 'ant-design:delete-outlined',
                  color: 'error',
                  label: '删除',
                  popConfirm: {
                    title: '是否确认删除',
                    placement: 'left',
                    confirm: handleDelete.bind(null, record),
                  },
                },
              ]" />
          </template>
          <template v-else-if="column.key === 'isMilestone'">
            <a-tag :color="record.isMilestone ? 'success' : 'default'">
              {{ record.isMilestone ? '是' : '否' }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'duration'">
            {{ record.duration ? `${record.duration}天` : '-' }}
          </template>
        </template>
      </BasicTable>
    </a-card>

    <!-- 阶段编辑抽屉 -->
    <BasicDrawer @register="registerDrawer" :title="drawerTitle" width="600px" @ok="handleSubmit">
      <BasicForm @register="registerForm" />
    </BasicDrawer>

    <!-- 从阶段库导入模态框 -->
    <BasicModal @register="registerImportModal" title="从阶段库导入" width="800px" @ok="handleImportSubmit">
      <PhaseLibrarySelector @register="registerLibrarySelector" />
    </BasicModal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed, onMounted } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { BasicDrawer, useDrawer } from '/@/components/Drawer';
  import { BasicModal, useModal } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form';
  import { Icon } from '/@/components/Icon';
  import { useMessage } from '/@/hooks/web/useMessage';
  import PhaseLibrarySelector from './components/PhaseLibrarySelector.vue';
  import {
    getProjectTemplatePhaseConfigs,
    saveProjectTemplatePhaseConfigs,
    deleteProjectTemplatePhaseConfigs,
    importFromPhaseLibrary,
  } from '/@/api/project/projectTemplate';
  import { phaseFormSchema } from '../../../formSchemas';

  const props = defineProps({
    templateId: {
      type: String,
      required: true,
    },
  });

  const emit = defineEmits(['success']);

  const { createMessage } = useMessage();

  // 搜索信息
  const searchInfo = reactive<Recordable>({});

  // 表格列配置
  const columns = [
    {
      title: '阶段名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
    },
    {
      title: '阶段编码',
      dataIndex: 'code',
      key: 'code',
      width: 120,
    },
    {
      title: '预估工期',
      dataIndex: 'duration',
      key: 'duration',
      width: 100,
    },
    {
      title: '里程碑',
      dataIndex: 'isMilestone',
      key: 'isMilestone',
      width: 80,
    },
    {
      title: '排序',
      dataIndex: 'sortOrder',
      key: 'sortOrder',
      width: 80,
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
  ];

  // 表格配置
  const [registerTable, { reload, getDataSource }] = useTable({
    api: loadPhaseData,
    columns,
    useSearchForm: false,
    actionColumn: {
      width: 120,
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      fixed: 'right',
    },
    rowKey: 'id',
    pagination: false,
  });

  // 抽屉配置
  const [registerDrawer, { openDrawer, closeDrawer }] = useDrawer();

  // 表单配置
  const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
    labelWidth: 100,
    schemas: phaseFormSchema,
    showActionButtonGroup: false,
  });

  // 导入模态框配置
  const [registerImportModal, { openModal: openImportModal }] = useModal();
  const [registerLibrarySelector, { getSelectedPhases }] = useComponentRegister();

  // 编辑状态
  const isEdit = ref(false);
  const currentRecord = ref<any>(null);

  const drawerTitle = computed(() => {
    return isEdit.value ? '编辑阶段' : '新增阶段';
  });

  // 加载阶段数据
  async function loadPhaseData() {
    if (!props.templateId) return { list: [] };

    try {
      const response = await getProjectTemplatePhaseConfigs(props.templateId);
      if (response.code === 200) {
        return { list: response.data || [] };
      }
    } catch (error) {
      console.error('加载阶段数据失败:', error);
    }
    return { list: [] };
  }

  // 新增阶段
  function handleAdd() {
    isEdit.value = false;
    currentRecord.value = null;
    resetFields();
    openDrawer(true);
  }

  // 编辑阶段
  function handleEdit(record: any) {
    isEdit.value = true;
    currentRecord.value = record;
    setFieldsValue(record);
    openDrawer(true);
  }

  // 删除阶段
  async function handleDelete(record: any) {
    try {
      const response = await deleteProjectTemplatePhaseConfigs([record.id]);
      if (response.code === 200) {
        createMessage.success('删除成功');
        reload();
        emit('success');
      } else {
        createMessage.error(response.msg || '删除失败');
      }
    } catch (error) {
      console.error('删除失败:', error);
      createMessage.error('删除失败');
    }
  }

  // 提交表单
  async function handleSubmit() {
    try {
      const values = await validate();
      const dataSource = getDataSource();

      let newDataSource;
      if (isEdit.value && currentRecord.value) {
        // 编辑模式
        newDataSource = dataSource.map(item => (item.id === currentRecord.value.id ? { ...item, ...values } : item));
      } else {
        // 新增模式
        const newPhase = {
          id: Date.now().toString(), // 临时ID
          templateId: props.templateId,
          ...values,
        };
        newDataSource = [...dataSource, newPhase];
      }

      const response = await saveProjectTemplatePhaseConfigs(props.templateId, newDataSource);
      if (response.code === 200) {
        createMessage.success(isEdit.value ? '编辑成功' : '新增成功');
        closeDrawer();
        reload();
        emit('success');
      } else {
        createMessage.error(response.msg || '保存失败');
      }
    } catch (error) {
      console.error('保存失败:', error);
      createMessage.error('保存失败');
    }
  }

  // 从阶段库导入
  function handleImportFromLibrary() {
    openImportModal(true);
  }

  // 导入提交
  async function handleImportSubmit() {
    try {
      const selectedPhases = await getSelectedPhases();
      if (!selectedPhases || selectedPhases.length === 0) {
        createMessage.warning('请选择要导入的阶段');
        return;
      }

      const phaseIds = selectedPhases.map(phase => phase.id);
      const response = await importFromPhaseLibrary(props.templateId, phaseIds);

      if (response.code === 200) {
        createMessage.success('导入成功');
        reload();
        emit('success');
      } else {
        createMessage.error(response.msg || '导入失败');
      }
    } catch (error) {
      console.error('导入失败:', error);
      createMessage.error('导入失败');
    }
  }

  // 临时的组件注册器（需要根据实际组件调整）
  function useComponentRegister() {
    const getSelectedPhases = async () => {
      // 这里需要根据实际的PhaseLibrarySelector组件实现
      return [];
    };

    return [() => {}, { getSelectedPhases }];
  }

  onMounted(() => {
    reload();
  });
</script>

<style lang="less" scoped>
  .phase-manager {
    padding: 16px;

    :deep(.ant-card) {
      .ant-card-head {
        border-bottom: 1px solid #f0f0f0;
      }

      .ant-card-body {
        padding: 24px;
      }
    }
  }
</style>
