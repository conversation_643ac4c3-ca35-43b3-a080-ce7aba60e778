import { defHttp } from '/@/utils/http/axios';

enum Api {
  GetList = '/api/project/schema/wbs/getList',
  GetListByTemplateId = '/api/project/schema/wbs/getListByTemplateId',
  GetInfo = '/api/project/schema/wbs/getInfo',
  Create = '/api/project/schema/wbs/create',
  Update = '/api/project/schema/wbs/update',
  Delete = '/api/project/schema/wbs/delete',
  BatchDelete = '/api/project/schema/wbs/batchDelete',
  ImportFromWbsTemplate = '/api/project/schema/wbs/importFromWbsTemplate',
  AddActivitiesFromLibrary = '/api/project/schema/wbs/addActivitiesFromLibrary',
  AdjustSeqNo = '/api/project/schema/wbs/adjustSeqNo',
  RecalculateWbsCode = '/api/project/schema/wbs/recalculateWbsCode',
  RecalculateLevel = '/api/project/schema/wbs/recalculateLevel',
  MoveNode = '/api/project/schema/wbs/moveNode',
  CopyNode = '/api/project/schema/wbs/copyNode',
  GetWbsTree = '/api/project/schema/wbs/getWbsTree',
  GetChildren = '/api/project/schema/wbs/getChildren',
  HasChildren = '/api/project/schema/wbs/hasChildren',
  GetWbsStatistics = '/api/project/schema/wbs/getWbsStatistics',
  ValidateWbsStructure = '/api/project/schema/wbs/validateWbsStructure',
  BatchSave = '/api/project/schema/wbs/batchSave',
  GetWbsPath = '/api/project/schema/wbs/getWbsPath',
  CheckNameExists = '/api/project/schema/wbs/checkNameExists',
  CheckWbsCodeExists = '/api/project/schema/wbs/checkWbsCodeExists',
  GenerateWbsCode = '/api/project/schema/wbs/generateWbsCode',
  UpdatePredecessors = '/api/project/schema/wbs/updatePredecessors',
  GetPredecessors = '/api/project/schema/wbs/getPredecessors',
  CheckCircularDependency = '/api/project/schema/wbs/checkCircularDependency',
  GetNextSeqNo = '/api/project/schema/wbs/getNextSeqNo',
}

/**
 * 项目模板WBS计划接口
 */

// 获取WBS计划列表
export function getProjectSchemaWbsList(params: any) {
  return defHttp.post({
    url: Api.GetList,
    data: params,
  });
}

// 根据项目模板ID获取WBS计划列表
export function getProjectSchemaWbsListByTemplateId(projectTemplateId: string) {
  return defHttp.get({
    url: `${Api.GetListByTemplateId}/${projectTemplateId}`,
  });
}

// 获取WBS计划详情
export function getProjectSchemaWbsInfo(id: string) {
  return defHttp.get({
    url: `${Api.GetInfo}/${id}`,
  });
}

// 创建WBS计划
export function createProjectSchemaWbs(params: any) {
  return defHttp.post({
    url: Api.Create,
    data: params,
  });
}

// 更新WBS计划
export function updateProjectSchemaWbs(id: string, params: any) {
  return defHttp.put({
    url: `${Api.Update}/${id}`,
    data: params,
  });
}

// 删除WBS计划
export function deleteProjectSchemaWbs(id: string) {
  return defHttp.delete({
    url: `${Api.Delete}/${id}`,
  });
}

// 批量删除WBS计划
export function batchDeleteProjectSchemaWbs(ids: string[]) {
  return defHttp.delete({
    url: Api.BatchDelete,
    data: ids,
  });
}

// 从WBS模板导入WBS计划
export function importProjectSchemaWbsFromTemplate(projectTemplateId: string, wbsTemplateId: string, parentId?: string) {
  return defHttp.post({
    url: `${Api.ImportFromWbsTemplate}/${projectTemplateId}`,
    params: { wbsTemplateId, parentId },
  });
}

// 从活动库添加活动到WBS计划
export function addActivitiesFromLibraryToProjectSchemaWbs(projectTemplateId: string, activityIds: string[], parentId?: string) {
  return defHttp.post({
    url: `${Api.AddActivitiesFromLibrary}/${projectTemplateId}`,
    data: activityIds,
    params: { parentId },
  });
}

// 调整WBS计划序号
export function adjustProjectSchemaWbsSeqNo(id: string, direction: 'up' | 'down') {
  return defHttp.put({
    url: `${Api.AdjustSeqNo}/${id}`,
    params: { direction },
  });
}

// 重新计算WBS编码
export function recalculateProjectSchemaWbsCode(projectTemplateId: string) {
  return defHttp.put({
    url: `${Api.RecalculateWbsCode}/${projectTemplateId}`,
  });
}

// 重新计算层级深度
export function recalculateProjectSchemaWbsLevel(projectTemplateId: string) {
  return defHttp.put({
    url: `${Api.RecalculateLevel}/${projectTemplateId}`,
  });
}

// 移动WBS节点
export function moveProjectSchemaWbsNode(id: string, newParentId?: string, newSeqNo?: number) {
  return defHttp.put({
    url: `${Api.MoveNode}/${id}`,
    params: { newParentId, newSeqNo },
  });
}

// 复制WBS节点
export function copyProjectSchemaWbsNode(id: string, newParentId?: string) {
  return defHttp.post({
    url: `${Api.CopyNode}/${id}`,
    params: { newParentId },
  });
}

// 获取WBS树形结构
export function getProjectSchemaWbsTree(projectTemplateId: string) {
  return defHttp.get({
    url: `${Api.GetWbsTree}/${projectTemplateId}`,
  });
}

// 获取子节点列表
export function getProjectSchemaWbsChildren(parentId: string) {
  return defHttp.get({
    url: `${Api.GetChildren}/${parentId}`,
  });
}

// 检查是否存在子节点
export function hasProjectSchemaWbsChildren(parentId: string) {
  return defHttp.get({
    url: `${Api.HasChildren}/${parentId}`,
  });
}

// 获取WBS统计信息
export function getProjectSchemaWbsStatistics(projectTemplateId: string) {
  return defHttp.get({
    url: `${Api.GetWbsStatistics}/${projectTemplateId}`,
  });
}

// 验证WBS结构完整性
export function validateProjectSchemaWbsStructure(projectTemplateId: string) {
  return defHttp.get({
    url: `${Api.ValidateWbsStructure}/${projectTemplateId}`,
  });
}

// 批量保存WBS计划
export function batchSaveProjectSchemaWbs(projectTemplateId: string, wbsList: any[]) {
  return defHttp.post({
    url: `${Api.BatchSave}/${projectTemplateId}`,
    data: wbsList,
  });
}

// 获取WBS路径
export function getProjectSchemaWbsPath(id: string) {
  return defHttp.get({
    url: `${Api.GetWbsPath}/${id}`,
  });
}

// 检查WBS名称是否存在
export function checkProjectSchemaWbsNameExists(projectTemplateId: string, name: string, excludeId?: string) {
  return defHttp.get({
    url: Api.CheckNameExists,
    params: { projectTemplateId, name, excludeId },
  });
}

// 检查WBS编码是否存在
export function checkProjectSchemaWbsCodeExists(projectTemplateId: string, wbsCode: string, excludeId?: string) {
  return defHttp.get({
    url: Api.CheckWbsCodeExists,
    params: { projectTemplateId, wbsCode, excludeId },
  });
}

// 生成WBS编码
export function generateProjectSchemaWbsCode(projectTemplateId: string, parentId?: string) {
  return defHttp.get({
    url: Api.GenerateWbsCode,
    params: { projectTemplateId, parentId },
  });
}

// 更新前置任务关系
export function updateProjectSchemaWbsPredecessors(id: string, predecessors: string) {
  return defHttp.put({
    url: `${Api.UpdatePredecessors}/${id}`,
    params: { predecessors },
  });
}

// 获取前置任务列表
export function getProjectSchemaWbsPredecessors(id: string) {
  return defHttp.get({
    url: `${Api.GetPredecessors}/${id}`,
  });
}

// 检查循环依赖
export function checkProjectSchemaWbsCircularDependency(id: string, predecessorId: string) {
  return defHttp.get({
    url: Api.CheckCircularDependency,
    params: { id, predecessorId },
  });
}

// 获取下一个序号
export function getProjectSchemaWbsNextSeqNo(projectTemplateId: string, parentId?: string) {
  return defHttp.get({
    url: Api.GetNextSeqNo,
    params: { projectTemplateId, parentId },
  });
}
