<template>
  <div class="xh-content-wrapper">
    <div class="xh-content-wrapper-center">
      <div class="xh-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="handleCreate">新增供应商</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'action'">
              <TableAction
                :actions="[
                  {
                    icon: 'clarity:info-standard-line',
                    label: '查看',
                    onClick: handleView.bind(null, record),
                  },
                  {
                    icon: 'clarity:note-edit-line',
                    label: '编辑',
                    onClick: handleEdit.bind(null, record),
                  },
                  {
                    icon: 'ant-design:delete-outlined',
                    color: 'error',
                    label: '删除',
                    popConfirm: {
                      title: '是否确认删除',
                      confirm: handleDelete.bind(null, record),
                    },
                  },
                ]" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <FormDrawer @register="registerFormDrawer" @reload="reload" />
    <DetailDrawer @register="registerDetailDrawer" />
  </div>
</template>

<script lang="ts" setup>
  import { defineComponent, h } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { getSupplierList, deleteSupplier, SupplierModel } from '/@/api/project/supplier';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useDrawer } from '/@/components/Drawer';
  import { formatToDateTime } from '/@/utils/dateUtil';
  import FormDrawer from './FormDrawer.vue';
  import DetailDrawer from './DetailDrawer.vue';

  defineComponent({ name: 'project-biz-supplier' });

  const { createMessage } = useMessage();
  const [registerFormDrawer, { openDrawer: openFormDrawer }] = useDrawer();
  const [registerDetailDrawer, { openDrawer: openDetailDrawer }] = useDrawer();

  // 表格列定义
  const columns = [
    {
      title: '供应商名称',
      dataIndex: 'name',
      width: 200,
    },
    {
      title: '联系人',
      dataIndex: 'linkman',
      width: 120,
    },
    {
      title: '联系电话',
      dataIndex: 'telephone',
      width: 150,
    },

    {
      title: '排序码',
      dataIndex: 'sortCode',
      width: 100,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      width: 200,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 150,
      customRender: ({ record }) => {
        return formatToDateTime(record.createdAt);
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 150,
      fixed: 'right' as const,
    },
  ];

  // 搜索表单配置
  const searchFormSchema = [
    {
      field: 'name',
      label: '供应商名称',
      component: 'Input',
      colProps: { span: 4 },
    },
    {
      field: 'linkman',
      label: '联系人',
      component: 'Input',
      colProps: { span: 4 },
    },
    {
      field: 'telephone',
      label: '联系电话',
      component: 'Input',
      colProps: { span: 4 },
    },
  ];

  // 注册表格
  const [registerTable, { reload }] = useTable({
    title: '供应商列表',
    api: getSupplierList,
    columns,
    formConfig: {
      labelWidth: 80,
      schemas: searchFormSchema,
    },
    useSearchForm: true,
    showTableSetting: true,
    bordered: true,
    showIndexColumn: true,
    canResize: true,
    beforeFetch: params => {
      console.log('表格请求参数:', params);
      return params;
    },
    afterFetch: data => {
      console.log('表格返回数据:', data);
      return data;
    },
  });

  // 新增供应商
  function handleCreate() {
    openFormDrawer(true, {
      isUpdate: false,
    });
  }

  // 编辑供应商
  function handleEdit(record: SupplierModel) {
    openFormDrawer(true, {
      record,
      isUpdate: true,
    });
  }

  // 查看供应商详情
  function handleView(record: SupplierModel) {
    openDetailDrawer(true, {
      id: record.id,
    });
  }

  // 删除供应商
  async function handleDelete(record: SupplierModel) {
    try {
      await deleteSupplier(record.id);
      createMessage.success('删除成功');
      reload();
    } catch (error: any) {
      if (error.message && error.message.includes('已关联采购合同')) {
        createMessage.error('该供应商已关联采购合同，无法删除');
      } else {
        createMessage.error('删除失败');
      }
    }
  }
</script>
