interface TreeHelperConfig {
  id: string;
  children: string;
  pid: string;
  type?: string;
}

// 默认配置
const DEFAULT_CONFIG: TreeHelperConfig = {
  id: 'id',
  children: 'children',
  pid: 'parentId',
};

// 获取配置。  Object.assign 从一个或多个源对象复制到目标对象
const getConfig = (config: Partial<TreeHelperConfig>) => Object.assign({}, DEFAULT_CONFIG, config);

// tree from list
// 列表中的树
export function listToTree<T = any>(list: any[], config: Partial<TreeHelperConfig> = {}): T[] {
  const conf = getConfig(config) as TreeHelperConfig;
  const nodeMap = new Map();
  const result: T[] = [];
  const { id, children, pid } = conf;

  for (const node of list) {
    node[children] = node[children] || [];
    nodeMap.set(node[id], node);
  }
  for (const node of list) {
    const parent = nodeMap.get(node[pid]);
    (parent ? parent[children] : result).push(node);
  }
  return result;
}

export function treeToList<T = any>(tree: any, config: Partial<TreeHelperConfig> = {}): T {
  config = getConfig(config);
  const { children, type = '' } = config;
  const result: any = [...tree];
  for (let i = 0; i < result.length; i++) {
    if (!result[i][children!]) continue;
    result.splice(i + 1, 0, ...result[i][children!]);
  }
  if (!type) return result;
  return result.filter(o => o.type == type);
}

export function findNode<T = any>(tree: any, func: Fn, config: Partial<TreeHelperConfig> = {}): T | null {
  config = getConfig(config);
  const { children } = config;
  const list = [...tree];
  for (const node of list) {
    if (func(node)) return node;
    node[children!] && list.push(...node[children!]);
  }
  return null;
}

export function findNodeAll<T = any>(tree: any, func: Fn, config: Partial<TreeHelperConfig> = {}): T[] {
  config = getConfig(config);
  const { children } = config;
  const list = [...tree];
  const result: T[] = [];
  for (const node of list) {
    func(node) && result.push(node);
    node[children!] && list.push(...node[children!]);
  }
  return result;
}

export function findPath<T = any>(tree: any, func: Fn, config: Partial<TreeHelperConfig> = {}): T | T[] | null {
  config = getConfig(config);
  const path: T[] = [];
  const list = [...tree];
  const visitedSet = new Set();
  const { children } = config;
  while (list.length) {
    const node = list[0];
    if (visitedSet.has(node)) {
      path.pop();
      list.shift();
    } else {
      visitedSet.add(node);
      node[children!] && list.unshift(...node[children!]);
      path.push(node);
      if (func(node)) {
        return path;
      }
    }
  }
  return null;
}

export function findPathAll(tree: any, func: Fn, config: Partial<TreeHelperConfig> = {}) {
  config = getConfig(config);
  const path: any[] = [];
  const list = [...tree];
  const result: any[] = [];
  const visitedSet = new Set(),
    { children } = config;
  while (list.length) {
    const node = list[0];
    if (visitedSet.has(node)) {
      path.pop();
      list.shift();
    } else {
      visitedSet.add(node);
      node[children!] && list.unshift(...node[children!]);
      path.push(node);
      func(node) && result.push([...path]);
    }
  }
  return result;
}

export function filter<T = any>(
  tree: T[],
  func: (n: T) => boolean,
  // Partial 将 T 中的所有属性设为可选
  config: Partial<TreeHelperConfig> = {},
): T[] {
  // 获取配置
  config = getConfig(config);
  const children = config.children as string;

  function listFilter(list: T[]) {
    return list
      .map((node: any) => ({ ...node }))
      .filter(node => {
        // 递归调用 对含有children项  进行再次调用自身函数 listFilter
        node[children] = node[children] && listFilter(node[children]);
        // 执行传入的回调 func 进行过滤
        return func(node) || (node[children] && node[children].length);
      });
  }

  return listFilter(tree);
}

export function forEach<T = any>(tree: T[], func: (n: T) => any, config: Partial<TreeHelperConfig> = {}): void {
  config = getConfig(config);
  const list: any[] = [...tree];
  const { children } = config;
  for (let i = 0; i < list.length; i++) {
    //func 返回true就终止遍历，避免大量节点场景下无意义循环，引起浏览器卡顿
    if (func(list[i])) {
      return;
    }
    children && list[i][children] && list.splice(i + 1, 0, ...list[i][children]);
  }
}

/**
 * @description: Extract tree specified structure
 * @description: 提取树指定结构
 */
export function treeMap<T = any>(treeData: T[], opt: { children?: string; conversion: Fn }): T[] {
  return treeData.map(item => treeMapEach(item, opt));
}

/**
 * @description: Extract tree specified structure
 * @description: 提取树指定结构
 */
export function treeMapEach(data: any, { children = 'children', conversion }: { children?: string; conversion: Fn }) {
  const haveChildren = Array.isArray(data[children]) && data[children].length > 0;
  const conversionData = conversion(data) || {};
  if (haveChildren) {
    return {
      ...conversionData,
      [children]: data[children].map((i: number) =>
        treeMapEach(i, {
          children,
          conversion,
        }),
      ),
    };
  } else {
    return {
      ...conversionData,
    };
  }
}

/**
 * 递归遍历树结构
 * @param treeDatas 树
 * @param callBack 回调
 * @param parentNode 父节点
 */
export function eachTree(treeDatas: any[], callBack: Fn, parentNode = {}) {
  treeDatas.forEach(element => {
    const newNode = callBack(element, parentNode) || element;
    if (element.children) {
      eachTree(element.children, callBack, newNode);
    }
  });
}

export function getNodeAndDescendantIds(nodes, targetId) {
  const childrenMap = new Map();
  nodes.forEach(node => {
    const parentId = node.parentId ?? '';
    if (!childrenMap.has(parentId)) {
      childrenMap.set(parentId, []);
    }
    childrenMap.get(parentId).push(node.id);
  });

  const resultIds = new Set();
  const stack = [targetId];

  while (stack.length > 0) {
    const currentId = stack.pop();
    resultIds.add(currentId);

    const children = childrenMap.get(currentId) || [];
    children.forEach(childId => {
      if (!resultIds.has(childId)) {
        stack.push(childId);
      }
    });
  }

  return Array.from(resultIds);
}
// filterTreeByLevel

/**
 * 从扁平化的节点列表中，筛选出所有层级小于等于maxLevel的节点，并返回一个新的扁平列表。
 *
 * @param {Array<Object>} flatData - 包含所有节点的扁平数组。
 * @param {number} maxLevel - 要包含的最大层级深度。
 * @returns {Array<Object>} - 一个新的扁平数组，只包含符合层级条件的节点。
 */
export function filterTreeByLevel(flatData, maxLevel) {
  if (maxLevel < 0) return [];

  // 使用Map来缓存已计算过的节点的层级，避免重复计算。
  const levelCache = new Map();
  // 使用Map来快速查找节点。
  const nodeMap = new Map(flatData.map(node => [node.id, node]));

  // 递归函数，用于计算任何一个节点的层级。
  const getNodeLevel = nodeId => {
    // 如果层级已在缓存中，直接返回。
    if (levelCache.has(nodeId)) {
      return levelCache.get(nodeId);
    }

    const node = nodeMap.get(nodeId);
    if (!node) {
      // 如果节点找不到（数据有问题），可以返回一个大数或-1
      return Infinity;
    }

    // 根节点的层级是0。
    if (node.parentId === null) {
      levelCache.set(nodeId, 0);
      return 0;
    }

    // 递归计算父节点的层级，然后加1。
    const level = getNodeLevel(node.parentId) + 1;
    levelCache.set(nodeId, level);
    return level;
  };

  const result = [];
  for (const node of flatData) {
    const level = getNodeLevel(node.id);
    if (level <= maxLevel) {
      // 创建一个副本并添加计算出的level属性，然后加入结果集
      result.push({ ...node, calculatedLevel: level });
    }
  }

  return result;
}

/**
 * 根据扁平化的树形数组，重置每个节点的层级(level)和同级顺序(seqNo)。
 * level 和 seqNo 都从 0 开始。
 *
 * @param {Array<Object>} nodes - 包含id和parentId的扁平化节点数组。
 * @returns {Array<Object>} - 更新了 level 和 seqNo 后的节点数组。
 */
export function resetTreeLevelAndSeq(nodes) {
  // 处理空数组或无效输入
  if (!nodes || nodes.length === 0) {
    return [];
  }

  const parentToChildrenMap = new Map();
  const roots = [];

  // 1. 遍历数组，构建父子关系图并找出根节点
  // 这样做可以保留同级节点在原数组中的相对顺序
  nodes.forEach(node => {
    const parentId = node.parentId;

    if (parentId === null || parentId === undefined) {
      roots.push(node);
    } else {
      if (!parentToChildrenMap.has(parentId)) {
        parentToChildrenMap.set(parentId, []);
      }
      parentToChildrenMap.get(parentId).push(node);
    }
  });

  // 2. 定义一个递归函数（深度优先遍历）来更新 level 和 seqNo
  const traverse = (nodeList, currentLevel) => {
    if (!nodeList || nodeList.length === 0) {
      return;
    }

    // 遍历当前层级的节点
    // 节点的 index 就是它在同级中的顺序 (seqNo)
    nodeList.forEach((node, index) => {
      // 重置 level 和 seqNo
      node.level = currentLevel;
      node.seqNo = index;

      // 递归处理其子节点
      const children = parentToChildrenMap.get(node.id);
      if (children && children.length > 0) {
        // 子节点的层级是当前层级 + 1
        traverse(children, currentLevel + 1);
      }
    });
  };

  // 3. 从根节点开始遍历，根节点的 level 为 0
  traverse(roots, 0);

  // 函数直接修改了输入数组中的对象引用，所以返回原数组即可
  return nodes;
}
