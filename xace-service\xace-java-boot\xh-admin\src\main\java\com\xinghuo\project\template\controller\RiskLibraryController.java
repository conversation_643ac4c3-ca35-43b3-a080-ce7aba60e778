package com.xinghuo.project.template.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.project.template.entity.RiskLibraryEntity;
import com.xinghuo.project.template.model.RiskLibraryPagination;
import com.xinghuo.project.template.model.vo.RiskLibraryVO;
import com.xinghuo.project.template.model.vo.RiskLibrarySelectVO;
import com.xinghuo.project.template.service.RiskLibraryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 标准项目风险库管理控制器
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Slf4j
@Tag(name = "标准项目风险库管理", description = "标准项目风险库管理相关接口")
@RestController
@RequestMapping("/api/project/template/riskLibrary")
public class RiskLibraryController {

    @Resource
    private RiskLibraryService riskLibraryService;

    /**
     * 获取风险库列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取风险库列表")
    public ActionResult<PageListVO<RiskLibraryVO>> list(@RequestBody RiskLibraryPagination pagination) {
        List<RiskLibraryEntity> list = riskLibraryService.getList(pagination);
        List<RiskLibraryVO> listVO = BeanCopierUtils.copyList(list, RiskLibraryVO.class);

        // 对结果进行数据转换和补充
        for (RiskLibraryVO vo : listVO) {
            // 状态名称转换
            if (vo.getStatus() != null) {
                switch (vo.getStatus()) {
                    case "draft":
                        vo.setStatusName("草稿");
                        break;
                    case "published":
                        vo.setStatusName("已发布");
                        break;
                    case "archived":
                        vo.setStatusName("归档");
                        break;
                    default:
                        vo.setStatusName(vo.getStatus());
                }
            }

            // TODO: 可以在这里添加其他关联数据的查询和设置
            // 例如：创建用户名称、风险类别名称、等级名称等
        }

        PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
        return ActionResult.page(listVO, page);
    }

    /**
     * 根据状态获取风险库列表
     */
    @GetMapping("/getListByStatus/{status}")
    @Operation(summary = "根据状态获取风险库列表")
    public ActionResult<List<RiskLibraryEntity>> getListByStatus(
            @Parameter(description = "状态") @PathVariable String status) {
        List<RiskLibraryEntity> list = riskLibraryService.getListByStatus(status);
        return ActionResult.success(list);
    }

    /**
     * 根据风险类别获取风险库列表
     */
    @GetMapping("/getListByRiskCategory/{riskCategoryId}")
    @Operation(summary = "根据风险类别获取风险库列表")
    public ActionResult<List<RiskLibraryEntity>> getListByRiskCategory(
            @Parameter(description = "风险类别ID") @PathVariable String riskCategoryId) {
        List<RiskLibraryEntity> list = riskLibraryService.getListByRiskCategory(riskCategoryId);
        return ActionResult.success(list);
    }

    /**
     * 根据概率等级获取风险库列表
     */
    @GetMapping("/getListByProbabilityLevel/{probabilityLevelId}")
    @Operation(summary = "根据概率等级获取风险库列表")
    public ActionResult<List<RiskLibraryEntity>> getListByProbabilityLevel(
            @Parameter(description = "概率等级ID") @PathVariable String probabilityLevelId) {
        List<RiskLibraryEntity> list = riskLibraryService.getListByProbabilityLevel(probabilityLevelId);
        return ActionResult.success(list);
    }

    /**
     * 根据影响等级获取风险库列表
     */
    @GetMapping("/getListByImpactLevel/{impactLevelId}")
    @Operation(summary = "根据影响等级获取风险库列表")
    public ActionResult<List<RiskLibraryEntity>> getListByImpactLevel(
            @Parameter(description = "影响等级ID") @PathVariable String impactLevelId) {
        List<RiskLibraryEntity> list = riskLibraryService.getListByImpactLevel(impactLevelId);
        return ActionResult.success(list);
    }

    /**
     * 获取风险库详情
     */
    @GetMapping("/getInfo/{id}")
    @Operation(summary = "获取风险库详情")
    public ActionResult<RiskLibraryEntity> getInfo(
            @Parameter(description = "风险库ID") @PathVariable String id) {
        RiskLibraryEntity entity = riskLibraryService.getInfo(id);
        if (entity == null) {
            return ActionResult.fail("风险库不存在");
        }
        return ActionResult.success(entity);
    }

    /**
     * 根据编码获取风险库
     */
    @GetMapping("/getByCode/{code}")
    @Operation(summary = "根据编码获取风险库")
    public ActionResult<RiskLibraryEntity> getByCode(
            @Parameter(description = "风险编码") @PathVariable String code) {
        RiskLibraryEntity entity = riskLibraryService.getByCode(code);
        if (entity == null) {
            return ActionResult.fail("风险库不存在");
        }
        return ActionResult.success(entity);
    }

    /**
     * 创建风险库
     */
    @PostMapping("/create")
    @Operation(summary = "创建风险库")
    public ActionResult<String> create(@RequestBody @Valid RiskLibraryEntity entity) {
        String id = riskLibraryService.create(entity);
        return ActionResult.success("创建成功", id);
    }

    /**
     * 更新风险库
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新风险库")
    public ActionResult<String> update(
            @Parameter(description = "风险库ID") @PathVariable String id,
            @RequestBody @Valid RiskLibraryEntity entity) {
        riskLibraryService.update(id, entity);
        return ActionResult.success("更新成功");
    }

    /**
     * 删除风险库
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除风险库")
    public ActionResult<String> delete(
            @Parameter(description = "风险库ID") @PathVariable String id) {
        riskLibraryService.delete(id);
        return ActionResult.success("删除成功");
    }

    /**
     * 批量删除风险库
     */
    @DeleteMapping("/batchDelete")
    @Operation(summary = "批量删除风险库")
    public ActionResult<String> batchDelete(@RequestBody List<String> ids) {
        riskLibraryService.batchDelete(ids);
        return ActionResult.success("批量删除成功");
    }

    /**
     * 更新风险库状态
     */
    @PutMapping("/{id}/status")
    @Operation(summary = "更新风险库状态")
    public ActionResult<String> updateStatus(
            @Parameter(description = "风险库ID") @PathVariable String id,
            @RequestParam String status) {
        riskLibraryService.updateStatus(id, status);
        return ActionResult.success("状态更新成功");
    }

    /**
     * 批量更新状态
     */
    @PutMapping("/batchUpdateStatus")
    @Operation(summary = "批量更新状态")
    public ActionResult<String> batchUpdateStatus(
            @RequestBody List<String> ids,
            @RequestParam String status) {
        riskLibraryService.batchUpdateStatus(ids, status);
        return ActionResult.success("批量更新状态成功");
    }

    /**
     * 发布风险库
     */
    @PutMapping("/publish/{id}")
    @Operation(summary = "发布风险库")
    public ActionResult<String> publish(
            @Parameter(description = "风险库ID") @PathVariable String id) {
        riskLibraryService.publish(id);
        return ActionResult.success("发布成功");
    }

    /**
     * 归档风险库
     */
    @PutMapping("/archive/{id}")
    @Operation(summary = "归档风险库")
    public ActionResult<String> archive(
            @Parameter(description = "风险库ID") @PathVariable String id) {
        riskLibraryService.archive(id);
        return ActionResult.success("归档成功");
    }

    /**
     * 批量发布风险库
     */
    @PutMapping("/batchPublish")
    @Operation(summary = "批量发布风险库")
    public ActionResult<String> batchPublish(@RequestBody List<String> ids) {
        riskLibraryService.batchPublish(ids);
        return ActionResult.success("批量发布成功");
    }

    /**
     * 批量归档风险库
     */
    @PutMapping("/batchArchive")
    @Operation(summary = "批量归档风险库")
    public ActionResult<String> batchArchive(@RequestBody List<String> ids) {
        riskLibraryService.batchArchive(ids);
        return ActionResult.success("批量归档成功");
    }

    /**
     * 复制风险库
     */
    @PostMapping("/copy/{id}")
    @Operation(summary = "复制风险库")
    public ActionResult<String> copy(
            @Parameter(description = "风险库ID") @PathVariable String id,
            @RequestParam String newTitle) {
        String newId = riskLibraryService.copy(id, newTitle);
        return ActionResult.success("复制成功", newId);
    }

    /**
     * 检查风险编码是否存在
     */
    @GetMapping("/checkCodeExists")
    @Operation(summary = "检查风险编码是否存在")
    public ActionResult<Boolean> checkCodeExists(
            @RequestParam String code,
            @RequestParam(required = false) String excludeId) {
        boolean exists = riskLibraryService.isExistByCode(code, excludeId);
        return ActionResult.success(exists);
    }

    /**
     * 检查风险标题是否存在
     */
    @GetMapping("/checkTitleExists")
    @Operation(summary = "检查风险标题是否存在")
    public ActionResult<Boolean> checkTitleExists(
            @RequestParam String title,
            @RequestParam(required = false) String excludeId) {
        boolean exists = riskLibraryService.isExistByTitle(title, excludeId);
        return ActionResult.success(exists);
    }

    /**
     * 获取风险库选择列表
     */
    @GetMapping("/getSelectList")
    @Operation(summary = "获取风险库选择列表")
    public ActionResult<List<RiskLibrarySelectVO>> getSelectList(
            @RequestParam(required = false) String keyword) {
        List<RiskLibraryEntity> list = riskLibraryService.getSelectList(keyword);
        List<RiskLibrarySelectVO> listVO = BeanCopierUtils.copyList(list, RiskLibrarySelectVO.class);

        // 构建fullName字段，格式：[编码] 标题
        for (RiskLibrarySelectVO vo : listVO) {
            if (vo.getCode() != null && vo.getTitle() != null) {
                vo.setFullName("[" + vo.getCode() + "] " + vo.getTitle());
            } else if (vo.getTitle() != null) {
                vo.setFullName(vo.getTitle());
            } else {
                vo.setFullName(vo.getId());
            }
        }

        return ActionResult.success(listVO);
    }

    /**
     * 生成风险编码
     */
    @GetMapping("/generateCode")
    @Operation(summary = "生成风险编码")
    public ActionResult<String> generateCode() {
        String code = riskLibraryService.generateCode();
        return ActionResult.success(code);
    }

    /**
     * 获取风险库使用情况
     */
    @GetMapping("/getRiskLibraryUsageInfo/{id}")
    @Operation(summary = "获取风险库使用情况")
    public ActionResult<Map<String, Object>> getRiskLibraryUsageInfo(
            @Parameter(description = "风险库ID") @PathVariable String id) {
        Map<String, Object> usageInfo = riskLibraryService.getRiskLibraryUsageInfo(id);
        return ActionResult.success(usageInfo);
    }
}
