# XACE 前端开发规范

基于 Vue 3 + TypeScript + Ant Design Vue 的企业级前端开发规范。

## 🚨 核心要点

### 必须遵循
- **ActionResult处理**：检查 `response.code === 200` 再使用 `response.data`
- **组件数据格式**：使用 `{id, fullName}` 不是 `{value, label}`
- **Composition API**：优先使用 Composition API 而不是 Options API
- **TypeScript**：严格的类型定义，避免使用 `any`

## 📋 规范文档

按照开发层次组织的前端规范文档：

### 1. [架构规范](./01_ARCHITECTURE.md)
- 技术栈选择：Vue 3, TypeScript, Ant Design Vue
- 项目结构：目录组织和文件命名规范
- 开发环境：工具配置和最佳实践

### 2. [Vue 开发标准](./02_VUE_STANDARDS.md)
- Composition API：标准使用模式和最佳实践
- 组件设计：Props、Emits、生命周期规范
- 性能优化：响应式数据和组件优化策略

### 3. [XACE 组件库](./03_XACE_COMPONENTS.md)
- Xh 系列组件：业务组件使用规范和数据格式约定
- Basic 组件：表格、表单、弹窗等基础组件用法
- 按需导入：Ant Design Vue 组件导入策略

### 4. [API 响应与样式](./04_API_STYLES.md)
- ActionResult：统一的API响应格式处理规范
- CSS/LESS：样式编写规范和主题管理
- 响应式设计：移动端适配和断点管理

### 5. [工具函数库](./05_UTILS_TOOLS.md)
- 日期工具：formatToDate、formatToDateTime 使用指南
- 常用工具：字符串、数组、对象处理函数
- 表单验证：常用验证规则和自定义验证器

### 开发工具
- [AI提示词模板](../../../ai-assistants/frontend/08_PROMPTS_TEMPLATES.md) - 前端开发辅助提示词

## 🔧 开发命令

### 基础命令
```bash
# 进入前端目录
cd xace-web/xh-web-vue3

# 安装依赖
pnpm install

# 开发服务器
pnpm dev

# 生产构建
pnpm build

# 类型检查
pnpm type:check

# 代码检查和格式化
pnpm lint:eslint:fix
pnpm lint:prettier
```

## ⚠️ 常见错误

1. **API响应处理错误**：直接使用 `response` 而不检查 `response.code`
2. **分页数据错误**：使用 `response.data` 而不是 `response.data.list`
3. **组件数据格式错误**：使用 `{value, label}` 而不是 `{id, fullName}`
4. **类型定义缺失**：使用 `any` 类型而不是具体的接口定义
5. **响应式数据错误**：未正确使用 `ref` 或 `reactive`

## 🎯 开发检查清单

### API调用
- [ ] 检查 `response.code === 200`
- [ ] 正确访问 `response.data`
- [ ] 处理错误情况并显示 `response.msg`
- [ ] 分页数据使用 `response.data.list`

### 组件开发
- [ ] 使用 Composition API
- [ ] 定义正确的 TypeScript 类型
- [ ] 组件数据格式为 `{id, fullName}`
- [ ] 使用 XACE 组件库优先