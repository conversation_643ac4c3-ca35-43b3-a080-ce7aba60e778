// 商机周报状态选项
export const BusinessWeeklogStatusOptions = [
  { label: '未填写', value: 0, color: 'default' },
  { label: '已填写', value: 1, color: 'blue' },
  { label: '审核中', value: 2, color: 'orange' },
  { label: '已发布', value: 3, color: 'green' },
  { label: '已驳回', value: -1, color: 'red' },
];

// 显示状态选项
export const ShowStatusOptions = [
  { label: '显示', value: 1, color: 'green' },
  { label: '隐藏', value: 0, color: 'default' },
];

// 商机周报接口响应
export interface BusinessWeeklogInfo {
  id: string;
  projId: string;
  projName: string;
  projType: string;
  projTypeName: string;
  projectLevel: string;
  projectLevelName: string;
  fbId: string;
  fbName: string;
  ownId: string;
  ownName: string;
  startDate: string;
  endDate: string;
  inputDate: string;
  projNote: string;
  note: string;
  plan: string;
  risk: string;
  status: number;
  showStatus: number;
  createdAt: string;
  createdBy: string;
  createdByName: string;
  lastUpdatedAt: string;
  lastUpdatedBy: string;
  lastUpdatedByName: string;
}

// 商机周报列表项
export type BusinessWeeklogListItem = BusinessWeeklogInfo;

// 商机周报分页响应
export interface BusinessWeeklogPageResult {
  list: BusinessWeeklogListItem[];
  pagination: {
    total: number;
    pageSize: number;
    currentPage: number;
  };
}

// 创建商机周报请求参数
export interface CreateBusinessWeeklogParams {
  projId: string;
  projName: string;
  projType?: string;
  projectLevel?: string;
  fbId?: string;
  ownId: string;
  startDate: string;
  endDate: string;
  inputDate?: string;
  projNote?: string;
  note: string;
  plan: string;
  risk?: string;
  showStatus?: number;
}

// 更新商机周报请求参数
export interface UpdateBusinessWeeklogParams extends CreateBusinessWeeklogParams {
  id: string;
}

// 查询商机周报参数
export interface QueryBusinessWeeklogParams {
  projName?: string;
  projType?: string;
  projectLevel?: string;
  ownId?: string;
  status?: number;
  showStatus?: number;
  startDateBegin?: string;
  startDateEnd?: string;
  pageSize?: number;
  currentPage?: number;
}

// 商机周报审核表单
export interface BizBusinessWeeklogAuditForm {
  id: string;
  status: number; // 3-已发布，-1-已驳回
  auditNote?: string;
}

// 商机周报VO
export type BizBusinessWeeklogVO = BusinessWeeklogInfo;

// 商机周报表单
export type BizBusinessWeeklogForm = CreateBusinessWeeklogParams;

// 商机周报分页参数
export type BizBusinessWeeklogPagination = QueryBusinessWeeklogParams;

// 商机周报历史记录VO
export interface BizBusinessWeeklogHistoryVO {
  id: string;
  projId: string;
  projName: string;
  startDate: string;
  endDate: string;
  note: string;
  plan: string;
  risk?: string;
  status: number;
  statusName: string;
  createdAt: string;
  createdByName: string;
}
