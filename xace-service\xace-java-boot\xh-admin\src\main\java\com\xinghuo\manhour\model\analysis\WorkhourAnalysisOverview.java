package com.xinghuo.manhour.model.analysis;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 工时分析概览数据
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@Schema(description = "工时分析概览数据")
public class WorkhourAnalysisOverview {

    @Schema(description = "总工时")
    private BigDecimal totalWorkMonth;

    @Schema(description = "参与项目数")
    private Integer projectCount;

    @Schema(description = "参与人员数")
    private Integer userCount;

    @Schema(description = "平均工时效率")
    private BigDecimal avgEfficiency;

    @Schema(description = "总工时记录数")
    private Integer totalRecords;

    @Schema(description = "活跃项目数")
    private Integer activeProjectCount;

    @Schema(description = "平均项目参与度")
    private BigDecimal avgProjectParticipation;

    @Schema(description = "工时完成率")
    private BigDecimal completionRate;
}


