# Claude 快速指令 - XACE 后端代码生成

本文档提供基于XACE后端规范的Claude快速指令，方便开发者快速生成符合项目标准的代码。

## 🚀 快速指令使用说明

**使用格式**：直接在Claude对话中输入指令和参数即可生成代码。

**重要提醒**：所有生成的代码都严格遵循XACE项目规范：
- 使用Jakarta EE包（不是javax.*）
- 继承BaseEntityV2体系
- 使用XHBaseMapper和BaseServiceImpl
- 统一ActionResult返回格式
- LambdaQueryWrapper查询

---

## 📋 核心指令列表

### 1. 实体类生成

**指令格式**：
```
/xace-entity [表名] [业务名] [字段列表]
```

**快速指令**：
```
生成XACE Entity：
表名：user_info
业务名：User
字段：account(String,账号), fullName(String,姓名), email(String,邮箱), status(Integer,状态)
```

**自动遵循**：
- 继承BaseEntityV2.CUBaseEntityV2<String>
- @TableField注解映射
- 完整JavaDoc注释

---

### 2. Form表单对象生成

**指令格式**：
```
/xace-form [业务名] [字段列表] [验证规则]
```

**快速指令**：
```
生成XACE Form：
业务名：User
字段：account(String,必填), fullName(String,必填), email(String,邮箱格式), password(String,新增必填)
```

**自动遵循**：
- 合并设计（创建+更新）
- Jakarta EE验证注解
- Swagger文档注解

---

### 3. VO视图对象生成

**指令格式**：
```
/xace-vo [业务名] [展示字段] [关联字段]
```

**快速指令**：
```
生成XACE VO：
业务名：User
展示字段：id, account, fullName, email, status, createdAt
关联字段：departmentName(String,部门名称), statusName(String,状态名称)
```

**自动遵循**：
- 扁平化设计
- 日期格式化@JsonFormat
- 状态转换字段

---

### 4. Pagination分页对象生成

**指令格式**：
```
/xace-pagination [业务名] [查询条件]
```

**快速指令**：
```
生成XACE Pagination：
业务名：User
查询条件：account(String,模糊), status(Integer,精确), keyword(String,关键字)
```

**自动遵循**：
- 继承Pagination基类
- 所有查询字段可选
- 不添加验证注解

---

### 5. Mapper接口生成

**指令格式**：
```
/xace-mapper [业务名] [实体类名]
```

**快速指令**：
```
生成XACE Mapper：
业务名：User
实体类：UserEntity
```

**自动遵循**：
- 仅继承XHBaseMapper<T>
- 不定义自定义方法
- 标准@Mapper注解

---

### 6. Service层生成

**指令格式**：
```
/xace-service [业务名] [实体类] [特殊功能]
```

**快速指令**：
```
生成XACE Service：
业务名：User
实体类：UserEntity
特殊功能：检查账号重复, 批量更新状态, 重置密码
```

**自动遵循**：
- Service接口继承BaseService<T>
- ServiceImpl继承BaseServiceImpl<M,E>
- LambdaQueryWrapper查询
- @Transactional事务管理

---

### 7. Controller生成

**指令格式**：
```
/xace-controller [业务名] [API路径] [功能列表]
```

**快速指令**：
```
生成XACE Controller：
业务名：User
API路径：/api/user
功能：分页查询, 详情查询, 创建, 更新, 删除, 重置密码
```

**自动遵循**：
- @Resource依赖注入
- ActionResult<T>返回
- Swagger文档注解
- @Valid参数验证

---

### 8. 完整CRUD生成

**指令格式**：
```
/xace-crud [表名] [业务名] [字段列表] [模块包名]
```

**快速指令**：
```
生成XACE完整CRUD：
表名：product_info
业务名：Product
字段：productName(String,商品名称), categoryId(String,分类ID), price(BigDecimal,价格), stock(Integer,库存), status(Integer,状态)
模块：com.xinghuo.product
```

**自动生成**：
- ProductEntity + ProductForm + ProductVO + ProductPagination
- ProductMapper + ProductService + ProductServiceImpl + ProductController
- 完整的CRUD操作和业务逻辑

---

### 9. 根据数据库表生成

**指令格式**：
```
/xace-from-sql [建表SQL]
```

**快速指令**：
```
根据SQL生成XACE代码：
CREATE TABLE `order_info` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `order_no` varchar(50) NOT NULL COMMENT '订单号',
  `user_id` varchar(32) NOT NULL COMMENT '用户ID',
  `total_amount` decimal(10,2) NOT NULL COMMENT '总金额',
  `status` tinyint NOT NULL COMMENT '订单状态',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单信息表';
```

**自动解析**：
- 表结构和字段类型
- 生成对应的Entity类
- 正确的类型映射

---

### 10. 单元测试生成

**指令格式**：
```
/xace-test [Service类名] [测试方法列表]
```

**快速指令**：
```
生成XACE单元测试：
Service类：UserServiceImpl
测试方法：create(创建用户), update(更新用户), isExistByAccount(账号重复检查)
```

**自动遵循**：
- JUnit 5 + Mockito
- @ExtendWith(MockitoExtension.class)
- 模拟所有依赖
- 完整的测试场景覆盖

---

## 🔧 高级指令

### 业务方法实现

**快速指令**：
```
实现XACE业务方法：
方法签名：public String createOrder(OrderForm orderForm)
业务逻辑：检查库存 -> 计算价格 -> 创建订单 -> 扣减库存 -> 返回订单ID
依赖服务：ProductService, OrderItemService, CouponService
```

### 复杂查询实现

**快速指令**：
```
实现XACE复杂查询：
方法名：getUsersByConditions
查询条件：部门ID, 角色列表, 年龄范围, 创建时间范围, 关键字模糊查询
排序：创建时间倒序
分页：支持
```

---

## 💡 使用技巧

### 1. 批量生成
```
连续生成多个相关类：
/xace-entity + /xace-form + /xace-vo + /xace-pagination 业务名：Product
```

### 2. 快速修改
```
基于现有代码修改：
在现有UserService基础上添加方法：batchUpdateStatus, exportUserData
```

### 3. 规范检查
```
检查代码是否符合XACE规范：
检查以下代码是否符合XACE规范：[粘贴代码]
```

---

## 📚 规范参考

**重要文档**：
- [后端框架规范总览](../../framework-standards/backend/README.md)
- [Entity层规范](../../framework-standards/backend/03_ENTITY_LAYER.md) 
- [Service层规范](../../framework-standards/backend/05_SERVICE_LAYER.md)
- [Model类规范](../../framework-standards/backend/07_MODEL_CLASSES.md)

**关键约定**：
- 使用Jakarta EE包（`jakarta.*`不是`javax.*`）
- BaseEntityV2字段：`createdAt`不是`createTime`
- QueryWrapper：强制使用`wrapper.lambda()`
- 依赖注入：推荐`@Resource`而不是`@Autowired`
- 返回格式：统一使用`ActionResult<T>`

---

## 🎯 快速开始示例

**最常用的完整流程**：

1. **生成实体**
```
生成XACE Entity：
表名：sys_user
业务名：User  
字段：account(String,账号), fullName(String,姓名), email(String,邮箱), mobile(String,手机), status(Integer,状态)
```

2. **生成Form和VO**
```
生成XACE Form：业务名：User，包含所有业务字段和验证规则
生成XACE VO：业务名：User，包含展示字段和statusName状态名称
```

3. **生成完整服务层**
```
生成XACE Service：
业务名：User
特殊功能：检查账号重复, 重置密码, 批量更新状态
```

4. **生成控制器**
```
生成XACE Controller：
业务名：User
API路径：/api/system/user
功能：完整CRUD + 重置密码 + 批量操作
```

使用这些快速指令，可以在几分钟内生成完整的、符合XACE规范的业务模块代码！