<template>
  <div class="business-tracking-list">
    <div class="operation-bar">
      <a-button type="primary" @click="handleAdd">
        <plus-outlined />
        添加跟踪记录
      </a-button>
    </div>

    <a-table :columns="columns" :dataSource="trackingList" :loading="loading" :pagination="pagination" @change="handleTableChange" rowKey="id">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <a-button type="link" @click="handleEdit(record)">编辑</a-button>
          <a-popconfirm title="确定要删除这条跟踪记录吗?" @confirm="handleDelete(record)" okText="确定" cancelText="取消">
            <a-button type="link" danger>删除</a-button>
          </a-popconfirm>
        </template>
      </template>
    </a-table>

    <!-- 添加/编辑跟踪记录弹窗 -->
    <a-modal v-model:visible="modalVisible" :title="modalTitle" @ok="handleModalOk" @cancel="handleModalCancel" :confirmLoading="confirmLoading">
      <a-form :model="formState" :rules="rules" ref="formRef" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <a-form-item label="跟踪日期" name="trackDate">
          <a-date-picker v-model:value="formState.trackDate" style="width: 100%" />
        </a-form-item>
        <a-form-item label="跟踪内容" name="content">
          <a-textarea v-model:value="formState.content" :rows="4" placeholder="请输入跟踪内容" :maxlength="2000" show-count />
        </a-form-item>
        <a-form-item label="跟踪人" name="trackUser">
          <a-input v-model:value="formState.trackUser" placeholder="请输入跟踪人" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted, PropType } from 'vue';
  import { PlusOutlined } from '@ant-design/icons-vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { getBusinessTrackingList, addBusinessTracking, updateBusinessTracking, deleteBusinessTracking } from '/@/api/project/businessTracking';

  const props = defineProps({
    businessId: {
      type: String as PropType<string>,
      required: true,
    },
  });

  const { createMessage } = useMessage();
  const loading = ref(false);
  const trackingList = ref([]);
  const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showTotal: (total: number) => `共 ${total} 条`,
  });

  // 表格列定义
  const columns = [
    {
      title: '跟踪日期',
      dataIndex: 'trackDate',
      key: 'trackDate',
      width: 150,
    },
    {
      title: '跟踪内容',
      dataIndex: 'content',
      key: 'content',
      ellipsis: true,
    },
    {
      title: '跟踪人',
      dataIndex: 'trackUser',
      key: 'trackUser',
      width: 120,
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
    },
  ];

  // 弹窗相关
  const modalVisible = ref(false);
  const modalTitle = ref('添加跟踪记录');
  const confirmLoading = ref(false);
  const formRef = ref();
  const formState = reactive({
    id: '',
    businessId: props.businessId,
    trackDate: null,
    content: '',
    trackUser: '',
  });

  // 表单验证规则
  const rules = {
    trackDate: [{ required: true, message: '请选择跟踪日期', trigger: 'change' }],
    content: [
      { required: true, message: '请输入跟踪内容', trigger: 'blur' },
      { max: 2000, message: '跟踪内容最多为2000个字符', trigger: 'blur' },
    ],
    trackUser: [{ required: true, message: '请输入跟踪人', trigger: 'blur' }],
  };

  // 获取跟踪记录列表
  async function fetchTrackingList() {
    if (!props.businessId) return;

    loading.value = true;
    try {
      const { items, total } = await getBusinessTrackingList({
        businessId: props.businessId,
        page: pagination.current,
        pageSize: pagination.pageSize,
      });
      trackingList.value = items;
      pagination.total = total;
    } catch (error) {
      console.error('获取跟踪记录失败:', error);
      createMessage.error('获取跟踪记录失败');
    } finally {
      loading.value = false;
    }
  }

  // 表格分页、排序、筛选变化
  function handleTableChange(pag) {
    pagination.current = pag.current;
    pagination.pageSize = pag.pageSize;
    fetchTrackingList();
  }

  // 添加跟踪记录
  function handleAdd() {
    modalTitle.value = '添加跟踪记录';
    formState.id = '';
    formState.businessId = props.businessId;
    formState.trackDate = null;
    formState.content = '';
    formState.trackUser = '';
    modalVisible.value = true;
  }

  // 编辑跟踪记录
  function handleEdit(record) {
    modalTitle.value = '编辑跟踪记录';
    formState.id = record.id;
    formState.businessId = props.businessId;
    formState.trackDate = record.trackDate;
    formState.content = record.content;
    formState.trackUser = record.trackUser;
    modalVisible.value = true;
  }

  // 删除跟踪记录
  async function handleDelete(record) {
    try {
      await deleteBusinessTracking(record.id);
      createMessage.success('删除成功');
      fetchTrackingList();
    } catch (error) {
      console.error('删除跟踪记录失败:', error);
      createMessage.error('删除跟踪记录失败');
    }
  }

  // 弹窗确认
  async function handleModalOk() {
    try {
      await formRef.value.validate();
      confirmLoading.value = true;

      if (formState.id) {
        // 更新
        await updateBusinessTracking(formState.id, {
          businessId: formState.businessId,
          trackDate: formState.trackDate,
          content: formState.content,
          trackUser: formState.trackUser,
        });
        createMessage.success('更新成功');
      } else {
        // 新增
        await addBusinessTracking({
          businessId: formState.businessId,
          trackDate: formState.trackDate,
          content: formState.content,
          trackUser: formState.trackUser,
        });
        createMessage.success('添加成功');
      }

      modalVisible.value = false;
      fetchTrackingList();
    } catch (error) {
      console.error('保存跟踪记录失败:', error);
    } finally {
      confirmLoading.value = false;
    }
  }

  // 弹窗取消
  function handleModalCancel() {
    modalVisible.value = false;
  }

  // 组件挂载时获取数据
  onMounted(() => {
    fetchTrackingList();
  });
</script>

<style lang="less" scoped>
  .business-tracking-list {
    .operation-bar {
      margin-bottom: 16px;
    }
  }
</style>
