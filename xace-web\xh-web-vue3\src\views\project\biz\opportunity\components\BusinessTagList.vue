<template>
  <div class="business-tag-list">
    <div class="operation-bar">
      <a-button type="primary" @click="handleAdd">
        <plus-outlined />
        添加标签记录
      </a-button>
    </div>

    <a-table :columns="columns" :dataSource="tagList" :loading="loading" :pagination="pagination" @change="handleTableChange" rowKey="id">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'tagType'">
          <a-tag :color="getTagColor(record.tagType)">{{ record.tagType }}</a-tag>
        </template>
        <template v-if="column.key === 'action'">
          <a-button type="link" @click="handleView(record)">查看</a-button>
          <a-button type="link" @click="handleEdit(record)">编辑</a-button>
          <a-popconfirm title="确定要删除这条标签记录吗?" @confirm="handleDelete(record)" okText="确定" cancelText="取消">
            <a-button type="link" danger>删除</a-button>
          </a-popconfirm>
        </template>
      </template>
    </a-table>

    <!-- 添加/编辑标签记录弹窗 -->
    <a-modal v-model:visible="modalVisible" :title="modalTitle" @ok="handleModalOk" @cancel="handleModalCancel" :confirmLoading="confirmLoading" width="700px">
      <a-form :model="formState" :rules="rules" ref="formRef" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <a-form-item label="标签类型" name="tagType">
          <a-select v-model:value="formState.tagType" placeholder="请选择标签类型">
            <a-select-option value="重要信息">重要信息</a-select-option>
            <a-select-option value="进度更新">进度更新</a-select-option>
            <a-select-option value="风险提示">风险提示</a-select-option>
            <a-select-option value="其他">其他</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="标签标题" name="tagTitle">
          <a-input v-model:value="formState.tagTitle" placeholder="请输入标签标题" />
        </a-form-item>
        <a-form-item label="标签内容" name="tagContent">
          <a-textarea v-model:value="formState.tagContent" :rows="4" placeholder="请输入标签内容" :maxlength="2000" show-count />
        </a-form-item>
        <a-form-item label="创建人" name="createUser">
          <a-input v-model:value="formState.createUser" placeholder="请输入创建人" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 查看标签记录弹窗 -->
    <a-modal v-model:visible="viewModalVisible" title="查看标签记录" @cancel="() => (viewModalVisible = false)" :footer="null">
      <a-descriptions bordered :column="1">
        <a-descriptions-item label="标签类型">
          <a-tag :color="getTagColor(currentTag.tagType)">{{ currentTag.tagType }}</a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="标签标题">{{ currentTag.tagTitle }}</a-descriptions-item>
        <a-descriptions-item label="标签内容">{{ currentTag.tagContent }}</a-descriptions-item>
        <a-descriptions-item label="创建人">{{ currentTag.createUser }}</a-descriptions-item>
        <a-descriptions-item label="创建时间">{{ currentTag.createTime }}</a-descriptions-item>
      </a-descriptions>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted, PropType } from 'vue';
  import { PlusOutlined } from '@ant-design/icons-vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { getBusinessTagList, addBusinessTag, updateBusinessTag, deleteBusinessTag } from '/@/api/project/businessTag';

  const props = defineProps({
    businessId: {
      type: String as PropType<string>,
      required: true,
    },
  });

  const { createMessage } = useMessage();
  const loading = ref(false);
  const tagList = ref([]);
  const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showTotal: (total: number) => `共 ${total} 条`,
  });

  // 表格列定义
  const columns = [
    {
      title: '标签类型',
      dataIndex: 'tagType',
      key: 'tagType',
      width: 120,
    },
    {
      title: '标签标题',
      dataIndex: 'tagTitle',
      key: 'tagTitle',
      ellipsis: true,
    },
    {
      title: '创建人',
      dataIndex: 'createUser',
      key: 'createUser',
      width: 120,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 150,
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
    },
  ];

  // 编辑弹窗相关
  const modalVisible = ref(false);
  const modalTitle = ref('添加标签记录');
  const confirmLoading = ref(false);
  const formRef = ref();
  const formState = reactive({
    id: '',
    businessId: props.businessId,
    tagType: '',
    tagTitle: '',
    tagContent: '',
    createUser: '',
  });

  // 查看弹窗相关
  const viewModalVisible = ref(false);
  const currentTag = ref({
    id: '',
    tagType: '',
    tagTitle: '',
    tagContent: '',
    createUser: '',
    createTime: '',
  });

  // 表单验证规则
  const rules = {
    tagType: [{ required: true, message: '请选择标签类型', trigger: 'change' }],
    tagTitle: [{ required: true, message: '请输入标签标题', trigger: 'blur' }],
    tagContent: [
      { required: true, message: '请输入标签内容', trigger: 'blur' },
      { max: 2000, message: '标签内容最多为2000个字符', trigger: 'blur' },
    ],
    createUser: [{ required: true, message: '请输入创建人', trigger: 'blur' }],
  };

  // 获取标签颜色
  function getTagColor(tagType) {
    switch (tagType) {
      case '重要信息':
        return 'blue';
      case '进度更新':
        return 'green';
      case '风险提示':
        return 'red';
      case '其他':
        return 'orange';
      default:
        return 'default';
    }
  }

  // 获取标签记录列表
  async function fetchTagList() {
    if (!props.businessId) return;

    loading.value = true;
    try {
      const { items, total } = await getBusinessTagList({
        businessId: props.businessId,
        page: pagination.current,
        pageSize: pagination.pageSize,
      });
      tagList.value = items;
      pagination.total = total;
    } catch (error) {
      console.error('获取标签记录失败:', error);
      createMessage.error('获取标签记录失败');
    } finally {
      loading.value = false;
    }
  }

  // 表格分页、排序、筛选变化
  function handleTableChange(pag) {
    pagination.current = pag.current;
    pagination.pageSize = pag.pageSize;
    fetchTagList();
  }

  // 查看标签记录
  function handleView(record) {
    currentTag.value = { ...record };
    viewModalVisible.value = true;
  }

  // 添加标签记录
  function handleAdd() {
    modalTitle.value = '添加标签记录';
    formState.id = '';
    formState.businessId = props.businessId;
    formState.tagType = '';
    formState.tagTitle = '';
    formState.tagContent = '';
    formState.createUser = '';
    modalVisible.value = true;
  }

  // 编辑标签记录
  function handleEdit(record) {
    modalTitle.value = '编辑标签记录';
    formState.id = record.id;
    formState.businessId = props.businessId;
    formState.tagType = record.tagType;
    formState.tagTitle = record.tagTitle;
    formState.tagContent = record.tagContent;
    formState.createUser = record.createUser;
    modalVisible.value = true;
  }

  // 删除标签记录
  async function handleDelete(record) {
    try {
      await deleteBusinessTag(record.id);
      createMessage.success('删除成功');
      fetchTagList();
    } catch (error) {
      console.error('删除标签记录失败:', error);
      createMessage.error('删除标签记录失败');
    }
  }

  // 弹窗确认
  async function handleModalOk() {
    try {
      await formRef.value.validate();
      confirmLoading.value = true;

      if (formState.id) {
        // 更新
        await updateBusinessTag(formState.id, {
          businessId: formState.businessId,
          tagType: formState.tagType,
          tagTitle: formState.tagTitle,
          tagContent: formState.tagContent,
          createUser: formState.createUser,
        });
        createMessage.success('更新成功');
      } else {
        // 新增
        await addBusinessTag({
          businessId: formState.businessId,
          tagType: formState.tagType,
          tagTitle: formState.tagTitle,
          tagContent: formState.tagContent,
          createUser: formState.createUser,
        });
        createMessage.success('添加成功');
      }

      modalVisible.value = false;
      fetchTagList();
    } catch (error) {
      console.error('保存标签记录失败:', error);
    } finally {
      confirmLoading.value = false;
    }
  }

  // 弹窗取消
  function handleModalCancel() {
    modalVisible.value = false;
  }

  // 组件挂载时获取数据
  onMounted(() => {
    fetchTagList();
  });
</script>

<style lang="less" scoped>
  .business-tag-list {
    .operation-bar {
      margin-bottom: 16px;
    }
  }
</style>
