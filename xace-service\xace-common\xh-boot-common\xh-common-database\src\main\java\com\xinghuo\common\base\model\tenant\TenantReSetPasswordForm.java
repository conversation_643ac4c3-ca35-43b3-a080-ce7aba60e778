package com.xinghuo.common.base.model.tenant;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;


/**
 *
 */
@Data
public class TenantReSetPasswordForm {
    @NotNull(message = "主键不能为空")
    @Schema(description ="主键")
    private String id;
    @NotNull(message = "新密码不能为空")
    @Schema(description ="新密码")
    private String userPassword;
    @NotNull(message = "确认新密码不能为空")
    @Schema(description ="确认新密码")
    private String validatePassword;
    @Schema(description ="tenantId")
    private String tenantId;
}
