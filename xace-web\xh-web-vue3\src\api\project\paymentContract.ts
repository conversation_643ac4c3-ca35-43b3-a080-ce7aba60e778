import { defHttp } from '/@/utils/http/axios';

/**
 * 付款合同管理相关类型定义
 */

// 付款合同VO (基于PaymentContractEntity)
export interface PaymentContractVO {
  id: string;
  pcId: string; // 采购合同ID
  name: string; // 合同名称
  cno: string; // 采购合同编号
  cid?: string; // 收款合同ID
  contractName?: string; // 关联收款合同名称
  contractCno?: string; // 关联收款合同编号
  suppilerId?: string; // 供应商ID
  suppilerName?: string; // 供应商名称
  ownId?: string; // 负责人ID
  ownName?: string; // 负责人名称
  amount?: number; // 合同金额
  status?: string; // 状态
  statusText?: string; // 状态文本

  // 日期字段
  signDate?: string; // 签订日期
  signYear?: number; // 签署年份
  estSignDate?: string; // 预计签订日期

  // 金额分配
  yfAmount?: number; // 已付金额
  kfybAmount?: number; // 开发一部金额
  kfebAmount?: number; // 开发二部金额
  otherAmount?: number; // 综合金额

  // 状态信息
  moneyStatus?: string; // 收款状态
  moneyStatusText?: string; // 收款状态文本
  note?: string; // 备注

  // 系统字段
  createUserId?: string;
  createTime?: string;
  lastModifiedUserId?: string;
  updateTime?: string;
  tenantId?: string;
  flowId?: string;
  deleteMark?: number;
}

// 付款合同表单对象
export interface PaymentContractForm {
  id?: string;
  pcId?: string;
  name: string; // 合同名称 (必填)
  cno: string; // 采购合同编号 (必填)
  cid?: string; // 收款合同ID
  suppilerId?: string; // 供应商ID
  ownId?: string; // 负责人ID
  amount?: number; // 合同金额
  status?: string; // 状态

  // 日期字段
  signDate?: string; // 签订日期
  estSignDate?: string; // 预计签订日期

  // 金额分配
  kfybAmount?: number; // 开发一部金额
  kfebAmount?: number; // 开发二部金额
  otherAmount?: number; // 综合金额

  // 状态信息
  moneyStatus?: string; // 收款状态
  note?: string; // 备注
}

// 付款合同分页查询参数
export interface PaymentContractPagination {
  currentPage: number;
  pageSize: number;
  keyword?: string; // 关键字搜索（合同名称/编号）
  name?: string; // 合同名称
  cno?: string; // 合同编号
  cid?: string; // 收款合同ID
  suppilerId?: string; // 供应商ID
  ownId?: string; // 负责人ID
  status?: string; // 状态
  moneyStatus?: string; // 收款状态
  signYear?: number; // 签署年份

  // 时间范围查询
  signDateStart?: string; // 签订日期开始
  signDateEnd?: string; // 签订日期结束
  estSignDateStart?: string; // 预计签订日期开始
  estSignDateEnd?: string; // 预计签订日期结束

  // 金额范围查询
  minAmount?: number; // 最小合同金额
  maxAmount?: number; // 最大合同金额
  minYfAmount?: number; // 最小已付金额
  maxYfAmount?: number; // 最大已付金额
}

// 分页结果
export interface PageResult<T> {
  list: T[];
  total: number;
  currentPage: number;
  pageSize: number;
}

// 付款合同统计信息
export interface PaymentContractStats {
  totalContracts: number; // 付款合同总数
  totalAmount: number; // 合同总金额
  totalPaidAmount: number; // 已付总金额
  totalUnpaidAmount: number; // 未付总金额

  // 按状态统计
  statusStats: Array<{
    status: string;
    statusText: string;
    count: number;
    amount: number;
  }>;

  // 按供应商统计
  supplierStats: Array<{
    suppilerId: string;
    suppilerName: string;
    count: number;
    amount: number;
    paidAmount: number;
  }>;

  // 按部门统计
  deptStats: Array<{
    deptType: string; // 部门类型：kfyb(开发一部)/kfeb(开发二部)/other(综合)
    deptName: string;
    amount: number;
    percentage: number;
  }>;

  // 按收款合同统计
  contractStats: Array<{
    cid: string;
    contractName: string;
    paymentContractCount: number;
    totalAmount: number;
    paidAmount: number;
  }>;
}

// 付款合同状态更新表单
export interface PaymentContractStatusUpdateForm {
  status: string;
  note?: string;
  signDate?: string; // 签订日期
}

/**
 * 付款合同管理API接口
 */
export const paymentContractApi = {
  /**
   * 获取付款合同列表 (分页)
   */
  getList: (params: PaymentContractPagination) => {
    return defHttp.post<PageResult<PaymentContractVO>>({ url: '/api/project/biz/paymentContract/getList', data: params });
  },

  /**
   * 根据收款合同ID获取付款合同列表
   */
  getByContractId: (contractId: string) => {
    return defHttp.get<PaymentContractVO[]>({ url: `/api/project/biz/paymentContract/contract/${contractId}` });
  },

  /**
   * 获取付款合同详情
   */
  getInfo: (id: string) => {
    return defHttp.get<PaymentContractVO>({ url: `/api/project/biz/paymentContract/${id}` });
  },

  /**
   * 创建付款合同
   */
  create: (data: PaymentContractForm) => {
    return defHttp.post<string>({ url: '/api/project/biz/paymentContract', data });
  },

  /**
   * 更新付款合同
   */
  update: (id: string, data: PaymentContractForm) => {
    return defHttp.put<string>({ url: `/api/project/biz/paymentContract/${id}`, data });
  },

  /**
   * 删除付款合同
   */
  delete: (id: string) => {
    return defHttp.delete<string>({ url: `/api/project/biz/paymentContract/${id}` });
  },

  /**
   * 批量删除付款合同
   */
  batchDelete: (ids: string[]) => {
    return defHttp.post<string>({ url: '/api/project/biz/paymentContract/batchDelete', data: { ids } });
  },

  /**
   * 更新付款合同状态
   */
  updateStatus: (id: string, status: string) => {
    return defHttp.put<string>({ url: `/api/project/biz/paymentContract/${id}/status/${status}` });
  },

  /**
   * 批量更新付款合同状态
   */
  batchUpdateStatus: (ids: string[], data: PaymentContractStatusUpdateForm) => {
    return defHttp.put<string>({ url: '/api/project/biz/paymentContract/batchUpdateStatus', data: { ids, ...data } });
  },

  /**
   * 获取付款合同统计信息
   */
  getStats: (params?: {
    cid?: string; // 收款合同ID
    suppilerId?: string; // 供应商ID
    ownId?: string; // 负责人ID
    startDate?: string; // 开始日期
    endDate?: string; // 结束日期
    status?: string; // 状态
  }) => {
    return defHttp.post<PaymentContractStats>({ url: '/api/project/biz/paymentContract/stats', data: params || {} });
  },

  /**
   * 导出付款合同数据
   */
  export: (params: PaymentContractPagination) => {
    return defHttp.post({ url: '/api/project/biz/paymentContract/export', data: params, responseType: 'blob' });
  },

  /**
   * 获取付款合同选择器数据
   */
  getSelector: (contractId?: string, keyword?: string) => {
    return defHttp.get<Array<{ id: string; fullName: string }>>({
      url: '/api/project/biz/paymentContract/selector',
      params: { contractId, keyword },
    });
  },

  /**
   * 检查付款合同编号是否存在
   */
  checkCnoExists: (cno: string, excludeId?: string) => {
    return defHttp.get<boolean>({
      url: '/api/project/biz/paymentContract/check-cno',
      params: { cNo: cno, id: excludeId },
    });
  },

  /**
   * 根据收款合同自动计算部门分配金额
   */
  calculateDeptAmount: (contractId: string, totalAmount: number) => {
    return defHttp.get<{
      kfybAmount: number;
      kfebAmount: number;
      otherAmount: number;
    }>({
      url: '/api/project/biz/paymentContract/calculateDeptAmount',
      params: { contractId, totalAmount },
    });
  },

  /**
   * 获取供应商列表（用于选择器）
   */
  getSupplierSelector: (keyword?: string) => {
    return defHttp.get<Array<{ id: string; fullName: string }>>({
      url: '/api/project/biz/paymentContract/supplierSelector',
      params: { keyword },
    });
  },

  /**
   * 批量关联收款合同
   */
  batchLinkContract: (paymentContractIds: string[], contractId: string) => {
    return defHttp.put<string>({
      url: '/api/project/biz/paymentContract/batchLinkContract',
      data: { paymentContractIds, contractId },
    });
  },
};

// 默认导出
export default paymentContractApi;
