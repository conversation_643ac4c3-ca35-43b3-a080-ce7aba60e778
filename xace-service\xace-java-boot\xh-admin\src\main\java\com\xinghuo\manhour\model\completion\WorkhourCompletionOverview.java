package com.xinghuo.manhour.model.completion;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 工时填写情况概览数据
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@Schema(description = "工时填写情况概览数据")
public class WorkhourCompletionOverview {

    @Schema(description = "应填写人数")
    private Integer totalUsers;

    @Schema(description = "已填写人数")
    private Integer completedUsers;

    @Schema(description = "填写完成率")
    private BigDecimal completionRate;

    @Schema(description = "待审批数量")
    private Integer pendingApproval;

    @Schema(description = "未填写人数")
    private Integer unfilledUsers;

    @Schema(description = "逾期人数")
    private Integer overdueUsers;

    @Schema(description = "已审批数量")
    private Integer approvedCount;

    @Schema(description = "审批完成率")
    private BigDecimal approvalRate;

    @Schema(description = "平均审批时长(天)")
    private BigDecimal avgApprovalDays;

    @Schema(description = "最长等待天数")
    private Integer maxWaitingDays;
}
