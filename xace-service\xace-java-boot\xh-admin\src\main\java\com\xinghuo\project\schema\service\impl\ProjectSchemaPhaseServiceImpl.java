package com.xinghuo.project.schema.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.schema.dao.ProjectSchemaPhaseMapper;
import com.xinghuo.project.schema.entity.ProjectSchemaPhaseEntity;
import com.xinghuo.project.schema.model.ProjectSchemaPhasePagination;
import com.xinghuo.project.schema.service.ProjectSchemaPhaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目模板阶段配置服务实现类
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
@Slf4j
@Service
public class ProjectSchemaPhaseServiceImpl extends BaseServiceImpl<ProjectSchemaPhaseMapper, ProjectSchemaPhaseEntity> implements ProjectSchemaPhaseService {

    @Override
    public List<ProjectSchemaPhaseEntity> getList(ProjectSchemaPhasePagination pagination) {
        QueryWrapper<ProjectSchemaPhaseEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<ProjectSchemaPhaseEntity> lambda = queryWrapper.lambda();

        // 根据项目模板ID查询
        if (StrXhUtil.isNotEmpty(pagination.getProjectTemplateId())) {
            lambda.eq(ProjectSchemaPhaseEntity::getProjectTemplateId, pagination.getProjectTemplateId());
        }

        // 根据阶段库ID查询
        if (StrXhUtil.isNotEmpty(pagination.getPhaseLibraryId())) {
            lambda.eq(ProjectSchemaPhaseEntity::getPhaseLibraryId, pagination.getPhaseLibraryId());
        }

        // 序号范围查询
        if (pagination.getSeqNoMin() != null) {
            lambda.ge(ProjectSchemaPhaseEntity::getSeqNo, pagination.getSeqNoMin());
        }
        if (pagination.getSeqNoMax() != null) {
            lambda.le(ProjectSchemaPhaseEntity::getSeqNo, pagination.getSeqNoMax());
        }

        // 工期范围查询
        if (pagination.getDurationMin() != null) {
            lambda.ge(ProjectSchemaPhaseEntity::getDuration, pagination.getDurationMin());
        }
        if (pagination.getDurationMax() != null) {
            lambda.le(ProjectSchemaPhaseEntity::getDuration, pagination.getDurationMax());
        }

        // 权重范围查询
        if (pagination.getCompletionWeightMin() != null) {
            lambda.ge(ProjectSchemaPhaseEntity::getCompletionWeight, pagination.getCompletionWeightMin());
        }
        if (pagination.getCompletionWeightMax() != null) {
            lambda.le(ProjectSchemaPhaseEntity::getCompletionWeight, pagination.getCompletionWeightMax());
        }

        // 是否可裁剪查询
        if (pagination.getCanCut() != null) {
            lambda.eq(ProjectSchemaPhaseEntity::getCanCut, pagination.getCanCut());
        }

        // 审批流程ID查询
        if (StrXhUtil.isNotEmpty(pagination.getApprovalId())) {
            lambda.eq(ProjectSchemaPhaseEntity::getApprovalId, pagination.getApprovalId());
        }

        // 检查单模板ID查询
        if (StrXhUtil.isNotEmpty(pagination.getChecklistId())) {
            lambda.eq(ProjectSchemaPhaseEntity::getChecklistId, pagination.getChecklistId());
        }

        // 交付物模板ID查询
        if (StrXhUtil.isNotEmpty(pagination.getWorkproductTplId())) {
            lambda.eq(ProjectSchemaPhaseEntity::getWorkproductTplId, pagination.getWorkproductTplId());
        }

        // 创建时间范围查询
        if (pagination.getCreateTimeStart() != null) {
            lambda.ge(ProjectSchemaPhaseEntity::getCreatedAt, pagination.getCreateTimeStart());
        }
        if (pagination.getCreateTimeEnd() != null) {
            lambda.le(ProjectSchemaPhaseEntity::getCreatedAt, pagination.getCreateTimeEnd());
        }

        // 更新时间范围查询
        if (pagination.getUpdateTimeStart() != null) {
            lambda.ge(ProjectSchemaPhaseEntity::getLastUpdatedAt, pagination.getUpdateTimeStart());
        }
        if (pagination.getUpdateTimeEnd() != null) {
            lambda.le(ProjectSchemaPhaseEntity::getLastUpdatedAt, pagination.getUpdateTimeEnd());
        }

        // 关键字搜索（这里需要关联阶段库表查询阶段名称和编码）
        String keyword = pagination.getKeyword();
        if (StrXhUtil.isNotEmpty(keyword)) {
            // TODO: 需要关联阶段库表进行搜索，暂时跳过
            log.warn("关键字搜索功能需要关联阶段库表实现，当前跳过");
        }

        // 排序：按序号升序，创建时间倒序
        lambda.orderByAsc(ProjectSchemaPhaseEntity::getSeqNo)
              .orderByDesc(ProjectSchemaPhaseEntity::getCreatedAt);
        
        return processDataType(queryWrapper, pagination);
    }

    @Override
    public List<ProjectSchemaPhaseEntity> getListByTemplateId(String projectTemplateId) {
        if (StrXhUtil.isEmpty(projectTemplateId)) {
            return new ArrayList<>();
        }

        QueryWrapper<ProjectSchemaPhaseEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                   .eq(ProjectSchemaPhaseEntity::getProjectTemplateId, projectTemplateId)
                   .orderByAsc(ProjectSchemaPhaseEntity::getSeqNo);

        return this.list(queryWrapper);
    }

    @Override
    public ProjectSchemaPhaseEntity getInfo(String id) {
        if (StrXhUtil.isEmpty(id)) {
            log.warn("查询阶段配置信息ID为空");
            return null;
        }
        return this.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String create(ProjectSchemaPhaseEntity entity) {
        if (entity == null) {
            log.warn("创建阶段配置信息为空");
            throw new RuntimeException("阶段配置信息不能为空");
        }

        // 验证必填字段
        if (StrXhUtil.isEmpty(entity.getProjectTemplateId())) {
            log.warn("项目模板ID不能为空");
            throw new RuntimeException("项目模板ID不能为空");
        }

        if (StrXhUtil.isEmpty(entity.getPhaseLibraryId())) {
            log.warn("阶段库ID不能为空");
            throw new RuntimeException("阶段库ID不能为空");
        }

        // 检查阶段库ID在该模板中是否重复
        boolean exists = isExistByPhaseLibraryId(entity.getProjectTemplateId(), entity.getPhaseLibraryId(), null);
        if (exists) {
            log.warn("该阶段在模板中已存在: templateId={}, phaseLibraryId={}", 
                    entity.getProjectTemplateId(), entity.getPhaseLibraryId());
            throw new RuntimeException("该阶段在模板中已存在");
        }

        // 设置ID
        String id = RandomUtil.snowId();
        entity.setId(id);

        // 如果没有设置序号，自动生成
        if (entity.getSeqNo() == null) {
            entity.setSeqNo(getNextSeqNo(entity.getProjectTemplateId()));
        }

        // 设置默认值
        if (entity.getCanCut() == null) {
            entity.setCanCut(1); // 默认可裁剪
        }

        boolean success = this.save(entity);
        if (!success) {
            log.error("创建阶段配置失败");
            throw new RuntimeException("创建阶段配置失败");
        }

        log.info("创建阶段配置成功, ID: {}", id);
        return id;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(String id, ProjectSchemaPhaseEntity entity) {
        if (StrXhUtil.isEmpty(id) || entity == null) {
            log.warn("更新阶段配置参数无效, ID: {}", id);
            throw new RuntimeException("更新参数无效");
        }

        // 查询原记录是否存在
        ProjectSchemaPhaseEntity dbEntity = this.getById(id);
        if (dbEntity == null) {
            log.warn("更新的阶段配置不存在, ID: {}", id);
            throw new RuntimeException("阶段配置不存在");
        }

        // 验证必填字段
        if (StrXhUtil.isEmpty(entity.getProjectTemplateId())) {
            log.warn("项目模板ID不能为空");
            throw new RuntimeException("项目模板ID不能为空");
        }

        if (StrXhUtil.isEmpty(entity.getPhaseLibraryId())) {
            log.warn("阶段库ID不能为空");
            throw new RuntimeException("阶段库ID不能为空");
        }

        // 检查阶段库ID在该模板中是否重复（排除自身）
        boolean exists = isExistByPhaseLibraryId(entity.getProjectTemplateId(), entity.getPhaseLibraryId(), id);
        if (exists) {
            log.warn("该阶段在模板中已存在: templateId={}, phaseLibraryId={}", 
                    entity.getProjectTemplateId(), entity.getPhaseLibraryId());
            throw new RuntimeException("该阶段在模板中已存在");
        }

        // 设置ID
        entity.setId(id);

        boolean success = this.updateById(entity);
        if (!success) {
            log.error("更新阶段配置失败, ID: {}", id);
            throw new RuntimeException("更新阶段配置失败");
        }

        log.info("更新阶段配置成功, ID: {}", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        if (StrXhUtil.isEmpty(id)) {
            log.warn("删除阶段配置ID为空");
            throw new RuntimeException("阶段配置ID不能为空");
        }

        // 查询记录是否存在
        ProjectSchemaPhaseEntity entity = this.getById(id);
        if (entity == null) {
            log.warn("删除的阶段配置不存在, ID: {}", id);
            throw new RuntimeException("阶段配置不存在");
        }

        // TODO: 检查是否被项目使用，如果被使用则不允许删除
        // checkUsageBeforeDelete(id);

        boolean success = this.removeById(id);
        if (!success) {
            log.error("删除阶段配置失败, ID: {}", id);
            throw new RuntimeException("删除阶段配置失败");
        }

        log.info("删除阶段配置成功, ID: {}", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            log.warn("批量删除阶段配置ID列表为空");
            throw new RuntimeException("阶段配置ID列表不能为空");
        }

        // TODO: 批量检查是否被项目使用
        // batchCheckUsageBeforeDelete(ids);

        boolean success = this.removeByIds(ids);
        if (!success) {
            log.error("批量删除阶段配置失败, IDs: {}", ids);
            throw new RuntimeException("批量删除阶段配置失败");
        }

        log.info("批量删除阶段配置成功, 删除数量: {}", ids.size());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByTemplateId(String projectTemplateId) {
        if (StrXhUtil.isEmpty(projectTemplateId)) {
            log.warn("删除阶段配置的项目模板ID为空");
            return;
        }

        QueryWrapper<ProjectSchemaPhaseEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ProjectSchemaPhaseEntity::getProjectTemplateId, projectTemplateId);

        boolean success = this.remove(queryWrapper);
        if (!success) {
            log.error("根据模板ID删除阶段配置失败, templateId: {}", projectTemplateId);
            throw new RuntimeException("删除阶段配置失败");
        }

        log.info("根据模板ID删除阶段配置成功, templateId: {}", projectTemplateId);
    }

    @Override
    public boolean isExistByPhaseLibraryId(String projectTemplateId, String phaseLibraryId, String excludeId) {
        if (StrXhUtil.isEmpty(projectTemplateId) || StrXhUtil.isEmpty(phaseLibraryId)) {
            return false;
        }

        QueryWrapper<ProjectSchemaPhaseEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                   .eq(ProjectSchemaPhaseEntity::getProjectTemplateId, projectTemplateId)
                   .eq(ProjectSchemaPhaseEntity::getPhaseLibraryId, phaseLibraryId);

        // 如果有排除ID，则添加排除条件
        if (StrXhUtil.isNotEmpty(excludeId)) {
            queryWrapper.lambda().ne(ProjectSchemaPhaseEntity::getId, excludeId);
        }

        long count = this.count(queryWrapper);
        return count > 0;
    }

    @Override
    public ProjectSchemaPhaseEntity getByPhaseLibraryId(String projectTemplateId, String phaseLibraryId) {
        if (StrXhUtil.isEmpty(projectTemplateId) || StrXhUtil.isEmpty(phaseLibraryId)) {
            return null;
        }

        QueryWrapper<ProjectSchemaPhaseEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                   .eq(ProjectSchemaPhaseEntity::getProjectTemplateId, projectTemplateId)
                   .eq(ProjectSchemaPhaseEntity::getPhaseLibraryId, phaseLibraryId);

        return this.getOne(queryWrapper);
    }

    @Override
    public List<ProjectSchemaPhaseEntity> getSelectList(String projectTemplateId, String keyword) {
        QueryWrapper<ProjectSchemaPhaseEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<ProjectSchemaPhaseEntity> lambda = queryWrapper.lambda();

        // 根据项目模板ID查询
        if (StrXhUtil.isNotEmpty(projectTemplateId)) {
            lambda.eq(ProjectSchemaPhaseEntity::getProjectTemplateId, projectTemplateId);
        }

        // 关键字搜索（这里需要关联阶段库表查询阶段名称和编码）
        if (StrXhUtil.isNotEmpty(keyword)) {
            // TODO: 需要关联阶段库表进行搜索，暂时跳过
            log.warn("关键字搜索功能需要关联阶段库表实现，当前跳过");
        }

        // 按序号排序
        lambda.orderByAsc(ProjectSchemaPhaseEntity::getSeqNo);

        return this.list(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int copyPhaseConfigs(String sourceTemplateId, String targetTemplateId) {
        if (StrXhUtil.isEmpty(sourceTemplateId) || StrXhUtil.isEmpty(targetTemplateId)) {
            log.warn("复制阶段配置参数无效, sourceTemplateId: {}, targetTemplateId: {}",
                    sourceTemplateId, targetTemplateId);
            throw new RuntimeException("复制参数无效");
        }

        // 获取源模板的阶段配置
        List<ProjectSchemaPhaseEntity> sourceConfigs = getListByTemplateId(sourceTemplateId);
        if (sourceConfigs.isEmpty()) {
            log.info("源模板没有阶段配置, sourceTemplateId: {}", sourceTemplateId);
            return 0;
        }

        // 复制配置到目标模板
        List<ProjectSchemaPhaseEntity> targetConfigs = new ArrayList<>();
        for (ProjectSchemaPhaseEntity sourceConfig : sourceConfigs) {
            ProjectSchemaPhaseEntity targetConfig = new ProjectSchemaPhaseEntity();

            // 复制属性
            targetConfig.setProjectTemplateId(targetTemplateId);
            targetConfig.setPhaseLibraryId(sourceConfig.getPhaseLibraryId());
            targetConfig.setSeqNo(sourceConfig.getSeqNo());
            targetConfig.setDuration(sourceConfig.getDuration());
            targetConfig.setCompletionWeight(sourceConfig.getCompletionWeight());
            targetConfig.setCanCut(sourceConfig.getCanCut());
            targetConfig.setApprovalId(sourceConfig.getApprovalId());
            targetConfig.setChecklistId(sourceConfig.getChecklistId());
            targetConfig.setWorkproductTplId(sourceConfig.getWorkproductTplId());

            // 生成新ID
            targetConfig.setId(RandomUtil.snowId());

            targetConfigs.add(targetConfig);
        }

        // 批量保存
        boolean success = this.saveBatch(targetConfigs);
        if (!success) {
            log.error("复制阶段配置失败");
            throw new RuntimeException("复制阶段配置失败");
        }

        log.info("复制阶段配置成功, 复制数量: {}", targetConfigs.size());
        return targetConfigs.size();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSave(String projectTemplateId, List<ProjectSchemaPhaseEntity> phaseConfigs) {
        if (StrXhUtil.isEmpty(projectTemplateId) || phaseConfigs == null || phaseConfigs.isEmpty()) {
            log.warn("批量保存阶段配置参数无效");
            return;
        }

        // 先删除原有配置
        deleteByTemplateId(projectTemplateId);

        // 设置模板ID和生成ID
        for (int i = 0; i < phaseConfigs.size(); i++) {
            ProjectSchemaPhaseEntity config = phaseConfigs.get(i);
            config.setProjectTemplateId(projectTemplateId);

            if (StrXhUtil.isEmpty(config.getId())) {
                config.setId(RandomUtil.snowId());
            }

            // 如果没有设置序号，按顺序设置
            if (config.getSeqNo() == null) {
                config.setSeqNo(i + 1);
            }

            // 设置默认值
            if (config.getCanCut() == null) {
                config.setCanCut(1);
            }
        }

        // 批量保存
        boolean success = this.saveBatch(phaseConfigs);
        if (!success) {
            log.error("批量保存阶段配置失败");
            throw new RuntimeException("批量保存阶段配置失败");
        }

        log.info("批量保存阶段配置成功, 保存数量: {}", phaseConfigs.size());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSeqNo(String id, Integer seqNo) {
        if (StrXhUtil.isEmpty(id) || seqNo == null) {
            log.warn("更新序号参数无效, ID: {}, seqNo: {}", id, seqNo);
            throw new RuntimeException("更新序号参数无效");
        }

        UpdateWrapper<ProjectSchemaPhaseEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                    .eq(ProjectSchemaPhaseEntity::getId, id)
                    .set(ProjectSchemaPhaseEntity::getSeqNo, seqNo);

        boolean success = this.update(updateWrapper);
        if (!success) {
            log.error("更新序号失败, ID: {}, seqNo: {}", id, seqNo);
            throw new RuntimeException("更新序号失败");
        }

        log.info("更新序号成功, ID: {}, seqNo: {}", id, seqNo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateSeqNo(Map<String, Integer> seqNoMap) {
        if (seqNoMap == null || seqNoMap.isEmpty()) {
            log.warn("批量更新序号参数为空");
            return;
        }

        for (Map.Entry<String, Integer> entry : seqNoMap.entrySet()) {
            updateSeqNo(entry.getKey(), entry.getValue());
        }

        log.info("批量更新序号成功, 更新数量: {}", seqNoMap.size());
    }

    @Override
    public Map<String, Object> getPhaseConfigUsageInfo(String id) {
        Map<String, Object> usageInfo = new HashMap<>();

        if (StrXhUtil.isEmpty(id)) {
            usageInfo.put("usageCount", 0);
            usageInfo.put("usageDetails", new ArrayList<>());
            return usageInfo;
        }

        // TODO: 实现使用情况统计
        // 查询该阶段配置被哪些项目使用
        usageInfo.put("usageCount", 0);
        usageInfo.put("usageDetails", new ArrayList<>());

        log.info("获取阶段配置使用情况, ID: {}", id);
        return usageInfo;
    }

    @Override
    public Integer getNextSeqNo(String projectTemplateId) {
        if (StrXhUtil.isEmpty(projectTemplateId)) {
            return 1;
        }

        QueryWrapper<ProjectSchemaPhaseEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                   .eq(ProjectSchemaPhaseEntity::getProjectTemplateId, projectTemplateId)
                   .orderByDesc(ProjectSchemaPhaseEntity::getSeqNo)
                   .last("LIMIT 1");

        ProjectSchemaPhaseEntity lastEntity = this.getOne(queryWrapper);
        if (lastEntity == null || lastEntity.getSeqNo() == null) {
            return 1;
        }

        return lastEntity.getSeqNo() + 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void adjustSeqNo(String id, String direction) {
        if (StrXhUtil.isEmpty(id) || StrXhUtil.isEmpty(direction)) {
            log.warn("调整序号参数无效, ID: {}, direction: {}", id, direction);
            throw new RuntimeException("调整序号参数无效");
        }

        ProjectSchemaPhaseEntity entity = this.getById(id);
        if (entity == null) {
            log.warn("调整序号的阶段配置不存在, ID: {}", id);
            throw new RuntimeException("阶段配置不存在");
        }

        Integer currentSeqNo = entity.getSeqNo();
        if (currentSeqNo == null) {
            log.warn("当前阶段配置序号为空, ID: {}", id);
            throw new RuntimeException("当前阶段配置序号为空");
        }

        // 获取同模板下的所有阶段配置
        List<ProjectSchemaPhaseEntity> allConfigs = getListByTemplateId(entity.getProjectTemplateId());

        ProjectSchemaPhaseEntity targetEntity = null;
        if ("up".equals(direction)) {
            // 上移：找到序号比当前小的最大序号
            for (ProjectSchemaPhaseEntity config : allConfigs) {
                if (config.getSeqNo() != null && config.getSeqNo() < currentSeqNo) {
                    if (targetEntity == null || config.getSeqNo() > targetEntity.getSeqNo()) {
                        targetEntity = config;
                    }
                }
            }
        } else if ("down".equals(direction)) {
            // 下移：找到序号比当前大的最小序号
            for (ProjectSchemaPhaseEntity config : allConfigs) {
                if (config.getSeqNo() != null && config.getSeqNo() > currentSeqNo) {
                    if (targetEntity == null || config.getSeqNo() < targetEntity.getSeqNo()) {
                        targetEntity = config;
                    }
                }
            }
        } else {
            log.warn("不支持的调整方向: {}", direction);
            throw new RuntimeException("不支持的调整方向");
        }

        if (targetEntity == null) {
            log.warn("无法调整序号，已经是边界位置, ID: {}, direction: {}", id, direction);
            throw new RuntimeException("无法调整序号，已经是边界位置");
        }

        // 交换序号
        Integer targetSeqNo = targetEntity.getSeqNo();
        updateSeqNo(id, targetSeqNo);
        updateSeqNo(targetEntity.getId(), currentSeqNo);

        log.info("调整序号成功, ID: {}, direction: {}, 原序号: {}, 新序号: {}",
                id, direction, currentSeqNo, targetSeqNo);
    }
}
