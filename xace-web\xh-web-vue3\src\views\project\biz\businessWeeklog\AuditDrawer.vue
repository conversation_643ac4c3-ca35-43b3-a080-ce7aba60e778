<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" title="商机周报审核" width="900px" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { BasicForm, useForm } from '/@/components/Form';
  import { auditFormSchema } from './businessWeeklog.data';
  import { auditBusinessWeeklog } from '/@/api/project/biz/businessWeeklog';
  import { useMessage } from '/@/hooks/web/useMessage';

  const emit = defineEmits(['success']);
  const { createMessage } = useMessage();
  const recordId = ref('');

  // 注册表单
  const [registerForm, { setFieldsValue, validate, resetFields }] = useForm({
    labelWidth: 120,
    schemas: auditFormSchema,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });

  // 注册抽屉
  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async data => {
    await resetFields();
    setDrawerProps({ confirmLoading: false });

    recordId.value = data.record?.id || '';

    if (data.record) {
      const formData = {
        projName: data.record.projName,
        ownName: data.record.ownName,
        dateRange: `${data.record.startDate} ~ ${data.record.endDate}`,
        note: data.record.note,
        plan: data.record.plan,
        risk: data.record.risk,
      };

      await setFieldsValue(formData);
    }
  });

  // 处理提交
  async function handleSubmit() {
    try {
      const values = await validate();
      setDrawerProps({ confirmLoading: true });

      // 构建审核表单数据
      const auditForm = {
        id: recordId.value,
        status: values.status,
        auditNote: values.auditNote || '',
      };

      await auditBusinessWeeklog(auditForm);

      if (values.status === 3) {
        createMessage.success('审核通过');
      } else {
        createMessage.success('已驳回');
      }

      emit('success');
      closeDrawer();
    } catch (error) {
      console.error('审核失败:', error);
      createMessage.error('审核失败');
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
