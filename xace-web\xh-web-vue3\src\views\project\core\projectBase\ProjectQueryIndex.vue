<template>
  <div class="project-query-index">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">
            <Icon icon="ant-design:search-outlined" />
            项目查询
          </h1>
          <p class="page-description">快速查找和管理您的项目</p>
        </div>
        <div class="action-section">
          <a-button type="primary" @click="handleCreateProject" v-auth="'project:core:project:create'">
            <Icon icon="ant-design:plus-outlined" />
            新增项目
          </a-button>
        </div>
      </div>
    </div>

    <!-- 快速访问卡片 -->
    <div class="quick-access-cards">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="8" :lg="6" v-for="card in quickAccessCards" :key="card.key">
          <a-card :hoverable="true" class="quick-access-card" @click="handleQuickAccess(card.key)">
            <div class="card-content">
              <div class="card-icon">
                <Icon :icon="card.icon" :size="32" :color="card.color" />
              </div>
              <div class="card-info">
                <h3 class="card-title">{{ card.title }}</h3>
                <p class="card-description">{{ card.description }}</p>
                <div class="card-count" v-if="card.count !== undefined">
                  <span class="count-number">{{ card.count }}</span>
                  <span class="count-label">个项目</span>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 项目查询组件 -->
    <div class="query-content">
      <ProjectQuery ref="projectQueryRef" />
    </div>

    <!-- 项目创建模态框 -->
    <ProjectBaseModal @register="registerModal" @success="handleCreateSuccess" />
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { Icon } from '/@/components/Icon';
  import { useModal } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { usePermission } from '/@/hooks/web/usePermission';

  import ProjectQuery from './ProjectQuery.vue';
  import ProjectBaseModal from './ProjectBaseModal.vue';
  import { getProjectStatistics } from '/@/api/project/projectBase';

  defineOptions({ name: 'ProjectQueryIndex' });

  const router = useRouter();
  const { createMessage } = useMessage();
  const { hasPermission } = usePermission();
  const [registerModal, { openModal }] = useModal();
  const projectQueryRef = ref();

  // 快速访问卡片配置
  const quickAccessCards = reactive([
    {
      key: 'RECENT_VISITED',
      title: '最近访问',
      description: '查看最近访问的项目',
      icon: 'ant-design:clock-circle-outlined',
      color: '#1890ff',
      count: 0,
    },
    {
      key: 'MY_FAVORITE',
      title: '我关注的',
      description: '查看我关注的项目',
      icon: 'ant-design:star-outlined',
      color: '#faad14',
      count: 0,
    },
    {
      key: 'MY_MANAGED',
      title: '我管理的',
      description: '查看我作为项目经理的项目',
      icon: 'ant-design:user-outlined',
      color: '#52c41a',
      count: 0,
    },
    {
      key: 'MY_PARTICIPATED',
      title: '我参与的',
      description: '查看我参与的项目团队',
      icon: 'ant-design:team-outlined',
      color: '#722ed1',
      count: 0,
    },
    {
      key: 'ADVANCED',
      title: '高级查询',
      description: '使用复杂条件查询项目',
      icon: 'ant-design:search-outlined',
      color: '#13c2c2',
    },
    {
      key: 'ALL',
      title: '全部项目',
      description: '查看所有项目列表',
      icon: 'ant-design:project-outlined',
      color: '#eb2f96',
      count: 0,
    },
  ]);

  // 快速访问处理
  function handleQuickAccess(queryType: string) {
    if (projectQueryRef.value) {
      projectQueryRef.value.switchToQueryType(queryType);
    }
  }

  // 创建项目
  function handleCreateProject() {
    openModal(true, {
      isUpdate: false,
    });
  }

  // 创建成功回调
  function handleCreateSuccess() {
    createMessage.success('项目创建成功');
    if (projectQueryRef.value) {
      projectQueryRef.value.reload();
    }
    loadStatistics();
  }

  // 加载统计数据
  async function loadStatistics() {
    try {
      const statistics = await getProjectStatistics({});

      // 更新卡片统计数据
      quickAccessCards.forEach(card => {
        switch (card.key) {
          case 'RECENT_VISITED':
            card.count = statistics.recentVisitedCount || 0;
            break;
          case 'MY_FAVORITE':
            card.count = statistics.myFavoriteCount || 0;
            break;
          case 'MY_MANAGED':
            card.count = statistics.myManagedCount || 0;
            break;
          case 'MY_PARTICIPATED':
            card.count = statistics.myParticipatedCount || 0;
            break;
          case 'ALL':
            card.count = statistics.totalCount || 0;
            break;
        }
      });
    } catch (error) {
      console.warn('加载项目统计数据失败:', error);
    }
  }

  onMounted(() => {
    loadStatistics();
  });
</script>

<style lang="less" scoped>
  .project-query-index {
    .page-header {
      background: #fff;
      padding: 24px;
      margin-bottom: 24px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

      .header-content {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .title-section {
          .page-title {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
            color: #262626;
            display: flex;
            align-items: center;
            gap: 8px;
          }

          .page-description {
            margin: 8px 0 0;
            color: #8c8c8c;
            font-size: 14px;
          }
        }

        .action-section {
          .ant-btn {
            height: 40px;
            padding: 0 20px;
            font-size: 14px;
          }
        }
      }
    }

    .quick-access-cards {
      margin-bottom: 24px;

      .quick-access-card {
        height: 120px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
        }

        .card-content {
          display: flex;
          align-items: center;
          height: 100%;
          gap: 16px;

          .card-icon {
            flex-shrink: 0;
          }

          .card-info {
            flex: 1;
            min-width: 0;

            .card-title {
              margin: 0 0 4px;
              font-size: 16px;
              font-weight: 600;
              color: #262626;
            }

            .card-description {
              margin: 0 0 8px;
              font-size: 12px;
              color: #8c8c8c;
              line-height: 1.4;
            }

            .card-count {
              display: flex;
              align-items: baseline;
              gap: 4px;

              .count-number {
                font-size: 20px;
                font-weight: 600;
                color: #1890ff;
              }

              .count-label {
                font-size: 12px;
                color: #8c8c8c;
              }
            }
          }
        }
      }
    }

    .query-content {
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      overflow: hidden;
    }
  }

  @media (max-width: 768px) {
    .project-query-index {
      .page-header {
        padding: 16px;

        .header-content {
          flex-direction: column;
          align-items: flex-start;
          gap: 16px;
        }
      }

      .quick-access-cards {
        .quick-access-card {
          height: 100px;

          .card-content {
            .card-info {
              .card-title {
                font-size: 14px;
              }

              .card-description {
                font-size: 11px;
              }

              .card-count {
                .count-number {
                  font-size: 16px;
                }
              }
            }
          }
        }
      }
    }
  }
</style>
