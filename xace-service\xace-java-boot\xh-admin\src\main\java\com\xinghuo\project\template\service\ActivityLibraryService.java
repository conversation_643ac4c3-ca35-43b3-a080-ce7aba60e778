package com.xinghuo.project.template.service;

import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.project.template.entity.ActivityLibraryEntity;
import com.xinghuo.project.template.model.ActivityLibraryPagination;

import java.util.List;
import java.util.Map;

/**
 * 标准项目活动库服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
public interface ActivityLibraryService extends BaseService<ActivityLibraryEntity> {

    /**
     * 获取活动库列表
     *
     * @param pagination 分页参数
     * @return 活动库列表
     */
    List<ActivityLibraryEntity> getList(ActivityLibraryPagination pagination);

    /**
     * 根据状态获取活动库列表
     *
     * @param status 状态
     * @return 活动库列表
     */
    List<ActivityLibraryEntity> getListByStatus(Integer status);

    /**
     * 获取活动库详情
     *
     * @param id 活动库ID
     * @return 活动库信息
     */
    ActivityLibraryEntity getInfo(String id);

    /**
     * 创建活动库
     *
     * @param entity 活动库信息
     * @return 活动库ID
     */
    String create(ActivityLibraryEntity entity);

    /**
     * 更新活动库
     *
     * @param id 活动库ID
     * @param entity 活动库信息
     */
    void update(String id, ActivityLibraryEntity entity);

    /**
     * 删除活动库
     *
     * @param id 活动库ID
     */
    void delete(String id);

    /**
     * 批量删除活动库
     *
     * @param ids 活动库ID列表
     */
    void batchDelete(List<String> ids);

    /**
     * 更新活动库状态
     *
     * @param id 活动库ID
     * @param status 状态
     */
    void updateStatus(String id, Integer status);

    /**
     * 批量更新状态
     *
     * @param ids 活动库ID列表
     * @param status 状态
     */
    void batchUpdateStatus(List<String> ids, Integer status);

    /**
     * 检查活动编码是否存在
     *
     * @param code 活动编码
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean isExistByCode(String code, String excludeId);

    /**
     * 根据编码获取活动库
     *
     * @param code 活动编码
     * @return 活动库信息
     */
    ActivityLibraryEntity getByCode(String code);

    /**
     * 获取活动库选择列表
     *
     * @param keyword 关键字
     * @return 活动库列表
     */
    List<ActivityLibraryEntity> getSelectList(String keyword);

    /**
     * 启用活动库
     *
     * @param id 活动库ID
     */
    void enable(String id);

    /**
     * 禁用活动库
     *
     * @param id 活动库ID
     */
    void disable(String id);

    /**
     * 复制活动库
     *
     * @param id 源活动库ID
     * @param newName 新名称
     * @return 新活动库ID
     */
    String copy(String id, String newName);

    /**
     * 生成活动编码
     *
     * @return 活动编码
     */
    String generateCode();

    /**
     * 获取活动库使用情况
     *
     * @param id 活动库ID
     * @return 使用情况统计
     */
    Map<String, Object> getActivityLibraryUsageInfo(String id);

    /**
     * 根据活动类型获取活动库列表
     *
     * @param activityTypeId 活动类型ID
     * @return 活动库列表
     */
    List<ActivityLibraryEntity> getListByActivityType(String activityTypeId);

    /**
     * 根据里程碑标识获取活动库列表
     *
     * @param isMilestone 是否是里程碑
     * @return 活动库列表
     */
    List<ActivityLibraryEntity> getListByMilestone(Integer isMilestone);

    /**
     * 检查活动名称是否存在
     *
     * @param name 活动名称
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean isExistByName(String name, String excludeId);
}
