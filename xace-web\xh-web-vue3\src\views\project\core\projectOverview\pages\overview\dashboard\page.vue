<template>
  <div class="project-overview-dashboard p-4">
    <!-- 页面标题 -->
    <div class="page-header mb-4">
      <h2 class="text-xl font-semibold mb-2">项目概况</h2>
      <p class="text-gray-600">项目整体进展情况和关键指标一览</p>
    </div>

    <a-spin :spinning="loading">
      <!-- 顶部汇总卡片 -->
      <div class="summary-cards grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <!-- 项目进度 -->
        <div class="summary-card bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
              <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                <PercentageOutlined class="text-blue-600" />
              </div>
              <div>
                <h3 class="font-medium text-gray-900">项目进度</h3>
                <p class="text-sm text-gray-500">总体完成情况</p>
              </div>
            </div>
          </div>
          <div class="space-y-3">
            <div class="flex items-center justify-between">
              <span class="text-2xl font-bold text-blue-600">{{ dashboardData.projectSchedule.percentage }}%</span>
              <a-tag color="processing">进行中</a-tag>
            </div>
            <a-progress :percent="dashboardData.projectSchedule.percentage" :show-info="false" stroke-color="#1890ff" />
            <div class="flex justify-between text-sm text-gray-600">
              <span>计划: {{ dashboardData.projectSchedule.planned }}天</span>
              <span>实际: {{ dashboardData.projectSchedule.actual }}天</span>
            </div>
          </div>
        </div>

        <!-- 项目交付物 -->
        <div class="summary-card bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
              <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                <FileTextOutlined class="text-green-600" />
              </div>
              <div>
                <h3 class="font-medium text-gray-900">项目交付物</h3>
                <p class="text-sm text-gray-500">交付物状态</p>
              </div>
            </div>
          </div>
          <div class="space-y-3">
            <div class="flex items-center justify-between">
              <span class="text-2xl font-bold text-green-600">{{ dashboardData.projectDeliverables.total }}</span>
              <a-tag color="success">{{ dashboardData.projectDeliverables.completed }}已完成</a-tag>
            </div>
            <div class="grid grid-cols-3 gap-2 text-xs">
              <div class="text-center">
                <div class="font-medium text-green-600">{{ dashboardData.projectDeliverables.completed }}</div>
                <div class="text-gray-500">已完成</div>
              </div>
              <div class="text-center">
                <div class="font-medium text-orange-600">{{ dashboardData.projectDeliverables.pending }}</div>
                <div class="text-gray-500">进行中</div>
              </div>
              <div class="text-center">
                <div class="font-medium text-gray-600">{{ dashboardData.projectDeliverables.planned }}</div>
                <div class="text-gray-500">计划中</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 项目任务 -->
        <div class="summary-card bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
              <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                <CheckSquareOutlined class="text-orange-600" />
              </div>
              <div>
                <h3 class="font-medium text-gray-900">项目任务</h3>
                <p class="text-sm text-gray-500">任务完成情况</p>
              </div>
            </div>
          </div>
          <div class="space-y-3">
            <div class="flex items-center justify-between">
              <span class="text-2xl font-bold text-orange-600">{{ dashboardData.projectTask.total }}</span>
              <a-tag color="orange">{{ dashboardData.projectTask.completed }}已完成</a-tag>
            </div>
            <a-progress
              :percent="Math.round((dashboardData.projectTask.completed / dashboardData.projectTask.total) * 100)"
              :show-info="false"
              stroke-color="#fa8c16" />
            <div class="text-sm text-gray-600"> 完成率: {{ Math.round((dashboardData.projectTask.completed / dashboardData.projectTask.total) * 100) }}% </div>
          </div>
        </div>

        <!-- 项目团队 -->
        <div class="summary-card bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
              <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                <TeamOutlined class="text-purple-600" />
              </div>
              <div>
                <h3 class="font-medium text-gray-900">项目团队</h3>
                <p class="text-sm text-gray-500">团队成员构成</p>
              </div>
            </div>
          </div>
          <div class="space-y-3">
            <div class="flex items-center justify-between">
              <span class="text-2xl font-bold text-purple-600">{{ dashboardData.projectTeam.total }}</span>
              <a-tag color="purple">成员</a-tag>
            </div>
            <div class="grid grid-cols-2 gap-2 text-xs">
              <div class="text-center">
                <div class="font-medium text-purple-600">{{ dashboardData.projectTeam.internal }}</div>
                <div class="text-gray-500">内部</div>
              </div>
              <div class="text-center">
                <div class="font-medium text-blue-600">{{ dashboardData.projectTeam.external }}</div>
                <div class="text-gray-500">外部</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 阶段进度和项目信息 -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <!-- 阶段进度 -->
        <div class="lg:col-span-2 bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold">阶段</h3>
            <a-button type="link" size="small">查看详情</a-button>
          </div>
          <div class="phase-timeline">
            <div class="flex items-center justify-between mb-4">
              <div v-for="(phase, index) in phaseData" :key="phase.id" class="flex-1 relative">
                <div class="flex flex-col items-center">
                  <div
                    :class="[
                      'w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium relative z-10',
                      phase.status === 'completed'
                        ? 'bg-green-500 text-white'
                        : phase.status === 'current'
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-300 text-gray-600',
                    ]">
                    {{ phase.seqNo }}
                  </div>
                  <div class="mt-2 text-xs text-center max-w-16">
                    <div class="font-medium">{{ phase.name }}</div>
                  </div>
                </div>
                <!-- 连接线 -->
                <div
                  v-if="index < phaseData.length - 1"
                  :class="[
                    'absolute top-4 left-1/2 h-0.5 transform -translate-y-1/2',
                    'w-full z-0',
                    phase.status === 'completed' ? 'bg-green-500' : 'bg-gray-300',
                  ]"
                  style="margin-left: 16px; width: calc(100% - 32px)"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 项目信息 -->
        <div class="bg-white rounded-lg shadow-sm border p-6">
          <h3 class="text-lg font-semibold mb-4">项目信息</h3>
          <div class="space-y-4">
            <div class="project-info-item">
              <div class="text-sm text-gray-500 mb-1">项目名称</div>
              <div class="font-medium">{{ projectInfo.name }}</div>
            </div>
            <div class="project-info-item">
              <div class="text-sm text-gray-500 mb-1">项目经理</div>
              <div class="font-medium">{{ projectInfo.manager }}</div>
            </div>
            <div class="project-info-item">
              <div class="text-sm text-gray-500 mb-1">开始日期</div>
              <div class="font-medium">{{ projectInfo.startDate }}</div>
            </div>
            <div class="project-info-item">
              <div class="text-sm text-gray-500 mb-1">结束日期</div>
              <div class="font-medium">{{ projectInfo.endDate }}</div>
            </div>
            <div class="project-info-item">
              <div class="text-sm text-gray-500 mb-1">项目状态</div>
              <a-tag :color="getStatusColor(projectInfo.status)">{{ projectInfo.status }}</a-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 里程碑进度 -->
      <div class="bg-white rounded-lg shadow-sm border p-6 mb-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold">项目里程碑进度</h3>
          <a-button type="link" size="small">查看全部</a-button>
        </div>
        <div class="milestone-chart">
          <div class="space-y-4">
            <div v-for="milestone in milestoneData" :key="milestone.id" class="milestone-item flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div class="flex items-center">
                <div
                  :class="[
                    'w-3 h-3 rounded-full mr-3',
                    milestone.status === 'completed'
                      ? 'bg-green-500'
                      : milestone.status === 'current'
                      ? 'bg-blue-500'
                      : milestone.status === 'overdue'
                      ? 'bg-red-500'
                      : 'bg-gray-400',
                  ]"></div>
                <div>
                  <div class="font-medium">{{ milestone.name }}</div>
                  <div class="text-sm text-gray-500">{{ milestone.description }}</div>
                </div>
              </div>
              <div class="text-right">
                <div class="text-sm text-gray-600">{{ milestone.dueDate }}</div>
                <a-tag
                  :color="
                    milestone.status === 'completed' ? 'green' : milestone.status === 'current' ? 'blue' : milestone.status === 'overdue' ? 'red' : 'default'
                  "
                  size="small">
                  {{ milestone.statusText }}
                </a-tag>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 活动完成率和风险管理 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- 项目活动 -->
        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold">项目活动</h3>
            <a-button type="link" size="small">查看详情</a-button>
          </div>
          <div class="activity-metrics space-y-4">
            <div class="metric-item">
              <div class="flex justify-between items-center mb-2">
                <span class="text-gray-600">总活动数</span>
                <span class="font-bold">{{ dashboardData.projectActivity.total }}</span>
              </div>
            </div>
            <div class="metric-item">
              <div class="flex justify-between items-center mb-2">
                <span class="text-gray-600">按期完成率</span>
                <span class="font-bold text-green-600">{{ dashboardData.activityCompletionRate.percentage }}%</span>
              </div>
              <a-progress :percent="dashboardData.activityCompletionRate.percentage" stroke-color="#52c41a" />
            </div>
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div class="text-center p-3 bg-green-50 rounded">
                <div class="font-bold text-green-600">{{ dashboardData.activityCompletionRate.onTime }}</div>
                <div class="text-gray-600">按时完成</div>
              </div>
              <div class="text-center p-3 bg-red-50 rounded">
                <div class="font-bold text-red-600">{{ dashboardData.activityCompletionRate.overdue }}</div>
                <div class="text-gray-600">逾期</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 风险管理 -->
        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold">风险</h3>
            <a-button type="link" size="small">管理风险</a-button>
          </div>
          <div class="risk-overview space-y-4">
            <div
              v-for="risk in riskData"
              :key="risk.level"
              class="risk-item flex items-center justify-between p-3 rounded-lg"
              :class="getRiskBgClass(risk.level)">
              <div class="flex items-center">
                <WarningOutlined :class="['mr-2', risk.level === 'high' ? 'text-red-600' : risk.level === 'medium' ? 'text-orange-600' : 'text-yellow-600']" />
                <span class="font-medium">{{ risk.name }}</span>
              </div>
              <div class="flex items-center">
                <span class="text-lg font-bold mr-2">{{ risk.count }}</span>
                <a-tag :color="risk.level === 'high' ? 'red' : risk.level === 'medium' ? 'orange' : 'gold'" size="small">
                  {{ risk.level === 'high' ? '高' : risk.level === 'medium' ? '中' : '低' }}
                </a-tag>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 工作包和问题跟踪 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 主计划工作包 -->
        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold">主计划工作包</h3>
            <a-button type="link" size="small">查看详情</a-button>
          </div>
          <div class="work-package-list space-y-3">
            <div
              v-for="workPackage in workPackageData"
              :key="workPackage.id"
              class="work-package-item flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div class="flex items-center">
                <div
                  :class="[
                    'w-2 h-2 rounded-full mr-3',
                    workPackage.status === 'completed' ? 'bg-green-500' : workPackage.status === 'inProgress' ? 'bg-blue-500' : 'bg-gray-400',
                  ]"></div>
                <div>
                  <div class="font-medium">{{ workPackage.name }}</div>
                  <div class="text-sm text-gray-500">{{ workPackage.progress }}% 完成</div>
                </div>
              </div>
              <a-progress :percent="workPackage.progress" size="small" style="width: 60px" :show-info="false" />
            </div>
          </div>
        </div>

        <!-- 问题跟踪 -->
        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold">问题</h3>
            <a-button type="link" size="small">管理问题</a-button>
          </div>
          <div class="issue-overview space-y-4">
            <div
              v-for="issue in issueData"
              :key="issue.priority"
              class="issue-item flex items-center justify-between p-3 rounded-lg"
              :class="getIssueBgClass(issue.priority)">
              <div class="flex items-center">
                <ExclamationCircleOutlined
                  :class="['mr-2', issue.priority === 'high' ? 'text-red-600' : issue.priority === 'medium' ? 'text-orange-600' : 'text-blue-600']" />
                <span class="font-medium">{{ issue.name }}</span>
              </div>
              <div class="flex items-center">
                <span class="text-lg font-bold mr-2">{{ issue.count }}</span>
                <a-tag :color="issue.priority === 'high' ? 'red' : issue.priority === 'medium' ? 'orange' : 'blue'" size="small">
                  {{ issue.priority === 'high' ? '高' : issue.priority === 'medium' ? '中' : '低' }}
                </a-tag>
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import { PercentageOutlined, FileTextOutlined, CheckSquareOutlined, TeamOutlined, WarningOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';

  // 响应式数据
  const loading = ref(false);

  // 仪表盘数据
  const dashboardData = reactive({
    projectSchedule: {
      percentage: 18,
      planned: 180,
      actual: 32,
    },
    projectDeliverables: {
      total: 16,
      completed: 3,
      pending: 8,
      planned: 5,
    },
    projectTask: {
      total: 11,
      completed: 4,
    },
    projectTeam: {
      total: 36,
      internal: 28,
      external: 8,
    },
    projectActivity: {
      total: 45,
      completionRate: 72,
    },
    activityCompletionRate: {
      percentage: 72,
      onTime: 32,
      overdue: 13,
    },
  });

  // 项目信息
  const projectInfo = reactive({
    name: '企业级项目管理系统',
    manager: '张三',
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    status: '进行中',
  });

  // 阶段数据
  const phaseData = ref([
    { id: '1', name: '需求分析', seqNo: 1, status: 'completed' },
    { id: '2', name: '系统设计', seqNo: 2, status: 'completed' },
    { id: '3', name: '开发实施', seqNo: 3, status: 'current' },
    { id: '4', name: '系统测试', seqNo: 4, status: 'pending' },
    { id: '5', name: '用户验收', seqNo: 5, status: 'pending' },
    { id: '6', name: '项目收尾', seqNo: 6, status: 'pending' },
  ]);

  // 里程碑数据
  const milestoneData = ref([
    {
      id: '1',
      name: '需求评审完成',
      description: '完成业务需求分析和评审',
      dueDate: '2024-02-28',
      status: 'completed',
      statusText: '已完成',
    },
    {
      id: '2',
      name: '设计文档确认',
      description: '系统架构和详细设计文档确认',
      dueDate: '2024-04-15',
      status: 'current',
      statusText: '进行中',
    },
    {
      id: '3',
      name: '核心功能开发',
      description: '完成核心业务功能开发',
      dueDate: '2024-07-31',
      status: 'pending',
      statusText: '计划中',
    },
    {
      id: '4',
      name: '系统集成测试',
      description: '完成系统集成和性能测试',
      dueDate: '2024-10-15',
      status: 'pending',
      statusText: '计划中',
    },
  ]);

  // 风险数据
  const riskData = ref([
    { level: 'high', name: '高风险', count: 2 },
    { level: 'medium', name: '中风险', count: 5 },
    { level: 'low', name: '低风险', count: 8 },
  ]);

  // 工作包数据
  const workPackageData = ref([
    { id: '1', name: '用户管理模块', progress: 85, status: 'inProgress' },
    { id: '2', name: '权限控制模块', progress: 100, status: 'completed' },
    { id: '3', name: '报表系统', progress: 45, status: 'inProgress' },
    { id: '4', name: '数据接口', progress: 20, status: 'inProgress' },
    { id: '5', name: '系统配置', progress: 0, status: 'pending' },
  ]);

  // 问题数据
  const issueData = ref([
    { priority: 'high', name: '高优先级', count: 3 },
    { priority: 'medium', name: '中优先级', count: 7 },
    { priority: 'low', name: '低优先级', count: 12 },
  ]);

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      进行中: 'processing',
      已完成: 'success',
      已暂停: 'warning',
      已取消: 'error',
    };
    return colorMap[status] || 'default';
  };

  // 获取风险背景样式
  const getRiskBgClass = (level: string) => {
    const classMap: Record<string, string> = {
      high: 'bg-red-50 border border-red-200',
      medium: 'bg-orange-50 border border-orange-200',
      low: 'bg-yellow-50 border border-yellow-200',
    };
    return classMap[level] || 'bg-gray-50';
  };

  // 获取问题背景样式
  const getIssueBgClass = (priority: string) => {
    const classMap: Record<string, string> = {
      high: 'bg-red-50 border border-red-200',
      medium: 'bg-orange-50 border border-orange-200',
      low: 'bg-blue-50 border border-blue-200',
    };
    return classMap[priority] || 'bg-gray-50';
  };

  // 加载数据
  const loadData = async () => {
    loading.value = true;
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 800));

      console.log('项目概况数据加载完成');
    } catch (error) {
      console.error('加载失败:', error);
      message.error('加载失败，请刷新页面重试');
    } finally {
      loading.value = false;
    }
  };

  // 页面初始化
  onMounted(() => {
    loadData();
  });
</script>

<style lang="less" scoped>
  .project-overview-dashboard {
    min-height: 100vh;
    background-color: var(--section-bg-color);
  }

  .summary-card {
    transition: all 0.3s ease;
  }

  .summary-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  }

  .phase-timeline {
    position: relative;
  }

  .milestone-item:hover {
    background-color: #f0f0f0;
  }

  .work-package-item:hover,
  .risk-item:hover,
  .issue-item:hover {
    background-color: #ffffff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .project-info-item {
    padding-bottom: 12px;
    border-bottom: 1px solid #f0f0f0;
  }

  .project-info-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
  }

  :deep(.ant-progress-inner) {
    background-color: var(--section-bg-color);
  }

  :deep(.ant-tag) {
    margin: 0;
  }
</style>
