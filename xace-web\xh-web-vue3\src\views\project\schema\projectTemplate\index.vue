<template>
  <Layout v-if="menuData.length" :menu-data="menuData" @onChangeMain="data => onMenuChange(data)" @onChangeSub="data => onSubMenuChange(data)">
    <template #header>
      <div class="px-[8px] relative flex items-center">
        <TemplateSelector :key="renderKey" v-model:value="templateId" placeholder="选择项目模板进行配置" class="min-w-[300px]" />
        <Tooltip title="新增项目模板">
          <a-button class="ml-[8px]" type="primary" @click="handleCreate">
            <template #icon><PlusOutlined /></template>
            新增项目模板
          </a-button>
        </Tooltip>
      </div>
    </template>

    <!-- 模板列表页面 -->
    <div v-if="!templateId" class="template-list-container p-4">
      <div class="mb-4 flex justify-between items-center">
        <h2 class="text-xl font-semibold">项目模板</h2>
        <a-button type="primary" @click="handleCreate">
          <template #icon><PlusOutlined /></template>
          新增项目模板
        </a-button>
      </div>

      <!-- 说明文字 -->
      <div class="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
        <div class="text-sm text-blue-700">
          <InfoCircleOutlined class="mr-2" />
          点击项目模板卡片进行配置，左侧菜单将显示模板的详细配置页面
        </div>
      </div>

      <!-- 搜索和筛选 -->
      <div class="mb-6">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-input-search v-model:value="searchKeyword" placeholder="搜索模板名称或描述" @search="handleSearch" allow-clear />
          </a-col>
          <a-col :span="4">
            <a-select v-model:value="filterStatus" placeholder="状态筛选" allow-clear class="w-full">
              <a-select-option :value="0">启用</a-select-option>
              <a-select-option :value="1">禁用</a-select-option>
            </a-select>
          </a-col>
          <a-col :span="4">
            <a-select v-model:value="filterType" placeholder="类型筛选" allow-clear class="w-full">
              <!-- TODO: 从字典API加载 -->
            </a-select>
          </a-col>
        </a-row>
      </div>

      <!-- 模板卡片网格 -->
      <div class="template-grid">
        <a-row :gutter="[16, 16]">
          <a-col :xl="6" :lg="8" :md="12" :sm="24" v-for="template in filteredTemplates" :key="template.id">
            <a-card :hoverable="true" class="template-card h-full cursor-pointer" :class="{ disabled: template.status === 1 }" @click="handleConfig(template)">
              <template #cover>
                <div class="template-cover h-[120px] bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
                  <div class="text-center">
                    <Avatar :size="48" class="mb-2">
                      <template #icon>
                        <i :class="template.icon || 'icon-ym icon-ym-generator-template'" class="text-2xl"></i>
                      </template>
                    </Avatar>
                    <div class="text-sm text-gray-600">{{ template.code || 'TPL' }}</div>
                  </div>
                </div>
              </template>

              <template #actions>
                <Tooltip title="配置模板">
                  <SettingOutlined @click.stop="handleConfig(template)" />
                </Tooltip>
                <Tooltip title="查看详情">
                  <EyeOutlined @click.stop="handleView(template)" />
                </Tooltip>
                <Tooltip title="编辑">
                  <EditOutlined @click.stop="handleEdit(template)" />
                </Tooltip>
                <Tooltip title="复制">
                  <CopyOutlined @click.stop="handleCopy(template)" />
                </Tooltip>
                <a-dropdown>
                  <MoreOutlined class="cursor-pointer" />
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="handleToggleStatus(template)">
                        {{ getStatusToggleText(template.status) }}
                      </a-menu-item>
                      <a-menu-item @click="handleApplyToProjects(template)"> 应用到项目 </a-menu-item>
                      <a-menu-item @click="handleUsageInfo(template)"> 使用情况 </a-menu-item>
                      <a-menu-divider />
                      <a-menu-item danger @click="handleDelete(template)"> 删除 </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </template>

              <a-card-meta>
                <template #title>
                  <div class="flex items-center justify-between">
                    <span class="truncate">{{ template.name }}</span>
                    <a-tag :color="getStatusColor(template.status)" class="ml-2">
                      {{ getStatusText(template.status) }}
                    </a-tag>
                  </div>
                </template>
                <template #description>
                  <div class="template-description">
                    <p class="text-gray-600 text-xs mb-2 line-clamp-2">
                      {{ template.description || '暂无描述' }}
                    </p>
                    <div class="template-stats flex justify-between text-xs text-gray-500">
                      <span>WBS: {{ template.wbsCount || 0 }}</span>
                      <span>阶段: {{ template.phaseCount || 0 }}</span>
                      <span>使用: {{ template.usageCount || 0 }}</span>
                    </div>
                  </div>
                </template>
              </a-card-meta>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 空状态 -->
      <div v-if="filteredTemplates.length === 0" class="empty-state text-center py-16">
        <Empty :image="Empty.PRESENTED_IMAGE_SIMPLE" description="暂无项目模板">
          <a-button type="primary" @click="handleCreate"> 创建第一个项目模板 </a-button>
        </Empty>
      </div>
    </div>

    <!-- 模板配置页面 -->
    <Suspense v-else>
      <template #default>
        <keep-alive :key="templateId">
          <component :template-id="templateId" :is="activeComponent" @success="reload" />
        </keep-alive>
      </template>
      <template #fallback>
        <div class="w-full h-full flex justify-center items-center">
          <Spin :spinning="true" size="large" />
        </div>
      </template>
    </Suspense>

    <!-- 弹窗组件 -->
    <FormDrawer @register="registerFormDrawer" @success="handleSuccess" />
    <DetailDrawer @register="registerDetailDrawer" />
    <ProjectTemplateCopyModal @register="registerCopyModal" @success="handleSuccess" />
    <ApplyToProjectsModal @register="registerApplyModal" @success="handleSuccess" />
  </Layout>
</template>

<script setup lang="ts">
  import { ref, onMounted, markRaw, shallowRef, defineAsyncComponent, computed, watch } from 'vue';
  import {
    Spin,
    Tooltip,
    Card as ACard,
    CardMeta as ACardMeta,
    Row as ARow,
    Col as ACol,
    Input as AInput,
    InputSearch as AInputSearch,
    Select as ASelect,
    SelectOption as ASelectOption,
    Button as AButton,
    Tag as ATag,
    Avatar,
    Empty,
    Dropdown as ADropdown,
    Menu as AMenu,
    MenuItem as AMenuItem,
    MenuDivider as AMenuDivider,
  } from 'ant-design-vue';
  import { PlusOutlined, EyeOutlined, EditOutlined, CopyOutlined, SettingOutlined, MoreOutlined, InfoCircleOutlined } from '@ant-design/icons-vue';
  import { useModal } from '/@/components/Modal';
  import { useDrawer } from '/@/components/Drawer';
  import { useMessage } from '/@/hooks/web/useMessage';
  import Layout from '/@/views/project/portfolio/layout.vue';
  import Exception from '/@/views/basic/exception/Exception.vue';
  import TemplateSelector from './components/TemplateSelector.vue';
  import FormDrawer from './FormDrawer.vue';
  import DetailDrawer from './DetailDrawer.vue';
  import ProjectTemplateCopyModal from './ProjectTemplateCopyModal.vue';
  import ApplyToProjectsModal from './ApplyToProjectsModal.vue';
  import { getProjectTemplateList, deleteProjectTemplate, enableProjectTemplate, disableProjectTemplate } from '/@/api/project/projectTemplate';

  type AsyncComponentType = ReturnType<typeof defineAsyncComponent>;

  const { createMessage, createConfirm } = useMessage();

  // 菜单数据
  const menuData = ref([]);
  const activeComponent = shallowRef<AsyncComponentType | typeof Exception | null>(null);
  const activeMenuKey = ref('');
  const renderKey = ref(0);
  const componentCache = new Map<string, AsyncComponentType | typeof Exception | null>();

  // 模板相关
  const templateId = ref(undefined);
  const templateList = ref([]);
  const loading = ref(false);

  // 搜索和筛选
  const searchKeyword = ref('');
  const filterStatus = ref(undefined);
  const filterType = ref(undefined);

  // 抽屉和弹窗
  const [registerFormDrawer, { openDrawer: openFormDrawer }] = useDrawer();
  const [registerDetailDrawer, { openDrawer: openDetailDrawer }] = useDrawer();
  const [registerCopyModal, { openModal: openCopyModal }] = useModal();
  const [registerApplyModal, { openModal: openApplyModal }] = useModal();

  // 状态处理函数
  function getStatusText(status: number | string): string {
    const statusValue = typeof status === 'string' ? parseInt(status) : status;
    switch (statusValue) {
      case 0:
        return '启用';
      case 1:
        return '禁用';
      default:
        return '未知';
    }
  }

  function getStatusColor(status: number | string): string {
    const statusValue = typeof status === 'string' ? parseInt(status) : status;
    switch (statusValue) {
      case 0:
        return 'green';
      case 1:
        return 'red';
      default:
        return 'default';
    }
  }

  function getStatusToggleText(status: number | string): string {
    const statusValue = typeof status === 'string' ? parseInt(status) : status;
    return statusValue === 0 ? '禁用' : '启用';
  }

  // 计算属性：过滤后的模板列表
  const filteredTemplates = computed(() => {
    let result = templateList.value;

    // 关键字搜索
    if (searchKeyword.value) {
      const keyword = searchKeyword.value.toLowerCase();
      result = result.filter(
        template =>
          template.name?.toLowerCase().includes(keyword) ||
          template.description?.toLowerCase().includes(keyword) ||
          template.code?.toLowerCase().includes(keyword),
      );
    }

    // 状态筛选
    if (filterStatus.value !== undefined) {
      result = result.filter(template => template.status === filterStatus.value);
    }

    // 类型筛选
    if (filterType.value) {
      result = result.filter(template => template.typeId === filterType.value);
    }

    return result;
  });

  // 加载菜单配置
  async function loadMenuData() {
    try {
      const response = await fetch('/data/projectTemplateConfig.json');
      menuData.value = await response.json();
    } catch (error) {
      console.error('加载菜单配置失败:', error);
      menuData.value = [];
    }
  }

  // 加载模板列表
  async function loadTemplateList() {
    try {
      loading.value = true;
      const response = await getProjectTemplateList({
        pageSize: 100,
        currentPage: 1,
      });

      if (response.code === 200) {
        templateList.value = response.data?.list || [];
      } else {
        createMessage.error(response.msg || '加载模板列表失败');
      }
    } catch (error) {
      console.error('加载模板列表失败:', error);
      createMessage.error('加载模板列表失败，请稍后重试');
    } finally {
      loading.value = false;
    }
  }

  // 主菜单切换
  function onMenuChange(data: any) {
    activeMenuKey.value = data.enCode;
    loadComponent(data.enCode);
  }

  // 子菜单切换
  function onSubMenuChange(data: any) {
    activeMenuKey.value = data.enCode;
    loadComponent(data.enCode);
  }

  // 动态加载组件
  function loadComponent(enCode: string) {
    if (!enCode) return;

    if (componentCache.has(enCode)) {
      activeComponent.value = componentCache.get(enCode);
      return;
    }

    try {
      const component = defineAsyncComponent(() => import(`./pages/${enCode}/page.vue`));
      componentCache.set(enCode, markRaw(component));
      activeComponent.value = component;
    } catch (error) {
      console.error(`加载组件失败: ${enCode}`, error);
      const exceptionComponent = markRaw(Exception);
      componentCache.set(enCode, exceptionComponent);
      activeComponent.value = exceptionComponent;
    }
  }

  // 处理函数
  function handleCreate() {
    openFormDrawer(true, { isUpdate: false });
  }

  function handleEdit(template: any) {
    openFormDrawer(true, { record: template, isUpdate: true });
  }

  function handleView(template: any) {
    openDetailDrawer(true, { record: template });
  }

  function handleCopy(template: any) {
    openCopyModal(true, { record: template });
  }

  function handleConfig(template: any) {
    templateId.value = template.id;
    renderKey.value++;
    // 显示配置成功的提示
    createMessage.success(`已选择模板：${template.name}，请在左侧菜单进行配置`);
  }

  async function handleToggleStatus(template: any) {
    const action = getStatusToggleText(template.status);
    const apiCall = template.status === 0 ? disableProjectTemplate : enableProjectTemplate;

    createConfirm({
      title: `确认${action}`,
      content: `确定要${action}项目模板"${template.name}"吗？`,
      onOk: async () => {
        try {
          const response = await apiCall(template.id);
          if (response.code === 200) {
            createMessage.success(`${action}成功`);
            await loadTemplateList();
          } else {
            createMessage.error(response.msg || `${action}失败`);
          }
        } catch (error) {
          console.error(`${action}失败:`, error);
          createMessage.error(`${action}失败，请稍后重试`);
        }
      },
    });
  }

  function handleApplyToProjects(template: any) {
    openApplyModal(true, { record: template });
  }

  function handleUsageInfo(template: any) {
    createMessage.info('使用情况功能开发中...');
  }

  async function handleDelete(template: any) {
    createConfirm({
      title: '确认删除',
      content: `确定要删除项目模板"${template.name}"吗？此操作不可恢复。`,
      iconType: 'warning',
      onOk: async () => {
        try {
          const response = await deleteProjectTemplate(template.id);
          if (response.code === 200) {
            createMessage.success('删除成功');
            await loadTemplateList();
          } else {
            createMessage.error(response.msg || '删除失败');
          }
        } catch (error) {
          console.error('删除失败:', error);
          createMessage.error('删除失败，请稍后重试');
        }
      },
    });
  }

  function handleSearch() {
    // 搜索逻辑在computed中处理
  }

  function handleSuccess() {
    loadTemplateList();
    renderKey.value++;
  }

  function reload() {
    loadTemplateList();
    renderKey.value++;
  }

  // 监听模板选择变化
  watch(templateId, newId => {
    if (newId) {
      // 加载第一个菜单项
      if (menuData.value.length > 0) {
        const firstMenu = menuData.value[0];
        // 如果有子菜单，加载第一个子菜单
        if (firstMenu.children && firstMenu.children.length > 0) {
          onSubMenuChange(firstMenu.children[0]);
        } else {
          onMenuChange(firstMenu);
        }
      }
    }
  });

  // 初始化
  onMounted(async () => {
    await Promise.all([loadMenuData(), loadTemplateList()]);
  });

  defineOptions({ name: 'project-schema-projectTemplate' });
</script>

<style scoped>
  .template-list-container {
    height: 100%;
    overflow-y: auto;
  }

  .template-grid {
    min-height: 400px;
  }

  .template-card {
    transition: all 0.3s ease;
    border-radius: 8px;
  }

  .template-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }

  .template-card.disabled {
    opacity: 0.6;
    filter: grayscale(0.3);
  }

  .template-cover {
    position: relative;
    overflow: hidden;
    border-radius: 8px 8px 0 0;
  }

  .template-description {
    height: 60px;
    overflow: hidden;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .empty-state {
    background: #fafafa;
    border-radius: 8px;
    margin: 20px 0;
  }

  :deep(.ant-card-actions) {
    background: #f8f9fa;
  }

  :deep(.ant-card-actions > li) {
    margin: 4px 0;
  }

  :deep(.ant-card-actions > li > span) {
    color: #666;
    font-size: 16px;
    transition: color 0.3s ease;
  }

  :deep(.ant-card-actions > li > span:hover) {
    color: #1890ff;
  }
</style>
