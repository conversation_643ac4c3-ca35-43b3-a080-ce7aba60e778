import { LAYOUT } from '/@/router/constant';
import { t } from '/@/hooks/web/useI18n';

// 绩效管理路由配置
export default {
  path: '/performance',
  name: 'Performance',
  component: LAYOUT,
  redirect: '/performance/scoring',
  meta: {
    orderNo: 60,
    icon: 'icon-ym icon-ym-performance',
    title: t('绩效管理'),
    defaultTitle: '绩效管理',
  },
  children: [
    {
      path: 'scoring',
      name: 'PerformanceScoring',
      component: () => import('/@/views/performance/scoring/index.vue'),
      meta: {
        title: t('绩效打分'),
        defaultTitle: '绩效打分',
        icon: 'icon-ym icon-ym-scoring',
      },
    },
    {
      path: 'topScoring',
      name: 'PerformanceTopScoring',
      component: () => import('/@/views/performance/topScoring/index.vue'),
      meta: {
        title: t('上级打分'),
        defaultTitle: '上级打分',
        icon: 'icon-ym icon-ym-top-scoring',
      },
    },
    {
      path: 'display',
      name: 'PerformanceDisplay',
      component: () => import('/@/views/performance/display/index.vue'),
      meta: {
        title: t('绩效展示'),
        defaultTitle: '绩效展示',
        icon: 'icon-ym icon-ym-display',
      },
    },
    {
      path: 'publish',
      name: 'PerformancePublish',
      component: () => import('/@/views/performance/publish/index.vue'),
      meta: {
        title: t('绩效发布'),
        defaultTitle: '绩效发布',
        icon: 'icon-ym icon-ym-publish',
      },
    },
    {
      path: 'analysis',
      name: 'PerformanceAnalysis',
      component: () => import('/@/views/performance/analysis/index.vue'),
      meta: {
        title: t('绩效报表分析'),
        defaultTitle: '绩效报表分析',
        icon: 'icon-ym icon-ym-chart',
      },
    },
    {
      path: 'setting',
      name: 'PerformanceSetting',
      component: () => import('/@/views/performance/setting/index.vue'),
      meta: {
        title: t('绩效设置'),
        defaultTitle: '绩效设置',
        icon: 'icon-ym icon-ym-setting',
      },
    },
  ],
};
