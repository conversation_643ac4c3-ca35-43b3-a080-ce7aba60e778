<template>
  <div>
    <a-select
      v-model:value="selectedLinkmanId"
      :placeholder="placeholder"
      :options="linkmanOptions"
      :showSearch="true"
      :disabled="disabled"
      :filterOption="filterOption"
      @change="handleChange"
      style="width: 100%" />
  </div>
</template>

<script lang="ts" setup>
  import { ref, watch, onMounted, PropType } from 'vue';
  import { getCustomerLinkmanList, ProjCustomerLinkmanModel } from '/@/api/project/customerLinkman/index';

  const props = defineProps({
    value: {
      type: String,
      default: '',
    },
    customerId: {
      type: String,
      default: '',
    },
    placeholder: {
      type: String,
      default: '请选择联系人',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits(['update:value', 'change']);

  const selectedLinkmanId = ref(props.value);
  const linkmanOptions = ref<{ label: string; value: string; linkman: string; telephone: string }[]>([]);
  const linkmanList = ref<ProjCustomerLinkmanModel[]>([]);

  // 过滤选项
  const filterOption = (input: string, option: any) => {
    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  };

  // 加载客户联系人列表
  async function loadLinkmanOptions(customerId: string) {
    if (!customerId) {
      linkmanOptions.value = [];
      linkmanList.value = [];
      return;
    }

    try {
      const list = await getCustomerLinkmanList(customerId);
      linkmanList.value = list;
      linkmanOptions.value = list.map(item => ({
        fullName: `${item.linkman}${item.telephone ? ' - ' + item.telephone : ''}`,
        id: item.id,
        linkman: item.linkman,
        telephone: item.telephone,
      }));
    } catch (error) {
      console.error('获取客户联系人列表失败:', error);
    }
  }

  // 处理选择变化
  function handleChange(value: string, option: any) {
    emit('update:value', value);

    if (option) {
      const selectedLinkman = linkmanList.value.find(item => item.id === value);
      emit('change', value, selectedLinkman);
    } else {
      emit('change', value, null);
    }
  }

  // 监听客户ID变化
  watch(
    () => props.customerId,
    newVal => {
      if (newVal) {
        loadLinkmanOptions(newVal);
      } else {
        linkmanOptions.value = [];
        linkmanList.value = [];
      }
    },
    { immediate: true },
  );

  // 监听外部值变化
  watch(
    () => props.value,
    newVal => {
      selectedLinkmanId.value = newVal;
    },
  );

  onMounted(() => {
    if (props.customerId) {
      loadLinkmanOptions(props.customerId);
    }
  });
</script>
