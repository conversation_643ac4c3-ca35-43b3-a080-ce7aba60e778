package com.xinghuo.project.biz.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 项目业务分部分配 VO
 * 
 * <AUTHOR>
 * @version V3.0
 * @copyright 易趋集团
 * @date 2024-01-01
 */
@Data
public class ProjBizAllocationVO {

    @Schema(description = "主键ID")
    private String id;

    @Schema(description = "项目基础ID")
    private String projBaseId;

    @Schema(description = "商机ID")
    private String opportunityId;

    @Schema(description = "合同ID")
    private String contractId;

    @Schema(description = "主业务ID")
    private String businessId;

    @Schema(description = "业务类型")
    private Integer businessType;

    @Schema(description = "业务类型名称")
    private String businessTypeName;

    @Schema(description = "一部营收分配")
    private BigDecimal ybAmount;

    @Schema(description = "二部营收分配")
    private BigDecimal ebAmount;

    @Schema(description = "交付营收分配")
    private BigDecimal jfAmount;

    @Schema(description = "综合营收分配")
    private BigDecimal otherAmount;

    @Schema(description = "一部外采分配")
    private BigDecimal outYbAmount;

    @Schema(description = "二部外采分配")
    private BigDecimal outEbAmount;

    @Schema(description = "交付外采分配")
    private BigDecimal outJfAmount;

    @Schema(description = "综合外采分配")
    private BigDecimal outOtherAmount;

    @Schema(description = "分配备注")
    private String remarks;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @Schema(description = "创建人")
    private String createdBy;

    @Schema(description = "更新时间")
    private LocalDateTime lastUpdatedAt;

    @Schema(description = "更新人")
    private String lastUpdatedBy;

    // ==================== 计算字段 ====================

    @Schema(description = "营收分配总额")
    private BigDecimal totalAmount;

    @Schema(description = "外采分配总额")
    private BigDecimal totalOutAmount;

    @Schema(description = "分配总计")
    private BigDecimal grandTotal;

    @Schema(description = "分部分配明细")
    private List<DeptAllocationVO> deptAllocations;

    /**
     * 分部分配明细VO
     */
    @Data
    public static class DeptAllocationVO {
        @Schema(description = "分部编码")
        private String deptCode;

        @Schema(description = "分部名称")
        private String deptName;

        @Schema(description = "营收分配")
        private BigDecimal amount;

        @Schema(description = "外采分配")
        private BigDecimal outAmount;

        @Schema(description = "分部总计")
        private BigDecimal deptTotal;

        @Schema(description = "业务类型")
        private String businessType;
    }
}