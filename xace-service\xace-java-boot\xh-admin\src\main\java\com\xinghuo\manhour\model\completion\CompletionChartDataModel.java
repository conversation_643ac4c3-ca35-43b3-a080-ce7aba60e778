package com.xinghuo.manhour.model.completion;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 工时填写情况图表数据模型
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@Schema(description = "工时填写情况图表数据模型")
public class CompletionChartDataModel {

    @Schema(description = "填写状态分布数据")
    private List<StatusDistributionData> statusDistribution;

    @Schema(description = "审批状态分布数据")
    private List<ApprovalStatusData> approvalStatus;

    @Schema(description = "分部完成率对比数据")
    private List<DepartmentCompletionData> departmentCompletion;

    @Schema(description = "填写完成趋势数据")
    private List<CompletionTrendData> completionTrend;

    @Schema(description = "审批效率趋势数据")
    private List<ApprovalEfficiencyData> approvalEfficiency;

    /**
     * 填写状态分布数据
     */
    @Data
    @Schema(description = "填写状态分布数据")
    public static class StatusDistributionData {

        @Schema(description = "状态码")
        private String status;

        @Schema(description = "状态名称")
        private String statusName;

        @Schema(description = "用户数量")
        private Integer userCount;
    }

    /**
     * 审批状态分布数据
     */
    @Data
    @Schema(description = "审批状态分布数据")
    public static class ApprovalStatusData {

        @Schema(description = "状态码")
        private String status;

        @Schema(description = "状态名称")
        private String statusName;

        @Schema(description = "记录数量")
        private Integer recordCount;
    }

    /**
     * 分部完成率对比数据
     */
    @Data
    @Schema(description = "分部完成率对比数据")
    public static class DepartmentCompletionData {

        @Schema(description = "分部名称")
        private String fbName;

        @Schema(description = "完成率")
        private BigDecimal completionRate;
    }

    /**
     * 填写完成趋势数据
     */
    @Data
    @Schema(description = "填写完成趋势数据")
    public static class CompletionTrendData {

        @Schema(description = "时间周期")
        private String period;

        @Schema(description = "应填写人数")
        private Integer totalUsers;

        @Schema(description = "已填写人数")
        private Integer completedUsers;

        @Schema(description = "完成率")
        private BigDecimal completionRate;
    }

    /**
     * 审批效率趋势数据
     */
    @Data
    @Schema(description = "审批效率趋势数据")
    public static class ApprovalEfficiencyData {

        @Schema(description = "时间周期")
        private String period;

        @Schema(description = "审批效率")
        private BigDecimal approvalEfficiency;

        @Schema(description = "平均审批时长")
        private BigDecimal avgApprovalDays;
    }
}
