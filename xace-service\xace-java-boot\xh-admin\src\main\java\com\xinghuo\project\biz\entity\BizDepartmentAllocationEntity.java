package com.xinghuo.project.biz.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 业务部门分配实体（固定字段版本）
 * 将原有的硬编码字段迁移到独立表中，但保持固定字段结构
 * 
 * <AUTHOR>
 * @version V2.0
 * @copyright 易趋集团
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_department_allocation")
public class BizDepartmentAllocationEntity extends BaseEntityV2.CUDBaseEntityV2<String> {

    /**
     * 业务ID（关联的主业务记录ID）
     */
    @TableField("F_BUSINESS_ID")
    private String businessId;

    /**
     * 业务类型（1-商机 2-合同 3-收款 4-付款）
     */
    @TableField("F_BUSINESS_TYPE")
    private Integer businessType;

    // ==================== 分部标识字段 ====================
    
    /**
     * 所属分部ID
     */
    @TableField("F_DEPT_ID")
    private String deptId;

    /**
     * 研发分部ID
     */
    @TableField("F_YF_DEPT_ID")
    private String yfDeptId;

    // ==================== 分部分配金额字段（内部资源分配） ====================
    
    /**
     * 软件部金额
     */
    @TableField("F_DEPT_MONEY")
    private BigDecimal deptMoney;
    
    /**
     * 一部分配金额
     */
    @TableField("F_YF_YB_AMOUNT")
    private BigDecimal yfYbAmount;

    /**
     * 二部分配金额
     */
    @TableField("F_YF_EB_AMOUNT")
    private BigDecimal yfEbAmount;

    /**
     * 交付分配金额
     */
    @TableField("F_YF_JF_AMOUNT")
    private BigDecimal yfJfAmount;

    /**
     * 综合分配金额
     */
    @TableField("F_YF_OTHER_AMOUNT")
    private BigDecimal yfOtherAmount;

    // ==================== 外采分配字段 ====================
    
    /**
     * 一部外采金额
     */
    @TableField("F_OUT_YB_AMOUNT")
    private BigDecimal outYbAmount;

    /**
     * 二部外采金额
     */
    @TableField("F_OUT_EB_AMOUNT")
    private BigDecimal outEbAmount;

    /**
     * 交付外采金额
     */
    @TableField("F_OUT_JF_AMOUNT")
    private BigDecimal outJfAmount;

    /**
     * 综合外采金额
     */
    @TableField("F_OUT_OTHER_AMOUNT")
    private BigDecimal outOtherAmount;

    // ==================== 待签外采字段 ====================
    
    /**
     * 待签一部外采金额
     */
    @TableField("F_UNSIGN_OUT_YB_AMOUNT")
    private BigDecimal unsignOutYbAmount;

    /**
     * 待签二部外采金额
     */
    @TableField("F_UNSIGN_OUT_EB_AMOUNT")
    private BigDecimal unsignOutEbAmount;

    /**
     * 待签交付外采金额
     */
    @TableField("F_UNSIGN_OUT_JF_AMOUNT")
    private BigDecimal unsignOutJfAmount;

    /**
     * 待签综合外采金额
     */
    @TableField("F_UNSIGN_OUT_OTHER_AMOUNT")
    private BigDecimal unsignOutOtherAmount;

    // ==================== 总体汇总字段 ====================
    
    /**
     * 外采总金额
     */
    @TableField("F_PURCHASE_MONEY")
    private BigDecimal purchaseMoney;

    // ==================== 外采已付字段 ====================
    
    /**
     * 一部外采已付金额
     */
    @TableField("F_OUT_YF_YB_AMOUNT")
    private BigDecimal outYfYbAmount;

    /**
     * 二部外采已付金额
     */
    @TableField("F_OUT_YF_EB_AMOUNT")
    private BigDecimal outYfEbAmount;

    /**
     * 交付外采已付金额
     */
    @TableField("F_OUT_YF_JF_AMOUNT")
    private BigDecimal outYfJfAmount;

    /**
     * 综合外采已付金额
     */
    @TableField("F_OUT_YF_OTHER_AMOUNT")
    private BigDecimal outYfOtherAmount;

    // ==================== 汇总计算字段 ====================
    
    /**
     * 获取分部分配总额
     */
    public BigDecimal getTotalYfAmount() {
        return (yfYbAmount != null ? yfYbAmount : BigDecimal.ZERO)
            .add(yfEbAmount != null ? yfEbAmount : BigDecimal.ZERO)
            .add(yfJfAmount != null ? yfJfAmount : BigDecimal.ZERO)
            .add(yfOtherAmount != null ? yfOtherAmount : BigDecimal.ZERO);
    }

    /**
     * 获取分部外采总额
     */
    public BigDecimal getTotalOutAmount() {
        return (outYbAmount != null ? outYbAmount : BigDecimal.ZERO)
            .add(outEbAmount != null ? outEbAmount : BigDecimal.ZERO)
            .add(outJfAmount != null ? outJfAmount : BigDecimal.ZERO)
            .add(outOtherAmount != null ? outOtherAmount : BigDecimal.ZERO);
    }

    /**
     * 获取分部外采已付总额
     */
    public BigDecimal getTotalOutYfAmount() {
        return (outYfYbAmount != null ? outYfYbAmount : BigDecimal.ZERO)
            .add(outYfEbAmount != null ? outYfEbAmount : BigDecimal.ZERO)
            .add(outYfJfAmount != null ? outYfJfAmount : BigDecimal.ZERO)
            .add(outYfOtherAmount != null ? outYfOtherAmount : BigDecimal.ZERO);
    }

    /**
     * 验证分配金额一致性
     */
    public boolean validateAllocationConsistency() {
        // 验证软件部金额是否等于各分部分配金额之和
        BigDecimal calculatedTotal = getTotalYfAmount();
        if (deptMoney != null && calculatedTotal.compareTo(BigDecimal.ZERO) > 0) {
            return Math.abs(deptMoney.subtract(calculatedTotal).doubleValue()) < 0.01;
        }
        
        // 验证外采总金额是否等于各分部外采金额之和
        BigDecimal calculatedOutTotal = getTotalOutAmount();
        if (purchaseMoney != null && calculatedOutTotal.compareTo(BigDecimal.ZERO) > 0) {
            return Math.abs(purchaseMoney.subtract(calculatedOutTotal).doubleValue()) < 0.01;
        }
        
        return true;
    }

    /**
     * 获取指定部门的营收金额
     */
    public BigDecimal getYfAmountByDept(String deptCode) {
        switch (deptCode.toUpperCase()) {
            case "YB": return yfYbAmount != null ? yfYbAmount : BigDecimal.ZERO;
            case "EB": return yfEbAmount != null ? yfEbAmount : BigDecimal.ZERO;
            case "JF": return yfJfAmount != null ? yfJfAmount : BigDecimal.ZERO;
            case "OTHER": case "ZH": return yfOtherAmount != null ? yfOtherAmount : BigDecimal.ZERO;
            default: return BigDecimal.ZERO;
        }
    }

    /**
     * 设置指定部门的营收金额
     */
    public void setYfAmountByDept(String deptCode, BigDecimal amount) {
        switch (deptCode.toUpperCase()) {
            case "YB": this.yfYbAmount = amount; break;
            case "EB": this.yfEbAmount = amount; break;
            case "JF": this.yfJfAmount = amount; break;
            case "OTHER": case "ZH": this.yfOtherAmount = amount; break;
        }
    }
}