package com.xinghuo.project.biz.model.paymentContractMoney;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 采购付款计划表单对象
 */
@Data
@Schema(description = "采购付款计划表单对象")
public class PaymentContractMoneyForm {

    /**
     * 采购合同ID
     */
    @NotBlank(message = "采购合同ID不能为空")
    @Schema(description = "采购合同ID")
    private String pcId;

    /**
     * 付款条件
     */
    @NotBlank(message = "付款条件不能为空")
    @Size(max = 200, message = "付款条件长度不能超过200个字符")
    @Schema(description = "付款条件")
    private String fktj;

    /**
     * 付款比例
     */
    @Schema(description = "付款比例")
    private BigDecimal ratio;

    /**
     * 付款金额
     */
    @NotNull(message = "付款金额不能为空")
    @Schema(description = "付款金额")
    private BigDecimal cmMoney;

    /**
     * 预付日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "预付日期")
    private Date yufuDate;

    /**
     * 付款日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "付款日期")
    private Date fukuanDate;

    /**
     * 付款状态 1-已付款，0-未付款
     */
    @Schema(description = "付款状态 1-已付款，0-未付款")
    private Integer payStatus;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @Schema(description = "备注")
    private String note;

    /**
     * 负责人ID
     */
    @Schema(description = "负责人ID")
    private String ownId;

    /**
     * 部门ID
     */
    @Schema(description = "部门ID")
    private String deptId;

    /**
     * 一部金额
     */
    @Schema(description = "一部金额")
    private BigDecimal ybAmount;

    /**
     * 二部金额
     */
    @Schema(description = "二部金额")
    private BigDecimal ebAmount;

    /**
     * 其他金额
     */
    @Schema(description = "其他金额")
    private BigDecimal otherAmount;
}

