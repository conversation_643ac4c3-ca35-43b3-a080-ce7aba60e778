package com.xinghuo.manhour.model.analysis;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 图表数据模型
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@Schema(description = "图表数据模型")
public class ChartDataModel {
    
    @Schema(description = "项目分布数据")
    private List<ProjectDistributionData> projectDistribution;
    
    @Schema(description = "分部分布数据")
    private List<DepartmentDistributionData> departmentDistribution;
    
    @Schema(description = "项目类型分布数据")
    private List<ProjectTypeDistributionData> projectTypeDistribution;
    
    @Schema(description = "月度趋势数据")
    private List<MonthlyTrendData> monthlyTrend;

    /**
     * 项目分布数据
     */
    @Data
    @Schema(description = "项目分布数据")
    public static class ProjectDistributionData {
        
        @Schema(description = "项目名称")
        private String projectName;
        
        @Schema(description = "工时")
        private BigDecimal workMonth;
    }

    /**
     * 分部分布数据
     */
    @Data
    @Schema(description = "分部分布数据")
    public static class DepartmentDistributionData {
        
        @Schema(description = "分部名称")
        private String fbName;
        
        @Schema(description = "工时")
        private BigDecimal workMonth;
    }

    /**
     * 项目类型分布数据
     */
    @Data
    @Schema(description = "项目类型分布数据")
    public static class ProjectTypeDistributionData {
        
        @Schema(description = "项目类型名称")
        private String projTypeName;
        
        @Schema(description = "工时")
        private BigDecimal workMonth;
    }

    /**
     * 月度趋势数据
     */
    @Data
    @Schema(description = "月度趋势数据")
    public static class MonthlyTrendData {
        
        @Schema(description = "月份")
        private String month;
        
        @Schema(description = "工时")
        private BigDecimal workMonth;
    }
}
