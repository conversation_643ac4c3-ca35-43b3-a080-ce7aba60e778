package com.xinghuo.project.schema.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.project.schema.entity.ProjectTemplateEntity;
import com.xinghuo.project.schema.entity.ProjectSchemaWbsEntity;
import com.xinghuo.project.schema.entity.ProjectSchemaPhaseEntity;
import com.xinghuo.project.schema.model.ProjectTemplatePagination;
import com.xinghuo.project.schema.model.vo.ProjectTemplateVO;
import com.xinghuo.project.schema.model.vo.ProjectTemplateSelectVO;
import com.xinghuo.project.schema.service.ProjectTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import cn.dev33.satoken.annotation.SaCheckPermission;

import java.util.List;
import java.util.Map;

/**
 * 项目模板管理控制器
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Slf4j
@Tag(name = "项目模板管理", description = "项目模板管理相关接口")
@RestController
@RequestMapping("/api/project/schema/projectTemplate")
public class ProjectTemplateController {

    @Resource
    private ProjectTemplateService projectTemplateService;

    /**
     * 获取项目模板列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取项目模板列表")
    @SaCheckPermission("project:template:view")
    public ActionResult<PageListVO<ProjectTemplateVO>> list(@RequestBody ProjectTemplatePagination pagination) {
        try {
            List<ProjectTemplateVO> list = projectTemplateService.getList(pagination);

            // 对结果进行数据转换和补充
            for (ProjectTemplateVO vo : list) {
                // 状态名称转换（0:启用, 1:禁用）
                if (vo.getStatus() != null) {
                    switch (vo.getStatus()) {
                        case 0:
                            vo.setStatusName("启用");
                            break;
                        case 1:
                            vo.setStatusName("禁用");
                            break;
                        default:
                            vo.setStatusName("未知");
                    }
                }

                // TODO: 可以在这里添加其他关联数据的查询和设置
                // 例如：创建用户名称、模板类型名称等
            }

            PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
            return ActionResult.page(list, page);
        } catch (Exception e) {
            log.error("获取项目模板列表失败", e);
            return ActionResult.fail("获取项目模板列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据状态获取模板列表
     */
    @GetMapping("/getListByStatus/{status}")
    @Operation(summary = "根据状态获取模板列表")
    @SaCheckPermission("project:template:view")
    public ActionResult<List<ProjectTemplateEntity>> getListByStatus(
            @Parameter(description = "状态") @PathVariable Integer status) {
        try {
            List<ProjectTemplateEntity> list = projectTemplateService.getListByStatus(status);
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("根据状态获取模板列表失败", e);
            return ActionResult.fail("获取模板列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据类型获取模板列表
     */
    @GetMapping("/getListByType/{typeId}")
    @Operation(summary = "根据类型获取模板列表")
    @SaCheckPermission("project:template:view")
    public ActionResult<List<ProjectTemplateEntity>> getListByType(
            @Parameter(description = "类型ID") @PathVariable String typeId) {
        try {
            List<ProjectTemplateEntity> list = projectTemplateService.getListByType(typeId);
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("根据类型获取模板列表失败", e);
            return ActionResult.fail("获取模板列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取模板详情（包含WBS和阶段配置）
     */
    @GetMapping("/getDetailInfo/{id}")
    @Operation(summary = "获取模板详情")
    @SaCheckPermission("project:template:view")
    public ActionResult<ProjectTemplateVO> getDetailInfo(
            @Parameter(description = "模板ID") @PathVariable String id) {
        try {
            ProjectTemplateVO templateVO = projectTemplateService.getDetailInfo(id);
            if (templateVO == null) {
                return ActionResult.fail("项目模板不存在");
            }
            return ActionResult.success(templateVO);
        } catch (Exception e) {
            log.error("获取模板详情失败", e);
            return ActionResult.fail("获取模板详情失败：" + e.getMessage());
        }
    }

    /**
     * 获取模板基本信息
     */
    @GetMapping("/getInfo/{id}")
    @Operation(summary = "获取模板基本信息")
    @SaCheckPermission("project:template:view")
    public ActionResult<ProjectTemplateEntity> getInfo(
            @Parameter(description = "模板ID") @PathVariable String id) {
        try {
            ProjectTemplateEntity entity = projectTemplateService.getInfo(id);
            if (entity == null) {
                return ActionResult.fail("项目模板不存在");
            }
            return ActionResult.success(entity);
        } catch (Exception e) {
            log.error("获取模板信息失败", e);
            return ActionResult.fail("获取模板信息失败：" + e.getMessage());
        }
    }

    /**
     * 创建项目模板
     */
    @PostMapping("/create")
    @Operation(summary = "创建项目模板")
    @SaCheckPermission("project:template:create")
    public ActionResult<String> create(@RequestBody @Valid ProjectTemplateVO templateVO) {
        try {
            String id = projectTemplateService.create(templateVO);
            return ActionResult.success("创建成功", id);
        } catch (Exception e) {
            log.error("创建项目模板失败", e);
            return ActionResult.fail("创建项目模板失败：" + e.getMessage());
        }
    }

    /**
     * 更新项目模板
     */
    @PutMapping("/update/{id}")
    @Operation(summary = "更新项目模板")
    @SaCheckPermission("project:template:update")
    public ActionResult<String> update(
            @Parameter(description = "模板ID") @PathVariable String id,
            @RequestBody @Valid ProjectTemplateVO templateVO) {
        try {
            projectTemplateService.update(id, templateVO);
            return ActionResult.success("更新成功");
        } catch (Exception e) {
            log.error("更新项目模板失败", e);
            return ActionResult.fail("更新项目模板失败：" + e.getMessage());
        }
    }

    /**
     * 删除项目模板
     */
    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除项目模板")
    @SaCheckPermission("project:template:delete")
    public ActionResult<String> delete(
            @Parameter(description = "模板ID") @PathVariable String id) {
        try {
            projectTemplateService.delete(id);
            return ActionResult.success("删除成功");
        } catch (Exception e) {
            log.error("删除项目模板失败", e);
            return ActionResult.fail("删除项目模板失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除项目模板
     */
    @DeleteMapping("/batchDelete")
    @Operation(summary = "批量删除项目模板")
    @SaCheckPermission("project:template:delete")
    public ActionResult<String> batchDelete(@RequestBody List<String> ids) {
        try {
            projectTemplateService.batchDelete(ids);
            return ActionResult.success("批量删除成功");
        } catch (Exception e) {
            log.error("批量删除项目模板失败", e);
            return ActionResult.fail("批量删除项目模板失败：" + e.getMessage());
        }
    }

    /**
     * 更新模板状态
     */
    @PutMapping("/updateStatus/{id}")
    @Operation(summary = "更新模板状态")
    @SaCheckPermission("project:template:edit")
    public ActionResult<String> updateStatus(
            @Parameter(description = "模板ID") @PathVariable String id,
            @RequestParam Integer status) {
        try {
            projectTemplateService.updateStatus(id, status);
            return ActionResult.success("状态更新成功");
        } catch (Exception e) {
            log.error("更新模板状态失败", e);
            return ActionResult.fail("更新模板状态失败：" + e.getMessage());
        }
    }

    /**
     * 批量更新状态
     */
    @PutMapping("/batchUpdateStatus")
    @Operation(summary = "批量更新状态")
    @SaCheckPermission("project:template:edit")
    public ActionResult<String> batchUpdateStatus(
            @RequestBody List<String> ids,
            @RequestParam Integer status) {
        try {
            projectTemplateService.batchUpdateStatus(ids, status);
            return ActionResult.success("批量更新状态成功");
        } catch (Exception e) {
            log.error("批量更新模板状态失败", e);
            return ActionResult.fail("批量更新状态失败：" + e.getMessage());
        }
    }

    /**
     * 启用模板
     */
    @PutMapping("/enable/{id}")
    @Operation(summary = "启用模板")
    @SaCheckPermission("project:template:edit")
    public ActionResult<String> enable(
            @Parameter(description = "模板ID") @PathVariable String id) {
        try {
            projectTemplateService.enable(id);
            return ActionResult.success("启用成功");
        } catch (Exception e) {
            log.error("启用模板失败", e);
            return ActionResult.fail("启用模板失败：" + e.getMessage());
        }
    }

    /**
     * 禁用模板
     */
    @PutMapping("/disable/{id}")
    @Operation(summary = "禁用模板")
    @SaCheckPermission("project:template:edit")
    public ActionResult<String> disable(
            @Parameter(description = "模板ID") @PathVariable String id) {
        try {
            projectTemplateService.disable(id);
            return ActionResult.success("禁用成功");
        } catch (Exception e) {
            log.error("禁用模板失败", e);
            return ActionResult.fail("禁用模板失败：" + e.getMessage());
        }
    }

    /**
     * 批量启用模板
     */
    @PutMapping("/batchEnable")
    @Operation(summary = "批量启用模板")
    @SaCheckPermission("project:template:edit")
    public ActionResult<String> batchEnable(@RequestBody List<String> ids) {
        try {
            projectTemplateService.batchEnable(ids);
            return ActionResult.success("批量启用成功");
        } catch (Exception e) {
            log.error("批量启用模板失败", e);
            return ActionResult.fail("批量启用模板失败：" + e.getMessage());
        }
    }

    /**
     * 批量禁用模板
     */
    @PutMapping("/batchDisable")
    @Operation(summary = "批量禁用模板")
    @SaCheckPermission("project:template:edit")
    public ActionResult<String> batchDisable(@RequestBody List<String> ids) {
        try {
            projectTemplateService.batchDisable(ids);
            return ActionResult.success("批量禁用成功");
        } catch (Exception e) {
            log.error("批量禁用模板失败", e);
            return ActionResult.fail("批量禁用模板失败：" + e.getMessage());
        }
    }

    /**
     * 复制项目模板
     */
    @PostMapping("/copy/{id}")
    @Operation(summary = "复制项目模板")
    @SaCheckPermission("project:template:copy")
    public ActionResult<String> copy(
            @Parameter(description = "模板ID") @PathVariable String id,
            @RequestParam String newName) {
        try {
            String newId = projectTemplateService.copy(id, newName);
            return ActionResult.success("复制成功", newId);
        } catch (Exception e) {
            log.error("复制项目模板失败", e);
            return ActionResult.fail("复制项目模板失败：" + e.getMessage());
        }
    }

    /**
     * 检查模板名称是否存在
     */
    @GetMapping("/checkNameExists")
    @Operation(summary = "检查模板名称是否存在")
    @SaCheckPermission("project:template:view")
    public ActionResult<Boolean> checkNameExists(
            @RequestParam String name,
            @RequestParam(required = false) String excludeId) {
        try {
            boolean exists = projectTemplateService.isExistByName(name, excludeId);
            return ActionResult.success(exists);
        } catch (Exception e) {
            log.error("检查模板名称失败", e);
            return ActionResult.fail("检查模板名称失败：" + e.getMessage());
        }
    }

    /**
     * 检查模板编码是否存在
     */
    @GetMapping("/checkCodeExists")
    @Operation(summary = "检查模板编码是否存在")
    @SaCheckPermission("project:template:view")
    public ActionResult<Boolean> checkCodeExists(
            @RequestParam String code,
            @RequestParam(required = false) String excludeId) {
        try {
            boolean exists = projectTemplateService.isExistByCode(code, excludeId);
            return ActionResult.success(exists);
        } catch (Exception e) {
            log.error("检查模板编码失败", e);
            return ActionResult.fail("检查模板编码失败：" + e.getMessage());
        }
    }

    /**
     * 获取模板选择列表
     */
    @GetMapping("/getSelectList")
    @Operation(summary = "获取模板选择列表")
    @SaCheckPermission("project:template:view")
    public ActionResult<List<ProjectTemplateSelectVO>> getSelectList(
            @RequestParam(required = false) String keyword) {
        try {
            List<ProjectTemplateEntity> list = projectTemplateService.getSelectList(keyword);
            List<ProjectTemplateSelectVO> listVO = BeanCopierUtils.copyList(list, ProjectTemplateSelectVO.class);

            // 构建fullName字段，格式：[编码] 名称
            for (ProjectTemplateSelectVO vo : listVO) {
                if (vo.getCode() != null && vo.getName() != null) {
                    vo.setFullName("[" + vo.getCode() + "] " + vo.getName());
                } else if (vo.getName() != null) {
                    vo.setFullName(vo.getName());
                } else {
                    vo.setFullName(vo.getId());
                }
            }

            return ActionResult.success(listVO);
        } catch (Exception e) {
            log.error("获取模板选择列表失败", e);
            return ActionResult.fail("获取模板选择列表失败：" + e.getMessage());
        }
    }

    /**
     * 生成模板编码
     */
    @GetMapping("/generateCode")
    @Operation(summary = "生成模板编码")
    @SaCheckPermission("project:template:create")
    public ActionResult<String> generateCode() {
        try {
            String code = projectTemplateService.generateCode();
            return ActionResult.success(code);
        } catch (Exception e) {
            log.error("生成模板编码失败", e);
            return ActionResult.fail("生成模板编码失败：" + e.getMessage());
        }
    }

    /**
     * 获取模板使用情况
     */
    @GetMapping("/getTemplateUsageInfo/{id}")
    @Operation(summary = "获取模板使用情况")
    @SaCheckPermission("project:template:view")
    public ActionResult<Map<String, Object>> getTemplateUsageInfo(
            @Parameter(description = "模板ID") @PathVariable String id) {
        try {
            Map<String, Object> usageInfo = projectTemplateService.getTemplateUsageInfo(id);
            return ActionResult.success(usageInfo);
        } catch (Exception e) {
            log.error("获取模板使用情况失败", e);
            return ActionResult.fail("获取模板使用情况失败：" + e.getMessage());
        }
    }

    /**
     * 从WBS模板导入配置
     */
    @PostMapping("/importFromWbsTemplate/{templateId}")
    @Operation(summary = "从WBS模板导入配置")
    @SaCheckPermission("project:template:edit")
    public ActionResult<String> importFromWbsTemplate(
            @Parameter(description = "项目模板ID") @PathVariable String templateId,
            @RequestParam String wbsTemplateId) {
        try {
            projectTemplateService.importFromWbsTemplate(templateId, wbsTemplateId);
            return ActionResult.success("导入成功");
        } catch (Exception e) {
            log.error("从WBS模板导入配置失败", e);
            return ActionResult.fail("导入配置失败：" + e.getMessage());
        }
    }

    /**
     * 从阶段模板导入配置
     */
    @PostMapping("/importFromPhaseTemplate/{templateId}")
    @Operation(summary = "从阶段模板导入配置")
    @SaCheckPermission("project:template:edit")
    public ActionResult<String> importFromPhaseTemplate(
            @Parameter(description = "项目模板ID") @PathVariable String templateId,
            @RequestParam String phaseTemplateId) {
        try {
            projectTemplateService.importFromPhaseTemplate(templateId, phaseTemplateId);
            return ActionResult.success("导入成功");
        } catch (Exception e) {
            log.error("从阶段模板导入配置失败", e);
            return ActionResult.fail("导入配置失败：" + e.getMessage());
        }
    }

    /**
     * 从标准阶段库导入阶段
     */
    @PostMapping("/importFromPhaseLibrary/{templateId}")
    @Operation(summary = "从标准阶段库导入阶段")
    @SaCheckPermission("project:template:edit")
    public ActionResult<String> importFromPhaseLibrary(
            @Parameter(description = "项目模板ID") @PathVariable String templateId,
            @RequestBody List<String> phaseIds) {
        try {
            projectTemplateService.importFromPhaseLibrary(templateId, phaseIds);
            return ActionResult.success("导入成功");
        } catch (Exception e) {
            log.error("从标准阶段库导入阶段失败", e);
            return ActionResult.fail("导入阶段失败：" + e.getMessage());
        }
    }

    /**
     * 获取WBS配置列表
     */
    @GetMapping("/getWbsConfigs/{templateId}")
    @Operation(summary = "获取WBS配置列表")
    @SaCheckPermission("project:template:view")
    public ActionResult<List<ProjectSchemaWbsEntity>> getWbsConfigs(
            @Parameter(description = "模板ID") @PathVariable String templateId) {
        try {
            List<ProjectSchemaWbsEntity> configs = projectTemplateService.getWbsConfigs(templateId);
            return ActionResult.success(configs);
        } catch (Exception e) {
            log.error("获取WBS配置失败", e);
            return ActionResult.fail("获取WBS配置失败：" + e.getMessage());
        }
    }

    /**
     * 获取阶段配置列表
     */
    @GetMapping("/getPhaseConfigs/{templateId}")
    @Operation(summary = "获取阶段配置列表")
    @SaCheckPermission("project:template:view")
    public ActionResult<List<ProjectSchemaPhaseEntity>> getPhaseConfigs(
            @Parameter(description = "模板ID") @PathVariable String templateId) {
        try {
            List<ProjectSchemaPhaseEntity> configs = projectTemplateService.getPhaseConfigs(templateId);
            return ActionResult.success(configs);
        } catch (Exception e) {
            log.error("获取阶段配置失败", e);
            return ActionResult.fail("获取阶段配置失败：" + e.getMessage());
        }
    }

    /**
     * 保存WBS配置
     */
    @PostMapping("/saveWbsConfigs/{templateId}")
    @Operation(summary = "保存WBS配置")
    @SaCheckPermission("project:template:edit")
    public ActionResult<String> saveWbsConfigs(
            @Parameter(description = "模板ID") @PathVariable String templateId,
            @RequestBody List<ProjectSchemaWbsEntity> wbsConfigs) {
        try {
            projectTemplateService.saveWbsConfigs(templateId, wbsConfigs);
            return ActionResult.success("保存成功");
        } catch (Exception e) {
            log.error("保存WBS配置失败", e);
            return ActionResult.fail("保存WBS配置失败：" + e.getMessage());
        }
    }

    /**
     * 保存阶段配置
     */
    @PostMapping("/savePhaseConfigs/{templateId}")
    @Operation(summary = "保存阶段配置")
    @SaCheckPermission("project:template:edit")
    public ActionResult<String> savePhaseConfigs(
            @Parameter(description = "模板ID") @PathVariable String templateId,
            @RequestBody List<ProjectSchemaPhaseEntity> phaseConfigs) {
        try {
            projectTemplateService.savePhaseConfigs(templateId, phaseConfigs);
            return ActionResult.success("保存成功");
        } catch (Exception e) {
            log.error("保存阶段配置失败", e);
            return ActionResult.fail("保存阶段配置失败：" + e.getMessage());
        }
    }

    /**
     * 删除WBS配置
     */
    @DeleteMapping("/deleteWbsConfigs")
    @Operation(summary = "删除WBS配置")
    @SaCheckPermission("project:template:edit")
    public ActionResult<String> deleteWbsConfigs(@RequestBody List<String> wbsIds) {
        try {
            projectTemplateService.deleteWbsConfigs(wbsIds);
            return ActionResult.success("删除成功");
        } catch (Exception e) {
            log.error("删除WBS配置失败", e);
            return ActionResult.fail("删除WBS配置失败：" + e.getMessage());
        }
    }

    /**
     * 删除阶段配置
     */
    @DeleteMapping("/deletePhaseConfigs")
    @Operation(summary = "删除阶段配置")
    @SaCheckPermission("project:template:edit")
    public ActionResult<String> deletePhaseConfigs(@RequestBody List<String> phaseIds) {
        try {
            projectTemplateService.deletePhaseConfigs(phaseIds);
            return ActionResult.success("删除成功");
        } catch (Exception e) {
            log.error("删除阶段配置失败", e);
            return ActionResult.fail("删除阶段配置失败：" + e.getMessage());
        }
    }

    /**
     * 应用到项目
     */
    @PostMapping("/applyToProjects/{templateId}")
    @Operation(summary = "应用到项目")
    @SaCheckPermission("project:template:apply")
    public ActionResult<String> applyToProjects(
            @Parameter(description = "模板ID") @PathVariable String templateId,
            @RequestBody List<String> projectIds) {
        try {
            projectTemplateService.applyToProjects(templateId, projectIds);
            return ActionResult.success("应用成功");
        } catch (Exception e) {
            log.error("应用到项目失败", e);
            return ActionResult.fail("应用到项目失败：" + e.getMessage());
        }
    }
}
