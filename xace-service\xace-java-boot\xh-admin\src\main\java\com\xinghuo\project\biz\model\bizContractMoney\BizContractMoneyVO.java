package com.xinghuo.project.biz.model.bizContractMoney;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 合同收款视图对象
 */
@Data
@Schema(description = "合同收款视图对象")
public class BizContractMoneyVO {

    /**
     * 收款主键ID
     */
    @Schema(description = "主键ID")
    private String id;

    /**
     * 基础项目ID
     */
    @Schema(description = "基础项目ID")
    private String projBaseId;

    /**
     * 合同ID
     */
    @Schema(description = "合同ID")
    private String contractId;

    /**
     * 合同名称（非数据库字段，需要关联查询）
     */
    @Schema(description = "合同名称")
    private String contractName;

    /**
     * 合同编号（非数据库字段，需要关联查询）
     */
    @Schema(description = "合同编号")
    private String contractNo;

    /**
     * 付款条件
     */
    @Schema(description = "付款条件")
    private String fktj;

    /**
     * 收款比例
     */
    @Schema(description = "收款比例")
    private String ratio;

    /**
     * 收款金额
     */
    @Schema(description = "收款金额")
    private BigDecimal cmMoney;

    /**
     * 项目经理
     */
    @Schema(description = "项目经理")
    private String ownId;

    /**
     * 项目经理名称（非数据库字段，需要关联查询）
     */
    @Schema(description = "项目经理名称")
    private String ownName;

    /**
     * 收款状态
     */
    @Schema(description = "收款状态")
    private Integer payStatus;

    /**
     * 收款状态文本（非数据库字段，需要转换）
     */
    @Schema(description = "收款状态文本")
    private String payStatusText;

    /**
     * 开票日期
     */
    @Schema(description = "开票日期")
    private Date kaipiaoDate;

    /**
     * 应收日期
     */
    @Schema(description = "应收日期")
    private Date yingshouDate;

    /**
     * 预收日期
     */
    @Schema(description = "预收日期")
    private Date yushouDate;

    /**
     * 收款日期
     */
    @Schema(description = "收款日期")
    private Date shoukuanDate;

    /**
     * 临时保存预收日期
     */
    @Schema(description = "临时保存预收日期")
    private Date tmpDate;

    /**
     * 最后备注
     */
    @Schema(description = "最后备注")
    private String lastNote;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 部门ID
     */
    @Schema(description = "部门ID")
    private String deptId;

    /**
     * 部门名称（非数据库字段，需要关联查询）
     */
    @Schema(description = "部门名称")
    private String deptName;

    /**
     * 一部金额
     */
    @Schema(description = "一部金额")
    private BigDecimal ybAmount;

    /**
     * 二部金额
     */
    @Schema(description = "二部金额")
    private BigDecimal ebAmount;

    /**
     * 综合金额
     */
    @Schema(description = "综合金额")
    private BigDecimal otherAmount;

    /**
     * 创建用户
     */
    @Schema(description = "创建用户")
    private String createUserId;

    /**
     * 创建用户名称（非数据库字段，需要关联查询）
     */
    @Schema(description = "创建用户名称")
    private String createUserName;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 最后修改人
     */
    @Schema(description = "最后修改人")
    private String lastModifiedUserId;

    /**
     * 最后修改人名称（非数据库字段，需要关联查询）
     */
    @Schema(description = "最后修改人名称")
    private String lastModifiedUserName;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;
}
