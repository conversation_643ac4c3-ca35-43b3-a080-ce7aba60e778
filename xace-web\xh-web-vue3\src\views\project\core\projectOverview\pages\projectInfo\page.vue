<template>
  <div class="project-info">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="flex justify-between items-center">
        <div>
          <div class="flex items-center gap-3 mb-2">
            <h2 class="text-xl font-semibold">项目信息</h2>
            <a-tooltip v-if="editMode" title="编辑模式">
              <a-tag color="orange">
                <EditOutlined />
              </a-tag>
            </a-tooltip>
            <a-tooltip v-else title="查看模式">
              <a-tag color="blue">
                <EyeOutlined />
              </a-tag>
            </a-tooltip>
          </div>
          <p class="text-gray-600">{{ editMode ? '编辑项目的基本信息' : '查看项目的基本信息' }}</p>
        </div>
        <div class="flex items-center space-x-2">
          <a-button type="primary" @click="handleEdit" v-if="projectInfo && !editMode">
            <template #icon><EditOutlined /></template>
            编辑
          </a-button>
          <a-space v-if="editMode">
            <a-button type="primary" @click="handleSave" :loading="saveLoading"> 保存 </a-button>
            <a-button @click="handleCancel"> 取消 </a-button>
          </a-space>
          <a-button @click="handleRefresh">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
        </div>
      </div>
    </div>

    <a-card>
      <a-spin :spinning="loading">
        <div v-if="projectInfo" class="project-detail">
          <!-- 统一使用表单布局，通过 disabled 控制是否可编辑 -->
          <div class="project-form">
            <BasicForm @register="handleFormRegister" />
          </div>
        </div>

        <div v-else-if="!loading" class="empty-state">
          <a-empty description="暂无项目信息">
            <a-button type="primary" @click="handleCreate"> 创建项目 </a-button>
          </a-empty>
        </div>
      </a-spin>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, watch, computed, nextTick } from 'vue';
  import { EditOutlined, ReloadOutlined, EyeOutlined } from '@ant-design/icons-vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useProjectContext } from '/@/hooks/web/useProjectContext';
  import { getInfo, update } from '/@/api/project/core/projectBaseInfo';

  import { to } from '/@/utils/xh';
  import dayjs from 'dayjs';
  import { BasicForm, useForm, FormSchema } from '/@/components/Form';

  const props = defineProps({
    projectId: {
      type: String,
      default: '',
    },
  });

  // 优先使用props中的项目ID，如果没有则使用context fallback
  const { projectId: contextProjectId } = useProjectContext();
  const projectId = ref(props.projectId || contextProjectId.value);

  const { createMessage } = useMessage();

  const loading = ref(false);
  const editMode = ref(false);
  const saveLoading = ref(false);
  const projectInfo = ref<any>(null);

  // 表单配置
  const formSchemas = computed((): FormSchema[] => [
    {
      field: 'code',
      label: '项目编码',
      component: 'Input',
      componentProps: {
        placeholder: '系统自动生成',
        readonly: true,
      },
      colProps: { span: 12 },
    },
    {
      field: 'status',
      label: '状态',
      component: 'Select',
      componentProps: {
        placeholder: '请选择状态',
        allowClear: true,
        disabled: !editMode.value,
        options: [
          { fullName: '规划中', id: 'PLANNING' },
          { fullName: '执行中', id: 'EXECUTING' },
          { fullName: '跟踪中', id: 'TRACKING' },
          { fullName: '已签约', id: 'SIGNED' },
          { fullName: '暂停', id: 'SUSPENDED' },
          { fullName: '已完成', id: 'COMPLETED' },
          { fullName: '已取消', id: 'CANCELLED' },
        ],
      },
      colProps: { span: 12 },
    },
    {
      field: 'fullName',
      label: '项目名称',
      component: 'Input',
      required: editMode.value,
      componentProps: {
        placeholder: '请输入项目名称',
        disabled: !editMode.value,
      },
      colProps: { span: 24 },
      rules: editMode.value ? [{ required: true, message: '请输入项目名称', trigger: 'blur' }] : [],
    },
    {
      field: 'description',
      label: '描述',
      component: 'Textarea',
      componentProps: {
        placeholder: '请输入项目目标',
        rows: 3,
        disabled: !editMode.value,
      },
      colProps: { span: 24 },
    },
    {
      field: 'managerId',
      label: '项目经理',
      component: 'UserSelect',
      componentProps: {
        placeholder: '请选择项目经理',
        modalTitle: '选择项目经理',
        selectType: 'all',
        multiple: false,
        allowClear: true,
        disabled: !editMode.value,
      },
      colProps: { span: 12 },
    },
    {
      field: 'sponsorId',
      label: '项目发起人',
      component: 'UserSelect',
      componentProps: {
        placeholder: '请选择项目发起人',
        modalTitle: '选择项目发起人',
        selectType: 'all',
        multiple: false,
        allowClear: true,
        disabled: !editMode.value,
      },
      colProps: { span: 12 },
    },
    {
      field: 'plannedStartDate',
      label: '日历开始时间',
      component: 'DatePicker',
      componentProps: {
        placeholder: '请选择开始时间',
        style: { width: '100%' },
        disabled: !editMode.value,
      },
      colProps: { span: 12 },
    },
    {
      field: 'plannedEndDate',
      label: '日历结束时间',
      component: 'DatePicker',
      componentProps: {
        placeholder: '请选择结束时间',
        style: { width: '100%' },
        disabled: !editMode.value,
      },
      colProps: { span: 12 },
    },
    {
      field: 'strategicObjective',
      label: '战略目标',
      component: 'Select',
      componentProps: {
        placeholder: '请选择战略目标',
        allowClear: true,
        disabled: !editMode.value,
        options: [
          { fullName: '创新驱动', id: '1' },
          { fullName: '市场拓展', id: '2' },
          { fullName: '效率提升', id: '3' },
          { fullName: '成本控制', id: '4' },
        ],
      },
      colProps: { span: 12 },
    },
    {
      field: 'priority',
      label: '优先级',
      component: 'Select',
      componentProps: {
        placeholder: '请选择优先级',
        allowClear: true,
        disabled: !editMode.value,
        options: [
          { fullName: '低', id: 'low' },
          { fullName: '中', id: 'medium' },
          { fullName: '高', id: 'high' },
          { fullName: '紧急', id: 'urgent' },
        ],
      },
      colProps: { span: 12 },
    },
    {
      field: 'estimatedWorkload',
      label: '工作量估算（人天）',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入工作量估算',
        min: 0,
        precision: 1,
        style: { width: '100%' },
        addonAfter: '人天',
        disabled: !editMode.value,
      },
      colProps: { span: 12 },
    },
    {
      field: 'riskLevel',
      label: '风险等级',
      component: 'Select',
      componentProps: {
        placeholder: '请选择风险等级',
        allowClear: true,
        disabled: !editMode.value,
        options: [
          { fullName: '低', id: 'low' },
          { fullName: '中', id: 'medium' },
          { fullName: '高', id: 'high' },
        ],
      },
      colProps: { span: 12 },
    },
    {
      field: 'departmentId',
      label: '所属部门',
      component: 'DepSelect',
      required: editMode.value,
      componentProps: {
        placeholder: '请选择所属部门',
        modalTitle: '选择部门',
        selectType: 'all',
        multiple: false,
        allowClear: true,
        disabled: !editMode.value,
      },
      colProps: { span: 12 },
      rules: editMode.value ? [{ required: true, message: '请选择所属部门', trigger: 'change' }] : [],
    },
    {
      field: 'programId',
      label: '所属组合/项目群',
      component: 'Select',
      componentProps: {
        placeholder: '请选择所属组合/项目群',
        showSearch: true,
        allowClear: true,
        disabled: !editMode.value,
        options: [
          { fullName: '软件产品研发和交付', id: '1' },
          { fullName: '数字化转型项目群', id: '2' },
          { fullName: '基础设施建设项目群', id: '3' },
        ],
      },
      colProps: { span: 12 },
    },
    {
      field: 'projectScope',
      label: '备注',
      component: 'Textarea',
      componentProps: {
        placeholder: '请输入项目范围',
        rows: 3,
        disabled: !editMode.value,
      },
      colProps: { span: 24 },
    },
  ]);

  // 注册表单
  const [registerForm, { setFieldsValue, validate }] = useForm({
    labelWidth: 120,
    schemas: formSchemas,
    showActionButtonGroup: false, // 不显示默认的操作按钮，使用自定义按钮
    baseColProps: { span: 24 },
  });

  // 表单是否已注册
  const formRegistered = ref(false);

  // 处理表单注册
  const handleFormRegister = async (formInstance: any) => {
    registerForm(formInstance);
    formRegistered.value = true;

    // 表单注册完成后立即设置表单值
    if (projectInfo.value) {
      await nextTick();
      await setFormValues();
    }
  };

  // 加载项目信息
  const loadProjectInfo = async () => {
    if (!projectId.value) {
      console.warn('项目ID为空，无法加载项目信息');
      return;
    }

    loading.value = true;
    try {
      const [err, response] = await to(getInfo(projectId.value));
      if (!err && response && response.code === 200) {
        projectInfo.value = response.data;

        // 等待表单注册完成后设置表单值
        await nextTick();
        if (formRegistered.value) {
          await setFormValues();
        }
      } else {
        createMessage.error(response?.msg || err?.message || '加载项目信息失败');
      }
    } catch (error) {
      console.error('加载项目信息失败:', error);
      createMessage.error('加载项目信息失败');
    } finally {
      loading.value = false;
    }
  };

  // 设置表单值的通用方法
  const setFormValues = async () => {
    if (!projectInfo.value || !formRegistered.value) return;

    try {
      const formValues = {
        // 基本字段
        code: projectInfo.value.code,
        status: projectInfo.value.status,
        fullName: projectInfo.value.fullName,
        description: projectInfo.value.description,
        managerId: projectInfo.value.managerId,
        sponsorId: projectInfo.value.sponsorId,
        plannedStartDate: projectInfo.value.plannedStartDate || null,
        plannedEndDate: projectInfo.value.plannedEndDate || null,
        investmentBudget: projectInfo.value.investmentBudget,
        strategicObjective: projectInfo.value.strategicObjective,
        priority: projectInfo.value.priority,
        estimatedWorkload: projectInfo.value.estimatedWorkload,
        riskLevel: projectInfo.value.riskLevel,
        departmentId: projectInfo.value.departmentId,
        programId: projectInfo.value.programId,
        projectScope: projectInfo.value.projectScope,
      };

      await setFieldsValue(formValues);
    } catch (error) {
      console.warn('设置表单值失败:', error);
    }
  };

  // 编辑项目
  const handleEdit = async () => {
    editMode.value = true;

    // 等待表单渲染并注册完成
    await nextTick();

    // 等待表单注册完成
    let retryCount = 0;
    const maxRetries = 10;

    while (!formRegistered.value && retryCount < maxRetries) {
      await new Promise(resolve => setTimeout(resolve, 50));
      retryCount++;
    }

    // 设置表单值
    if (projectInfo.value && formRegistered.value) {
      try {
        await setFormValues();
      } catch (error) {
        console.warn('设置表单值失败:', error);
        createMessage.error('表单初始化失败，请重试');
      }
    } else {
      console.warn('表单未注册或项目信息为空');
      createMessage.error('表单初始化失败，请重试');
    }
  };

  // 保存项目
  const handleSave = async () => {
    try {
      // 表单验证
      const formData = await validate();

      saveLoading.value = true;

      // 处理日期格式
      const submitData = {
        ...formData,
        plannedStartDate:
          formData.plannedStartDate && dayjs.isDayjs(formData.plannedStartDate) ? formData.plannedStartDate.format('YYYY-MM-DD') : formData.plannedStartDate,
        plannedEndDate:
          formData.plannedEndDate && dayjs.isDayjs(formData.plannedEndDate) ? formData.plannedEndDate.format('YYYY-MM-DD') : formData.plannedEndDate,
      };

      const response = await update(projectId.value, submitData);
      if (response && response.code === 200) {
        createMessage.success(response.msg || '项目信息更新成功');
        editMode.value = false;
        await loadProjectInfo(); // 重新加载数据
        // 重新加载数据后，重新设置表单值为查看模式
        await nextTick();
        if (formRegistered.value && projectInfo.value) {
          await setFormValues();
        }
      } else {
        createMessage.error(response?.msg || '更新项目信息失败');
      }
    } catch (error) {
      console.error('保存项目信息失败:', error);
      createMessage.error('保存项目信息失败');
    } finally {
      saveLoading.value = false;
    }
  };

  // 取消编辑
  const handleCancel = async () => {
    editMode.value = false;
    // 重新设置表单值为查看模式
    await nextTick();
    if (formRegistered.value && projectInfo.value) {
      await setFormValues();
    }
  };

  // 创建项目
  const handleCreate = () => {
    createMessage.info('创建项目功能开发中...');
  };

  // 刷新
  const handleRefresh = () => {
    loadProjectInfo();
  };

  // 监听props项目ID变化
  watch(
    () => props.projectId,
    newProjectId => {
      projectId.value = newProjectId;
      if (newProjectId) {
        loadProjectInfo();
      }
    },
    { immediate: true },
  );

  // 监听context项目ID变化（当没有props时使用）
  watch(
    contextProjectId,
    newProjectId => {
      if (!props.projectId && newProjectId) {
        console.log('🔄 [项目信息] context项目切换:', newProjectId);
        projectId.value = newProjectId;
        loadProjectInfo();
      }
    },
    { immediate: false },
  );

  onMounted(() => {
    if (projectId.value) {
      loadProjectInfo();
    }
  });
</script>

<style lang="less" scoped>
  .project-info {
    background-color: var(--section-bg-color);

    .page-header {
      border-radius: 6px;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
      padding: 16px;
    }

    .project-detail {
      background: var(--content-bg-color);

      .ant-card {
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
      }
    }

    // 项目表单样式
    .project-form {
      padding: 16px;
      background: #fff;
      border-radius: 6px;
    }

    .empty-state {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 400px;
      border-radius: 6px;
    }
  }

  // 响应式布局
  @media (max-width: 768px) {
    .project-info {
      .project-form {
        padding: 12px;
      }
    }
  }
</style>
