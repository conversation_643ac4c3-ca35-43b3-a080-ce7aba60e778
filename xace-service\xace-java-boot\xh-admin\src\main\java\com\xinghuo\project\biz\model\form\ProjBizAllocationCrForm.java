package com.xinghuo.project.biz.model.form;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 项目业务分部分配 创建表单
 * 
 * <AUTHOR>
 * @version V3.0
 * @copyright 易趋集团
 * @date 2024-01-01
 */
@Data
public class ProjBizAllocationCrForm {

    /**
     * 项目基础ID
     */
    private String projBaseId;

    /**
     * 商机ID
     */
    private String opportunityId;

    /**
     * 合同ID
     */
    private String contractId;

    /**
     * 主业务ID（必填）
     */
    private String businessId;

    /**
     * 业务类型（必填）1-商机 2-合同 3-收款 4-付款
     */
    private Integer businessType;

    /**
     * 一部营收分配
     */
    private BigDecimal ybAmount;

    /**
     * 二部营收分配
     */
    private BigDecimal ebAmount;

    /**
     * 交付营收分配
     */
    private BigDecimal jfAmount;

    /**
     * 综合营收分配
     */
    private BigDecimal otherAmount;

    /**
     * 一部外采分配
     */
    private BigDecimal outYbAmount;

    /**
     * 二部外采分配
     */
    private BigDecimal outEbAmount;

    /**
     * 交付外采分配
     */
    private BigDecimal outJfAmount;

    /**
     * 综合外采分配
     */
    private BigDecimal outOtherAmount;

    /**
     * 分配备注
     */
    private String remarks;

    /**
     * 验证表单数据
     */
    public boolean validate() {
        if (businessId == null || businessId.trim().isEmpty()) {
            return false;
        }
        
        if (businessType == null || businessType < 1 || businessType > 4) {
            return false;
        }
        
        // 至少要有一个分配金额大于0
        BigDecimal totalAmount = getTotalAmount().add(getTotalOutAmount());
        return totalAmount.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * 获取营收分配总额
     */
    public BigDecimal getTotalAmount() {
        return (ybAmount != null ? ybAmount : BigDecimal.ZERO)
            .add(ebAmount != null ? ebAmount : BigDecimal.ZERO)
            .add(jfAmount != null ? jfAmount : BigDecimal.ZERO)
            .add(otherAmount != null ? otherAmount : BigDecimal.ZERO);
    }

    /**
     * 获取外采分配总额
     */
    public BigDecimal getTotalOutAmount() {
        return (outYbAmount != null ? outYbAmount : BigDecimal.ZERO)
            .add(outEbAmount != null ? outEbAmount : BigDecimal.ZERO)
            .add(outJfAmount != null ? outJfAmount : BigDecimal.ZERO)
            .add(outOtherAmount != null ? outOtherAmount : BigDecimal.ZERO);
    }
}