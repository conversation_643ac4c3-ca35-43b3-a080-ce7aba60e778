import { defHttp } from '/@/utils/http/axios';

enum Api {
  GetList = '/api/project/template/workProductPlanTemplate/getList',
  GetListByKnStatus = '/api/project/template/workProductPlanTemplate/getListByKnStatus',
  GetDetailInfo = '/api/project/template/workProductPlanTemplate/getDetailInfo',
  GetInfo = '/api/project/template/workProductPlanTemplate/getInfo',
  Create = '/api/project/template/workProductPlanTemplate/create',
  Update = '/api/project/template/workProductPlanTemplate/update',
  Delete = '/api/project/template/workProductPlanTemplate/delete',
  BatchDelete = '/api/project/template/workProductPlanTemplate/batchDelete',
  UpdateKnStatus = '/api/project/template/workProductPlanTemplate/updateKnStatus',
  BatchUpdateKnStatus = '/api/project/template/workProductPlanTemplate/batchUpdateKnStatus',
  Publish = '/api/project/template/workProductPlanTemplate/publish',
  Archive = '/api/project/template/workProductPlanTemplate/archive',
  Copy = '/api/project/template/workProductPlanTemplate/copy',
  CheckNameExists = '/api/project/template/workProductPlanTemplate/checkNameExists',
  GetSelectList = '/api/project/template/workProductPlanTemplate/getSelectList',
}

// 获取交付物计划模板列表
export function getWorkProductPlanTemplateList(params: any) {
  return defHttp.post({
    url: Api.GetList,
    data: params,
  });
}

// 根据知识状态获取模板列表
export function getWorkProductPlanTemplateListByKnStatus(knStatusId: string) {
  return defHttp.get({
    url: `${Api.GetListByKnStatus}/${knStatusId}`,
  });
}

// 获取模板详情（包含交付物明细）
export function getWorkProductPlanTemplateDetailInfo(id: string) {
  return defHttp.get({
    url: `${Api.GetDetailInfo}/${id}`,
  });
}

// 获取模板基本信息
export function getWorkProductPlanTemplateInfo(id: string) {
  return defHttp.get({
    url: `${Api.GetInfo}/${id}`,
  });
}

// 创建交付物计划模板
export function createWorkProductPlanTemplate(params: any) {
  return defHttp.post({
    url: Api.Create,
    data: params,
  });
}

// 更新交付物计划模板
export function updateWorkProductPlanTemplate(id: string, params: any) {
  return defHttp.put({
    url: `${Api.Update}/${id}`,
    data: params,
  });
}

// 删除交付物计划模板
export function deleteWorkProductPlanTemplate(id: string) {
  return defHttp.delete({
    url: `${Api.Delete}/${id}`,
  });
}

// 批量删除交付物计划模板
export function batchDeleteWorkProductPlanTemplate(ids: string[]) {
  return defHttp.delete({
    url: Api.BatchDelete,
    data: ids,
  });
}

// 更新模板知识状态
export function updateWorkProductPlanTemplateKnStatus(id: string, knStatusId: string) {
  return defHttp.put({
    url: `${Api.UpdateKnStatus}/${id}?knStatusId=${encodeURIComponent(knStatusId)}`,
  });
}

// 批量更新知识状态
export function batchUpdateWorkProductPlanTemplateKnStatus(ids: string[], knStatusId: string) {
  return defHttp.put({
    url: `${Api.BatchUpdateKnStatus}?knStatusId=${encodeURIComponent(knStatusId)}`,
    data: ids,
  });
}

// 发布模板
export function publishWorkProductPlanTemplate(id: string) {
  return defHttp.put({
    url: `${Api.Publish}/${id}`,
  });
}

// 归档模板
export function archiveWorkProductPlanTemplate(id: string) {
  return defHttp.put({
    url: `${Api.Archive}/${id}`,
  });
}

// 复制模板
export function copyWorkProductPlanTemplate(id: string, newName: string) {
  return defHttp.post({
    url: `${Api.Copy}/${id}?newName=${encodeURIComponent(newName)}`,
  });
}

// 检查模板名称是否存在
export function checkWorkProductPlanTemplateNameExists(name: string, excludeId?: string) {
  return defHttp.get({
    url: Api.CheckNameExists,
    params: { name, excludeId },
  });
}

// 获取模板选择列表
export function getWorkProductPlanTemplateSelectList(keyword?: string, knStatusId?: string) {
  return defHttp.get({
    url: Api.GetSelectList,
    params: { keyword, knStatusId },
  });
}
