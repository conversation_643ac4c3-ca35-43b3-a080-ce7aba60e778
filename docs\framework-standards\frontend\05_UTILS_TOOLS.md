# 工具函数与实用工具

## 日期工具使用指南

### 概述

XACE 项目使用 dayjs 作为日期处理库，并在 `/@/utils/dateUtil.ts` 中提供了统一的日期格式化函数。

**重要约定**：使用 `formatToDate` 和 `formatToDateTime` 而不是 `formatDate` 和 `formatDateTime`。

### 核心日期函数

#### formatToDate - 日期格式化

```typescript
import { formatToDate } from '/@/utils/dateUtil';

// 基础用法
const dateStr = formatToDate(new Date());              // "2024-01-15"
const dateStr2 = formatToDate('2024-01-15T10:30:00'); // "2024-01-15"
const dateStr3 = formatToDate(undefined);             // 当前日期

// 自定义格式
const customFormat = formatToDate(new Date(), 'YYYY/MM/DD');  // "2024/01/15"
```

#### formatToDateTime - 日期时间格式化

```typescript
import { formatToDateTime } from '/@/utils/dateUtil';

// 基础用法
const dateTimeStr = formatToDateTime(new Date());              // "2024-01-15 10:30:45"
const dateTimeStr2 = formatToDateTime('2024-01-15T10:30:00'); // "2024-01-15 10:30:00"
const dateTimeStr3 = formatToDateTime(undefined);             // 当前日期时间

// 自定义格式
const customFormat = formatToDateTime(new Date(), 'YYYY/MM/DD HH:mm'); // "2024/01/15 10:30"
```

#### dateUtil - 高级日期操作

```typescript
import { dateUtil } from '/@/utils/dateUtil';

// 日期计算
const tomorrow = dateUtil().add(1, 'day').format('YYYY-MM-DD');
const lastWeek = dateUtil().subtract(7, 'day').format('YYYY-MM-DD');

// 日期比较
const isAfter = dateUtil('2024-01-15').isAfter('2024-01-01');
const isBefore = dateUtil('2024-01-15').isBefore('2024-12-31');

// 获取日期部分
const year = dateUtil().year();
const month = dateUtil().month() + 1;  // 注意：month() 返回 0-11
const day = dateUtil().date();
```

### 组件中的使用

#### 显示格式化日期

```vue
<template>
  <div>
    <!-- 显示日期 -->
    <span>{{ formatToDate(record.startDate) }}</span>
    
    <!-- 显示日期时间 -->
    <span>{{ formatToDateTime(record.createdAt) }}</span>
    
    <!-- 安全显示（处理空值） -->
    <span>{{ record.date ? formatToDate(record.date) : '-' }}</span>
  </div>
</template>

<script lang="ts" setup>
import { formatToDate, formatToDateTime } from '/@/utils/dateUtil';

interface Record {
  startDate: string;
  endDate: string;
  createdAt: string;
  date?: string;
}
</script>
```

#### 表格中的日期显示

```typescript
import { BasicColumn } from '/@/components/Table';
import { formatToDate, formatToDateTime } from '/@/utils/dateUtil';

export const columns: BasicColumn[] = [
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    width: 150,
    customRender: ({ text }) => formatToDateTime(text),
  },
  {
    title: '开始日期',
    dataIndex: 'startDate',
    width: 100,
    customRender: ({ text }) => text ? formatToDate(text) : '-',
  },
  {
    title: '日期范围',
    dataIndex: 'dateRange',
    width: 200,
    customRender: ({ record }) => {
      if (record.startDate && record.endDate) {
        return `${formatToDate(record.startDate)} ~ ${formatToDate(record.endDate)}`;
      }
      return '-';
    },
  },
];
```

#### 表单中的日期选择器

```typescript
import type { FormSchema } from '/@/components/Form/src/types/form';

export const formSchema: FormSchema[] = [
  {
    field: 'startDate',
    label: '开始日期',
    component: 'DatePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',  // 重要：设置值的格式
      style: { width: '100%' },
    },
  },
  {
    field: 'dateRange',
    label: '日期范围',
    component: 'DateRange',
    componentProps: {
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      placeholder: ['开始日期', '结束日期'],
      style: { width: '100%' },
    },
  },
  {
    field: 'createdAt',
    label: '创建时间',
    component: 'DatePicker',
    componentProps: {
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      showTime: true,
      style: { width: '100%' },
    },
  },
];
```

### 高级日期工具

#### 相对时间显示

```typescript
import { dateUtil, formatToDate } from '/@/utils/dateUtil';

export function getRelativeTime(date: string): string {
  const now = dateUtil();
  const target = dateUtil(date);
  const diffDays = now.diff(target, 'day');
  
  if (diffDays === 0) return '今天';
  if (diffDays === 1) return '昨天';
  if (diffDays === -1) return '明天';
  if (diffDays > 0 && diffDays < 7) return `${diffDays}天前`;
  if (diffDays < 0 && diffDays > -7) return `${-diffDays}天后`;
  
  return formatToDate(date);
}
```

#### 日期范围工具

```typescript
import { dateUtil } from '/@/utils/dateUtil';

// 获取本周范围
export function getThisWeekRange(): [string, string] {
  const start = dateUtil().startOf('week').format('YYYY-MM-DD');
  const end = dateUtil().endOf('week').format('YYYY-MM-DD');
  return [start, end];
}

// 获取本月范围
export function getThisMonthRange(): [string, string] {
  const start = dateUtil().startOf('month').format('YYYY-MM-DD');
  const end = dateUtil().endOf('month').format('YYYY-MM-DD');
  return [start, end];
}

// 获取最近N天
export function getRecentDays(days: number): [string, string] {
  const end = dateUtil().format('YYYY-MM-DD');
  const start = dateUtil().subtract(days - 1, 'day').format('YYYY-MM-DD');
  return [start, end];
}

// 日期验证
export function isValidDate(dateStr: string): boolean {
  return dateUtil(dateStr).isValid();
}

export function isDateInRange(date: string, start: string, end: string): boolean {
  const target = dateUtil(date);
  return target.isAfter(start) && target.isBefore(end);
}
```

## 常用工具函数

### 字符串处理工具

```typescript
// utils/stringUtil.ts

// 首字母大写
export function capitalize(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1);
}

// 驼峰转下划线
export function camelToSnake(str: string): string {
  return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
}

// 下划线转驼峰
export function snakeToCamel(str: string): string {
  return str.replace(/_([a-z])/g, (match, letter) => letter.toUpperCase());
}

// 截取字符串并添加省略号
export function truncate(str: string, length: number): string {
  return str.length > length ? str.substring(0, length) + '...' : str;
}

// 移除HTML标签
export function stripHtml(html: string): string {
  return html.replace(/<[^>]*>/g, '');
}
```

### 数字格式化工具

```typescript
// utils/numberUtil.ts

// 金额格式化
export function formatCurrency(
  amount: number | string, 
  currency = '¥',
  decimalPlaces = 2
): string {
  const num = typeof amount === 'string' ? parseFloat(amount) : amount;
  return `${currency}${num.toFixed(decimalPlaces).replace(/\d(?=(\d{3})+\.)/g, '$&,')}`;
}

// 数字千分位格式化
export function formatNumber(num: number | string): string {
  const number = typeof num === 'string' ? parseFloat(num) : num;
  return number.toLocaleString();
}

// 百分比格式化
export function formatPercent(num: number, decimalPlaces = 2): string {
  return `${(num * 100).toFixed(decimalPlaces)}%`;
}

// 文件大小格式化
export function formatFileSize(bytes: number): string {
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  if (bytes === 0) return '0 Bytes';
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return `${(bytes / Math.pow(1024, i)).toFixed(2)} ${sizes[i]}`;
}
```

### 数组处理工具

```typescript
// utils/arrayUtil.ts

// 数组去重
export function unique<T>(arr: T[]): T[] {
  return [...new Set(arr)];
}

// 对象数组根据属性去重
export function uniqueBy<T>(arr: T[], key: keyof T): T[] {
  const seen = new Set();
  return arr.filter(item => {
    const value = item[key];
    if (seen.has(value)) {
      return false;
    }
    seen.add(value);
    return true;
  });
}

// 数组分组
export function groupBy<T>(arr: T[], key: keyof T): Record<string, T[]> {
  return arr.reduce((groups, item) => {
    const group = String(item[key]);
    groups[group] = groups[group] || [];
    groups[group].push(item);
    return groups;
  }, {} as Record<string, T[]>);
}

// 数组排序
export function sortBy<T>(arr: T[], key: keyof T, direction: 'asc' | 'desc' = 'asc'): T[] {
  return [...arr].sort((a, b) => {
    const valueA = a[key];
    const valueB = b[key];
    
    if (valueA < valueB) return direction === 'asc' ? -1 : 1;
    if (valueA > valueB) return direction === 'asc' ? 1 : -1;
    return 0;
  });
}

// 数组分页
export function chunk<T>(arr: T[], size: number): T[][] {
  return Array.from({ length: Math.ceil(arr.length / size) }, (v, i) =>
    arr.slice(i * size, i * size + size)
  );
}
```

### 对象处理工具

```typescript
// utils/objectUtil.ts

// 深拷贝
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T;
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as unknown as T;
  }
  
  if (typeof obj === 'object') {
    const clonedObj = {} as T;
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }
  
  return obj;
}

// 删除对象中的空值
export function removeEmpty(obj: Record<string, any>): Record<string, any> {
  return Object.fromEntries(
    Object.entries(obj).filter(([_, value]) => 
      value !== null && value !== undefined && value !== ''
    )
  );
}

// 对象属性重命名
export function renameKeys(
  obj: Record<string, any>, 
  keyMap: Record<string, string>
): Record<string, any> {
  return Object.fromEntries(
    Object.entries(obj).map(([key, value]) => [
      keyMap[key] || key,
      value
    ])
  );
}

// 提取对象指定属性
export function pick<T, K extends keyof T>(
  obj: T, 
  keys: K[]
): Pick<T, K> {
  const result = {} as Pick<T, K>;
  keys.forEach(key => {
    if (key in obj) {
      result[key] = obj[key];
    }
  });
  return result;
}

// 排除对象指定属性
export function omit<T, K extends keyof T>(
  obj: T, 
  keys: K[]
): Omit<T, K> {
  const result = { ...obj };
  keys.forEach(key => {
    delete result[key];
  });
  return result;
}
```

### URL 和查询参数工具

```typescript
// utils/urlUtil.ts

// 对象转查询字符串
export function objectToQuery(obj: Record<string, any>): string {
  const params = new URLSearchParams();
  Object.entries(obj).forEach(([key, value]) => {
    if (value !== null && value !== undefined && value !== '') {
      params.append(key, String(value));
    }
  });
  return params.toString();
}

// 查询字符串转对象
export function queryToObject(query: string): Record<string, string> {
  const params = new URLSearchParams(query);
  const result: Record<string, string> = {};
  params.forEach((value, key) => {
    result[key] = value;
  });
  return result;
}

// 获取当前页面查询参数
export function getUrlParams(): Record<string, string> {
  return queryToObject(window.location.search);
}

// 更新URL查询参数
export function updateUrlParams(params: Record<string, any>): void {
  const url = new URL(window.location.href);
  Object.entries(params).forEach(([key, value]) => {
    if (value !== null && value !== undefined && value !== '') {
      url.searchParams.set(key, String(value));
    } else {
      url.searchParams.delete(key);
    }
  });
  window.history.replaceState({}, '', url.toString());
}
```

### 表单验证工具

```typescript
// utils/validationUtil.ts

// 邮箱验证
export function isEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// 手机号验证（中国大陆）
export function isPhoneNumber(phone: string): boolean {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
}

// 身份证号验证
export function isIdCard(idCard: string): boolean {
  const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
  return idCardRegex.test(idCard);
}

// 密码强度验证
export function validatePassword(password: string): {
  isValid: boolean;
  strength: 'weak' | 'medium' | 'strong';
  issues: string[];
} {
  const issues: string[] = [];
  let score = 0;
  
  if (password.length < 8) {
    issues.push('密码长度至少8位');
  } else {
    score++;
  }
  
  if (!/[a-z]/.test(password)) {
    issues.push('需要包含小写字母');
  } else {
    score++;
  }
  
  if (!/[A-Z]/.test(password)) {
    issues.push('需要包含大写字母');
  } else {
    score++;
  }
  
  if (!/\d/.test(password)) {
    issues.push('需要包含数字');
  } else {
    score++;
  }
  
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    issues.push('需要包含特殊字符');
  } else {
    score++;
  }
  
  let strength: 'weak' | 'medium' | 'strong';
  if (score <= 2) strength = 'weak';
  else if (score <= 4) strength = 'medium';
  else strength = 'strong';
  
  return {
    isValid: issues.length === 0,
    strength,
    issues
  };
}
```

### 本地存储工具

```typescript
// utils/storageUtil.ts

interface StorageOptions {
  expire?: number; // 过期时间（毫秒）
}

interface StorageData {
  value: any;
  expire?: number;
}

class Storage {
  private storage: globalThis.Storage;
  
  constructor(storage: globalThis.Storage = localStorage) {
    this.storage = storage;
  }
  
  set(key: string, value: any, options?: StorageOptions): void {
    const data: StorageData = { value };
    
    if (options?.expire) {
      data.expire = Date.now() + options.expire;
    }
    
    this.storage.setItem(key, JSON.stringify(data));
  }
  
  get<T = any>(key: string, defaultValue?: T): T | null {
    try {
      const item = this.storage.getItem(key);
      if (!item) return defaultValue || null;
      
      const data: StorageData = JSON.parse(item);
      
      // 检查是否过期
      if (data.expire && Date.now() > data.expire) {
        this.remove(key);
        return defaultValue || null;
      }
      
      return data.value;
    } catch {
      return defaultValue || null;
    }
  }
  
  remove(key: string): void {
    this.storage.removeItem(key);
  }
  
  clear(): void {
    this.storage.clear();
  }
  
  has(key: string): boolean {
    return this.storage.getItem(key) !== null;
  }
}

export const localStorage = new Storage(window.localStorage);
export const sessionStorage = new Storage(window.sessionStorage);
```

## 常见错误和解决方案

### 日期工具常见错误

```typescript
// ❌ 错误：使用了不存在的函数名
import { formatDate, formatDateTime } from '/@/utils/dateUtil';

// ✅ 正确：使用正确的函数名
import { formatToDate, formatToDateTime } from '/@/utils/dateUtil';

// ❌ 错误：没有处理空值
const dateStr = formatToDate(record.date);

// ✅ 正确：安全处理空值
const dateStr = record.date ? formatToDate(record.date) : '-';
// 或者
const dateStr = formatToDate(record.date || undefined);
```

### 性能优化注意事项

```typescript
// ❌ 避免：在模板中直接调用工具函数
<template>
  <div v-for="item in items" :key="item.id">
    {{ formatCurrency(item.price) }}
  </div>
</template>

// ✅ 推荐：使用计算属性
<template>
  <div v-for="item in formattedItems" :key="item.id">
    {{ item.formattedPrice }}
  </div>
</template>

<script setup>
const formattedItems = computed(() => 
  items.value.map(item => ({
    ...item,
    formattedPrice: formatCurrency(item.price)
  }))
);
</script>
```

## 日期处理常见问题与解决方案

### 问题1：XhDatePicker 组件类型错误

**问题描述**：
```
[Vue warn]: Invalid prop: type check failed for prop "value". Expected Number | String, got Object
```

**原因分析**：
- `XhDatePicker` 组件的 `value` 属性只接受 `Number | String` 类型
- 错误地传递了 dayjs 对象给组件

**错误示例**：
```typescript
// ❌ 错误：传递 dayjs 对象
setFieldsValue({
  plannedStartDate: dayjs(timestamp), // 这会导致类型错误
});
```

**正确示例**：
```typescript
// ✅ 正确：直接传递时间戳或字符串
setFieldsValue({
  plannedStartDate: timestamp, // 时间戳（数字）
  // 或
  plannedStartDate: '2024-01-15', // 日期字符串
});
```

### 问题2：自定义 formatDate 函数类型限制

**问题描述**：
自定义的 `formatDate` 函数类型定义为 `string`，无法处理后端返回的时间戳。

**错误示例**：
```typescript
// ❌ 错误：类型限制导致无法处理时间戳
function formatDate(date: string) {
  if (!date) return '-';
  return dayjs(date).format('YYYY-MM-DD');
}
```

**正确示例**：
```typescript
// ✅ 正确：使用项目统一的工具函数
import { formatToDate } from '/@/utils/dateUtil';

// formatToDate 支持多种输入类型：时间戳、字符串、Date对象
const formattedDate = formatToDate(timestamp); // 自动处理
```

### 问题3：表单组件映射混淆

**问题描述**：
不清楚表单配置中的 `DatePicker` 组件实际映射到哪个具体组件。

**解决方案**：
查看 `/@/components/Form/src/componentMap.ts` 了解组件映射关系：

```typescript
// 表单配置中的组件名 -> 实际组件
componentMap.set('DatePicker', XhDatePicker);     // 日期选择器
componentMap.set('DateRange', XhDateRange);       // 日期范围选择器
componentMap.set('TimePicker', XhTimePicker);     // 时间选择器
componentMap.set('TimeRange', XhTimeRange);       // 时间范围选择器
```

### 最佳实践总结

#### 1. 日期显示格式化
```typescript
// ✅ 推荐：使用统一的工具函数
import { formatToDate, formatToDateTime } from '/@/utils/dateUtil';

// 显示日期
const displayDate = formatToDate(timestamp);

// 显示日期时间
const displayDateTime = formatToDateTime(timestamp);

// 自定义格式
const customFormat = formatToDate(timestamp, 'MM-DD HH:mm');
```

#### 2. 表单日期字段处理
```typescript
// ✅ 表单配置
const formSchema: FormSchema[] = [
  {
    field: 'plannedStartDate',
    label: '计划开始日期',
    component: 'DatePicker', // 映射到 XhDatePicker
    componentProps: {
      format: 'YYYY-MM-DD',
      placeholder: '请选择计划开始日期',
    },
  },
];

// ✅ 设置表单值（直接传递后端数据）
setFieldsValue({
  plannedStartDate: projectInfo.plannedStartDate, // 时间戳或字符串
});
```

#### 3. 组件类型兼容性
```typescript
// ✅ XhDatePicker 组件特点
// - value 属性接受：Number（时间戳）| String（日期字符串）
// - 内部自动处理格式转换和显示
// - useTimestamp=true 时输出时间戳，false 时输出格式化字符串
```

#### 4. 避免重复造轮子
```typescript
// ❌ 避免：自定义日期格式化函数
function formatDate(date: string) {
  // 自定义实现...
}

// ✅ 推荐：使用项目统一工具
import { formatToDate } from '/@/utils/dateUtil';
```

## 开发检查清单

### 日期处理
- [ ] 使用 `formatToDate` 和 `formatToDateTime`
- [ ] 处理空值情况
- [ ] 在表单中设置 `valueFormat`
- [ ] 使用 `dateUtil` 进行复杂日期操作
- [ ] 确认组件类型要求（XhDatePicker 需要 Number | String）
- [ ] 避免自定义日期格式化函数
- [ ] 检查组件映射关系（componentMap.ts）

### 工具函数使用
- [ ] 优先使用项目提供的工具函数
- [ ] 避免重复造轮子
- [ ] 在计算属性中使用工具函数优化性能
- [ ] 适当使用类型定义确保安全性

### 性能考虑
- [ ] 避免在模板中直接调用复杂工具函数
- [ ] 使用计算属性缓存计算结果
- [ ] 大数据量处理时考虑分批处理
- [ ] 适当使用 Web Workers 处理耗时计算