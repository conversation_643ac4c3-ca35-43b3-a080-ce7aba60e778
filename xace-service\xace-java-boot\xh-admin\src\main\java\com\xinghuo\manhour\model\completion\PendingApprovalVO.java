package com.xinghuo.manhour.model.completion;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 待审批记录数据VO
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@Schema(description = "待审批记录数据")
public class PendingApprovalVO {

    @Schema(description = "工时记录ID")
    private String id;

    @Schema(description = "用户ID")
    private String userId;

    @Schema(description = "员工姓名")
    private String userName;

    @Schema(description = "分部ID")
    private String fbId;

    @Schema(description = "所属分部")
    private String fbName;

    @Schema(description = "填写月份")
    private String month;

    @Schema(description = "工时人月")
    private BigDecimal workMonth;

    @Schema(description = "当前状态")
    private Integer status;

    @Schema(description = "状态名称")
    private String statusName;

    @Schema(description = "审批人ID")
    private String approverId;

    @Schema(description = "审批人")
    private String approverName;

    @Schema(description = "提交时间")
    private String submitTime;

    @Schema(description = "等待天数")
    private Integer waitingDays;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "工作内容")
    private String workNote;

    @Schema(description = "优先级")
    private String priority;
}
