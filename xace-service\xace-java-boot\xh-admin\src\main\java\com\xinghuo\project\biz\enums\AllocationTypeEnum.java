package com.xinghuo.project.biz.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 分配类型枚举
 * 
 * <AUTHOR>
 * @version V2.0
 */
@Getter
@AllArgsConstructor
public enum AllocationTypeEnum {
    
    /**
     * 营收分配
     */
    REVENUE(1, "营收分配", "revenue"),
    
    /**
     * 外采分配
     */
    OUTSOURCING(2, "外采分配", "outsourcing"),
    
    /**
     * 待签外采
     */
    PENDING_OUTSOURCING(3, "待签外采", "pending_outsourcing"),
    
    /**
     * 外采已付
     */
    PAID_OUTSOURCING(4, "外采已付", "paid_outsourcing");

    private final Integer code;
    private final String message;
    private final String value;

    public static AllocationTypeEnum getByCode(Integer code) {
        for (AllocationTypeEnum type : AllocationTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}