package com.xinghuo.project.biz.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.project.biz.entity.BizCustomerEntity;
import com.xinghuo.project.biz.model.CustomerPagination;
import com.xinghuo.project.biz.service.BizCustomerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 客户管理控制器
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Tag(name = "客户管理", description = "客户管理相关接口")
@RestController
@RequestMapping("/api/project/biz/customer")
public class BizCustomerController {

    @Resource
    private BizCustomerService bizCustomerService;

    /**
     * 获取客户列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取客户列表")
    public ActionResult<PageListVO<BizCustomerEntity>> list(@RequestBody CustomerPagination pagination) {
        List<BizCustomerEntity> list = bizCustomerService.getList(pagination);
        PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
        return ActionResult.page(list, page);
    }

    /**
     * 根据客户类型获取客户列表
     */
    @GetMapping("/getListByCustType/{custType}")
    @Operation(summary = "根据客户类型获取客户列表")
    public ActionResult<List<BizCustomerEntity>> getListByCustType(
            @Parameter(description = "客户类型") @PathVariable String custType) {
        List<BizCustomerEntity> list = bizCustomerService.getListByCustType(custType);
        return ActionResult.success(list);
    }

    /**
     * 根据业务线获取客户列表
     */
    @GetMapping("/getListByCustLine/{custLine}")
    @Operation(summary = "根据业务线获取客户列表")
    public ActionResult<List<BizCustomerEntity>> getListByCustLine(
            @Parameter(description = "业务线") @PathVariable String custLine) {
        List<BizCustomerEntity> list = bizCustomerService.getListByCustLine(custLine);
        return ActionResult.success(list);
    }

    /**
     * 根据负责人获取客户列表
     */
    @GetMapping("/getListByLeader/{leader}")
    @Operation(summary = "根据负责人获取客户列表")
    public ActionResult<List<BizCustomerEntity>> getListByLeader(
            @Parameter(description = "负责人") @PathVariable String leader) {
        List<BizCustomerEntity> list = bizCustomerService.getListByLeader(leader);
        return ActionResult.success(list);
    }

    /**
     * 获取客户详情
     */
    @GetMapping("/getInfo/{id}")
    @Operation(summary = "获取客户详情")
    public ActionResult<BizCustomerEntity> getInfo(
            @Parameter(description = "客户ID") @PathVariable String id) {
        BizCustomerEntity entity = bizCustomerService.getInfo(id);
        return ActionResult.success(entity);
    }

    /**
     * 创建客户
     */
    @PostMapping("/create")
    @Operation(summary = "创建客户")
    public ActionResult<String> create(@RequestBody @Valid BizCustomerEntity entity) {
        String id = bizCustomerService.create(entity);
        return ActionResult.success("创建成功", id);
    }

    /**
     * 更新客户
     */
    @PutMapping("/update/{id}")
    @Operation(summary = "更新客户")
    public ActionResult<String> update(
            @Parameter(description = "客户ID") @PathVariable String id,
            @RequestBody @Valid BizCustomerEntity entity) {
        bizCustomerService.update(id, entity);
        return ActionResult.success("更新成功");
    }

    /**
     * 删除客户
     */
    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除客户")
    public ActionResult<String> delete(
            @Parameter(description = "客户ID") @PathVariable String id) {
        bizCustomerService.delete(id);
        return ActionResult.success("删除成功");
    }

    /**
     * 检查客户名称是否存在
     */
    @GetMapping("/checkNameExists")
    @Operation(summary = "检查客户名称是否存在")
    public ActionResult<Boolean> checkNameExists(
            @RequestParam String name,
            @RequestParam(required = false) String excludeId) {
        boolean exists = bizCustomerService.isExistByName(name, excludeId);
        return ActionResult.success(exists);
    }

    /**
     * 获取客户选择列表
     */
    @GetMapping("/getSelectList")
    @Operation(summary = "获取客户选择列表")
    public ActionResult<List<BizCustomerEntity>> getSelectList() {
        List<BizCustomerEntity> list = bizCustomerService.getSelectList();
        return ActionResult.success(list);
    }
}
