import { defHttp } from '/@/utils/http/axios';
import { ListResult } from '/@/api/model/baseModel';

/**
 * 客户联系人模型
 */
export interface ProjCustomerLinkmanModel {
  id: string;
  cuId: string;
  customerName?: string; // 客户单位名称
  linkman: string;
  telephone: string;
  status: number;
  topic: string;
  content: string;
  note: string;
  creatorUserId: string;
  creatorTime: string;
  lastModifyUserId: string;
  lastModifyTime: string;
}

/**
 * 客户联系人查询参数接口
 */
export interface ProjCustomerLinkmanQueryParams {
  cuId?: string;
  linkman?: string;
  telephone?: string;
  status?: number;
  pageSize?: number;
  currentPage?: number;
}

/**
 * 获取联系人列表
 * @param params 查询参数
 */
export function getLinkmanList(params?: ProjCustomerLinkmanQueryParams) {
  return defHttp.post<ListResult<ProjCustomerLinkmanModel>>({
    url: '/api/project/biz/customer/linkman/getList',
    data: params,
  });
}

/**
 * 获取联系人详情
 * @param id 联系人ID
 */
export function getLinkmanInfo(id: string) {
  return defHttp.get<ProjCustomerLinkmanModel>({
    url: `/api/project/biz/customer/linkman/${id}`,
  });
}

/**
 * 创建联系人
 * @param data 联系人数据
 */
export function createLinkman(data: any) {
  return defHttp.post<string>({
    url: '/api/project/biz/customer/linkman',
    data,
  });
}

/**
 * 更新联系人
 * @param id 联系人ID
 * @param data 联系人数据
 */
export function updateLinkman(id: string, data: any) {
  return defHttp.put<void>({
    url: `/api/project/biz/customer/linkman/${id}`,
    data,
  });
}

/**
 * 删除联系人
 * @param id 联系人ID
 */
export function deleteLinkman(id: string) {
  return defHttp.delete<void>({
    url: `/api/project/biz/customer/linkman/${id}`,
  });
}

/**
 * 更新联系人状态
 * @param id 联系人ID
 * @param status 状态值 1-有效，0-无效
 */
export function updateLinkmanStatus(id: string, status: number) {
  return defHttp.put<void>({
    url: `/api/project/biz/customer/linkman/updateStatus/${id}?status=${status}`,
  });
}

/**
 * 根据客户ID获取联系人列表
 * @param customerId 客户ID
 * @deprecated 使用 getLinkmanList 替代
 */
export function getCustomerLinkmanList(customerId: string) {
  return getLinkmanList({ cuId: customerId });
}

// 为了兼容新的API命名，添加别名导出
export const createCustomerLinkman = createLinkman;
export const updateCustomerLinkman = updateLinkman;
export const deleteCustomerLinkman = deleteLinkman;
export const updateCustomerLinkmanStatus = updateLinkmanStatus;
export const getCustomerLinkmanInfo = getLinkmanInfo;
