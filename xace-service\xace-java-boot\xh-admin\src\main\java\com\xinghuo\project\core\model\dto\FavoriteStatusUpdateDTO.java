package com.xinghuo.project.core.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 关注状态更新DTO
 * 
 * 用于接收前端传递的关注状态更新请求参数
 * 
 * <AUTHOR>
 * @date 2025-01-08
 */
@Data
@Schema(description = "关注状态更新请求")
public class FavoriteStatusUpdateDTO {

    /**
     * 目标关注状态
     */
    @Schema(description = "关注状态 (true: 关注, false: 取消关注)", required = true, example = "true")
    private boolean isFavorite;
}
