<template>
  <div class="opportunity-dashboard">
    <!-- 加载状态 -->
    <a-spin :spinning="loading">
      <!-- 商机不存在提示 -->
      <a-result v-if="!hasOpportunity && !loading" status="info" title="该项目暂无关联商机" sub-title="只有商机类型的项目才会有关联的商机信息">
        <template #extra>
          <a-button type="primary" @click="goToProjectInfo"> 查看项目信息 </a-button>
        </template>
      </a-result>

      <!-- 商机信息展示 -->
      <div v-else-if="hasOpportunity">
        <!-- 商机状态卡片 -->
        <div class="opportunity-header">
          <a-card :bordered="false">
            <div class="header-content">
              <div class="header-left">
                <h2 class="opportunity-title">{{ opportunityInfo.projectName }}</h2>
                <div class="opportunity-meta">
                  <a-space size="large">
                    <span>商机编号：{{ opportunityInfo.businessNo }}</span>
                    <span
                      >项目等级：<a-tag :color="getProjectLevelColor(opportunityInfo.projectLevel)">{{ opportunityInfo.projectLevel }}</a-tag></span
                    >
                    <span>商机类型：{{ getProjectTypeName(opportunityInfo.projType) }}</span>
                  </a-space>
                </div>
              </div>
              <div class="header-right">
                <a-tag :color="getStatusColor(opportunityInfo.status)" class="status-tag">
                  {{ getStatusName(opportunityInfo.status) }}
                </a-tag>
              </div>
            </div>
          </a-card>
        </div>

        <!-- 关键指标统计 -->
        <div class="key-metrics">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-card hoverable>
                <a-statistic title="预计总金额" :value="getTotalAmount()" :precision="1" suffix="万元" :value-style="{ color: '#cf1322' }">
                  <template #prefix>
                    <money-collect-outlined />
                  </template>
                </a-statistic>
              </a-card>
            </a-col>
            <a-col :span="6">
              <a-card hoverable>
                <a-statistic title="软件部金额" :value="opportunityInfo.deptMoney || 0" :precision="1" suffix="万元" :value-style="{ color: '#1890ff' }">
                  <template #prefix>
                    <fund-outlined />
                  </template>
                </a-statistic>
              </a-card>
            </a-col>
            <a-col :span="6">
              <a-card hoverable>
                <a-statistic title="外采金额" :value="opportunityInfo.purchaseMoney || 0" :precision="1" suffix="万元" :value-style="{ color: '#faad14' }">
                  <template #prefix>
                    <shopping-cart-outlined />
                  </template>
                </a-statistic>
              </a-card>
            </a-col>
            <a-col :span="6">
              <a-card hoverable>
                <a-statistic title="预计毛利" :value="opportunityInfo.profitMoney || 0" :precision="1" suffix="万元" :value-style="{ color: '#52c41a' }">
                  <template #prefix>
                    <rise-outlined />
                  </template>
                </a-statistic>
              </a-card>
            </a-col>
          </a-row>
        </div>

        <!-- 详细信息区域 -->
        <div class="opportunity-details">
          <a-row :gutter="16">
            <!-- 基本信息 -->
            <a-col :span="8">
              <a-card title="基本信息" :bordered="false">
                <a-descriptions :column="1" size="small">
                  <a-descriptions-item label="客户单位">
                    {{ opportunityInfo.custName || '-' }}
                  </a-descriptions-item>
                  <a-descriptions-item label="市场负责人">
                    {{ opportunityInfo.marketLinkmanName || '-' }}
                  </a-descriptions-item>
                  <a-descriptions-item label="售前负责人">
                    {{ opportunityInfo.presaleLinkmanName || '-' }}
                  </a-descriptions-item>
                  <a-descriptions-item label="项目负责人">
                    {{ opportunityInfo.projectLeaderName || '-' }}
                  </a-descriptions-item>
                  <a-descriptions-item label="所属分部">
                    {{ opportunityInfo.deptName || '-' }}
                  </a-descriptions-item>
                  <a-descriptions-item label="研发分部">
                    {{ opportunityInfo.yfDeptName || '-' }}
                  </a-descriptions-item>
                  <a-descriptions-item label="工时填写状态">
                    <a-tag :color="opportunityInfo.workStatus === 1 ? 'success' : 'default'">
                      {{ opportunityInfo.workStatus === 1 ? '可填写' : '已结束' }}
                    </a-tag>
                  </a-descriptions-item>
                </a-descriptions>
              </a-card>
            </a-col>

            <!-- 时间节点 -->
            <a-col :span="8">
              <a-card title="关键时间节点" :bordered="false">
                <a-descriptions :column="1" size="small">
                  <a-descriptions-item label="启动日期">
                    {{ formatDate(opportunityInfo.startDate) }}
                  </a-descriptions-item>
                  <a-descriptions-item label="预计落地日期">
                    {{ formatDate(opportunityInfo.evaSignMonth) }}
                  </a-descriptions-item>
                  <a-descriptions-item label="预计首笔回款">
                    {{ formatDate(opportunityInfo.evaFirstMonth) }}
                  </a-descriptions-item>
                  <a-descriptions-item label="预计二笔回款">
                    {{ formatDate(opportunityInfo.evaSecondMonth) }}
                  </a-descriptions-item>
                  <a-descriptions-item label="首次外采时间">
                    {{ formatDate(opportunityInfo.evaFirstexternalMonth) }}
                  </a-descriptions-item>
                  <a-descriptions-item label="二次外采时间">
                    {{ formatDate(opportunityInfo.evaSecondexternalMonth) }}
                  </a-descriptions-item>
                  <a-descriptions-item label="合同审核日期">
                    {{ formatDate(opportunityInfo.checkDate) }}
                  </a-descriptions-item>
                  <a-descriptions-item label="交底日期">
                    {{ formatDate(opportunityInfo.transDate) }}
                  </a-descriptions-item>
                </a-descriptions>
              </a-card>
            </a-col>

            <!-- 收款计划 -->
            <a-col :span="8">
              <a-card title="收款计划" :bordered="false">
                <a-descriptions :column="1" size="small">
                  <a-descriptions-item label="首笔回款金额">
                    {{ formatMoney(opportunityInfo.evaFirstAmount) }}
                  </a-descriptions-item>
                  <a-descriptions-item label="二笔回款金额">
                    {{ formatMoney(opportunityInfo.evaSecondAmount) }}
                  </a-descriptions-item>
                  <a-descriptions-item label="今年收款比例">
                    {{ opportunityInfo.yearMoneyRatio || '-' }}
                  </a-descriptions-item>
                  <a-descriptions-item label="今年收款金额">
                    {{ formatMoney(opportunityInfo.yearMoney) }}
                  </a-descriptions-item>
                  <a-descriptions-item label="首次外采金额">
                    {{ formatMoney(opportunityInfo.evaFirstexternalAmount) }}
                  </a-descriptions-item>
                  <a-descriptions-item label="二次外采金额">
                    {{ formatMoney(opportunityInfo.evaSecondexternalAmount) }}
                  </a-descriptions-item>
                  <a-descriptions-item label="累计工时"> {{ opportunityInfo.autoManhours || 0 }}人日 </a-descriptions-item>
                </a-descriptions>
              </a-card>
            </a-col>
          </a-row>
        </div>

        <!-- 合同分配信息 -->
        <div class="contract-allocation">
          <a-card title="合同分配信息" :bordered="false">
            <a-row :gutter="16">
              <a-col :span="12">
                <h4>分部金额分配</h4>
                <a-descriptions :column="2" size="small">
                  <a-descriptions-item label="一部金额">
                    {{ formatMoney(opportunityInfo.yfYbAmount) }}
                  </a-descriptions-item>
                  <a-descriptions-item label="二部金额">
                    {{ formatMoney(opportunityInfo.yfEbAmount) }}
                  </a-descriptions-item>
                  <a-descriptions-item label="交付金额">
                    {{ formatMoney(opportunityInfo.yfJfAmount) }}
                  </a-descriptions-item>
                  <a-descriptions-item label="综合金额">
                    {{ formatMoney(opportunityInfo.yfOtherAmount) }}
                  </a-descriptions-item>
                </a-descriptions>
              </a-col>
              <a-col :span="12">
                <h4>外采分配</h4>
                <a-descriptions :column="2" size="small">
                  <a-descriptions-item label="一部外采">
                    {{ formatMoney(opportunityInfo.outYbAmount) }}
                  </a-descriptions-item>
                  <a-descriptions-item label="二部外采">
                    {{ formatMoney(opportunityInfo.outEbAmount) }}
                  </a-descriptions-item>
                  <a-descriptions-item label="交付外采">
                    {{ formatMoney(opportunityInfo.outJfAmount) }}
                  </a-descriptions-item>
                  <a-descriptions-item label="综合外采">
                    {{ formatMoney(opportunityInfo.outOtherAmount) }}
                  </a-descriptions-item>
                </a-descriptions>
              </a-col>
            </a-row>
          </a-card>
        </div>

        <!-- 项目简介 -->
        <div class="project-content">
          <a-card title="项目简介" :bordered="false">
            <p>{{ opportunityInfo.projectContent || '暂无项目简介' }}</p>
            <div v-if="opportunityInfo.businessTag" class="business-tags">
              <span class="tag-label">商机标签：</span>
              <a-space>
                <a-tag v-for="tag in getBusinessTags()" :key="tag" :color="getBusinessTagColor(tag)">
                  {{ tag }}
                </a-tag>
              </a-space>
            </div>
            <div v-if="opportunityInfo.note" class="note-section">
              <h4>备注信息</h4>
              <p>{{ opportunityInfo.note }}</p>
            </div>
          </a-card>
        </div>

        <!-- 最新跟踪记录 -->
        <div class="latest-tracking">
          <a-card title="最新跟踪记录" :bordered="false">
            <template #extra>
              <a @click="goToTracking">查看全部</a>
            </template>
            <div v-if="opportunityInfo.lastNote" class="last-note">
              <p>{{ opportunityInfo.lastNote }}</p>
              <p class="note-time">更新时间：{{ formatDateTime(opportunityInfo.lastUpdatedAt) }}</p>
            </div>
            <a-empty v-else description="暂无跟踪记录" />
          </a-card>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, watch, inject } from 'vue';
  import { useRouter } from 'vue-router';
  import { MoneyCollectOutlined, FundOutlined, ShoppingCartOutlined, RiseOutlined } from '@ant-design/icons-vue';
  import { formatToDate, formatToDateTime } from '/@/utils/dateUtil';
  import { getBusinessInfoByProjectId, type BusinessModel } from '/@/api/project/business';
  import { useMessage } from '/@/hooks/web/useMessage';

  const props = defineProps<{
    projectId?: string;
  }>();

  const router = useRouter();
  const { createMessage } = useMessage();

  // 从父组件注入项目ID
  const projectId = inject('projectId', ref(''));

  const hasOpportunity = ref(false);
  const opportunityInfo = ref<BusinessModel>({} as BusinessModel);
  const loading = ref(false);

  // 格式化日期
  const formatDate = (date: string) => {
    return date ? formatToDate(date) : '-';
  };

  // 格式化日期时间
  const formatDateTime = (date: string) => {
    return date ? formatToDateTime(date) : '-';
  };

  // 获取项目类型名称
  const getProjectTypeName = (type: number) => {
    const typeMap = {
      1: '建设商机',
      2: '维护商机',
      3: '产品销售商机',
    };
    return typeMap[type] || '未知类型';
  };

  // 获取项目等级颜色
  const getProjectLevelColor = (level: string) => {
    const colorMap = {
      'A+': 'red',
      A: 'orange',
      B: 'blue',
      C: 'green',
      D: 'gray',
    };
    return colorMap[level] || 'default';
  };

  // 获取状态名称
  const getStatusName = (status: string) => {
    const statusMap = {
      '1': '跟踪中',
      '2': '方案报价中',
      '3': '商务谈判中',
      '4': '已签',
      '5': '已废弃',
      '6': '明年跟踪',
    };
    return statusMap[status] || '未知状态';
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    const colorMap = {
      '1': 'processing',
      '2': 'warning',
      '3': 'cyan',
      '4': 'success',
      '5': 'error',
      '6': 'purple',
    };
    return colorMap[status] || 'default';
  };

  // 获取总金额
  const getTotalAmount = () => {
    const first = opportunityInfo.value.evaFirstAmount || 0;
    const second = opportunityInfo.value.evaSecondAmount || 0;
    return first + second;
  };

  // 格式化金额
  const formatMoney = (amount: number) => {
    if (!amount) return '0万元';
    return `${amount.toFixed(1)}万元`;
  };

  // 获取商机标签
  const getBusinessTags = () => {
    if (!opportunityInfo.value.businessTag) return [];
    return opportunityInfo.value.businessTag.split(',').filter(tag => tag.trim());
  };

  // 获取商机标签颜色
  const getBusinessTagColor = (tag: string) => {
    const colorMap = {
      可靠项目: 'green',
      竞争项目: 'orange',
      暂无经费: 'red',
      智慧园区: 'blue',
      物联网: 'purple',
      大数据: 'cyan',
    };
    return colorMap[tag] || 'default';
  };

  // 跳转到项目信息
  const goToProjectInfo = () => {
    console.log('跳转到项目信息页面');
  };

  // 跳转到跟踪记录
  const goToTracking = () => {
    console.log('跳转到跟踪记录页面');
  };

  // 加载商机信息
  const loadOpportunityInfo = async () => {
    if (!projectId.value) {
      console.log('⚠️ [商机页面] 没有项目ID');
      hasOpportunity.value = false;
      return;
    }

    loading.value = true;
    try {
      console.log('📋 [商机页面] 开始加载商机信息, 项目ID:', projectId.value);
      const response = await getBusinessInfoByProjectId(projectId.value);

      if (response && response.code === 200 && response.data && response.data.list && response.data.list.length > 0) {
        // 找到了商机信息
        opportunityInfo.value = response.data.list[0];
        hasOpportunity.value = true;
        console.log('✅ [商机页面] 商机信息加载成功:', opportunityInfo.value);
      } else {
        // 没有找到商机信息
        hasOpportunity.value = false;
        console.log('⚠️ [商机页面] 该项目没有关联的商机信息');
      }
    } catch (error) {
      console.error('❌ [商机页面] 加载商机信息失败:', error);
      createMessage.error('加载商机信息失败');
      hasOpportunity.value = false;
    } finally {
      loading.value = false;
    }
  };

  // 监听项目ID变化
  watch(
    projectId,
    (newProjectId, oldProjectId) => {
      console.log('🔄 [商机页面] 项目ID变化:', { 旧项目: oldProjectId, 新项目: newProjectId });
      if (newProjectId && newProjectId !== oldProjectId) {
        loadOpportunityInfo();
      }
    },
    { immediate: true },
  );

  onMounted(() => {
    if (projectId.value) {
      loadOpportunityInfo();
    }
  });
</script>

<style lang="less" scoped>
  .opportunity-dashboard {
    padding: 20px;

    .opportunity-header {
      margin-bottom: 24px;

      .header-content {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;

        .header-left {
          flex: 1;

          .opportunity-title {
            margin: 0 0 12px 0;
            font-size: 24px;
            font-weight: 500;
          }

          .opportunity-meta {
            color: #666;
          }
        }

        .header-right {
          .status-tag {
            font-size: 16px;
            padding: 4px 16px;
          }
        }
      }
    }

    .key-metrics {
      margin-bottom: 24px;
    }

    .opportunity-details {
      margin-bottom: 24px;
    }

    .contract-allocation {
      margin-bottom: 24px;

      h4 {
        margin-bottom: 16px;
        color: #262626;
        font-size: 14px;
        font-weight: 500;
      }
    }

    .project-content {
      margin-bottom: 24px;

      .business-tags {
        margin-top: 16px;

        .tag-label {
          color: #666;
          margin-right: 8px;
          font-weight: 500;
        }
      }

      .note-section {
        margin-top: 16px;
        padding-top: 16px;
        border-top: 1px solid #f0f0f0;

        h4 {
          margin-bottom: 8px;
          color: #262626;
          font-size: 14px;
          font-weight: 500;
        }

        p {
          color: #666;
          line-height: 1.6;
        }
      }
    }

    .latest-tracking {
      .last-note {
        p {
          margin-bottom: 8px;
          line-height: 1.6;
        }

        .note-time {
          color: #999;
          font-size: 12px;
        }
      }
    }
  }
</style>
