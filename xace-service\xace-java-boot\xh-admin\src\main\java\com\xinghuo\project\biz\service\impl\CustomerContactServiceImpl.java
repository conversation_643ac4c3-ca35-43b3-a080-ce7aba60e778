package com.xinghuo.project.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.biz.dao.CustomerContactMapper;
import com.xinghuo.project.biz.entity.CustomerContactEntity;
import com.xinghuo.project.biz.model.CustomerLinkmanPagination;
import com.xinghuo.project.biz.service.CustomerContactService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 客户联系人服务实现类
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Service
public class CustomerContactServiceImpl extends BaseServiceImpl<CustomerContactMapper, CustomerContactEntity> implements CustomerContactService {

    @Resource
    private CustomerContactMapper customerContactMapper;

    @Override
    public List<CustomerContactEntity> getList(CustomerLinkmanPagination pagination) {
        QueryWrapper<CustomerContactEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<CustomerContactEntity> lambda = queryWrapper.lambda();

        // 根据客户ID精确查询
        if (StrXhUtil.isNotEmpty(pagination.getCustomerId())) {
            lambda.eq(CustomerContactEntity::getCuId, pagination.getCustomerId());
        }

        // 根据联系人姓名模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getName())) {
            lambda.like(CustomerContactEntity::getLinkman, pagination.getName());
        }

        // 根据联系电话模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getPhone())) {
            lambda.like(CustomerContactEntity::getTelephone, pagination.getPhone());
        }

        // 根据状态精确查询
        if (pagination.getStatus() != null) {
            lambda.eq(CustomerContactEntity::getStatus, pagination.getStatus());
        }

        // 根据关键字搜索（姓名、电话）
        if (StrXhUtil.isNotEmpty(pagination.getKeyword())) {
            lambda.and(wrapper -> wrapper
                    .like(CustomerContactEntity::getLinkman, pagination.getKeyword())
                    .or()
                    .like(CustomerContactEntity::getTelephone, pagination.getKeyword())
            );
        }

        // 排序
        lambda.orderByDesc(CustomerContactEntity::getCreatorTime);

        return processDataType(queryWrapper, pagination);
    }

    @Override
    public List<CustomerContactEntity> getListByCustomerId(String customerId) {
        QueryWrapper<CustomerContactEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(CustomerContactEntity::getCuId, customerId);
        return this.list(queryWrapper);
    }

    @Override
    public CustomerContactEntity getInfo(String id) {
        return this.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String create(CustomerContactEntity entity) {
        String id = RandomUtil.snowId();
        entity.setId(id);
        this.save(entity);
        return id;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(String id, CustomerContactEntity entity) {
        entity.setId(id);
        this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        this.removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(String id, Integer status) {
        UpdateWrapper<CustomerContactEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .set(CustomerContactEntity::getStatus, status)
                .eq(CustomerContactEntity::getId, id);
        customerContactMapper.update(updateWrapper);
    }

    @Override
    public List<CustomerContactEntity> getSelectList(String customerId, String keyword) {
         QueryWrapper<CustomerContactEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(StrXhUtil.isNotEmpty(customerId), CustomerContactEntity::getCuId, customerId)
                .like(StrXhUtil.isNotEmpty(keyword), CustomerContactEntity::getLinkman, keyword);

        return this.list(queryWrapper);
    }
}
