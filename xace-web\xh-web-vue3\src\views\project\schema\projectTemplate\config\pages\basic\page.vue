<template>
  <div class="template-basic-info-page p-4">
    <!-- 页面标题 -->
    <div class="page-header mb-4">
      <h2 class="text-xl font-semibold mb-2">基本信息</h2>
      <p class="text-gray-600">配置项目模板的基本信息，包括名称、编码、描述等</p>
    </div>

    <a-spin :spinning="loading">
      <a-card :bordered="false" class="basic-info-card">
        <BasicForm @register="register" @submit="handleSubmit" />
      </a-card>
    </a-spin>
  </div>
</template>

<script lang="ts" setup>
  import { onMounted, ref, inject } from 'vue';
  import { BasicForm, useForm } from '/@/components/Form';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { getProjectTemplateInfo, updateProjectTemplate, generateProjectTemplateCode } from '/@/api/project/projectTemplate';
  import { basicInfoFormSchema } from '../../formSchemas';

  defineOptions({ name: 'ProjectTemplateBasicInfoPage' });

  const emit = defineEmits(['success']);

  // 注入模板ID
  const templateId = inject<any>('templateId');
  const { createMessage } = useMessage();
  const loading = ref(false);

  const [register, { setFieldsValue, resetFields, validate }] = useForm({
    labelWidth: 120,
    schemas: basicInfoFormSchema,
    showActionButtonGroup: true,
    actionColOptions: {
      span: 24,
    },
    submitButtonOptions: {
      text: '保存',
    },
    resetButtonOptions: {
      text: '重置',
    },
  });

  // 加载模板信息
  const loadTemplateInfo = async () => {
    if (!templateId?.value) return;

    loading.value = true;
    try {
      const response = await getProjectTemplateInfo(templateId.value);
      if (response.code === 200 && response.data) {
        await setFieldsValue({
          ...response.data,
          createdAt: response.data.createdAt ? new Date(response.data.createdAt).toLocaleString() : '',
          updatedAt: response.data.updatedAt ? new Date(response.data.updatedAt).toLocaleString() : '',
        });
      }
    } catch (error) {
      console.error('加载模板信息失败:', error);
      createMessage.error('加载模板信息失败');
    } finally {
      loading.value = false;
    }
  };

  // 生成模板编码
  const generateCode = async () => {
    try {
      const response = await generateProjectTemplateCode();
      if (response.code === 200) {
        await setFieldsValue({ code: response.data });
        createMessage.success('编码生成成功');
      }
    } catch (error) {
      console.error('生成编码失败:', error);
      createMessage.error('生成编码失败');
    }
  };

  // 提交处理
  const handleSubmit = async (values: any) => {
    if (!templateId?.value) {
      createMessage.error('模板ID无效');
      return;
    }

    loading.value = true;
    try {
      // 过滤掉只读字段
      const { createdAt, updatedAt, createdBy, updatedBy, ...submitData } = values;

      const response = await updateProjectTemplate(templateId.value, submitData);
      if (response.code === 200) {
        createMessage.success('保存成功');
        emit('success');
      } else {
        createMessage.error(response.msg || '保存失败');
      }
    } catch (error) {
      console.error('保存失败:', error);
      createMessage.error('保存失败');
    } finally {
      loading.value = false;
    }
  };

  onMounted(() => {
    loadTemplateInfo();
  });

  // 暴露生成编码方法给表单使用
  defineExpose({
    generateCode,
  });
</script>

<style lang="less" scoped>
  .template-basic-info-page {
    .page-header {
      h2 {
        color: rgba(0, 0, 0, 0.85);
        font-weight: 600;
      }
    }

    .basic-info-card {
      :deep(.ant-card-body) {
        padding: 24px;
      }
    }
  }
</style>
