<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinghuo.permission.dao.AuthorizeMapper">

    <resultMap id="ModuleVO" type="com.xinghuo.system.base.model.module.ModuleModel">
        <id column="F_Id" property="id"/>
        <result column="F_ParentId" property="parentId"/>
        <result column="F_FullName" property="fullName"/>
        <result column="F_EnCode" property="enCode"/>
        <result column="F_Type" property="type"/>
        <result column="F_UrlAddress" property="urlAddress"/>
        <result column="F_LinkTarget" property="linkTarget"/>
        <result column="F_Category" property="category"/>
        <result column="F_Icon" property="icon"/>
        <result column="F_SortCode" property="sortCode"/>
        <result column="F_PropertyJson" property="propertyJson"/>
        <result column="F_SystemId" property="systemId"/>
        <result column="F_EnabledHide" property="enabledHide"/>
    </resultMap>

    <resultMap id="ButtonVO" type="com.xinghuo.permission.model.button.ButtonModel">
        <id column="F_Id" property="id"/>
        <result column="F_ParentId" property="parentId"/>
        <result column="F_FullName" property="fullName"/>
        <result column="F_EnCode" property="enCode"/>
        <result column="F_Icon" property="icon"/>
        <result column="F_UrlAddress" property="urlAddress"/>
        <result column="F_ModuleId" property="moduleId"/>
    </resultMap>

    <resultMap id="ColumnVO" type="com.xinghuo.permission.model.column.ColumnModel">
        <id column="F_Id" property="id"/>
        <result column="F_ParentId" property="parentId"/>
        <result column="F_FullName" property="fullName"/>
        <result column="F_EnCode" property="enCode"/>
        <result column="F_BindTable" property="bindTable"/>
        <result column="F_BindTableName" property="bindTableName"/>
        <result column="F_ModuleId" property="moduleId"/>
    </resultMap>

    <resultMap id="ResourceVO" type="com.xinghuo.permission.model.resource.ResourceModel">
        <id column="F_Id" property="id"/>
        <result column="F_FullName" property="fullName"/>
        <result column="F_EnCode" property="enCode"/>
        <result column="F_ConditionJson" property="conditionJson"/>
        <result column="F_ConditionText" property="conditionText"/>
        <result column="F_AllData" property="allData"/>
        <result column="F_ModuleId" property="moduleId"/>
    </resultMap>

    <resultMap id="FormVO" type="com.xinghuo.permission.model.form.ModuleFormModel">
        <id column="F_Id" property="id"/>
        <result column="F_ParentId" property="parentId"/>
        <result column="F_FullName" property="fullName"/>
        <result column="F_EnCode" property="enCode"/>
        <result column="F_ModuleId" property="moduleId"/>
    </resultMap>

    <resultMap id="SystemVO" type="com.xinghuo.permission.model.SystemBaeModel">
        <id column="F_Id" property="id"/>
        <result column="F_FullName" property="fullName"/>
        <result column="F_EnCode" property="enCode"/>
        <result column="F_Icon" property="icon"/>
        <result column="F_PropertyJson" property="propertyJson"/>
        <result column="F_Description" property="description"/>
        <result column="F_SortCode" property="sortCode"/>
        <result column="F_EnabledMark" property="enabledMark"/>
    </resultMap>

    <select id="findModule" parameterType="String" resultMap="ModuleVO">
        SELECT * FROM base_module WHERE F_Id IN (SELECT F_ItemId FROM base_authorize WHERE F_ObjectId IN (${objectId}) AND F_ItemType = 'module') and F_EnabledMark = 1  Order by F_SortCode ASC
    </select>

    <select id="findButton" parameterType="String" resultMap="ButtonVO">
        SELECT * FROM base_modulebutton WHERE F_Id IN (SELECT F_ItemId FROM base_authorize WHERE F_ObjectId IN (${objectId}) AND F_ItemType = 'button') and F_EnabledMark = 1  Order by F_SortCode desc
    </select>


    <select id="findColumn" parameterType="String" resultMap="ColumnVO">
        SELECT * FROM base_modulecolumn WHERE F_Id IN (SELECT F_ItemId FROM base_authorize WHERE F_ObjectId IN (${objectId}) AND F_ItemType = 'column') and F_EnabledMark = 1  Order by F_SortCode desc
    </select>

    <select id="findResource" parameterType="String" resultMap="ResourceVO">
        SELECT * FROM base_moduledataauthorizescheme WHERE F_Id IN (SELECT F_ItemId FROM base_authorize WHERE F_ObjectId IN (${objectId}) AND F_ItemType = 'resource') and F_EnabledMark = 1  Order by F_SortCode desc
    </select>

    <select id="findForms" parameterType="String" resultMap="FormVO">
        SELECT * FROM base_moduleform WHERE F_Id IN (SELECT F_ItemId FROM base_authorize WHERE F_ObjectId IN (${objectId}) AND F_ItemType = 'form') and F_EnabledMark = 1  Order by F_SortCode desc
    </select>

    <select id="findSystem" parameterType="String" resultMap="SystemVO">
        SELECT * FROM base_system WHERE F_Id IN (SELECT F_ItemId FROM base_authorize WHERE F_ObjectId IN (${objectId}) AND F_ItemType = 'system') and F_EnabledMark = 1  Order by F_SortCode desc
    </select>

    <select id="findModuleAdmin" parameterType="Integer" resultMap="ModuleVO">
      SELECT * FROM base_module WHERE F_EnabledMark = #{mark} Order by F_SortCode Asc
    </select>

    <select id="findButtonAdmin" parameterType="Integer" resultMap="ButtonVO">
       SELECT * FROM base_modulebutton WHERE F_EnabledMark = #{mark} Order by F_SortCode desc
    </select>

    <select id="findColumnAdmin" parameterType="Integer" resultMap="ColumnVO">
        SELECT * FROM base_modulecolumn WHERE F_EnabledMark = #{mark} Order by F_SortCode desc
    </select>

    <select id="findResourceAdmin" parameterType="Integer" resultMap="ResourceVO">
       SELECT * FROM base_moduledataauthorizescheme WHERE F_EnabledMark = #{mark} Order by F_SortCode desc
    </select>

    <select id="findFormsAdmin" parameterType="Integer" resultMap="FormVO">
       SELECT * FROM base_moduleform WHERE F_EnabledMark = #{mark} Order by F_SortCode desc
    </select>

    <insert id="saveBatch" parameterType="String">
        INSERT INTO base_authorize (F_ID, F_ITEMTYPE, F_ITEMID, F_OBJECTTYPE, F_OBJECTID, F_SORTCODE, F_CREATORTIME, F_CREATORUSERID ) VALUES ${values}
    </insert>


    <insert id="savaAuth" parameterType="com.xinghuo.permission.entity.AuthorizeEntity" >
            INSERT INTO base_authorize (F_ID, F_ITEMTYPE, F_ITEMID, F_OBJECTTYPE, F_OBJECTID, F_SORTCODE, F_CREATORTIME,
            F_CREATORUSERID ) VALUES (
            #{id,jdbcType=VARCHAR},
            #{itemType,jdbcType=VARCHAR},
            #{itemId,jdbcType=VARCHAR},
            #{objectType,jdbcType=VARCHAR},
            #{objectId,jdbcType=VARCHAR},
            #{sortCode,jdbcType=NUMERIC},
            #{creatorTime, jdbcType=TIMESTAMP},
            #{creatorUserId,jdbcType=VARCHAR})
    </insert>

</mapper>
