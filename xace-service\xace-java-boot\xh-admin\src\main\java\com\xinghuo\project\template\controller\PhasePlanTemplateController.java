package com.xinghuo.project.template.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.project.template.entity.PhasePlanTemplateDetailEntity;
import com.xinghuo.project.template.entity.PhasePlanTemplateEntity;
import com.xinghuo.project.template.model.PhasePlanTemplatePagination;
import com.xinghuo.project.template.model.dto.PhasePlanTemplateVO;
import com.xinghuo.project.template.model.vo.PhasePlanTemplateSelectVO;
import com.xinghuo.project.template.service.PhasePlanTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 阶段计划模板管理控制器
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Tag(name = "阶段计划模板管理", description = "阶段计划模板管理相关接口")
@RestController
@RequestMapping("/api/project/template/phasePlanTemplate")
public class PhasePlanTemplateController {

    @Resource
    private PhasePlanTemplateService phasePlanTemplateService;

    /**
     * 获取阶段计划模板列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取阶段计划模板列表")
    public ActionResult<PageListVO<PhasePlanTemplateVO>> list(@RequestBody PhasePlanTemplatePagination pagination) {
        try {
            List<PhasePlanTemplateVO> list = phasePlanTemplateService.getList(pagination);
            PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
            return ActionResult.page(list, page);
        } catch (Exception e) {
            log.error("获取阶段计划模板列表失败", e);
            return ActionResult.fail("获取阶段计划模板列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据知识状态获取模板列表
     */
    @GetMapping("/getListByKnStatus/{knStatusId}")
    @Operation(summary = "根据知识状态获取模板列表")
    public ActionResult<List<PhasePlanTemplateEntity>> getListByKnStatus(
            @Parameter(description = "知识状态ID") @PathVariable String knStatusId) {
        try {
            List<PhasePlanTemplateEntity> list = phasePlanTemplateService.getListByKnStatus(knStatusId);
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("根据知识状态获取模板列表失败", e);
            return ActionResult.fail("获取模板列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取模板详情（包含阶段明细）
     */
    @GetMapping("/getDetailInfo/{id}")
    @Operation(summary = "获取模板详情")
    public ActionResult<PhasePlanTemplateVO> getDetailInfo(
            @Parameter(description = "模板ID") @PathVariable String id) {
        try {
            PhasePlanTemplateVO dto = phasePlanTemplateService.getDetailInfo(id);
            if (dto == null) {
                return ActionResult.fail("模板不存在");
            }
            return ActionResult.success(dto);
        } catch (Exception e) {
            log.error("获取模板详情失败", e);
            return ActionResult.fail("获取模板详情失败：" + e.getMessage());
        }
    }

    /**
     * 获取模板基本信息
     */
    @GetMapping("/getInfo/{id}")
    @Operation(summary = "获取模板基本信息")
    public ActionResult<PhasePlanTemplateEntity> getInfo(
            @Parameter(description = "模板ID") @PathVariable String id) {
        try {
            PhasePlanTemplateEntity entity = phasePlanTemplateService.getInfo(id);
            if (entity == null) {
                return ActionResult.fail("模板不存在");
            }
            return ActionResult.success(entity);
        } catch (Exception e) {
            log.error("获取模板基本信息失败", e);
            return ActionResult.fail("获取模板基本信息失败：" + e.getMessage());
        }
    }

    /**
     * 创建阶段计划模板
     */
    @PostMapping("/create")
    @Operation(summary = "创建阶段计划模板")
    public ActionResult<String> create(@RequestBody @Valid PhasePlanTemplateVO templateDTO) {
        try {
            String id = phasePlanTemplateService.create(templateDTO);
            return ActionResult.success("创建成功", id);
        } catch (Exception e) {
            log.error("创建阶段计划模板失败", e);
            return ActionResult.fail("创建阶段计划模板失败：" + e.getMessage());
        }
    }

    /**
     * 更新阶段计划模板
     */
    @PutMapping("/update/{id}")
    @Operation(summary = "更新阶段计划模板")
    public ActionResult<String> update(
            @Parameter(description = "模板ID") @PathVariable String id,
            @RequestBody @Valid PhasePlanTemplateVO templateDTO) {
        try {
            phasePlanTemplateService.update(id, templateDTO);
            return ActionResult.success("更新成功");
        } catch (Exception e) {
            log.error("更新阶段计划模板失败", e);
            return ActionResult.fail("更新阶段计划模板失败：" + e.getMessage());
        }
    }

    /**
     * 删除阶段计划模板
     */
    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除阶段计划模板")
    public ActionResult<String> delete(
            @Parameter(description = "模板ID") @PathVariable String id) {
        try {
            phasePlanTemplateService.delete(id);
            return ActionResult.success("删除成功");
        } catch (Exception e) {
            log.error("删除阶段计划模板失败", e);
            return ActionResult.fail("删除阶段计划模板失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除阶段计划模板
     */
    @DeleteMapping("/batchDelete")
    @Operation(summary = "批量删除阶段计划模板")
    public ActionResult<String> batchDelete(@RequestBody List<String> ids) {
        try {
            phasePlanTemplateService.batchDelete(ids);
            return ActionResult.success("批量删除成功");
        } catch (Exception e) {
            log.error("批量删除阶段计划模板失败", e);
            return ActionResult.fail("批量删除阶段计划模板失败：" + e.getMessage());
        }
    }

    /**
     * 更新模板知识状态
     */
    @PutMapping("/updateKnStatus/{id}")
    @Operation(summary = "更新模板知识状态")
    public ActionResult<String> updateKnStatus(
            @Parameter(description = "模板ID") @PathVariable String id,
            @RequestParam String knStatusId) {
        try {
            phasePlanTemplateService.updateKnStatus(id, knStatusId);
            return ActionResult.success("状态更新成功");
        } catch (Exception e) {
            log.error("更新模板知识状态失败", e);
            return ActionResult.fail("更新模板知识状态失败：" + e.getMessage());
        }
    }

    /**
     * 批量更新知识状态
     */
    @PutMapping("/batchUpdateKnStatus")
    @Operation(summary = "批量更新知识状态")
    public ActionResult<String> batchUpdateKnStatus(
            @RequestBody List<String> ids,
            @RequestParam String knStatusId) {
        try {
            phasePlanTemplateService.batchUpdateKnStatus(ids, knStatusId);
            return ActionResult.success("批量更新状态成功");
        } catch (Exception e) {
            log.error("批量更新模板知识状态失败", e);
            return ActionResult.fail("批量更新状态失败：" + e.getMessage());
        }
    }

    /**
     * 发布模板
     */
    @PutMapping("/publish/{id}")
    @Operation(summary = "发布模板")
    public ActionResult<String> publish(
            @Parameter(description = "模板ID") @PathVariable String id) {
        try {
            phasePlanTemplateService.publish(id);
            return ActionResult.success("发布成功");
        } catch (Exception e) {
            log.error("发布模板失败", e);
            return ActionResult.fail("发布模板失败：" + e.getMessage());
        }
    }

    /**
     * 归档模板
     */
    @PutMapping("/archive/{id}")
    @Operation(summary = "归档模板")
    public ActionResult<String> archive(
            @Parameter(description = "模板ID") @PathVariable String id) {
        try {
            phasePlanTemplateService.archive(id);
            return ActionResult.success("归档成功");
        } catch (Exception e) {
            log.error("归档模板失败", e);
            return ActionResult.fail("归档模板失败：" + e.getMessage());
        }
    }

    /**
     * 复制模板
     */
    @PostMapping("/copy/{id}")
    @Operation(summary = "复制模板")
    public ActionResult<String> copy(
            @Parameter(description = "模板ID") @PathVariable String id,
            @RequestParam String newName) {
        try {
            String newId = phasePlanTemplateService.copy(id, newName);
            return ActionResult.success("复制成功", newId);
        } catch (Exception e) {
            log.error("复制模板失败", e);
            return ActionResult.fail("复制模板失败：" + e.getMessage());
        }
    }

    /**
     * 检查模板名称是否存在
     */
    @GetMapping("/checkNameExists")
    @Operation(summary = "检查模板名称是否存在")
    public ActionResult<Boolean> checkNameExists(
            @RequestParam String name,
            @RequestParam(required = false) String excludeId) {
        try {
            boolean exists = phasePlanTemplateService.isExistByName(name, excludeId);
            return ActionResult.success(exists);
        } catch (Exception e) {
            log.error("检查模板名称失败", e);
            return ActionResult.fail("检查模板名称失败：" + e.getMessage());
        }
    }

    /**
     * 获取模板选择列表
     */
    @GetMapping("/getSelectList")
    @Operation(summary = "获取模板选择列表")
    public ActionResult<List<PhasePlanTemplateSelectVO>> getSelectList(
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String knStatusId) {
        try {
            List<PhasePlanTemplateVO> list = phasePlanTemplateService.getSelectList(keyword, knStatusId);
            List<PhasePlanTemplateSelectVO> listVO = BeanCopierUtils.copyList(list, PhasePlanTemplateSelectVO.class);

            // 构建fullName字段，格式：模板名称
            for (PhasePlanTemplateSelectVO vo : listVO) {
                if (vo.getName() != null) {
                    vo.setFullName(vo.getName());
                } else {
                    vo.setFullName(vo.getId());
                }

                // 设置知识状态名称
                if (vo.getKnStatusId() != null) {
                    switch (vo.getKnStatusId()) {
                        case "draft":
                            vo.setKnStatusName("未发布");
                            break;
                        case "published":
                            vo.setKnStatusName("已发布");
                            break;
                        case "archived":
                            vo.setKnStatusName("已归档");
                            break;
                        default:
                            vo.setKnStatusName(vo.getKnStatusId());
                            break;
                    }
                }
            }

            return ActionResult.success(listVO);
        } catch (Exception e) {
            log.error("获取模板选择列表失败", e);
            return ActionResult.fail("获取模板选择列表失败：" + e.getMessage());
        }
    }

    /**
     * 从标准阶段库添加阶段
     */
    @PostMapping("/addPhasesFromStandardLibrary/{templateId}")
    @Operation(summary = "从标准阶段库添加阶段")
    public ActionResult<List<PhasePlanTemplateDetailEntity>> addPhasesFromStandardLibrary(
            @Parameter(description = "模板ID") @PathVariable String templateId,
            @RequestBody List<String> phaseCodes) {
        try {
            List<PhasePlanTemplateDetailEntity> details = phasePlanTemplateService.addPhasesFromStandardLibrary(templateId, phaseCodes);
            return ActionResult.success("添加成功", details);
        } catch (Exception e) {
            log.error("从标准阶段库添加阶段失败", e);
            return ActionResult.fail("添加阶段失败：" + e.getMessage());
        }
    }

    /**
     * 更新阶段顺序
     */
    @PutMapping("/updatePhaseOrder/{templateId}")
    @Operation(summary = "更新阶段顺序")
    public ActionResult<String> updatePhaseOrder(
            @Parameter(description = "模板ID") @PathVariable String templateId,
            @RequestBody List<Map<String, Object>> phaseOrders) {
        try {
            phasePlanTemplateService.updatePhaseOrder(templateId, phaseOrders);
            return ActionResult.success("更新顺序成功");
        } catch (Exception e) {
            log.error("更新阶段顺序失败", e);
            return ActionResult.fail("更新阶段顺序失败：" + e.getMessage());
        }
    }

    /**
     * 获取模板统计信息
     */
    @PostMapping("/getTemplateStatistics")
    @Operation(summary = "获取模板统计信息")
    public ActionResult<List<Map<String, Object>>> getTemplateStatistics(@RequestBody Map<String, Object> params) {
        try {
            List<Map<String, Object>> statistics = phasePlanTemplateService.getTemplateStatistics(params);
            return ActionResult.success(statistics);
        } catch (Exception e) {
            log.error("获取模板统计信息失败", e);
            return ActionResult.fail("获取模板统计信息失败：" + e.getMessage());
        }
    }

    /**
     * 根据项目模板ID查询关联的阶段计划模板
     */
    @GetMapping("/getByProjectTemplateId/{projectTplId}")
    @Operation(summary = "根据项目模板ID查询关联的阶段计划模板")
    public ActionResult<List<PhasePlanTemplateEntity>> getByProjectTemplateId(
            @Parameter(description = "项目模板ID") @PathVariable String projectTplId) {
        try {
            List<PhasePlanTemplateEntity> list = phasePlanTemplateService.getByProjectTemplateId(projectTplId);
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("根据项目模板ID查询关联的阶段计划模板失败", e);
            return ActionResult.fail("查询失败：" + e.getMessage());
        }
    }

    /**
     * 更新模板与项目模板的关联关系
     */
    @PutMapping("/updateProjectTemplateRelations/{templateId}")
    @Operation(summary = "更新模板与项目模板的关联关系")
    public ActionResult<String> updateProjectTemplateRelations(
            @Parameter(description = "模板ID") @PathVariable String templateId,
            @RequestBody List<String> projectTemplateIds) {
        try {
            phasePlanTemplateService.updateProjectTemplateRelations(templateId, projectTemplateIds);
            return ActionResult.success("更新关联关系成功");
        } catch (Exception e) {
            log.error("更新模板与项目模板的关联关系失败", e);
            return ActionResult.fail("更新关联关系失败：" + e.getMessage());
        }
    }

    /**
     * 从项目阶段计划创建模板
     */
    @PostMapping("/createFromProject/{projectId}")
    @Operation(summary = "从项目阶段计划创建模板")
    public ActionResult<String> createFromProject(
            @Parameter(description = "项目ID") @PathVariable String projectId,
            @RequestParam String templateName,
            @RequestParam(required = false) String description) {
        try {
            String templateId = phasePlanTemplateService.createFromProject(projectId, templateName, description);
            return ActionResult.success("创建成功", templateId);
        } catch (Exception e) {
            log.error("从项目阶段计划创建模板失败", e);
            return ActionResult.fail("创建模板失败：" + e.getMessage());
        }
    }

    /**
     * 应用模板到项目
     */
    @PostMapping("/applyToProject/{templateId}")
    @Operation(summary = "应用模板到项目")
    public ActionResult<Map<String, Object>> applyToProject(
            @Parameter(description = "模板ID") @PathVariable String templateId,
            @RequestParam String projectId) {
        try {
            Map<String, Object> result = phasePlanTemplateService.applyToProject(templateId, projectId);
            return ActionResult.success("应用成功", result);
        } catch (Exception e) {
            log.error("应用模板到项目失败", e);
            return ActionResult.fail("应用模板失败：" + e.getMessage());
        }
    }

    /**
     * 获取模板使用情况
     */
    @GetMapping("/getTemplateUsageInfo/{id}")
    @Operation(summary = "获取模板使用情况")
    public ActionResult<Map<String, Object>> getTemplateUsageInfo(
            @Parameter(description = "模板ID") @PathVariable String id) {
        try {
            Map<String, Object> usageInfo = phasePlanTemplateService.getTemplateUsageInfo(id);
            return ActionResult.success(usageInfo);
        } catch (Exception e) {
            log.error("获取模板使用情况失败", e);
            return ActionResult.fail("获取模板使用情况失败：" + e.getMessage());
        }
    }
}
