package com.xinghuo.project.biz.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.xinghuo.common.annotation.UserPermission;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.constant.MsgCode;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.project.biz.entity.OpportunityEntity;
import com.xinghuo.project.biz.model.OpportunityPagination;
import com.xinghuo.project.biz.model.OpportunityStatusForm;
import com.xinghuo.project.biz.model.opportunity.OpportunityCrForm;
import com.xinghuo.project.biz.model.opportunity.OpportunityInfoVO;
import com.xinghuo.project.biz.model.opportunity.OpportunityUpForm;
import com.xinghuo.project.biz.model.vo.OpportunityListVO;
import com.xinghuo.project.biz.service.OpportunityService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 商机管理控制器
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Tag(name = "商机管理", description = "商机管理相关接口")
@RestController
@RequestMapping("/api/project/biz/opportunity")
public class OpportunityController {

    @Resource
    private OpportunityService opportunityService;

    /**
     * 获取商机列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取商机列表")
    @SaCheckPermission("project.business.opportunity.view")
    @UserPermission
    public ActionResult<PageListVO<OpportunityListVO>> list(@RequestBody OpportunityPagination pagination) {
        List<OpportunityListVO> voList = opportunityService.getListWithDetails(pagination);
        PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
        return ActionResult.page(voList, page);
    }

    /**
     * 根据客户ID获取商机列表
     */
    @GetMapping("/getListByCustId/{custId}")
    @Operation(summary = "根据客户ID获取商机列表")
    @Parameters({
            @Parameter(name = "custId", description = "客户ID", required = true)
    })
    @SaCheckPermission("project.business.opportunity.view")
    @UserPermission
    public ActionResult<List<OpportunityEntity>> getListByCustId(@PathVariable String custId) {
        List<OpportunityEntity> list = opportunityService.getListByCustId(custId);
        return ActionResult.success(list);
    }

    /**
     * 获取商机详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取商机详情")
    @Parameters({
            @Parameter(name = "id", description = "商机ID", required = true)
    })
    @SaCheckPermission("project.business.opportunity.view")
    @UserPermission
    public ActionResult<OpportunityInfoVO> getInfo(@PathVariable String id) {
        OpportunityInfoVO vo = opportunityService.getDetailInfo(id);
        if (vo == null) {
            return ActionResult.fail("商机不存在");
        }
        return ActionResult.success(vo);
    }

    /**
     * 创建商机
     */
    @PostMapping
    @Operation(summary = "创建商机")
    @SaCheckPermission("project.business.opportunity.create")
    @UserPermission
    @Transactional(rollbackFor = Exception.class)
    public ActionResult<String> create(@RequestBody @Valid OpportunityCrForm form) {
        OpportunityEntity entity = BeanCopierUtils.copy(form, OpportunityEntity.class);
        String id = opportunityService.create(entity);
        return ActionResult.success(id, MsgCode.SU001.get());
    }

    /**
     * 更新商机
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新商机")
    @Parameters({
            @Parameter(name = "id", description = "商机ID", required = true)
    })
    @SaCheckPermission("project.business.opportunity.edit")
    @UserPermission
    @Transactional(rollbackFor = Exception.class)
    public ActionResult<String> update(@PathVariable String id, @RequestBody @Valid OpportunityUpForm form) {
        OpportunityEntity entity = BeanCopierUtils.copy(form, OpportunityEntity.class);
        opportunityService.update(id, entity);
        return ActionResult.success(MsgCode.SU002.get());
    }

    /**
     * 删除商机
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除商机")
    @Parameters({
            @Parameter(name = "id", description = "商机ID", required = true)
    })
    @SaCheckPermission("project.business.opportunity.delete")
    @UserPermission
    @Transactional(rollbackFor = Exception.class)
    public ActionResult<String> delete(@PathVariable String id) {
        opportunityService.delete(id);
        return ActionResult.success(MsgCode.SU003.get());
    }

    /**
     * 更新商机状态
     */
    @PutMapping("/{id}/status")
    @Operation(summary = "更新商机状态")
    @Parameters({
            @Parameter(name = "id", description = "商机ID", required = true)
    })
    @SaCheckPermission("project.business.opportunity.edit")
    @UserPermission
    @Transactional(rollbackFor = Exception.class)
    public ActionResult<String> updateStatus(@PathVariable String id, @RequestBody @Valid OpportunityStatusForm form) {
        opportunityService.updateStatus(id, form);
        return ActionResult.success("状态更新成功");
    }

    /**
     * 更新最后跟踪记录
     */
    @PutMapping("/{id}/lastNote")
    @Operation(summary = "更新最后跟踪记录")
    @Parameters({
            @Parameter(name = "id", description = "商机ID", required = true)
    })
    @SaCheckPermission("project.business.opportunity.edit")
    @UserPermission
    @Transactional(rollbackFor = Exception.class)
    public ActionResult<String> updateLastNote(@PathVariable String id, @RequestParam String lastNote) {
        opportunityService.updateLastNote(id, lastNote);
        return ActionResult.success("跟踪记录更新成功");
    }

    /**
     * 更新工时填写状态
     */
    @PutMapping("/{id}/workStatus")
    @Operation(summary = "更新工时填写状态")
    @Parameters({
            @Parameter(name = "id", description = "商机ID", required = true)
    })
    @SaCheckPermission("project.business.opportunity.edit")
    @UserPermission
    @Transactional(rollbackFor = Exception.class)
    public ActionResult<String> updateWorkStatus(@PathVariable String id, @RequestParam Integer workStatus) {
        opportunityService.updateWorkStatus(id, workStatus);
        return ActionResult.success("工时状态更新成功");
    }

    /**
     * 商机转合同
     */
    @PostMapping("/{id}/convertToContract")
    @Operation(summary = "商机转合同")
    @Parameters({
            @Parameter(name = "id", description = "商机ID", required = true)
    })
    @SaCheckPermission("project.business.opportunity.convert")
    @UserPermission
    @Transactional(rollbackFor = Exception.class)
    public ActionResult<String> convertToContract(@PathVariable String id, @RequestParam String projectNo) {
        String contractId = opportunityService.convertToContract(id, projectNo);
        return ActionResult.success(contractId, "转换成功");
    }

    /**
     * 获取销售漏斗数据
     */
    @PostMapping("/salesFunnelData")
    @Operation(summary = "获取销售漏斗数据")
    @SaCheckPermission("project.business.opportunity.view")
    @UserPermission
    public ActionResult<List<Map<String, Object>>> getSalesFunnelData(@RequestBody Map<String, Object> params) {
        List<Map<String, Object>> data = opportunityService.getSalesFunnelData(params);
        return ActionResult.success(data);
    }

    /**
     * 获取商机预测数据
     */
    @PostMapping("/businessForecastData")
    @Operation(summary = "获取商机预测数据")
    @SaCheckPermission("project.business.opportunity.view")
    @UserPermission
    public ActionResult<List<Map<String, Object>>> getBusinessForecastData(@RequestBody Map<String, Object> params) {
        List<Map<String, Object>> data = opportunityService.getBusinessForecastData(params);
        return ActionResult.success(data);
    }

    /**
     * 获取赢单/输单分析数据
     */
    @PostMapping("/winLoseAnalysisData")
    @Operation(summary = "获取赢单/输单分析数据")
    @SaCheckPermission("project.business.opportunity.view")
    @UserPermission
    public ActionResult<List<Map<String, Object>>> getWinLoseAnalysisData(@RequestBody Map<String, Object> params) {
        List<Map<String, Object>> data = opportunityService.getWinLoseAnalysisData(params);
        return ActionResult.success(data);
    }
}
