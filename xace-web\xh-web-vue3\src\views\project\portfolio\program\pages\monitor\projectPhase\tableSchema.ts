import { BasicColumn } from '/@/components/Table';
import { Tag, Progress } from 'ant-design-vue';
import { h } from 'vue';

export function getTableSchemas(): BasicColumn[] {
  return [
    {
      title: '项目名称',
      dataIndex: 'projectName',
      width: 280,
      ellipsis: true,
    },
    {
      title: '项目编号',
      dataIndex: 'projectCode',
      width: 120,
    },
    {
      title: '项目经理',
      dataIndex: 'projectManager',
      width: 150,
      ellipsis: true,
    },
    {
      title: '所属部门',
      dataIndex: 'department',
      width: 150,
      ellipsis: true,
    },
    {
      title: '当前阶段',
      dataIndex: 'currentPhase',
      key: 'currentPhase',
      width: 180,
      customRender: ({ record }) => {
        return h('span', `${record.stageIndex}/${record.totalStages}: ${record.currentPhase}`);
      },
    },
    {
      title: '阶段状态',
      dataIndex: 'phaseStatus',
      width: 100,
      customRender: ({ record }) => {
        const colorMap = {
          正常进行: 'success',
          进行中: 'processing',
          刚启动: 'default',
          准备中: 'default',
          延期风险: 'warning',
          严重延期: 'error',
          已完成: 'success',
        };
        return h(Tag, { color: colorMap[record.phaseStatus] || 'default' }, () => record.phaseStatus);
      },
    },
    {
      title: '阶段进度',
      dataIndex: 'phaseProgress',
      key: 'phaseProgress',
      width: 120,
      customRender: ({ record }) => {
        return h(Progress, {
          percent: record.phaseProgress,
          size: 'small',
          status: record.phaseProgress === 100 ? 'success' : record.delayDays > 30 ? 'exception' : 'active',
        });
      },
    },
    {
      title: '阶段开始日期',
      dataIndex: 'phaseStartDate',
      width: 120,
    },
    {
      title: '阶段结束日期',
      dataIndex: 'phaseEndDate',
      width: 120,
    },
    {
      title: '延期天数',
      dataIndex: 'delayDays',
      width: 100,
      customRender: ({ record }) => {
        if (record.delayDays === 0) return '-';
        const color = record.delayDays > 30 ? '#f5222d' : record.delayDays > 7 ? '#faad14' : '#f5222d';
        return h('span', { style: { color } }, `${record.delayDays}天`);
      },
    },
    {
      title: '下一阶段',
      dataIndex: 'nextPhase',
      width: 150,
    },
    {
      title: '主要交付物',
      dataIndex: 'phaseDeliverable',
      width: 250,
      ellipsis: true,
    },
  ];
}
