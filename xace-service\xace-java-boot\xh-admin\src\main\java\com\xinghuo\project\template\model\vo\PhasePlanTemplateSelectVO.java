package com.xinghuo.project.template.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 阶段计划模板选择列表视图对象
 * 用于下拉选择框等场景，只包含必要的id和fullName字段
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@Data
@Schema(description = "阶段计划模板选择列表视图对象")
public class PhasePlanTemplateSelectVO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private String id;

    /**
     * 完整名称 (格式: 模板名称)
     * 用于option显示，符合fullName和id的规范要求
     */
    @Schema(description = "完整名称")
    private String fullName;

    /**
     * 模板名称 (用于构建fullName)
     */
    @Schema(description = "模板名称")
    private String name;

    /**
     * 模板描述
     */
    @Schema(description = "模板描述")
    private String description;

    /**
     * 知识状态ID
     */
    @Schema(description = "知识状态ID")
    private String knStatusId;

    /**
     * 知识状态名称
     */
    @Schema(description = "知识状态名称")
    private String knStatusName;

    /**
     * 阶段总数
     */
    @Schema(description = "阶段总数")
    private Integer phaseCount;

    /**
     * 总工期（所有阶段工期之和）
     */
    @Schema(description = "总工期")
    private Integer totalDuration;
}
