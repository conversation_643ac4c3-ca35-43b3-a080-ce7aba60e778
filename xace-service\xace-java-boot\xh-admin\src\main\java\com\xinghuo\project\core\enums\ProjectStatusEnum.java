package com.xinghuo.project.core.enums;

/**
 * 项目状态枚举
 */
public enum ProjectStatusEnum {
    
    NOT_STARTED("NOT_STARTED", "未启动"),
    IN_PROGRESS("IN_PROGRESS", "进行中"),
    COMPLETED("COMPLETED", "已完成"),
    CLOSED("CLOSED", "已关闭"),
    CANCELLED("CANCELLED", "已取消"),
    ON_HOLD("ON_HOLD", "已暂停"),
    DELAYED("DELAYED", "已延期");
    
    private final String code;
    private final String name;
    
    ProjectStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    /**
     * 根据代码获取枚举
     */
    public static ProjectStatusEnum getByCode(String code) {
        for (ProjectStatusEnum statusEnum : values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }
}
