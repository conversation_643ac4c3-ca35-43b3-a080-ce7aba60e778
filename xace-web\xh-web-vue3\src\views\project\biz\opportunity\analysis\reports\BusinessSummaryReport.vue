<template>
  <div class="business-summary-report">
    <a-card :bordered="false" title="商机汇总分析">
      <a-row :gutter="16" class="filter-row">
        <a-col :span="6">
          <a-form-item label="年份">
            <a-select v-model:value="queryParams.year" placeholder="请选择年份">
              <a-select-option :value="2024">2024年</a-select-option>
              <a-select-option :value="2025">2025年</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="部门">
            <a-select v-model:value="queryParams.deptId" placeholder="请选择部门">
              <a-select-option value="">全部</a-select-option>
              <a-select-option value="1">软件一部</a-select-option>
              <a-select-option value="2">软件二部</a-select-option>
              <a-select-option value="3">软件三部</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-button type="primary" @click="fetchData">
            <search-outlined />
            查询
          </a-button>
          <a-button style="margin-left: 8px" @click="resetQuery">
            <reload-outlined />
            重置
          </a-button>
        </a-col>
      </a-row>

      <!-- 汇总卡片 -->
      <a-row :gutter="16" class="summary-cards">
        <a-col :span="6">
          <a-card class="summary-card">
            <template #title>
              <span>商机总数</span>
            </template>
            <div class="card-content">
              <div class="card-value">{{ summaryData.totalCount }}</div>
              <div class="card-desc">当前筛选条件下的商机总数</div>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="summary-card">
            <template #title>
              <span>A+类商机</span>
            </template>
            <div class="card-content">
              <div class="card-value">{{ summaryData.aPlus }}</div>
              <div class="card-desc">必须落地的重点项目</div>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="summary-card">
            <template #title>
              <span>A类商机</span>
            </template>
            <div class="card-content">
              <div class="card-value">{{ summaryData.aClass }}</div>
              <div class="card-desc">上半年落地项目</div>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="summary-card">
            <template #title>
              <span>预计总金额</span>
            </template>
            <div class="card-content">
              <div class="card-value">{{ summaryData.totalAmount }}万</div>
              <div class="card-desc">当前筛选条件下的预计总金额</div>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 分类统计表格 -->
      <a-tabs v-model:activeKey="activeTab" class="report-tabs">
        <a-tab-pane key="aplus" tab="A+类商机">
          <a-table :columns="columns" :dataSource="aPlusData" :loading="loading" :pagination="false" rowKey="id">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'projectLevel'">
                <a-tag color="red">{{ record.projectLevel }}</a-tag>
              </template>
              <template v-if="column.key === 'status'">
                <a-tag :color="getStatusColor(record.status)">{{ record.status }}</a-tag>
              </template>
              <template v-if="column.key === 'action'">
                <a-button type="link" @click="handleView(record)">查看</a-button>
              </template>
            </template>
          </a-table>
        </a-tab-pane>
        <a-tab-pane key="aclass" tab="A类商机">
          <a-table :columns="columns" :dataSource="aClassData" :loading="loading" :pagination="false" rowKey="id">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'projectLevel'">
                <a-tag color="orange">{{ record.projectLevel }}</a-tag>
              </template>
              <template v-if="column.key === 'status'">
                <a-tag :color="getStatusColor(record.status)">{{ record.status }}</a-tag>
              </template>
              <template v-if="column.key === 'action'">
                <a-button type="link" @click="handleView(record)">查看</a-button>
              </template>
            </template>
          </a-table>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted } from 'vue';
  import { SearchOutlined, ReloadOutlined } from '@ant-design/icons-vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { getBusinessList } from '/@/api/project/business';

  const { createMessage } = useMessage();
  const loading = ref(false);
  const activeTab = ref('aplus');

  // 查询参数
  const queryParams = reactive({
    year: 2024,
    deptId: '',
  });

  // 汇总数据
  const summaryData = reactive({
    totalCount: 0,
    aPlus: 0,
    aClass: 0,
    totalAmount: 0,
  });

  // A+类商机数据
  const aPlusData = ref([]);
  // A类商机数据
  const aClassData = ref([]);

  // 表格列定义
  const columns = [
    {
      title: '项目名称',
      dataIndex: 'projectName',
      key: 'projectName',
      ellipsis: true,
    },
    {
      title: '项目编号',
      dataIndex: 'businessNo',
      key: 'businessNo',
      width: 120,
    },
    {
      title: '项目等级',
      dataIndex: 'projectLevel',
      key: 'projectLevel',
      width: 100,
    },
    {
      title: '客户单位',
      dataIndex: 'custName',
      key: 'custName',
      ellipsis: true,
      width: 150,
    },
    {
      title: '项目负责人',
      dataIndex: 'projectLeaderName',
      key: 'projectLeaderName',
      width: 120,
    },
    {
      title: '商机状态',
      dataIndex: 'status',
      key: 'status',
      width: 120,
    },
    {
      title: '预计金额(万)',
      dataIndex: 'yearMoney',
      key: 'yearMoney',
      width: 120,
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
    },
  ];

  // 获取商机状态对应的颜色
  function getStatusColor(status: string) {
    switch (status) {
      case '跟踪中':
        return 'blue';
      case '方案报价中':
        return 'cyan';
      case '商务谈判中':
        return 'orange';
      case '已签':
        return 'green';
      case '已废弃':
        return 'red';
      case '明年跟踪':
        return 'purple';
      default:
        return 'default';
    }
  }

  // 获取数据
  async function fetchData() {
    loading.value = true;
    try {
      const { items } = await getBusinessList({
        year: queryParams.year,
        deptId: queryParams.deptId,
        pageSize: 1000, // 获取足够多的数据进行分析
      });

      // 分类数据
      aPlusData.value = items.filter(item => item.projectLevel === 'A+');
      aClassData.value = items.filter(item => item.projectLevel === 'A');

      // 计算汇总数据
      summaryData.totalCount = items.length;
      summaryData.aPlus = aPlusData.value.length;
      summaryData.aClass = aClassData.value.length;
      summaryData.totalAmount = items.reduce((sum, item) => sum + (item.yearMoney || 0), 0).toFixed(2);
    } catch (error) {
      console.error('获取商机数据失败:', error);
      createMessage.error('获取商机数据失败');
    } finally {
      loading.value = false;
    }
  }

  // 重置查询条件
  function resetQuery() {
    queryParams.year = 2024;
    queryParams.deptId = '';
    fetchData();
  }

  // 查看商机详情
  function handleView(record) {
    // 这里可以调用打开商机详情抽屉的方法
    console.log('查看商机详情:', record);
  }

  // 组件挂载时获取数据
  onMounted(() => {
    fetchData();
  });
</script>

<style lang="less" scoped>
  .business-summary-report {
    .filter-row {
      margin-bottom: 16px;
    }

    .summary-cards {
      margin-bottom: 24px;

      .summary-card {
        .card-content {
          text-align: center;

          .card-value {
            font-size: 24px;
            font-weight: bold;
            color: #1890ff;
            margin-bottom: 8px;
          }

          .card-desc {
            color: rgba(0, 0, 0, 0.45);
            font-size: 14px;
          }
        }
      }
    }

    .report-tabs {
      margin-top: 16px;
    }
  }
</style>
