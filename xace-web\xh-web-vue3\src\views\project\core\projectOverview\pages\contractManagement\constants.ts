/**
 * 合同管理模块常量定义
 */

// 合同状态映射
export const CONTRACT_STATUS_MAP: Record<string, string> = {
  '1': '草稿',
  '2': '待审核',
  '3': '已审核',
  '4': '执行中',
  '5': '已完成',
  '6': '已终止',
};

// 收款状态映射
export const MONEY_STATUS_MAP: Record<string, string> = {
  '0': '未收款',
  '1': '部分收款',
  '2': '已收款',
};

// 验收状态映射
export const ACCDOC_STATUS_MAP: Record<string, string> = {
  '0': '未验收',
  '1': '初验完成',
  '2': '终验完成',
};

// 续签状态映射
export const CONTINUE_STATUS_MAP: Record<string, { id: number; fullName: string }[]> = [
  { id: 0, fullName: '正常' },
  { id: 1, fullName: '已续签' },
  { id: 9, fullName: '不续签' },
];

// 归档状态映射
export const ARCHIVE_STATUS_MAP: Record<string, { id: number; fullName: string }[]> = [
  { id: 0, fullName: '未归档' },
  { id: 1, fullName: '已归档' },
];

// 工时状态映射
export const WORK_STATUS_MAP: Record<string, { id: number; fullName: string }[]> = [
  { id: 0, fullName: '已结束' },
  { id: 1, fullName: '可填写' },
];

// 汇报频率选项
export const REPORT_FREQUENCY_OPTIONS = [
  { id: 'daily', fullName: '每日' },
  { id: 'weekly', fullName: '每周' },
  { id: 'monthly', fullName: '每月' },
  { id: 'quarterly', fullName: '每季度' },
  { id: 'yearly', fullName: '每年' },
];

// 日期字段列表
export const DATE_FIELDS = [
  'signDate',
  'bidDate',
  'commencementDate',
  'initialCheckDate',
  'finalCheckDate',
  'auditDate',
  'cstartDate',
  'cendDate',
  'mstartDate',
  'mendDate',
];

// 金额字段列表
export const AMOUNT_FIELDS = [
  'amount',
  'ysAmount',
  'yearYsAmount',
  'externalAmount',
  'estProbit',
  'actProbit',
  'evaExternalAmount',
  'evaCostAmount',
  'actExternalAmount',
  'actCostAmount',
  'yfYbAmount',
  'yfEbAmount',
  'yfJfAmount',
  'yfOtherAmount',
  'outYbAmount',
  'outEbAmount',
  'outJfAmount',
  'outOtherAmount',
];

// 环境配置
export const DEBUG_MODE = import.meta.env.DEV;

// 日志工具
export const logger = {
  log: (...args: any[]) => DEBUG_MODE && console.log('[合同管理]', ...args),
  warn: (...args: any[]) => DEBUG_MODE && console.warn('[合同管理]', ...args),
  error: (...args: any[]) => console.error('[合同管理]', ...args),
};
