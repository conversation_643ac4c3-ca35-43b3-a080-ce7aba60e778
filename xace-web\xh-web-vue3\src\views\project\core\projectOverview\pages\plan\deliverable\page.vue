<template>
  <div class="deliverable-page p-4">
    <!-- 页面标题 -->
    <div class="page-header mb-4">
      <h2 class="text-xl font-semibold mb-2">交付物管理</h2>
      <p class="text-gray-600">管理项目的各类交付物，跟踪交付物的状态和质量</p>
    </div>

    <a-spin :spinning="loading">
      <!-- 工具栏 -->
      <div class="toolbar mb-4 flex justify-between items-center">
        <div class="search-area flex items-center space-x-4">
          <a-input-search v-model:value="searchText" placeholder="搜索交付物名称" style="width: 300px" @search="handleSearch" />
          <a-select v-model:value="typeFilter" placeholder="筛选类型" style="width: 150px" allow-clear @change="handleTypeFilter">
            <a-select-option value="document">文档</a-select-option>
            <a-select-option value="software">软件</a-select-option>
            <a-select-option value="report">报告</a-select-option>
            <a-select-option value="plan">计划</a-select-option>
          </a-select>
          <a-select v-model:value="statusFilter" placeholder="筛选状态" style="width: 150px" allow-clear @change="handleStatusFilter">
            <a-select-option value="completed">已完成</a-select-option>
            <a-select-option value="in_progress">进行中</a-select-option>
            <a-select-option value="pending">未开始</a-select-option>
            <a-select-option value="review">评审中</a-select-option>
          </a-select>
        </div>
        <div class="actions">
          <a-space>
            <a-button type="primary" @click="handleAddDeliverable">
              <template #icon><PlusOutlined /></template>
              添加交付物
            </a-button>
            <a-button @click="handleBatchUpload">
              <template #icon><UploadOutlined /></template>
              批量上传
            </a-button>
            <a-button @click="handleExport">
              <template #icon><ExportOutlined /></template>
              导出
            </a-button>
            <a-button @click="handleRefresh">
              <template #icon><ReloadOutlined /></template>
              刷新
            </a-button>
          </a-space>
        </div>
      </div>

      <!-- 交付物统计卡片 -->
      <div class="deliverable-stats grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
        <div class="stat-card bg-blue-50 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-blue-600">{{ deliverableStats.total }}</div>
          <div class="text-sm text-blue-600">总交付物</div>
        </div>
        <div class="stat-card bg-green-50 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-green-600">{{ deliverableStats.completed }}</div>
          <div class="text-sm text-green-600">已完成</div>
        </div>
        <div class="stat-card bg-orange-50 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-orange-600">{{ deliverableStats.inProgress }}</div>
          <div class="text-sm text-orange-600">进行中</div>
        </div>
        <div class="stat-card bg-purple-50 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-purple-600">{{ deliverableStats.review }}</div>
          <div class="text-sm text-purple-600">评审中</div>
        </div>
        <div class="stat-card bg-gray-50 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-gray-600">{{ deliverableStats.pending }}</div>
          <div class="text-sm text-gray-600">未开始</div>
        </div>
      </div>

      <!-- 交付物表格 -->
      <div class="deliverable-table bg-white rounded-lg shadow-sm border">
        <a-table
          :columns="deliverableColumns"
          :data-source="filteredDeliverables"
          :pagination="pagination"
          :scroll="{ x: 1600 }"
          row-key="id"
          :row-selection="{
            selectedRowKeys: selectedRowKeys,
            onChange: onSelectChange,
          }">
          <!-- 交付物名称列 -->
          <template #deliverableName="{ record }">
            <div class="flex items-center">
              <div class="deliverable-icon mr-2">
                <a-avatar :style="{ backgroundColor: getTypeColor(record.type) }" :size="24">
                  <template #icon>
                    <component :is="getTypeIcon(record.type)" />
                  </template>
                </a-avatar>
              </div>
              <div>
                <div class="font-medium">{{ record.deliverableName }}</div>
                <div class="text-sm text-gray-500">{{ record.deliverableCode }}</div>
              </div>
            </div>
          </template>

          <!-- 类型列 -->
          <template #type="{ record }">
            <a-tag :color="getTypeColor(record.type)">
              {{ getTypeText(record.type) }}
            </a-tag>
          </template>

          <!-- 状态列 -->
          <template #status="{ record }">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>

          <!-- 进度列 -->
          <template #progress="{ record }">
            <div class="flex items-center">
              <a-progress
                :percent="record.completionRate"
                :show-info="false"
                size="small"
                class="flex-1 mr-2"
                :stroke-color="getProgressColor(record.completionRate)" />
              <span class="text-sm">{{ record.completionRate }}%</span>
            </div>
          </template>

          <!-- 优先级列 -->
          <template #priority="{ record }">
            <a-tag :color="getPriorityColor(record.priority)">
              {{ getPriorityText(record.priority) }}
            </a-tag>
          </template>

          <!-- 文件列 -->
          <template #files="{ record }">
            <div v-if="record.files && record.files.length > 0">
              <a-space direction="vertical" size="small">
                <div v-for="file in record.files.slice(0, 2)" :key="file.id" class="flex items-center text-sm">
                  <FileOutlined class="mr-1" />
                  <a @click="handlePreviewFile(file)" class="text-blue-600 hover:text-blue-800">
                    {{ file.name }}
                  </a>
                </div>
                <div v-if="record.files.length > 2" class="text-sm text-gray-500"> 还有 {{ record.files.length - 2 }} 个文件... </div>
              </a-space>
            </div>
            <span v-else class="text-gray-400">暂无文件</span>
          </template>

          <!-- 负责人列 -->
          <template #assignee="{ record }">
            <div v-if="record.assignee" class="flex items-center">
              <a-avatar :size="24" class="mr-2">
                {{ record.assignee.charAt(0) }}
              </a-avatar>
              <span class="text-sm">{{ record.assignee }}</span>
            </div>
            <span v-else class="text-gray-400">未分配</span>
          </template>

          <!-- 操作列 -->
          <template #action="{ record }">
            <a-space>
              <a-button type="link" size="small" @click="handleView(record)"> 查看 </a-button>
              <a-button type="link" size="small" @click="handleEdit(record)"> 编辑 </a-button>
              <a-dropdown>
                <a-button type="link" size="small"> 更多 <DownOutlined /> </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="handleUploadFile(record)">
                      <UploadOutlined />
                      上传文件
                    </a-menu-item>
                    <a-menu-item @click="handleDownload(record)">
                      <DownloadOutlined />
                      下载
                    </a-menu-item>
                    <a-menu-item @click="handleSubmitReview(record)" :disabled="record.status === 'completed'">
                      <SendOutlined />
                      提交评审
                    </a-menu-item>
                    <a-menu-item @click="handleComplete(record)" :disabled="record.status === 'completed'">
                      <CheckCircleOutlined />
                      标记完成
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item @click="handleDelete(record)" danger>
                      <DeleteOutlined />
                      删除
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </a-table>
      </div>
    </a-spin>

    <!-- 交付物详情抽屉 -->
    <a-modal v-model:open="drawerVisible" title="交付物详情" width="80%" :footer="null" :mask-closable="false">
      <div v-if="selectedDeliverable" class="deliverable-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="交付物名称">
            {{ selectedDeliverable.deliverableName }}
          </a-descriptions-item>
          <a-descriptions-item label="交付物编码">
            {{ selectedDeliverable.deliverableCode }}
          </a-descriptions-item>
          <a-descriptions-item label="类型">
            <a-tag :color="getTypeColor(selectedDeliverable.type)">
              {{ getTypeText(selectedDeliverable.type) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getStatusColor(selectedDeliverable.status)">
              {{ getStatusText(selectedDeliverable.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="优先级">
            <a-tag :color="getPriorityColor(selectedDeliverable.priority)">
              {{ getPriorityText(selectedDeliverable.priority) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="完成率">
            <div class="flex items-center">
              <a-progress :percent="selectedDeliverable.completionRate" :show-info="false" size="small" class="flex-1 mr-2" />
              <span>{{ selectedDeliverable.completionRate }}%</span>
            </div>
          </a-descriptions-item>
          <a-descriptions-item label="负责人">
            {{ selectedDeliverable.assignee || '未分配' }}
          </a-descriptions-item>
          <a-descriptions-item label="关联阶段">
            {{ selectedDeliverable.stageName || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="计划开始">
            {{ formatDate(selectedDeliverable.planStartDate) }}
          </a-descriptions-item>
          <a-descriptions-item label="计划完成">
            {{ formatDate(selectedDeliverable.planEndDate) }}
          </a-descriptions-item>
          <a-descriptions-item label="实际开始">
            {{ formatDate(selectedDeliverable.actualStartDate) }}
          </a-descriptions-item>
          <a-descriptions-item label="实际完成">
            {{ formatDate(selectedDeliverable.actualEndDate) }}
          </a-descriptions-item>
          <a-descriptions-item label="质量要求" :span="2">
            {{ selectedDeliverable.qualityRequirement || '无特殊要求' }}
          </a-descriptions-item>
          <a-descriptions-item label="交付物描述" :span="2">
            {{ selectedDeliverable.description || '暂无描述' }}
          </a-descriptions-item>
        </a-descriptions>

        <!-- 文件列表 -->
        <div class="mt-6">
          <h4 class="text-lg font-medium mb-4">关联文件</h4>
          <div v-if="selectedDeliverable.files && selectedDeliverable.files.length > 0">
            <a-list item-layout="horizontal" :data-source="selectedDeliverable.files" size="small">
              <template #renderItem="{ item }">
                <a-list-item>
                  <template #actions>
                    <a @click="handlePreviewFile(item)">预览</a>
                    <a @click="handleDownloadFile(item)">下载</a>
                  </template>
                  <a-list-item-meta>
                    <template #title>
                      <div class="flex items-center">
                        <FileOutlined class="mr-2" />
                        {{ item.name }}
                      </div>
                    </template>
                    <template #description>
                      大小: {{ formatFileSize(item.size) }} | 上传时间: {{ formatDate(item.uploadTime) }} | 上传人: {{ item.uploader }}
                    </template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
          </div>
          <a-empty v-else description="暂无文件" />
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import dayjs from 'dayjs';
  import {
    PlusOutlined,
    UploadOutlined,
    ExportOutlined,
    ReloadOutlined,
    DownOutlined,
    FileOutlined,
    DownloadOutlined,
    SendOutlined,
    CheckCircleOutlined,
    DeleteOutlined,
    FileTextOutlined,
    CodeOutlined,
    FilePdfOutlined,
    FileWordOutlined,
  } from '@ant-design/icons-vue';

  const { createMessage } = useMessage();

  // 响应式数据
  const loading = ref(false);
  const searchText = ref('');
  const typeFilter = ref('');
  const statusFilter = ref('');
  const selectedRowKeys = ref([]);
  const drawerVisible = ref(false);
  const selectedDeliverable = ref(null);

  // 交付物统计数据
  const deliverableStats = ref({
    total: 18,
    completed: 5,
    inProgress: 8,
    review: 3,
    pending: 2,
  });

  // 分页配置
  const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number) => `共 ${total} 条记录`,
  });

  // 交付物数据
  const deliverableList = ref([
    {
      id: '1',
      deliverableName: '项目启动文档',
      deliverableCode: 'DEL_001',
      type: 'document',
      status: 'completed',
      priority: 'high',
      completionRate: 100,
      assignee: '张三',
      stageName: '项目开始',
      planStartDate: '2025-03-01',
      planEndDate: '2025-03-07',
      actualStartDate: '2025-03-01',
      actualEndDate: '2025-03-06',
      qualityRequirement: '文档格式规范，内容完整准确',
      description: '项目启动相关的正式文档，包含项目目标、范围等信息',
      files: [
        {
          id: '1',
          name: '项目启动文档v1.0.docx',
          size: 2048576,
          uploadTime: '2025-03-06',
          uploader: '张三',
        },
        {
          id: '2',
          name: '项目启动演示文稿.pptx',
          size: 5242880,
          uploadTime: '2025-03-06',
          uploader: '张三',
        },
      ],
    },
    {
      id: '2',
      deliverableName: '项目章程',
      deliverableCode: 'DEL_002',
      type: 'document',
      status: 'completed',
      priority: 'high',
      completionRate: 100,
      assignee: '李四',
      stageName: '项目开始',
      planStartDate: '2025-03-01',
      planEndDate: '2025-03-07',
      actualStartDate: '2025-03-02',
      actualEndDate: '2025-03-07',
      qualityRequirement: '符合公司项目章程模板要求',
      description: '正式的项目章程文件',
      files: [
        {
          id: '3',
          name: '项目章程v2.0.pdf',
          size: 1048576,
          uploadTime: '2025-03-07',
          uploader: '李四',
        },
      ],
    },
    {
      id: '3',
      deliverableName: '项目需求文档',
      deliverableCode: 'DEL_003',
      type: 'document',
      status: 'in_progress',
      priority: 'high',
      completionRate: 60,
      assignee: '王五',
      stageName: '项目启动',
      planStartDate: '2025-03-08',
      planEndDate: '2025-03-15',
      actualStartDate: '2025-03-08',
      actualEndDate: null,
      qualityRequirement: '需求描述清晰，可测试可验证',
      description: '详细的项目需求规格说明书',
      files: [
        {
          id: '4',
          name: '需求文档草稿v0.8.docx',
          size: 3145728,
          uploadTime: '2025-03-12',
          uploader: '王五',
        },
      ],
    },
    {
      id: '4',
      deliverableName: '资源分配计划',
      deliverableCode: 'DEL_004',
      type: 'plan',
      status: 'review',
      priority: 'medium',
      completionRate: 90,
      assignee: '赵六',
      stageName: '项目启动',
      planStartDate: '2025-03-16',
      planEndDate: '2025-03-22',
      actualStartDate: '2025-03-16',
      actualEndDate: null,
      qualityRequirement: '资源配置合理，时间安排可行',
      description: '人力资源和物力资源的详细分配计划',
      files: [
        {
          id: '5',
          name: '资源分配计划v1.0.xlsx',
          size: 512000,
          uploadTime: '2025-03-20',
          uploader: '赵六',
        },
      ],
    },
    {
      id: '5',
      deliverableName: '审计计划书',
      deliverableCode: 'DEL_005',
      type: 'plan',
      status: 'pending',
      priority: 'high',
      completionRate: 0,
      assignee: '钱七',
      stageName: '审计计划',
      planStartDate: '2025-03-23',
      planEndDate: '2025-03-30',
      actualStartDate: null,
      actualEndDate: null,
      qualityRequirement: '审计计划详细、可执行',
      description: '详细的审计实施计划',
      files: [],
    },
    {
      id: '6',
      deliverableName: '风险评估报告',
      deliverableCode: 'DEL_006',
      type: 'report',
      status: 'pending',
      priority: 'medium',
      completionRate: 0,
      assignee: '孙八',
      stageName: '审计计划',
      planStartDate: '2025-03-31',
      planEndDate: '2025-04-05',
      actualStartDate: null,
      actualEndDate: null,
      qualityRequirement: '风险识别全面，评估准确',
      description: '项目风险识别和评估报告',
      files: [],
    },
  ]);

  // 表格列配置
  const deliverableColumns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 60,
      customRender: ({ index }) => index + 1,
    },
    {
      title: '交付物名称',
      dataIndex: 'deliverableName',
      key: 'deliverableName',
      width: 200,
      slots: { customRender: 'deliverableName' },
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      slots: { customRender: 'type' },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      slots: { customRender: 'status' },
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      width: 80,
      slots: { customRender: 'priority' },
    },
    {
      title: '完成率',
      dataIndex: 'completionRate',
      key: 'completionRate',
      width: 120,
      slots: { customRender: 'progress' },
    },
    {
      title: '负责人',
      dataIndex: 'assignee',
      key: 'assignee',
      width: 100,
      slots: { customRender: 'assignee' },
    },
    {
      title: '关联阶段',
      dataIndex: 'stageName',
      key: 'stageName',
      width: 120,
    },
    {
      title: '计划完成',
      dataIndex: 'planEndDate',
      key: 'planEndDate',
      width: 100,
      customRender: ({ text }) => formatDate(text),
    },
    {
      title: '关联文件',
      dataIndex: 'files',
      key: 'files',
      width: 200,
      slots: { customRender: 'files' },
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      fixed: 'right',
      slots: { customRender: 'action' },
    },
  ];

  // 计算属性
  const filteredDeliverables = computed(() => {
    let result = deliverableList.value;

    // 搜索过滤
    if (searchText.value) {
      result = result.filter(
        deliverable =>
          deliverable.deliverableName.toLowerCase().includes(searchText.value.toLowerCase()) ||
          deliverable.deliverableCode.toLowerCase().includes(searchText.value.toLowerCase()),
      );
    }

    // 类型过滤
    if (typeFilter.value) {
      result = result.filter(deliverable => deliverable.type === typeFilter.value);
    }

    // 状态过滤
    if (statusFilter.value) {
      result = result.filter(deliverable => deliverable.status === statusFilter.value);
    }

    return result;
  });

  onMounted(() => {
    loadDeliverableData();
  });

  // 加载交付物数据
  const loadDeliverableData = async () => {
    loading.value = true;
    try {
      // 这里调用实际的API
      // const result = await getDeliverableList();
      // deliverableList.value = result.data;

      pagination.total = deliverableList.value.length;
    } catch (error) {
      console.error('加载交付物数据失败:', error);
      createMessage.error('加载交付物数据失败');
    } finally {
      loading.value = false;
    }
  };

  // 工具函数
  const getTypeColor = (type: string) => {
    const colorMap = {
      document: '#1890ff',
      software: '#52c41a',
      report: '#faad14',
      plan: '#722ed1',
    };
    return colorMap[type] || '#1890ff';
  };

  const getTypeIcon = (type: string) => {
    const iconMap = {
      document: FileTextOutlined,
      software: CodeOutlined,
      report: FilePdfOutlined,
      plan: FileWordOutlined,
    };
    return iconMap[type] || FileTextOutlined;
  };

  const getTypeText = (type: string) => {
    const textMap = {
      document: '文档',
      software: '软件',
      report: '报告',
      plan: '计划',
    };
    return textMap[type] || '未知';
  };

  const getStatusColor = (status: string) => {
    const colorMap = {
      completed: 'green',
      in_progress: 'blue',
      review: 'orange',
      pending: 'default',
    };
    return colorMap[status] || 'default';
  };

  const getStatusText = (status: string) => {
    const textMap = {
      completed: '已完成',
      in_progress: '进行中',
      review: '评审中',
      pending: '未开始',
    };
    return textMap[status] || '未知';
  };

  const getPriorityColor = (priority: string) => {
    const colorMap = {
      high: 'red',
      medium: 'orange',
      low: 'green',
    };
    return colorMap[priority] || 'default';
  };

  const getPriorityText = (priority: string) => {
    const textMap = {
      high: '高',
      medium: '中',
      low: '低',
    };
    return textMap[priority] || '未知';
  };

  const getProgressColor = (progress: number) => {
    if (progress === 100) return '#52c41a';
    if (progress >= 80) return '#1890ff';
    if (progress >= 60) return '#faad14';
    if (progress >= 40) return '#fa8c16';
    return '#ff4d4f';
  };

  const formatDate = (date: string) => {
    return date ? dayjs(date).format('YYYY-MM-DD') : '-';
  };

  const formatFileSize = (size: number) => {
    if (size < 1024) return size + ' B';
    if (size < 1024 * 1024) return (size / 1024).toFixed(1) + ' KB';
    return (size / (1024 * 1024)).toFixed(1) + ' MB';
  };

  // 事件处理函数
  const handleSearch = () => {
    // 触发搜索
  };

  const handleTypeFilter = () => {
    // 触发类型过滤
  };

  const handleStatusFilter = () => {
    // 触发状态过滤
  };

  const handleAddDeliverable = () => {
    createMessage.info('添加交付物功能开发中...');
  };

  const handleBatchUpload = () => {
    createMessage.info('批量上传功能开发中...');
  };

  const handleExport = () => {
    createMessage.info('导出功能开发中...');
  };

  const handleRefresh = () => {
    loadDeliverableData();
  };

  const handleView = (record: any) => {
    selectedDeliverable.value = record;
    drawerVisible.value = true;
  };

  const handleEdit = (record: any) => {
    createMessage.info('编辑交付物功能开发中...');
  };

  const handleUploadFile = (record: any) => {
    createMessage.info('上传文件功能开发中...');
  };

  const handleDownload = (record: any) => {
    createMessage.info('下载功能开发中...');
  };

  const handleSubmitReview = (record: any) => {
    createMessage.info('提交评审功能开发中...');
  };

  const handleComplete = (record: any) => {
    createMessage.info('标记完成功能开发中...');
  };

  const handleDelete = (record: any) => {
    createMessage.info('删除交付物功能开发中...');
  };

  const handlePreviewFile = (file: any) => {
    createMessage.info('文件预览功能开发中...');
  };

  const handleDownloadFile = (file: any) => {
    createMessage.info('文件下载功能开发中...');
  };

  const onSelectChange = (selectedKeys: string[]) => {
    selectedRowKeys.value = selectedKeys;
  };
</script>

<style scoped>
  .deliverable-page {
    background: var(--section-bg-color);
  }

  .stat-card {
    transition: all 0.3s ease;
  }

  .stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .deliverable-table {
    min-height: 400px;
  }

  .deliverable-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }
</style>
