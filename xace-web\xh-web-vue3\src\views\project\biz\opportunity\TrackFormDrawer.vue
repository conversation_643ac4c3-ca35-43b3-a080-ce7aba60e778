<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" showFooter :title="getTitle" width="60%" @ok="handleSubmit">
    <a-spin :spinning="loading">
      <BasicForm @register="registerForm" />

      <template v-if="showHistory">
        <a-divider>历史跟踪记录</a-divider>
        <a-list :dataSource="historyRecords" :pagination="{ pageSize: 5 }" size="small">
          <template #renderItem="{ item }">
            <a-list-item>
              <a-list-item-meta>
                <template #title>
                  <div class="history-header">
                    <span class="history-week">{{ formatWeek(item) }}</span>
                    <span class="history-time">{{ item.createTime }}</span>
                    <span class="history-user">{{ item.createUserName }}</span>
                  </div>
                </template>
                <template #description>
                  <div class="history-content">{{ item.content }}</div>
                </template>
              </a-list-item-meta>
            </a-list-item>
          </template>
        </a-list>
      </template>
    </a-spin>
  </BasicDrawer>
</template>

<script lang="ts" setup>
  import { computed, ref, unref, onMounted } from 'vue';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { BasicForm, useForm } from '/@/components/Form';
  import { FormSchema } from '/@/components/Form/src/types/form';
  import {
    createBusinessWeeklog,
    updateBusinessWeeklog,
    getBusinessWeeklogListByBusinessId,
    BusinessWeeklogModel,
    BusinessWeeklogFormModel,
  } from '/@/api/project/businessWeeklog';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useUserStore } from '/@/store/modules/user';
  import { formatToDate } from '/@/utils/dateUtil';
  import { Spin, List, ListItem, ListItemMeta, Divider } from 'ant-design-vue';

  const { createMessage } = useMessage();
  const userStore = useUserStore();
  const emit = defineEmits(['register', 'reload', 'success']);

  const isUpdate = ref(false);
  const recordId = ref('');
  const businessId = ref('');
  const businessName = ref('');
  const loading = ref(false);
  const historyRecords = ref<BusinessWeeklogModel[]>([]);
  const showHistory = ref(true);
  const currentUserRoles = computed(() => userStore.getRoleList || []);
  const hasEditPermission = computed(() => {
    // 检查用户是否有编辑权限
    // 这里可以根据实际需求设置权限规则
    return (
      Array.isArray(currentUserRoles.value) &&
      currentUserRoles.value.some(role => role && role.value && ['admin', 'manager', 'business_manager'].includes(role.value))
    );
  });

  // 表单配置
  const formSchemas: FormSchema[] = [
    {
      field: 'businessId',
      label: '商机ID',
      component: 'Input',
      show: false,
    },
    {
      field: 'businessName',
      label: '商机名称',
      component: 'Input',
      dynamicDisabled: true,
      colProps: { span: 24 },
    },
    {
      field: 'weekNo',
      label: '周次',
      component: 'InputNumber',
      defaultValue: getCurrentWeekNumber(),
      componentProps: {
        min: 1,
        max: 53,
        placeholder: '请输入周次',
      },
      colProps: { span: 8 },
    },
    {
      field: 'year',
      label: '年份',
      component: 'InputNumber',
      defaultValue: new Date().getFullYear(),
      componentProps: {
        min: 2000,
        max: 2100,
        placeholder: '请输入年份',
      },
      colProps: { span: 8 },
    },
    {
      field: 'month',
      label: '月份',
      component: 'InputNumber',
      defaultValue: new Date().getMonth() + 1,
      componentProps: {
        min: 1,
        max: 12,
        placeholder: '请输入月份',
      },
      colProps: { span: 8 },
    },
    {
      field: 'content',
      label: '跟踪内容',
      component: 'InputTextArea',
      required: true,
      componentProps: {
        placeholder: '请输入本周跟踪内容',
        rows: 6,
        maxlength: 2000,
        showCount: true,
      },
      rules: [{ required: true, message: '请输入跟踪内容' }],
      colProps: { span: 24 },
    },
    {
      field: 'nextPlan',
      label: '下一步计划',
      component: 'InputTextArea',
      componentProps: {
        placeholder: '请输入下一步工作计划',
        rows: 4,
        maxlength: 1000,
        showCount: true,
      },
      colProps: { span: 24 },
    },
    {
      field: 'remark',
      label: '备注',
      component: 'InputTextArea',
      componentProps: {
        placeholder: '请输入备注信息',
        rows: 2,
        maxlength: 500,
        showCount: true,
      },
      colProps: { span: 24 },
    },
  ];

  // 注册表单
  const [registerForm, { resetFields, setFieldsValue, validate, setProps }] = useForm({
    labelWidth: 100,
    schemas: formSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });

  // 注册抽屉
  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async data => {
    resetFields();
    setDrawerProps({ confirmLoading: false });

    // 设置权限控制
    setProps({
      disabled: !unref(hasEditPermission),
    });

    isUpdate.value = !!data?.isUpdate;
    businessId.value = data?.businessId || '';
    businessName.value = data?.businessName || '';

    if (data?.id) {
      recordId.value = data.id;
    }

    // 设置表单初始值
    setFieldsValue({
      businessId: businessId.value,
      businessName: businessName.value,
    });

    // 加载历史跟踪记录
    if (businessId.value) {
      await loadHistoryRecords();
    }
  });

  // 获取标题
  const getTitle = computed(() => {
    return unref(isUpdate) ? '编辑商机跟踪记录' : '新增商机跟踪记录';
  });

  // 加载历史跟踪记录
  async function loadHistoryRecords() {
    try {
      loading.value = true;
      const data = await getBusinessWeeklogListByBusinessId(businessId.value);
      historyRecords.value = data;
    } catch (error) {
      console.error('获取历史跟踪记录失败:', error);
      createMessage.error('获取历史跟踪记录失败');
    } finally {
      loading.value = false;
    }
  }

  // 提交表单
  async function handleSubmit() {
    if (!unref(hasEditPermission)) {
      createMessage.warning('您没有编辑权限');
      return;
    }

    try {
      const values = await validate();
      setDrawerProps({ confirmLoading: true });

      const params: BusinessWeeklogFormModel = {
        businessId: values.businessId,
        content: values.content,
        weekNo: values.weekNo,
        year: values.year,
        month: values.month,
        day: new Date().getDate(),
      };

      if (values.nextPlan) {
        params.nextPlan = values.nextPlan;
      }

      if (values.remark) {
        params.remark = values.remark;
      }

      if (unref(isUpdate)) {
        await updateBusinessWeeklog(recordId.value, params);
        createMessage.success('更新成功');
      } else {
        await createBusinessWeeklog(params);
        createMessage.success('新增成功');
      }

      closeDrawer();
      emit('success');
      emit('reload');
    } catch (error) {
      console.error('表单验证失败或提交出错:', error);
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }

  // 获取当前周次
  function getCurrentWeekNumber() {
    const now = new Date();
    const start = new Date(now.getFullYear(), 0, 1);
    const diff = now.getTime() - start.getTime();
    const oneDay = 1000 * 60 * 60 * 24;
    const day = Math.floor(diff / oneDay);
    return Math.ceil((day + start.getDay()) / 7);
  }

  // 格式化周次显示
  function formatWeek(item: BusinessWeeklogModel) {
    if (item.year && item.weekNo) {
      return `${item.year}年第${item.weekNo}周`;
    }
    return '';
  }

  onMounted(() => {
    // 检查权限
    if (!unref(hasEditPermission)) {
      createMessage.warning('您只有查看权限，无法编辑');
    }
  });
</script>

<style lang="less" scoped>
  .history-header {
    display: flex;
    align-items: center;

    .history-week {
      font-weight: bold;
      margin-right: 16px;
    }

    .history-time {
      color: #999;
      margin-right: 16px;
    }

    .history-user {
      color: #666;
    }
  }

  .history-content {
    white-space: pre-wrap;
    padding: 8px;
    background-color: #f9f9f9;
    border-radius: 4px;
    margin-top: 8px;
  }
</style>
