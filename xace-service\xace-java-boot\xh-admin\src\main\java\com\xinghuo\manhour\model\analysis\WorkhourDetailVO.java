package com.xinghuo.manhour.model.analysis;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 工时明细数据VO
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@Schema(description = "工时明细数据")
public class WorkhourDetailVO {

    @Schema(description = "工时ID")
    private String id;

    @Schema(description = "用户ID")
    private String userId;

    @Schema(description = "员工姓名")
    private String userName;

    @Schema(description = "分部名称")
    private String fbName;

    @Schema(description = "项目ID")
    private String projectId;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "模块ID")
    private String moduleId;

    @Schema(description = "模块名称")
    private String moduleName;

    @Schema(description = "项目类型")
    private String projType;

    @Schema(description = "项目类型名称")
    private String projTypeName;

    @Schema(description = "工时类型")
    private String workType;

    @Schema(description = "工作月份")
    private String month;

    @Schema(description = "工时人月")
    private BigDecimal workMonth;

    @Schema(description = "工作内容")
    private String workNote;

    @Schema(description = "创建时间")
    private String createTime;

    @Schema(description = "更新时间")
    private String updateTime;

    @Schema(description = "审核状态")
    private Integer status;

    @Schema(description = "审核状态名称")
    private String statusName;
}
