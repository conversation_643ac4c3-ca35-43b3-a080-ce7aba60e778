# Service 层规范

## 📋 核心规范总览

**XACE项目Service层架构原则（按重要性排序）：**
1. **业务逻辑集中**：所有业务规则、数据验证、业务计算在Service层实现
2. **LambdaQueryWrapper优先**：90%的数据查询使用LambdaQueryWrapper，避免自定义SQL
3. **继承体系标准化**：ServiceImpl继承 `BaseServiceImpl<M, E>`，获得标准CRUD能力
4. **事务边界清晰**：使用 `@Transactional` 明确控制事务范围和传播行为
5. **BaseEntityV2充分利用**：自动处理创建/更新时间、逻辑删除、多租户等
6. **用户上下文集成**：使用 `UserProvider` 获取当前用户，自动填充操作人信息

## 基本结构

Service层负责处理业务逻辑，是连接Controller和DAO/Mapper的桥梁，分为接口定义和实现类两部分。

> **重要**：Mapper接口的定义和XHBaseMapper的使用请参考 [数据访问层规范](./04_DATA_ACCESS.md)。本文档专注于Service层的业务逻辑实现和QueryWrapper的使用方法。

### 服务接口

#### 位置与命名
* **包路径:** `com.xinghuo.[模块名].service`
* **命名规范:** 使用名词加Service，如 `UserService`, `ProductService`

#### 接口示例
```java
import com.xinghuo.common.base.service.BaseService;

public interface ProjBizAllocationService extends BaseService<ProjBizAllocationEntity> {
    
    /**
     * 分页查询业务分配列表
     */
    List<ProjBizAllocationEntity> getList(ProjBizAllocationPagination pagination);
    
    /**
     * 根据ID查询业务分配详情
     */
    ProjBizAllocationEntity getInfo(String id);
    
    /**
     * 创建业务分配
     */
    String create(ProjBizAllocationEntity entity);
    
    /**
     * 更新业务分配
     */
    boolean update(String id, ProjBizAllocationEntity entity);
    
    /**
     * 删除业务分配
     */
    void delete(String id);
}
```

### 服务实现类

#### 基本结构
```java
import com.xinghuo.common.base.service.BaseServiceImpl;


@Service
public class ProjBizAllocationServiceImpl extends BaseServiceImpl<ProjBizAllocationMapper, ProjBizAllocationEntity> 
        implements ProjBizAllocationService {

    /**
     * 分页查询列表
     */
    @Override
    public List<ProjBizAllocationEntity> getList(ProjBizAllocationPagination pagination) {
        QueryWrapper<ProjBizAllocationEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .like(StrUtil.isNotBlank(pagination.getKeyword()), 
                  ProjBizAllocationEntity::getFullName, pagination.getKeyword())
            .eq(pagination.getStatus() != null, 
                  ProjBizAllocationEntity::getEnabledMark, pagination.getStatus())
            .orderByDesc(ProjBizAllocationEntity::getCreatedAt);  // BaseEntityV2字段

        // 分页查询
        return this.page(PageXhUtil.getPage(pagination), queryWrapper).getRecords();
    }

    @Override
    public String create(ProjBizAllocationEntity entity) {
        // 业务校验
        if (this.isExistByFullName(entity.getFullName(), null)) {
            throw new DataException("名称已存在");
        }
        
        // 保存数据
        this.save(entity);
        return entity.getId();
    }

    @Override 
    public boolean update(String id, ProjBizAllocationEntity entity) {
        // 业务校验
        if (this.isExistByFullName(entity.getFullName(), id)) {
            throw new DataException("名称已存在");
        }
        
        entity.setId(id);
        return this.updateById(entity);
    }

    @Override
    public void delete(String id) {
        ProjBizAllocationEntity entity = this.getById(id);
        if (entity != null) {
            this.removeById(id);
        }
    }

    /**
     * 检查名称是否重复
     */
    private boolean isExistByFullName(String fullName, String excludeId) {
        QueryWrapper<ProjBizAllocationEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda()
               .eq(ProjBizAllocationEntity::getFullName, fullName)
               .ne(StrUtil.isNotBlank(excludeId), ProjBizAllocationEntity::getId, excludeId);
        return this.count(wrapper) > 0;
    }
}
```

## 🔥 LambdaQueryWrapper 使用指南

### 强制要求：使用Lambda表达式
**必须使用 `queryWrapper.lambda()` 方法，避免字段名硬编码！**

```java
// ❌ 错误写法（字段硬编码）
QueryWrapper<UserEntity> wrapper = new QueryWrapper<>();
wrapper.eq("status", 1).like("name", "张");

// ✅ 正确写法（Lambda表达式）
QueryWrapper<UserEntity> wrapper = new QueryWrapper<>();
wrapper.lambda()
       .eq(UserEntity::getStatus, 1)
       .like(UserEntity::getName, "张");
```

### 基础查询模式

#### 1. 条件查询
```java
public List<UserEntity> searchUsers(String name, Integer status) {
    QueryWrapper<UserEntity> wrapper = new QueryWrapper<>();
    wrapper.lambda()
           .like(StrUtil.isNotBlank(name), UserEntity::getName, name)
           .eq(status != null, UserEntity::getStatus, status)
           .orderByDesc(UserEntity::getCreatedAt);
    return this.list(wrapper);
}
```

#### 2. 分页查询
```java
public IPage<UserEntity> getUserPage(Page<UserEntity> page, String keyword) {
    QueryWrapper<UserEntity> wrapper = new QueryWrapper<>();  
    wrapper.lambda()
           .like(StrUtil.isNotBlank(keyword), UserEntity::getName, keyword)
           .eq(UserEntity::getDeleteMark, 0)
           .orderByDesc(UserEntity::getCreatedAt);
    return this.page(page, wrapper);
}
```

#### 3. 复杂条件
```java
public List<UserEntity> complexSearch(UserQuery query) {
    QueryWrapper<UserEntity> wrapper = new QueryWrapper<>();
    wrapper.lambda()
           .eq(query.getDepartmentId() != null, UserEntity::getDepartmentId, query.getDepartmentId())
           .in(CollUtil.isNotEmpty(query.getRoleIds()), UserEntity::getRoleId, query.getRoleIds())
           .between(query.getStartAge() != null && query.getEndAge() != null, 
                    UserEntity::getAge, query.getStartAge(), query.getEndAge())
           .ge(query.getStartDate() != null, UserEntity::getCreatedAt, query.getStartDate())
           .le(query.getEndDate() != null, UserEntity::getCreatedAt, query.getEndDate());
    return this.list(wrapper);
}
```

#### 4. 批量操作
```java
public boolean batchUpdateStatus(List<String> userIds, Integer status) {
    QueryWrapper<UserEntity> wrapper = new QueryWrapper<>();
    wrapper.lambda().in(UserEntity::getId, userIds);
    
    UserEntity updateEntity = new UserEntity();
    updateEntity.setStatus(status);
    
    return this.update(updateEntity, wrapper);
}
```

### QueryWrapper优势
1. **类型安全**：编译时检查字段名，避免拼写错误
2. **重构友好**：字段名变更时IDE会自动更新
3. **代码提示**：IDE提供完整的代码提示和自动补全
4. **可读性强**：代码更清晰，易于理解和维护

## 事务管理

### 基本事务配置
```java
@Service
@Transactional(rollbackFor = Exception.class)
public class UserServiceImpl extends BaseServiceImpl<UserMapper, UserEntity> implements UserService {
    
    @Transactional(propagation = Propagation.REQUIRED)
    public void create(UserEntity user) {
        // 事务方法
        this.save(user);
    }
    
    @Transactional(readOnly = true)
    public UserEntity getInfo(String id) {
        // 只读事务
        return this.getById(id);
    }
}
```

### 事务传播行为
- **REQUIRED**：默认，如果当前存在事务则加入，否则新建事务
- **REQUIRES_NEW**：总是新建事务，挂起当前事务
- **SUPPORTS**：支持当前事务，如果不存在则以非事务方式执行

## 性能优化

### 1. 分页查询优化
```java
// ✅ 大表分页使用字段选择
QueryWrapper<UserEntity> wrapper = new QueryWrapper<>();
wrapper.lambda()
       .select(UserEntity::getId, UserEntity::getName, UserEntity::getStatus)
       .eq(UserEntity::getStatus, 1)
       .orderByDesc(UserEntity::getCreatedAt);
```

### 2. 批量操作优化  
```java
// ✅ 使用MyBatis-Plus批量方法
public boolean batchSaveUsers(List<UserEntity> users) {
    return this.saveBatch(users, 1000);  // 每批1000条
}
```

## 核心要点总结

1. **ServiceImpl继承BaseServiceImpl**：获得标准CRUD和QueryWrapper支持
2. **强制使用Lambda表达式**：避免字段名硬编码，类型安全
3. **业务逻辑集中处理**：数据验证、业务规则都在Service层实现
4. **合理的事务边界**：使用@Transactional控制事务范围
5. **性能优化意识**：大表查询使用字段选择、分页、批量操作

## 开发检查清单

- [ ] ServiceImpl继承BaseServiceImpl，充分利用BaseEntityV2特性
- [ ] 所有查询使用LambdaQueryWrapper
- [ ] 查询条件使用三元表达式进行null判断
- [ ] 正确使用@Transactional注解
- [ ] 大表查询使用字段选择(.select())
- [ ] 批量操作使用MyBatis-Plus批量方法

遵循以上规范，可以构建出高效、可维护、类型安全的Service层。