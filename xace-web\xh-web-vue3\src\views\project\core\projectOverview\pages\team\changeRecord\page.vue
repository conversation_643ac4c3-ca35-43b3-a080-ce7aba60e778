<template>
  <div class="change-record-page p-4">
    <!-- 页面标题 -->
    <div class="page-header mb-4">
      <h2 class="text-xl font-semibold mb-2">人员变动记录</h2>
      <p class="text-gray-600">查看项目团队成员的变动历史记录和操作日志</p>
    </div>

    <a-spin :spinning="loading">
      <!-- 工具栏 -->
      <div class="toolbar mb-4 flex justify-between items-center">
        <div class="filters flex items-center space-x-4">
          <a-input-search v-model:value="searchText" placeholder="搜索操作日志或人员" style="width: 300px" @search="handleSearch" />
          <a-select v-model:value="operatorFilter" placeholder="筛选操作者" style="width: 150px" allow-clear @change="handleOperatorFilter">
            <a-select-option v-for="operator in operators" :key="operator.value" :value="operator.value">
              {{ operator.label }}
            </a-select-option>
          </a-select>
          <a-select v-model:value="operationTypeFilter" placeholder="筛选操作类型" style="width: 150px" allow-clear @change="handleOperationTypeFilter">
            <a-select-option v-for="type in operationTypes" :key="type.value" :value="type.value">
              {{ type.label }}
            </a-select-option>
          </a-select>
          <a-range-picker v-model:value="dateRange" format="YYYY-MM-DD" placeholder="选择日期范围" @change="handleDateRangeChange" />
        </div>
        <div class="actions">
          <a-space>
            <a-button @click="handleExport">
              <template #icon><ExportOutlined /></template>
              导出记录
            </a-button>
            <a-button @click="handleRefresh">
              <template #icon><ReloadOutlined /></template>
              刷新
            </a-button>
          </a-space>
        </div>
      </div>

      <!-- 变动统计卡片 -->
      <div class="change-stats grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="stat-card bg-blue-50 rounded-lg p-4">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-sm text-blue-600">总记录数</div>
              <div class="text-2xl font-bold text-blue-600">{{ changeStats.totalRecords }}</div>
            </div>
            <FileTextOutlined class="text-3xl text-blue-600" />
          </div>
        </div>
        <div class="stat-card bg-green-50 rounded-lg p-4">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-sm text-green-600">本月新增</div>
              <div class="text-2xl font-bold text-green-600">{{ changeStats.monthlyAdded }}</div>
            </div>
            <UserAddOutlined class="text-3xl text-green-600" />
          </div>
        </div>
        <div class="stat-card bg-orange-50 rounded-lg p-4">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-sm text-orange-600">本月变更</div>
              <div class="text-2xl font-bold text-orange-600">{{ changeStats.monthlyChanged }}</div>
            </div>
            <EditOutlined class="text-3xl text-orange-600" />
          </div>
        </div>
        <div class="stat-card bg-red-50 rounded-lg p-4">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-sm text-red-600">本月移除</div>
              <div class="text-2xl font-bold text-red-600">{{ changeStats.monthlyRemoved }}</div>
            </div>
            <UserDeleteOutlined class="text-3xl text-red-600" />
          </div>
        </div>
      </div>

      <!-- 变动记录表格 -->
      <div class="change-record-table bg-white rounded-lg shadow-sm border">
        <a-table
          :columns="recordColumns"
          :data-source="filteredRecords"
          :pagination="pagination"
          :scroll="{ x: 1200 }"
          row-key="id"
          size="small"
          :row-selection="{
            selectedRowKeys: selectedRowKeys,
            onChange: onSelectChange,
          }">
          <!-- 操作日志列 -->
          <template #operationLog="{ record }">
            <div class="operation-log">
              <div class="log-content">
                <a-tooltip :title="record.operationDescription">
                  <div class="font-medium text-gray-800">{{ record.operationLog }}</div>
                </a-tooltip>
                <div v-if="record.operationDescription" class="text-sm text-gray-500 mt-1">
                  {{ record.operationDescription }}
                </div>
              </div>
            </div>
          </template>

          <!-- 操作者列 -->
          <template #operator="{ record }">
            <div class="flex items-center">
              <a-avatar :size="28" :src="record.operatorAvatar" class="mr-2">
                {{ record.operator?.charAt(0) || '?' }}
              </a-avatar>
              <div>
                <div class="font-medium">{{ record.operator }}</div>
                <div class="text-sm text-gray-500">{{ record.operatorDepartment }}</div>
              </div>
            </div>
          </template>

          <!-- 操作类型列 -->
          <template #operationType="{ record }">
            <a-tag :color="getOperationTypeColor(record.operationType)">
              <template #icon>
                <component :is="getOperationTypeIcon(record.operationType)" />
              </template>
              {{ record.operationType }}
            </a-tag>
          </template>

          <!-- 操作对象列 -->
          <template #operationObject="{ record }">
            <div class="flex items-center">
              <a-avatar :size="28" :src="record.targetUserAvatar" class="mr-2">
                {{ record.operationObject?.charAt(0) || '?' }}
              </a-avatar>
              <div>
                <div class="font-medium">{{ record.operationObject }}</div>
                <div v-if="record.targetUserDepartment" class="text-sm text-gray-500">
                  {{ record.targetUserDepartment }}
                </div>
              </div>
            </div>
          </template>

          <!-- 操作时间列 -->
          <template #operationTime="{ record }">
            <div class="text-sm">
              <div>{{ formatDateTime(record.operationTime) }}</div>
              <div class="text-gray-500">{{ formatTimeAgo(record.operationTime) }}</div>
            </div>
          </template>

          <!-- 审批流程列 -->
          <template #approvalStatus="{ record }">
            <div v-if="record.approvalStatus">
              <a-tag :color="getApprovalStatusColor(record.approvalStatus)">
                <template #icon>
                  <component :is="getApprovalStatusIcon(record.approvalStatus)" />
                </template>
                {{ getApprovalStatusText(record.approvalStatus) }}
              </a-tag>
              <div v-if="record.approvalWorkflow" class="text-sm text-gray-500 mt-1">
                {{ record.approvalWorkflow }}
              </div>
            </div>
            <span v-else class="text-gray-400">无需审批</span>
          </template>

          <!-- 操作列 -->
          <template #action="{ record }">
            <a-space>
              <a-button type="link" size="small" @click="handleViewDetail(record)"> 查看详情 </a-button>
              <a-dropdown v-if="record.canRevert">
                <a-button type="link" size="small"> 更多 <DownOutlined /> </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="handleRevertChange(record)" :disabled="!record.canRevert">
                      <UndoOutlined />
                      撤销变更
                    </a-menu-item>
                    <a-menu-item @click="handleViewApprovalProcess(record)" v-if="record.approvalStatus">
                      <AuditOutlined />
                      查看审批流程
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </a-table>
      </div>
    </a-spin>

    <!-- 详情模态框 -->
    <a-modal v-model:open="detailModalVisible" title="变动记录详情" width="70%" :footer="null" :mask-closable="false">
      <div v-if="selectedRecord" class="record-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="操作时间">
            {{ formatDateTime(selectedRecord.operationTime) }}
          </a-descriptions-item>
          <a-descriptions-item label="操作者">
            <div class="flex items-center">
              <a-avatar :size="32" :src="selectedRecord.operatorAvatar" class="mr-2">
                {{ selectedRecord.operator?.charAt(0) || '?' }}
              </a-avatar>
              <div>
                <div class="font-medium">{{ selectedRecord.operator }}</div>
                <div class="text-sm text-gray-500">{{ selectedRecord.operatorDepartment }}</div>
              </div>
            </div>
          </a-descriptions-item>
          <a-descriptions-item label="操作类型">
            <a-tag :color="getOperationTypeColor(selectedRecord.operationType)">
              <template #icon>
                <component :is="getOperationTypeIcon(selectedRecord.operationType)" />
              </template>
              {{ selectedRecord.operationType }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="操作对象">
            <div class="flex items-center">
              <a-avatar :size="32" :src="selectedRecord.targetUserAvatar" class="mr-2">
                {{ selectedRecord.operationObject?.charAt(0) || '?' }}
              </a-avatar>
              <div>
                <div class="font-medium">{{ selectedRecord.operationObject }}</div>
                <div v-if="selectedRecord.targetUserDepartment" class="text-sm text-gray-500">
                  {{ selectedRecord.targetUserDepartment }}
                </div>
              </div>
            </div>
          </a-descriptions-item>
          <a-descriptions-item label="操作描述" :span="2">
            {{ selectedRecord.operationDescription || selectedRecord.operationLog }}
          </a-descriptions-item>
          <a-descriptions-item label="审批状态" v-if="selectedRecord.approvalStatus">
            <a-tag :color="getApprovalStatusColor(selectedRecord.approvalStatus)">
              <template #icon>
                <component :is="getApprovalStatusIcon(selectedRecord.approvalStatus)" />
              </template>
              {{ getApprovalStatusText(selectedRecord.approvalStatus) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="审批流程" v-if="selectedRecord.approvalWorkflow">
            {{ selectedRecord.approvalWorkflow }}
          </a-descriptions-item>
          <a-descriptions-item label="变更前信息" v-if="selectedRecord.beforeChange" :span="2">
            <a-typography-paragraph>
              <pre class="text-sm">{{ JSON.stringify(selectedRecord.beforeChange, null, 2) }}</pre>
            </a-typography-paragraph>
          </a-descriptions-item>
          <a-descriptions-item label="变更后信息" v-if="selectedRecord.afterChange" :span="2">
            <a-typography-paragraph>
              <pre class="text-sm">{{ JSON.stringify(selectedRecord.afterChange, null, 2) }}</pre>
            </a-typography-paragraph>
          </a-descriptions-item>
          <a-descriptions-item label="备注" :span="2">
            {{ selectedRecord.remark || '无' }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import dayjs from 'dayjs';
  import relativeTime from 'dayjs/plugin/relativeTime';
  import {
    ExportOutlined,
    ReloadOutlined,
    FileTextOutlined,
    UserAddOutlined,
    EditOutlined,
    UserDeleteOutlined,
    DownOutlined,
    UndoOutlined,
    AuditOutlined,
    PlusOutlined,
    MinusOutlined,
    SwapOutlined,
    CheckCircleOutlined,
    ClockCircleOutlined,
    CloseCircleOutlined,
    ExclamationCircleOutlined,
  } from '@ant-design/icons-vue';

  dayjs.extend(relativeTime);

  const { createMessage } = useMessage();

  // 响应式数据
  const loading = ref(false);
  const searchText = ref('');
  const operatorFilter = ref('');
  const operationTypeFilter = ref('');
  const dateRange = ref([]);
  const selectedRowKeys = ref([]);
  const detailModalVisible = ref(false);
  const selectedRecord = ref(null);

  // 变动统计数据
  const changeStats = ref({
    totalRecords: 28,
    monthlyAdded: 15,
    monthlyChanged: 8,
    monthlyRemoved: 2,
  });

  // 操作者选项
  const operators = ref([
    { value: 'DM', label: 'DM' },
    { value: '邓柳斌', label: '邓柳斌' },
    { value: '张强', label: '张强' },
    { value: '系统管理员', label: '系统管理员' },
  ]);

  // 操作类型选项
  const operationTypes = ref([
    { value: '添加人员', label: '添加人员' },
    { value: '移除人员', label: '移除人员' },
    { value: '变更角色', label: '变更角色' },
    { value: '修改信息', label: '修改信息' },
    { value: '权限调整', label: '权限调整' },
  ]);

  // 分页配置
  const pagination = reactive({
    current: 1,
    pageSize: 20,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number) => `共 ${total} 条记录`,
  });

  // 变动记录数据
  const recordList = ref([
    {
      id: '1',
      operationLog: 'DM添加成员PM，角色为项目干系人',
      operator: 'DM',
      operatorAvatar: '',
      operatorDepartment: '易趋集团',
      operationType: '添加人员',
      operationObject: 'PM',
      targetUserAvatar: '',
      targetUserDepartment: '易趋集团',
      operationTime: '2025-03-14 17:25:00',
      approvalStatus: null,
      approvalWorkflow: '',
      canRevert: true,
      operationDescription: '项目干系人PM加入项目团队',
      beforeChange: null,
      afterChange: {
        name: 'PM',
        role: '项目干系人',
        department: '易趋集团',
        joinDate: '2025-03-14',
      },
      remark: '经项目经理批准加入',
    },
    {
      id: '2',
      operationLog: 'DM添加成员DM，角色为项目干系人',
      operator: 'DM',
      operatorAvatar: '',
      operatorDepartment: '易趋集团',
      operationType: '添加人员',
      operationObject: 'DM',
      targetUserAvatar: '',
      targetUserDepartment: '易趋集团',
      operationTime: '2025-03-14 17:25:00',
      approvalStatus: null,
      approvalWorkflow: '',
      canRevert: true,
      operationDescription: '项目干系人DM加入项目团队',
      beforeChange: null,
      afterChange: {
        name: 'DM',
        role: '项目干系人',
        department: '易趋集团',
        joinDate: '2025-03-14',
      },
      remark: '项目启动时加入',
    },
    {
      id: '3',
      operationLog: 'DM添加成员PMO，角色为项目干系人',
      operator: 'DM',
      operatorAvatar: '',
      operatorDepartment: '易趋集团',
      operationType: '添加人员',
      operationObject: 'PMO',
      targetUserAvatar: '',
      targetUserDepartment: '易趋集团',
      operationTime: '2025-03-14 17:25:00',
      approvalStatus: null,
      approvalWorkflow: '',
      canRevert: true,
      operationDescription: '项目干系人PMO加入项目团队',
      beforeChange: null,
      afterChange: {
        name: 'PMO',
        role: '项目干系人',
        department: '易趋集团',
        joinDate: '2025-03-14',
      },
      remark: '项目管理办公室支持',
    },
    {
      id: '4',
      operationLog: 'DM添加成员TM，角色为项目干系人',
      operator: 'DM',
      operatorAvatar: '',
      operatorDepartment: '易趋集团',
      operationType: '添加人员',
      operationObject: 'TM',
      targetUserAvatar: '',
      targetUserDepartment: '易趋集团',
      operationTime: '2025-03-14 17:25:00',
      approvalStatus: null,
      approvalWorkflow: '',
      canRevert: true,
      operationDescription: '项目干系人TM加入项目团队',
      beforeChange: null,
      afterChange: {
        name: 'TM',
        role: '项目干系人',
        department: '易趋集团',
        joinDate: '2025-03-14',
      },
      remark: '技术管理支持',
    },
    {
      id: '5',
      operationLog: '邓柳斌添加成员陈涛龙，角色为项目成员',
      operator: '邓柳斌',
      operatorAvatar: '',
      operatorDepartment: '专业服务事业部',
      operationType: '添加人员',
      operationObject: '陈涛龙',
      targetUserAvatar: '',
      targetUserDepartment: '软件开发中心',
      operationTime: '2025-03-07 00:15:00',
      approvalStatus: 'approved',
      approvalWorkflow: '项目经理审批',
      canRevert: true,
      operationDescription: '项目成员陈涛龙加入项目团队',
      beforeChange: null,
      afterChange: {
        name: '陈涛龙',
        role: '项目成员',
        department: '软件开发中心',
        joinDate: '2025-03-07',
      },
      remark: '负责产品设计工作',
    },
    {
      id: '6',
      operationLog: '邓柳斌添加成员陈涛华，角色为项目成员',
      operator: '邓柳斌',
      operatorAvatar: '',
      operatorDepartment: '专业服务事业部',
      operationType: '添加人员',
      operationObject: '陈涛华',
      targetUserAvatar: '',
      targetUserDepartment: 'DevOps平台组',
      operationTime: '2025-03-07 00:15:00',
      approvalStatus: 'approved',
      approvalWorkflow: '项目经理审批',
      canRevert: true,
      operationDescription: '项目成员陈涛华加入项目团队',
      beforeChange: null,
      afterChange: {
        name: '陈涛华',
        role: '项目成员',
        department: 'DevOps平台组',
        joinDate: '2025-03-07',
      },
      remark: '负责运维支持工作',
    },
    {
      id: '7',
      operationLog: '邓柳斌添加成员梁伟，角色为投资经理',
      operator: '邓柳斌',
      operatorAvatar: '',
      operatorDepartment: '专业服务事业部',
      operationType: '添加人员',
      operationObject: '梁伟',
      targetUserAvatar: '',
      targetUserDepartment: '咨询服务中心',
      operationTime: '2025-03-07 00:15:00',
      approvalStatus: 'approved',
      approvalWorkflow: '项目经理审批',
      canRevert: true,
      operationDescription: '投资经理梁伟加入项目团队',
      beforeChange: null,
      afterChange: {
        name: '梁伟',
        role: '投资经理',
        department: '咨询服务中心',
        joinDate: '2025-03-07',
      },
      remark: '负责投资分析工作',
    },
    {
      id: '8',
      operationLog: '邓柳斌添加成员李伟，角色为投资经理',
      operator: '邓柳斌',
      operatorAvatar: '',
      operatorDepartment: '专业服务事业部',
      operationType: '添加人员',
      operationObject: '李伟',
      targetUserAvatar: '',
      targetUserDepartment: '咨询服务中心',
      operationTime: '2025-03-07 00:15:00',
      approvalStatus: 'approved',
      approvalWorkflow: '项目经理审批',
      canRevert: true,
      operationDescription: '投资经理李伟加入项目团队',
      beforeChange: null,
      afterChange: {
        name: '李伟',
        role: '投资经理',
        department: '咨询服务中心',
        joinDate: '2025-03-07',
      },
      remark: '负责投资管理工作',
    },
    {
      id: '9',
      operationLog: '邓柳斌添加成员张敏，角色为项目IT支持工程师',
      operator: '邓柳斌',
      operatorAvatar: '',
      operatorDepartment: '专业服务事业部',
      operationType: '添加人员',
      operationObject: '张敏',
      targetUserAvatar: '',
      targetUserDepartment: '咨询服务中心',
      operationTime: '2025-03-07 00:15:00',
      approvalStatus: 'approved',
      approvalWorkflow: '项目经理审批',
      canRevert: true,
      operationDescription: '项目IT支持工程师张敏加入项目团队',
      beforeChange: null,
      afterChange: {
        name: '张敏',
        role: '项目IT支持工程师',
        department: '咨询服务中心',
        joinDate: '2025-03-07',
      },
      remark: '负责IT技术支持工作',
    },
    {
      id: '10',
      operationLog: '邓柳斌添加成员吴迪国，角色为项目IT支持工程师',
      operator: '邓柳斌',
      operatorAvatar: '',
      operatorDepartment: '专业服务事业部',
      operationType: '添加人员',
      operationObject: '吴迪国',
      targetUserAvatar: '',
      targetUserDepartment: '咨询服务中心',
      operationTime: '2025-03-07 00:15:00',
      approvalStatus: 'approved',
      approvalWorkflow: '项目经理审批',
      canRevert: true,
      operationDescription: '项目IT支持工程师吴迪国加入项目团队',
      beforeChange: null,
      afterChange: {
        name: '吴迪国',
        role: '项目IT支持工程师',
        department: '咨询服务中心',
        joinDate: '2025-03-07',
      },
      remark: '负责数据分析和IT支持',
    },
    {
      id: '11',
      operationLog: '邓柳斌添加成员陈伟，角色为财务分析师',
      operator: '邓柳斌',
      operatorAvatar: '',
      operatorDepartment: '专业服务事业部',
      operationType: '添加人员',
      operationObject: '陈伟',
      targetUserAvatar: '',
      targetUserDepartment: '技术培训学院',
      operationTime: '2025-03-07 00:15:00',
      approvalStatus: 'approved',
      approvalWorkflow: '项目经理审批',
      canRevert: true,
      operationDescription: '财务分析师陈伟加入项目团队',
      beforeChange: null,
      afterChange: {
        name: '陈伟',
        role: '财务分析师',
        department: '技术培训学院',
        joinDate: '2025-03-07',
      },
      remark: '负责财务分析工作',
    },
    {
      id: '12',
      operationLog: '邓柳斌添加成员孙涛国，角色为财务分析师',
      operator: '邓柳斌',
      operatorAvatar: '',
      operatorDepartment: '专业服务事业部',
      operationType: '添加人员',
      operationObject: '孙涛国',
      targetUserAvatar: '',
      targetUserDepartment: '技术培训学院',
      operationTime: '2025-03-07 00:15:00',
      approvalStatus: 'approved',
      approvalWorkflow: '项目经理审批',
      canRevert: true,
      operationDescription: '财务分析师孙涛国加入项目团队',
      beforeChange: null,
      afterChange: {
        name: '孙涛国',
        role: '财务分析师',
        department: '技术培训学院',
        joinDate: '2025-03-07',
      },
      remark: '负责财务审计工作',
    },
    {
      id: '13',
      operationLog: '邓柳斌添加成员赵伟国，角色为项目助理',
      operator: '邓柳斌',
      operatorAvatar: '',
      operatorDepartment: '专业服务事业部',
      operationType: '添加人员',
      operationObject: '赵伟国',
      targetUserAvatar: '',
      targetUserDepartment: '技术培训学院',
      operationTime: '2025-03-07 00:15:00',
      approvalStatus: 'approved',
      approvalWorkflow: '项目经理审批',
      canRevert: true,
      operationDescription: '项目助理赵伟国加入项目团队',
      beforeChange: null,
      afterChange: {
        name: '赵伟国',
        role: '项目助理',
        department: '技术培训学院',
        joinDate: '2025-03-07',
      },
      remark: '负责项目协调工作',
    },
    {
      id: '14',
      operationLog: '邓柳斌添加成员张强，角色为项目经理',
      operator: '邓柳斌',
      operatorAvatar: '',
      operatorDepartment: '专业服务事业部',
      operationType: '添加人员',
      operationObject: '张强',
      targetUserAvatar: '',
      targetUserDepartment: '专业服务事业部',
      operationTime: '2025-03-07 00:15:00',
      approvalStatus: 'approved',
      approvalWorkflow: '项目经理审批',
      canRevert: false,
      operationDescription: '项目经理张强加入项目团队',
      beforeChange: null,
      afterChange: {
        name: '张强',
        role: '项目经理',
        department: '专业服务事业部',
        joinDate: '2025-03-07',
      },
      remark: '项目主要负责人',
    },
    {
      id: '15',
      operationLog: '邓柳斌添加成员张强，角色为质量控制专员',
      operator: '邓柳斌',
      operatorAvatar: '',
      operatorDepartment: '专业服务事业部',
      operationType: '变更角色',
      operationObject: '张强',
      targetUserAvatar: '',
      targetUserDepartment: '专业服务事业部',
      operationTime: '2025-03-07 00:15:00',
      approvalStatus: 'approved',
      approvalWorkflow: '项目经理审批',
      canRevert: true,
      operationDescription: '张强新增质量控制专员角色',
      beforeChange: {
        name: '张强',
        roles: ['项目经理'],
      },
      afterChange: {
        name: '张强',
        roles: ['项目经理', '质量控制专员'],
      },
      remark: '兼任质量控制职责',
    },
    {
      id: '16',
      operationLog: '邓柳斌添加成员陈涛中，角色为质量控制专员',
      operator: '邓柳斌',
      operatorAvatar: '',
      operatorDepartment: '专业服务事业部',
      operationType: '添加人员',
      operationObject: '陈涛中',
      targetUserAvatar: '',
      targetUserDepartment: '专业服务事业部',
      operationTime: '2025-03-07 00:15:00',
      approvalStatus: 'approved',
      approvalWorkflow: '项目经理审批',
      canRevert: true,
      operationDescription: '质量控制专员陈涛中加入项目团队',
      beforeChange: null,
      afterChange: {
        name: '陈涛中',
        role: '质量控制专员',
        department: '专业服务事业部',
        joinDate: '2025-03-07',
      },
      remark: '负责质量控制工作',
    },
    {
      id: '17',
      operationLog: '邓柳斌添加成员赵敏，角色为审计经理',
      operator: '邓柳斌',
      operatorAvatar: '',
      operatorDepartment: '专业服务事业部',
      operationType: '添加人员',
      operationObject: '赵敏',
      targetUserAvatar: '',
      targetUserDepartment: '专业服务事业部',
      operationTime: '2025-03-07 00:15:00',
      approvalStatus: 'approved',
      approvalWorkflow: '项目经理审批',
      canRevert: true,
      operationDescription: '审计经理赵敏加入项目团队',
      beforeChange: null,
      afterChange: {
        name: '赵敏',
        role: '审计经理',
        department: '专业服务事业部',
        joinDate: '2025-03-07',
      },
      remark: '负责审计管理工作',
    },
    {
      id: '18',
      operationLog: '邓柳斌添加成员赵伟，角色为审计经理',
      operator: '邓柳斌',
      operatorAvatar: '',
      operatorDepartment: '专业服务事业部',
      operationType: '添加人员',
      operationObject: '赵伟',
      targetUserAvatar: '',
      targetUserDepartment: '专业服务事业部',
      operationTime: '2025-03-07 00:15:00',
      approvalStatus: 'approved',
      approvalWorkflow: '项目经理审批',
      canRevert: true,
      operationDescription: '审计经理赵伟加入项目团队',
      beforeChange: null,
      afterChange: {
        name: '赵伟',
        role: '审计经理',
        department: '专业服务事业部',
        joinDate: '2025-03-07',
      },
      remark: '负责审计项目管理',
    },
    {
      id: '19',
      operationLog: '邓柳斌添加成员吴迪国，角色为审计员',
      operator: '邓柳斌',
      operatorAvatar: '',
      operatorDepartment: '专业服务事业部',
      operationType: '变更角色',
      operationObject: '吴迪国',
      targetUserAvatar: '',
      targetUserDepartment: '咨询服务中心',
      operationTime: '2025-03-07 00:15:00',
      approvalStatus: 'approved',
      approvalWorkflow: '项目经理审批',
      canRevert: true,
      operationDescription: '吴迪国新增审计员角色',
      beforeChange: {
        name: '吴迪国',
        roles: ['项目IT支持工程师'],
      },
      afterChange: {
        name: '吴迪国',
        roles: ['项目IT支持工程师', '审计员'],
      },
      remark: '兼任审计员职责',
    },
    {
      id: '20',
      operationLog: '邓柳斌添加成员孙涛国，角色为审计员',
      operator: '邓柳斌',
      operatorAvatar: '',
      operatorDepartment: '专业服务事业部',
      operationType: '变更角色',
      operationObject: '孙涛国',
      targetUserAvatar: '',
      targetUserDepartment: '技术培训学院',
      operationTime: '2025-03-07 00:15:00',
      approvalStatus: 'approved',
      approvalWorkflow: '项目经理审批',
      canRevert: true,
      operationDescription: '孙涛国新增审计员角色',
      beforeChange: {
        name: '孙涛国',
        roles: ['财务分析师'],
      },
      afterChange: {
        name: '孙涛国',
        roles: ['财务分析师', '审计员'],
      },
      remark: '兼任审计员职责',
    },
    {
      id: '21',
      operationLog: '邓柳斌添加成员陈涛国，角色为法律顾问',
      operator: '邓柳斌',
      operatorAvatar: '',
      operatorDepartment: '专业服务事业部',
      operationType: '添加人员',
      operationObject: '陈涛国',
      targetUserAvatar: '',
      targetUserDepartment: '投资分析组',
      operationTime: '2025-03-07 00:15:00',
      approvalStatus: 'approved',
      approvalWorkflow: '项目经理审批',
      canRevert: true,
      operationDescription: '法律顾问陈涛国加入项目团队',
      beforeChange: null,
      afterChange: {
        name: '陈涛国',
        role: '法律顾问',
        department: '投资分析组',
        joinDate: '2025-03-07',
      },
      remark: '负责法律事务咨询',
    },
    {
      id: '22',
      operationLog: '邓柳斌添加成员陈涛，角色为法律顾问',
      operator: '邓柳斌',
      operatorAvatar: '',
      operatorDepartment: '专业服务事业部',
      operationType: '添加人员',
      operationObject: '陈涛',
      targetUserAvatar: '',
      targetUserDepartment: '知识产权管理部',
      operationTime: '2025-03-07 00:15:00',
      approvalStatus: 'approved',
      approvalWorkflow: '项目经理审批',
      canRevert: true,
      operationDescription: '法律顾问陈涛加入项目团队',
      beforeChange: null,
      afterChange: {
        name: '陈涛',
        role: '法律顾问',
        department: '知识产权管理部',
        joinDate: '2025-03-07',
      },
      remark: '负责知识产权法律事务',
    },
    {
      id: '23',
      operationLog: '邓柳斌添加成员王涛，角色为项目助理',
      operator: '邓柳斌',
      operatorAvatar: '',
      operatorDepartment: '专业服务事业部',
      operationType: '添加人员',
      operationObject: '王涛',
      targetUserAvatar: '',
      targetUserDepartment: '技术培训学院',
      operationTime: '2025-03-07 00:15:00',
      approvalStatus: 'approved',
      approvalWorkflow: '项目经理审批',
      canRevert: true,
      operationDescription: '项目助理王涛加入项目团队',
      beforeChange: null,
      afterChange: {
        name: '王涛',
        role: '项目助理',
        department: '技术培训学院',
        joinDate: '2025-03-07',
      },
      remark: '负责项目协调和会议组织',
    },
    {
      id: '24',
      operationLog: '邓柳斌添加成员陈涛中，角色为项目经理',
      operator: '邓柳斌',
      operatorAvatar: '',
      operatorDepartment: '专业服务事业部',
      operationType: '变更角色',
      operationObject: '陈涛中',
      targetUserAvatar: '',
      targetUserDepartment: '专业服务事业部',
      operationTime: '2025-03-07 00:15:00',
      approvalStatus: 'approved',
      approvalWorkflow: '项目经理审批',
      canRevert: true,
      operationDescription: '陈涛中新增项目经理角色',
      beforeChange: {
        name: '陈涛中',
        roles: ['质量控制专员'],
      },
      afterChange: {
        name: '陈涛中',
        roles: ['质量控制专员', '项目经理'],
      },
      remark: '兼任项目经理职责',
    },
    {
      id: '25',
      operationLog: '邓柳斌添加成员Demo演示用户，角色为项目经理',
      operator: '邓柳斌',
      operatorAvatar: '',
      operatorDepartment: '专业服务事业部',
      operationType: '添加人员',
      operationObject: 'Demo演示用户',
      targetUserAvatar: '',
      targetUserDepartment: '易趋集团',
      operationTime: '2025-03-07 00:15:00',
      approvalStatus: 'approved',
      approvalWorkflow: '项目经理审批',
      canRevert: true,
      operationDescription: '项目经理Demo演示用户加入项目团队',
      beforeChange: null,
      afterChange: {
        name: 'Demo演示用户',
        role: '项目经理',
        department: '易趋集团',
        joinDate: '2025-03-07',
      },
      remark: '演示用户',
    },
    {
      id: '26',
      operationLog: '邓柳斌添加成员Demo演示用户，角色为项目发起人',
      operator: '邓柳斌',
      operatorAvatar: '',
      operatorDepartment: '专业服务事业部',
      operationType: '变更角色',
      operationObject: 'Demo演示用户',
      targetUserAvatar: '',
      targetUserDepartment: '易趋集团',
      operationTime: '2025-03-07 00:15:00',
      approvalStatus: 'approved',
      approvalWorkflow: '项目经理审批',
      canRevert: true,
      operationDescription: 'Demo演示用户新增项目发起人角色',
      beforeChange: {
        name: 'Demo演示用户',
        roles: ['项目经理'],
      },
      afterChange: {
        name: 'Demo演示用户',
        roles: ['项目经理', '项目发起人'],
      },
      remark: '兼任项目发起人',
    },
  ]);

  // 表格列配置
  const recordColumns = [
    {
      title: '操作日志',
      dataIndex: 'operationLog',
      key: 'operationLog',
      width: 300,
      slots: { customRender: 'operationLog' },
    },
    {
      title: '操作者',
      dataIndex: 'operator',
      key: 'operator',
      width: 120,
      slots: { customRender: 'operator' },
    },
    {
      title: '操作类型',
      dataIndex: 'operationType',
      key: 'operationType',
      width: 100,
      slots: { customRender: 'operationType' },
    },
    {
      title: '操作对象',
      dataIndex: 'operationObject',
      key: 'operationObject',
      width: 120,
      slots: { customRender: 'operationObject' },
    },
    {
      title: '操作时间',
      dataIndex: 'operationTime',
      key: 'operationTime',
      width: 160,
      slots: { customRender: 'operationTime' },
      sorter: (a, b) => dayjs(a.operationTime).unix() - dayjs(b.operationTime).unix(),
    },
    {
      title: '审批流程',
      dataIndex: 'approvalStatus',
      key: 'approvalStatus',
      width: 120,
      slots: { customRender: 'approvalStatus' },
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right',
      slots: { customRender: 'action' },
    },
  ];

  // 计算属性
  const filteredRecords = computed(() => {
    let result = recordList.value;

    // 搜索过滤
    if (searchText.value) {
      const searchLower = searchText.value.toLowerCase();
      result = result.filter(
        record =>
          record.operationLog.toLowerCase().includes(searchLower) ||
          record.operator.toLowerCase().includes(searchLower) ||
          record.operationObject.toLowerCase().includes(searchLower),
      );
    }

    // 操作者过滤
    if (operatorFilter.value) {
      result = result.filter(record => record.operator === operatorFilter.value);
    }

    // 操作类型过滤
    if (operationTypeFilter.value) {
      result = result.filter(record => record.operationType === operationTypeFilter.value);
    }

    // 日期范围过滤
    if (dateRange.value && dateRange.value.length === 2) {
      const [startDate, endDate] = dateRange.value;
      result = result.filter(record => {
        const recordDate = dayjs(record.operationTime);
        return recordDate.isAfter(startDate) && recordDate.isBefore(endDate.add(1, 'day'));
      });
    }

    return result.sort((a, b) => dayjs(b.operationTime).unix() - dayjs(a.operationTime).unix());
  });

  onMounted(() => {
    loadRecordData();
  });

  // 加载记录数据
  const loadRecordData = async () => {
    loading.value = true;
    try {
      // 这里调用实际的API
      // const result = await getChangeRecords();
      // recordList.value = result.data;

      pagination.total = filteredRecords.value.length;
    } catch (error) {
      console.error('加载变动记录失败:', error);
      createMessage.error('加载变动记录失败');
    } finally {
      loading.value = false;
    }
  };

  // 工具函数
  const getOperationTypeColor = (operationType: string) => {
    const colorMap = {
      添加人员: 'green',
      移除人员: 'red',
      变更角色: 'orange',
      修改信息: 'blue',
      权限调整: 'purple',
    };
    return colorMap[operationType] || 'default';
  };

  const getOperationTypeIcon = (operationType: string) => {
    const iconMap = {
      添加人员: PlusOutlined,
      移除人员: MinusOutlined,
      变更角色: SwapOutlined,
      修改信息: EditOutlined,
      权限调整: UserAddOutlined,
    };
    return iconMap[operationType] || EditOutlined;
  };

  const getApprovalStatusColor = (status: string) => {
    const colorMap = {
      pending: 'orange',
      approved: 'green',
      rejected: 'red',
      cancelled: 'default',
    };
    return colorMap[status] || 'default';
  };

  const getApprovalStatusIcon = (status: string) => {
    const iconMap = {
      pending: ClockCircleOutlined,
      approved: CheckCircleOutlined,
      rejected: CloseCircleOutlined,
      cancelled: ExclamationCircleOutlined,
    };
    return iconMap[status] || ClockCircleOutlined;
  };

  const getApprovalStatusText = (status: string) => {
    const textMap = {
      pending: '待审批',
      approved: '已通过',
      rejected: '已拒绝',
      cancelled: '已取消',
    };
    return textMap[status] || '未知';
  };

  const formatDateTime = (dateTime: string) => {
    return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss');
  };

  const formatTimeAgo = (dateTime: string) => {
    return dayjs(dateTime).fromNow();
  };

  // 事件处理函数
  const handleSearch = () => {
    // 触发搜索
  };

  const handleOperatorFilter = () => {
    // 触发操作者过滤
  };

  const handleOperationTypeFilter = () => {
    // 触发操作类型过滤
  };

  const handleDateRangeChange = () => {
    // 触发日期范围过滤
  };

  const handleExport = () => {
    createMessage.info('导出功能开发中...');
  };

  const handleRefresh = () => {
    loadRecordData();
  };

  const handleViewDetail = (record: any) => {
    selectedRecord.value = record;
    detailModalVisible.value = true;
  };

  const handleRevertChange = (record: any) => {
    createMessage.info('撤销变更功能开发中...');
  };

  const handleViewApprovalProcess = (record: any) => {
    createMessage.info('查看审批流程功能开发中...');
  };

  const onSelectChange = (selectedKeys: string[]) => {
    selectedRowKeys.value = selectedKeys;
  };
</script>

<style scoped>
  .change-record-page {
    background: var(--section-bg-color);
  }

  .stat-card {
    transition: all 0.3s ease;
  }

  .stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .change-record-table {
    min-height: 400px;
  }

  .operation-log {
    line-height: 1.4;
  }

  .log-content {
    max-width: 280px;
  }

  .record-detail {
    max-height: 70vh;
    overflow-y: auto;
  }

  /* 表格样式 */
  :deep(.ant-table-tbody > tr > td) {
    vertical-align: top;
    padding: 12px 8px;
  }

  :deep(.ant-table-thead > tr > th) {
    background-color: #fafafa;
    font-weight: 600;
  }

  :deep(.ant-table-tbody > tr:hover > td) {
    background-color: var(--section-bg-color);
  }

  :deep(.ant-typography-paragraph) {
    margin: 0;
  }

  :deep(.ant-typography-paragraph pre) {
    background: var(--section-bg-color);
    padding: 8px;
    border-radius: 4px;
    max-height: 200px;
    overflow-y: auto;
  }
</style>
