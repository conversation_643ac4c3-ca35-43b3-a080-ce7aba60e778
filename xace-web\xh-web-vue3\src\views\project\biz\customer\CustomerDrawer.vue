<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" :title="getTitle" width="500px" @ok="handleSubmit" showFooter>
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>

<script lang="ts" setup>
  import { computed, ref, unref, onMounted } from 'vue';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { BasicForm, useForm } from '/@/components/Form';
  import { FormSchema } from '/@/components/Form/src/types/form';
  import { createCustomer, updateCustomer, ProjCustomerModel } from '/@/api/project/customer';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useBaseStore } from '/@/store/modules/base';
  import { getDictionaryDataSelector } from '/@/api/systemData/dictionary';
  import { UserSelect } from '/@/components/Xh/Organize';

  const emit = defineEmits(['register', 'success']);
  const { createMessage } = useMessage();
  const baseStore = useBaseStore();
  const isUpdate = ref(false);
  const customerId = ref('');

  // 单位类型选项
  const custLineOptions = ref([]);
  // 客户类型选项
  const custTypeOptions = ref([]);

  // 表单配置
  const formSchema: FormSchema[] = [
    {
      field: 'name',
      label: '单位名称',
      component: 'Input',
      required: true,
      componentProps: {
        placeholder: '请输入单位名称',
        maxlength: 100,
      },
      rules: [{ required: true, message: '请输入单位名称' }],
    },
    {
      field: 'custType',
      label: '单位类型',
      component: 'Select',
      required: true,
      componentProps: {
        placeholder: '请选择单位类型',
        options: custTypeOptions,
      },
      rules: [{ required: true, message: '请选择单位类型' }],
    },
    {
      field: 'custLine',
      label: '业务线',
      component: 'Select',
      componentProps: {
        placeholder: '请选择业务线',
        options: custLineOptions,
      },
    },
    {
      field: 'leader',
      label: '市场负责人',
      component: 'UserSelect',
      componentProps: {
        placeholder: '请选择市场负责人',
      },
    },
    {
      field: 'remark',
      label: '备注',
      component: 'InputTextArea' as const,
      componentProps: {
        placeholder: '请输入备注',
        rows: 4,
        maxlength: 500,
        showCount: true,
      },
    },
  ];

  // 注册表单
  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 100,
    schemas: formSchema,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });

  // 加载数据字典
  async function loadDictionaries() {
    try {
      // 加载单位类型字典 (dwlx)
      const custLineRes = await getDictionaryDataSelector('custLine');
      if (custLineRes && custLineRes.data && custLineRes.data.list) {
        custLineOptions.value = custLineRes.data.list.map(item => ({
          fullName: item.fullName,
          id: item.enCode,
        }));
      }

      // 加载客户类型字典 (custType)
      const custTypeRes = await getDictionaryDataSelector('dwlx');
      if (custTypeRes && custTypeRes.data && custTypeRes.data.list) {
        custTypeOptions.value = custTypeRes.data.list.map(item => ({
          fullName: item.fullName,
          id: item.enCode,
        }));
      }
    } catch (error) {
      console.error('加载数据字典失败:', error);
    }
  }

  // 初始化加载数据字典
  onMounted(() => {
    loadDictionaries();
  });

  // 注册抽屉
  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async data => {
    resetFields();
    setDrawerProps({ confirmLoading: false });

    // 确保数据字典已加载
    if (custLineOptions.value.length === 0 || custTypeOptions.value.length === 0) {
      await loadDictionaries();
    }

    isUpdate.value = !!data?.isUpdate;
    if (unref(isUpdate)) {
      customerId.value = data.record.id;
      setFieldsValue({
        ...data.record,
      });
    }
  });

  // 获取标题
  const getTitle = computed(() => {
    return unref(isUpdate) ? '编辑客户单位' : '新增客户单位';
  });

  // 提交表单
  async function handleSubmit() {
    try {
      const values = await validate();
      setDrawerProps({ confirmLoading: true });

      if (unref(isUpdate)) {
        await updateCustomer(customerId.value, values);
        createMessage.success('更新成功');
      } else {
        await createCustomer(values);
        createMessage.success('新增成功');
      }

      closeDrawer();
      emit('success');
    } catch (error) {
      console.error('表单验证失败或提交出错:', error);
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
