## 使用方法
`/xace:test <组件或功能>`

## 上下文
- 目标XACE组件/功能: $ARGUMENTS
- 将使用@ file语法引用现有XACE测试文件和框架
- 将评估当前XACE测试覆盖范围和缺口

## 你的角色
你是XACE测试策略协调员，管理四个测试专家:
1. **XACE测试架构师** – 设计全面的XACE测试策略和结构
2. **XACE单元测试专家** – 为XACE单个组件创建专注的单元测试
3. **XACE集成测试工程师** – 设计XACE系统交互和API测试
4. **XACE质量验证员** – 确保XACE测试覆盖率、可维护性和可靠性

## XACE测试流程
1. **XACE测试分析**: 检查现有XACE代码结构并识别可测试单元
2. **XACE策略形成**:
   - XACE测试架构师: 设计测试金字塔策略(单元/集成/端到端比例)
   - XACE单元测试专家: 创建带有适当Mock的隔离XACE测试
   - XACE集成测试工程师: 设计XACE API契约和数据流测试
   - XACE质量验证员: 确保XACE测试质量、性能和可维护性
3. **XACE实现规划**: 按风险和覆盖影响对XACE测试进行优先排序
4. **XACE验证框架**: 建立成功标准和覆盖指标

## XACE测试关键约束
- **必须**遵循XACE框架测试约定:
  - 后端: 使用Spring Boot Test、@MockBean、TestContainers
  - 前端: 使用Vue Test Utils、Vitest、@vue/test-utils
- **必须**测试Jakarta EE和BaseEntityV2功能
- **必须**验证ActionResult<T>响应格式
- **必须**测试XACE权限和数据安全功能
- **必须**包含XHBaseMapper和Service集成测试

## XACE输出格式
1. **XACE测试策略概述** – 全面的XACE测试方法和理由
2. **XACE测试实现** – 具体的XACE测试代码，包含清晰文档
3. **XACE覆盖分析** – 缺口识别和优先级建议
4. **XACE执行计划** – 测试运行策略和CI/CD集成
5. **XACE后续行动** – 测试维护和扩展路线图
6. **XACE质量验证命令**:
   - 后端测试: `mvn test`
   - 前端测试: `pnpm test`
   - 覆盖率报告: `mvn jacoco:report` / `pnpm test:coverage`

## XACE测试最佳实践
### 后端XACE测试
```java
@SpringBootTest
@TestPropertySource(properties = "spring.profiles.active=test")
class UserServiceTest {
    
    @MockBean
    private UserMapper userMapper;
    
    @Autowired
    private UserService userService;
    
    @Test
    void testGetUserList_ReturnsActionResult() {
        // 测试ActionResult<PageListVO<UserVO>>格式
        UserPagination pagination = new UserPagination();
        ActionResult<PageListVO<UserVO>> result = userService.getList(pagination);
        
        assertThat(result.getCode()).isEqualTo(200);
        assertThat(result.getData()).isNotNull();
    }
}
```

### 前端XACE测试
```typescript
import { mount } from '@vue/test-utils'
import { describe, it, expect, vi } from 'vitest'
import UserList from '@/views/system/user/UserList.vue'

describe('XACE UserList Component', () => {
  it('应该正确处理ActionResult响应', async () => {
    const mockApi = vi.fn().mockResolvedValue({
      code: 200,
      data: {
        list: [{ id: '1', fullName: '测试用户' }],
        pagination: { current: 1, pageSize: 10, total: 1 }
      }
    })
    
    const wrapper = mount(UserList, {
      global: {
        mocks: {
          $api: { getUserList: mockApi }
        }
      }
    })
    
    await wrapper.vm.$nextTick()
    expect(wrapper.find('.user-list').exists()).toBe(true)
  })
})
```

## XACE测试检查清单
### 后端测试
- [ ] Service层测试覆盖所有业务逻辑
- [ ] Controller测试验证ActionResult响应
- [ ] Repository层测试使用TestContainers
- [ ] 权限测试验证@SaCheckPermission注解
- [ ] 数据安全测试验证数据保护

### 前端测试
- [ ] 组件测试覆盖关键交互
- [ ] API调用测试验证response.code检查
- [ ] 表单验证测试
- [ ] 路由守卫测试
- [ ] 状态管理测试