import { defHttp } from '/@/utils/http/axios';
import { ListResult } from '/@/api/model/baseModel';

/**
 * 标签管理API
 */

// API URL前缀
const API_PREFIX = '/api/project/core/tag';

/**
 * 标签对象接口
 */
export interface TagModel {
  id: string;
  tagName: string;
  tagColor: string;
  description: string;
  scope: string;
  createTime: string;
  createBy: string;
  updateTime: string;
  updateBy: string;
}

/**
 * 标签创建表单接口
 */
export interface TagCreateModel {
  tagName: string;
  tagColor: string;
  description: string;
  scope: string;
}

/**
 * 标签更新表单接口
 */
export interface TagUpdateModel {
  tagName: string;
  tagColor: string;
  description: string;
  scope: string;
}

/**
 * 标签查询参数接口
 */
export interface TagQueryParams {
  tagName?: string;
  scope?: string;
  keyword?: string;
  pageSize?: number;
  currentPage?: number;
}

/**
 * 获取标签列表
 * @param params 查询参数
 * @returns 标签列表
 */
export const getTagList = (params?: TagQueryParams) => {
  return defHttp.post<ListResult<TagModel>>({
    url: API_PREFIX + '/getList',
    params,
  });
};

/**
 * 获取标签详情
 * @param id 标签ID
 * @returns 标签详情
 */
export const getTagInfo = (id: string) => {
  return defHttp.get<TagModel>({
    url: `${API_PREFIX}/${id}`,
  });
};

/**
 * 创建标签
 * @param params 标签创建参数
 * @returns 操作结果
 */
export const createTag = (params: TagCreateModel) => {
  return defHttp.post<void>({
    url: API_PREFIX,
    data: params,
  });
};

/**
 * 更新标签
 * @param id 标签ID
 * @param params 标签更新参数
 * @returns 操作结果
 */
export const updateTag = (id: string, params: TagUpdateModel) => {
  return defHttp.put<void>({
    url: `${API_PREFIX}/${id}`,
    data: params,
  });
};

/**
 * 删除标签
 * @param id 标签ID
 * @returns 操作结果
 */
export const deleteTag = (id: string) => {
  return defHttp.delete<void>({
    url: `${API_PREFIX}/${id}`,
  });
};
