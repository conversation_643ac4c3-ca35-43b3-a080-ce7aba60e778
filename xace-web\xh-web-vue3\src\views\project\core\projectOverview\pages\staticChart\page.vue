<template>
  <div class="project-static-chart p-4">
    <!-- 页面标题 -->
    <div class="page-header mb-4">
      <h2 class="text-xl font-semibold mb-2">项目静态图</h2>
      <p class="text-gray-600">展示项目的整体结构、组织架构和关键信息的静态可视化图表</p>
    </div>

    <!-- 工具栏 -->
    <div class="toolbar mb-4 flex justify-between items-center">
      <div class="left-tools flex items-center space-x-2">
        <a-select v-model:value="chartType" style="width: 150px" @change="onChartTypeChange">
          <a-select-option value="organization">组织架构图</a-select-option>
          <a-select-option value="workflow">工作流程图</a-select-option>
          <a-select-option value="milestone">里程碑图</a-select-option>
          <a-select-option value="resource">资源分布图</a-select-option>
        </a-select>

        <a-button type="primary" @click="generateChart" :loading="loading">
          <template #icon><PictureOutlined /></template>
          生成图表
        </a-button>
      </div>

      <div class="right-tools flex items-center space-x-2">
        <a-button @click="exportChart" :disabled="!chartGenerated">
          <template #icon><DownloadOutlined /></template>
          导出图片
        </a-button>

        <a-button @click="printChart" :disabled="!chartGenerated">
          <template #icon><PrinterOutlined /></template>
          打印
        </a-button>

        <a-button @click="refreshData">
          <template #icon><ReloadOutlined /></template>
          刷新数据
        </a-button>
      </div>
    </div>

    <!-- 图表容器 -->
    <div class="chart-container">
      <a-spin :spinning="loading" tip="正在生成图表...">
        <div class="chart-wrapper bg-white rounded-lg shadow-sm border">
          <!-- 图表标题 -->
          <div class="chart-title p-4 border-b">
            <h3 class="text-lg font-medium">{{ chartTitle }}</h3>
            <p class="text-sm text-gray-500 mt-1">{{ chartDescription }}</p>
          </div>

          <!-- 图表内容区域 -->
          <div class="chart-content p-4" style="min-height: 500px">
            <div v-if="!chartGenerated" class="empty-state text-center py-20">
              <div class="text-gray-400 mb-4">
                <PictureOutlined style="font-size: 48px" />
              </div>
              <p class="text-gray-500">请选择图表类型并点击"生成图表"按钮</p>
            </div>

            <!-- 组织架构图 -->
            <div v-else-if="chartType === 'organization'" class="organization-chart">
              <div class="org-node-container">
                <div class="org-level-1 text-center mb-8">
                  <div class="org-node primary">
                    <div class="node-title">项目经理</div>
                    <div class="node-subtitle">张三</div>
                  </div>
                </div>

                <div class="org-level-2 flex justify-center space-x-8 mb-6">
                  <div class="org-node secondary">
                    <div class="node-title">技术负责人</div>
                    <div class="node-subtitle">李四</div>
                  </div>
                  <div class="org-node secondary">
                    <div class="node-title">产品负责人</div>
                    <div class="node-subtitle">王五</div>
                  </div>
                  <div class="org-node secondary">
                    <div class="node-title">测试负责人</div>
                    <div class="node-subtitle">赵六</div>
                  </div>
                </div>

                <div class="org-level-3 flex justify-center space-x-4">
                  <div class="org-node tertiary">
                    <div class="node-title">前端开发</div>
                    <div class="node-subtitle">3人</div>
                  </div>
                  <div class="org-node tertiary">
                    <div class="node-title">后端开发</div>
                    <div class="node-subtitle">4人</div>
                  </div>
                  <div class="org-node tertiary">
                    <div class="node-title">UI设计</div>
                    <div class="node-subtitle">2人</div>
                  </div>
                  <div class="org-node tertiary">
                    <div class="node-title">测试工程师</div>
                    <div class="node-subtitle">3人</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 工作流程图 -->
            <div v-else-if="chartType === 'workflow'" class="workflow-chart">
              <div class="workflow-container">
                <div class="workflow-step">
                  <div class="step-node start">需求分析</div>
                  <div class="step-arrow">→</div>
                </div>
                <div class="workflow-step">
                  <div class="step-node">系统设计</div>
                  <div class="step-arrow">→</div>
                </div>
                <div class="workflow-step">
                  <div class="step-node">开发实现</div>
                  <div class="step-arrow">→</div>
                </div>
                <div class="workflow-step">
                  <div class="step-node">测试验证</div>
                  <div class="step-arrow">→</div>
                </div>
                <div class="workflow-step">
                  <div class="step-node end">上线部署</div>
                </div>
              </div>
            </div>

            <!-- 里程碑图 -->
            <div v-else-if="chartType === 'milestone'" class="milestone-chart">
              <div class="timeline-container">
                <div class="timeline-item" v-for="(milestone, index) in milestones" :key="index">
                  <div class="timeline-dot" :class="milestone.status"></div>
                  <div class="timeline-content">
                    <h4 class="milestone-title">{{ milestone.title }}</h4>
                    <p class="milestone-date">{{ milestone.date }}</p>
                    <p class="milestone-desc">{{ milestone.description }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 资源分布图 -->
            <div v-else-if="chartType === 'resource'" class="resource-chart">
              <div class="resource-grid grid grid-cols-2 gap-6">
                <div class="resource-item">
                  <h4 class="resource-title">人力资源</h4>
                  <div class="resource-bar">
                    <div class="bar-item">
                      <span>开发人员</span>
                      <div class="bar-progress">
                        <div class="bar-fill" style="width: 70%"></div>
                      </div>
                      <span>7人</span>
                    </div>
                    <div class="bar-item">
                      <span>测试人员</span>
                      <div class="bar-progress">
                        <div class="bar-fill" style="width: 30%"></div>
                      </div>
                      <span>3人</span>
                    </div>
                    <div class="bar-item">
                      <span>设计人员</span>
                      <div class="bar-progress">
                        <div class="bar-fill" style="width: 20%"></div>
                      </div>
                      <span>2人</span>
                    </div>
                  </div>
                </div>

                <div class="resource-item">
                  <h4 class="resource-title">技术资源</h4>
                  <div class="resource-bar">
                    <div class="bar-item">
                      <span>服务器</span>
                      <div class="bar-progress">
                        <div class="bar-fill" style="width: 60%"></div>
                      </div>
                      <span>6台</span>
                    </div>
                    <div class="bar-item">
                      <span>数据库</span>
                      <div class="bar-progress">
                        <div class="bar-fill" style="width: 40%"></div>
                      </div>
                      <span>4个</span>
                    </div>
                    <div class="bar-item">
                      <span>第三方服务</span>
                      <div class="bar-progress">
                        <div class="bar-fill" style="width: 50%"></div>
                      </div>
                      <span>5个</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </a-spin>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted } from 'vue';
  import { Spin, Select, Button, message } from 'ant-design-vue';
  import { PictureOutlined, DownloadOutlined, PrinterOutlined, ReloadOutlined } from '@ant-design/icons-vue';
  import { getProjectStaticChart } from '../../api';

  const props = defineProps({
    projectId: {
      type: String,
      default: '',
    },
  });

  const chartType = ref('organization');
  const loading = ref(false);
  const chartGenerated = ref(false);

  // 里程碑数据
  const milestones = ref([
    {
      title: '项目启动',
      date: '2024-01-01',
      description: '项目正式启动，团队组建完成',
      status: 'completed',
    },
    {
      title: '需求确认',
      date: '2024-01-15',
      description: '需求分析完成，原型设计确认',
      status: 'completed',
    },
    {
      title: '开发阶段',
      date: '2024-02-01',
      description: '进入开发阶段，各模块并行开发',
      status: 'current',
    },
    {
      title: '测试阶段',
      date: '2024-03-01',
      description: '系统测试，bug修复',
      status: 'pending',
    },
    {
      title: '项目上线',
      date: '2024-03-15',
      description: '项目正式上线运行',
      status: 'pending',
    },
  ]);

  const chartTitle = computed(() => {
    const titles = {
      organization: '项目组织架构图',
      workflow: '项目工作流程图',
      milestone: '项目里程碑时间线',
      resource: '项目资源分布图',
    };
    return titles[chartType.value] || '项目静态图';
  });

  const chartDescription = computed(() => {
    const descriptions = {
      organization: '展示项目团队的组织结构和人员分工',
      workflow: '展示项目的主要工作流程和步骤',
      milestone: '展示项目的关键里程碑和时间节点',
      resource: '展示项目的资源配置和分布情况',
    };
    return descriptions[chartType.value] || '项目静态图表';
  });

  onMounted(() => {
    // 默认生成组织架构图
    generateChart();
  });

  const onChartTypeChange = (value: string) => {
    chartType.value = value;
    chartGenerated.value = false;
  };

  const generateChart = async () => {
    loading.value = true;
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 这里可以调用实际的API获取数据
      // const result = await getProjectStaticChart({
      //   projectId: props.projectId,
      //   chartType: chartType.value
      // });

      chartGenerated.value = true;
      message.success('图表生成成功');
    } catch (error) {
      console.error('生成图表失败:', error);
      message.error('生成图表失败');
    } finally {
      loading.value = false;
    }
  };

  const exportChart = () => {
    // 导出图表为图片
    message.info('导出功能开发中...');
  };

  const printChart = () => {
    // 打印图表
    window.print();
  };

  const refreshData = () => {
    generateChart();
  };
</script>

<style lang="less" scoped>
  .project-static-chart {
    .page-header {
      border-bottom: 1px solid #f0f0f0;
      padding-bottom: 16px;
    }

    .toolbar {
      background: #fafafa;
      padding: 12px 16px;
      border-radius: 6px;
      border: 1px solid #e8e8e8;
    }

    .chart-container {
      .chart-wrapper {
        min-height: 600px;
      }

      .empty-state {
        color: #999;
      }
    }

    // 组织架构图样式
    .organization-chart {
      .org-node-container {
        position: relative;
      }

      .org-node {
        display: inline-block;
        padding: 12px 20px;
        border-radius: 8px;
        text-align: center;
        min-width: 120px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        &.primary {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
        }

        &.secondary {
          background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
          color: white;
        }

        &.tertiary {
          background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
          color: white;
        }

        .node-title {
          font-weight: 600;
          font-size: 14px;
        }

        .node-subtitle {
          font-size: 12px;
          opacity: 0.9;
          margin-top: 4px;
        }
      }
    }

    // 工作流程图样式
    .workflow-chart {
      .workflow-container {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-wrap: wrap;
        gap: 20px;
      }

      .workflow-step {
        display: flex;
        align-items: center;
      }

      .step-node {
        padding: 16px 24px;
        background: #f8f9fa;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        font-weight: 500;
        min-width: 120px;
        text-align: center;

        &.start {
          background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
          border-color: #84fab0;
          color: #2d5a27;
        }

        &.end {
          background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
          border-color: #fa709a;
          color: #8b2635;
        }
      }

      .step-arrow {
        font-size: 20px;
        color: #666;
        margin: 0 10px;
      }
    }

    // 里程碑图样式
    .milestone-chart {
      .timeline-container {
        position: relative;
        padding-left: 30px;

        &::before {
          content: '';
          position: absolute;
          left: 15px;
          top: 0;
          bottom: 0;
          width: 2px;
          background: #e8e8e8;
        }
      }

      .timeline-item {
        position: relative;
        margin-bottom: 30px;

        .timeline-dot {
          position: absolute;
          left: -23px;
          top: 8px;
          width: 16px;
          height: 16px;
          border-radius: 50%;
          border: 3px solid #e8e8e8;
          background: white;

          &.completed {
            background: #52c41a;
            border-color: #52c41a;
          }

          &.current {
            background: #1890ff;
            border-color: #1890ff;
          }

          &.pending {
            background: #d9d9d9;
            border-color: #d9d9d9;
          }
        }

        .timeline-content {
          .milestone-title {
            font-weight: 600;
            margin-bottom: 4px;
            color: #262626;
          }

          .milestone-date {
            color: #1890ff;
            font-size: 12px;
            margin-bottom: 8px;
          }

          .milestone-desc {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
          }
        }
      }
    }

    // 资源分布图样式
    .resource-chart {
      .resource-item {
        background: #fafafa;
        padding: 20px;
        border-radius: 8px;
        border: 1px solid #e8e8e8;

        .resource-title {
          font-weight: 600;
          margin-bottom: 16px;
          color: #262626;
        }

        .bar-item {
          display: flex;
          align-items: center;
          margin-bottom: 12px;
          font-size: 14px;

          span:first-child {
            width: 80px;
            color: #666;
          }

          span:last-child {
            width: 40px;
            text-align: right;
            color: #262626;
            font-weight: 500;
          }

          .bar-progress {
            flex: 1;
            height: 8px;
            background: #f0f0f0;
            border-radius: 4px;
            margin: 0 12px;
            overflow: hidden;

            .bar-fill {
              height: 100%;
              background: linear-gradient(90deg, #1890ff, #36cfc9);
              border-radius: 4px;
              transition: width 0.3s ease;
            }
          }
        }
      }
    }
  }
</style>
