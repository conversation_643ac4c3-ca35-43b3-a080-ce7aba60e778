import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { h } from 'vue';
import { Tag, Badge } from 'ant-design-vue';

export const columns: BasicColumn[] = [
  {
    title: '模板编码',
    dataIndex: 'code',
    width: 120,
    fixed: 'left',
  },
  {
    title: '模板名称',
    dataIndex: 'name',
    width: 200,
    fixed: 'left',
  },
  {
    title: '模板描述',
    dataIndex: 'description',
    width: 300,
    ellipsis: true,
  },
  {
    title: '模板类型',
    dataIndex: 'typeName',
    width: 120,
  },
  {
    title: 'WBS数量',
    dataIndex: 'wbsCount',
    width: 100,
    align: 'center',
    slots: { customRender: 'wbsCount' },
  },
  {
    title: '阶段数量',
    dataIndex: 'phaseCount',
    width: 100,
    align: 'center',
    slots: { customRender: 'phaseCount' },
  },
  {
    title: '里程碑数',
    dataIndex: 'milestoneCount',
    width: 100,
    align: 'center',
    slots: { customRender: 'milestoneCount' },
  },
  {
    title: '总工期(天)',
    dataIndex: 'totalDuration',
    width: 120,
    align: 'center',
  },
  {
    title: '配置完整度',
    dataIndex: 'configCompleteness',
    width: 120,
    align: 'center',
    slots: { customRender: 'configCompleteness' },
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    slots: { customRender: 'status' },
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    width: 150,
    format: 'date|YYYY-MM-DD HH:mm:ss',
  },
  {
    title: '创建人',
    dataIndex: 'creatorUserName',
    width: 120,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'keyword',
    label: '关键字',
    component: 'Input',
    componentProps: {
      placeholder: '请输入模板编码或名称',
    },
  },
  {
    field: 'status',
    label: '状态',
    component: 'Select',
    componentProps: {
      options: [
        { fullName: '启用', id: 0 },
        { fullName: '禁用', id: 1 },
      ],
    },
  },
  {
    field: 'typeId',
    label: '模板类型',
    component: 'ApiSelect',
    componentProps: {
      // TODO: 替换为实际的字典API
      api: () => Promise.resolve([]),
      labelField: 'fullName',
      valueField: 'id',
      placeholder: '请选择模板类型',
    },
  },
  {
    field: 'createTimeGroup',
    label: '创建时间',
    component: 'DateRange',
  },
  {
    field: 'descriptionKeyword',
    label: '描述关键字',
    component: 'Input',
    componentProps: {
      placeholder: '请输入描述关键字',
    },
  },
];

export const formSchema: FormSchema[] = [
  {
    field: 'id',
    label: 'ID',
    component: 'Input',
    show: false,
  },
  {
    field: 'code',
    label: '模板编码',
    component: 'Input',
    required: true,
    rules: [
      { required: true, message: '请输入模板编码' },
      { pattern: /^[A-Z0-9_-]+$/, message: '编码只能包含大写字母、数字、下划线和中划线' },
      { max: 50, message: '编码长度不能超过50个字符' },
    ],
    componentProps: {
      placeholder: '请输入模板编码（可自动生成）',
      maxlength: 50,
      showCount: true,
    },
    colProps: { span: 12 },
  },
  {
    field: 'name',
    label: '模板名称',
    component: 'Input',
    required: true,
    rules: [
      { required: true, message: '请输入模板名称' },
      { min: 2, max: 255, message: '模板名称长度必须在2-255个字符之间' },
      { pattern: /^[^<>"'&]*$/, message: '模板名称不能包含特殊字符' },
    ],
    componentProps: {
      placeholder: '请输入模板名称',
      maxlength: 255,
      showCount: true,
    },
    colProps: { span: 12 },
  },
  {
    field: 'description',
    label: '模板描述',
    component: 'Textarea',
    rules: [{ max: 1000, message: '模板描述不能超过1000个字符' }],
    componentProps: {
      placeholder: '请输入模板描述，详细说明模板的使用场景和特点',
      rows: 4,
      maxlength: 1000,
      showCount: true,
    },
    colProps: { span: 24 },
  },
  {
    field: 'typeId',
    label: '模板类型',
    component: 'ApiSelect',
    componentProps: {
      // TODO: 替换为实际的字典API
      api: () => Promise.resolve([]),
      labelField: 'fullName',
      valueField: 'id',
      placeholder: '请选择模板类型',
    },
    colProps: { span: 12 },
  },
  {
    field: 'status',
    label: '状态',
    component: 'Radio',
    defaultValue: 0,
    componentProps: {
      options: [
        { fullName: '启用', id: 0 },
        { fullName: '禁用', id: 1 },
      ],
    },
    colProps: { span: 12 },
  },
  {
    field: 'icon',
    label: '模板图标',
    component: 'Input',
    rules: [{ max: 100, message: '图标名称不能超过100个字符' }],
    componentProps: {
      placeholder: '请输入图标CSS类名，如：icon-ym icon-ym-generator-template',
      maxlength: 100,
      showCount: true,
    },
    colProps: { span: 24 },
  },
];

// WBS配置表格列
export const wbsConfigColumns: BasicColumn[] = [
  {
    title: '序号',
    dataIndex: 'seqNo',
    width: 80,
    align: 'center',
  },
  {
    title: 'WBS编码',
    dataIndex: 'wbsCode',
    width: 120,
  },
  {
    title: '活动名称',
    dataIndex: 'name',
    width: 200,
    ellipsis: true,
  },
  {
    title: '节点类型',
    dataIndex: 'nodeType',
    width: 100,
    slots: { customRender: 'nodeType' },
  },
  {
    title: '是否里程碑',
    dataIndex: 'isMilestone',
    width: 100,
    align: 'center',
    slots: { customRender: 'isMilestone' },
  },
  {
    title: '工期(天)',
    dataIndex: 'duration',
    width: 100,
    align: 'center',
  },
  {
    title: '层级',
    dataIndex: 'level',
    width: 80,
    align: 'center',
  },
  {
    title: '责任角色',
    dataIndex: 'responseRoleName',
    width: 120,
  },
];

// 阶段配置表格列
export const phaseConfigColumns: BasicColumn[] = [
  {
    title: '序号',
    dataIndex: 'seqNo',
    width: 80,
    align: 'center',
  },
  {
    title: '阶段名称',
    dataIndex: 'phaseName',
    width: 200,
  },
  {
    title: '工期(天)',
    dataIndex: 'duration',
    width: 100,
    align: 'center',
  },
  {
    title: '完成权重(%)',
    dataIndex: 'completionWeight',
    width: 120,
    align: 'center',
  },
  {
    title: '可裁剪',
    dataIndex: 'canCut',
    width: 100,
    align: 'center',
    slots: { customRender: 'canCut' },
  },
  {
    title: '审批流程',
    dataIndex: 'approvalName',
    width: 120,
    ellipsis: true,
  },
  {
    title: '检查清单',
    dataIndex: 'checklistName',
    width: 120,
    ellipsis: true,
  },
  {
    title: '交付物模板',
    dataIndex: 'workproductTplName',
    width: 120,
    ellipsis: true,
  },
];
