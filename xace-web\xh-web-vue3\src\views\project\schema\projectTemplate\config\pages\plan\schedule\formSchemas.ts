import { FormSchema } from '/@/components/Table';

// 主计划任务表单配置
export const scheduleFormSchema: FormSchema[] = [
  {
    field: 'name',
    label: '任务名称',
    component: 'Input',
    required: true,
    colProps: { span: 12 },
    componentProps: {
      placeholder: '请输入任务名称',
    },
    rules: [
      { required: true, message: '请输入任务名称' },
      { max: 100, message: '任务名称不能超过100个字符' },
    ],
  },
  {
    field: 'code',
    label: '任务编码',
    component: 'Input',
    required: true,
    colProps: { span: 12 },
    componentProps: {
      placeholder: '请输入任务编码',
    },
    rules: [
      { required: true, message: '请输入任务编码' },
      { max: 20, message: '任务编码不能超过20个字符' },
      { pattern: /^[A-Z0-9_]+$/, message: '任务编码只能包含大写字母、数字和下划线' },
    ],
  },
  {
    field: 'description',
    label: '任务描述',
    component: 'InputTextArea',
    colProps: { span: 24 },
    componentProps: {
      placeholder: '请输入任务描述',
      rows: 3,
      maxlength: 500,
      showCount: true,
    },
  },
  {
    field: 'duration',
    label: '计划工期(天)',
    component: 'InputNumber',
    required: true,
    colProps: { span: 8 },
    componentProps: {
      placeholder: '请输入计划工期',
      min: 1,
      max: 999,
    },
    rules: [{ required: true, message: '请输入计划工期' }],
  },
  {
    field: 'responsibleRole',
    label: '负责角色',
    component: 'Select',
    colProps: { span: 8 },
    componentProps: {
      placeholder: '请选择负责角色',
      options: [
        { label: '项目经理', value: 'PM' },
        { label: '技术负责人', value: 'TECH_LEAD' },
        { label: '开发工程师', value: 'DEVELOPER' },
        { label: '测试工程师', value: 'TESTER' },
        { label: '产品经理', value: 'PRODUCT_MANAGER' },
        { label: '设计师', value: 'DESIGNER' },
      ],
    },
  },
  {
    field: 'isMilestone',
    label: '里程碑标记',
    component: 'Switch',
    colProps: { span: 8 },
    componentProps: {
      checkedChildren: '是',
      unCheckedChildren: '否',
    },
    defaultValue: false,
  },
  {
    field: 'priority',
    label: '优先级',
    component: 'Select',
    colProps: { span: 8 },
    componentProps: {
      placeholder: '请选择优先级',
      options: [
        { label: '高', value: 'HIGH' },
        { label: '中', value: 'MEDIUM' },
        { label: '低', value: 'LOW' },
      ],
    },
    defaultValue: 'MEDIUM',
  },
  {
    field: 'workload',
    label: '工作量(人天)',
    component: 'InputNumber',
    colProps: { span: 8 },
    componentProps: {
      placeholder: '请输入工作量',
      min: 0.5,
      max: 999,
      step: 0.5,
    },
  },
  {
    field: 'sortOrder',
    label: '排序',
    component: 'InputNumber',
    colProps: { span: 8 },
    componentProps: {
      placeholder: '请输入排序号',
      min: 1,
      max: 999,
    },
  },
  {
    field: 'deliverables',
    label: '关联交付物',
    component: 'Select',
    colProps: { span: 24 },
    componentProps: {
      mode: 'multiple',
      placeholder: '请选择关联的交付物',
      options: [], // 需要在组件中动态加载
    },
  },
  {
    field: 'skills',
    label: '所需技能',
    component: 'Select',
    colProps: { span: 12 },
    componentProps: {
      mode: 'tags',
      placeholder: '请输入所需技能标签',
      maxTagCount: 5,
    },
  },
  {
    field: 'riskLevel',
    label: '风险等级',
    component: 'Select',
    colProps: { span: 12 },
    componentProps: {
      placeholder: '请选择风险等级',
      options: [
        { label: '低风险', value: 'LOW' },
        { label: '中风险', value: 'MEDIUM' },
        { label: '高风险', value: 'HIGH' },
      ],
    },
    defaultValue: 'LOW',
  },
];
