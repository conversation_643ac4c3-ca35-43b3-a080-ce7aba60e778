# Controller 层规范 - XACE 标准实现

## 总体概述

Controller层是XACE框架中的表示层，负责处理HTTP请求，参数验证，业务委托和响应封装。本规范基于 `PhaseTemplateController` 标准实现制定，确保所有Controller遵循统一的开发模式。

## 基本结构

### 位置与命名

* **包路径:** `com.xinghuo.[模块名].controller`
* **命名规范:** 以 `Controller` 结尾，如 `UserController`、`PhaseTemplateController`
* **类注释:** 包含功能描述、作者、日期信息

### 标准注解配置

```java
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;

@Slf4j  // Lombok日志注解
@Tag(name = "功能模块名称", description = "功能模块相关接口")
@RestController  // REST控制器标识
@RequestMapping("/api/[模块]/[资源]")
public class XxxController {
    // 控制器实现
}
```

### 依赖注入规范

* 使用 `@Resource` 注解注入依赖服务（推荐使用 Jakarta EE 标准）
* 每个依赖独占一行，按功能逻辑排序
* 主要业务服务放在前面，辅助服务放在后面
* 导入 import com.xinghuo.common.base.ActionResult;


```java
@Resource
private PhaseTemplateService phaseTemplateService;

@Resource  
private NotificationService notificationService;
```

## XACE 标准 Controller 完整示例

以下是基于 `PhaseTemplateController` 的标准实现：

```java
import com.xinghuo.common.base.ActionResult;

/**
 * 阶段模板管理控制器
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Tag(name = "阶段模板管理", description = "阶段模板管理相关接口")
@RestController
@RequestMapping("/api/project/template/phaseTemplate")
public class PhaseTemplateController {

    @Resource
    private PhaseTemplateService phaseTemplateService;

    /**
     * 获取阶段模板列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取阶段模板列表")
    public ActionResult<PageListVO<PhaseTemplateVO>> list(@RequestBody PhaseTemplatePagination pagination) {
        List<PhaseTemplateEntity> list = phaseTemplateService.getList(pagination);
        List<PhaseTemplateVO> listVO = BeanCopierUtils.copyList(list, PhaseTemplateVO.class);

        // 对结果进行数据转换和补充
        for (PhaseTemplateVO vo : listVO) {
            // 状态名称转换
            if (vo.getStatus() != null) {
                vo.setStatusName(vo.getStatus() == 1 ? "启用" : "禁用");
            }
        }

        PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
        return ActionResult.page(listVO, page);
    }

    /**
     * 获取阶段模板详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取阶段模板详情")
    public ActionResult<PhaseTemplateEntity> getInfo(
            @Parameter(description = "阶段模板ID") @PathVariable String id) {
        PhaseTemplateEntity entity = phaseTemplateService.getInfo(id);
        if (entity == null) {
            return ActionResult.fail("阶段模板不存在");
        }
        return ActionResult.success(entity);
    }

    /**
     * 创建阶段模板
     */
    @PostMapping("")
    @Operation(summary = "创建阶段模板")
    public ActionResult<String> create(@RequestBody @Valid PhaseTemplateForm form) {
        // 检查阶段编码是否已存在
        if (phaseTemplateService.isExistByCode(form.getCode(), null)) {
            return ActionResult.fail("阶段编码已存在");
        }

        PhaseTemplateEntity entity = BeanCopierUtils.copy(form, PhaseTemplateEntity.class);
        String id = phaseTemplateService.create(entity);
        return ActionResult.success("创建成功", id);
    }

    /**
     * 更新阶段模板
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新阶段模板")
    public ActionResult<String> update(
            @Parameter(description = "阶段模板ID") @PathVariable String id,
            @RequestBody @Valid PhaseTemplateForm form) {
        // 检查阶段编码是否已存在
        if (phaseTemplateService.isExistByCode(form.getCode(), id)) {
            return ActionResult.fail("阶段编码已存在");
        }

        PhaseTemplateEntity entity = BeanCopierUtils.copy(form, PhaseTemplateEntity.class);
        phaseTemplateService.update(id, entity);
        return ActionResult.success("更新成功");
    }

    /**
     * 删除阶段模板
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除阶段模板")
    public ActionResult<String> delete(
            @Parameter(description = "阶段模板ID") @PathVariable String id) {
        phaseTemplateService.delete(id);
        return ActionResult.success("删除成功");
    }
}
```

## RESTful 设计原则

### HTTP 方法映射
- **GET** - 查询资源（如详情、选择器列表）
- **POST** - 创建资源或复杂查询（如分页列表、创建实体）
- **PUT** - 更新资源（如完整更新、状态更新）
- **DELETE** - 删除资源（如单个删除、批量删除）

### URL 设计规范
- API 前缀：`/api/[模块]/[资源]`
- 资源使用名词，如：`/api/project/template/phaseTemplate`
- ID 参数使用路径变量：`/{id}`
- 查询参数使用 `@RequestParam`，复杂条件使用 Pagination 对象

### 标准接口模式
完整的 Controller 实现示例请参考上面的 `PhaseTemplateController` 示例。标准模式包括：
- 分页列表：`POST /getList`
- 详情查询：`GET /{id}`
- 创建：`POST ""`
- 更新：`PUT /{id}`  
- 删除：`DELETE /{id}`
- 选择器：`GET /getSelectList`

### 分页查询规范

**标准模式**：
```java
@PostMapping("/getList")
public ActionResult<PageListVO<EntityVO>> getList(@RequestBody EntityPagination pagination) {
    List<Entity> list = entityService.getList(pagination);
    List<EntityVO> listVO = BeanCopierUtils.copyList(list, EntityVO.class);
    PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
    return ActionResult.page(listVO, page);  // ✅ 使用ActionResult.page()
}
```

**禁止模式**：
- ❌ 返回 `Pagination<T>` 或 `PageResult<T>`
- ❌ Service返回分页对象

**Service配套**：
```java
List<Entity> getList(EntityPagination pagination);
Long getCount(EntityPagination pagination);
```


## 异常处理规范

**项目已配置全局异常处理器 `@ControllerAdvice`，Controller 方法中不应使用 try-catch 包装业务逻辑。**

详细的异常处理规范和示例请参考：[编码规范 - 异常处理](./02_CODING_STANDARDS.md#异常处理)

## Controller 最佳实践

1. **单一职责**：仅负责HTTP请求处理，不包含业务逻辑
2. **统一封装**：使用 ActionResult 包装所有返回结果
3. **参数校验**：使用 `@Valid` 进行参数验证
4. **RESTful 设计**：遵循标准HTTP方法和资源路径命名
5. **API 文档**：使用 Swagger 注解提供完整的接口文档

## XACE Controller 核心特征

1. **标准CRUD接口**：list、getInfo、create、update、delete
2. **ActionResult封装**：统一的响应格式
3. **RESTful设计**：标准HTTP方法和资源路径
4. **参数验证**：使用 `@Valid` 自动验证
5. **API文档**：完整的Swagger注解

### 标准接口清单

**基础CRUD接口（必须实现）：**
- [ ] `POST /getList` - 分页列表查询
- [ ] `GET /{id}` - 详情查询  
- [ ] `POST ""` - 创建
- [ ] `PUT /{id}` - 更新
- [ ] `DELETE /{id}` - 删除

**扩展接口（按需实现）：**
- [ ] `GET /getSelectList` - 选择器列表
- [ ] `DELETE /batchDelete` - 批量删除
- [ ] `PUT /updateStatus/{id}` - 状态更新
- [ ] `PUT /enable/{id}` - 启用
- [ ] `PUT /disable/{id}` - 禁用
- [ ] `PUT /batchUpdateStatus` - 批量状态更新
- [ ] `POST /copy/{id}` - 复制
- [ ] `GET /checkCodeExists` - 唯一性检查
- [ ] `GET /generateCode` - 代码生成
- [ ] `GET /getUsageInfo/{id}` - 使用情况查询
- [ ] `GET /getByCode/{code}` - 按编码查询

### 开发检查清单

- [ ] 使用 `@RestController` 和 `@RequestMapping`
- [ ] 实现标准CRUD接口
- [ ] 所有方法使用 ActionResult 封装返回值
- [ ] 使用 `@Valid` 进行参数校验
- [ ] 添加完整的 Swagger 文档注解
- [ ] 不在Controller中使用 try-catch

## Controller 代码生成常见问题及解决方案

### 1. Import 路径错误

**❌ 错误的导入路径：**
```java
import com.xinghuo.common.base.model.PageListVO;
import com.xinghuo.common.base.model.PaginationVO;
```

**✅ 正确的导入路径：**
```java
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
```

### 2. 分页接口设计错误

**❌ 错误的分页接口设计：**
```java
@PostMapping("/list")  // 错误的命名
public ActionResult<Pagination<Model>> getList(@RequestBody Pagination pagination) {
    // 手动分页逻辑
    Page<Entity> page = new Page<>(pagination.getCurrentPage(), pagination.getPageSize());
    IPage<Entity> pageResult = service.page(page, queryWrapper);
    // 手动转换...
    return ActionResult.success(result);
}
```

**✅ 正确的分页接口设计：**
```java
@PostMapping("/manageList")  // 或 /getList
public ActionResult<PageListVO<Model>> manageList(@RequestBody ModelPagination pagination) {
    List<Entity> list = service.getList(pagination);
    List<Model> listVO = BeanCopierUtils.copyList(list, Model.class);
    PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
    return ActionResult.page(listVO, page);
}
```

### 3. Service 层缺少必要方法

**❌ 直接在Controller中写查询逻辑：**
```java
@PostMapping("/list")
public ActionResult<PageListVO<Model>> getList(@RequestBody Pagination pagination) {
    LambdaQueryWrapper<Entity> lambda = new LambdaQueryWrapper<>();
    // 在Controller中写查询条件...
    IPage<Entity> pageResult = service.page(page, lambda);
    // 违反分层原则
}
```

**✅ 正确的Service层调用：**
```java
@PostMapping("/manageList")
public ActionResult<PageListVO<Model>> manageList(@RequestBody ModelPagination pagination) {
    List<Entity> list = service.getList(pagination);  // Service层处理查询逻辑
    List<Model> listVO = BeanCopierUtils.copyList(list, Model.class);
    PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
    return ActionResult.page(listVO, page);
}
```

### 4. 缺少业务逻辑处理

**❌ 仅做数据转换：**
```java
List<Model> listVO = BeanCopierUtils.copyList(list, Model.class);
return ActionResult.page(listVO, page);
```

**✅ 包含完整业务逻辑：**
```java
List<Model> listVO = BeanCopierUtils.copyList(list, Model.class);

// 获取用户信息映射
Map<String, String> userMap = getUserMap();

// 处理列表数据
listVO.forEach(item -> {
    // 设置用户名
    item.setUserName(userMap.get(item.getUserId()));
    
    // 检查状态
    if (item.getSyncFlag() && item.getExpireDate() != null && 
        item.getExpireDate().getTime() < System.currentTimeMillis()) {
        item.setTokenStatus(Boolean.FALSE);
    } else {
        item.setTokenStatus(Boolean.TRUE);
    }
    
    // 格式化时间
    if (item.getCreatedAt() != null) {
        item.setCreatedAtStr(DateXhUtil.formatDateTime(item.getCreatedAt()));
    }
    
    // 隐藏敏感信息
    item.setAccessToken("***");
    item.setRefreshToken("***");
});

PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
return ActionResult.page(listVO, page);
```

### 5. Model 类缺少必要字段

**❌ Model类只包含Entity字段：**
```java
@Data
public class StoreModel {
    private String id;
    private String storeName;
    // 只有Entity的字段
}
```

**✅ Model类包含显示所需的所有字段：**
```java
@Data
public class StoreModel {
    // Entity字段
    private String id;
    private String storeName;
    private Date createdAt;
    private Date expireDate;
    
    // 显示字段
    private String userName;              // 用户名（显示用）
    private String createdAtStr;          // 格式化时间
    private String expireDateStr;         // 格式化时间
    private String tokenStatusText;       // 状态描述
    
    // 业务方法
    public String getTokenStatusText() {
        return Boolean.TRUE.equals(tokenStatus) ? "正常" : "异常";
    }
}
```

### 6. 缺少必要的依赖注入

**❌ 缺少业务相关的Service：**
```java
@Resource
private MainService mainService;  // 只有主要Service
```

**✅ 注入所有必要的Service：**
```java
@Resource
private MainService mainService;

@Resource
private UserService userService;  // 用户相关

@Resource
private UserProvider userProvider;  // 用户上下文
```

### 7. 权限控制缺失

**❌ 没有权限控制：**
```java
public ActionResult<PageListVO<Model>> manageList(@RequestBody Pagination pagination) {
    List<Entity> list = service.list();  // 查询所有数据
    // ...
}
```

**✅ 包含权限控制：**
```java
public ActionResult<PageListVO<Model>> manageList(@RequestBody ModelPagination pagination) {
    List<Entity> list = service.getList(pagination);  // Service层处理权限
    // 或者在Controller层添加权限检查
    // ...
}
```

### 代码生成检查清单

生成Controller代码后，请按以下清单逐项检查：

**导入和注解：**
- [ ] 导入路径使用 `com.xinghuo.common.base.vo.*`
- [ ] 使用 `@RestController` 和正确的 `@RequestMapping`
- [ ] 添加 `@Tag` 和 `@Operation` 注解

**接口设计：**
- [ ] 分页接口使用 `/manageList` 或 `/getList`
- [ ] 返回类型为 `ActionResult<PageListVO<Model>>`
- [ ] 参数类型为具体的 `ModelPagination`

**Service层：**
- [ ] Service接口包含 `getList(pagination)` 方法
- [ ] Service实现包含完整的查询逻辑和权限控制

**业务逻辑：**
- [ ] 包含用户信息映射
- [ ] 包含状态检查和转换
- [ ] 包含时间格式化
- [ ] 包含敏感信息脱敏

**Model类：**
- [ ] 包含所有Entity字段
- [ ] 包含显示所需的格式化字段
- [ ] 包含状态描述方法
- [ ] 包含业务计算字段

**依赖注入：**
- [ ] 注入主要业务Service
- [ ] 注入UserService和UserProvider
- [ ] 按功能分组排列
