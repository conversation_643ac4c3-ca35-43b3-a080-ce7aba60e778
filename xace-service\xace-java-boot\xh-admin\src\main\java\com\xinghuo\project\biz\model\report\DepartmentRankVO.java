package com.xinghuo.project.biz.model.report;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 部门收款排行数据VO
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Schema(description = "部门收款排行数据VO")
public class DepartmentRankVO {

    /**
     * 部门名称列表
     */
    @Schema(description = "部门名称列表")
    private List<String> departments;

    /**
     * 收款金额列表
     */
    @Schema(description = "收款金额列表")
    private List<BigDecimal> amounts;
}
