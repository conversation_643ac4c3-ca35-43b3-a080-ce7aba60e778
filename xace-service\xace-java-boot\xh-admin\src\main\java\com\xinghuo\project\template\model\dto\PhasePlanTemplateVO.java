package com.xinghuo.project.template.model.dto;

import com.xinghuo.project.template.entity.PhasePlanTemplateDetailEntity;
import com.xinghuo.project.template.entity.PhasePlanTemplateEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 阶段计划模板DTO类
 * 包含主表信息和明细列表
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PhasePlanTemplateVO extends PhasePlanTemplateEntity {

    /**
     * 阶段明细列表
     */
    private List<PhasePlanTemplateDetailEntity> phaseDetails;

    /**
     * 关联的项目模板ID列表
     */
    private List<String> projectTemplateIds;

    /**
     * 标签ID列表
     */
    private List<String> tagIds;

    /**
     * 知识状态名称（冗余字段，便于显示）
     */
    private String knStatusName;

    /**
     * 创建人姓名（冗余字段，便于显示）
     */
    private String creatorUserName;

    /**
     * 阶段总数
     */
    private Integer phaseCount;

    /**
     * 总工期（所有阶段工期之和）
     */
    private Integer totalDuration;
}
