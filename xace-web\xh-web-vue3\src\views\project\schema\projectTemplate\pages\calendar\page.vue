<template>
  <div class="template-calendar-page p-6">
    <div class="max-w-7xl mx-auto">
      <!-- 页面标题 -->
      <div class="header-section mb-6">
        <h1 class="text-2xl font-bold mb-2">项目日历配置</h1>
        <p class="text-gray-600">配置项目模板的工作日历和节假日安排</p>
      </div>

      <!-- 工作日历配置 -->
      <a-card title="工作日历设置" :bordered="false" class="mb-6">
        <a-form :model="calendarForm" layout="vertical">
          <a-row :gutter="24">
            <a-col :span="8">
              <a-form-item label="工作日设置" name="workDays">
                <a-checkbox-group v-model:value="calendarForm.workDays">
                  <a-checkbox value="1">周一</a-checkbox>
                  <a-checkbox value="2">周二</a-checkbox>
                  <a-checkbox value="3">周三</a-checkbox>
                  <a-checkbox value="4">周四</a-checkbox>
                  <a-checkbox value="5">周五</a-checkbox>
                  <a-checkbox value="6">周六</a-checkbox>
                  <a-checkbox value="0">周日</a-checkbox>
                </a-checkbox-group>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="每日工作时长" name="dailyHours">
                <a-input-number v-model:value="calendarForm.dailyHours" :min="1" :max="24" :step="0.5" class="w-full" addon-after="小时" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="时区设置" name="timezone">
                <a-select v-model:value="calendarForm.timezone" placeholder="选择时区">
                  <a-select-option value="Asia/Shanghai">东八区 (GMT+8)</a-select-option>
                  <a-select-option value="UTC">UTC (GMT+0)</a-select-option>
                  <a-select-option value="America/New_York">东部时间 (GMT-5)</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <!-- 节假日配置 -->
      <a-card title="节假日配置" :bordered="false">
        <template #extra>
          <a-button type="primary" @click="handleAddHoliday">
            <template #icon><PlusOutlined /></template>
            添加节假日
          </a-button>
        </template>

        <a-table :columns="holidayColumns" :data-source="holidayList" :pagination="false" row-key="id" size="small">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'type'">
              <a-tag :color="record.type === 'holiday' ? 'red' : 'green'">
                {{ record.type === 'holiday' ? '节假日' : '调休' }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="handleEditHoliday(record)"> 编辑 </a-button>
                <a-button type="link" size="small" danger @click="handleDeleteHoliday(record)"> 删除 </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 节假日编辑弹窗 -->
    <a-modal
      v-model:open="holidayModalVisible"
      :title="holidayEditMode === 'add' ? '添加节假日' : '编辑节假日'"
      @ok="handleSaveHoliday"
      @cancel="handleCancelHoliday">
      <a-form :model="holidayForm" :rules="holidayRules" layout="vertical">
        <a-form-item label="名称" name="name" required>
          <a-input v-model:value="holidayForm.name" placeholder="请输入节假日名称" />
        </a-form-item>
        <a-form-item label="类型" name="type">
          <a-radio-group v-model:value="holidayForm.type">
            <a-radio value="holiday">节假日</a-radio>
            <a-radio value="workday">调休</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="日期范围" name="dateRange">
          <a-range-picker v-model:value="holidayForm.dateRange" class="w-full" />
        </a-form-item>
        <a-form-item label="描述" name="description">
          <a-textarea v-model:value="holidayForm.description" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import {
    Card as ACard,
    Form as AForm,
    FormItem as AFormItem,
    Row as ARow,
    Col as ACol,
    CheckboxGroup as ACheckboxGroup,
    Checkbox as ACheckbox,
    InputNumber as AInputNumber,
    Select as ASelect,
    SelectOption as ASelectOption,
    Button as AButton,
    Table as ATable,
    Tag as ATag,
    Space as ASpace,
    Modal as AModal,
    Input as AInput,
    RadioGroup as ARadioGroup,
    Radio as ARadio,
    RangePicker as ARangePicker,
    Textarea as ATextarea,
  } from 'ant-design-vue';
  import { PlusOutlined } from '@ant-design/icons-vue';
  import { useMessage } from '/@/hooks/web/useMessage';

  interface Props {
    templateId: string;
  }

  const props = defineProps<Props>();
  const { createMessage } = useMessage();

  // 响应式数据
  const calendarForm = reactive({
    workDays: ['1', '2', '3', '4', '5'], // 默认周一到周五
    dailyHours: 8,
    timezone: 'Asia/Shanghai',
  });

  const holidayList = ref([]);
  const holidayModalVisible = ref(false);
  const holidayEditMode = ref('add');
  const holidayForm = reactive({
    id: '',
    name: '',
    type: 'holiday',
    dateRange: [],
    description: '',
  });

  // 表格列定义
  const holidayColumns = [
    { title: '名称', dataIndex: 'name', width: 150 },
    { title: '类型', key: 'type', width: 100, align: 'center' },
    { title: '开始日期', dataIndex: 'startDate', width: 120, align: 'center' },
    { title: '结束日期', dataIndex: 'endDate', width: 120, align: 'center' },
    { title: '描述', dataIndex: 'description' },
    { title: '操作', key: 'action', width: 120, align: 'center', fixed: 'right' },
  ];

  // 表单验证规则
  const holidayRules = {
    name: [{ required: true, message: '请输入节假日名称', trigger: 'blur' }],
  };

  // 方法
  function handleAddHoliday() {
    resetHolidayForm();
    holidayEditMode.value = 'add';
    holidayModalVisible.value = true;
  }

  function handleEditHoliday(record: any) {
    Object.assign(holidayForm, record);
    holidayEditMode.value = 'edit';
    holidayModalVisible.value = true;
  }

  function handleDeleteHoliday(record: any) {
    createMessage.info('删除功能开发中...');
  }

  function handleSaveHoliday() {
    createMessage.info('保存功能开发中...');
    holidayModalVisible.value = false;
  }

  function handleCancelHoliday() {
    holidayModalVisible.value = false;
    resetHolidayForm();
  }

  function resetHolidayForm() {
    Object.assign(holidayForm, {
      id: '',
      name: '',
      type: 'holiday',
      dateRange: [],
      description: '',
    });
  }

  // 初始化
  onMounted(() => {
    // 加载日历配置
    loadCalendarConfig();
  });

  function loadCalendarConfig() {
    // TODO: 从API加载日历配置
    createMessage.info('日历配置加载功能开发中...');
  }
</script>

<style scoped>
  .template-calendar-page {
    background: #f5f5f5;
    min-height: 100vh;
  }

  :deep(.ant-card-head) {
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
  }

  :deep(.ant-table-thead > tr > th) {
    background: #fafafa;
    font-weight: 600;
  }
</style>
