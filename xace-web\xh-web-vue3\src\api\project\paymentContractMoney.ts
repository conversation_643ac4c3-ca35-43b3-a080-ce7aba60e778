import { defHttp } from '/@/utils/http/axios';

/**
 * 付款计划管理相关类型定义
 */

// 付款计划VO (基于PaymentContractMoneyVO)
export interface PaymentContractMoneyVO {
  id: string;
  pcId: string; // 采购合同ID
  paycontractName?: string; // 采购合同名称
  paycontractNo?: string; // 采购合同编号
  fktj: string; // 付款条件
  ratio?: string; // 付款比例（后端为String类型）
  cmMoney: number; // 付款金额
  yufuDate?: string; // 预付日期
  fukuanDate?: string; // 付款日期
  payStatus?: string; // 付款状态
  note?: string; // 备注
  lastNote?: string; // 最后备注
  ownId?: string; // 负责人ID
  ownName?: string; // 负责人名称
  deptId?: string; // 部门ID
  deptName?: string; // 部门名称
  ybAmount?: number; // 一部金额
  ebAmount?: number; // 二部金额
  otherAmount?: number; // 其他金额

  // 系统字段
  createUserId?: string;
  createUserName?: string;
  createTime?: string;
  lastModifiedUserId?: string;
  lastModifiedUserName?: string;
  updateTime?: string;
}

// 付款计划表单对象
export interface PaymentContractMoneyForm {
  id?: string;
  pcId: string; // 采购合同ID (必填)
  fktj: string; // 付款条件 (必填)
  ratio?: string; // 付款比例（后端为String类型）
  cmMoney: number; // 付款金额 (必填)
  yufuDate?: string; // 预付日期
  fukuanDate?: string; // 付款日期
  payStatus?: string; // 付款状态
  note?: string; // 备注
  ownId?: string; // 负责人ID
  deptId?: string; // 部门ID
  ybAmount?: number; // 一部金额
  ebAmount?: number; // 二部金额
  otherAmount?: number; // 其他金额
}

// 付款计划分页查询参数
export interface PaymentContractMoneyPagination {
  currentPage: number;
  pageSize: number;
  keyword?: string; // 关键字搜索
  pcId?: string; // 采购合同ID
  fktj?: string; // 付款条件
  payStatus?: string; // 付款状态
  ownId?: string; // 负责人ID

  // 时间范围查询
  yufuDateStart?: string; // 预付日期开始
  yufuDateEnd?: string; // 预付日期结束
  fukuanDateStart?: string; // 付款日期开始
  fukuanDateEnd?: string; // 付款日期结束

  // 金额范围查询
  minCmMoney?: number; // 最小付款金额
  maxCmMoney?: number; // 最大付款金额
}

// 付款状态更新表单
export interface PaymentContractMoneyStatusForm {
  payStatus: string;
  fukuanDate?: string; // 付款日期
  lastNote?: string; // 备注
}

// 分页结果
export interface PageResult<T> {
  list: T[];
  total: number;
  currentPage: number;
  pageSize: number;
}

// API响应包装类型
export interface ApiResponse<T = any> {
  code: number;
  data: T;
  msg?: string;
}

// 付款计划统计信息
export interface PaymentContractMoneyStats {
  totalAmount: number; // 计划总金额
  paidAmount: number; // 已付金额
  unpaidAmount: number; // 待付金额
  completedCount: number; // 已完成计划数量
  pendingCount: number; // 待付计划数量
  progressRate: number; // 执行进度
}

/**
 * 付款计划管理API接口
 */
export const paymentContractMoneyApi = {
  /**
   * 获取付款计划列表 (分页)
   */
  getList: (params: PaymentContractMoneyPagination) => {
    return defHttp.post<ApiResponse<PageResult<PaymentContractMoneyVO>>>({ url: '/api/project/biz/paymentContract/money/getList', data: params });
  },

  /**
   * 根据采购合同ID获取付款计划列表
   */
  getByPaycontractId: (paycontractId: string) => {
    return defHttp.get<ApiResponse<PaymentContractMoneyVO[]>>({ url: `/api/project/biz/paymentContract/money/paycontract/${paycontractId}` });
  },

  /**
   * 获取付款计划详情
   */
  getInfo: (id: string) => {
    return defHttp.get<ApiResponse<PaymentContractMoneyVO>>({ url: `/api/project/biz/paymentContract/money/${id}` });
  },

  /**
   * 创建付款计划
   */
  create: (data: PaymentContractMoneyForm) => {
    return defHttp.post<ApiResponse<string>>({ url: '/api/project/biz/paymentContract/money', data });
  },

  /**
   * 更新付款计划
   */
  update: (id: string, data: PaymentContractMoneyForm) => {
    return defHttp.put<ApiResponse<string>>({ url: `/api/project/biz/paymentContract/money/${id}`, data });
  },

  /**
   * 删除付款计划
   */
  delete: (id: string) => {
    return defHttp.delete<ApiResponse<string>>({ url: `/api/project/biz/paymentContract/money/${id}` });
  },

  /**
   * 更新付款状态
   */
  updateStatus: (id: string, data: PaymentContractMoneyStatusForm) => {
    return defHttp.put<ApiResponse<string>>({ url: `/api/project/biz/paymentContract/money/${id}/status`, data });
  },

  /**
   * 登记付款
   */
  registerPayment: (id: string, fukuanDate: string, lastNote?: string) => {
    return defHttp.put<ApiResponse<string>>({
      url: `/api/project/biz/paymentContract/money/${id}/payment`,
      params: { fukuanDate, lastNote },
    });
  },

  /**
   * 获取付款计划统计信息
   */
  getStats: (paycontractId?: string) => {
    return defHttp.get<ApiResponse<PaymentContractMoneyStats>>({
      url: '/api/project/biz/paymentContract/money/stats',
      params: { paycontractId },
    });
  },
};

// 默认导出
export default paymentContractMoneyApi;
