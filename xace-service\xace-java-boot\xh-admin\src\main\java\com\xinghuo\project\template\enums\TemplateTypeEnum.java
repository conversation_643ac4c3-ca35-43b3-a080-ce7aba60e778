package com.xinghuo.project.template.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 模板类型枚举
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Getter
@AllArgsConstructor
public enum TemplateTypeEnum {

    /**
     * 项目模板
     */
    PROJECT("project", "项目模板"),

    /**
     * 阶段模板
     */
    PHASE("phase", "阶段模板"),

    /**
     * 任务模板
     */
    TASK("task", "任务模板");

    private final String code;
    private final String message;

    /**
     * 根据代码获取枚举
     */
    public static TemplateTypeEnum getByCode(String code) {
        for (TemplateTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 检查代码是否有效
     */
    public static boolean isValidCode(String code) {
        return getByCode(code) != null;
    }
}
