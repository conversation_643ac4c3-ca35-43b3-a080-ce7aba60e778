<template>
  <div class="contract-analysis-management">
    <div class="page-header">
      <div class="flex items-center">
        <i class="icon-ym icon-ym-analysis mr-2 text-lg"></i>
        <span class="text-base font-medium">合同信息分析</span>
      </div>
      <div class="flex items-center space-x-2">
        <a-button @click="handleExport">
          <template #icon><DownloadOutlined /></template>
          导出报告
        </a-button>
        <a-button @click="handleRefresh">
          <template #icon><ReloadOutlined /></template>
          刷新
        </a-button>
      </div>
    </div>

    <div class="page-content">
      <a-spin :spinning="loading">
        <!-- 合同基础信息概览 -->
        <div class="contract-overview mb-4" v-if="currentContractInfo">
          <a-card>
            <div class="grid grid-cols-4 gap-4">
              <div class="info-item">
                <div class="text-sm text-gray-500">合同名称</div>
                <div class="text-base font-medium">{{ currentContractInfo.name || '-' }}</div>
              </div>
              <div class="info-item">
                <div class="text-sm text-gray-500">合同编号</div>
                <div class="text-base font-medium">{{ currentContractInfo.cno || '-' }}</div>
              </div>
              <div class="info-item">
                <div class="text-sm text-gray-500">签订日期</div>
                <div class="text-base font-medium">{{ currentContractInfo.signDate ? formatDate(currentContractInfo.signDate) : '-' }}</div>
              </div>
              <div class="info-item">
                <div class="text-sm text-gray-500">合同状态</div>
                <div class="text-base font-medium">
                  <a-tag :color="getContractStatusColor(currentContractInfo.contractStatus)">
                    {{ getContractStatusText(currentContractInfo.contractStatus) }}
                  </a-tag>
                </div>
              </div>
            </div>
          </a-card>
        </div>

        <!-- 合同概览统计 -->
        <div class="analysis-overview mb-4">
          <!-- 收入统计 -->
          <a-card title="收入情况" class="mb-4">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-statistic title="合同总金额" :value="overviewStats.totalAmount" :precision="2" suffix="元" :value-style="{ color: '#1890ff' }" />
              </a-col>
              <a-col :span="8">
                <a-statistic title="已收金额" :value="overviewStats.receivedAmount" :precision="2" suffix="元" :value-style="{ color: '#52c41a' }" />
              </a-col>
              <a-col :span="8">
                <a-statistic title="待收金额" :value="overviewStats.pendingReceiveAmount" :precision="2" suffix="元" :value-style="{ color: '#faad14' }" />
              </a-col>
            </a-row>
          </a-card>

          <!-- 支出统计 -->
          <a-card title="支出情况" class="mb-4">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-statistic title="付款合同总额" :value="paymentStats?.totalAmount || 0" :precision="2" suffix="元" :value-style="{ color: '#722ed1' }" />
              </a-col>
              <a-col :span="6">
                <a-statistic title="已付金额" :value="paymentStats?.totalPaidAmount || 0" :precision="2" suffix="元" :value-style="{ color: '#f5222d' }" />
              </a-col>
              <a-col :span="6">
                <a-statistic title="待付金额" :value="paymentStats?.totalUnpaidAmount || 0" :precision="2" suffix="元" :value-style="{ color: '#fa8c16' }" />
              </a-col>
              <a-col :span="6">
                <a-statistic title="预估毛利" :value="overviewStats.grossProfit" :precision="2" suffix="元" :value-style="{ color: '#52c41a' }" />
              </a-col>
            </a-row>
          </a-card>
        </div>

        <!-- 图表分析 -->
        <div class="analysis-charts">
          <a-row :gutter="16">
            <!-- 收款进度图表 -->
            <a-col :span="12">
              <a-card title="收款进度分析" class="mb-4">
                <div class="chart-container">
                  <div class="progress-chart">
                    <a-progress
                      type="circle"
                      :percent="receivedProgress"
                      :width="120"
                      :stroke-color="{
                        '0%': '#108ee9',
                        '100%': '#87d068',
                      }" />
                    <div class="progress-info mt-4">
                      <p class="text-center text-gray-600"> 已收款 {{ receivedProgress }}%，剩余 {{ 100 - receivedProgress }}% 待收 </p>
                    </div>
                  </div>
                </div>
              </a-card>
            </a-col>

            <!-- 付款进度图表 -->
            <a-col :span="12">
              <a-card title="付款进度分析" class="mb-4">
                <div class="chart-container">
                  <div class="progress-chart">
                    <a-progress
                      type="circle"
                      :percent="paymentProgress"
                      :width="120"
                      :stroke-color="{
                        '0%': '#ffc53d',
                        '100%': '#ff7a45',
                      }" />
                    <div class="progress-info mt-4">
                      <p class="text-center text-gray-600"> 已付款 {{ paymentProgress }}%，剩余 {{ 100 - paymentProgress }}% 待付 </p>
                    </div>
                  </div>
                </div>
              </a-card>
            </a-col>
          </a-row>

          <!-- 月度收付款趋势分析 -->
          <a-row :gutter="16">
            <a-col :span="24">
              <a-card class="mb-4">
                <template #title>
                  <div class="flex justify-between items-center">
                    <span>月度收付款趋势分析</span>
                    <div class="flex items-center space-x-4">
                      <div class="flex items-center">
                        <div class="w-3 h-3 bg-green-500 rounded mr-1"></div>
                        <span class="text-sm">收款</span>
                      </div>
                      <div class="flex items-center">
                        <div class="w-3 h-3 bg-red-500 rounded mr-1"></div>
                        <span class="text-sm">付款</span>
                      </div>
                      <div class="flex items-center">
                        <div class="w-3 h-3 bg-blue-500 rounded mr-1"></div>
                        <span class="text-sm">净现金流</span>
                      </div>
                    </div>
                  </div>
                </template>
                
                <!-- 趋势图表区域 -->
                <div class="trend-chart-visual mb-4" v-if="trendData.length > 0">
                  <div class="chart-container" style="height: 300px;">
                    <!-- 这里可以集成 ECharts 或其他图表库 -->
                    <div class="simple-bar-chart">
                      <div class="chart-bars flex justify-around items-end h-60 bg-gray-50 p-4 rounded">
                        <div v-for="item in trendData" :key="item.month" class="bar-group flex flex-col items-center min-w-16">
                          <div class="bars flex items-end h-40 mb-2">
                            <!-- 收款柱 -->
                            <div 
                              class="bar bg-green-500 w-4 mr-1 rounded-t"
                              :style="{ height: `${Math.max(5, (item.receiveAmount / getMaxAmount()) * 100)}%` }"
                              :title="`收款: ¥${formatMoney(item.receiveAmount)}`"
                            ></div>
                            <!-- 付款柱 -->
                            <div 
                              class="bar bg-red-500 w-4 mr-1 rounded-t"
                              :style="{ height: `${Math.max(5, (item.paymentAmount / getMaxAmount()) * 100)}%` }"
                              :title="`付款: ¥${formatMoney(item.paymentAmount)}`"
                            ></div>
                            <!-- 净现金流柱 -->
                            <div 
                              :class="`bar w-4 rounded-t ${item.netCashFlow >= 0 ? 'bg-blue-500' : 'bg-orange-500'}`"
                              :style="{ height: `${Math.max(5, (Math.abs(item.netCashFlow) / getMaxAmount()) * 100)}%` }"
                              :title="`净现金流: ${item.netCashFlow >= 0 ? '+' : ''}¥${formatMoney(Math.abs(item.netCashFlow))}`"
                            ></div>
                          </div>
                          <span class="text-xs text-gray-600">{{ item.month.substring(5) }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="trend-chart">
                  <a-table :columns="trendColumns" :data-source="trendData" :pagination="false" size="small" :scroll="{ x: 800 }">
                    <template #bodyCell="{ column, record }">
                      <template v-if="column.key === 'receiveAmount'">
                        <span class="text-green-600 font-medium"> ¥{{ formatMoney(record.receiveAmount) }} </span>
                      </template>
                      <template v-else-if="column.key === 'paymentAmount'">
                        <span class="text-red-600 font-medium"> ¥{{ formatMoney(record.paymentAmount) }} </span>
                      </template>
                      <template v-else-if="column.key === 'netCashFlow'">
                        <span :class="record.netCashFlow >= 0 ? 'text-green-600' : 'text-red-600'" class="font-medium">
                          {{ record.netCashFlow >= 0 ? '+' : '' }}¥{{ formatMoney(Math.abs(record.netCashFlow)) }}
                        </span>
                      </template>
                      <template v-else-if="column.key === 'cumulativeReceive'">
                        <span class="text-blue-600 font-medium"> ¥{{ formatMoney(record.cumulativeReceive) }} </span>
                      </template>
                      <template v-else-if="column.key === 'cumulativePayment'">
                        <span class="text-orange-600 font-medium"> ¥{{ formatMoney(record.cumulativePayment) }} </span>
                      </template>
                      <template v-else-if="column.key === 'trend'">
                        <a-tag :color="record.trend > 0 ? 'green' : record.trend < 0 ? 'red' : 'gray'">
                          {{ record.trend > 0 ? '+' : '' }}{{ record.trend.toFixed(1) }}%
                        </a-tag>
                      </template>
                    </template>
                  </a-table>
                </div>
              </a-card>
            </a-col>
          </a-row>
        </div>

        <!-- 合同执行分析 -->
        <div class="execution-analysis">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-card title="收付款进度对比" class="mb-4">
                <div class="progress-comparison">
                  <!-- 收款进度 -->
                  <div class="progress-item mb-6">
                    <div class="flex justify-between items-center mb-2">
                      <span class="font-medium">收款进度</span>
                      <span class="text-sm text-gray-600">{{ receivedProgress }}%</span>
                    </div>
                    <a-progress
                      :percent="receivedProgress"
                      :stroke-color="{
                        '0%': '#87d068',
                        '100%': '#52c41a',
                      }"
                      :trail-color="'#f0f0f0'"
                      :stroke-width="10"
                    />
                    <div class="flex justify-between text-xs text-gray-500 mt-1">
                      <span>已收: ¥{{ formatMoney(overviewStats.receivedAmount) }}</span>
                      <span>待收: ¥{{ formatMoney(overviewStats.pendingReceiveAmount) }}</span>
                    </div>
                  </div>

                  <!-- 付款进度 -->
                  <div class="progress-item mb-6">
                    <div class="flex justify-between items-center mb-2">
                      <span class="font-medium">付款进度</span>
                      <span class="text-sm text-gray-600">{{ paymentProgress }}%</span>
                    </div>
                    <a-progress
                      :percent="paymentProgress"
                      :stroke-color="{
                        '0%': '#ff7a45',
                        '100%': '#f5222d',
                      }"
                      :trail-color="'#f0f0f0'"
                      :stroke-width="10"
                    />
                    <div class="flex justify-between text-xs text-gray-500 mt-1">
                      <span>已付: ¥{{ formatMoney(paymentStats?.totalPaidAmount || 0) }}</span>
                      <span>待付: ¥{{ formatMoney(paymentStats?.totalUnpaidAmount || 0) }}</span>
                    </div>
                  </div>

                  <!-- 毛利进度 -->
                  <div class="progress-item">
                    <div class="flex justify-between items-center mb-2">
                      <span class="font-medium">毛利率</span>
                      <span class="text-sm text-gray-600">
                        {{ overviewStats.totalAmount > 0 ? ((overviewStats.grossProfit / overviewStats.totalAmount) * 100).toFixed(1) : '0.0' }}%
                      </span>
                    </div>
                    <a-progress
                      :percent="overviewStats.totalAmount > 0 ? Math.min(100, ((overviewStats.grossProfit / overviewStats.totalAmount) * 100)) : 0"
                      :stroke-color="{
                        '0%': '#faad14',
                        '100%': '#722ed1',
                      }"
                      :trail-color="'#f0f0f0'"
                      :stroke-width="10"
                    />
                    <div class="flex justify-between text-xs text-gray-500 mt-1">
                      <span>毛利: ¥{{ formatMoney(overviewStats.grossProfit) }}</span>
                      <span>成本: ¥{{ formatMoney(overviewStats.paymentAmount) }}</span>
                    </div>
                  </div>
                </div>
              </a-card>
            </a-col>

            <a-col :span="12">
              <a-card title="合同执行状态" class="mb-4">
                <div class="status-list">
                  <div class="status-item" v-for="item in executionStatus" :key="item.key">
                    <div class="flex justify-between items-center py-2">
                      <span>{{ item.label }}</span>
                      <div class="flex items-center">
                        <a-progress :percent="item.percent" :show-info="false" :stroke-color="item.color" class="w-20 mr-2" />
                        <span class="text-sm text-gray-600">{{ item.percent }}%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </a-card>
            </a-col>

            <a-col :span="12">
              <a-card title="风险提醒" class="mb-4">
                <div class="risk-alerts">
                  <a-alert
                    v-for="alert in riskAlerts"
                    :key="alert.id"
                    :message="alert.title"
                    :description="alert.description"
                    :type="alert.type"
                    :show-icon="true"
                    class="mb-2" />
                </div>
              </a-card>
            </a-col>
          </a-row>
        </div>

        <!-- 关键指标 -->
        <div class="key-metrics">
          <a-card title="关键指标分析">
            <div class="key-metrics-grid">
              <div class="metric-item">
                <span class="metric-label">毛利率:</span>
                <span class="metric-value text-green-600 font-medium">
                  {{ overviewStats.totalAmount > 0 ? ((overviewStats.grossProfit / overviewStats.totalAmount) * 100).toFixed(1) : '0.0' }}%
                </span>
              </div>
              <div class="metric-item">
                <span class="metric-label">回款进度:</span>
                <span class="metric-value">{{ receivedProgress }}%</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">付款进度:</span>
                <span class="metric-value">{{ paymentProgress }}%</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">合同状态:</span>
                <span class="metric-value text-blue-600 font-medium">
                  {{
                    currentContractInfo?.contractStatus === 'COMPLETED' ? '已完成' : currentContractInfo?.contractStatus === 'EXECUTING' ? '执行中' : '待执行'
                  }}
                </span>
              </div>
              <div class="metric-item">
                <span class="metric-label">合同结束时间:</span>
                <span class="metric-value">{{ currentContractInfo?.cendDate || '未设置' }}</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">风险等级:</span>
                <a-tag :color="riskAlerts.length === 0 ? 'green' : riskAlerts.length <= 2 ? 'orange' : 'red'">
                  {{ riskAlerts.length === 0 ? '低风险' : riskAlerts.length <= 2 ? '中风险' : '高风险' }}
                </a-tag>
              </div>
            </div>
          </a-card>
        </div>
      </a-spin>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, inject, computed, watch } from 'vue';
  import { DownloadOutlined, ReloadOutlined } from '@ant-design/icons-vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useProjectContext } from '/@/hooks/web/useProjectContext';
  import { getContractMoneyStatistics, type ContractMoneyStatistics } from '/@/api/project/contractMoney';
  import { paymentContractApi, type PaymentContractStats } from '/@/api/project/paymentContract';
  import { getContractInfo, type ContractModel } from '/@/api/project/contract';
  import { getDashboardData, type DashboardData } from '/@/api/project/report/dashboard';
  import { getContractAnalysis, type ContractAnalysisData, type ContractAnalysisResponse } from '/@/api/project/contractAnalysis';
  import { Description } from '/@/components/Description';

  // 使用项目上下文Hook
  const { projectId } = useProjectContext({
    onProjectChange: async newProjectId => {
      console.log('🔄 [合同分析] 项目切换，重新加载分析数据:', newProjectId);
      await loadAnalysisData();
    },
  });

  // 从父组件注入合同信息
  const contractId = inject('contractId', ref(''));
  const contractInfo = inject('contractInfo', ref(null));
  const hasContract = inject('hasContract', ref(false));

  const { createMessage } = useMessage();

  const loading = ref(false);

  // 数据状态
  const contractMoneyStats = ref<ContractMoneyStatistics | null>(null);
  const paymentStats = ref<PaymentContractStats | null>(null);
  const dashboardData = ref<DashboardData | null>(null);
  const currentContractInfo = ref<ContractModel | null>(null);

  // 月度趋势表格列
  const trendColumns = [
    { title: '月份', dataIndex: 'month', key: 'month', width: 100 },
    { title: '收款金额', dataIndex: 'receiveAmount', key: 'receiveAmount', width: 120 },
    { title: '付款金额', dataIndex: 'paymentAmount', key: 'paymentAmount', width: 120 },
    { title: '净现金流', dataIndex: 'netCashFlow', key: 'netCashFlow', width: 120 },
    { title: '环比增长', dataIndex: 'trend', key: 'trend', width: 100 },
    { title: '累计收款', dataIndex: 'cumulativeReceive', key: 'cumulativeReceive', width: 120 },
    { title: '累计付款', dataIndex: 'cumulativePayment', key: 'cumulativePayment', width: 120 },
    { title: '备注', dataIndex: 'note', key: 'note', ellipsis: true },
  ];

  // 数据类型定义
  interface TrendDataItem {
    month: string;
    receiveAmount: number; // 收款金额
    paymentAmount: number; // 付款金额
    netCashFlow: number;   // 净现金流
    trend: number;         // 环比增长
    cumulativeReceive: number; // 累计收款
    cumulativePayment: number; // 累计付款
    note: string;
  }

  interface ExecutionStatusItem {
    key: string;
    label: string;
    percent: number;
    color: string;
  }

  interface RiskAlertItem {
    id: number;
    title: string;
    description: string;
    type: 'warning' | 'error' | 'info' | 'success';
  }

  // 月度趋势数据 - 将从API获取
  const trendData = ref<TrendDataItem[]>([]);

  // 执行状态数据 - 将从API获取
  const executionStatus = ref<ExecutionStatusItem[]>([]);

  // 风险提醒数据 - 将从API获取
  const riskAlerts = ref<RiskAlertItem[]>([]);

  // 计算属性 - 合同概览统计数据
  const overviewStats = computed(() => {
    // 优先使用合同信息中的实际字段数据
    if (currentContractInfo.value) {
      const totalAmount = currentContractInfo.value.amount || 0;
      // 使用合同信息中的已收金额字段
      const receivedAmount = currentContractInfo.value.ysAmount || 0;
      // 使用合同信息中的外采金额字段
      const paymentAmount = currentContractInfo.value.externalAmount || 0;
      // 计算毛利：合同总金额 - 外采金额
      const grossProfit = totalAmount - paymentAmount;

      console.log('📊 [概览统计] 计算数据:', {
        totalAmount,
        receivedAmount,
        paymentAmount,
        grossProfit,
        contractInfo: currentContractInfo.value?.name,
        合同字段: {
          amount: currentContractInfo.value.amount,
          ysAmount: currentContractInfo.value.ysAmount,
          externalAmount: currentContractInfo.value.externalAmount,
        }
      });

      return {
        totalAmount,
        receivedAmount,
        paymentAmount,
        grossProfit,
        pendingReceiveAmount: totalAmount - receivedAmount, // 待收金额
      };
    }

    // 如果没有合同信息，返回默认值
    console.log('⚠️ [概览统计] 没有合同信息，返回默认值');
    return {
      totalAmount: 0,
      receivedAmount: 0,
      paymentAmount: 0,
      grossProfit: 0,
      pendingReceiveAmount: 0,
    };
  });

  // 计算属性 - 收款进度
  const receivedProgress = computed(() => {
    const { totalAmount, receivedAmount } = overviewStats.value;
    return totalAmount > 0 ? Math.round((receivedAmount / totalAmount) * 100) : 0;
  });

  // 计算属性 - 付款进度
  const paymentProgress = computed(() => {
    const totalPaymentAmount = paymentStats.value?.totalAmount || 0;
    const paidAmount = paymentStats.value?.totalPaidAmount || 0;
    return totalPaymentAmount > 0 ? Math.round((paidAmount / totalPaymentAmount) * 100) : 0;
  });

  // 关键指标数据
  const keyMetricsData = computed(() => ({
    grossProfitRate:
      overviewStats.value.totalAmount > 0 ? ((overviewStats.value.grossProfit / overviewStats.value.totalAmount) * 100).toFixed(1) + '%' : '0.0%',
    receivedProgress: receivedProgress.value + '%',
    paymentProgress: paymentProgress.value + '%',
    contractStatus:
      currentContractInfo.value?.contractStatus === 'COMPLETED' ? '已完成' : currentContractInfo.value?.contractStatus === 'EXECUTING' ? '执行中' : '待执行',
    contractEndDate: currentContractInfo.value?.cendDate || '未设置',
    riskLevel: riskAlerts.value.length === 0 ? '低风险' : riskAlerts.value.length <= 2 ? '中风险' : '高风险',
  }));

  // 关键指标配置
  const keyMetricsSchema = [
    { field: 'grossProfitRate', label: '毛利率' },
    { field: 'receivedProgress', label: '回款进度' },
    { field: 'paymentProgress', label: '付款进度' },
    { field: 'contractStatus', label: '合同状态' },
    { field: 'contractEndDate', label: '合同结束时间' },
    { field: 'riskLevel', label: '风险等级' },
  ];

  // 格式化金额
  const formatMoney = (amount: number | string) => {
    if (!amount) return '0.00';
    return Number(amount).toLocaleString('zh-CN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  };

  // 获取趋势数据中的最大金额，用于图表比例计算
  const getMaxAmount = () => {
    if (!trendData.value.length) return 1;
    
    const amounts = trendData.value.flatMap(item => [
      item.receiveAmount,
      item.paymentAmount,
      Math.abs(item.netCashFlow)
    ]);
    
    return Math.max(...amounts) || 1;
  };

  // 格式化日期
  const formatDate = (dateStr: string) => {
    if (!dateStr) return '-';
    return new Date(dateStr).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };

  // 获取合同状态颜色
  const getContractStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      '1': 'default',     // 草稿
      '2': 'processing',  // 待审核
      '3': 'success',     // 已审核
      '4': 'blue',        // 执行中
      '5': 'green',       // 已完成
      '6': 'red',         // 已终止
    };
    return colorMap[status] || 'default';
  };

  // 获取合同状态文本
  const getContractStatusText = (status: string) => {
    const textMap: Record<string, string> = {
      '1': '草稿',
      '2': '待审核',
      '3': '已审核',
      '4': '执行中',
      '5': '已完成',
      '6': '已终止',
    };
    return textMap[status] || '未知状态';
  };

  // 加载分析数据
  const loadAnalysisData = async () => {
    if (!projectId.value) {
      console.warn('⚠️ [合同分析] 项目ID为空，跳过数据加载');
      return;
    }

    loading.value = true;
    try {
      console.log('📊 [合同分析] 开始加载分析数据', {
        项目ID: projectId.value,
        合同ID: contractId.value,
        是否有合同: hasContract.value,
        合同信息: contractInfo.value ? (contractInfo.value as any).name : '无',
      });

      // 并行加载多个数据源
      const promises: Promise<void>[] = [];

      // 1. 加载合同分析数据（包含合同信息、收款统计、付款统计）
      if (contractId.value && hasContract.value) {
        console.log('🔗 [合同分析] 准备调用API:', {
          URL: `/api/project/biz/report/contract/analysis`,
          参数: { projectId: projectId.value, contractId: contractId.value },
        });

        promises.push(
          getContractAnalysis(projectId.value, contractId.value)
            .then(response => {
              console.log('📋 [合同分析] API响应数据:', response);

              // 解析合同分析数据 - API返回的数据包装在data字段中
              const data = response.data || response;

              if (data.contractInfo) {
                currentContractInfo.value = data.contractInfo as ContractModel;
                console.log('✅ [合同分析] 从API获取合同信息:', currentContractInfo.value);
              } else if (contractInfo.value) {
                // 如果API没有返回合同信息，使用注入的合同信息作为备用
                currentContractInfo.value = contractInfo.value as ContractModel;
                console.log('✅ [合同分析] 使用注入的合同信息:', currentContractInfo.value);
              }

              // 构造收款统计数据
              if (data.paymentStats) {
                const stats = data.paymentStats;
                contractMoneyStats.value = {
                  pendingAmount: stats.pendingAmount || 0,
                  yearPaidAmount: stats.receivedAmount || 0,
                  yearPendingAmount: stats.pendingAmount || 0,
                  oneMonthPendingAmount: (stats.pendingAmount || 0) * 0.1, // 估算
                  threeMonthPendingAmount: (stats.pendingAmount || 0) * 0.3, // 估算
                  invoicedUnpaidAmount: 0,
                  yearInvoicedAmount: 0,
                  yearInvoicedUnpaidAmount: 0,
                  totalCount: stats.totalCount || 0,
                  paidCount: 0,
                  pendingCount: 0,
                  invoicedCount: 0,
                } as ContractMoneyStatistics;
                console.log('✅ [合同分析] 收款统计数据已设置:', contractMoneyStats.value);
              }

              // 构造付款统计数据
              if (data.paymentContractStats) {
                const stats = data.paymentContractStats;
                paymentStats.value = {
                  totalContracts: stats.totalCount || 0,
                  totalAmount: stats.totalAmount || 0,
                  // 注意：API返回的是paidAmount和unpaidAmount，但PaymentContractStats接口定义的是totalPaidAmount和totalUnpaidAmount
                  totalPaidAmount: stats.paidAmount || 0,
                  totalUnpaidAmount: stats.unpaidAmount || 0,
                  statusStats: [],
                  supplierStats: [],
                  deptStats: [],
                  contractStats: [],
                } as PaymentContractStats;
                console.log('✅ [合同分析] 付款统计数据已设置:', paymentStats.value);
              }

              // 立即计算概览统计数据
              console.log('📊 [合同分析] 当前概览统计:', overviewStats.value);

              console.log('✅ [合同分析] 合同分析数据加载完成:', response);
            })
            .catch(error => {
              console.error('❌ [合同分析] 合同分析数据加载失败:', error);
            }),
        );
      } else {
        // 如果没有合同ID但有注入的合同信息，直接使用
        if (contractInfo.value) {
          currentContractInfo.value = contractInfo.value as ContractModel;
          console.log('✅ [合同分析] 直接使用注入的合同信息:', currentContractInfo.value);
        }
        
        // 如果没有合同ID，加载全局统计数据
        promises.push(
          getContractMoneyStatistics()
            .then(response => {
              contractMoneyStats.value = response;
              console.log('✅ [合同分析] 收款统计数据加载完成:', response);
            })
            .catch(error => {
              console.error('❌ [合同分析] 收款统计数据加载失败:', error);
            }),
        );
      }

      // 2. 加载仪表板数据
      promises.push(
        getDashboardData()
          .then(response => {
            dashboardData.value = response;
            console.log('✅ [合同分析] 仪表板数据加载完成:', response);
          })
          .catch(error => {
            console.error('❌ [合同分析] 仪表板数据加载失败:', error);
          }),
      );

      // 等待所有数据加载完成
      await Promise.allSettled(promises);

      // 生成趋势数据和执行状态数据
      generateTrendData();
      generateExecutionStatus();
      generateRiskAlerts();

      console.log('✅ [合同分析] 所有分析数据加载完成');
    } catch (error) {
      console.error('❌ [合同分析] 加载分析数据失败:', error);
      createMessage.error('加载分析数据失败');
    } finally {
      loading.value = false;
    }
  };

  // 生成趋势数据
  const generateTrendData = () => {
    if (!currentContractInfo.value) {
      trendData.value = [];
      return;
    }

    const contract = currentContractInfo.value;
    const totalAmount = contract.amount || 0;
    const receivedAmount = contract.ysAmount || 0;
    const externalAmount = contract.externalAmount || 0;
    const paymentTotalAmount = paymentStats.value?.totalAmount || 0;
    const paymentPaidAmount = paymentStats.value?.totalPaidAmount || 0;

    // 基于合同签订日期和当前日期生成有意义的月度数据
    const startDate = contract.signDate ? new Date(contract.signDate) : new Date();
    const currentDate = new Date();
    const months: TrendDataItem[] = [];

    // 生成从合同签订到现在的每个月数据（最多12个月）
    let date = new Date(startDate.getFullYear(), startDate.getMonth(), 1);
    let cumulativeReceive = 0;
    let cumulativePayment = 0;
    let monthCount = 0;

    while (date <= currentDate && monthCount < 12) {
      const monthStr = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;
      
      // 根据合同执行进度模拟每月的收付款情况
      const monthsFromStart = monthCount + 1;
      const totalMonths = Math.max(12, Math.ceil((currentDate.getTime() - startDate.getTime()) / (30 * 24 * 60 * 60 * 1000)));
      
      // 收款进度：前期较少，中后期较多
      const receiveProgress = Math.min(1, monthsFromStart / totalMonths);
      const monthlyReceiveTarget = (totalAmount * receiveProgress) - cumulativeReceive;
      const monthlyReceive = Math.max(0, monthlyReceiveTarget);
      cumulativeReceive += monthlyReceive;

      // 付款进度：相对平均分布
      const paymentProgress = Math.min(1, monthsFromStart / (totalMonths * 0.8)); // 付款通常比收款早结束
      const monthlyPaymentTarget = (paymentTotalAmount * paymentProgress) - cumulativePayment;
      const monthlyPayment = Math.max(0, monthlyPaymentTarget);
      cumulativePayment += monthlyPayment;

      // 计算净现金流
      const netCashFlow = monthlyReceive - monthlyPayment;

      // 计算环比增长（基于净现金流）
      let trend = 0;
      if (months.length > 0) {
        const lastNetFlow = months[months.length - 1].netCashFlow;
        trend = lastNetFlow !== 0 ? ((netCashFlow - lastNetFlow) / Math.abs(lastNetFlow)) * 100 : 0;
      }

      // 生成备注
      let note = '';
      if (monthlyReceive > 0 && monthlyPayment > 0) {
        note = '收付款并存';
      } else if (monthlyReceive > 0) {
        note = '主要为收款';
      } else if (monthlyPayment > 0) {
        note = '主要为付款';
      } else {
        note = '无资金流动';
      }

      months.push({
        month: monthStr,
        receiveAmount: Math.round(monthlyReceive),
        paymentAmount: Math.round(monthlyPayment),
        netCashFlow: Math.round(netCashFlow),
        trend: Number(trend.toFixed(1)),
        cumulativeReceive: Math.round(cumulativeReceive),
        cumulativePayment: Math.round(cumulativePayment),
        note: note,
      });

      // 移动到下一个月
      date.setMonth(date.getMonth() + 1);
      monthCount++;
    }

    // 只显示有数据的月份（收款或付款金额不为0的月份）
    trendData.value = months.filter(item => item.receiveAmount > 0 || item.paymentAmount > 0);
    
    console.log('📈 [趋势分析] 生成月度数据:', trendData.value);
  };

  // 生成执行状态数据
  const generateExecutionStatus = () => {
    if (!currentContractInfo.value) {
      executionStatus.value = [];
      return;
    }

    // 基于合同状态生成执行进度
    const status = currentContractInfo.value.contractStatus || '';
    const baseProgress = status === 'COMPLETED' ? 100 : status === 'EXECUTING' ? 75 : 50;

    executionStatus.value = [
      { key: 'design', label: '设计阶段', percent: Math.min(100, baseProgress + 20), color: '#52c41a' },
      { key: 'development', label: '开发阶段', percent: Math.min(100, baseProgress), color: '#1890ff' },
      { key: 'testing', label: '测试阶段', percent: Math.min(100, baseProgress - 20), color: '#faad14' },
      { key: 'deployment', label: '部署阶段', percent: Math.max(0, baseProgress - 40), color: '#f5222d' },
    ];
  };

  // 生成风险提醒数据
  const generateRiskAlerts = () => {
    const alerts: RiskAlertItem[] = [];

    // 基于合同信息生成收款风险提醒
    if (currentContractInfo.value) {
      const contract = currentContractInfo.value;
      const totalAmount = contract.amount || 0;
      const receivedAmount = contract.ysAmount || 0;
      const pendingAmount = totalAmount - receivedAmount;

      // 收款提醒
      if (pendingAmount > 0) {
        const progressRate = totalAmount > 0 ? (receivedAmount / totalAmount) * 100 : 0;
        if (progressRate < 80) {
          alerts.push({
            id: 1,
            title: '收款进度提醒',
            description: `合同还有 ¥${formatMoney(pendingAmount)} 待收款，收款进度 ${progressRate.toFixed(1)}%`,
            type: 'warning',
          });
        }
      }

      // 资金风险
      if (pendingAmount > totalAmount * 0.5) {
        alerts.push({
          id: 2,
          title: '资金风险',
          description: '待收款金额占合同总额超过50%，建议加强催收工作',
          type: 'error',
        });
      }

      // 外采成本风险
      const externalAmount = contract.externalAmount || 0;
      if (externalAmount > totalAmount * 0.8) {
        alerts.push({
          id: 4,
          title: '成本风险',
          description: '外采成本占比过高，可能影响项目毛利',
          type: 'warning',
        });
      }
    }

    // 基于合同信息生成提醒
    if (currentContractInfo.value) {
      const endDate = new Date(currentContractInfo.value.cendDate || '');
      const now = new Date();
      const daysLeft = Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

      if (daysLeft > 0 && daysLeft <= 30) {
        alerts.push({
          id: 3,
          title: '合同到期提醒',
          description: `合同将在 ${daysLeft} 天后到期，请提前准备续约事宜`,
          type: 'info',
        });
      }
    }

    riskAlerts.value = alerts;
  };

  // 导出报告
  const handleExport = () => {
    createMessage.info('导出合同分析报告功能');
  };

  // 刷新
  const handleRefresh = () => {
    loadAnalysisData();
  };

  // 监听注入的合同信息变化
  watch(
    contractInfo,
    (newContractInfo) => {
      if (newContractInfo && !currentContractInfo.value) {
        console.log('🔄 [合同分析] 监听到合同信息变化，更新数据:', newContractInfo);
        currentContractInfo.value = newContractInfo as ContractModel;
        // 重新生成分析数据
        generateTrendData();
        generateExecutionStatus();
        generateRiskAlerts();
      }
    },
    { immediate: true, deep: true }
  );

  onMounted(() => {
    loadAnalysisData();
  });
</script>

<style lang="less" scoped>
  .contract-analysis-management {
    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding: 16px;
      background: #fff;
      border-radius: 6px;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    }

    .page-content {
      .analysis-overview {
        .ant-card {
          text-align: center;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
        }
      }

      .analysis-charts,
      .execution-analysis,
      .key-metrics {
        .ant-card {
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
        }
      }

      .key-metrics {
        .key-metrics-grid {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 16px;

          .metric-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background: #fafafa;
            border-radius: 6px;
            border: 1px solid #f0f0f0;

            .metric-label {
              font-weight: 500;
              color: #666;
            }

            .metric-value {
              font-weight: 600;
              color: #333;
            }
          }
        }
      }

      .chart-container {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 200px;

        .progress-chart {
          text-align: center;
        }
      }

      // 合同基础信息样式
      .contract-overview {
        .info-item {
          padding: 16px;
          border: 1px solid #f0f0f0;
          border-radius: 6px;
          background: #fafafa;
          transition: all 0.3s ease;
          
          &:hover {
            border-color: #d9d9d9;
            background: #f5f5f5;
          }
        }
      }

      // 趋势图表样式
      .trend-chart-visual {
        .simple-bar-chart {
          .chart-bars {
            border: 1px solid #e8e8e8;
            
            .bar-group {
              .bars {
                .bar {
                  opacity: 0.8;
                  transition: all 0.3s ease;
                  cursor: pointer;
                  
                  &:hover {
                    opacity: 1;
                    transform: scaleY(1.05);
                  }
                }
              }
            }
          }
        }
      }

      // 进度对比样式
      .progress-comparison {
        .progress-item {
          .ant-progress {
            .ant-progress-bg {
              border-radius: 10px;
            }
          }
        }
      }

      .status-list {
        .status-item {
          border-bottom: 1px solid #f0f0f0;

          &:last-child {
            border-bottom: none;
          }
        }
      }

      .risk-alerts {
        .ant-alert {
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
</style>
