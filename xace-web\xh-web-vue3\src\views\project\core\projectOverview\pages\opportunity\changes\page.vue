<template>
  <div class="opportunity-changes p-4">
    <div class="page-header mb-4">
      <h2 class="text-xl font-semibold mb-2">商机变更</h2>
      <p class="text-gray-600">管理商机的变更记录和审批流程</p>
    </div>

    <a-result status="info" title="商机变更页面" sub-title="该功能正在开发中，敬请期待...">
      <template #icon>
        <i class="icon-ym icon-ym-change text-6xl text-orange-500"></i>
      </template>
      <template #extra>
        <a-space>
          <a-button type="primary"> 查看变更记录 </a-button>
          <a-button> 返回概览 </a-button>
        </a-space>
      </template>
    </a-result>
  </div>
</template>

<script lang="ts" setup>
  const props = defineProps<{
    projectId?: string;
  }>();

  console.log('商机变更页面 - 项目ID:', props.projectId);
</script>

<style lang="less" scoped>
  .opportunity-changes {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
</style>
