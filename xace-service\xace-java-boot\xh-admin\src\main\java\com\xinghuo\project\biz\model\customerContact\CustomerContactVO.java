package com.xinghuo.project.biz.model.customerContact;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 客户联系人视图对象
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@Schema(description = "客户联系人视图对象")
public class CustomerContactVO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private String id;

    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    private String cuId;

    /**
     * 客户单位名称（关联查询字段）
     */
    @Schema(description = "客户单位名称")
    private String customerName;

    /**
     * 联系人姓名
     */
    @Schema(description = "联系人姓名")
    private String linkman;

    /**
     * 联系电话
     */
    @Schema(description = "联系电话")
    private String telephone;

    /**
     * 状态 1-有效，0-无效
     */
    @Schema(description = "状态")
    private Integer status;

    /**
     * 状态名称
     */
    @Schema(description = "状态名称")
    private String statusName;

    /**
     * 岗位/主题
     */
    @Schema(description = "岗位")
    private String topic;

    /**
     * 内容
     */
    @Schema(description = "内容")
    private String content;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 创建用户ID
     */
    @Schema(description = "创建用户ID")
    private String creatorUserId;

    /**
     * 创建用户名称
     */
    @Schema(description = "创建用户名称")
    private String creatorUserName;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date creatorTime;

    /**
     * 最后修改用户ID
     */
    @Schema(description = "最后修改用户ID")
    private String lastModifyUserId;

    /**
     * 最后修改用户名称
     */
    @Schema(description = "最后修改用户名称")
    private String lastModifyUserName;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private Date lastModifyTime;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private String tenantId;

    /**
     * 流程ID
     */
    @Schema(description = "流程ID")
    private String flowId;
}
