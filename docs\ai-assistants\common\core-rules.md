# XACE 核心开发规则 - AI工具必读

## 🚨 关键约定

### Jakarta EE（必须）
```java
// ✅ 正确
import jakarta.validation.constraints.NotNull;
import jakarta.servlet.http.HttpServletRequest;

// ❌ 错误 - 会导致编译失败
import javax.validation.constraints.NotNull;
import javax.servlet.http.HttpServletRequest;
```

### API响应格式（必须）
```typescript
// 后端统一返回格式
interface ActionResult<T> {
  code: number;     // 200=成功，其他=失败
  msg: string;      // 消息
  data: T;          // 业务数据
}

// ✅ 正确处理
const response = await getUser(id);
if (response.code === 200) {
  user.value = response.data;
}

// ❌ 错误 - 直接使用response
user.value = response; // 错误！
```

### 实体类基类（必须）
```java
// ✅ 使用BaseEntityV2
public class UserEntity extends BaseEntityV2.CUDBaseEntityV2<String> {
    @TableField("F_ACCOUNT")
    private String account;
}

// 字段映射：
// createdAt (不是createTime)
// lastUpdatedAt (不是updateTime)
// deleteMark (Integer: 0=未删除, 1=已删除)
```

### Mapper继承（必须）
```java
// ✅ 正确
@Mapper
public interface UserMapper extends XHBaseMapper<UserEntity> {}

// ❌ 错误
public interface UserMapper extends BaseMapper<UserEntity> {}
```

### Vue组件数据格式（重要）
```typescript
// ✅ XACE组件使用 fullName/id
const options = [
  { id: '1', fullName: '选项一' },
  { id: '2', fullName: '选项二' }
];

// ❌ 需要转换
const options = [
  { value: '1', label: '选项一' },
  { value: '2', label: '选项二' }
];
```

## 📁 关键路径

### 重要导入
```java
// 分页
import com.xinghuo.common.base.model.Pagination;

// 响应
import com.xinghuo.common.base.ActionResult;

// 基础服务
import com.xinghuo.common.base.service.BaseService;

// 工具类
import com.xinghuo.common.util.core.BeanCopierUtils;
```

### 项目结构
```
xace-service/
├── xace-common/           # 公共模块
├── xace-java-boot/        # 主应用
│   ├── xh-admin/          # 主应用入口
│   ├── xh-system/         # 系统管理
│   └── xh-*/              # 其他模块

# 模型类包结构
com.xinghuo.[模块名].model.[业务功能]
├── UserVO                 # 视图对象 (返回给前端)
├── UserForm              # 表单对象 (推荐合并设计)
│   ├── UserCrForm        # 创建表单 (仅在验证差异大时分离)
│   └── UserUpForm        # 更新表单 (仅在验证差异大时分离)
└── UserPagination        # 分页查询对象
```

## 🔧 常用命令

### 后端构建
```bash
mvn clean package -DskipTests
cd xace-service/xace-java-boot/xh-admin && mvn spring-boot:run
```

### 前端开发
```bash
cd xace-web/xh-web-vue3
pnpm install
pnpm dev
pnpm type:check
```

## ⚠️ 常见错误

1. **BaseEntityV2字段错误**：使用 `getCreatedAt()` 不是 `getCreateTime()`
2. **API响应未检查code**：必须检查 `response.code === 200`
3. **分页数据错误**：使用 `response.data.list` 不是 `response.data`
4. **逻辑删除查询**：使用 `eq(Entity::getDeleteMark, 0)`
5. **日期格式化**：使用 `formatToDate()` 不是 `formatDate()`

## 🎯 AI生成代码检查清单

### 后端代码
- [ ] 使用Jakarta EE导入 (jakarta.* 不是 javax.*)
- [ ] Controller返回ActionResult
- [ ] 实体继承BaseEntityV2
- [ ] Mapper继承XHBaseMapper
- [ ] 模型类使用VO/Form/Pagination (推荐Form合并设计)
- [ ] 包结构为com.xinghuo.[模块名].model.[业务功能]
- [ ] 包含完整的import路径

### 前端代码
- [ ] 前端检查response.code
- [ ] Vue组件使用fullName/id格式