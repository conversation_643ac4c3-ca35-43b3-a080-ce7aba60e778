<template>
  <BasicDrawer
    v-bind="$attrs"
    @register="registerDrawer"
    :title="getTitle"
    width="90%"
    showFooter
    :canFullscreen="true"
    :maskClosable="false"
    :keyboard="false"
    class="contract-detail-drawer">
    <template #footer>
      <a-space>
        <a-button v-if="hasBusinessPermission" type="primary" @click="handleEdit">
          <template #icon><EditOutlined /></template>
          编辑合同
        </a-button>
        <a-button @click="closeDrawer">
          <template #icon><CloseOutlined /></template>
          关闭
        </a-button>
      </a-space>
    </template>

    <div class="contract-detail-container">
      <a-spin :spinning="loading" tip="加载中...">
        <!-- 合同概览卡片 -->
        <a-card :bordered="false" class="overview-card mb-4">
          <template #title>
            <div class="card-title">
              <FileTextOutlined class="title-icon" />
              合同概览 【{{ contractInfo?.cno || '-' }}】{{ contractInfo?.name || '-' }}
            </div>
          </template>
          <template #extra>
            <a-space>
              <a-tag :color="getContractStatusColor(contractInfo?.contractStatus)" v-if="contractInfo?.contractStatus">
                {{ getContractStatusText(contractInfo.contractStatus) }}
              </a-tag>
              <a-tag :color="getMoneyStatusColor(contractInfo?.moneyStatus)" v-if="contractInfo?.moneyStatus">
                {{ getMoneyStatusText(contractInfo.moneyStatus) }}
              </a-tag>
            </a-space>
          </template>

          <div class="overview-content">
            <!-- 金额信息 -->
            <div class="overview-item">
              <div class="overview-label">
                <DollarCircleOutlined class="label-icon" />
                合同金额
              </div>
              <div class="overview-value amount">{{ formatAmount(contractInfo?.amount) }}</div>
            </div>
            <div class="overview-item">
              <div class="overview-label">
                <MoneyCollectOutlined class="label-icon" />
                已收金额
              </div>
              <div class="overview-value amount received">{{ formatAmount(contractInfo?.ysAmount) }}</div>
            </div>

            <!-- 收款进度 -->
            <div class="overview-item progress-item">
              <div class="overview-label">
                <BarChartOutlined class="label-icon" />
                收款进度
              </div>
              <div class="overview-value">
                <div class="progress-wrapper">
                  <a-progress :percent="getPaymentProgress()" :stroke-color="getProgressColor(getPaymentProgress())" :show-info="true" :stroke-width="8" />
                  <div class="progress-detail"> 已收 {{ formatAmount(contractInfo?.ysAmount) }} / 总计 {{ formatAmount(contractInfo?.amount) }} </div>
                </div>
              </div>
            </div>
          </div>
        </a-card>

        <a-tabs v-model:activeKey="activeTab" type="card" class="contract-detail-tabs" :tabBarGutter="8" :key="`tabs-${contractId}`">
          <!-- 基本信息标签页 -->
          <a-tab-pane key="basic" class="tab-pane-content">
            <template #tab>
              <span class="tab-title">
                <InfoCircleOutlined class="tab-icon" />
                基本信息
              </span>
            </template>
            <a-card :bordered="false" class="detail-card">
              <template #title>
                <div class="card-title">
                  <UserOutlined class="title-icon" />
                  合同基础信息
                </div>
              </template>
              <div class="detail-grid">
                <div
                  class="detail-item"
                  :class="{
                    'description-field': item.field === '备注' || item.field === '说明',
                  }"
                  v-for="item in basicData"
                  :key="item.field">
                  <div class="detail-label">
                    <!-- 去掉合同名称的图标判断 -->
                    <TeamOutlined class="label-icon" v-if="item.field === '甲方' || item.field === '最终用户'" />
                    <ContactsOutlined class="label-icon" v-else-if="item.field === '项目负责人'" />
                    <CodeOutlined class="label-icon" v-else-if="item.field === '合同财务编号'" />
                    {{ item.label }}
                  </div>
                  <div
                    class="detail-value"
                    :class="{
                      important: item.field === '项目负责人',
                      description: item.field === '备注' || item.field === '说明',
                    }">
                    {{ item.value }}
                  </div>
                </div>
              </div>
            </a-card>
          </a-tab-pane>

          <!-- 商务信息标签页 -->
          <a-tab-pane key="business" class="tab-pane-content">
            <template #tab>
              <span class="tab-title">
                <DollarOutlined class="tab-icon" />
                商务信息
              </span>
            </template>
            <a-card :bordered="false" class="detail-card">
              <template #title>
                <div class="card-title">
                  <DollarCircleOutlined class="title-icon" />
                  商务财务信息
                </div>
              </template>
              <div class="detail-grid">
                <div class="detail-item" v-for="item in businessData" :key="item.field">
                  <div class="detail-label">
                    <DollarCircleOutlined class="label-icon" v-if="item.isAmount" />
                    <InfoCircleOutlined class="label-icon" v-else-if="item.field === '合同类型' || item.field === '合同状态'" />
                    <BarChartOutlined class="label-icon" v-else-if="item.field.includes('毛利')" />
                    {{ item.label }}
                  </div>
                  <div
                    class="detail-value"
                    :class="{
                      amount: item.isAmount,
                      status: item.field === '合同状态' || item.field === '收款状态' || item.field === '是否外采',
                    }">
                    {{ item.value }}
                  </div>
                </div>
              </div>
            </a-card>
          </a-tab-pane>

          <!-- 交付信息标签页 -->
          <a-tab-pane key="delivery" class="tab-pane-content">
            <template #tab>
              <span class="tab-title">
                <CalendarOutlined class="tab-icon" />
                交付信息
              </span>
            </template>
            <a-card :bordered="false" class="detail-card">
              <template #title>
                <div class="card-title">
                  <ScheduleOutlined class="title-icon" />
                  项目交付时间
                </div>
              </template>
              <template #extra>
                <a-button type="primary" size="small" @click="handleUpdateDate">
                  <template #icon><EditOutlined /></template>
                  更新日期
                </a-button>
              </template>
              <div class="detail-grid">
                <div class="detail-item" v-for="item in deliveryData" :key="item.field">
                  <div class="detail-label">
                    <CalendarOutlined class="label-icon" v-if="item.isDate" />
                    <ScheduleOutlined class="label-icon" v-else />
                    {{ item.label }}
                  </div>
                  <div class="detail-value" :class="{ date: item.isDate }">{{ item.value }}</div>
                </div>
              </div>
            </a-card>
          </a-tab-pane>

          <!-- 研发信息标签页 -->
          <a-tab-pane key="development" class="tab-pane-content">
            <template #tab>
              <span class="tab-title">
                <CodeOutlined class="tab-icon" />
                研发信息
              </span>
            </template>
            <a-card :bordered="false" class="detail-card">
              <template #title>
                <div class="card-title">
                  <ToolOutlined class="title-icon" />
                  研发联系信息
                </div>
              </template>
              <div class="detail-grid">
                <div class="detail-item" v-for="item in developmentData" :key="item.field">
                  <div class="detail-label">
                    <ContactsOutlined class="label-icon" v-if="item.field.includes('联系人')" />
                    <UserOutlined class="label-icon" v-else-if="item.field.includes('负责人')" />
                    <ToolOutlined class="label-icon" v-else />
                    {{ item.label }}
                  </div>
                  <div class="detail-value">{{ item.value }}</div>
                </div>
              </div>
            </a-card>
          </a-tab-pane>

          <!-- 外部单位信息标签页 -->
          <a-tab-pane key="partners" class="tab-pane-content">
            <template #tab>
              <span class="tab-title">
                <TeamOutlined class="tab-icon" />
                外部单位
              </span>
            </template>
            <a-card :bordered="false" class="detail-card">
              <template #title>
                <div class="card-title">
                  <ContactsOutlined class="title-icon" />
                  外部单位及干系人信息
                </div>
              </template>
              <div class="detail-grid">
                <div class="detail-item" v-for="item in partnersData" :key="item.field">
                  <div class="detail-label">
                    <TeamOutlined class="label-icon" v-if="item.field.includes('单位')" />
                    <ContactsOutlined class="label-icon" v-else-if="item.field.includes('联系人')" />
                    <UserOutlined class="label-icon" v-else />
                    {{ item.label }}
                  </div>
                  <div class="detail-value">{{ item.value }}</div>
                </div>
              </div>
            </a-card>
          </a-tab-pane>

          <!-- 收款管理标签页 -->
          <a-tab-pane key="money" class="tab-pane-content">
            <template #tab>
              <span class="tab-title">
                <MoneyCollectOutlined class="tab-icon" />
                收款管理
              </span>
            </template>
            <MoneyList v-if="contractId && !loading" :key="`money-${contractId}`" :contractId="contractId" :contractAmount="contractInfo?.amount" />
            <div v-else-if="loading" class="loading-placeholder">
              <a-spin tip="加载中..." />
            </div>
            <div v-else class="empty-placeholder">
              <a-empty description="请先选择合同" />
            </div>
          </a-tab-pane>

          <!-- 采购合同标签页 -->
          <a-tab-pane key="paycontract" class="tab-pane-content">
            <template #tab>
              <span class="tab-title">
                <ShoppingCartOutlined class="tab-icon" />
                采购合同
              </span>
            </template>
            <PaycontractList v-if="contractId && !loading" :key="`paycontract-${contractId}`" :contractId="contractId" />
            <div v-else-if="loading" class="loading-placeholder">
              <a-spin tip="加载中..." />
            </div>
            <div v-else class="empty-placeholder">
              <a-empty description="请先选择合同" />
            </div>
          </a-tab-pane>

          <!-- 项目成员标签页 -->
          <a-tab-pane key="members" class="tab-pane-content">
            <template #tab>
              <span class="tab-title">
                <TeamOutlined class="tab-icon" />
                项目成员
              </span>
            </template>
            <ContractMemberList
              v-if="contractId && !loading"
              :key="`members-${contractId}`"
              :contractId="contractId"
              :hasManagePermission="hasManagePermission" />
            <div v-else-if="loading" class="loading-placeholder">
              <a-spin tip="加载中..." />
            </div>
            <div v-else class="empty-placeholder">
              <a-empty description="请先选择合同" />
            </div>
          </a-tab-pane>
        </a-tabs>
      </a-spin>
    </div>

    <DateUpdateForm @register="registerDateForm" @reload="loadContractInfo" />
  </BasicDrawer>
</template>

<script lang="ts" setup>
  import { ref, computed, watch, nextTick } from 'vue';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { useModal } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { getContractInfo, ContractModel } from '/@/api/project/contract';
  import DateUpdateForm from './DateUpdateForm.vue';
  import MoneyList from './money/List.vue';
  import PaycontractList from './PaycontractList.vue';
  import ContractMemberList from './components/ContractMemberList.vue';
  import { translateDictFields } from '/@/utils/dict';
  import {
    FileTextOutlined,
    InfoCircleOutlined,
    DollarOutlined,
    CalendarOutlined,
    CodeOutlined,
    TeamOutlined,
    UserOutlined,
    DollarCircleOutlined,
    ScheduleOutlined,
    ToolOutlined,
    ContactsOutlined,
    MoneyCollectOutlined,
    ShoppingCartOutlined,
    EditOutlined,
    CloseOutlined,
    BarChartOutlined,
  } from '@ant-design/icons-vue';

  const emit = defineEmits(['register', 'reload', 'edit']);
  const { createMessage } = useMessage();
  const [registerDateForm, { openModal: openDateModal }] = useModal();

  const contractId = ref('');
  const contractInfo = ref<ContractModel | null>(null);
  const activeTab = ref('basic');
  const loading = ref(false);

  // 判断用户是否有商务权限
  const hasBusinessPermission = computed(() => {
    // 这里根据实际权限判断逻辑修改
    // 简化处理，假设所有用户都有权限
    return true;
  });

  // 判断用户是否有成员管理权限
  const hasManagePermission = computed(() => {
    // 这里根据实际权限判断逻辑修改
    // 合同管理员和PMO有管理权限
    // 简化处理，假设所有用户都有权限
    return true;
  });

  // 合同类型映射
  const typeStatusMap = {
    '1': { text: '新签', color: 'blue' },
    '2': { text: '续签', color: 'green' },
    '3': { text: '变更', color: 'orange' },
  };

  // 合同状态映射
  const contractStatusMap = {
    '1': { text: '草稿', color: 'default' },
    '2': { text: '执行中', color: 'processing' },
    '3': { text: '已完成', color: 'success' },
    '4': { text: '已终止', color: 'error' },
    '5': { text: '已归档', color: 'warning' },
  };

  // 收款状态映射
  const moneyStatusMap = {
    '0': { text: '未收款', color: 'red' },
    '1': { text: '部分收款', color: 'orange' },
    '2': { text: '已结清', color: 'green' },
  };

  // 是否外采映射
  const externalStatusMap = {
    '0': { text: '否', color: 'default' },
    '1': { text: '是', color: 'blue' },
  };

  // 格式化金额
  function formatAmount(amount?: number) {
    return amount ? amount.toLocaleString('zh-CN', { style: 'currency', currency: 'CNY' }) : '¥0.00';
  }

  // 获取合同状态颜色
  function getContractStatusColor(status: string) {
    return contractStatusMap[status]?.color || 'default';
  }

  // 获取合同状态文本
  function getContractStatusText(status: string) {
    return contractStatusMap[status]?.text || status || '-';
  }

  // 获取收款状态颜色
  function getMoneyStatusColor(status: string) {
    return moneyStatusMap[status]?.color || 'default';
  }

  // 获取收款状态文本
  function getMoneyStatusText(status: string) {
    return moneyStatusMap[status]?.text || status || '-';
  }

  // 计算收款进度
  function getPaymentProgress() {
    if (!contractInfo.value?.amount || !contractInfo.value?.ysAmount) return 0;
    const progress = (contractInfo.value.ysAmount / contractInfo.value.amount) * 100;
    return Math.min(Math.round(progress), 100);
  }

  // 获取进度条颜色
  function getProgressColor(percent: number) {
    if (percent >= 100) return '#52c41a';
    if (percent >= 80) return '#1890ff';
    if (percent >= 50) return '#faad14';
    return '#ff4d4f';
  }

  // 获取标题
  const getTitle = computed(() => {
    return contractInfo.value?.name ? `合同详情 - ${contractInfo.value.name}` : '合同详情';
  });

  // 基本信息展示数据（参考EditDrawer的basicFormSchemas）
  const basicData = computed(() => {
    if (!contractInfo.value) return [];

    return [
      {
        field: '合同财务编号',
        label: '合同财务编号',
        value: contractInfo.value.cno || '-',
      },
      {
        field: '甲方',
        label: '甲方',
        value: contractInfo.value.custName || '-',
      },
      {
        field: '最终用户',
        label: '最终用户',
        value: contractInfo.value.finalUserName || '-',
      },
      {
        field: '项目负责人',
        label: '项目负责人',
        value: contractInfo.value.ownName || '-',
      },
      {
        field: '所属部门',
        label: '所属部门',
        value: contractInfo.value.deptName || '-',
      },
    ];
  });

  // 商务信息展示数据（参考EditDrawer的businessFormSchemas）
  const businessData = computed(() => {
    if (!contractInfo.value) return [];

    return [
      {
        field: '合同类型',
        label: '合同类型',
        value: typeStatusMap[contractInfo.value.typeStatus]?.text || contractInfo.value.typeStatusText || contractInfo.value.typeStatus || '-',
      },
      {
        field: '部门合同金额',
        label: '部门合同金额',
        value: formatAmount(contractInfo.value.amount),
        isAmount: true,
      },
      {
        field: '费用预测',
        label: '费用预测',
        value: formatAmount(contractInfo.value.evaCostAmount),
        isAmount: true,
      },
      {
        field: '实际费用',
        label: '实际费用',
        value: formatAmount(contractInfo.value.actCostAmount),
        isAmount: true,
      },
      {
        field: '采购费用预测',
        label: '采购费用预测',
        value: formatAmount(contractInfo.value.evaExternalAmount),
        isAmount: true,
      },
      {
        field: '实际采购金额',
        label: '实际采购金额',
        value: formatAmount(contractInfo.value.actExternalAmount),
        isAmount: true,
      },
      {
        field: '待签外采金额',
        label: '待签外采金额',
        value: formatAmount(contractInfo.value.unsignExternalAmount),
        isAmount: true,
      },
      {
        field: '预估毛利',
        label: '预估毛利',
        value: formatAmount(contractInfo.value.estProbit),
        isAmount: true,
      },
      {
        field: '预估毛利率',
        label: '预估毛利率',
        value: contractInfo.value.estProbitRatio ? `${contractInfo.value.estProbitRatio}%` : '-',
      },
      {
        field: '实际毛利',
        label: '实际毛利',
        value: formatAmount(contractInfo.value.actProbit),
        isAmount: true,
      },
      {
        field: '实际毛利率',
        label: '实际毛利率',
        value: contractInfo.value.actProbitRatio ? `${contractInfo.value.actProbitRatio}%` : '-',
      },
      {
        field: '合同状态',
        label: '合同状态',
        value: contractStatusMap[contractInfo.value.contractStatus]?.text || contractInfo.value.contractStatusText || contractInfo.value.contractStatus || '-',
      },
      {
        field: '是否外采',
        label: '是否外采',
        value: externalStatusMap[contractInfo.value.externalStatus]?.text || contractInfo.value.externalStatusText || contractInfo.value.externalStatus || '-',
      },
      {
        field: '收款状态',
        label: '收款状态',
        value: moneyStatusMap[contractInfo.value.moneyStatus]?.text || contractInfo.value.moneyStatusText || contractInfo.value.moneyStatus || '-',
      },
      {
        field: '已收金额',
        label: '已收金额',
        value: formatAmount(contractInfo.value.ysAmount),
        isAmount: true,
      },
      {
        field: '本年度收款金额',
        label: '本年度收款金额',
        value: formatAmount(contractInfo.value.yearYsAmount),
        isAmount: true,
      },
      {
        field: '汇报频率',
        label: '汇报频率',
        value: contractInfo.value.reportFrequency || '-',
      },
      {
        field: '合同年度',
        label: '合同年度',
        value: contractInfo.value.signYear || '-',
      },
      // 分部金额分配
      {
        field: '一部金额',
        label: '一部金额',
        value: formatAmount(contractInfo.value.yfYbAmount),
        isAmount: true,
      },
      {
        field: '二部金额',
        label: '二部金额',
        value: formatAmount(contractInfo.value.yfEbAmount),
        isAmount: true,
      },
      {
        field: '交付分部金额',
        label: '交付分部金额',
        value: formatAmount(contractInfo.value.yfJfAmount),
        isAmount: true,
      },
      {
        field: '综合分配金额',
        label: '综合分配金额',
        value: formatAmount(contractInfo.value.yfOtherAmount),
        isAmount: true,
      },
      // 外采金额分配
      {
        field: '一部外采金额',
        label: '一部外采金额',
        value: formatAmount(contractInfo.value.outYbAmount),
        isAmount: true,
      },
      {
        field: '二部外采金额',
        label: '二部外采金额',
        value: formatAmount(contractInfo.value.outEbAmount),
        isAmount: true,
      },
      {
        field: '交付外采金额',
        label: '交付外采金额',
        value: formatAmount(contractInfo.value.outJfAmount),
        isAmount: true,
      },
      {
        field: '综合外采金额',
        label: '综合外采金额',
        value: formatAmount(contractInfo.value.outOtherAmount),
        isAmount: true,
      },
      // 待签外采金额
      {
        field: '待签一部外采',
        label: '待签一部外采',
        value: formatAmount(contractInfo.value.unsignOutYbAmount),
        isAmount: true,
      },
      {
        field: '待签二部外采',
        label: '待签二部外采',
        value: formatAmount(contractInfo.value.unsignOutEbAmount),
        isAmount: true,
      },
      {
        field: '待签交付外采',
        label: '待签交付外采',
        value: formatAmount(contractInfo.value.unsignOutJfAmount),
        isAmount: true,
      },
      {
        field: '待签综合外采',
        label: '待签综合外采',
        value: formatAmount(contractInfo.value.unsignOutOtherAmount),
        isAmount: true,
      },
      // 外采已付金额
      {
        field: '一部外采已付',
        label: '一部外采已付',
        value: formatAmount(contractInfo.value.outYfYbAmount),
        isAmount: true,
      },
      {
        field: '二部外采已付',
        label: '二部外采已付',
        value: formatAmount(contractInfo.value.outYfEbAmount),
        isAmount: true,
      },
      {
        field: '交付外采已付',
        label: '交付外采已付',
        value: formatAmount(contractInfo.value.outYfJfAmount),
        isAmount: true,
      },
      {
        field: '综合外采已付',
        label: '综合外采已付',
        value: formatAmount(contractInfo.value.outYfOtherAmount),
        isAmount: true,
      },
    ];
  });

  // 交付信息展示数据（参考EditDrawer的deliveryFormSchemas）
  const deliveryData = computed(() => {
    if (!contractInfo.value) return [];

    return [
      {
        field: '合同签订日期',
        label: '合同签订日期',
        value: contractInfo.value.signDate || '-',
        isDate: true,
      },
      {
        field: '合同开始日期',
        label: '合同开始日期',
        value: contractInfo.value.cstartDate || '-',
        isDate: true,
      },
      {
        field: '合同结束日期',
        label: '合同结束日期',
        value: contractInfo.value.cendDate || '-',
        isDate: true,
      },
      {
        field: '维保开始日期',
        label: '维保开始日期',
        value: contractInfo.value.mstartDate || '-',
        isDate: true,
      },
      {
        field: '维保结束日期',
        label: '维保结束日期',
        value: contractInfo.value.mendDate || '-',
        isDate: true,
      },
      {
        field: '中标日期',
        label: '中标日期',
        value: contractInfo.value.bidDate || '-',
      },
      {
        field: '开工日期',
        label: '开工日期',
        value: contractInfo.value.commencementDate || '-',
      },
      {
        field: '初验日期',
        label: '初验日期',
        value: contractInfo.value.initialCheckDate || '-',
      },
      {
        field: '终验日期',
        label: '终验日期',
        value: contractInfo.value.finalCheckDate || '-',
      },
      {
        field: '审计日期',
        label: '审计日期',
        value: contractInfo.value.auditDate || '-',
      },
      {
        field: '续签合同',
        label: '续签合同',
        value: contractInfo.value.newCid || '-',
      },
    ];
  });

  // 研发信息展示数据（参考EditDrawer的developmentFormSchemas）
  const developmentData = computed(() => {
    if (!contractInfo.value) return [];

    return [
      {
        field: '合同方联系人',
        label: '合同方联系人',
        value: contractInfo.value.linkman || '-',
      },
      {
        field: '联系电话',
        label: '联系电话',
        value: contractInfo.value.linkTelephone || '-',
      },
      {
        field: '备注',
        label: '备注',
        value: contractInfo.value.note || '-',
      },
    ];
  });

  // 外部单位/干系人信息展示数据
  const partnersData = computed(() => {
    if (!contractInfo.value) return [];

    return [
      {
        field: '监理单位',
        label: '监理单位',
        value: contractInfo.value.svDeptName || '-',
      },
      {
        field: '监理联系人',
        label: '监理联系人',
        value: contractInfo.value.svLinkman || '-',
      },
      {
        field: '监理联系电话',
        label: '监理联系电话',
        value: contractInfo.value.svTelephone || '-',
      },
      {
        field: '三方测评单位',
        label: '三方测评单位',
        value: contractInfo.value.reviewDeptName || '-',
      },
      {
        field: '测评联系人',
        label: '测评联系人',
        value: contractInfo.value.reviewLinkman || '-',
      },
      {
        field: '测评联系电话',
        label: '测评联系电话',
        value: contractInfo.value.reviewTelephone || '-',
      },
      {
        field: '等保单位',
        label: '等保单位',
        value: contractInfo.value.dbDeptName || '-',
      },
      {
        field: '等保联系人',
        label: '等保联系人',
        value: contractInfo.value.dbLinkman || '-',
      },
      {
        field: '等保联系电话',
        label: '等保联系电话',
        value: contractInfo.value.dbTelephone || '-',
      },
      {
        field: '商密评测单位',
        label: '商密评测单位',
        value: contractInfo.value.smDeptName || '-',
      },
      {
        field: '商密联系人',
        label: '商密联系人',
        value: contractInfo.value.smLinkman || '-',
      },
      {
        field: '商密联系电话',
        label: '商密联系电话',
        value: contractInfo.value.smTelephone || '-',
      },
      {
        field: '结算单位',
        label: '结算单位',
        value: contractInfo.value.jsDeptName || '-',
      },
      {
        field: '结算联系人',
        label: '结算联系人',
        value: contractInfo.value.jsLinkman || '-',
      },
      {
        field: '结算联系电话',
        label: '结算联系电话',
        value: contractInfo.value.jsTelephone || '-',
      },
    ];
  });

  // 加载合同信息
  async function loadContractInfo() {
    if (!contractId.value) {
      console.warn('合同ID为空，跳过加载');
      return;
    }

    try {
      loading.value = true;
      setDrawerProps({ loading: true });

      console.log('开始加载合同信息，ID:', contractId.value);
      const dataRes = await getContractInfo(contractId.value);
      const data = dataRes.data;

      if (!data) {
        throw new Error('获取合同数据为空');
      }

      console.log('获取到的合同数据:', data);

      // 使用数据字典翻译字段
      const translatedData = await translateDictFields([data], {
        typeStatus: { dictType: 'contractType', targetField: 'typeStatusText' },
        contractStatus: { dictType: 'contractStatus', targetField: 'contractStatusText' },
        moneyStatus: { dictType: 'HSZ', targetField: 'moneyStatusText' },
      });

      // 使用 nextTick 确保数据更新后再设置状态
      await nextTick();
      contractInfo.value = translatedData[0];
      console.log('翻译后的合同数据:', contractInfo.value);
      createMessage.success('合同信息加载成功');
    } catch (error) {
      console.error('获取合同信息失败:', error);
      createMessage.error(`获取合同信息失败: ${(error as any)?.message || '未知错误'}`);
      // 重置数据
      contractInfo.value = null;
    } finally {
      loading.value = false;
      setDrawerProps({ loading: false });
    }
  }

  // 更新合同日期
  function handleUpdateDate() {
    openDateModal(true, {
      contractId: contractId.value,
    });
  }

  // 编辑合同
  function handleEdit() {
    emit('edit', contractInfo.value);
  }

  // 注册抽屉
  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async data => {
    try {
      setDrawerProps({ confirmLoading: false, loading: true });

      // 重置状态
      contractInfo.value = null;
      activeTab.value = 'basic';

      contractId.value = data.contractId || data.id;
      console.log('DetailDrawer接收到的数据:', data);
      console.log('合同ID:', contractId.value);

      if (contractId.value) {
        // 使用 nextTick 确保 DOM 更新完成
        await nextTick();
        await loadContractInfo();
      } else {
        createMessage.error('合同ID不能为空');
        setDrawerProps({ loading: false });
      }
    } catch (error) {
      console.error('初始化抽屉失败:', error);
      createMessage.error('初始化失败');
      setDrawerProps({ loading: false });
    }
  });

  // 监听标签页切换
  watch(activeTab, () => {
    // 可以在这里添加标签页切换时的逻辑
  });
</script>

<style lang="less" scoped>
  // 响应式设计
  @media (max-width: 1200px) {
    .contract-detail-drawer {
      :deep(.ant-drawer) {
        width: 95% !important;
      }
    }

    .contract-detail-container {
      padding: 16px;
    }

    .overview-content {
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
    }

    .detail-grid {
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 16px 24px;
    }

    .tab-pane-content {
      padding: 16px;
    }

    .detail-card {
      :deep(.ant-card-body) {
        padding: 24px 16px;
      }
    }
  }

  @media (max-width: 768px) {
    .contract-detail-tabs {
      :deep(.ant-tabs-nav) {
        padding: 12px 16px 0;

        .ant-tabs-tab {
          padding: 8px 12px;
          margin-right: 4px;

          .tab-title {
            font-size: 12px;

            .tab-icon {
              font-size: 14px;
            }
          }
        }
      }
    }

    .tab-pane-content {
      padding: 12px;
    }

    .overview-content {
      grid-template-columns: 1fr;
      gap: 12px;
    }

    .detail-grid {
      grid-template-columns: 1fr;
      gap: 12px;
    }

    .detail-card {
      border-radius: 8px;

      :deep(.ant-card-head) {
        padding: 12px 16px;
        border-radius: 8px 8px 0 0;

        .ant-card-head-title {
          font-size: 14px;
        }
      }

      :deep(.ant-card-body) {
        padding: 20px 16px;
      }
    }

    .card-title {
      .title-icon {
        font-size: 16px;
      }
    }
  }
  .contract-detail-drawer {
    :deep(.ant-drawer-body) {
      padding: 0;
      background: #f5f7fa;
    }

    :deep(.ant-drawer-header) {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-bottom: none;

      .ant-drawer-title {
        color: #fff;
        font-weight: 600;
        font-size: 16px;
      }

      .ant-drawer-close {
        color: rgba(255, 255, 255, 0.8);

        &:hover {
          color: #fff;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 4px;
        }
      }
    }

    :deep(.ant-drawer-footer) {
      background: #fff;
      border-top: 1px solid #e8eaec;
      padding: 16px 24px;
    }
  }

  .contract-detail-container {
    height: 100%;
    padding: 24px;
    overflow-y: auto;
  }

  .overview-card {
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid #e8eaec;

    :deep(.ant-card-head) {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-bottom: 1px solid #e8eaec;
      border-radius: 12px 12px 0 0;
      padding: 16px 24px;

      .ant-card-head-title {
        padding: 0;
        font-size: 18px;
        font-weight: 600;
      }
    }

    :deep(.ant-card-body) {
      padding: 24px;
    }
  }

  .overview-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;

    .overview-item {
      .overview-label {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
        font-weight: 500;

        .label-icon {
          margin-right: 6px;
          font-size: 16px;
          color: #1890ff;
        }
      }

      .overview-value {
        font-size: 16px;
        font-weight: 600;
        color: #2c3e50;

        &.amount {
          font-size: 20px;
          color: #1890ff;
          font-weight: 700;

          &.received {
            color: #52c41a;
          }
        }

        &.contract-name {
          font-size: 18px;
          font-weight: 700;
          color: #1a1a1a;
          line-height: 1.4;
          margin-bottom: 8px;
        }
      }

      // 合同名称特殊样式
      &.contract-name-item {
        grid-column: 1 / -1; // 占满整行
        background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
        padding: 20px;
        border-radius: 12px;
        border: 1px solid #e6f0ff;
        margin-bottom: 8px;

        .contract-code {
          font-size: 13px;
          color: #8c8c8c;
          font-weight: 400;
          margin-top: 4px;
          padding: 4px 8px;
          background: rgba(24, 144, 255, 0.1);
          border-radius: 4px;
          display: inline-block;
        }
      }

      // 进度项特殊样式
      &.progress-item {
        .progress-wrapper {
          .progress-detail {
            font-size: 12px;
            color: #8c8c8c;
            margin-top: 8px;
            text-align: center;
          }
        }
      }
    }
  }

  .contract-detail-tabs {
    height: calc(100% - 200px);

    :deep(.ant-tabs-nav) {
      background: #fff;
      margin: 0;
      padding: 16px 24px 0;
      border-bottom: 1px solid #e8eaec;
      border-radius: 12px 12px 0 0;

      .ant-tabs-tab {
        border: 1px solid #e8eaec;
        border-radius: 8px 8px 0 0;
        margin-right: 8px;
        padding: 12px 20px;
        background: #fafbfc;
        transition: all 0.3s ease;

        &:hover {
          background: #f0f2f5;
          border-color: #d9d9d9;
        }

        &.ant-tabs-tab-active {
          background: #fff;
          border-color: #1890ff;
          border-bottom-color: #fff;

          .tab-title {
            color: #1890ff;
            font-weight: 600;
          }
        }
      }
    }

    :deep(.ant-tabs-content-holder) {
      background: #f5f7fa;
      padding: 0;
      height: calc(100% - 60px);
      overflow-y: auto;
    }

    :deep(.ant-tabs-tabpane) {
      height: 100%;
      padding: 0;
    }
  }

  .tab-pane-content {
    padding: 24px;
    height: 100%;
    overflow-y: auto;
  }

  .tab-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #666;
    transition: color 0.3s ease;

    .tab-icon {
      font-size: 16px;
    }
  }

  .detail-card {
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #e8eaec;
    background: #fff;

    :deep(.ant-card-head) {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-bottom: 1px solid #e8eaec;
      border-radius: 12px 12px 0 0;
      padding: 16px 24px;

      .ant-card-head-title {
        padding: 0;
        font-size: 16px;
        font-weight: 600;
      }
    }

    :deep(.ant-card-body) {
      padding: 32px 24px;
    }
  }

  .card-title {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #2c3e50;

    .title-icon {
      font-size: 18px;
      color: #1890ff;
    }
  }

  .detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px 32px;

    .detail-item {
      .detail-label {
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
        font-weight: 500;
        display: flex;
        align-items: center;

        .label-icon {
          margin-right: 6px;
          font-size: 14px;
          color: #1890ff;
        }
      }

      .detail-value {
        font-size: 15px;
        font-weight: 500;
        color: #2c3e50;
        word-break: break-all;
        line-height: 1.5;

        &.amount {
          font-size: 16px;
          color: #1890ff;
          font-weight: 600;
        }

        &.date {
          color: #52c41a;
          font-weight: 500;
        }

        // 重要字段突出显示
        &.important {
          font-size: 16px;
          font-weight: 700;
          color: #1a1a1a;
        }

        // 描述类字段
        &.description {
          font-size: 14px;
          line-height: 1.6;
          color: #595959;
          background: #fafafa;
          padding: 12px;
          border-radius: 6px;
          border-left: 3px solid #1890ff;
        }

        // 状态类字段
        &.status {
          font-weight: 600;
        }
      }

      // 特殊字段样式
      &.name-field {
        grid-column: 1 / -1; // 占满整行

        .detail-value {
          font-size: 17px;
          font-weight: 700;
          color: #1a1a1a;
          background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
          padding: 16px;
          border-radius: 8px;
          border: 1px solid #e6f0ff;
        }
      }

      &.description-field {
        grid-column: 1 / -1; // 占满整行
      }
    }
  }

  .mb-4 {
    margin-bottom: 24px;
  }

  // 加载状态样式
  :deep(.ant-spin-container) {
    height: 100%;
  }

  :deep(.ant-spin-spinning) {
    .contract-detail-tabs {
      opacity: 0.7;
      pointer-events: none;
    }
  }

  // 加载和空状态占位符样式
  .loading-placeholder,
  .empty-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    padding: 40px;
  }

  .loading-placeholder {
    background: #fafafa;
    border-radius: 8px;
  }

  .empty-placeholder {
    background: #f9f9f9;
    border-radius: 8px;
    border: 1px dashed #d9d9d9;
  }

  // 滚动条美化
  .contract-detail-container,
  .tab-pane-content {
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }

  // 进度条样式
  :deep(.ant-progress) {
    .ant-progress-text {
      font-weight: 600;
    }
  }

  // 标签样式
  :deep(.ant-tag) {
    border-radius: 6px;
    padding: 4px 12px;
    font-weight: 500;
    border: none;
  }
</style>
