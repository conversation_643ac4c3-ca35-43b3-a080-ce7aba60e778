package com.xinghuo.project.core.model.projectBase;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目基础信息VO
 * 
 * <AUTHOR>
 * @since 2024-08-05
 */
@Data
@Schema(description = "项目基础信息VO")
public class ProjectBaseInfoVO {

    /**
     * 项目ID
     */
    @Schema(description = "项目ID")
    private String id;

    /**
     * 项目编码
     */
    @Schema(description = "项目编码")
    private String code;

    /**
     * 项目名称
     */
    @Schema(description = "项目名称")
    private String fullName;

    /**
     * 项目描述
     */
    @Schema(description = "项目描述")
    private String description;

    /**
     * 项目状态
     */
    @Schema(description = "项目状态")
    private String status;

    /**
     * 项目类型ID
     */
    @Schema(description = "项目类型ID")
    private String typeId;

    /**
     * 项目类型名称
     */
    @Schema(description = "项目类型名称")
    private String typeName;

    /**
     * 项目经理ID
     */
    @Schema(description = "项目经理ID")
    private String managerId;

    /**
     * 项目经理名称
     */
    @Schema(description = "项目经理名称")
    private String managerName;

    /**
     * 项目发起人ID
     */
    @Schema(description = "项目发起人ID")
    private String sponsorId;

    /**
     * 项目发起人名称
     */
    @Schema(description = "项目发起人名称")
    private String sponsorName;

    /**
     * 计划开始日期
     */
    @Schema(description = "计划开始日期")
    private Date plannedStartDate;

    /**
     * 计划结束日期
     */
    @Schema(description = "计划结束日期")
    private Date plannedEndDate;

    /**
     * 实际开始日期
     */
    @Schema(description = "实际开始日期")
    private Date actualStartDate;

    /**
     * 实际结束日期
     */
    @Schema(description = "实际结束日期")
    private Date actualEndDate;

    /**
     * 投资预算
     */
    @Schema(description = "投资预算")
    private BigDecimal investmentBudget;

    /**
     * 战略目标
     */
    @Schema(description = "战略目标")
    private String strategicObjective;

    /**
     * 战略目标名称
     */
    @Schema(description = "战略目标名称")
    private String strategicObjectiveName;

    /**
     * 优先级
     */
    @Schema(description = "优先级")
    private String priority;

    /**
     * 优先级名称
     */
    @Schema(description = "优先级名称")
    private String priorityName;

    /**
     * 工作量估算（人天）
     */
    @Schema(description = "工作量估算（人天）")
    private BigDecimal estimatedWorkload;

    /**
     * 风险等级
     */
    @Schema(description = "风险等级")
    private String riskLevel;

    /**
     * 风险等级名称
     */
    @Schema(description = "风险等级名称")
    private String riskLevelName;

    /**
     * 所属部门ID
     */
    @Schema(description = "所属部门ID")
    private String departmentId;

    /**
     * 所属部门名称
     */
    @Schema(description = "所属部门名称")
    private String departmentName;

    /**
     * 所属项目群ID
     */
    @Schema(description = "所属项目群ID")
    private String programId;

    /**
     * 所属项目群名称
     */
    @Schema(description = "所属项目群名称")
    private String programName;

    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    private String customerId;

    /**
     * 客户名称
     */
    @Schema(description = "客户名称")
    private String customerName;

    /**
     * 合同ID
     */
    @Schema(description = "合同ID")
    private String contractId;

    /**
     * 合同名称
     */
    @Schema(description = "合同名称")
    private String contractName;

    /**
     * 项目范围
     */
    @Schema(description = "项目范围")
    private String projectScope;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createdAt;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createdBy;

    /**
     * 创建人名称
     */
    @Schema(description = "创建人名称")
    private String createdByName;

    /**
     * 最后更新时间
     */
    @Schema(description = "最后更新时间")
    private Date lastUpdatedAt;

    /**
     * 最后更新人
     */
    @Schema(description = "最后更新人")
    private String lastUpdatedBy;

    /**
     * 最后更新人名称
     */
    @Schema(description = "最后更新人名称")
    private String lastUpdatedByName;
}
