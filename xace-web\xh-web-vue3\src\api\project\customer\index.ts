import { defHttp } from '/@/utils/http/axios';
import { ListResult } from '/@/api/model/baseModel';

/**
 * 客户单位管理API
 */

// API URL前缀
const API_PREFIX = '/api/project/biz/customer';

/**
 * 客户单位对象接口
 */
export interface ProjCustomerModel {
  id: string;
  custType: string;
  type: string;
  leader: string;
  name: string;
  remark: string;
  sortCode: number;
  creatorUserId: string;
  creatorTime: string;
  lastModifyUserId: string;
  lastModifyTime: string;
}

/**
 * 客户单位表单接口
 */
export interface ProjCustomerFormModel {
  custType: string;
  type?: string;
  leader?: string;
  name: string;
  remark?: string;
  sortCode?: number;
}

/**
 * 客户单位查询参数接口
 */
export interface ProjCustomerQueryParams {
  name?: string;
  custType?: string;
  type?: string;
  leader?: string;
  keyword?: string;
  pageSize?: number;
  currentPage?: number;
}

/**
 * 获取客户单位列表
 * @param params 查询参数
 * @returns 客户单位列表
 */
export const getCustomerList = (params?: ProjCustomerQueryParams) => {
  return defHttp.post<ListResult<ProjCustomerModel>>({
    url: `${API_PREFIX}/getList`,
    data: params,
  });
};

/**
 * 获取客户单位详情
 * @param id 客户单位ID
 * @returns 客户单位详情
 */
export const getCustomerInfo = (id: string) => {
  return defHttp.get<ProjCustomerModel>({
    url: `${API_PREFIX}/${id}`,
  });
};

/**
 * 创建客户单位
 * @param params 客户单位创建参数
 * @returns 操作结果
 */
export const createCustomer = (params: ProjCustomerFormModel) => {
  return defHttp.post<void>({
    url: API_PREFIX,
    data: params,
  });
};

/**
 * 更新客户单位
 * @param id 客户单位ID
 * @param params 客户单位更新参数
 * @returns 操作结果
 */
export const updateCustomer = (id: string, params: ProjCustomerFormModel) => {
  return defHttp.put<void>({
    url: `${API_PREFIX}/${id}`,
    data: params,
  });
};

/**
 * 删除客户单位
 * @param id 客户单位ID
 * @returns 操作结果
 */
export const deleteCustomer = (id: string) => {
  return defHttp.delete<void>({
    url: `${API_PREFIX}/${id}`,
  });
};
