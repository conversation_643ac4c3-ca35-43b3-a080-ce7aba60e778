package com.xinghuo.project.portfolio.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.project.portfolio.entity.PortfolioEntity;
import com.xinghuo.project.portfolio.model.PortfolioPagination;
import com.xinghuo.project.portfolio.service.PortfolioService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 项目组合管理控制器
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Tag(name = "项目组合管理", description = "项目组合管理相关接口")
@RestController
@RequestMapping("/api/project/portfolio/portfolio")
public class PortfolioController {

    @Resource
    private PortfolioService portfolioService;

    /**
     * 获取项目组合列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取项目组合列表")
    public ActionResult<PageListVO<PortfolioEntity>> list(@RequestBody PortfolioPagination pagination) {
        List<PortfolioEntity> list = portfolioService.getList(pagination);
        PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
        return ActionResult.page(list, page);
    }

    /**
     * 根据负责人ID获取组合列表
     */
    @GetMapping("/getListByOwnerId/{ownerId}")
    @Operation(summary = "根据负责人ID获取组合列表")
    public ActionResult<List<PortfolioEntity>> getListByOwnerId(
            @Parameter(description = "负责人ID") @PathVariable String ownerId) {
        List<PortfolioEntity> list = portfolioService.getListByOwnerId(ownerId);
        return ActionResult.success(list);
    }

    /**
     * 根据组合类型ID获取组合列表
     */
    @GetMapping("/getListByTypeId/{typeId}")
    @Operation(summary = "根据组合类型ID获取组合列表")
    public ActionResult<List<PortfolioEntity>> getListByTypeId(
            @Parameter(description = "组合类型ID") @PathVariable String typeId) {
        List<PortfolioEntity> list = portfolioService.getListByTypeId(typeId);
        return ActionResult.success(list);
    }

    /**
     * 根据状态获取组合列表
     */
    @GetMapping("/getListByStatus/{status}")
    @Operation(summary = "根据状态获取组合列表")
    public ActionResult<List<PortfolioEntity>> getListByStatus(
            @Parameter(description = "状态") @PathVariable String status) {
        List<PortfolioEntity> list = portfolioService.getListByStatus(status);
        return ActionResult.success(list);
    }

    /**
     * 获取项目组合详情
     */
    @GetMapping("/getInfo/{id}")
    @Operation(summary = "获取项目组合详情")
    public ActionResult<PortfolioEntity> getInfo(
            @Parameter(description = "组合ID") @PathVariable String id) {
        PortfolioEntity entity = portfolioService.getInfo(id);
        return ActionResult.success(entity);
    }

    /**
     * 创建项目组合
     */
    @PostMapping("/create")
    @Operation(summary = "创建项目组合")
    public ActionResult<String> create(@RequestBody @Valid PortfolioEntity entity) {
        boolean result = portfolioService.saveInfo(entity);
        if (result) {
            return ActionResult.success("创建成功", entity.getId());
        } else {
            return ActionResult.fail("创建失败");
        }
    }

    /**
     * 更新项目组合
     */
    @PutMapping("/update/{id}")
    @Operation(summary = "更新项目组合")
    public ActionResult<String> update(
            @Parameter(description = "组合ID") @PathVariable String id,
            @RequestBody @Valid PortfolioEntity entity) {
        entity.setId(id);
        boolean result = portfolioService.updateInfo(entity);
        if (result) {
            return ActionResult.success("更新成功");
        } else {
            return ActionResult.fail("更新失败");
        }
    }

    /**
     * 删除项目组合
     */
    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除项目组合")
    public ActionResult<String> delete(
            @Parameter(description = "组合ID") @PathVariable String id) {
        boolean result = portfolioService.deleteById(id);
        if (result) {
            return ActionResult.success("删除成功");
        } else {
            return ActionResult.fail("删除失败");
        }
    }

    /**
     * 检查组合编码是否存在
     */
    @GetMapping("/checkCodeExists")
    @Operation(summary = "检查组合编码是否存在")
    public ActionResult<Boolean> checkCodeExists(
            @RequestParam String code,
            @RequestParam(required = false) String excludeId) {
        boolean exists = portfolioService.isExistByCode(code, excludeId);
        return ActionResult.success(exists);
    }
}
