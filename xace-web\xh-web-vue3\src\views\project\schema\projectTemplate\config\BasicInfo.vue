<template>
  <div class="template-basic-info">
    <a-card title="基本信息" :bordered="false">
      <BasicForm @register="register" @submit="handleSubmit" />
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { onMounted } from 'vue';
  import { BasicForm, useForm } from '/@/components/Form';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { getProjectTemplateInfo, updateProjectTemplate, generateProjectTemplateCode } from '/@/api/project/projectTemplate';
  import { basicInfoFormSchema } from './formSchemas';

  const props = defineProps({
    templateId: {
      type: String,
      required: true,
    },
  });

  const emit = defineEmits(['success']);

  const { createMessage } = useMessage();

  const [register, { setFieldsValue, resetFields, validate }] = useForm({
    labelWidth: 120,
    schemas: basicInfoFormSchema,
    showActionButtonGroup: true,
    actionColOptions: {
      span: 24,
    },
    submitButtonOptions: {
      text: '保存',
    },
    resetButtonOptions: {
      text: '重置',
    },
  });

  // 加载模板信息
  const loadTemplateInfo = async () => {
    if (!props.templateId) return;

    try {
      const response = await getProjectTemplateInfo(props.templateId);
      if (response.code === 200 && response.data) {
        await setFieldsValue(response.data);
      }
    } catch (error) {
      console.error('加载模板信息失败:', error);
      createMessage.error('加载模板信息失败');
    }
  };

  // 生成模板编码
  const generateCode = async () => {
    try {
      const response = await generateProjectTemplateCode();
      if (response.code === 200) {
        await setFieldsValue({ code: response.data });
        createMessage.success('编码生成成功');
      }
    } catch (error) {
      console.error('生成编码失败:', error);
      createMessage.error('生成编码失败');
    }
  };

  // 提交处理
  const handleSubmit = async (values: any) => {
    try {
      const response = await updateProjectTemplate(props.templateId, values);
      if (response.code === 200) {
        createMessage.success('保存成功');
        emit('success');
      } else {
        createMessage.error(response.msg || '保存失败');
      }
    } catch (error) {
      console.error('保存失败:', error);
      createMessage.error('保存失败');
    }
  };

  onMounted(() => {
    loadTemplateInfo();
  });

  // 暴露生成编码方法给表单使用
  defineExpose({
    generateCode,
  });
</script>

<style lang="less" scoped>
  .template-basic-info {
    padding: 16px;

    :deep(.ant-card) {
      .ant-card-head {
        border-bottom: 1px solid #f0f0f0;
      }

      .ant-card-body {
        padding: 24px;
      }
    }
  }
</style>
