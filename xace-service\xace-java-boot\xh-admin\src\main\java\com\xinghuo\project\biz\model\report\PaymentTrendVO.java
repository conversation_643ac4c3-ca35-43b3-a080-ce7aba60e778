package com.xinghuo.project.biz.model.report;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 收款趋势数据VO
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Schema(description = "收款趋势数据VO")
public class PaymentTrendVO {

    /**
     * 月份列表
     */
    @Schema(description = "月份列表")
    private List<String> months;

    /**
     * 已收金额列表
     */
    @Schema(description = "已收金额列表")
    private List<BigDecimal> receivedAmounts;

    /**
     * 目标金额列表
     */
    @Schema(description = "目标金额列表")
    private List<BigDecimal> targetAmounts;
}
