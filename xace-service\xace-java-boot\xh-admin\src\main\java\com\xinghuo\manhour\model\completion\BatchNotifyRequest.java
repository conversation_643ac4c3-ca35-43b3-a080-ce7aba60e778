package com.xinghuo.manhour.model.completion;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 批量提醒请求
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@Schema(description = "批量提醒请求")
public class BatchNotifyRequest {

    @Schema(description = "用户ID列表")
    private List<String> userIds;

    @Schema(description = "月份")
    private String month;

    @Schema(description = "提醒类型")
    private String notifyType;

    @Schema(description = "提醒内容")
    private String message;
}
