import { defHttp } from '/@/utils/http/axios';

enum Api {
  GetList = '/api/project/template/activityLibrary/getList',
  GetListByStatus = '/api/project/template/activityLibrary/getListByStatus',
  GetListByActivityType = '/api/project/template/activityLibrary/getListByActivityType',
  GetListByMilestone = '/api/project/template/activityLibrary/getListByMilestone',
  GetInfo = '/api/project/template/activityLibrary/getInfo',
  GetByCode = '/api/project/template/activityLibrary/getByCode',
  Create = '/api/project/template/activityLibrary/create',
  Update = '/api/project/template/activityLibrary/update',
  Delete = '/api/project/template/activityLibrary/delete',
  BatchDelete = '/api/project/template/activityLibrary/batchDelete',
  UpdateStatus = '/api/project/template/activityLibrary/updateStatus',
  BatchUpdateStatus = '/api/project/template/activityLibrary/batchUpdateStatus',
  Enable = '/api/project/template/activityLibrary/enable',
  Disable = '/api/project/template/activityLibrary/disable',
  Copy = '/api/project/template/activityLibrary/copy',
  CheckCodeExists = '/api/project/template/activityLibrary/checkCodeExists',
  CheckNameExists = '/api/project/template/activityLibrary/checkNameExists',
  GetSelectList = '/api/project/template/activityLibrary/getSelectList',
  GenerateCode = '/api/project/template/activityLibrary/generateCode',
  GetUsageInfo = '/api/project/template/activityLibrary/getActivityLibraryUsageInfo',
}

/**
 * 标准项目活动库接口
 */

// 获取活动库列表
export function getActivityLibraryList(params: any) {
  return defHttp.post({
    url: Api.GetList,
    data: params,
  });
}

// 根据状态获取活动库列表
export function getActivityLibraryListByStatus(status: number) {
  return defHttp.get({
    url: `${Api.GetListByStatus}/${status}`,
  });
}

// 根据活动类型获取活动库列表
export function getActivityLibraryListByActivityType(activityTypeId: string) {
  return defHttp.get({
    url: `${Api.GetListByActivityType}/${activityTypeId}`,
  });
}

// 根据里程碑标识获取活动库列表
export function getActivityLibraryListByMilestone(isMilestone: number) {
  return defHttp.get({
    url: `${Api.GetListByMilestone}/${isMilestone}`,
  });
}

// 获取活动库详情
export function getActivityLibraryInfo(id: string) {
  return defHttp.get({
    url: `${Api.GetInfo}/${id}`,
  });
}

// 根据编码获取活动库
export function getActivityLibraryByCode(code: string) {
  return defHttp.get({
    url: `${Api.GetByCode}/${code}`,
  });
}

// 创建活动库
export function createActivityLibrary(params: any) {
  return defHttp.post({
    url: Api.Create,
    data: params,
  });
}

// 更新活动库
export function updateActivityLibrary(id: string, params: any) {
  return defHttp.put({
    url: `${Api.Update}/${id}`,
    data: params,
  });
}

// 删除活动库
export function deleteActivityLibrary(id: string) {
  return defHttp.delete({
    url: `${Api.Delete}/${id}`,
  });
}

// 批量删除活动库
export function batchDeleteActivityLibrary(ids: string[]) {
  return defHttp.delete({
    url: Api.BatchDelete,
    data: ids,
  });
}

// 更新活动库状态
export function updateActivityLibraryStatus(id: string, status: number) {
  return defHttp.put({
    url: `${Api.UpdateStatus}/${id}?status=${status}`,
  });
}

// 批量更新状态
export function batchUpdateActivityLibraryStatus(ids: string[], status: number) {
  return defHttp.put({
    url: `${Api.BatchUpdateStatus}?status=${status}`,
    data: ids,
  });
}

// 启用活动库
export function enableActivityLibrary(id: string) {
  return defHttp.put({
    url: `${Api.Enable}/${id}`,
  });
}

// 禁用活动库
export function disableActivityLibrary(id: string) {
  return defHttp.put({
    url: `${Api.Disable}/${id}`,
  });
}

// 复制活动库
export function copyActivityLibrary(id: string, newName: string) {
  return defHttp.post({
    url: `${Api.Copy}/${id}?newName=${encodeURIComponent(newName)}`,
  });
}

// 检查活动编码是否存在
export function checkActivityLibraryCodeExists(code: string, excludeId?: string) {
  return defHttp.get({
    url: Api.CheckCodeExists,
    params: { code, excludeId },
  });
}

// 检查活动名称是否存在
export function checkActivityLibraryNameExists(name: string, excludeId?: string) {
  return defHttp.get({
    url: Api.CheckNameExists,
    params: { name, excludeId },
  });
}

// 获取活动库选择列表
export function getActivityLibrarySelectList(keyword?: string) {
  return defHttp.get({
    url: Api.GetSelectList,
    params: { keyword },
  });
}

// 生成活动编码
export function generateActivityLibraryCode() {
  return defHttp.get({
    url: Api.GenerateCode,
  });
}

// 获取活动库使用情况
export function getActivityLibraryUsageInfo(id: string) {
  return defHttp.get({
    url: `${Api.GetUsageInfo}/${id}`,
  });
}
