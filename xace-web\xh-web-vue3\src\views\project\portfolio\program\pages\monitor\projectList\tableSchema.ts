import { BasicColumn } from '/@/components/Table';
import { Badge, Progress } from 'ant-design-vue';
import { h } from 'vue';

export function getTableSchemas(): BasicColumn[] {
  return [
    {
      title: '项目名称',
      dataIndex: 'projectName',
      width: 300,
      ellipsis: true,
    },
    {
      title: '项目编号',
      dataIndex: 'projectCode',
      width: 120,
    },
    {
      title: '所属部门',
      dataIndex: 'department',
      width: 200,
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      customRender: ({ record }) => {
        const statusMap = {
          进行中: { color: 'processing', text: '进行中' },
          已完成: { color: 'success', text: '已完成' },
          规划中: { color: 'default', text: '规划中' },
          暂停: { color: 'warning', text: '暂停' },
          风险: { color: 'error', text: '风险' },
        };
        const status = statusMap[record.status] || { color: 'default', text: record.status };
        return h(Badge, { status: status.color, text: status.text });
      },
    },
    {
      title: '类别',
      dataIndex: 'category',
      width: 140,
    },
    {
      title: '计划开始日期',
      dataIndex: 'planStartDate',
      width: 120,
    },
    {
      title: '实际开始日期',
      dataIndex: 'actualStartDate',
      width: 120,
    },
    {
      title: '计划结束日期',
      dataIndex: 'planEndDate',
      width: 120,
    },
    {
      title: '计划总工期(天)',
      dataIndex: 'planDuration',
      width: 120,
    },
    {
      title: '项目发起人',
      dataIndex: 'sponsor',
      width: 120,
      ellipsis: true,
    },
    {
      title: '项目经理',
      dataIndex: 'projectManager',
      width: 150,
      ellipsis: true,
    },
    {
      title: '所属项目群',
      dataIndex: 'programGroup',
      width: 200,
      ellipsis: true,
    },
    {
      title: '客户名称',
      dataIndex: 'client',
      width: 120,
    },
    {
      title: '当前阶段',
      dataIndex: 'currentPhase',
      width: 150,
    },
    {
      title: '进度',
      dataIndex: 'progress',
      width: 100,
      customRender: ({ record }) => {
        return h(Progress, {
          percent: record.progress,
          size: 'small',
          status: record.progress === 100 ? 'success' : 'active',
        });
      },
    },
    {
      title: '预算(万元)',
      dataIndex: 'budget',
      width: 120,
      customRender: ({ record }) => {
        return (record.budget / 10000).toFixed(1);
      },
    },
    {
      title: '实际成本(万元)',
      dataIndex: 'actualCost',
      width: 120,
      customRender: ({ record }) => {
        return (record.actualCost / 10000).toFixed(1);
      },
    },
    {
      title: '健康状态',
      dataIndex: 'healthStatus',
      width: 100,
      customRender: ({ record }) => {
        const colorMap = {
          健康: 'success',
          延期: 'warning',
          风险: 'error',
        };
        return h(Badge, {
          status: colorMap[record.healthStatus] || 'default',
          text: record.healthStatus,
        });
      },
    },
  ];
}
