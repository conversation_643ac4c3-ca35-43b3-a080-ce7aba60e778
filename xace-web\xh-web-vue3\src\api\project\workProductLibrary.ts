import { defHttp } from '/@/utils/http/axios';

enum Api {
  GetList = '/api/project/template/workProductLibrary/getList',
  GetInfo = '/api/project/template/workProductLibrary/getInfo',
  GetDetailInfo = '/api/project/template/workProductLibrary/getDetailInfo',
  GetByCode = '/api/project/template/workProductLibrary/getByCode',
  Create = '/api/project/template/workProductLibrary/create',
  Update = '/api/project/template/workProductLibrary/update',
  Delete = '/api/project/template/workProductLibrary/delete',
  BatchDelete = '/api/project/template/workProductLibrary/batchDelete',
  UpdateStatus = '/api/project/template/workProductLibrary/updateStatus',
  BatchUpdateStatus = '/api/project/template/workProductLibrary/batchUpdateStatus',
  Enable = '/api/project/template/workProductLibrary/enable',
  Disable = '/api/project/template/workProductLibrary/disable',
  Copy = '/api/project/template/workProductLibrary/copy',
  CheckNameExists = '/api/project/template/workProductLibrary/checkNameExists',
  CheckCodeExists = '/api/project/template/workProductLibrary/checkCodeExists',
  GetSelectList = '/api/project/template/workProductLibrary/getSelectList',
  GenerateCode = '/api/project/template/workProductLibrary/generateCode',
  GetListByStatus = '/api/project/template/workProductLibrary/getListByStatus',
  GetUsageInfo = '/api/project/template/workProductLibrary/getWorkProductUsageInfo',
}

// 获取交付物库列表
export function getWorkProductLibraryList(params: any) {
  return defHttp.post({
    url: Api.GetList,
    data: params,
  });
}

// 获取交付物库详情
export function getWorkProductLibraryInfo(id: string) {
  return defHttp.get({
    url: `${Api.GetInfo}/${id}`,
  });
}

// 获取交付物库详细信息
export function getWorkProductLibraryDetailInfo(id: string) {
  return defHttp.get({
    url: `${Api.GetDetailInfo}/${id}`,
  });
}

// 根据编码获取交付物库
export function getWorkProductLibraryByCode(code: string) {
  return defHttp.get({
    url: `${Api.GetByCode}/${code}`,
  });
}

// 创建交付物库
export function createWorkProductLibrary(params: any) {
  return defHttp.post({
    url: Api.Create,
    data: params,
  });
}

// 更新交付物库
export function updateWorkProductLibrary(id: string, params: any) {
  return defHttp.put({
    url: `${Api.Update}/${id}`,
    data: params,
  });
}

// 删除交付物库
export function deleteWorkProductLibrary(id: string) {
  return defHttp.delete({
    url: `${Api.Delete}/${id}`,
  });
}

// 批量删除交付物库
export function batchDeleteWorkProductLibrary(ids: string[]) {
  return defHttp.delete({
    url: Api.BatchDelete,
    data: ids,
  });
}

// 启用交付物库
export function enableWorkProductLibrary(id: string) {
  return defHttp.put({
    url: `${Api.Enable}/${id}`,
  });
}

// 禁用交付物库
export function disableWorkProductLibrary(id: string) {
  return defHttp.put({
    url: `${Api.Disable}/${id}`,
  });
}

// 更新交付物库状态
export function updateWorkProductLibraryStatus(id: string, statusId: string) {
  return defHttp.put({
    url: `${Api.UpdateStatus}/${id}?statusId=${encodeURIComponent(statusId)}`,
  });
}

// 批量更新交付物库状态
export function batchUpdateWorkProductLibraryStatus(ids: string[], statusId: string) {
  return defHttp.put({
    url: `${Api.BatchUpdateStatus}?statusId=${encodeURIComponent(statusId)}`,
    data: ids,
  });
}

// 复制交付物库
export function copyWorkProductLibrary(id: string, newName: string) {
  return defHttp.post({
    url: `${Api.Copy}/${id}?newName=${encodeURIComponent(newName)}`,
  });
}

// 检查交付物库名称是否存在
export function checkWorkProductLibraryNameExists(name: string, excludeId?: string) {
  return defHttp.get({
    url: Api.CheckNameExists,
    params: { name, excludeId },
  });
}

// 检查交付物库编码是否存在
export function checkWorkProductLibraryCodeExists(code: string, excludeId?: string) {
  return defHttp.get({
    url: Api.CheckCodeExists,
    params: { code, excludeId },
  });
}

// 获取交付物库选择列表
export function getWorkProductLibrarySelectList(keyword?: string, statusId?: string, typeId?: string) {
  return defHttp.get({
    url: Api.GetSelectList,
    params: { keyword, statusId, typeId },
  });
}

// 生成交付物库编码
export function generateWorkProductLibraryCode() {
  return defHttp.get({
    url: Api.GenerateCode,
  });
}

// 根据状态获取交付物库列表
export function getWorkProductLibraryListByStatus(statusId: string) {
  return defHttp.get({
    url: `${Api.GetListByStatus}/${statusId}`,
  });
}

// 获取交付物库使用情况
export function getWorkProductLibraryUsageInfo(id: string) {
  return defHttp.get({
    url: `${Api.GetUsageInfo}/${id}`,
  });
}
