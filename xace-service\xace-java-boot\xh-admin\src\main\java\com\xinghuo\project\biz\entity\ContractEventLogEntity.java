package com.xinghuo.project.biz.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 项目合同事件日志实体类
 * 对应数据库表：zz_proj_contract_datelog
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode
@TableName("zz_proj_contract_datelog_v2")
public class ContractEventLogEntity {

    /**
     * 主键ID
     */
    @TableId("id")
    private String id;

    /**
     * 关联ID
     */
    @TableField("relate_id")
    private String relateId;

    /**
     * 类型
     */
    @TableField("type")
    private String type;

    /**
     * 旧日期
     */
    @TableField("old_date")
    private Date oldDate;

    /**
     * 新日期
     */
    @TableField("new_date")
    private Date newDate;

    /**
     * 备注
     */
    @TableField("note")
    private String note;

    /**
     * 创建用户
     */
    @TableField("create_user_id")
    private String createUserId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
}
