<template>
  <div class="performance-analysis">
    <!-- 查询条件 -->
    <div class="search-section">
      <BasicForm @register="registerForm" @submit="handleSearch" @reset="handleReset" />
    </div>

    <!-- 概览统计 -->
    <div class="overview-section">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic title="总参与人数" :value="overview.totalUsers" :value-style="{ color: '#1890ff' }" />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic title="平均绩效得分" :value="overview.avgScore" :precision="2" suffix="分" :value-style="{ color: '#52c41a' }" />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic title="优秀率" :value="overview.excellentRate" :precision="1" suffix="%" :value-style="{ color: '#faad14' }" />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic title="完成评分人数" :value="overview.completedUsers" :value-style="{ color: '#722ed1' }" />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 图表分析 -->
    <div class="charts-section">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-card title="绩效得分分布" :bordered="false">
            <div ref="scoreDistributionChart" style="height: 300px"></div>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="分部绩效对比" :bordered="false">
            <div ref="departmentComparisonChart" style="height: 300px"></div>
          </a-card>
        </a-col>
      </a-row>
      <a-row :gutter="16" style="margin-top: 16px">
        <a-col :span="12">
          <a-card title="绩效趋势分析" :bordered="false">
            <div ref="trendChart" style="height: 300px"></div>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="评分维度分析" :bordered="false">
            <div ref="dimensionChart" style="height: 300px"></div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 详细数据表格 -->
    <div class="table-section">
      <a-card title="绩效分析详情" :bordered="false">
        <a-tabs v-model:activeKey="activeTab" @change="handleTabChange">
          <a-tab-pane key="personal" tab="个人绩效分析">
            <BasicTable @register="registerPersonalTable">
              <template #action="{ record }">
                <TableAction
                  :actions="[
                    {
                      icon: 'clarity:note-edit-line',
                      tooltip: '查看详情',
                      onClick: handleViewDetail.bind(null, record),
                    },
                  ]" />
              </template>
            </BasicTable>
          </a-tab-pane>
          <a-tab-pane key="department" tab="分部绩效分析">
            <BasicTable @register="registerDepartmentTable" />
          </a-tab-pane>
          <a-tab-pane key="ranking" tab="绩效排名">
            <BasicTable @register="registerRankingTable" />
          </a-tab-pane>
          <a-tab-pane key="advanced" tab="高级分析">
            <PerformanceAnalytics />
          </a-tab-pane>
          <a-tab-pane key="alerts" tab="预警监控">
            <PerformanceAlertSystem />
          </a-tab-pane>
          <a-tab-pane key="grouped" tab="分组展示">
            <GroupedPerformanceDisplay :dataSource="groupedDisplayData" :loading="loading" @refresh="handleRefresh" @export="handleGroupedExport" />
          </a-tab-pane>
        </a-tabs>
      </a-card>
    </div>

    <!-- 导出按钮 -->
    <div class="action-section" style="margin-top: 16px; text-align: right">
      <a-button type="primary" :loading="exportLoading" @click="handleExport">
        <template #icon>
          <ExportOutlined />
        </template>
        导出报表
      </a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted, nextTick } from 'vue';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { ExportOutlined } from '@ant-design/icons-vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import * as echarts from 'echarts';
  import { useComponentRegister } from '/@/components/Form/index';
  import FbSelect from '/@/views/performance/common/fbSelect.vue';
  import PerformanceAnalytics from '/@/views/performance/components/PerformanceAnalytics.vue';
  import PerformanceAlertSystem from '/@/views/performance/components/PerformanceAlertSystem.vue';
  import GroupedPerformanceDisplay from '/@/views/performance/components/GroupedPerformanceDisplay.vue';
  import {
    getPerformanceAnalysisOverview,
    getPerformanceAnalysisCharts,
    getPersonalPerformanceList,
    getDepartmentPerformanceList,
    getPerformanceRankingList,
    exportPerformanceAnalysis,
  } from './api';

  defineOptions({ name: 'PerformanceAnalysis' });

  // @ts-expect-error
  useComponentRegister('FbSelect', FbSelect);

  const { createMessage } = useMessage();
  const activeTab = ref('personal');
  const exportLoading = ref(false);

  // 存储当前查询参数
  const currentParams = ref<any>({});

  // 概览数据
  const overview = reactive({
    totalUsers: 0,
    avgScore: 0,
    excellentRate: 0,
    completedUsers: 0,
  });

  // 分组展示数据
  const groupedDisplayData = ref([]);

  // 图表实例
  const scoreDistributionChart = ref();
  const departmentComparisonChart = ref();
  const trendChart = ref();
  const dimensionChart = ref();

  const chartInstances = reactive({
    scoreDistribution: null,
    departmentComparison: null,
    trend: null,
    dimension: null,
  });

  // 查询表单配置
  const searchFormSchemas = [
    {
      field: 'dateRange',
      label: '评分周期',
      component: 'RangePicker',
      componentProps: {
        format: 'YYYY-MM',
        picker: 'month',
        placeholder: ['开始月份', '结束月份'],
      },
      colProps: { span: 8 },
    },
    {
      field: 'fbId',
      label: '分部',
      component: 'FbSelect',
      componentProps: {
        placeholder: '请选择分部',
      },
      colProps: { span: 8 },
    },
    {
      field: 'scoreRange',
      label: '得分范围',
      component: 'InputGroup',
      componentProps: {
        placeholder: ['最低分', '最高分'],
      },
      colProps: { span: 8 },
    },
  ];

  const [registerForm, { getFieldsValue, setFieldsValue }] = useForm({
    schemas: searchFormSchemas,
    labelWidth: 80,
    showActionButtonGroup: true,
    actionColOptions: { span: 24 },
    autoSubmitOnEnter: true,
  });

  // 个人绩效表格
  const [registerPersonalTable, { setTableData: setPersonalTableData }] = useTable({
    columns: [
      { title: '员工姓名', dataIndex: 'userName', width: 120 },
      { title: '所属分部', dataIndex: 'fbName', width: 120 },
      { title: '绩效得分', dataIndex: 'totalScore', width: 100 },
      { title: '排名', dataIndex: 'ranking', width: 80 },
      { title: '评分状态', dataIndex: 'status', width: 100 },
      { title: '评分周期', dataIndex: 'period', width: 120 },
    ],
    useSearchForm: false,
    showTableSetting: true,
    bordered: true,
    immediate: false,
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
  });

  // 分部绩效表格
  const [registerDepartmentTable, { setTableData: setDepartmentTableData }] = useTable({
    columns: [
      { title: '分部名称', dataIndex: 'fbName', width: 150 },
      { title: '参与人数', dataIndex: 'userCount', width: 100 },
      { title: '平均得分', dataIndex: 'avgScore', width: 100 },
      { title: '优秀率', dataIndex: 'excellentRate', width: 100 },
      { title: '完成率', dataIndex: 'completionRate', width: 100 },
    ],
    useSearchForm: false,
    showTableSetting: true,
    bordered: true,
    immediate: false,
  });

  // 绩效排名表格
  const [registerRankingTable, { setTableData: setRankingTableData }] = useTable({
    columns: [
      { title: '排名', dataIndex: 'ranking', width: 80 },
      { title: '员工姓名', dataIndex: 'userName', width: 120 },
      { title: '所属分部', dataIndex: 'fbName', width: 120 },
      { title: '绩效得分', dataIndex: 'totalScore', width: 100 },
      { title: '各维度得分', dataIndex: 'dimensionScores', ellipsis: true },
    ],
    useSearchForm: false,
    showTableSetting: true,
    bordered: true,
    immediate: false,
  });

  // 搜索处理
  function handleSearch() {
    const values = getFieldsValue();
    const params = {
      ...values,
      startMonth: values.dateRange?.[0]?.format('YYYY-MM'),
      endMonth: values.dateRange?.[1]?.format('YYYY-MM'),
    };
    loadData(params);

    // 重新加载当前激活的表格数据
    if (activeTab.value) {
      setTimeout(() => {
        loadTableData(activeTab.value);
      }, 500);
    }
  }

  // 重置处理
  function handleReset() {
    const now = new Date();
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const startMonth = new Date(lastMonth.getFullYear(), lastMonth.getMonth() - 2, 1);

    const defaultParams = {
      startMonth: startMonth.toISOString().slice(0, 7),
      endMonth: lastMonth.toISOString().slice(0, 7),
    };

    setFieldsValue(defaultParams);
    loadData(defaultParams);

    // 重新加载当前激活的表格数据
    if (activeTab.value) {
      setTimeout(() => {
        loadTableData(activeTab.value);
      }, 500);
    }
  }

  // 标签页切换
  function handleTabChange(key: string) {
    activeTab.value = key;
    setTimeout(() => {
      loadTableData(key);
    }, 100);
  }

  // 手动加载表格数据
  async function loadTableData(tableType: string) {
    try {
      const params = currentParams.value || {};

      switch (tableType) {
        case 'personal':
          const personalData = await getPersonalPerformanceList(params);
          if (personalData && personalData.data) {
            setPersonalTableData(personalData.data.list || []);
          }
          break;
        case 'department':
          const departmentData = await getDepartmentPerformanceList(params);
          if (departmentData && departmentData.data) {
            setDepartmentTableData(departmentData.data.list || []);
          }
          break;
        case 'ranking':
          const rankingData = await getPerformanceRankingList(params);
          if (rankingData && rankingData.data) {
            setRankingTableData(rankingData.data.list || []);
          }
          break;
      }
    } catch (error) {
      console.error(`加载${tableType}表格数据失败:`, error);
    }
  }

  // 查看详情
  function handleViewDetail(record: any) {
    console.log('查看详情:', record);
    createMessage.info('详情功能开发中...');
  }

  // 导出报表
  async function handleExport() {
    try {
      exportLoading.value = true;
      const values = getFieldsValue();
      await exportPerformanceAnalysis(values);
      createMessage.success('导出成功');
    } catch (error) {
      createMessage.error('导出失败');
    } finally {
      exportLoading.value = false;
    }
  }

  // 刷新数据
  function handleRefresh() {
    const values = getFieldsValue();
    const params = {
      ...values,
      startMonth: values.dateRange?.[0]?.format('YYYY-MM'),
      endMonth: values.dateRange?.[1]?.format('YYYY-MM'),
    };
    loadData(params);

    // 重新加载当前激活的表格数据
    if (activeTab.value) {
      setTimeout(() => {
        loadTableData(activeTab.value);
      }, 500);
    }
  }

  // 分组展示导出
  function handleGroupedExport(data: any) {
    console.log('导出分组数据:', data);
    createMessage.success('分组数据导出成功');
  }

  // 加载数据
  async function loadData(params: any = {}) {
    try {
      currentParams.value = { ...params };

      // 加载概览数据
      const overviewResponse = await getPerformanceAnalysisOverview(params);
      if (overviewResponse) {
        if (overviewResponse.data) {
          Object.assign(overview, overviewResponse.data);
        } else {
          Object.assign(overview, overviewResponse);
        }
      }

      // 加载图表数据
      const chartResponse = await getPerformanceAnalysisCharts(params);
      if (chartResponse) {
        if (chartResponse.data) {
          renderCharts(chartResponse.data);
        } else {
          renderCharts(chartResponse);
        }
      }

      console.log('概览和图表数据加载完成');
    } catch (error) {
      console.error('加载数据失败:', error);
      createMessage.error('加载数据失败');
    }
  }

  // 渲染图表
  function renderCharts(data: any) {
    if (!data || typeof data !== 'object') {
      console.warn('图表数据格式错误:', data);
      return;
    }

    nextTick(() => {
      renderScoreDistributionChart(data.scoreDistribution || []);
      renderDepartmentComparisonChart(data.departmentComparison || []);
      renderTrendChart(data.trend || []);
      renderDimensionChart(data.dimension || []);
    });
  }

  // 渲染得分分布图表
  function renderScoreDistributionChart(data: any[]) {
    if (!scoreDistributionChart.value || !Array.isArray(data)) return;

    if (chartInstances.scoreDistribution) {
      chartInstances.scoreDistribution.dispose();
    }

    chartInstances.scoreDistribution = echarts.init(scoreDistributionChart.value);

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c}人 ({d}%)',
      },
      series: [
        {
          name: '绩效得分分布',
          type: 'pie',
          radius: ['40%', '70%'],
          data: data.map(item => ({
            name: item.scoreRange || '未知',
            value: item.count || 0,
          })),
        },
      ],
    };

    chartInstances.scoreDistribution.setOption(option);
  }

  // 渲染分部对比图表
  function renderDepartmentComparisonChart(data: any[]) {
    if (!departmentComparisonChart.value || !Array.isArray(data)) return;

    if (chartInstances.departmentComparison) {
      chartInstances.departmentComparison.dispose();
    }

    chartInstances.departmentComparison = echarts.init(departmentComparisonChart.value);

    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'shadow' },
      },
      xAxis: {
        type: 'category',
        data: data.map(item => item.fbName || '未知分部'),
      },
      yAxis: {
        type: 'value',
        name: '平均得分',
      },
      series: [
        {
          name: '平均得分',
          type: 'bar',
          data: data.map(item => item.avgScore || 0),
          itemStyle: {
            color: '#1890ff',
          },
        },
      ],
    };

    chartInstances.departmentComparison.setOption(option);
  }

  // 渲染趋势图表
  function renderTrendChart(data: any[]) {
    if (!trendChart.value || !Array.isArray(data)) return;

    if (chartInstances.trend) {
      chartInstances.trend.dispose();
    }

    chartInstances.trend = echarts.init(trendChart.value);

    const option = {
      tooltip: {
        trigger: 'axis',
      },
      xAxis: {
        type: 'category',
        data: data.map(item => item.period || '未知周期'),
      },
      yAxis: {
        type: 'value',
        name: '平均得分',
      },
      series: [
        {
          name: '绩效趋势',
          type: 'line',
          data: data.map(item => item.avgScore || 0),
          smooth: true,
          itemStyle: {
            color: '#52c41a',
          },
        },
      ],
    };

    chartInstances.trend.setOption(option);
  }

  // 渲染维度分析图表
  function renderDimensionChart(data: any[]) {
    if (!dimensionChart.value || !Array.isArray(data)) return;

    if (chartInstances.dimension) {
      chartInstances.dimension.dispose();
    }

    chartInstances.dimension = echarts.init(dimensionChart.value);

    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'shadow' },
      },
      xAxis: {
        type: 'category',
        data: data.map(item => item.dimensionName || '未知维度'),
      },
      yAxis: {
        type: 'value',
        name: '平均得分',
      },
      series: [
        {
          name: '维度得分',
          type: 'bar',
          data: data.map(item => item.avgScore || 0),
          itemStyle: {
            color: '#faad14',
          },
        },
      ],
    };

    chartInstances.dimension.setOption(option);
  }

  onMounted(async () => {
    // 设置默认查询条件（最近3个月）
    const now = new Date();
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const startMonth = new Date(lastMonth.getFullYear(), lastMonth.getMonth() - 2, 1);

    const defaultParams = {
      startMonth: startMonth.toISOString().slice(0, 7),
      endMonth: lastMonth.toISOString().slice(0, 7),
    };

    await setFieldsValue(defaultParams);
    await nextTick();
    loadData(defaultParams);
  });
</script>

<style lang="less" scoped>
  .performance-analysis {
    .search-section {
      .ant-card {
        .ant-card-body {
          padding: 16px;
        }
      }
    }

    .overview-section {
      margin-top: 16px;

      .ant-card {
        text-align: center;

        .ant-statistic {
          .ant-statistic-title {
            font-size: 14px;
            color: #666;
          }

          .ant-statistic-content {
            font-size: 20px;
            font-weight: 600;
          }
        }
      }
    }

    .charts-section {
      margin-top: 16px;

      .ant-card {
        .ant-card-body {
          padding: 16px;
        }
      }
    }

    .table-section {
      margin-top: 16px;

      .ant-tabs {
        .ant-tabs-content-holder {
          padding-top: 16px;
        }
      }
    }
  }
</style>
