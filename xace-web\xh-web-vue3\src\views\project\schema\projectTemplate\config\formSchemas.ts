import { FormSchema } from '/@/components/Table';
import { h } from 'vue';
import { Button } from 'ant-design-vue';

// 基本信息表单配置
export const basicInfoFormSchema: FormSchema[] = [
  {
    field: 'name',
    label: '模板名称',
    component: 'Input',
    required: true,
    colProps: { span: 12 },
    componentProps: {
      placeholder: '请输入模板名称',
    },
    rules: [
      { required: true, message: '请输入模板名称' },
      { max: 50, message: '模板名称不能超过50个字符' },
    ],
  },
  {
    field: 'code',
    label: '模板编码',
    component: 'Input',
    required: true,
    colProps: { span: 12 },
    componentProps: {
      placeholder: '请输入模板编码',
    },
    rules: [
      { required: true, message: '请输入模板编码' },
      { max: 20, message: '模板编码不能超过20个字符' },
      { pattern: /^[A-Z0-9_]+$/, message: '模板编码只能包含大写字母、数字和下划线' },
    ],
    renderComponentContent: () => {
      return {
        suffix: () =>
          h(
            Button,
            {
              type: 'link',
              size: 'small',
              onClick: () => {
                // 这里需要在组件中通过 ref 调用生成编码方法
                console.log('生成编码');
              },
            },
            { default: () => '生成' },
          ),
      };
    },
  },
  {
    field: 'description',
    label: '模板描述',
    component: 'InputTextArea',
    colProps: { span: 24 },
    componentProps: {
      placeholder: '请输入模板描述',
      rows: 3,
      maxlength: 200,
      showCount: true,
    },
  },
  {
    field: 'type',
    label: '模板类型',
    component: 'Select',
    colProps: { span: 12 },
    componentProps: {
      placeholder: '请选择模板类型',
      options: [
        { label: '软件开发', value: 'SOFTWARE' },
        { label: '工程建设', value: 'CONSTRUCTION' },
        { label: '产品研发', value: 'PRODUCT' },
        { label: '活动策划', value: 'EVENT' },
        { label: '其他', value: 'OTHER' },
      ],
    },
  },
  {
    field: 'status',
    label: '模板状态',
    component: 'RadioButtonGroup',
    defaultValue: 0,
    colProps: { span: 12 },
    componentProps: {
      options: [
        { label: '启用', value: 0 },
        { label: '禁用', value: 1 },
      ],
    },
  },
  {
    field: 'tags',
    label: '标签',
    component: 'Select',
    colProps: { span: 12 },
    componentProps: {
      mode: 'tags',
      placeholder: '请输入标签，按回车添加',
      maxTagCount: 5,
    },
  },
  {
    field: 'priority',
    label: '优先级',
    component: 'Select',
    colProps: { span: 12 },
    componentProps: {
      placeholder: '请选择优先级',
      options: [
        { label: '高', value: 'HIGH' },
        { label: '中', value: 'MEDIUM' },
        { label: '低', value: 'LOW' },
      ],
    },
    defaultValue: 'MEDIUM',
  },
  {
    field: 'createdAt',
    label: '创建时间',
    component: 'Input',
    colProps: { span: 12 },
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'createdBy',
    label: '创建人',
    component: 'Input',
    colProps: { span: 12 },
    componentProps: {
      disabled: true,
    },
  },
];

// 阶段管理表单配置
export const phaseFormSchema: FormSchema[] = [
  {
    field: 'name',
    label: '阶段名称',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入阶段名称',
    },
    rules: [
      { required: true, message: '请输入阶段名称' },
      { max: 50, message: '阶段名称不能超过50个字符' },
    ],
  },
  {
    field: 'code',
    label: '阶段编码',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入阶段编码',
    },
    rules: [
      { required: true, message: '请输入阶段编码' },
      { max: 20, message: '阶段编码不能超过20个字符' },
      { pattern: /^[A-Z0-9_]+$/, message: '阶段编码只能包含大写字母、数字和下划线' },
    ],
  },
  {
    field: 'description',
    label: '阶段描述',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入阶段描述',
      rows: 3,
      maxlength: 200,
      showCount: true,
    },
  },
  {
    field: 'duration',
    label: '预估工期(天)',
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入预估工期',
      min: 1,
      max: 9999,
    },
  },
  {
    field: 'prerequisitePhases',
    label: '前置阶段',
    component: 'Select',
    componentProps: {
      mode: 'multiple',
      placeholder: '请选择前置阶段',
      options: [], // 需要在组件中动态加载
    },
  },
  {
    field: 'isMilestone',
    label: '里程碑标记',
    component: 'Switch',
    componentProps: {
      checkedChildren: '是',
      unCheckedChildren: '否',
    },
    defaultValue: false,
  },
  {
    field: 'sortOrder',
    label: '排序',
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入排序号',
      min: 1,
      max: 999,
    },
  },
];

// 交付物管理表单配置
export const deliverableFormSchema: FormSchema[] = [
  {
    field: 'name',
    label: '交付物名称',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入交付物名称',
    },
    rules: [
      { required: true, message: '请输入交付物名称' },
      { max: 100, message: '交付物名称不能超过100个字符' },
    ],
  },
  {
    field: 'code',
    label: '交付物编码',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入交付物编码',
    },
    rules: [
      { required: true, message: '请输入交付物编码' },
      { max: 20, message: '交付物编码不能超过20个字符' },
      { pattern: /^[A-Z0-9_]+$/, message: '交付物编码只能包含大写字母、数字和下划线' },
    ],
  },
  {
    field: 'description',
    label: '交付物描述',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入交付物描述',
      rows: 3,
      maxlength: 200,
      showCount: true,
    },
  },
  {
    field: 'phaseId',
    label: '所属阶段',
    component: 'Select',
    required: true,
    componentProps: {
      placeholder: '请选择所属阶段',
      options: [], // 需要在组件中动态加载
    },
    rules: [{ required: true, message: '请选择所属阶段' }],
  },
  {
    field: 'responsibleRole',
    label: '负责角色',
    component: 'Select',
    componentProps: {
      placeholder: '请选择负责角色',
      options: [
        { label: '项目经理', value: 'PM' },
        { label: '技术负责人', value: 'TECH_LEAD' },
        { label: '开发工程师', value: 'DEVELOPER' },
        { label: '测试工程师', value: 'TESTER' },
        { label: '产品经理', value: 'PRODUCT_MANAGER' },
        { label: '设计师', value: 'DESIGNER' },
      ],
    },
  },
  {
    field: 'templateFile',
    label: '模板文件',
    component: 'Upload',
    componentProps: {
      api: '', // 需要配置上传接口
      maxSize: 10,
      maxCount: 1,
      accept: ['.doc', '.docx', '.pdf', '.xls', '.xlsx'],
    },
  },
  {
    field: 'checkStandards',
    label: '检查标准',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入检查标准',
      rows: 3,
      maxlength: 500,
      showCount: true,
    },
  },
  {
    field: 'sortOrder',
    label: '排序',
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入排序号',
      min: 1,
      max: 999,
    },
  },
];
