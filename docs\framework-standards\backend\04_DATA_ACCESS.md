# DAO/Mapper 层规范

2025-07-21 已确认

## 📋 核心规范总览

**XACE项目数据访问架构原则：**
1. **Mapper接口简洁化**：仅继承 `XHBaseMapper<T>`，不定义自定义方法
2. **业务逻辑隔离**：所有查询逻辑在Service层使用LambdaQueryWrapper实现
3. **特殊场景例外**：极复杂的多表联查或特殊SQL函数才考虑自定义SQL
4. **数据库无关性**：优先使用MyBatis-Plus标准方法，保证多数据库兼容

## 🚨 重要原则：职责分离策略

**强制要求：Mapper层只负责数据访问接口定义，业务查询逻辑全部在Service层实现。**

## 基本结构

DAO/Mapper层负责定义数据库访问操作接口，基于MyBatis-Plus框架实现标准化的数据访问。

### 位置与命名

* **包路径:** `com.xinghuo.[模块名].dao` 
* **xml文件包路径:** `com.xinghuo.[模块名].dao.mapper` 
* **命名规范:** 以 `Mapper` 结尾，如 `UserMapper`，`ProductCategoryMapper`

### 基础设置

* 继承自 `XHBaseMapper<T>` 接口，获取基本CRUD操作
* 使用 `@Mapper` 注解或在配置类中统一扫描
* **不建议**定义自定义SQL方法，所有查询逻辑在ServiceImpl中实现
* **LambdaQueryWrapper使用指南**：详细的查询实现方法请参考 [Service层规范](./05_SERVICE_LAYER.md#-lambdaquerywrapper-使用指南)

## ✅ 推荐做法：简洁的Mapper接口

```java
import com.xinghuo.common.base.dao.XHBaseMapper;

/**
 * 产品分类Mapper接口
 * 
 * XACE数据访问层标准：
 * - 继承XHBaseMapper获得标准CRUD能力
 * - 不定义自定义查询方法
 * - 所有业务查询逻辑在Service层使用LambdaQueryWrapper实现
 * 
 * @author： XACE团队
 * @date： 2025-01-01
 */
@Mapper
public interface ProductCategoryMapper extends XHBaseMapper<ProductCategoryEntity> {
    // 不定义任何自定义方法
    // 所有查询操作通过Service层的LambdaQueryWrapper实现
}
```

## XHBaseMapper 核心能力

继承 `XHBaseMapper<T>` 后，自动获得以下标准数据访问能力：

### 基础CRUD操作
```java
// 插入操作
int insert(T entity);                           // 插入一条记录
int insertBatchSomeColumn(List<T> entityList);  // 批量插入

// 查询操作  
T selectById(Serializable id);                  // 根据ID查询
List<T> selectBatchIds(Collection<? extends Serializable> idList);  // 批量ID查询
List<T> selectByMap(Map<String, Object> columnMap);  // 根据columnMap查询
T selectOne(Wrapper<T> queryWrapper);           // 查询一条记录
List<T> selectList(Wrapper<T> queryWrapper);    // 查询列表
IPage<T> selectPage(IPage<T> page, Wrapper<T> queryWrapper);  // 分页查询

// 更新操作
int updateById(T entity);                       // 根据ID更新
int update(T entity, Wrapper<T> updateWrapper); // 根据条件更新

// 删除操作（逻辑删除）
int deleteById(Serializable id);                // 根据ID删除
int deleteBatchIds(Collection<? extends Serializable> idList);  // 批量删除
int delete(Wrapper<T> queryWrapper);            // 根据条件删除

// 统计操作
Integer selectCount(Wrapper<T> queryWrapper);   // 统计记录数
```

### 与BaseEntityV2集成
```java
// XHBaseMapper自动支持BaseEntityV2的特性：
// - 逻辑删除：查询时自动过滤deleteMark=1的记录
// - 多租户：自动添加租户隔离条件
// - 审计字段：自动填充创建/更新时间和操作人
// - 版本控制：支持乐观锁机制
```

## 数据库支持

XACE项目支持多种数据库，Mapper层设计保证数据库无关性：

### 支持的数据库类型
- **MySQL** 5.7+ (推荐)
- **SQL Server** 2012+  
- **Oracle** 11g+
- **PostgreSQL** 12+
- **达梦DM** 8.0+
- **人大金仓KingbaseES** V8.6+

### 数据库字段约定
```java
// 系统标准字段统一使用F_前缀：
// F_ID              - 主键ID
// F_FULLNAME        - 显示名称  
// F_ACCOUNT         - 账号
// F_CREATED_AT      - 创建时间
// F_LAST_UPDATED_AT - 更新时间
// F_CREATED_BY      - 创建人
// F_LAST_UPDATED_BY - 更新人
// F_DELETE_MARK     - 删除标记(0=正常,1=删除)
// F_DELETED_AT      - 删除时间
// F_DELETED_BY      - 删除人
```

### 多数据库兼容性
```java
// XHBaseMapper内置多数据库兼容性：
// - 自动识别数据库类型
// - 使用标准SQL语法
// - 分页查询自动适配不同数据库的分页语法
// - 数据类型映射自动处理
```

## 🚨 何时可以使用自定义SQL

仅在以下极特殊情况下才在Mapper接口中定义自定义方法：

### 适用场景
1. **复杂多表联查**：涉及5个以上表的复杂关联查询
2. **数据库特有函数**：必须使用特定数据库的专有函数或语法
3. **复杂聚合统计**：涉及多层嵌套、窗口函数等复杂聚合
4. **性能关键查询**：经过测试证明LambdaQueryWrapper性能不满足要求

### 自定义SQL示例（极特殊场景）
```java
@Mapper
public interface ReportDataMapper extends XHBaseMapper<ReportDataEntity> {
    
    /**
     * 复杂多表统计查询（仅当LambdaQueryWrapper无法实现时使用）
     */
    List<ComplexReportVO> selectComplexReport(@Param("params") ReportParams params);
}
```

> **注意**：90%以上的查询都应该在Service层使用LambdaQueryWrapper实现，仅在极复杂的报表统计等特殊场景才考虑自定义SQL。

* **Controller层职责**：
  - 调用Service方法处理业务
  - 不直接操作Mapper接口

### 2. 接口设计规范

* **命名规范**：
  - Mapper接口：`[业务域]Mapper`，如 `UserMapper`
  - 方法命名：遵循MyBatis-Plus标准，如 `selectById`、`insert`

* **注解使用**：
  - 必须使用 `@Mapper` 注解
  - 自定义方法可使用 `@Select`、`@Insert`、`@Update`、`@Delete`

### 3. 数据库兼容性原则

* **标准SQL优先**：使用标准SQL语法，避免数据库特有语法
* **类型映射**：使用MyBatis-Plus标准类型映射
* **分页处理**：使用MyBatis-Plus分页插件，自动适配不同数据库

### 4. 性能考虑

* **索引利用**：确保查询条件字段有合适的索引
* **查询优化**：复杂查询考虑使用数据库视图
* **连接池配置**：合理配置数据库连接池参数

## ✅ 最佳实践指南

### 1. 开发流程

1. **创建Entity实体类**：定义数据模型，继承BaseEntityV2
2. **创建Mapper接口**：仅继承 `XHBaseMapper<T>`，不定义自定义方法
3. **Service层实现查询**：使用LambdaQueryWrapper
4. **单元测试**：测试数据访问的正确性
5. **集成测试**：验证多数据库兼容性

### 2. 标准模板

```java
// Mapper接口标准模板
/**
 * [业务域]数据访问接口
 * 
 * XACE数据访问层标准实现：
 * - 继承XHBaseMapper获得标准CRUD能力
 * - 支持逻辑删除和多租户
 * - 兼容多种数据库
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Mapper
public interface [BusinessDomain]Mapper extends XHBaseMapper<[BusinessDomain]Entity> {
    // 保持接口简洁，不定义自定义方法
    // 所有查询逻辑在对应的Service层实现
}
```

### 3. 推荐做法

1. **接口简洁**：Mapper接口只继承XHBaseMapper，不添加自定义方法
2. **业务隔离**：查询逻辑全部在Service层使用QueryWrapper实现
3. **标准CRUD**：充分利用MyBatis-Plus内置的CRUD方法
4. **多数据库兼容**：避免使用数据库特有的SQL语法
5. **性能优化**：合理设计索引，使用分页查询

### 4. 避免的做法

1. ❌ 在Mapper接口中定义复杂的自定义查询方法
2. ❌ 在Mapper层处理业务逻辑
3. ❌ 使用数据库特有的SQL语法（除非必需）
4. ❌ 忽略逻辑删除和多租户支持
5. ❌ 在Controller中直接调用Mapper方法

## 📝 总结

### 核心要点

1. **接口简洁化**：Mapper接口仅继承XHBaseMapper，不定义自定义方法
2. **职责分离**：数据访问和业务逻辑严格分离
3. **多数据库支持**：使用标准SQL保证兼容性  
4. **特殊场景例外**：极复杂查询才考虑自定义SQL

### 开发检查清单

- [ ] Mapper接口仅继承XHBaseMapper<T>
- [ ] 不定义自定义查询方法
- [ ] 使用@Mapper注解标识
- [ ] 遵循标准的包结构和命名规范
- [ ] 配置正确的数据源和MyBatis-Plus参数
- [ ] 支持逻辑删除和多租户

**重要提醒：所有查询逻辑实现请参考 [05_SERVICE_LAYER.md](./05_SERVICE_LAYER.md) 中的QueryWrapper使用指南。**

遵循以上规范，可以构建出简洁、高效、可维护的数据访问层。