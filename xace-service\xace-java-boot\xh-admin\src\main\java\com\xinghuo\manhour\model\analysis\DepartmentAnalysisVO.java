package com.xinghuo.manhour.model.analysis;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 分部分析数据VO
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@Schema(description = "分部分析数据")
public class DepartmentAnalysisVO {

    @Schema(description = "分部ID")
    private String fbId;

    @Schema(description = "分部名称")
    private String fbName;

    @Schema(description = "总工时")
    private BigDecimal totalWorkMonth;

    @Schema(description = "人员数量")
    private Integer userCount;

    @Schema(description = "项目数量")
    private Integer projectCount;

    @Schema(description = "资源利用率")
    private BigDecimal utilizationRate;

    @Schema(description = "主要项目类型")
    private String mainProjType;

    @Schema(description = "平均效率")
    private BigDecimal avgEfficiency;

    @Schema(description = "分部负责人")
    private String fbLeader;

    @Schema(description = "人均工时")
    private BigDecimal avgUserWorkMonth;

    @Schema(description = "项目分布均匀度")
    private BigDecimal projectDistributionBalance;

    @Schema(description = "团队协作度")
    private BigDecimal teamCollaboration;

    @Schema(description = "工时完成率")
    private BigDecimal completionRate;
}
