<template>
  <div class="template-deliverable-page p-6">
    <div class="max-w-7xl mx-auto">
      <!-- 页面标题 -->
      <div class="header-section mb-6">
        <div class="flex justify-between items-center">
          <div>
            <h1 class="text-2xl font-bold mb-2">交付物配置</h1>
            <p class="text-gray-600">配置项目模板的交付物和文档管理</p>
          </div>
          <a-button type="primary" @click="handleAddDeliverable">
            <template #icon><PlusOutlined /></template>
            添加交付物
          </a-button>
        </div>
      </div>

      <!-- 交付物分类 -->
      <a-row :gutter="16" class="mb-6">
        <a-col :span="6">
          <a-card size="small">
            <a-statistic title="需求文档" :value="stats.requirements" suffix="个" />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small">
            <a-statistic title="设计文档" :value="stats.designs" suffix="个" />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small">
            <a-statistic title="开发交付" :value="stats.developments" suffix="个" />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small">
            <a-statistic title="测试交付" :value="stats.tests" suffix="个" />
          </a-card>
        </a-col>
      </a-row>

      <!-- 交付物列表 -->
      <a-card title="交付物管理" :bordered="false">
        <template #extra>
          <a-space>
            <a-input-search v-model:value="searchKeyword" placeholder="搜索交付物名称" style="width: 200px" @search="handleSearch" />
            <a-select v-model:value="filterCategory" placeholder="分类筛选" style="width: 120px" allow-clear>
              <a-select-option value="requirement">需求</a-select-option>
              <a-select-option value="design">设计</a-select-option>
              <a-select-option value="development">开发</a-select-option>
              <a-select-option value="test">测试</a-select-option>
            </a-select>
          </a-space>
        </template>

        <a-table :columns="deliverableColumns" :data-source="filteredDeliverables" :loading="loading" :pagination="{ pageSize: 10 }" row-key="id" size="small">
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.key === 'seqNo'">
              {{ index + 1 }}
            </template>
            <template v-else-if="column.key === 'category'">
              <a-tag :color="getCategoryColor(record.category)">
                {{ getCategoryName(record.category) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'isRequired'">
              <a-tag :color="record.isRequired ? 'red' : 'default'">
                {{ record.isRequired ? '必需' : '可选' }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'status'">
              <a-tag :color="record.status === 1 ? 'green' : 'default'">
                {{ record.status === 1 ? '启用' : '禁用' }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="handleEditDeliverable(record)"> 编辑 </a-button>
                <a-button type="link" size="small" @click="handlePreview(record)"> 预览 </a-button>
                <a-button type="link" size="small" danger @click="handleDeleteDeliverable(record)"> 删除 </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 交付物编辑弹窗 -->
    <a-modal
      v-model:open="deliverableModalVisible"
      :title="deliverableEditMode === 'add' ? '添加交付物' : '编辑交付物'"
      width="700px"
      @ok="handleSaveDeliverable"
      @cancel="handleCancelDeliverable">
      <a-form :model="deliverableForm" :rules="deliverableRules" layout="vertical">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="交付物名称" name="name" required>
              <a-input v-model:value="deliverableForm.name" placeholder="请输入交付物名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="交付物编码" name="code">
              <a-input v-model:value="deliverableForm.code" placeholder="请输入交付物编码" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="分类" name="category">
              <a-select v-model:value="deliverableForm.category" placeholder="选择分类">
                <a-select-option value="requirement">需求</a-select-option>
                <a-select-option value="design">设计</a-select-option>
                <a-select-option value="development">开发</a-select-option>
                <a-select-option value="test">测试</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="是否必需" name="isRequired">
              <a-radio-group v-model:value="deliverableForm.isRequired">
                <a-radio :value="true">必需</a-radio>
                <a-radio :value="false">可选</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="状态" name="status">
              <a-radio-group v-model:value="deliverableForm.status">
                <a-radio :value="1">启用</a-radio>
                <a-radio :value="0">禁用</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item label="文件格式" name="fileFormats">
          <a-select v-model:value="deliverableForm.fileFormats" mode="multiple" placeholder="选择支持的文件格式">
            <a-select-option value="doc">Word文档</a-select-option>
            <a-select-option value="pdf">PDF文档</a-select-option>
            <a-select-option value="xls">Excel表格</a-select-option>
            <a-select-option value="ppt">PowerPoint</a-select-option>
            <a-select-option value="jpg">图片</a-select-option>
            <a-select-option value="zip">压缩包</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="交付物描述" name="description">
          <a-textarea v-model:value="deliverableForm.description" :rows="4" />
        </a-form-item>
        <a-form-item label="模板路径" name="templatePath">
          <a-input v-model:value="deliverableForm.templatePath" placeholder="文档模板文件路径（可选）" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted } from 'vue';
  import {
    Card as ACard,
    Row as ARow,
    Col as ACol,
    Statistic as AStatistic,
    Table as ATable,
    Button as AButton,
    Space as ASpace,
    Tag as ATag,
    InputSearch as AInputSearch,
    Select as ASelect,
    SelectOption as ASelectOption,
    Modal as AModal,
    Form as AForm,
    FormItem as AFormItem,
    Input as AInput,
    Textarea as ATextarea,
    Radio as ARadio,
    RadioGroup as ARadioGroup,
  } from 'ant-design-vue';
  import { PlusOutlined } from '@ant-design/icons-vue';
  import { useMessage } from '/@/hooks/web/useMessage';

  interface Props {
    templateId: string;
  }

  const props = defineProps<Props>();
  const { createMessage, createConfirm } = useMessage();

  // 响应式数据
  const deliverableList = ref([]);
  const loading = ref(false);
  const searchKeyword = ref('');
  const filterCategory = ref(undefined);
  const deliverableModalVisible = ref(false);
  const deliverableEditMode = ref('add');
  const deliverableForm = reactive({
    id: '',
    name: '',
    code: '',
    category: 'requirement',
    isRequired: true,
    status: 1,
    fileFormats: [],
    description: '',
    templatePath: '',
  });

  // 统计数据
  const stats = computed(() => {
    const requirements = deliverableList.value.filter(item => item.category === 'requirement').length;
    const designs = deliverableList.value.filter(item => item.category === 'design').length;
    const developments = deliverableList.value.filter(item => item.category === 'development').length;
    const tests = deliverableList.value.filter(item => item.category === 'test').length;

    return { requirements, designs, developments, tests };
  });

  // 过滤后的交付物列表
  const filteredDeliverables = computed(() => {
    let result = deliverableList.value;

    if (searchKeyword.value) {
      const keyword = searchKeyword.value.toLowerCase();
      result = result.filter(item => item.name?.toLowerCase().includes(keyword) || item.code?.toLowerCase().includes(keyword));
    }

    if (filterCategory.value) {
      result = result.filter(item => item.category === filterCategory.value);
    }

    return result;
  });

  // 表格列定义
  const deliverableColumns = [
    { title: '序号', key: 'seqNo', width: 60, align: 'center' },
    { title: '交付物名称', dataIndex: 'name', width: 180 },
    { title: '编码', dataIndex: 'code', width: 120 },
    { title: '分类', key: 'category', width: 100, align: 'center' },
    { title: '是否必需', key: 'isRequired', width: 100, align: 'center' },
    { title: '状态', key: 'status', width: 80, align: 'center' },
    { title: '文件格式', dataIndex: 'fileFormats', width: 150 },
    { title: '描述', dataIndex: 'description' },
    { title: '操作', key: 'action', width: 150, align: 'center', fixed: 'right' },
  ];

  // 表单验证规则
  const deliverableRules = {
    name: [{ required: true, message: '请输入交付物名称', trigger: 'blur' }],
  };

  // 方法
  function getCategoryColor(category: string) {
    const colors = {
      requirement: 'blue',
      design: 'green',
      development: 'orange',
      test: 'purple',
    };
    return colors[category] || 'default';
  }

  function getCategoryName(category: string) {
    const names = {
      requirement: '需求',
      design: '设计',
      development: '开发',
      test: '测试',
    };
    return names[category] || category;
  }

  function handleSearch() {
    // 搜索逻辑在computed中处理
  }

  function handleAddDeliverable() {
    resetDeliverableForm();
    deliverableEditMode.value = 'add';
    deliverableModalVisible.value = true;
  }

  function handleEditDeliverable(record: any) {
    Object.assign(deliverableForm, record);
    deliverableEditMode.value = 'edit';
    deliverableModalVisible.value = true;
  }

  function handlePreview(record: any) {
    createMessage.info(`预览交付物：${record.name}（功能开发中...）`);
  }

  function handleDeleteDeliverable(record: any) {
    createConfirm({
      title: '确认删除',
      content: `确定要删除交付物"${record.name}"吗？`,
      onOk: () => {
        createMessage.info('删除功能开发中...');
      },
    });
  }

  function handleSaveDeliverable() {
    createMessage.info('保存功能开发中...');
    deliverableModalVisible.value = false;
  }

  function handleCancelDeliverable() {
    deliverableModalVisible.value = false;
    resetDeliverableForm();
  }

  function resetDeliverableForm() {
    Object.assign(deliverableForm, {
      id: '',
      name: '',
      code: '',
      category: 'requirement',
      isRequired: true,
      status: 1,
      fileFormats: [],
      description: '',
      templatePath: '',
    });
  }

  // 加载交付物配置
  async function loadDeliverables() {
    if (!props.templateId) return;

    try {
      loading.value = true;
      // TODO: 调用API加载交付物数据
      // 模拟数据
      deliverableList.value = [
        {
          id: '1',
          name: '需求规格说明书',
          code: 'SRS',
          category: 'requirement',
          isRequired: true,
          status: 1,
          fileFormats: ['doc', 'pdf'],
          description: '详细的软件需求规格说明文档',
        },
        {
          id: '2',
          name: '系统设计文档',
          code: 'SDD',
          category: 'design',
          isRequired: true,
          status: 1,
          fileFormats: ['doc', 'pdf'],
          description: '系统架构和详细设计文档',
        },
        {
          id: '3',
          name: '用户手册',
          code: 'UM',
          category: 'development',
          isRequired: false,
          status: 1,
          fileFormats: ['doc', 'pdf'],
          description: '最终用户使用手册',
        },
        {
          id: '4',
          name: '测试报告',
          code: 'TR',
          category: 'test',
          isRequired: true,
          status: 1,
          fileFormats: ['doc', 'pdf', 'xls'],
          description: '系统测试报告和测试结果',
        },
      ];
    } catch (error) {
      console.error('加载交付物配置失败:', error);
      createMessage.error('加载交付物配置失败');
    } finally {
      loading.value = false;
    }
  }

  // 初始化
  onMounted(() => {
    loadDeliverables();
  });
</script>

<style scoped>
  .template-deliverable-page {
    background: #f5f5f5;
    min-height: 100vh;
  }

  :deep(.ant-card-head) {
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
  }

  :deep(.ant-table-thead > tr > th) {
    background: #fafafa;
    font-weight: 600;
  }
</style>
