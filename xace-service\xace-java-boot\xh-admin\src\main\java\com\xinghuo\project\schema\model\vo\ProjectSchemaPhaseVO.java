package com.xinghuo.project.schema.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 项目模板阶段配置视图对象
 * 用于返回阶段配置列表和详情信息
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
@Data
@Schema(description = "项目模板阶段配置视图对象")
public class ProjectSchemaPhaseVO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private String id;

    /**
     * 所属项目模板ID
     */
    @Schema(description = "所属项目模板ID")
    private String projectTemplateId;

    /**
     * 项目模板名称（冗余字段，便于显示）
     */
    @Schema(description = "项目模板名称")
    private String projectTemplateName;

    /**
     * 关联的标准阶段ID
     */
    @Schema(description = "关联的标准阶段ID")
    private String phaseLibraryId;

    /**
     * 阶段编码（来自阶段库，冗余字段）
     */
    @Schema(description = "阶段编码")
    private String phaseCode;

    /**
     * 阶段名称（来自阶段库，冗余字段）
     */
    @Schema(description = "阶段名称")
    private String phaseName;

    /**
     * 阶段描述（来自阶段库，冗余字段）
     */
    @Schema(description = "阶段描述")
    private String phaseDescription;

    /**
     * 标准工期（来自阶段库，冗余字段）
     */
    @Schema(description = "标准工期（天）")
    private Integer phaseStdDuration;

    /**
     * 该阶段在此模板中的顺序
     */
    @Schema(description = "序号")
    private Integer seqNo;

    /**
     * 此模板中该阶段的计划工期(天)
     */
    @Schema(description = "计划工期（天）")
    private Integer duration;

    /**
     * 此模板中该阶段占项目总体的权重(%)
     */
    @Schema(description = "完成权重（%）")
    private Integer completionWeight;

    /**
     * 是否可裁剪 (1:是, 0:否)
     */
    @Schema(description = "是否可裁剪")
    private Integer canCut;

    /**
     * 是否可裁剪名称
     */
    @Schema(description = "是否可裁剪名称")
    private String canCutName;

    /**
     * 此模板中该阶段的审批流程ID
     */
    @Schema(description = "审批流程ID")
    private String approvalId;

    /**
     * 审批流程名称（冗余字段，便于显示）
     */
    @Schema(description = "审批流程名称")
    private String approvalName;

    /**
     * 此模板中该阶段的质量检查模板ID
     */
    @Schema(description = "检查单模板ID")
    private String checklistId;

    /**
     * 检查单模板名称（冗余字段，便于显示）
     */
    @Schema(description = "检查单模板名称")
    private String checklistName;

    /**
     * 此模板中该阶段关联的交付物清单模板ID
     */
    @Schema(description = "交付物模板ID")
    private String workproductTplId;

    /**
     * 交付物模板名称（冗余字段，便于显示）
     */
    @Schema(description = "交付物模板名称")
    private String workproductTplName;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private String tenantId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    /**
     * 创建用户ID
     */
    @Schema(description = "创建用户ID")
    private String createdBy;

    /**
     * 创建用户名称 (冗余字段，便于显示)
     */
    @Schema(description = "创建用户名称")
    private String createdByName;

    /**
     * 最后修改时间
     */
    @Schema(description = "最后修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    /**
     * 最后修改用户ID
     */
    @Schema(description = "最后修改用户ID")
    private String updatedBy;

    /**
     * 最后修改用户名称 (冗余字段，便于显示)
     */
    @Schema(description = "最后修改用户名称")
    private String updatedByName;
}
