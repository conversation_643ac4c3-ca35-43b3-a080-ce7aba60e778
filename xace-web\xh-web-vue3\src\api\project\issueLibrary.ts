import { defHttp } from '/@/utils/http/axios';

enum Api {
  GetList = '/api/project/template/issueLibrary/getList',
  GetListByStatus = '/api/project/template/issueLibrary/getListByStatus',
  GetListByIssueCategory = '/api/project/template/issueLibrary/getListByIssueCategory',
  GetListByPriority = '/api/project/template/issueLibrary/getListByPriority',
  GetInfo = '/api/project/template/issueLibrary/getInfo',
  GetByCode = '/api/project/template/issueLibrary/getByCode',
  Create = '/api/project/template/issueLibrary/create',
  Update = '/api/project/template/issueLibrary/update',
  Delete = '/api/project/template/issueLibrary/delete',
  BatchDelete = '/api/project/template/issueLibrary/batchDelete',
  UpdateStatus = '/api/project/template/issueLibrary/updateStatus',
  BatchUpdateStatus = '/api/project/template/issueLibrary/batchUpdateStatus',
  Publish = '/api/project/template/issueLibrary/publish',
  Archive = '/api/project/template/issueLibrary/archive',
  BatchPublish = '/api/project/template/issueLibrary/batchPublish',
  BatchArchive = '/api/project/template/issueLibrary/batchArchive',
  Copy = '/api/project/template/issueLibrary/copy',
  CheckCodeExists = '/api/project/template/issueLibrary/checkCodeExists',
  CheckTitleExists = '/api/project/template/issueLibrary/checkTitleExists',
  GetSelectList = '/api/project/template/issueLibrary/getSelectList',
  GenerateCode = '/api/project/template/issueLibrary/generateCode',
  GetUsageInfo = '/api/project/template/issueLibrary/getIssueLibraryUsageInfo',
}

/**
 * 标准项目问题库接口
 */

// 获取问题库列表
export function getIssueLibraryList(params: any) {
  return defHttp.post({
    url: Api.GetList,
    data: params,
  });
}

// 根据状态获取问题库列表
export function getIssueLibraryListByStatus(status: string) {
  return defHttp.get({
    url: `${Api.GetListByStatus}/${status}`,
  });
}

// 根据问题类别获取问题库列表
export function getIssueLibraryListByIssueCategory(issueCategoryId: string) {
  return defHttp.get({
    url: `${Api.GetListByIssueCategory}/${issueCategoryId}`,
  });
}

// 根据优先级获取问题库列表
export function getIssueLibraryListByPriority(priorityId: string) {
  return defHttp.get({
    url: `${Api.GetListByPriority}/${priorityId}`,
  });
}

// 获取问题库详情
export function getIssueLibraryInfo(id: string) {
  return defHttp.get({
    url: `${Api.GetInfo}/${id}`,
  });
}

// 根据编码获取问题库
export function getIssueLibraryByCode(code: string) {
  return defHttp.get({
    url: `${Api.GetByCode}/${code}`,
  });
}

// 创建问题库
export function createIssueLibrary(params: any) {
  return defHttp.post({
    url: Api.Create,
    data: params,
  });
}

// 更新问题库
export function updateIssueLibrary(id: string, params: any) {
  return defHttp.put({
    url: `${Api.Update}/${id}`,
    data: params,
  });
}

// 删除问题库
export function deleteIssueLibrary(id: string) {
  return defHttp.delete({
    url: `${Api.Delete}/${id}`,
  });
}

// 批量删除问题库
export function batchDeleteIssueLibrary(ids: string[]) {
  return defHttp.delete({
    url: Api.BatchDelete,
    data: ids,
  });
}

// 更新问题库状态
export function updateIssueLibraryStatus(id: string, status: string) {
  return defHttp.put({
    url: `${Api.UpdateStatus}/${id}?status=${status}`,
  });
}

// 批量更新状态
export function batchUpdateIssueLibraryStatus(ids: string[], status: string) {
  return defHttp.put({
    url: `${Api.BatchUpdateStatus}?status=${status}`,
    data: ids,
  });
}

// 发布问题库
export function publishIssueLibrary(id: string) {
  return defHttp.put({
    url: `${Api.Publish}/${id}`,
  });
}

// 归档问题库
export function archiveIssueLibrary(id: string) {
  return defHttp.put({
    url: `${Api.Archive}/${id}`,
  });
}

// 批量发布问题库
export function batchPublishIssueLibrary(ids: string[]) {
  return defHttp.put({
    url: Api.BatchPublish,
    data: ids,
  });
}

// 批量归档问题库
export function batchArchiveIssueLibrary(ids: string[]) {
  return defHttp.put({
    url: Api.BatchArchive,
    data: ids,
  });
}

// 复制问题库
export function copyIssueLibrary(id: string, newTitle: string) {
  return defHttp.post({
    url: `${Api.Copy}/${id}?newTitle=${encodeURIComponent(newTitle)}`,
  });
}

// 检查问题编码是否存在
export function checkIssueLibraryCodeExists(code: string, excludeId?: string) {
  return defHttp.get({
    url: Api.CheckCodeExists,
    params: { code, excludeId },
  });
}

// 检查问题标题是否存在
export function checkIssueLibraryTitleExists(title: string, excludeId?: string) {
  return defHttp.get({
    url: Api.CheckTitleExists,
    params: { title, excludeId },
  });
}

// 获取问题库选择列表
export function getIssueLibrarySelectList(keyword?: string) {
  return defHttp.get({
    url: Api.GetSelectList,
    params: { keyword },
  });
}

// 生成问题编码
export function generateIssueLibraryCode() {
  return defHttp.get({
    url: Api.GenerateCode,
  });
}

// 获取问题库使用情况
export function getIssueLibraryUsageInfo(id: string) {
  return defHttp.get({
    url: `${Api.GetUsageInfo}/${id}`,
  });
}
