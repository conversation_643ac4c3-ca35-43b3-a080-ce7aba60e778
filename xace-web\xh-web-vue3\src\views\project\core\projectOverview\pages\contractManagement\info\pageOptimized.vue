<template>
  <div class="contract-info-management">
    <div class="page-header">
      <div class="flex items-center">
        <i class="icon-ym icon-ym-contract mr-2 text-lg"></i>
        <span class="text-base font-medium">合同信息</span>
      </div>
      <div class="flex items-center space-x-2">
        <a-button type="primary" @click="handleEdit" v-if="contractInfo && !editMode">
          <template #icon><EditOutlined /></template>
          编辑
        </a-button>
        <a-space v-if="editMode">
          <a-button type="primary" @click="handleSave" :loading="saveLoading">保存</a-button>
          <a-button @click="handleCancel">取消</a-button>
        </a-space>
        <a-button @click="handleRefresh">
          <template #icon><ReloadOutlined /></template>
          刷新
        </a-button>
      </div>
    </div>

    <div class="page-content">
      <a-spin :spinning="loading">
        <!-- 无合同状态 -->
        <div v-if="!hasContract" class="empty-state">
          <a-empty description="当前项目暂无合同信息">
            <a-button type="primary" @click="handleCreateContract">
              <template #icon><PlusOutlined /></template>
              创建合同
            </a-button>
          </a-empty>
        </div>

        <!-- 有合同信息 -->
        <div v-else-if="contractInfo" class="contract-detail">
          <!-- 基础信息 -->
          <a-card title="基础信息" class="mb-4">
            <BasicForm @register="registerForm('basic')" />
          </a-card>

          <!-- 金额信息 -->
          <a-card title="金额信息" class="mb-4">
            <BasicForm @register="registerForm('amount')" />
          </a-card>

          <!-- 日期信息 -->
          <a-card title="日期信息" class="mb-4">
            <BasicForm @register="registerForm('date')" />
          </a-card>

          <!-- 合同周期 -->
          <a-card title="合同周期" class="mb-4">
            <BasicForm @register="registerForm('cycle')" />
          </a-card>

          <!-- 联系人信息 -->
          <a-card title="联系人信息" class="mb-4">
            <BasicForm @register="registerForm('contact')" />
          </a-card>

          <!-- 状态管理 -->
          <a-card title="状态管理" class="mb-4">
            <BasicForm @register="registerForm('status')" />
          </a-card>

          <!-- 毛利分析 -->
          <a-card title="毛利分析" class="mb-4">
            <BasicForm @register="registerForm('profit')" />
          </a-card>

          <!-- 备注信息 -->
          <a-card title="备注信息" class="mb-4">
            <BasicForm @register="registerForm('note')" />
          </a-card>
        </div>
      </a-spin>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, inject, watch, onMounted, onActivated, nextTick } from 'vue';
  import { EditOutlined, ReloadOutlined, PlusOutlined } from '@ant-design/icons-vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { updateContract, type ContractModel, type ContractFormModel } from '/@/api/project/contract';
  import { useProjectContext } from '/@/hooks/web/useProjectContext';
  import { BasicForm, useForm } from '/@/components/Form';
  import { useContractFormSchemas } from '../hooks/useContractFormSchemas';
  import { handleApiError, extractContractData, processDateFields, batchSetFormData } from '../utils';
  import { logger } from '../constants';

  // 从父组件注入合同信息
  const contractId = inject('contractId', ref(''));
  const contractInfo = inject('contractInfo', ref<ContractModel | null>(null));
  const hasContract = inject('hasContract', ref(false));

  const { createMessage } = useMessage();

  // 响应式数据
  const loading = ref(false);
  const editMode = ref(false);
  const saveLoading = ref(false);

  // 使用项目上下文Hook
  const { projectId } = useProjectContext({
    onProjectChange: () => {
      logger.log('项目切换，退出编辑模式');
      editMode.value = false;
    },
  });

  // 获取表单配置
  const { basicFormSchemas, amountFormSchemas, dateFormSchemas, cycleFormSchemas, contactFormSchemas, statusFormSchemas, profitFormSchemas, noteFormSchemas } =
    useContractFormSchemas(editMode);

  // 表单注册映射
  const formRegistrations = new Map<string, ReturnType<typeof useForm>>();

  // 注册所有表单
  const [registerBasicForm, basicFormMethods] = useForm({
    labelWidth: 120,
    schemas: basicFormSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });
  formRegistrations.set('basic', basicFormMethods);

  const [registerAmountForm, amountFormMethods] = useForm({
    labelWidth: 120,
    schemas: amountFormSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });
  formRegistrations.set('amount', amountFormMethods);

  const [registerDateForm, dateFormMethods] = useForm({
    labelWidth: 120,
    schemas: dateFormSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });
  formRegistrations.set('date', dateFormMethods);

  const [registerCycleForm, cycleFormMethods] = useForm({
    labelWidth: 120,
    schemas: cycleFormSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });
  formRegistrations.set('cycle', cycleFormMethods);

  const [registerContactForm, contactFormMethods] = useForm({
    labelWidth: 120,
    schemas: contactFormSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });
  formRegistrations.set('contact', contactFormMethods);

  const [registerStatusForm, statusFormMethods] = useForm({
    labelWidth: 120,
    schemas: statusFormSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });
  formRegistrations.set('status', statusFormMethods);

  const [registerProfitForm, profitFormMethods] = useForm({
    labelWidth: 120,
    schemas: profitFormSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });
  formRegistrations.set('profit', profitFormMethods);

  const [registerNoteForm, noteFormMethods] = useForm({
    labelWidth: 120,
    schemas: noteFormSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });
  formRegistrations.set('note', noteFormMethods);

  // 统一的表单注册函数
  const registerForm = (section: string) => {
    const registrations: Record<string, Function> = {
      basic: registerBasicForm,
      amount: registerAmountForm,
      date: registerDateForm,
      cycle: registerCycleForm,
      contact: registerContactForm,
      status: registerStatusForm,
      profit: registerProfitForm,
      note: registerNoteForm,
    };
    return registrations[section];
  };

  // 加载合同信息
  const loadContractInfo = async () => {
    if (!contractInfo.value) {
      logger.warn('没有合同信息');
      return;
    }

    loading.value = true;
    try {
      const contract = contractInfo.value;
      logger.log('开始加载合同信息:', contract.name);

      // 批量设置表单数据
      const formSetters: Record<string, { setter: Function; section: string }> = {};

      formRegistrations.forEach((methods, section) => {
        formSetters[`${section}表单`] = {
          setter: methods.setFieldsValue,
          section,
        };
      });

      await batchSetFormData(contract, formSetters);
      logger.log('合同信息加载完成');
    } catch (error) {
      handleApiError(error, '加载合同信息');
    } finally {
      loading.value = false;
    }
  };

  // 编辑合同
  const handleEdit = async () => {
    editMode.value = true;
    await nextTick();

    // 重新加载数据以确保编辑模式下的表单正确显示
    if (contractInfo.value) {
      await loadContractInfo();
    }
  };

  // 保存合同信息
  const handleSave = async () => {
    try {
      saveLoading.value = true;

      // 收集并验证所有表单数据
      const formDataPromises = Array.from(formRegistrations.entries()).map(async ([section, methods]) => {
        const data = await methods.validate();
        return { section, data };
      });

      const formDataArray = await Promise.all(formDataPromises);

      // 合并所有表单数据
      let submitData: ContractFormModel = {} as ContractFormModel;
      formDataArray.forEach(({ data }) => {
        Object.assign(submitData, data);
      });

      // 处理日期字段
      submitData = processDateFields(submitData, 'toSubmit') as ContractFormModel;

      // 添加必要的只读字段
      if (contractInfo.value) {
        submitData.moneyStatus = contractInfo.value.moneyStatus || '';
        submitData.typeStatus = contractInfo.value.typeStatus || '';
      }

      logger.log('提交合同数据:', submitData);

      // 调用API更新合同
      const response = await updateContract(contractId.value, submitData);
      logger.log('合同更新成功:', response);

      createMessage.success('合同信息更新成功');
      editMode.value = false;

      // 重新加载数据
      await loadContractInfo();
    } catch (error) {
      handleApiError(error, '保存合同信息');
    } finally {
      saveLoading.value = false;
    }
  };

  // 取消编辑
  const handleCancel = () => {
    editMode.value = false;
    loadContractInfo();
  };

  // 创建合同
  const handleCreateContract = () => {
    createMessage.info('创建合同功能开发中...');
  };

  // 刷新
  const handleRefresh = () => {
    loadContractInfo();
  };

  // 监听合同信息变化
  watch(
    contractInfo,
    newContractInfo => {
      if (newContractInfo) {
        logger.log('合同信息变化，重新加载数据');
        // 使用 nextTick 确保 DOM 更新完成
        nextTick(() => {
          loadContractInfo();
        });
      } else {
        // 合同信息清空时，退出编辑模式
        editMode.value = false;
      }
    },
    { immediate: false, deep: true },
  );

  // 组件挂载时加载数据
  onMounted(async () => {
    await nextTick();
    if (contractInfo.value) {
      await loadContractInfo();
    }
  });

  // 组件激活时加载数据（用于 keep-alive）
  onActivated(async () => {
    await nextTick();
    if (contractInfo.value) {
      await loadContractInfo();
    }
  });
</script>

<style lang="less" scoped>
  .contract-info-management {
    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding: 16px;
      background: #fff;
      border-radius: 6px;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    }

    .page-content {
      .contract-detail {
        .ant-card {
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);

          &:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            transition: box-shadow 0.3s;
          }
        }
      }

      .empty-state {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 400px;
        background: #fff;
        border-radius: 6px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
      }
    }
  }
</style>
