<template>
  <div class="department-page p-4">
    <!-- 页面标题 -->
    <div class="page-header mb-4">
      <h2 class="text-xl font-semibold mb-2">相关部门</h2>
      <p class="text-gray-600">管理与项目相关的部门信息</p>
    </div>

    <a-spin :spinning="loading">
      <!-- 工具栏 -->
      <div class="toolbar mb-4 flex justify-between items-center">
        <div class="filters flex items-center space-x-4">
          <a-input-search v-model:value="searchText" placeholder="搜索部门名称或描述" style="width: 300px" @search="handleSearch" />
        </div>
        <div class="actions">
          <a-space>
            <a-button type="primary" @click="handleAdd">
              <template #icon><PlusOutlined /></template>
              添加部门
            </a-button>
            <a-button @click="handleRefresh">
              <template #icon><ReloadOutlined /></template>
              刷新
            </a-button>
          </a-space>
        </div>
      </div>

      <!-- 部门统计卡片 -->
      <div class="department-stats grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div class="stat-card bg-blue-50 rounded-lg p-4">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-sm text-blue-600">总部门数</div>
              <div class="text-2xl font-bold text-blue-600">{{ departmentStats.total }}</div>
            </div>
            <ApartmentOutlined class="text-3xl text-blue-600" />
          </div>
        </div>
        <div class="stat-card bg-green-50 rounded-lg p-4">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-sm text-green-600">核心部门</div>
              <div class="text-2xl font-bold text-green-600">{{ departmentStats.core }}</div>
            </div>
            <StarOutlined class="text-3xl text-green-600" />
          </div>
        </div>
        <div class="stat-card bg-orange-50 rounded-lg p-4">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-sm text-orange-600">协作部门</div>
              <div class="text-2xl font-bold text-orange-600">{{ departmentStats.collaborative }}</div>
            </div>
            <TeamOutlined class="text-3xl text-orange-600" />
          </div>
        </div>
      </div>

      <!-- 部门表格 -->
      <div class="department-table bg-white rounded-lg shadow-sm border">
        <a-table
          :columns="departmentColumns"
          :data-source="filteredDepartments"
          row-key="id"
          :pagination="pagination"
          :scroll="{ x: 800 }"
          @change="handleTableChange">
          <template #headerCell="{ column }">
            <template v-if="column.key === 'name'">
              <ApartmentOutlined class="mr-1" />
              部门
            </template>
            <template v-else-if="column.key === 'description'">
              <FileTextOutlined class="mr-1" />
              描述
            </template>
          </template>

          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'name'">
              <div class="flex items-center">
                <a-avatar class="mr-2" :style="{ backgroundColor: getDepartmentColor(record.name) }">
                  {{ record.name.charAt(0) }}
                </a-avatar>
                <div>
                  <div class="font-medium">{{ record.name }}</div>
                  <div class="text-sm text-gray-500">{{ record.code }}</div>
                </div>
              </div>
            </template>
            <template v-else-if="column.key === 'description'">
              <div class="max-w-md">
                <a-tooltip :title="record.description">
                  <div class="truncate">{{ record.description }}</div>
                </a-tooltip>
              </div>
            </template>
            <template v-else-if="column.key === 'type'">
              <a-tag :color="getTypeColor(record.type)">
                {{ record.type }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'memberCount'">
              <div class="text-center">
                <a-badge :count="record.memberCount" :overflow-count="99">
                  <UserOutlined class="text-lg" />
                </a-badge>
              </div>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="handleView(record)">
                  <template #icon><EyeOutlined /></template>
                  查看
                </a-button>
                <a-button type="link" size="small" @click="handleEdit(record)">
                  <template #icon><EditOutlined /></template>
                  编辑
                </a-button>
                <a-popconfirm title="确定要移除这个部门吗？" @confirm="handleRemove(record)">
                  <a-button type="link" size="small" danger>
                    <template #icon><DeleteOutlined /></template>
                    移除
                  </a-button>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </a-spin>

    <!-- 添加/编辑部门模态框 -->
    <a-modal v-model:open="modalVisible" :title="modalTitle" :confirm-loading="modalLoading" @ok="handleModalOk" @cancel="handleModalCancel">
      <a-form ref="modalFormRef" :model="modalFormData" :rules="modalRules" layout="vertical">
        <a-form-item label="部门名称" name="name">
          <a-input v-model:value="modalFormData.name" placeholder="请输入部门名称" />
        </a-form-item>
        <a-form-item label="部门编码" name="code">
          <a-input v-model:value="modalFormData.code" placeholder="请输入部门编码" />
        </a-form-item>
        <a-form-item label="部门类型" name="type">
          <a-select v-model:value="modalFormData.type" placeholder="请选择部门类型">
            <a-select-option value="核心部门">核心部门</a-select-option>
            <a-select-option value="协作部门">协作部门</a-select-option>
            <a-select-option value="支持部门">支持部门</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="部门描述" name="description">
          <a-textarea v-model:value="modalFormData.description" placeholder="请输入部门描述" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 部门详情模态框 -->
    <a-modal v-model:open="detailModalVisible" title="部门详情" :footer="null" width="800px">
      <div v-if="selectedDepartment" class="department-detail">
        <div class="detail-header mb-4 flex items-center">
          <a-avatar :style="{ backgroundColor: getDepartmentColor(selectedDepartment.name) }" size="large">
            {{ selectedDepartment.name.charAt(0) }}
          </a-avatar>
          <div class="ml-4">
            <h3 class="text-lg font-semibold">{{ selectedDepartment.name }}</h3>
            <p class="text-gray-600">{{ selectedDepartment.code }}</p>
          </div>
        </div>

        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="部门类型">
            <a-tag :color="getTypeColor(selectedDepartment.type)">
              {{ selectedDepartment.type }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="成员数量">
            {{ selectedDepartment.memberCount }}
          </a-descriptions-item>
          <a-descriptions-item label="部门描述" span="2">
            {{ selectedDepartment.description }}
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            {{ selectedDepartment.createTime }}
          </a-descriptions-item>
          <a-descriptions-item label="更新时间">
            {{ selectedDepartment.updateTime }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted } from 'vue';
  import {
    PlusOutlined,
    ReloadOutlined,
    ApartmentOutlined,
    StarOutlined,
    TeamOutlined,
    FileTextOutlined,
    UserOutlined,
    EyeOutlined,
    EditOutlined,
    DeleteOutlined,
  } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';
  import type { TableColumnsType } from 'ant-design-vue';

  // 部门接口
  interface Department {
    id: string;
    name: string;
    code: string;
    description: string;
    type: string;
    memberCount: number;
    createTime: string;
    updateTime: string;
  }

  // 响应式数据
  const loading = ref(false);
  const searchText = ref('');
  const modalVisible = ref(false);
  const modalLoading = ref(false);
  const detailModalVisible = ref(false);
  const modalFormRef = ref();
  const isEditing = ref(false);
  const selectedDepartment = ref<Department | null>(null);

  // 分页配置
  const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number) => `共 ${total} 条记录`,
  });

  // 部门统计
  const departmentStats = reactive({
    total: 5,
    core: 2,
    collaborative: 3,
  });

  // 模态框表单数据
  const modalFormData = reactive({
    id: '',
    name: '',
    code: '',
    description: '',
    type: '',
  });

  // 模态框验证规则
  const modalRules = {
    name: [{ required: true, message: '请输入部门名称' }],
    code: [{ required: true, message: '请输入部门编码' }],
    type: [{ required: true, message: '请选择部门类型' }],
  };

  // 部门数据
  const departments = ref<Department[]>([
    {
      id: '1',
      name: '软件科技事业部',
      code: 'SOFT_TECH',
      description: '负责软件产品的研发、设计和技术创新，提供高质量的软件解决方案',
      type: '核心部门',
      memberCount: 45,
      createTime: '2024-01-15',
      updateTime: '2024-03-20',
    },
    {
      id: '2',
      name: '数字化解决方案部',
      code: 'DIGITAL_SOL',
      description: '专注于数字化转型咨询和解决方案设计，为客户提供全面的数字化服务',
      type: '核心部门',
      memberCount: 32,
      createTime: '2024-01-20',
      updateTime: '2024-03-18',
    },
    {
      id: '3',
      name: '质量保证部',
      code: 'QA_DEPT',
      description: '负责产品质量控制、测试流程管理和质量标准制定',
      type: '协作部门',
      memberCount: 18,
      createTime: '2024-02-01',
      updateTime: '2024-03-15',
    },
    {
      id: '4',
      name: '运维服务部',
      code: 'OPS_DEPT',
      description: '提供系统运维、技术支持和基础设施管理服务',
      type: '支持部门',
      memberCount: 25,
      createTime: '2024-02-10',
      updateTime: '2024-03-10',
    },
    {
      id: '5',
      name: '产品管理部',
      code: 'PROD_MGMT',
      description: '负责产品规划、需求分析和产品生命周期管理',
      type: '协作部门',
      memberCount: 15,
      createTime: '2024-02-15',
      updateTime: '2024-03-12',
    },
  ]);

  // 表格列配置
  const departmentColumns: TableColumnsType = [
    {
      title: '部门',
      dataIndex: 'name',
      key: 'name',
      width: 300,
      fixed: 'left',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 120,
    },
    {
      title: '成员数量',
      dataIndex: 'memberCount',
      key: 'memberCount',
      width: 100,
      align: 'center',
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
    },
  ];

  // 计算过滤后的部门列表
  const filteredDepartments = computed(() => {
    if (!searchText.value) return departments.value;
    return departments.value.filter(
      dept => dept.name.toLowerCase().includes(searchText.value.toLowerCase()) || dept.description.toLowerCase().includes(searchText.value.toLowerCase()),
    );
  });

  // 模态框标题
  const modalTitle = computed(() => (isEditing.value ? '编辑部门' : '添加部门'));

  // 获取部门颜色
  const getDepartmentColor = (name: string) => {
    const colors = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1', '#13c2c2'];
    const index = name.charCodeAt(0) % colors.length;
    return colors[index];
  };

  // 获取类型颜色
  const getTypeColor = (type: string) => {
    const colorMap: Record<string, string> = {
      核心部门: 'red',
      协作部门: 'blue',
      支持部门: 'green',
    };
    return colorMap[type] || 'default';
  };

  // 搜索处理
  const handleSearch = () => {
    console.log('搜索:', searchText.value);
  };

  // 刷新处理
  const handleRefresh = () => {
    loadData();
    message.success('刷新成功');
  };

  // 添加部门
  const handleAdd = () => {
    isEditing.value = false;
    resetModalForm();
    modalVisible.value = true;
  };

  // 查看部门
  const handleView = (record: Department) => {
    selectedDepartment.value = record;
    detailModalVisible.value = true;
  };

  // 编辑部门
  const handleEdit = (record: Department) => {
    isEditing.value = true;
    Object.assign(modalFormData, record);
    modalVisible.value = true;
  };

  // 移除部门
  const handleRemove = (record: Department) => {
    const index = departments.value.findIndex(d => d.id === record.id);
    if (index > -1) {
      departments.value.splice(index, 1);
      updateStats();
      message.success('移除成功');
    }
  };

  // 表格变化处理
  const handleTableChange = (pag: any) => {
    pagination.current = pag.current;
    pagination.pageSize = pag.pageSize;
  };

  // 重置模态框表单
  const resetModalForm = () => {
    Object.assign(modalFormData, {
      id: '',
      name: '',
      code: '',
      description: '',
      type: '',
    });
  };

  // 模态框确认
  const handleModalOk = async () => {
    try {
      await modalFormRef.value.validate();
      modalLoading.value = true;

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));

      if (isEditing.value) {
        // 编辑
        const index = departments.value.findIndex(d => d.id === modalFormData.id);
        if (index > -1) {
          departments.value[index] = {
            ...departments.value[index],
            ...modalFormData,
            updateTime: new Date().toISOString().split('T')[0],
          };
          message.success('编辑成功');
        }
      } else {
        // 添加
        const newDepartment: Department = {
          id: Date.now().toString(),
          name: modalFormData.name,
          code: modalFormData.code,
          description: modalFormData.description,
          type: modalFormData.type,
          memberCount: 0,
          createTime: new Date().toISOString().split('T')[0],
          updateTime: new Date().toISOString().split('T')[0],
        };
        departments.value.push(newDepartment);
        message.success('添加成功');
      }

      updateStats();
      modalVisible.value = false;
      resetModalForm();
    } catch (error) {
      console.error('操作失败:', error);
      message.error('操作失败，请检查输入信息');
    } finally {
      modalLoading.value = false;
    }
  };

  // 模态框取消
  const handleModalCancel = () => {
    resetModalForm();
    modalVisible.value = false;
  };

  // 更新统计数据
  const updateStats = () => {
    departmentStats.total = departments.value.length;
    departmentStats.core = departments.value.filter(d => d.type === '核心部门').length;
    departmentStats.collaborative = departments.value.filter(d => d.type === '协作部门').length;
    pagination.total = departments.value.length;
  };

  // 加载数据
  const loadData = async () => {
    loading.value = true;
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));

      updateStats();
      console.log('部门数据加载完成');
    } catch (error) {
      console.error('加载失败:', error);
      message.error('加载失败，请刷新页面重试');
    } finally {
      loading.value = false;
    }
  };

  // 页面初始化
  onMounted(() => {
    loadData();
  });
</script>

<style scoped>
  .department-page {
    min-height: 100vh;
    background-color: var(--section-bg-color);
  }

  .stat-card {
    transition: all 0.3s ease;
  }

  .stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .detail-header {
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 16px;
  }

  :deep(.ant-table-thead > tr > th) {
    background-color: #fafafa;
    font-weight: 600;
  }

  :deep(.ant-descriptions-item-label) {
    font-weight: 500;
  }
</style>
