<template>
  <div>
    <a-spin :spinning="loading">
      <a-empty v-if="contractList.length === 0" description="暂无关联合同" />

      <a-card v-else v-for="item in contractList" :key="item.id" class="contract-card" :bordered="false">
        <template #title>
          <div class="contract-title">
            <span>{{ item.name }}</span>
            <a-tag :color="getStatusColor(item.contractStatus)">{{ getStatusText(item.contractStatus) }}</a-tag>
          </div>
        </template>
        <template #extra>
          <a @click="handleViewContract(item)">查看详情</a>
        </template>

        <div class="contract-info">
          <div class="contract-item">
            <span class="label">合同编号:</span>
            <span class="value">{{ item.cno || '-' }}</span>
          </div>
          <div class="contract-item">
            <span class="label">客户单位:</span>
            <span class="value">{{ item.custName || '-' }}</span>
          </div>
          <div class="contract-item">
            <span class="label">合同金额:</span>
            <span class="value">{{ formatAmount(item.amount) }}</span>
          </div>
          <div class="contract-item">
            <span class="label">已收金额:</span>
            <span class="value">{{ formatAmount(item.ysAmount) }}</span>
          </div>
          <div class="contract-item">
            <span class="label">签订日期:</span>
            <span class="value">{{ item.signDate || '-' }}</span>
          </div>
          <div class="contract-item">
            <span class="label">负责人:</span>
            <span class="value">{{ item.ownName || '-' }}</span>
          </div>
        </div>
      </a-card>
    </a-spin>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, PropType } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useRouter } from 'vue-router';

  // 合同对象接口
  interface ContractModel {
    id: string;
    cId: string;
    name: string;
    cno?: string;
    custName?: string;
    ownName?: string;
    amount?: number;
    ysAmount?: number;
    contractStatus?: string;
    moneyStatus?: string;
    signDate?: string;
  }

  const props = defineProps({
    businessId: {
      type: String as PropType<string>,
      required: true,
    },
  });

  const { createMessage } = useMessage();
  const router = useRouter();
  const contractList = ref<ContractModel[]>([]);
  const loading = ref(false);

  // 合同状态映射
  const contractStatusMap = {
    draft: { text: '草稿', color: 'default' },
    executing: { text: '执行中', color: 'processing' },
    completed: { text: '已完成', color: 'success' },
    terminated: { text: '已终止', color: 'error' },
    archived: { text: '已归档', color: 'warning' },
  };

  // 获取合同状态文本
  function getStatusText(status: string) {
    return contractStatusMap[status]?.text || status;
  }

  // 获取合同状态颜色
  function getStatusColor(status: string) {
    return contractStatusMap[status]?.color || 'default';
  }

  // 格式化金额
  function formatAmount(amount: number | undefined) {
    return amount ? amount.toLocaleString('zh-CN', { style: 'currency', currency: 'CNY' }) : '¥0.00';
  }

  // 查看合同详情
  function handleViewContract(contract: ContractModel) {
    router.push({
      path: `/project/contract/detail/${contract.cId}`,
    });
  }

  // 加载关联合同列表
  async function loadContractList() {
    try {
      loading.value = true;

      // 模拟数据，实际项目中应该从API获取
      // 这里应该调用实际的API: getContractListByBusinessId(props.businessId)
      contractList.value = [
        {
          id: '1',
          cId: '1001',
          name: '某系统开发合同',
          cno: 'HT-2023-001',
          custName: '某科技有限公司',
          ownName: '张三',
          amount: 500000,
          ysAmount: 200000,
          contractStatus: 'executing',
          moneyStatus: 'partial',
          signDate: '2023-06-15',
        },
      ];

      // 延迟模拟API请求
      setTimeout(() => {
        loading.value = false;
      }, 500);
    } catch (error) {
      console.error('获取关联合同列表失败:', error);
      createMessage.error('获取关联合同列表失败');
      loading.value = false;
    }
  }

  onMounted(() => {
    loadContractList();
  });
</script>

<style lang="less" scoped>
  .contract-card {
    margin-bottom: 16px;

    .contract-title {
      display: flex;
      align-items: center;

      span {
        margin-right: 8px;
        font-weight: bold;
      }
    }

    .contract-info {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 12px;

      .contract-item {
        .label {
          color: #666;
          margin-right: 8px;
        }

        .value {
          font-weight: 500;
        }
      }
    }
  }

  @media (max-width: 768px) {
    .contract-info {
      grid-template-columns: repeat(2, 1fr) !important;
    }
  }

  @media (max-width: 576px) {
    .contract-info {
      grid-template-columns: 1fr !important;
    }
  }
</style>
