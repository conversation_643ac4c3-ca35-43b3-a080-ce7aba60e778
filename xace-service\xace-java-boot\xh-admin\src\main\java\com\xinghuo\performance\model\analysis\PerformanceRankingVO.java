package com.xinghuo.performance.model.analysis;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 绩效排名VO
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class PerformanceRankingVO {

    /**
     * 排名
     */
    private Integer ranking;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 分部名称
     */
    private String fbName;

    /**
     * 绩效总分
     */
    private BigDecimal totalScore;

    /**
     * 各维度得分
     */
    private String dimensionScores;

    /**
     * 等级
     */
    private String grade;
}
