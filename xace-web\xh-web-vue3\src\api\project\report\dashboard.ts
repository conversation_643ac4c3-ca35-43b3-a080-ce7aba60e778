import { defHttp } from '/@/utils/http/axios';

const Api = {
  DashboardData: '/api/project/biz/report/dashboard/data',
  PaymentTrend: '/api/project/biz/report/dashboard/payment-trend',
  ContractStatus: '/api/project/biz/report/dashboard/contract-status',
  DepartmentRank: '/api/project/biz/report/dashboard/department-rank',
  ConversionFunnel: '/api/project/biz/report/dashboard/conversion-funnel',
};

// 仪表板数据接口
export interface DashboardData {
  contractCount: number;
  monthlyNewContracts: number;
  totalContractAmount: number;
  receivedAmount: number;
  pendingAmount: number;
  overdueAmount: number;
  opportunityCount: number;
  conversionRate: number;
}

// 收款趋势数据接口
export interface PaymentTrendData {
  months: string[];
  receivedAmounts: number[];
  targetAmounts: number[];
}

// 合同状态分布数据接口
export interface ContractStatusData {
  name: string;
  value: number;
}

// 部门排行数据接口
export interface DepartmentRankData {
  departments: string[];
  amounts: number[];
}

// 商机转化漏斗数据接口
export interface ConversionFunnelData {
  name: string;
  value: number;
}

/**
 * 获取仪表板概览数据
 */
export function getDashboardData() {
  return defHttp
    .get<DashboardData>({
      url: Api.DashboardData,
    })
    .then((response: any) => {
      // 处理后端返回的 {code, msg, data} 格式
      if (response && response.data) {
        return response.data;
      }
      return response || {};
    });
}

/**
 * 获取收款趋势数据
 */
export function getPaymentTrendData() {
  return defHttp
    .get<PaymentTrendData>({
      url: Api.PaymentTrend,
    })
    .then((response: any) => {
      // 处理后端返回的 {code, msg, data} 格式
      if (response && response.data) {
        return response.data;
      }
      return response || { months: [], receivedAmounts: [], targetAmounts: [] };
    });
}

/**
 * 获取合同状态分布数据
 */
export function getContractStatusData() {
  return defHttp
    .get<ContractStatusData[]>({
      url: Api.ContractStatus,
    })
    .then((response: any) => {
      // 处理后端返回的 {code, msg, data} 格式
      if (response && response.data) {
        return response.data;
      }
      return response || [];
    });
}

/**
 * 获取部门收款排行数据
 */
export function getDepartmentRankData() {
  return defHttp
    .get<DepartmentRankData>({
      url: Api.DepartmentRank,
    })
    .then((response: any) => {
      // 处理后端返回的 {code, msg, data} 格式
      if (response && response.data) {
        return response.data;
      }
      return response || { departments: [], amounts: [] };
    });
}

/**
 * 获取商机转化漏斗数据
 */
export function getConversionFunnelData() {
  return defHttp
    .get<ConversionFunnelData[]>({
      url: Api.ConversionFunnel,
    })
    .then((response: any) => {
      // 处理后端返回的 {code, msg, data} 格式
      if (response && response.data) {
        return response.data;
      }
      return response || [];
    });
}
