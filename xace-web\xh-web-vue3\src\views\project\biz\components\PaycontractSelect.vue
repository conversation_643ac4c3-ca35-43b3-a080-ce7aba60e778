<template>
  <a-select
    v-model:value="currentValue"
    :placeholder="placeholder"
    :loading="loading"
    :allowClear="allowClear"
    :disabled="disabled"
    show-search
    :filter-option="false"
    @search="handleSearch"
    @change="handleChange"
    @clear="handleClear">
    <a-select-option v-for="item in options" :key="item.id" :value="item.id">
      {{ item.fullName }}
    </a-select-option>
  </a-select>
</template>

<script lang="ts" setup>
  import { ref, watch, onMounted } from 'vue';
  import { useDebounceFn } from '@vueuse/core';
  import { getPaycontractSelector } from '/@/api/project/paycontract';

  interface PaycontractOption {
    id: string;
    fullName: string;
    name: string;
    cNo?: string;
  }

  interface Props {
    value?: string;
    placeholder?: string;
    allowClear?: boolean;
    disabled?: boolean;
  }

  interface Emits {
    (e: 'update:value', value: string | undefined): void;
    (e: 'change', value: string | undefined, option?: PaycontractOption): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    placeholder: '请选择采购合同',
    allowClear: true,
    disabled: false,
  });

  const emit = defineEmits<Emits>();

  const currentValue = ref<string | undefined>(props.value);
  const options = ref<PaycontractOption[]>([]);
  const loading = ref(false);

  // 加载选项数据
  const loadOptions = async (keyword?: string) => {
    if (loading.value) {
      return; // 防止重复请求
    }

    try {
      loading.value = true;
      const result = await getPaycontractSelector(keyword);

      // 确保 result 是数组
      if (Array.isArray(result)) {
        options.value = result;
      } else if (result && typeof result === 'object' && 'data' in result && Array.isArray((result as any).data)) {
        // 如果 API 返回的是包装格式
        options.value = (result as any).data;
      } else {
        console.warn('PaycontractSelect: API 返回的数据格式不正确', result);
        options.value = [];
      }

      console.log('PaycontractSelect: 加载选项完成', {
        keyword,
        optionsCount: options.value.length,
        currentValue: currentValue.value,
        rawResult: result,
      });

      // 如果有当前值但在新加载的选项中找不到，打印调试信息
      if (currentValue.value && options.value.length > 0 && !options.value.find(item => item.id === currentValue.value)) {
        console.log('PaycontractSelect: 当前值在选项中未找到', {
          currentValue: currentValue.value,
          options: options.value,
          keyword,
        });
      }
    } catch (error) {
      console.error('加载采购合同选项失败:', error);
      options.value = [];
    } finally {
      loading.value = false;
    }
  };

  // 监听外部值变化
  watch(
    () => props.value,
    async (newVal, oldVal) => {
      try {
        currentValue.value = newVal;

        // 如果有新值且与旧值不同，需要确保选项列表中包含该值
        if (newVal && newVal !== oldVal) {
          // 如果选项列表中没有对应的选项，需要加载选项
          if (!options.value.find(item => item.id === newVal)) {
            console.log('PaycontractSelect: 加载选项以包含新值', newVal);
            await loadOptions();
          }
        }
      } catch (error) {
        console.error('PaycontractSelect: 监听值变化时出错', error);
      }
    },
    { immediate: true },
  );

  // 防抖搜索
  const debouncedSearch = useDebounceFn(loadOptions, 300);

  // 搜索处理
  function handleSearch(value: string) {
    if (value) {
      debouncedSearch(value);
    } else {
      loadOptions();
    }
  }

  // 值变化处理
  function handleChange(value: string | undefined) {
    currentValue.value = value;
    emit('update:value', value);

    const selectedOption = options.value.find(item => item.id === value);
    emit('change', value, selectedOption);
  }

  // 清空处理
  function handleClear() {
    currentValue.value = undefined;
    emit('update:value', undefined);
    emit('change', undefined);
  }

  // 组件挂载时加载初始数据
  onMounted(async () => {
    try {
      await loadOptions();
    } catch (error) {
      console.error('PaycontractSelect: 组件初始化失败', error);
    }
  });
</script>
