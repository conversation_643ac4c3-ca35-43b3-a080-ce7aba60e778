import { BasicColumn } from '/@/components/Table';
import { Tag } from 'ant-design-vue';
import { h } from 'vue';

export function getTableSchemas(): BasicColumn[] {
  return [
    {
      title: '项目',
      dataIndex: 'projectName',
      width: 300,
      ellipsis: true,
      customRender: ({ record }) => {
        return h('div', { style: { display: 'flex', alignItems: 'center' } }, [
          h('div', {
            style: {
              width: '16px',
              height: '16px',
              backgroundColor: '#1890ff',
              borderRadius: '2px',
              marginRight: '8px',
            },
          }),
          h('span', record.projectName),
        ]);
      },
    },
    {
      title: '状态',
      dataIndex: 'healthStatus',
      width: 80,
      customRender: ({ record }) => {
        const colorMap = {
          健康: 'success',
          警告: 'warning',
          风险: 'error',
        };
        return h(Tag, { color: colorMap[record.healthStatus] || 'default' }, () => record.healthStatus);
      },
    },
    {
      title: '项目经理',
      dataIndex: 'projectManager',
      width: 150,
      ellipsis: true,
    },
    {
      title: '阶段',
      dataIndex: 'currentPhase',
      width: 150,
      customRender: ({ record }) => {
        return h('div', { style: { display: 'flex', alignItems: 'center' } }, [
          h('div', {
            style: {
              width: '8px',
              height: '8px',
              backgroundColor: record.stageColor,
              transform: 'rotate(45deg)',
              marginRight: '8px',
            },
          }),
          h('span', `2:${record.currentPhase}`),
        ]);
      },
    },
    {
      title: '进度',
      dataIndex: 'phaseProgress',
      width: 120,
      customRender: ({ record }) => {
        const percent = record.phaseProgress / 100;
        return h(
          'div',
          {
            style: {
              width: '100%',
              height: '16px',
              backgroundColor: '#f0f0f0',
              borderRadius: '8px',
              overflow: 'hidden',
            },
          },
          [
            h('div', {
              style: {
                width: `${percent * 100}%`,
                height: '100%',
                backgroundColor: record.stageColor,
                transition: 'width 0.3s',
              },
            }),
          ],
        );
      },
    },
    {
      title: '交付物',
      dataIndex: 'deliverableProgress',
      width: 80,
      customRender: ({ record }) => {
        return h(
          'div',
          {
            style: {
              padding: '2px 8px',
              backgroundColor: record.stageColor,
              color: 'white',
              borderRadius: '4px',
              fontSize: '12px',
              textAlign: 'center',
            },
          },
          `${record.deliverableProgress}%`,
        );
      },
    },
    {
      title: '风险',
      dataIndex: 'riskCount',
      width: 60,
      customRender: ({ record }) => {
        if (record.riskCount === 0) return '';
        return h('div', {
          style: {
            width: '16px',
            height: '16px',
            backgroundColor: '#ffa93c',
            borderRadius: '50%',
          },
        });
      },
    },
    {
      title: '问题',
      dataIndex: 'issueCount',
      width: 60,
      customRender: ({ record }) => {
        if (record.issueCount === 0) return '';
        return h('div', {
          style: {
            width: '16px',
            height: '16px',
            backgroundColor: '#FF576E',
            borderRadius: '50%',
          },
        });
      },
    },
    {
      title: '项目周报',
      dataIndex: 'weeklyReport',
      width: 150,
      customRender: ({ record }) => {
        return h(
          'a',
          {
            style: {
              color: '#1890ff',
              textDecoration: 'underline',
            },
          },
          record.weeklyReport,
        );
      },
    },
  ];
}
