package com.xinghuo.project.template.service;

import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.project.template.entity.RiskLibraryEntity;
import com.xinghuo.project.template.model.RiskLibraryPagination;

import java.util.List;
import java.util.Map;

/**
 * 标准项目风险库服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
public interface RiskLibraryService extends BaseService<RiskLibraryEntity> {

    /**
     * 获取风险库列表
     *
     * @param pagination 分页参数
     * @return 风险库列表
     */
    List<RiskLibraryEntity> getList(RiskLibraryPagination pagination);

    /**
     * 根据状态获取风险库列表
     *
     * @param status 状态
     * @return 风险库列表
     */
    List<RiskLibraryEntity> getListByStatus(String status);

    /**
     * 获取风险库详情
     *
     * @param id 风险库ID
     * @return 风险库信息
     */
    RiskLibraryEntity getInfo(String id);

    /**
     * 创建风险库
     *
     * @param entity 风险库信息
     * @return 风险库ID
     */
    String create(RiskLibraryEntity entity);

    /**
     * 更新风险库
     *
     * @param id 风险库ID
     * @param entity 风险库信息
     */
    void update(String id, RiskLibraryEntity entity);

    /**
     * 删除风险库
     *
     * @param id 风险库ID
     */
    void delete(String id);

    /**
     * 批量删除风险库
     *
     * @param ids 风险库ID列表
     */
    void batchDelete(List<String> ids);

    /**
     * 更新风险库状态
     *
     * @param id 风险库ID
     * @param status 状态
     */
    void updateStatus(String id, String status);

    /**
     * 批量更新状态
     *
     * @param ids 风险库ID列表
     * @param status 状态
     */
    void batchUpdateStatus(List<String> ids, String status);

    /**
     * 检查风险编码是否存在
     *
     * @param code 风险编码
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean isExistByCode(String code, String excludeId);

    /**
     * 根据编码获取风险库
     *
     * @param code 风险编码
     * @return 风险库信息
     */
    RiskLibraryEntity getByCode(String code);

    /**
     * 获取风险库选择列表
     *
     * @param keyword 关键字
     * @return 风险库列表
     */
    List<RiskLibraryEntity> getSelectList(String keyword);

    /**
     * 发布风险库
     *
     * @param id 风险库ID
     */
    void publish(String id);

    /**
     * 归档风险库
     *
     * @param id 风险库ID
     */
    void archive(String id);

    /**
     * 复制风险库
     *
     * @param id 源风险库ID
     * @param newTitle 新标题
     * @return 新风险库ID
     */
    String copy(String id, String newTitle);

    /**
     * 生成风险编码
     *
     * @return 风险编码
     */
    String generateCode();

    /**
     * 获取风险库使用情况
     *
     * @param id 风险库ID
     * @return 使用情况统计
     */
    Map<String, Object> getRiskLibraryUsageInfo(String id);

    /**
     * 根据风险类别获取风险库列表
     *
     * @param riskCategoryId 风险类别ID
     * @return 风险库列表
     */
    List<RiskLibraryEntity> getListByRiskCategory(String riskCategoryId);

    /**
     * 根据概率等级获取风险库列表
     *
     * @param probabilityLevelId 概率等级ID
     * @return 风险库列表
     */
    List<RiskLibraryEntity> getListByProbabilityLevel(String probabilityLevelId);

    /**
     * 根据影响等级获取风险库列表
     *
     * @param impactLevelId 影响等级ID
     * @return 风险库列表
     */
    List<RiskLibraryEntity> getListByImpactLevel(String impactLevelId);

    /**
     * 检查风险标题是否存在
     *
     * @param title 风险标题
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean isExistByTitle(String title, String excludeId);

    /**
     * 批量发布风险库
     *
     * @param ids 风险库ID列表
     */
    void batchPublish(List<String> ids);

    /**
     * 批量归档风险库
     *
     * @param ids 风险库ID列表
     */
    void batchArchive(List<String> ids);
}
