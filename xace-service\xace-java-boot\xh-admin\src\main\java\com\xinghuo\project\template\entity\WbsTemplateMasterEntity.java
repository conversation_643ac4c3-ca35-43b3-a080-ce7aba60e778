package com.xinghuo.project.template.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * WBS计划模板主表实体类
 * 对应数据库表：zz_proj_tpl_wbs_master
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("zz_proj_tpl_wbs_master")
public class WbsTemplateMasterEntity extends BaseEntityV2.CUBaseEntityV2<String> {

    /**
     * WBS模板名称 (如: 标准软件开发计划)
     */
    @TableField("name")
    private String name;

    /**
     * WBS模板编码
     */
    @TableField("code")
    private String code;

    /**
     * 模板描述
     */
    @TableField("description")
    private String description;

    /**
     * 发布状态 (如: draft, published)
     */
    @TableField("status")
    private String status;
}
