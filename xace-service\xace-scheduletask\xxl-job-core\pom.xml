<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>xh-scheduletask-starter</artifactId>
        <groupId>com.xinghuo.xace</groupId>
        <version>1.2-RELEASE</version>
    </parent>
    <artifactId>xxl-job-core</artifactId>
    <packaging>jar</packaging>

    <name>xxl-job-core</name>
    <description>A distributed task scheduling framework.</description>
    <url>https://www.xuxueli.com/</url>

    <properties>
        <netty-all.version>4.1.63.Final</netty-all.version>
        <gson.version>2.8.6</gson.version>
        <spring.version>6.1.5</spring.version>
        <groovy.version>3.0.8</groovy.version>
        <slf4j-api.version>1.7.30</slf4j-api.version>
        <javax.annotation-api.version>1.3.2</javax.annotation-api.version>
    </properties>

    <dependencies>

        <!-- ********************** embed server: netty + gson ********************** -->
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
            <version>${netty-all.version}</version>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>${gson.version}</version>
        </dependency>

        <!-- ********************** plugin ********************** -->
        <!-- groovy-all -->
        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy</artifactId>
            <version>${groovy.version}</version>
        </dependency>

        <!-- spring-context -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
            <version>${spring.version}</version>
            <scope>provided</scope>
        </dependency>

        <!-- ********************** base ********************** -->
        <!-- slf4j -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>${slf4j-api.version}</version>
        </dependency>

        <!-- javax.annotation-api -->
        <dependency>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
            <version>${javax.annotation-api.version}</version>
            <scope>provided</scope>
        </dependency>

    </dependencies>

</project>
