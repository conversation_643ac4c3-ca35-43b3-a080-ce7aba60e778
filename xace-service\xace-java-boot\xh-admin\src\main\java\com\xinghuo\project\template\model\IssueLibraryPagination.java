package com.xinghuo.project.template.model;

import com.xinghuo.common.base.model.Pagination;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 标准项目问题库分页查询参数
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class IssueLibraryPagination extends Pagination {

    /**
     * 问题编码
     */
    private String code;

    /**
     * 问题标题/名称
     */
    private String title;

    /**
     * 发布状态 (如: draft 草稿, published 已发布, archived 归档)
     */
    private String status;

    /**
     * 关键字搜索（编码或标题）
     */
    private String keyword;

    /**
     * 创建时间开始
     */
    private Date createTimeStart;

    /**
     * 创建时间结束
     */
    private Date createTimeEnd;

    /**
     * 问题类别ID (关联字典表, 如: 过程不符合, 客户反馈, 事件)
     */
    private String issueCategoryId;

    /**
     * 默认优先级/严重性ID (关联字典表, 如: 严重, 一般, 较低)
     */
    private String defaultPriorityId;

    /**
     * 创建用户ID
     */
    private String createdBy;

    /**
     * 问题描述关键字
     */
    private String descriptionKeyword;

    /**
     * 解决方案关键字
     */
    private String solutionKeyword;
}
