<template>
  <div class="opportunity-tracking">
    <!-- 商机不存在提示 -->
    <a-result v-if="!hasOpportunity" status="info" title="该项目暂无关联商机" sub-title="只有商机类型的项目才会有商机跟踪记录">
      <template #extra>
        <a-button type="primary" @click="goBack"> 返回概览 </a-button>
      </template>
    </a-result>

    <!-- 商机跟踪内容 -->
    <div v-else>
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-content">
          <div class="header-left">
            <h1>{{ opportunityInfo.projectName }}</h1>
            <a-space size="large">
              <span>商机编号：{{ opportunityInfo.businessNo }}</span>
              <a-tag :color="getStatusColor(opportunityInfo.status)">
                {{ getStatusName(opportunityInfo.status) }}
              </a-tag>
            </a-space>
          </div>
          <div class="header-right">
            <a-space>
              <a-button type="primary" @click="showAddTrackModal">
                <plus-outlined />
                新增跟踪记录
              </a-button>
              <a-button @click="handleExport">
                <download-outlined />
                导出跟踪记录
              </a-button>
            </a-space>
          </div>
        </div>
      </div>

      <!-- 跟踪记录统计 -->
      <div class="tracking-stats">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-card>
              <a-statistic title="总跟踪记录" :value="trackingList.length" :value-style="{ color: '#3f8600' }">
                <template #prefix>
                  <file-text-outlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card>
              <a-statistic title="本月跟踪次数" :value="getMonthlyTrackingCount()" :value-style="{ color: '#1890ff' }">
                <template #prefix>
                  <calendar-outlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card>
              <a-statistic title="最近跟踪" :value="getLastTrackingDays()" suffix="天前" :value-style="{ color: '#722ed1' }">
                <template #prefix>
                  <clock-circle-outlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card>
              <a-statistic title="跟踪频率" :value="getTrackingFrequency()" suffix="次/月" :value-style="{ color: '#eb2f96' }">
                <template #prefix>
                  <line-chart-outlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 跟踪记录列表 -->
      <div class="tracking-list">
        <a-card title="跟踪记录" :bordered="false">
          <template #extra>
            <a-space>
              <a-select v-model:value="filterType" style="width: 120px" @change="handleFilterChange">
                <a-select-option value="all">全部</a-select-option>
                <a-select-option value="thisMonth">本月</a-select-option>
                <a-select-option value="lastMonth">上月</a-select-option>
                <a-select-option value="thisYear">今年</a-select-option>
              </a-select>
            </a-space>
          </template>

          <a-timeline>
            <a-timeline-item v-for="(item, index) in filteredTrackingList" :key="index" :color="getTrackingColor(item.type)">
              <template #dot>
                <component :is="getTrackingIcon(item.type)" />
              </template>
              <div class="tracking-item">
                <div class="tracking-header">
                  <div class="tracking-title">
                    <span class="tracking-type">{{ item.type }}</span>
                    <span class="tracking-time">{{ formatDateTime(item.createTime) }}</span>
                  </div>
                  <div class="tracking-actions">
                    <a-button type="text" size="small" @click="editTracking(item)">
                      <edit-outlined />
                    </a-button>
                    <a-button type="text" size="small" danger @click="deleteTracking(item)">
                      <delete-outlined />
                    </a-button>
                  </div>
                </div>
                <div class="tracking-content">
                  <p>{{ item.content }}</p>
                  <div v-if="item.attachments && item.attachments.length > 0" class="tracking-attachments">
                    <a-space>
                      <a-tag v-for="file in item.attachments" :key="file.id" color="blue" @click="downloadFile(file)">
                        <paper-clip-outlined />
                        {{ file.name }}
                      </a-tag>
                    </a-space>
                  </div>
                </div>
                <div class="tracking-footer">
                  <span class="tracking-author">记录人：{{ item.createUserName }}</span>
                  <span v-if="item.nextFollowTime" class="next-follow"> 下次跟踪：{{ formatDate(item.nextFollowTime) }} </span>
                </div>
              </div>
            </a-timeline-item>
          </a-timeline>

          <!-- 空状态 -->
          <a-empty v-if="filteredTrackingList.length === 0" description="暂无跟踪记录">
            <a-button type="primary" @click="showAddTrackModal"> 新增跟踪记录 </a-button>
          </a-empty>
        </a-card>
      </div>
    </div>

    <!-- 新增跟踪记录模态框 -->
    <a-modal v-model:open="addTrackModalVisible" title="新增跟踪记录" :width="800" @ok="handleAddTrack" @cancel="cancelAddTrack">
      <a-form ref="trackFormRef" :model="trackForm" :rules="trackFormRules" layout="vertical">
        <a-form-item name="type" label="跟踪类型">
          <a-select v-model:value="trackForm.type" placeholder="请选择跟踪类型">
            <a-select-option value="电话沟通">电话沟通</a-select-option>
            <a-select-option value="现场拜访">现场拜访</a-select-option>
            <a-select-option value="邮件联系">邮件联系</a-select-option>
            <a-select-option value="技术交流">技术交流</a-select-option>
            <a-select-option value="商务洽谈">商务洽谈</a-select-option>
            <a-select-option value="投标准备">投标准备</a-select-option>
            <a-select-option value="其他">其他</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item name="content" label="跟踪内容">
          <a-textarea v-model:value="trackForm.content" :rows="6" placeholder="请详细描述本次跟踪的内容、进展、问题等..." show-count :maxlength="2000" />
        </a-form-item>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item name="nextFollowTime" label="下次跟踪时间">
              <a-date-picker v-model:value="trackForm.nextFollowTime" style="width: 100%" placeholder="选择下次跟踪时间" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="priority" label="优先级">
              <a-select v-model:value="trackForm.priority" placeholder="请选择优先级">
                <a-select-option value="高">高</a-select-option>
                <a-select-option value="中">中</a-select-option>
                <a-select-option value="低">低</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item name="attachments" label="附件">
          <a-upload v-model:file-list="trackForm.attachments" :before-upload="beforeUpload" :on-remove="handleRemoveFile" multiple>
            <a-button>
              <upload-outlined />
              上传附件
            </a-button>
          </a-upload>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, computed } from 'vue';
  import { message } from 'ant-design-vue';
  import {
    PlusOutlined,
    DownloadOutlined,
    FileTextOutlined,
    CalendarOutlined,
    ClockCircleOutlined,
    LineChartOutlined,
    EditOutlined,
    DeleteOutlined,
    PaperClipOutlined,
    UploadOutlined,
  } from '@ant-design/icons-vue';
  import { formatToDate, formatToDateTime } from '/@/utils/dateUtil';
  import dayjs from 'dayjs';

  const props = defineProps<{
    projectId?: string;
  }>();

  const hasOpportunity = ref(true);
  const opportunityInfo = ref<any>({});
  const trackingList = ref<any[]>([]);
  const filterType = ref('all');
  const addTrackModalVisible = ref(false);
  const trackFormRef = ref();

  // 表单数据
  const trackForm = ref({
    type: '',
    content: '',
    nextFollowTime: null,
    priority: '中',
    attachments: [],
  });

  // 表单验证规则
  const trackFormRules = {
    type: [{ required: true, message: '请选择跟踪类型', trigger: 'change' }],
    content: [{ required: true, message: '请输入跟踪内容', trigger: 'blur' }],
  };

  // 过滤后的跟踪记录
  const filteredTrackingList = computed(() => {
    const now = dayjs();
    switch (filterType.value) {
      case 'thisMonth':
        return trackingList.value.filter(item => dayjs(item.createTime).isSame(now, 'month'));
      case 'lastMonth':
        return trackingList.value.filter(item => dayjs(item.createTime).isSame(now.subtract(1, 'month'), 'month'));
      case 'thisYear':
        return trackingList.value.filter(item => dayjs(item.createTime).isSame(now, 'year'));
      default:
        return trackingList.value;
    }
  });

  // 获取状态名称
  const getStatusName = (status: string) => {
    const statusMap = {
      '1': '跟踪中',
      '2': '方案报价中',
      '3': '商务谈判中',
      '4': '已签',
      '5': '已废弃',
      '6': '明年跟踪',
    };
    return statusMap[status] || '未知状态';
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    const colorMap = {
      '1': 'processing',
      '2': 'warning',
      '3': 'cyan',
      '4': 'success',
      '5': 'error',
      '6': 'purple',
    };
    return colorMap[status] || 'default';
  };

  // 获取跟踪类型颜色
  const getTrackingColor = (type: string) => {
    const colorMap = {
      电话沟通: 'blue',
      现场拜访: 'green',
      邮件联系: 'orange',
      技术交流: 'purple',
      商务洽谈: 'red',
      投标准备: 'gold',
      其他: 'default',
    };
    return colorMap[type] || 'default';
  };

  // 获取跟踪类型图标
  const getTrackingIcon = (type: string) => {
    const iconMap = {
      电话沟通: 'phone-outlined',
      现场拜访: 'home-outlined',
      邮件联系: 'mail-outlined',
      技术交流: 'code-outlined',
      商务洽谈: 'money-collect-outlined',
      投标准备: 'file-text-outlined',
      其他: 'more-outlined',
    };
    return iconMap[type] || 'file-text-outlined';
  };

  // 格式化日期
  const formatDate = (date: string) => {
    return date ? formatToDate(date) : '-';
  };

  // 格式化日期时间
  const formatDateTime = (date: string) => {
    return date ? formatToDateTime(date) : '-';
  };

  // 获取本月跟踪次数
  const getMonthlyTrackingCount = () => {
    const now = dayjs();
    return trackingList.value.filter(item => dayjs(item.createTime).isSame(now, 'month')).length;
  };

  // 获取最近跟踪天数
  const getLastTrackingDays = () => {
    if (trackingList.value.length === 0) return 0;
    const lastTracking = trackingList.value[0];
    return dayjs().diff(dayjs(lastTracking.createTime), 'day');
  };

  // 获取跟踪频率
  const getTrackingFrequency = () => {
    if (trackingList.value.length === 0) return 0;
    const now = dayjs();
    const threeMonthsAgo = now.subtract(3, 'month');
    const recentTrackings = trackingList.value.filter(item => dayjs(item.createTime).isAfter(threeMonthsAgo));
    return Math.round(recentTrackings.length / 3);
  };

  // 显示新增跟踪记录模态框
  const showAddTrackModal = () => {
    addTrackModalVisible.value = true;
  };

  // 取消新增跟踪记录
  const cancelAddTrack = () => {
    addTrackModalVisible.value = false;
    trackFormRef.value?.resetFields();
  };

  // 处理新增跟踪记录
  const handleAddTrack = async () => {
    try {
      await trackFormRef.value.validate();
      // 这里调用API新增跟踪记录
      const newTrack = {
        id: Date.now().toString(),
        type: trackForm.value.type,
        content: trackForm.value.content,
        nextFollowTime: trackForm.value.nextFollowTime,
        priority: trackForm.value.priority,
        attachments: trackForm.value.attachments,
        createTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        createUserName: '当前用户',
      };
      trackingList.value.unshift(newTrack);
      message.success('跟踪记录添加成功');
      addTrackModalVisible.value = false;
      trackFormRef.value?.resetFields();
    } catch (error) {
      console.error('添加跟踪记录失败:', error);
    }
  };

  // 编辑跟踪记录
  const editTracking = (item: any) => {
    message.info('编辑跟踪记录功能');
  };

  // 删除跟踪记录
  const deleteTracking = (item: any) => {
    message.info('删除跟踪记录功能');
  };

  // 下载文件
  const downloadFile = (file: any) => {
    message.info('下载文件功能');
  };

  // 处理过滤变化
  const handleFilterChange = (value: string) => {
    filterType.value = value;
  };

  // 导出跟踪记录
  const handleExport = () => {
    message.info('导出跟踪记录功能');
  };

  // 文件上传前处理
  const beforeUpload = (file: any) => {
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error('文件大小不能超过2MB!');
    }
    return isLt2M;
  };

  // 移除文件
  const handleRemoveFile = (file: any) => {
    const index = trackForm.value.attachments.indexOf(file);
    if (index > -1) {
      trackForm.value.attachments.splice(index, 1);
    }
  };

  // 返回
  const goBack = () => {
    console.log('返回概览');
  };

  // 加载数据
  const loadData = async () => {
    // 加载商机信息
    opportunityInfo.value = {
      businessNo: 'OPP-2024-0045',
      projectName: 'XX公司智慧园区建设项目',
      status: '1',
    };

    // 加载跟踪记录
    trackingList.value = [
      {
        id: '1',
        type: '电话沟通',
        content: '与客户技术负责人电话沟通，确认了系统架构方案，客户表示技术方案符合要求，准备进入商务谈判阶段。讨论了系统集成方案和技术实现细节。',
        createTime: '2024-01-18 14:30:00',
        createUserName: '张三',
        nextFollowTime: '2024-01-25',
        priority: '高',
        attachments: [
          { id: '1', name: '技术方案.pdf' },
          { id: '2', name: '架构图.png' },
        ],
      },
      {
        id: '2',
        type: '现场拜访',
        content: '前往客户现场进行需求调研，详细了解了业务流程和系统集成需求。客户对我们的技术能力表示认可，并提出了一些个性化需求。',
        createTime: '2024-01-15 09:00:00',
        createUserName: '李四',
        nextFollowTime: '2024-01-20',
        priority: '高',
        attachments: [],
      },
      {
        id: '3',
        type: '邮件联系',
        content: '发送项目初步方案和报价单给客户，客户反馈整体方案可行，但希望在价格方面能够进一步优化。',
        createTime: '2024-01-10 16:45:00',
        createUserName: '王五',
        nextFollowTime: '2024-01-15',
        priority: '中',
        attachments: [{ id: '3', name: '项目方案.docx' }],
      },
      {
        id: '4',
        type: '技术交流',
        content: '参加客户组织的技术交流会，展示了我们的技术优势和成功案例，获得了客户技术团队的认可。',
        createTime: '2024-01-05 10:30:00',
        createUserName: '赵六',
        nextFollowTime: '2024-01-12',
        priority: '中',
        attachments: [],
      },
    ];

    hasOpportunity.value = true;
  };

  onMounted(() => {
    loadData();
  });
</script>

<style lang="less" scoped>
  .opportunity-tracking {
    padding: 20px;

    .page-header {
      margin-bottom: 24px;

      .header-content {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 20px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        .header-left {
          h1 {
            margin: 0 0 8px 0;
            font-size: 24px;
            font-weight: 500;
          }
        }
      }
    }

    .tracking-stats {
      margin-bottom: 24px;
    }

    .tracking-list {
      .tracking-item {
        .tracking-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .tracking-title {
            display: flex;
            align-items: center;
            gap: 12px;

            .tracking-type {
              font-weight: 500;
              color: #262626;
            }

            .tracking-time {
              color: #999;
              font-size: 12px;
            }
          }

          .tracking-actions {
            opacity: 0;
            transition: opacity 0.3s;
          }
        }

        .tracking-content {
          margin-bottom: 8px;

          p {
            margin: 0;
            line-height: 1.6;
            color: #666;
          }

          .tracking-attachments {
            margin-top: 8px;
          }
        }

        .tracking-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 12px;
          color: #999;

          .next-follow {
            color: #1890ff;
          }
        }

        &:hover .tracking-actions {
          opacity: 1;
        }
      }
    }
  }
</style>
