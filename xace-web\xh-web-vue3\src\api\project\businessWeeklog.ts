import { defHttp } from '/@/utils/http/axios';
import { ListResult } from '/@/api/model/baseModel';

/**
 * 商机跟踪记录API
 */

// API URL前缀
const API_PREFIX = '/api/project/business/weeklog';

/**
 * 商机跟踪记录对象接口
 */
export interface BusinessWeeklogModel {
  id: string;
  businessId: string;
  businessName?: string;
  content: string;
  nextPlan?: string;
  remark?: string;
  weekNo?: number;
  year?: number;
  month?: number;
  day?: number;
  status?: string; // 状态：待填写、已填写
  isAutoGenerated?: boolean; // 是否自动生成
  createUserId?: string;
  createUserName?: string;
  createTime?: string;
  lastModifyUserId?: string;
  lastModifyUserName?: string;
  lastModifyTime?: string;
  deleteMark?: number;
}

/**
 * 商机跟踪记录表单接口
 */
export interface BusinessWeeklogFormModel {
  businessId: string;
  content: string;
  nextPlan?: string;
  remark?: string;
  weekNo?: number;
  year?: number;
  month?: number;
  day?: number;
  status?: string;
}

/**
 * 商机跟踪记录查询参数接口
 */
export interface BusinessWeeklogQueryParams {
  businessId?: string;
  weekNo?: number;
  year?: number;
  month?: number;
  day?: number;
  createTimeStart?: string;
  createTimeEnd?: string;
  pageSize?: number;
  currentPage?: number;
}

/**
 * 获取商机跟踪记录列表
 * @param params 查询参数
 * @returns 商机跟踪记录列表
 */
export const getBusinessWeeklogList = (params?: BusinessWeeklogQueryParams) => {
  return defHttp.get<ListResult<BusinessWeeklogModel>>({
    url: API_PREFIX,
    params,
  });
};

/**
 * 根据商机ID获取跟踪记录列表
 * @param businessId 商机ID
 * @returns 跟踪记录列表
 */
export const getBusinessWeeklogListByBusinessId = (businessId: string) => {
  return defHttp.get<BusinessWeeklogModel[]>({
    url: `${API_PREFIX}/${businessId}/list`,
  });
};

/**
 * 获取商机跟踪记录详情
 * @param id 跟踪记录ID
 * @returns 跟踪记录详情
 */
export const getBusinessWeeklogInfo = (id: string) => {
  return defHttp.get<BusinessWeeklogModel>({
    url: `${API_PREFIX}/${id}`,
  });
};

/**
 * 创建商机跟踪记录
 * @param params 跟踪记录创建参数
 * @returns 操作结果
 */
export const createBusinessWeeklog = (params: BusinessWeeklogFormModel) => {
  return defHttp.post<string>({
    url: API_PREFIX,
    data: params,
  });
};

/**
 * 更新商机跟踪记录
 * @param id 跟踪记录ID
 * @param params 跟踪记录更新参数
 * @returns 操作结果
 */
export const updateBusinessWeeklog = (id: string, params: BusinessWeeklogFormModel) => {
  return defHttp.put<void>({
    url: `${API_PREFIX}/${id}`,
    data: params,
  });
};

/**
 * 删除商机跟踪记录
 * @param id 跟踪记录ID
 * @returns 操作结果
 */
export const deleteBusinessWeeklog = (id: string) => {
  return defHttp.delete<void>({
    url: `${API_PREFIX}/${id}`,
  });
};

/**
 * 自动生成待填写的商机跟踪记录
 * @returns 操作结果
 */
export const autoGenerateBusinessWeeklogs = () => {
  return defHttp.post<void>({
    url: `${API_PREFIX}/auto-generate`,
  });
};

/**
 * 获取当前用户待填写的商机跟踪记录
 * @returns 待填写的跟踪记录列表
 */
export const getPendingBusinessWeeklogs = () => {
  return defHttp.get<BusinessWeeklogModel[]>({
    url: `${API_PREFIX}/pending`,
  });
};

/**
 * 批量填写商机跟踪记录
 * @param params 批量填写参数
 * @returns 操作结果
 */
export const batchUpdateBusinessWeeklogs = (params: BusinessWeeklogFormModel[]) => {
  return defHttp.put<void>({
    url: `${API_PREFIX}/batch`,
    data: params,
  });
};
