package com.xinghuo.project.schema.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目模板主表实体类
 * 对应数据库表：zz_proj_template
 *
 * 项目模板是项目管理的核心配置，包含了项目的WBS结构、阶段配置等信息。
 * 与传统的直接关联WBS模板和阶段模板不同，新架构采用私有配置存储方式，
 * 通过zz_proj_schema_wbs和zz_proj_schema_phase表存储项目模板的专属配置。
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("zz_proj_template")
public class ProjectTemplateEntity extends BaseEntityV2.CUBaseEntityV2<String> {

    /**
     * 项目模板名称 (如: 缺省项目模板)
     */
    @TableField("name")
    private String name;

    /**
     * 模板编码 (可选, 用于内部识别)
     */
    @TableField("code")
    private String code;

    /**
     * 模板描述
     */
    @TableField("description")
    private String description;

    /**
     * 状态 (0:启用, 1:禁用)
     * 注意：与ActivityLibraryEntity保持一致，0为启用，1为禁用
     */
    @TableField("status")
    private Integer status;

    /**
     * 模板类型ID (关联字典表, 如: 常规业务流程, 软件开发)
     */
    @TableField("type_id")
    private String typeId;

    /**
     * 模板图标 (用于UI展示)
     */
    @TableField("icon")
    private String icon;

    /**
     * 配置预留字段1
     */
    @TableField("config_str01")
    private String configStr01;

    /**
     * 配置预留字段2
     */
    @TableField("config_str02")
    private String configStr02;

    /**
     * 配置预留字段3
     */
    @TableField("config_str03")
    private String configStr03;
}
