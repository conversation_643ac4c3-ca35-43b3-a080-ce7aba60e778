<template>
  <div class="template-deliverable-page p-4">
    <!-- 页面标题 -->
    <div class="page-header mb-4">
      <h2 class="text-xl font-semibold mb-2">交付物管理</h2>
      <p class="text-gray-600">配置项目模板的交付物信息，设置各交付物的基本属性和质量要求</p>
    </div>

    <a-spin :spinning="loading">
      <!-- 工具栏 -->
      <div class="toolbar mb-4 flex justify-between items-center">
        <div class="search-area flex items-center space-x-4">
          <a-input-search v-model:value="searchText" placeholder="搜索交付物名称" style="width: 300px" @search="handleSearch" />
          <a-select v-model:value="categoryFilter" placeholder="筛选分类" style="width: 150px" allow-clear @change="handleCategoryFilter">
            <a-select-option value="document">文档类</a-select-option>
            <a-select-option value="system">系统类</a-select-option>
            <a-select-option value="report">报告类</a-select-option>
            <a-select-option value="plan">计划类</a-select-option>
          </a-select>
          <a-select v-model:value="statusFilter" placeholder="筛选状态" style="width: 150px" allow-clear @change="handleStatusFilter">
            <a-select-option value="active">启用</a-select-option>
            <a-select-option value="inactive">禁用</a-select-option>
          </a-select>
        </div>
        <div class="actions">
          <a-space>
            <a-button type="primary" @click="handleAdd">
              <template #icon><PlusOutlined /></template>
              添加交付物
            </a-button>
            <a-button @click="handleImportFromLibrary">
              <template #icon><ImportOutlined /></template>
              从交付物库导入
            </a-button>
            <a-button @click="handleExport">
              <template #icon><ExportOutlined /></template>
              导出
            </a-button>
            <a-button @click="handleRefresh">
              <template #icon><ReloadOutlined /></template>
              刷新
            </a-button>
          </a-space>
        </div>
      </div>

      <!-- 交付物表格 -->
      <BasicTable @register="registerTable">
        <!-- 自定义列 -->
        <template #deliverableName="{ record }">
          <div class="flex items-center">
            <div class="deliverable-icon mr-2">
              <a-avatar :style="{ backgroundColor: getCategoryColor(record.category) }" :size="24">
                <template #icon>
                  <component :is="getCategoryIcon(record.category)" />
                </template>
              </a-avatar>
            </div>
            <div>
              <div class="font-medium">{{ record.deliverableName }}</div>
              <div class="text-sm text-gray-500">{{ record.deliverableCode }}</div>
            </div>
          </div>
        </template>

        <template #category="{ record }">
          <a-tag :color="getCategoryColor(record.category)">
            {{ getCategoryText(record.category) }}
          </a-tag>
        </template>

        <template #priority="{ record }">
          <a-tag :color="getPriorityColor(record.priority)">
            {{ getPriorityText(record.priority) }}
          </a-tag>
        </template>

        <template #status="{ record }">
          <a-tag :color="record.status === 'active' ? 'green' : 'red'">
            {{ record.status === 'active' ? '启用' : '禁用' }}
          </a-tag>
        </template>

        <template #qualityRequirements="{ record }">
          <div class="quality-requirements">
            <a-tag v-for="requirement in record.qualityRequirements" :key="requirement" size="small" class="mb-1">
              {{ requirement }}
            </a-tag>
            <span v-if="!record.qualityRequirements?.length" class="text-gray-400">-</span>
          </div>
        </template>

        <template #action="{ record }">
          <TableAction
            :actions="[
              {
                icon: 'ant-design:edit-outlined',
                label: '编辑',
                onClick: handleEdit.bind(null, record),
              },
              {
                icon: 'ant-design:copy-outlined',
                label: '复制',
                onClick: handleCopy.bind(null, record),
              },
              {
                icon: 'ant-design:file-text-outlined',
                label: '模板设置',
                onClick: handleTemplate.bind(null, record),
              },
              {
                icon: 'ant-design:delete-outlined',
                label: '删除',
                color: 'error',
                popConfirm: {
                  title: '是否确认删除？',
                  placement: 'left',
                  confirm: handleDelete.bind(null, record),
                },
              },
            ]" />
        </template>
      </BasicTable>
    </a-spin>

    <!-- 添加/编辑弹窗 -->
    <BasicDrawer @register="registerDrawer" :title="drawerTitle" width="50%" @ok="handleSubmit">
      <BasicForm @register="registerForm" />
    </BasicDrawer>

    <!-- 从交付物库导入弹窗 -->
    <BasicModal @register="registerImportModal" title="从交付物库导入" width="80%" @ok="handleImportConfirm">
      <div class="import-content">
        <div class="mb-4 flex space-x-4">
          <a-input-search v-model:value="importSearchText" placeholder="搜索交付物库中的交付物" style="flex: 1" @search="loadLibraryDeliverables" />
          <a-select v-model:value="importCategoryFilter" placeholder="筛选分类" style="width: 150px" allow-clear @change="loadLibraryDeliverables">
            <a-select-option value="document">文档类</a-select-option>
            <a-select-option value="system">系统类</a-select-option>
            <a-select-option value="report">报告类</a-select-option>
            <a-select-option value="plan">计划类</a-select-option>
          </a-select>
        </div>

        <BasicTable @register="registerImportTable" :can-resize="false">
          <template #action="{ record }">
            <a-button type="link" size="small" @click="handleSelectDeliverable(record)" :disabled="isDeliverableSelected(record.id)">
              {{ isDeliverableSelected(record.id) ? '已选择' : '选择' }}
            </a-button>
          </template>
        </BasicTable>

        <!-- 已选择的交付物 -->
        <div v-if="selectedDeliverables.length > 0" class="mt-4">
          <h4 class="mb-2">已选择的交付物 ({{ selectedDeliverables.length }})</h4>
          <div class="selected-deliverables flex flex-wrap gap-2">
            <a-tag v-for="deliverable in selectedDeliverables" :key="deliverable.id" closable @close="handleRemoveDeliverable(deliverable.id)">
              {{ deliverable.deliverableName }}
            </a-tag>
          </div>
        </div>
      </div>
    </BasicModal>

    <!-- 交付物模板设置弹窗 -->
    <BasicModal @register="registerTemplateModal" title="交付物模板设置" width="60%" @ok="handleTemplateSubmit">
      <div class="template-setting">
        <a-form layout="vertical">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="模板文件">
                <a-upload v-model:file-list="templateFiles" :before-upload="() => false" @change="handleTemplateFileChange">
                  <a-button>
                    <UploadOutlined />
                    选择文件
                  </a-button>
                </a-upload>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="模板类型">
                <a-select v-model:value="templateType" placeholder="请选择模板类型">
                  <a-select-option value="word">Word文档</a-select-option>
                  <a-select-option value="excel">Excel表格</a-select-option>
                  <a-select-option value="pdf">PDF文档</a-select-option>
                  <a-select-option value="other">其他</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <a-form-item label="模板说明">
            <a-textarea v-model:value="templateDescription" :rows="3" placeholder="请输入模板使用说明" />
          </a-form-item>
        </a-form>
      </div>
    </BasicModal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed, inject, onMounted } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { BasicForm, useForm } from '/@/components/Form';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import {
    PlusOutlined,
    ImportOutlined,
    ExportOutlined,
    ReloadOutlined,
    FileTextOutlined,
    FileWordOutlined,
    FileExcelOutlined,
    FilePdfOutlined,
    UploadOutlined,
  } from '@ant-design/icons-vue';

  defineOptions({ name: 'ProjectTemplateDeliverableConfigPage' });

  // 注入模板ID
  const templateId = inject<any>('templateId');
  const { createMessage } = useMessage();

  const loading = ref(false);
  const searchText = ref('');
  const categoryFilter = ref('');
  const statusFilter = ref('');
  const drawerTitle = ref('');
  const currentRecord = ref<any>(null);
  const importSearchText = ref('');
  const importCategoryFilter = ref('');
  const selectedDeliverables = ref<any[]>([]);
  const templateFiles = ref<any[]>([]);
  const templateType = ref('');
  const templateDescription = ref('');
  const currentTemplateRecord = ref<any>(null);

  // 表格列配置
  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      width: 60,
      customRender: ({ index }) => index + 1,
    },
    {
      title: '交付物名称',
      dataIndex: 'deliverableName',
      width: 200,
      slots: { customRender: 'deliverableName' },
    },
    {
      title: '分类',
      dataIndex: 'category',
      width: 100,
      slots: { customRender: 'category' },
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      width: 80,
      slots: { customRender: 'priority' },
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 80,
      slots: { customRender: 'status' },
    },
    {
      title: '关联阶段',
      dataIndex: 'associatedStages',
      width: 150,
      customRender: ({ record }) => record.associatedStages?.map(s => s.stageName).join(', ') || '-',
    },
    {
      title: '质量要求',
      dataIndex: 'qualityRequirements',
      width: 200,
      slots: { customRender: 'qualityRequirements' },
    },
    {
      title: '预计工时',
      dataIndex: 'estimatedHours',
      width: 100,
      customRender: ({ text }) => (text ? `${text}小时` : '-'),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 150,
      customRender: ({ text }) => (text ? new Date(text).toLocaleDateString() : '-'),
    },
  ];

  // 表单配置
  const formSchemas = [
    {
      field: 'deliverableName',
      label: '交付物名称',
      component: 'Input',
      required: true,
      colProps: { span: 12 },
    },
    {
      field: 'deliverableCode',
      label: '交付物编码',
      component: 'Input',
      required: true,
      colProps: { span: 12 },
    },
    {
      field: 'category',
      label: '分类',
      component: 'Select',
      required: true,
      componentProps: {
        options: [
          { label: '文档类', value: 'document' },
          { label: '系统类', value: 'system' },
          { label: '报告类', value: 'report' },
          { label: '计划类', value: 'plan' },
        ],
      },
      colProps: { span: 12 },
    },
    {
      field: 'priority',
      label: '优先级',
      component: 'Select',
      required: true,
      componentProps: {
        options: [
          { label: '高', value: 'high' },
          { label: '中', value: 'medium' },
          { label: '低', value: 'low' },
        ],
      },
      colProps: { span: 12 },
    },
    {
      field: 'status',
      label: '状态',
      component: 'RadioGroup',
      defaultValue: 'active',
      componentProps: {
        options: [
          { label: '启用', value: 'active' },
          { label: '禁用', value: 'inactive' },
        ],
      },
      colProps: { span: 24 },
    },
    {
      field: 'associatedStageIds',
      label: '关联阶段',
      component: 'Select',
      componentProps: {
        mode: 'multiple',
        options: [], // 动态加载
      },
      colProps: { span: 24 },
    },
    {
      field: 'qualityRequirements',
      label: '质量要求',
      component: 'Select',
      componentProps: {
        mode: 'tags',
        options: [
          { label: '需要评审', value: '需要评审' },
          { label: '需要测试', value: '需要测试' },
          { label: '需要验收', value: '需要验收' },
          { label: '格式规范', value: '格式规范' },
          { label: '内容完整', value: '内容完整' },
        ],
      },
      colProps: { span: 24 },
    },
    {
      field: 'estimatedHours',
      label: '预计工时(小时)',
      component: 'InputNumber',
      componentProps: {
        min: 0,
        precision: 1,
      },
      colProps: { span: 12 },
    },
    {
      field: 'description',
      label: '描述',
      component: 'InputTextArea',
      componentProps: {
        rows: 3,
      },
      colProps: { span: 24 },
    },
  ];

  // 导入表格列配置
  const importColumns = [
    {
      title: '交付物名称',
      dataIndex: 'deliverableName',
      width: 200,
    },
    {
      title: '分类',
      dataIndex: 'category',
      width: 100,
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      width: 80,
    },
    {
      title: '预计工时',
      dataIndex: 'estimatedHours',
      width: 100,
      customRender: ({ text }) => (text ? `${text}小时` : '-'),
    },
    {
      title: '描述',
      dataIndex: 'description',
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      slots: { customRender: 'action' },
    },
  ];

  // 表格实例
  const [registerTable, { reload, getDataSource }] = useTable({
    api: loadDeliverableData,
    columns,
    useSearchForm: false,
    actionColumn: {
      width: 200,
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
    },
  });

  // 表单实例
  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 100,
    schemas: formSchemas,
    showActionButtonGroup: false,
  });

  // 抽屉实例
  const [registerDrawer, { openDrawer, closeDrawer }] = useDrawerInner();

  // 导入弹窗实例
  const [registerImportModal, { openModal: openImportModal, closeModal: closeImportModal }] = useModalInner();

  // 模板设置弹窗实例
  const [registerTemplateModal, { openModal: openTemplateModal, closeModal: closeTemplateModal }] = useModalInner();

  // 导入表格实例
  const [registerImportTable] = useTable({
    api: loadLibraryDeliverables,
    columns: importColumns,
    useSearchForm: false,
    pagination: {
      pageSize: 10,
    },
  });

  // 加载交付物数据
  async function loadDeliverableData() {
    if (!templateId?.value) return { list: [], total: 0 };

    loading.value = true;
    try {
      // 模拟数据
      const mockData = [
        {
          id: '1',
          deliverableName: '需求规格说明书',
          deliverableCode: 'DOC_REQUIREMENT',
          category: 'document',
          priority: 'high',
          status: 'active',
          associatedStages: [{ id: '2', stageName: '需求分析' }],
          associatedStageIds: ['2'],
          qualityRequirements: ['需要评审', '格式规范', '内容完整'],
          estimatedHours: 40,
          description: '详细描述系统功能和非功能性需求',
          createdAt: '2025-01-15T10:00:00Z',
        },
        {
          id: '2',
          deliverableName: '系统架构设计文档',
          deliverableCode: 'DOC_ARCHITECTURE',
          category: 'document',
          priority: 'high',
          status: 'active',
          associatedStages: [{ id: '3', stageName: '系统设计' }],
          associatedStageIds: ['3'],
          qualityRequirements: ['需要评审', '需要验收'],
          estimatedHours: 30,
          description: '系统整体架构和技术选型说明',
          createdAt: '2025-01-15T10:00:00Z',
        },
        {
          id: '3',
          deliverableName: '测试报告',
          deliverableCode: 'REPORT_TEST',
          category: 'report',
          priority: 'medium',
          status: 'active',
          associatedStages: [],
          associatedStageIds: [],
          qualityRequirements: ['需要测试', '数据准确'],
          estimatedHours: 20,
          description: '系统测试结果和质量评估报告',
          createdAt: '2025-01-15T10:00:00Z',
        },
      ];

      return { list: mockData, total: mockData.length };
    } catch (error) {
      console.error('加载交付物数据失败:', error);
      createMessage.error('加载交付物数据失败');
      return { list: [], total: 0 };
    } finally {
      loading.value = false;
    }
  }

  // 加载交付物库数据
  async function loadLibraryDeliverables() {
    // 模拟交付物库数据
    const mockLibraryData = [
      {
        id: 'lib_1',
        deliverableName: '项目计划书',
        category: 'plan',
        priority: 'high',
        estimatedHours: 16,
        description: '详细的项目实施计划',
      },
      {
        id: 'lib_2',
        deliverableName: '用户手册',
        category: 'document',
        priority: 'medium',
        estimatedHours: 24,
        description: '系统使用操作手册',
      },
      {
        id: 'lib_3',
        deliverableName: '验收报告',
        category: 'report',
        priority: 'high',
        estimatedHours: 8,
        description: '项目验收结果报告',
      },
    ];

    return { list: mockLibraryData, total: mockLibraryData.length };
  }

  // 工具函数
  const getCategoryColor = (category: string) => {
    const colorMap = {
      document: '#1890ff',
      system: '#52c41a',
      report: '#fa8c16',
      plan: '#722ed1',
    };
    return colorMap[category] || '#1890ff';
  };

  const getCategoryIcon = (category: string) => {
    const iconMap = {
      document: FileTextOutlined,
      system: FileWordOutlined,
      report: FileExcelOutlined,
      plan: FilePdfOutlined,
    };
    return iconMap[category] || FileTextOutlined;
  };

  const getCategoryText = (category: string) => {
    const textMap = {
      document: '文档类',
      system: '系统类',
      report: '报告类',
      plan: '计划类',
    };
    return textMap[category] || '未知分类';
  };

  const getPriorityColor = (priority: string) => {
    const colorMap = {
      high: 'red',
      medium: 'orange',
      low: 'green',
    };
    return colorMap[priority] || 'default';
  };

  const getPriorityText = (priority: string) => {
    const textMap = {
      high: '高',
      medium: '中',
      low: '低',
    };
    return textMap[priority] || '未知';
  };

  const isDeliverableSelected = (deliverableId: string) => {
    return selectedDeliverables.value.some(deliverable => deliverable.id === deliverableId);
  };

  // 事件处理
  const handleSearch = () => {
    reload();
  };

  const handleCategoryFilter = () => {
    reload();
  };

  const handleStatusFilter = () => {
    reload();
  };

  const handleAdd = () => {
    currentRecord.value = null;
    drawerTitle.value = '添加交付物';
    resetFields();
    openDrawer();
  };

  const handleEdit = (record: any) => {
    currentRecord.value = record;
    drawerTitle.value = '编辑交付物';
    setFieldsValue(record);
    openDrawer();
  };

  const handleCopy = (record: any) => {
    currentRecord.value = null;
    drawerTitle.value = '复制交付物';
    const copyData = { ...record };
    delete copyData.id;
    copyData.deliverableName = `${record.deliverableName} - 副本`;
    copyData.deliverableCode = `${record.deliverableCode}_COPY`;
    setFieldsValue(copyData);
    openDrawer();
  };

  const handleTemplate = (record: any) => {
    currentTemplateRecord.value = record;
    templateType.value = '';
    templateDescription.value = '';
    templateFiles.value = [];
    openTemplateModal();
  };

  const handleDelete = (record: any) => {
    createMessage.success('删除成功');
    reload();
  };

  const handleSubmit = async () => {
    try {
      const values = await validate();
      console.log('提交交付物数据:', values);

      createMessage.success('保存成功');
      closeDrawer();
      reload();
    } catch (error) {
      console.error('保存失败:', error);
      createMessage.error('保存失败');
    }
  };

  const handleExport = () => {
    createMessage.info('导出功能开发中...');
  };

  const handleRefresh = () => {
    reload();
  };

  const handleImportFromLibrary = () => {
    selectedDeliverables.value = [];
    openImportModal();
  };

  const handleSelectDeliverable = (record: any) => {
    if (!isDeliverableSelected(record.id)) {
      selectedDeliverables.value.push(record);
    }
  };

  const handleRemoveDeliverable = (deliverableId: string) => {
    selectedDeliverables.value = selectedDeliverables.value.filter(deliverable => deliverable.id !== deliverableId);
  };

  const handleImportConfirm = () => {
    if (selectedDeliverables.value.length === 0) {
      createMessage.warning('请选择要导入的交付物');
      return;
    }

    console.log('导入交付物:', selectedDeliverables.value);
    createMessage.success(`成功导入 ${selectedDeliverables.value.length} 个交付物`);
    closeImportModal();
    reload();
  };

  const handleTemplateFileChange = (info: any) => {
    console.log('模板文件变更:', info);
  };

  const handleTemplateSubmit = () => {
    if (!templateType.value) {
      createMessage.warning('请选择模板类型');
      return;
    }

    console.log('提交模板设置:', {
      deliverableId: currentTemplateRecord.value?.id,
      templateType: templateType.value,
      templateDescription: templateDescription.value,
      templateFiles: templateFiles.value,
    });

    createMessage.success('模板设置保存成功');
    closeTemplateModal();
  };

  onMounted(() => {
    console.log('交付物管理页面挂载，模板ID:', templateId?.value);
  });
</script>

<style lang="less" scoped>
  .template-deliverable-page {
    .deliverable-icon {
      display: inline-flex;
      align-items: center;
      justify-content: center;
    }

    .quality-requirements {
      .ant-tag {
        margin-bottom: 4px;
      }
    }

    .import-content {
      .selected-deliverables {
        max-height: 200px;
        overflow-y: auto;
        padding: 12px;
        background: #f5f5f5;
        border-radius: 6px;
      }
    }

    .template-setting {
      .ant-form-item {
        margin-bottom: 16px;
      }
    }
  }
</style>
