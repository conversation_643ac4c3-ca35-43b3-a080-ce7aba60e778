package com.xinghuo.project.biz.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.constant.MsgCode;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.common.util.json.JsonXhUtil;
import com.xinghuo.project.biz.entity.PaymentContractEntity;
import com.xinghuo.project.biz.entity.SupplierEntity;
import com.xinghuo.project.biz.model.paymentContract.PaymentContractPagination;
import com.xinghuo.project.biz.model.paymentContract.PaymentContractVO;
import com.xinghuo.project.biz.model.supplier.SupplierForm;
import com.xinghuo.project.biz.model.supplier.SupplierPagination;
import com.xinghuo.project.biz.model.supplier.SupplierVO;
import com.xinghuo.project.biz.service.PaymentContractService;
import com.xinghuo.project.biz.service.SupplierService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 供应商管理控制器
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Tag(name = "供应商管理", description = "供应商管理相关接口")
@RestController
@RequestMapping("/api/project/biz/supplier")
public class SupplierController {

    @Autowired
    private SupplierService supplierService;

    @Autowired
    private PaymentContractService paymentContractService;

    /**
     * 获取供应商列表
     *
     * @param pagination 分页查询参数
     * @return 供应商列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取供应商列表")
    public ActionResult list(@RequestBody SupplierPagination pagination) {
        List<SupplierEntity> list = supplierService.getList(pagination);
        List<SupplierVO> listVOs = BeanCopierUtils.copyList(list, SupplierVO.class);
        PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
        return ActionResult.page(listVOs,page);
    }

    /**
     * 获取供应商详情
     *
     * @param id 供应商ID
     * @return 供应商详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取供应商详情")
    @Parameters({
            @Parameter(name = "id", description = "供应商ID", required = true),
    })
    public ActionResult<SupplierVO> info(@PathVariable("id") String id) {
        SupplierEntity entity = supplierService.getInfo(id);
        SupplierVO vo = JsonXhUtil.jsonDeepCopy(entity, SupplierVO.class);
        return ActionResult.success(vo);
    }

    /**
     * 创建供应商
     *
     * @param supplierForm 供应商表单
     * @return 操作结果
     */
    @PostMapping
    @Operation(summary = "创建供应商")
    @Parameters({
            @Parameter(name = "supplierForm", description = "供应商表单", required = true),
    })
    public ActionResult create(@RequestBody @Valid SupplierForm supplierForm) {
        // 检查供应商名称是否已存在
        if (supplierService.isExistByName(supplierForm.getName(), null)) {
            return ActionResult.fail("供应商名称已存在");
        }

        SupplierEntity entity = BeanCopierUtils.copy(supplierForm, SupplierEntity.class);
        supplierService.create(entity);
        return ActionResult.success(MsgCode.SU001.get());
    }

    /**
     * 更新供应商
     *
     * @param id           供应商ID
     * @param supplierForm 供应商表单
     * @return 操作结果
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新供应商")
    @Parameters({
            @Parameter(name = "id", description = "供应商ID", required = true),
            @Parameter(name = "supplierForm", description = "供应商表单", required = true),
    })
    public ActionResult update(@PathVariable("id") String id, @RequestBody @Valid SupplierForm supplierForm) {
        // 检查供应商名称是否已存在
        if (supplierService.isExistByName(supplierForm.getName(), id)) {
            return ActionResult.fail("供应商名称已存在");
        }

        SupplierEntity entity = BeanCopierUtils.copy(supplierForm, SupplierEntity.class);
        supplierService.update(id, entity);
        return ActionResult.success(MsgCode.SU002.get());
    }

    /**
     * 删除供应商
     *
     * @param id 供应商ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除供应商")
    @Parameters({
            @Parameter(name = "id", description = "供应商ID", required = true),
    })
    public ActionResult delete(@PathVariable("id") String id) {
        // 检查供应商是否可以删除
        if (!supplierService.canDelete(id)) {
            return ActionResult.fail("该供应商已关联采购合同，无法删除");
        }

        supplierService.delete(id);
        return ActionResult.success(MsgCode.SU003.get());
    }

    /**
     * 检查供应商名称是否已存在
     *
     * @param name 供应商名称
     * @param id   供应商ID（更新时使用，新增时为null）
     * @return 是否存在
     */
    @GetMapping("/check-name")
    @Operation(summary = "检查供应商名称是否已存在")
    @Parameters({
            @Parameter(name = "name", description = "供应商名称", required = true),
            @Parameter(name = "id", description = "供应商ID（更新时使用，新增时为null）"),
    })
    public ActionResult<Boolean> checkName(@RequestParam("name") String name, @RequestParam(value = "id", required = false) String id) {
        boolean exists = supplierService.isExistByName(name, id);
        return ActionResult.success(exists);
    }

    /**
     * 获取供应商选择器列表
     *
     * @param keyword 关键字搜索
     * @return 供应商选择器列表
     */
    @GetMapping("/selector")
    @Operation(summary = "获取供应商选择器列表")
    public ActionResult<?> selector(@RequestParam(value = "keyword", required = false) String keyword) {
        SupplierPagination pagination = new SupplierPagination();
        pagination.setKeyword(keyword);
        pagination.setPageSize(3000); // 限制返回数量

        List<SupplierEntity> list = supplierService.getList(pagination);
        List<Map<String, Object>> selectorList = new ArrayList<>();

        for (SupplierEntity supplier : list) {
            Map<String, Object> option = new HashMap<>();
            option.put("id", supplier.getId());
            option.put("fullName", supplier.getName());
            option.put("name", supplier.getName());
            option.put("code", supplier.getId());
            selectorList.add(option);
        }

        return ActionResult.success(selectorList);
    }

    /**
     * 获取供应商关联的采购合同列表
     *
     * @param supplierId 供应商ID
     * @param pagination 分页查询参数
     * @return 采购合同列表
     */
    @PostMapping("/{supplierId}/paycontracts")
    @Operation(summary = "获取供应商关联的采购合同列表")
    @Parameters({
            @Parameter(name = "supplierId", description = "供应商ID", required = true),
    })
    public ActionResult getSupplierPaycontracts(@PathVariable("supplierId") String supplierId,
                                                @RequestBody PaymentContractPagination pagination) {
        try {
            // 设置供应商ID过滤条件
            pagination.setSuppilerId(supplierId);

            List<PaymentContractEntity> list = paymentContractService.getList(pagination);
            List<PaymentContractVO> listVOs = BeanCopierUtils.copyList(list, PaymentContractVO.class);

            // 填充关联信息
            paymentContractService.fillRelatedInfo(listVOs);

            PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
            return ActionResult.page(listVOs, page);
        } catch (Exception e) {
            return ActionResult.fail("获取采购合同列表失败", e.getMessage());
        }
    }
}
