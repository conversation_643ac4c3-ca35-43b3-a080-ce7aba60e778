import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { h } from 'vue';
import { Tag } from 'ant-design-vue';
import { BusinessWeeklogStatusOptions, ShowStatusOptions } from '/@/api/project/biz/model/businessWeeklogModel';

// 表格列配置
export const columns: BasicColumn[] = [
  {
    title: '项目名称',
    dataIndex: 'projName',
    width: 200,
    fixed: 'left',
  },
  {
    title: '项目类型',
    dataIndex: 'projTypeName',
    width: 100,
  },
  {
    title: '项目级别',
    dataIndex: 'projectLevelName',
    width: 100,
  },
  {
    title: '开始日期',
    dataIndex: 'startDate',
    width: 120,
    format: 'date|YYYY-MM-DD',
  },
  {
    title: '结束日期',
    dataIndex: 'endDate',
    width: 120,
    format: 'date|YYYY-MM-DD',
  },
  {
    title: '负责人',
    dataIndex: 'ownName',
    width: 100,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ record }) => {
      const status = record.status;
      const option = BusinessWeeklogStatusOptions.find(item => item.value === status);
      if (!option) return '';
      return h(Tag, { color: option.color }, () => option.label);
    },
  },
  {
    title: '显示状态',
    dataIndex: 'showStatus',
    width: 100,
    customRender: ({ record }) => {
      const showStatus = record.showStatus;
      const option = ShowStatusOptions.find(item => item.value === showStatus);
      if (!option) return '';
      return h(Tag, { color: option.color }, () => option.label);
    },
  },
  {
    title: '录入日期',
    dataIndex: 'inputDate',
    width: 120,
    format: 'date|YYYY-MM-DD',
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    width: 150,
    format: 'date|YYYY-MM-DD HH:mm:ss',
  },
  {
    title: '创建人',
    dataIndex: 'createdByName',
    width: 100,
  },
];

// 搜索表单配置
export const searchFormSchema: FormSchema[] = [
  {
    field: 'projName',
    label: '项目名称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入项目名称',
    },
    colProps: { span: 8 },
  },
  {
    field: 'projType',
    label: '项目类型',
    component: 'Select',
    componentProps: {
      placeholder: '请选择项目类型',
      options: [
        { label: '商机', value: '商机' },
        { label: '项目', value: '项目' },
        { label: '产品', value: '产品' },
      ],
    },
    colProps: { span: 8 },
  },
  {
    field: 'projectLevel',
    label: '项目级别',
    component: 'Select',
    componentProps: {
      placeholder: '请选择项目级别',
      options: [
        { label: '重要', value: '重要' },
        { label: '一般', value: '一般' },
        { label: '紧急', value: '紧急' },
      ],
    },
    colProps: { span: 8 },
  },
  {
    field: 'ownId',
    label: '负责人',
    component: 'ApiSelect',
    componentProps: {
      api: () => import('/@/api/permission/user').then(m => m.getUserListAll()),
      labelField: 'realName',
      valueField: 'id',
      placeholder: '请选择负责人',
    },
    colProps: { span: 8 },
  },
  {
    field: 'status',
    label: '状态',
    component: 'Select',
    componentProps: {
      placeholder: '请选择状态',
      options: BusinessWeeklogStatusOptions.map(item => ({
        label: item.label,
        value: item.value,
      })),
    },
    colProps: { span: 8 },
  },
  {
    field: 'dateRange',
    label: '开始日期',
    component: 'RangePicker',
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
    colProps: { span: 8 },
  },
];

// 表单配置
export const formSchema: FormSchema[] = [
  {
    field: 'projId',
    label: '项目ID',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入项目ID',
    },
  },
  {
    field: 'projName',
    label: '项目名称',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入项目名称',
    },
  },
  {
    field: 'projType',
    label: '项目类型',
    component: 'Select',
    componentProps: {
      placeholder: '请选择项目类型',
      options: [
        { label: '商机', value: '商机' },
        { label: '项目', value: '项目' },
        { label: '产品', value: '产品' },
      ],
    },
  },
  {
    field: 'projectLevel',
    label: '项目级别',
    component: 'Select',
    componentProps: {
      placeholder: '请选择项目级别',
      options: [
        { label: '重要', value: '重要' },
        { label: '一般', value: '一般' },
        { label: '紧急', value: '紧急' },
      ],
    },
  },
  {
    field: 'fbId',
    label: '所属分部',
    component: 'ApiTreeSelect',
    componentProps: {
      api: () => import('/@/api/permission/organize').then(m => m.getOrganizeTree()),
      fieldNames: {
        label: 'fullName',
        value: 'id',
        children: 'children',
      },
      placeholder: '请选择所属分部',
    },
  },
  {
    field: 'ownId',
    label: '负责人',
    component: 'ApiSelect',
    required: true,
    componentProps: {
      api: () => import('/@/api/permission/user').then(m => m.getUserListAll()),
      labelField: 'realName',
      valueField: 'id',
      placeholder: '请选择负责人',
    },
  },
  {
    field: 'dateRange',
    label: '时间范围',
    component: 'RangePicker',
    required: true,
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    field: 'inputDate',
    label: '录入日期',
    component: 'DatePicker',
    componentProps: {
      placeholder: '请选择录入日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    field: 'projNote',
    label: '项目备注',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入项目备注',
      rows: 3,
    },
  },
  {
    field: 'note',
    label: '备注',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入备注',
      rows: 3,
    },
  },
  {
    field: 'plan',
    label: '计划',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入计划',
      rows: 3,
    },
  },
  {
    field: 'risk',
    label: '风险',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入风险',
      rows: 3,
    },
  },
  {
    field: 'status',
    label: '状态',
    component: 'Select',
    componentProps: {
      placeholder: '请选择状态',
      options: BusinessWeeklogStatusOptions.map(item => ({
        label: item.label,
        value: item.value,
      })),
    },
  },
  {
    field: 'showStatus',
    label: '显示状态',
    component: 'Select',
    componentProps: {
      placeholder: '请选择显示状态',
      options: ShowStatusOptions.map(item => ({
        label: item.label,
        value: item.value,
      })),
    },
  },
];

// 审核表单配置
export const auditFormSchema: FormSchema[] = [
  {
    field: 'status',
    label: '审核结果',
    component: 'RadioGroup',
    required: true,
    componentProps: {
      options: [
        { label: '审核通过', value: 3 },
        { label: '驳回', value: -1 },
      ],
    },
  },
  {
    field: 'auditNote',
    label: '审核意见',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入审核意见',
      rows: 4,
    },
  },
];

// 历史记录表格列配置
export const historyColumns: BasicColumn[] = [
  {
    title: '开始日期',
    dataIndex: 'startDate',
    width: 120,
    format: 'date|YYYY-MM-DD',
  },
  {
    title: '结束日期',
    dataIndex: 'endDate',
    width: 120,
    format: 'date|YYYY-MM-DD',
  },
  {
    title: '负责人',
    dataIndex: 'ownName',
    width: 100,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ record }) => {
      const status = record.status;
      const option = BusinessWeeklogStatusOptions.find(item => item.value === status);
      if (!option) return '';
      return h(Tag, { color: option.color }, () => option.label);
    },
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    width: 150,
    format: 'date|YYYY-MM-DD HH:mm:ss',
  },
  {
    title: '创建人',
    dataIndex: 'createdByName',
    width: 100,
  },
];
