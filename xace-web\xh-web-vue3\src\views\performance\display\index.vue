<template>
  <div class="xh-content-wrapper">
    <div class="xh-content-wrapper-center">
      <div class="xh-content-wrapper-content">
        <div class="xh-basic-bg mb-2 pl-2 pr-2 pt-2">
          <span style="margin-bottom: 8px; display: inline-block">绩效成绩公示快速链接：</span>
          <span @click="handleShortClick(item)" class="month-item-short" v-for="item in state.monthList" :key="item">{{ item }}</span>
        </div>
        <!-- 添加标签页来展示不同的视图 -->
        <a-tabs v-model:activeKey="activeTab" @change="handleTabChange">
          <a-tab-pane key="table" tab="表格视图">
            <BasicTable @register="registerTable" ref="tableRef">
              <template #form-advanceBefore>
                <a-button @click="handleExport" color="success">导出</a-button>
                <a-button @click="handleAnalysis" type="primary" style="margin-left: 8px">
                  <template #icon>
                    <Icon icon="ant-design:bar-chart-outlined" />
                  </template>
                  绩效报表分析
                </a-button>
              </template>
            </BasicTable>
          </a-tab-pane>

          <a-tab-pane key="grouped" tab="分组展示">
            <GroupedPerformanceDisplay :dataSource="groupedDisplayData" :loading="loading" @refresh="handleRefresh" @export="handleGroupedExport" />
          </a-tab-pane>

          <a-tab-pane key="analytics" tab="数据分析">
            <PerformanceAnalytics />
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
  </div>
</template>
<script lang="tsx" setup>
  import { reactive, ref } from 'vue';
  import { BasicTable, useTable, BasicColumn } from '/@/components/Table';
  import { getMonthlyDisplayList, getMultipleMonthsDisplayList, monthList } from './api';
  import { sortChineseStr } from '/@/utils/xh';
  import dayjs from 'dayjs';
  import { isNullOrUnDef } from '/@/utils/is';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { jsonToSheetXlsx } from '/@/components/Excel';
  import { pick } from 'lodash-es';
  import { useComponentRegister } from '/@/components/Form/index';
  import { Icon } from '/@/components/Icon';
  import { useRouter } from 'vue-router';
  import FbSelect from '../common/fbSelect.vue';
  import GroupedPerformanceDisplay from '../components/GroupedPerformanceDisplay.vue';
  import PerformanceAnalytics from '../components/PerformanceAnalytics.vue';

  defineOptions({ name: 'performance-display' });
  // @ts-expect-error
  useComponentRegister('FbSelect', FbSelect);
  const { createMessage } = useMessage();
  const router = useRouter();

  // 标签页相关状态
  const activeTab = ref('table');
  const loading = ref(false);
  const groupedDisplayData = ref([]);
  const state = reactive({
    monthList: [],
  });
  const defaultDate = dayjs().subtract(1, 'month').format('YYYYMM');
  let exportColumns: Recordable[] = [];
  let exportData: Recordable[] = [];
  let exportParams: { [prop: string]: string } = {};
  const monthlyColumns: BasicColumn[] = [
    { title: '所属部门', dataIndex: 'fbName', width: 120, align: 'center', sorter: (a, b) => sortChineseStr(a.fbName, b.fbName) },
    { title: '所在分组', dataIndex: 'groupName', width: 120, align: 'center', sorter: (a, b) => sortChineseStr(a.groupName || '', b.groupName || '') },
    { title: '员工姓名', dataIndex: 'userName', width: 120, align: 'center', sorter: (a, b) => sortChineseStr(a.userName, b.userName) },
    { title: '绩效得分', dataIndex: 'actScore', width: 120, align: 'center', sorter: (a, b) => a.actScore - b.actScore },
    {
      title: '考核人',
      dataIndex: 'parentUserName',
      width: 180,
      align: 'left',
      sorter: (a, b) => sortChineseStr(a.parentUserName, b.parentUserName),
      customRender: opt => <span className="whitespace-pre-line">{(opt.text || '').replace(/,/g, '\n')}</span>,
    },
    { title: '考核信息', dataIndex: 'scoreNote', customRender: opt => <span v-html={opt.text}></span> },
  ];
  const multipleColumns: BasicColumn[] = [
    { title: '所属部门', dataIndex: 'fbName', width: 120, align: 'center', sorter: (a, b) => sortChineseStr(a.fbName, b.fbName) },
    { title: '所在分组', dataIndex: 'groupName', width: 120, align: 'center', sorter: (a, b) => sortChineseStr(a.groupName || '', b.groupName || '') },
    { title: '员工姓名', dataIndex: 'userName', width: 120, align: 'center', sorter: (a, b) => sortChineseStr(a.userName, b.userName) },
    { title: '绩效总分', dataIndex: 'actScore', width: 120, align: 'center', sorter: (a, b) => a.actScore - b.actScore },
    {
      title: '应工作/实际数[假]',
      dataIndex: '_shouldWorkDays',
      width: 150,
      align: 'center',
      customRender: ({ record }) => {
        return record.shouldWorkDays + '/' + record.actWorkDays;
      },
    },
    { title: '绩效平均分', dataIndex: 'actAvgScore', width: 120, sorter: (a, b) => a.actAvgScore - b.actAvgScore },
  ];
  const [registerTable, { setProps, getForm, reload }] = useTable({
    api: getMonthlyDisplayList,
    columns: monthlyColumns,
    immediate: true,
    useSearchForm: true,
    pagination: false,
    showTableSetting: false,
    bordered: true,
    ellipsis: false,
    searchInfo: { pageSize: 99999, currentPage: 1 },
    beforeFetch: params => {
      if (params.startMonth > params.endMonth) {
        createMessage.warning('开始月份不能大于结束月份！');
        return Promise.reject();
      }
      setProps({
        api: params.endMonth !== params.startMonth ? getMultipleMonthsDisplayList : getMonthlyDisplayList,
      });
      exportParams = params;
      return Promise.resolve(params);
    },
    afterFetch: data => {
      exportData = data;
      exportColumns = data?.[0]?.monthColumn
        ? multipleColumns.concat(
            data?.[0]?.monthColumn.map(t => ({
              title: t,
              dataIndex: t,
              minWidth: 80,
              align: 'center',
              customRender: opt => (isNullOrUnDef(opt.text) ? '-' : opt.text),
            })),
          )
        : monthlyColumns;
      setProps({ columns: exportColumns });
    },
    formConfig: {
      schemas: [
        {
          field: 'fbId',
          label: '所属分部',
          // @ts-expect-error
          component: 'FbSelect',
          colProps: {
            span: 4,
          },
          componentProps: {
            placeholder: '请选择',
          },
        },
        {
          field: 'startMonth',
          label: '开始月份',
          component: 'MonthPicker',
          defaultValue: defaultDate,
          colProps: {
            span: 4,
          },
          componentProps: {
            placeholder: '请选择',
            valueFormat: 'YYYYMM',
            allowClear: false,
          },
        },
        {
          field: 'endMonth',
          label: '结束月份',
          component: 'MonthPicker',
          defaultValue: defaultDate,
          colProps: {
            span: 4,
          },
          componentProps: {
            placeholder: '请选择',
            valueFormat: 'YYYYMM',
            allowClear: false,
          },
        },
        {
          field: 'userId',
          label: '员工姓名',
          component: 'UserSelectDropdown',
          colProps: {
            span: 4,
          },
          componentProps: {
            placeholder: '请输入',
          },
        },
      ],
    },
  });

  monthList().then(({ data }) => {
    state.monthList = data || [];
  });

  function handleShortClick(month) {
    getForm()
      .setFieldsValue({
        startMonth: month,
        endMonth: month,
      })
      .then(() => {
        reload();
      });
  }

  function handleExport() {
    const header = {};
    const headerOrders: string[] = [];
    exportColumns.forEach(t => {
      header[t.dataIndex] = t.title;
      headerOrders.push(t.dataIndex);
    });
    const { startMonth, endMonth } = exportParams;
    jsonToSheetXlsx({
      data: exportData.map(t => {
        t._shouldWorkDays = t.shouldWorkDays + '/' + t.actWorkDays;
        return pick(t, headerOrders);
      }),
      header: header,
      filename: `绩效公示${startMonth}-${endMonth}.xlsx`,
      json2sheetOpts: {
        // 指定顺序
        header: headerOrders,
      },
    });
  }

  function handleAnalysis() {
    // 跳转到绩效报表分析页面
    router.push('/performance/analysis');
  }

  // 标签页切换处理
  function handleTabChange(key: string) {
    activeTab.value = key;

    // 当切换到分组展示时，加载分组数据
    if (key === 'grouped') {
      loadGroupedDisplayData();
    }
  }

  // 加载分组展示数据
  async function loadGroupedDisplayData() {
    try {
      loading.value = true;

      // 获取当前表格的数据作为分组展示的数据源
      const tableData = getDataSource();

      // 转换数据格式以适配 GroupedPerformanceDisplay 组件
      groupedDisplayData.value = tableData.map((item: any) => ({
        ...item,
        // 确保数据格式符合组件要求
        id: item.id || item.userId,
        name: item.realName || item.userName,
        department: item.fbName || item.departmentName,
        score: item.totalScore || item.score,
        status: item.status || '已完成',
      }));
    } catch (error) {
      console.error('加载分组展示数据失败:', error);
      createMessage.error('加载分组展示数据失败');
    } finally {
      loading.value = false;
    }
  }

  // 刷新数据
  function handleRefresh() {
    // 重新加载当前标签页的数据
    if (activeTab.value === 'table') {
      reload();
    } else if (activeTab.value === 'grouped') {
      loadGroupedDisplayData();
    }
    createMessage.success('数据刷新成功');
  }

  // 分组展示导出
  function handleGroupedExport(data: any) {
    try {
      const exportData = data || groupedDisplayData.value;

      // 构建导出数据
      const header = {
        name: '姓名',
        department: '部门',
        score: '绩效得分',
        status: '状态',
      };

      const headerOrders = ['name', 'department', 'score', 'status'];

      jsonToSheetXlsx({
        data: exportData.map((item: any) => pick(item, headerOrders)),
        header: header,
        filename: `绩效分组展示_${dayjs().format('YYYY-MM-DD')}.xlsx`,
        json2sheetOpts: {
          header: headerOrders,
        },
      });

      createMessage.success('分组数据导出成功');
    } catch (error) {
      console.error('分组数据导出失败:', error);
      createMessage.error('分组数据导出失败');
    }
  }
</script>

<style scoped lang="less">
  .month-item-short {
    display: inline-block;
    margin: 0 8px 8px 0;
    cursor: pointer;
    color: #40a9ff;
    &:hover {
      color: red;
    }
  }
</style>
