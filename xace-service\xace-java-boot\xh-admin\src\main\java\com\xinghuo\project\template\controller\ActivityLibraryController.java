package com.xinghuo.project.template.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.project.template.entity.ActivityLibraryEntity;
import com.xinghuo.project.template.model.ActivityLibraryPagination;
import com.xinghuo.project.template.model.vo.ActivityLibraryVO;
import com.xinghuo.project.template.model.vo.ActivityLibrarySelectVO;
import com.xinghuo.project.template.service.ActivityLibraryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 标准项目活动库管理控制器
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Slf4j
@Tag(name = "标准项目活动库管理", description = "标准项目活动库管理相关接口")
@RestController
@RequestMapping("/api/project/template/activityLibrary")
public class ActivityLibraryController {

    @Resource
    private ActivityLibraryService activityLibraryService;

    /**
     * 获取活动库列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取活动库列表")
    public ActionResult<PageListVO<ActivityLibraryVO>> list(@RequestBody ActivityLibraryPagination pagination) {
        try {
            List<ActivityLibraryEntity> list = activityLibraryService.getList(pagination);
            List<ActivityLibraryVO> listVO = BeanCopierUtils.copyList(list, ActivityLibraryVO.class);

            // 对结果进行数据转换和补充
            for (ActivityLibraryVO vo : listVO) {
                // 状态名称转换
                if (vo.getStatus() != null) {
                    vo.setStatusName(vo.getStatus() == 0 ? "启用" : "禁用");
                }

                // 里程碑标识名称转换
                if (vo.getIsMilestone() != null) {
                    vo.setIsMilestoneName(vo.getIsMilestone() == 1 ? "是" : "否");
                }

                // TODO: 可以在这里添加其他关联数据的查询和设置
                // 例如：创建用户名称、角色名称、活动类型名称等
            }

            PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
            return ActionResult.page(listVO, page);
        } catch (Exception e) {
            log.error("获取活动库列表失败", e);
            return ActionResult.fail("获取活动库列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据状态获取活动库列表
     */
    @GetMapping("/getListByStatus/{status}")
    @Operation(summary = "根据状态获取活动库列表")
    public ActionResult<List<ActivityLibraryEntity>> getListByStatus(
            @Parameter(description = "状态") @PathVariable Integer status) {
        try {
            List<ActivityLibraryEntity> list = activityLibraryService.getListByStatus(status);
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("根据状态获取活动库列表失败", e);
            return ActionResult.fail("获取活动库列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据活动类型获取活动库列表
     */
    @GetMapping("/getListByActivityType/{activityTypeId}")
    @Operation(summary = "根据活动类型获取活动库列表")
    public ActionResult<List<ActivityLibraryEntity>> getListByActivityType(
            @Parameter(description = "活动类型ID") @PathVariable String activityTypeId) {
        try {
            List<ActivityLibraryEntity> list = activityLibraryService.getListByActivityType(activityTypeId);
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("根据活动类型获取活动库列表失败", e);
            return ActionResult.fail("获取活动库列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据里程碑标识获取活动库列表
     */
    @GetMapping("/getListByMilestone/{isMilestone}")
    @Operation(summary = "根据里程碑标识获取活动库列表")
    public ActionResult<List<ActivityLibraryEntity>> getListByMilestone(
            @Parameter(description = "是否是里程碑") @PathVariable Integer isMilestone) {
        try {
            List<ActivityLibraryEntity> list = activityLibraryService.getListByMilestone(isMilestone);
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("根据里程碑标识获取活动库列表失败", e);
            return ActionResult.fail("获取活动库列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取活动库详情
     */
    @GetMapping("/getInfo/{id}")
    @Operation(summary = "获取活动库详情")
    public ActionResult<ActivityLibraryEntity> getInfo(
            @Parameter(description = "活动库ID") @PathVariable String id) {
        try {
            ActivityLibraryEntity entity = activityLibraryService.getInfo(id);
            if (entity == null) {
                return ActionResult.fail("活动库不存在");
            }
            return ActionResult.success(entity);
        } catch (Exception e) {
            log.error("获取活动库详情失败", e);
            return ActionResult.fail("获取活动库详情失败：" + e.getMessage());
        }
    }

    /**
     * 根据编码获取活动库
     */
    @GetMapping("/getByCode/{code}")
    @Operation(summary = "根据编码获取活动库")
    public ActionResult<ActivityLibraryEntity> getByCode(
            @Parameter(description = "活动编码") @PathVariable String code) {
        try {
            ActivityLibraryEntity entity = activityLibraryService.getByCode(code);
            if (entity == null) {
                return ActionResult.fail("活动库不存在");
            }
            return ActionResult.success(entity);
        } catch (Exception e) {
            log.error("根据编码获取活动库失败", e);
            return ActionResult.fail("获取活动库失败：" + e.getMessage());
        }
    }

    /**
     * 创建活动库
     */
    @PostMapping("/create")
    @Operation(summary = "创建活动库")
    public ActionResult<String> create(@RequestBody @Valid ActivityLibraryEntity entity) {
        try {
            String id = activityLibraryService.create(entity);
            return ActionResult.success("创建成功", id);
        } catch (Exception e) {
            log.error("创建活动库失败", e);
            return ActionResult.fail("创建活动库失败：" + e.getMessage());
        }
    }

    /**
     * 更新活动库
     */
    @PutMapping("/update/{id}")
    @Operation(summary = "更新活动库")
    public ActionResult<String> update(
            @Parameter(description = "活动库ID") @PathVariable String id,
            @RequestBody @Valid ActivityLibraryEntity entity) {
        try {
            activityLibraryService.update(id, entity);
            return ActionResult.success("更新成功");
        } catch (Exception e) {
            log.error("更新活动库失败", e);
            return ActionResult.fail("更新活动库失败：" + e.getMessage());
        }
    }

    /**
     * 删除活动库
     */
    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除活动库")
    public ActionResult<String> delete(
            @Parameter(description = "活动库ID") @PathVariable String id) {
        try {
            activityLibraryService.delete(id);
            return ActionResult.success("删除成功");
        } catch (Exception e) {
            log.error("删除活动库失败", e);
            return ActionResult.fail("删除活动库失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除活动库
     */
    @DeleteMapping("/batchDelete")
    @Operation(summary = "批量删除活动库")
    public ActionResult<String> batchDelete(@RequestBody List<String> ids) {
        try {
            activityLibraryService.batchDelete(ids);
            return ActionResult.success("批量删除成功");
        } catch (Exception e) {
            log.error("批量删除活动库失败", e);
            return ActionResult.fail("批量删除活动库失败：" + e.getMessage());
        }
    }

    /**
     * 更新活动库状态
     */
    @PutMapping("/updateStatus/{id}")
    @Operation(summary = "更新活动库状态")
    public ActionResult<String> updateStatus(
            @Parameter(description = "活动库ID") @PathVariable String id,
            @RequestParam Integer status) {
        try {
            activityLibraryService.updateStatus(id, status);
            return ActionResult.success("状态更新成功");
        } catch (Exception e) {
            log.error("更新活动库状态失败", e);
            return ActionResult.fail("更新活动库状态失败：" + e.getMessage());
        }
    }

    /**
     * 批量更新状态
     */
    @PutMapping("/batchUpdateStatus")
    @Operation(summary = "批量更新状态")
    public ActionResult<String> batchUpdateStatus(
            @RequestBody List<String> ids,
            @RequestParam Integer status) {
        try {
            activityLibraryService.batchUpdateStatus(ids, status);
            return ActionResult.success("批量更新状态成功");
        } catch (Exception e) {
            log.error("批量更新活动库状态失败", e);
            return ActionResult.fail("批量更新状态失败：" + e.getMessage());
        }
    }

    /**
     * 启用活动库
     */
    @PutMapping("/enable/{id}")
    @Operation(summary = "启用活动库")
    public ActionResult<String> enable(
            @Parameter(description = "活动库ID") @PathVariable String id) {
        try {
            activityLibraryService.enable(id);
            return ActionResult.success("启用成功");
        } catch (Exception e) {
            log.error("启用活动库失败", e);
            return ActionResult.fail("启用活动库失败：" + e.getMessage());
        }
    }

    /**
     * 禁用活动库
     */
    @PutMapping("/disable/{id}")
    @Operation(summary = "禁用活动库")
    public ActionResult<String> disable(
            @Parameter(description = "活动库ID") @PathVariable String id) {
        try {
            activityLibraryService.disable(id);
            return ActionResult.success("禁用成功");
        } catch (Exception e) {
            log.error("禁用活动库失败", e);
            return ActionResult.fail("禁用活动库失败：" + e.getMessage());
        }
    }

    /**
     * 复制活动库
     */
    @PostMapping("/copy/{id}")
    @Operation(summary = "复制活动库")
    public ActionResult<String> copy(
            @Parameter(description = "活动库ID") @PathVariable String id,
            @RequestParam String newName) {
        try {
            String newId = activityLibraryService.copy(id, newName);
            return ActionResult.success("复制成功", newId);
        } catch (Exception e) {
            log.error("复制活动库失败", e);
            return ActionResult.fail("复制活动库失败：" + e.getMessage());
        }
    }

    /**
     * 检查活动编码是否存在
     */
    @GetMapping("/checkCodeExists")
    @Operation(summary = "检查活动编码是否存在")
    public ActionResult<Boolean> checkCodeExists(
            @RequestParam String code,
            @RequestParam(required = false) String excludeId) {
        try {
            boolean exists = activityLibraryService.isExistByCode(code, excludeId);
            return ActionResult.success(exists);
        } catch (Exception e) {
            log.error("检查活动编码失败", e);
            return ActionResult.fail("检查活动编码失败：" + e.getMessage());
        }
    }

    /**
     * 检查活动名称是否存在
     */
    @GetMapping("/checkNameExists")
    @Operation(summary = "检查活动名称是否存在")
    public ActionResult<Boolean> checkNameExists(
            @RequestParam String name,
            @RequestParam(required = false) String excludeId) {
        try {
            boolean exists = activityLibraryService.isExistByName(name, excludeId);
            return ActionResult.success(exists);
        } catch (Exception e) {
            log.error("检查活动名称失败", e);
            return ActionResult.fail("检查活动名称失败：" + e.getMessage());
        }
    }

    /**
     * 获取活动库选择列表
     */
    @GetMapping("/getSelectList")
    @Operation(summary = "获取活动库选择列表")
    public ActionResult<List<ActivityLibrarySelectVO>> getSelectList(
            @RequestParam(required = false) String keyword) {
        try {
            List<ActivityLibraryEntity> list = activityLibraryService.getSelectList(keyword);
            List<ActivityLibrarySelectVO> listVO = BeanCopierUtils.copyList(list, ActivityLibrarySelectVO.class);

            // 构建fullName字段，格式：[编码] 名称
            for (ActivityLibrarySelectVO vo : listVO) {
                if (vo.getCode() != null && vo.getName() != null) {
                    vo.setFullName("[" + vo.getCode() + "] " + vo.getName());
                } else if (vo.getName() != null) {
                    vo.setFullName(vo.getName());
                } else {
                    vo.setFullName(vo.getId());
                }
            }

            return ActionResult.success(listVO);
        } catch (Exception e) {
            log.error("获取活动库选择列表失败", e);
            return ActionResult.fail("获取活动库选择列表失败：" + e.getMessage());
        }
    }

    /**
     * 生成活动编码
     */
    @GetMapping("/generateCode")
    @Operation(summary = "生成活动编码")
    public ActionResult<String> generateCode() {
        try {
            String code = activityLibraryService.generateCode();
            return ActionResult.success(code);
        } catch (Exception e) {
            log.error("生成活动编码失败", e);
            return ActionResult.fail("生成活动编码失败：" + e.getMessage());
        }
    }

    /**
     * 获取活动库使用情况
     */
    @GetMapping("/getActivityLibraryUsageInfo/{id}")
    @Operation(summary = "获取活动库使用情况")
    public ActionResult<Map<String, Object>> getActivityLibraryUsageInfo(
            @Parameter(description = "活动库ID") @PathVariable String id) {
        try {
            Map<String, Object> usageInfo = activityLibraryService.getActivityLibraryUsageInfo(id);
            return ActionResult.success(usageInfo);
        } catch (Exception e) {
            log.error("获取活动库使用情况失败", e);
            return ActionResult.fail("获取活动库使用情况失败：" + e.getMessage());
        }
    }
}
