# XACE 前端架构与技术栈

## 核心技术栈

### 基础技术
- **框架**：Vue 3.3.x
- **开发语言**：TypeScript 5.x
- **构建工具**：Vite 4.x
- **UI框架**：VbenAdmin 2.x
- **UI组件库**：Ant Design Vue 3.2.x
- **状态管理**：Pinia 2.x
- **路由管理**：Vue Router 4.x
- **HTTP客户端**：Axios
- **CSS预处理器**：Less

### 工具库生态
- **Lodash-es**：工具函数库
- **Dayjs**：日期处理库
- **Echarts**：图表可视化
- **VueUse**：组合式API工具集
- **Sortablejs**：拖拽排序
- **vue-i18n**：国际化方案

## 开发环境配置

### 环境要求
- **Node.js**：16.15.0+
- **包管理器**：pnpm 8.1.0+（推荐）或 yarn
- **TypeScript**：5.x 严格模式

### VS Code 插件推荐
- Vue Language Features (Volar)
- TypeScript Vue Plugin (Volar)
- ESLint
- Prettier
- i18n Ally（国际化支持）

### 代码质量工具
- **Prettier**：代码格式化
- **ESLint**：代码质量检查
- **StyleLint**：样式代码规范检查
- **CommitLint**：Git提交信息规范
- **Husky**：Git钩子管理
- **lint-staged**：提交前代码检查

## 项目架构模式

### 核心设计模式
- **组件设计**：Composition API + `<script setup>` 语法
- **状态管理**：Store模式 (Pinia)
- **API调用**：基于Promise的服务层封装
- **权限控制**：基于角色的访问控制 (RBAC)
- **主题方案**：基于CSS变量的动态主题切换

### 性能优化策略
- 路由懒加载
- 组件按需导入
- 大型依赖库按需引入
- 图片资源优化（WebP格式、适当压缩）
- 虚拟滚动处理大数据列表
- 合理使用缓存策略

## 项目目录结构

```
xace-web-vue3/
├── build/                  # 构建相关配置
├── public/                 # 静态资源
├── src/
│   ├── api/                # 框架公用后端API接口定义
│   ├── assets/             # 项目资源文件
│   │   ├── icons/          # 图标资源
│   │   └── images/         # 图片资源
│   ├── components/         # 公共组件
│   │   ├── Basic/          # 基础UI组件
│   │   ├── Form/           # 表单相关组件
│   │   ├── Table/          # 表格相关组件
│   │   └── Xh/             # 自定义业务组件
│   ├── directives/         # 自定义指令
│   ├── enums/              # 枚举定义
│   ├── hooks/              # 业务逻辑钩子
│   │   ├── core/           # 核心钩子函数
│   │   ├── event/          # 事件相关钩子
│   │   └── web/            # Web API相关钩子
│   ├── layouts/            # 布局组件
│   ├── locales/            # 国际化资源
│   ├── router/             # 路由配置
│   │   ├── guard/          # 路由守卫
│   │   ├── modules/        # 路由模块
│   │   └── helper/         # 路由辅助函数
│   ├── store/              # 状态管理
│   │   └── modules/        # 状态模块
│   ├── utils/              # 工具函数
│   │   ├── auth/           # 认证相关
│   │   ├── cache/          # 缓存相关
│   │   ├── http/           # HTTP请求相关
│   │   └── helper/         # 辅助函数
│   ├── views/              # 页面组件（按业务模块划分）
│   ├── App.vue             # 根组件
│   └── main.ts             # 入口文件
├── types/                  # 全局类型定义
├── .env                    # 环境变量
├── .env.development        # 开发环境变量
├── .env.production         # 生产环境变量
├── .env.test               # 测试环境变量
├── .eslintrc.js            # ESLint配置
├── .prettierrc             # Prettier配置
├── stylelint.config.js     # StyleLint配置
├── tsconfig.json           # TypeScript配置
├── vite.config.ts          # Vite配置
└── package.json            # 项目依赖
```

## 命名规范

### 文件与文件夹命名

| 类型 | 命名规则 | 示例 |
|------|----------|------|
| 组件文件 | PascalCase | `UserProfile.vue`, `DataTable.vue` |
| 基础组件 | Base前缀 | `BaseButton.vue`, `BaseInput.vue` |
| 单例组件 | The前缀 | `TheHeader.vue`, `TheFooter.vue` |
| 业务组件 | Biz前缀 | `BizProjectCard.vue`, `BizUserSelector.vue` |
| Hook文件 | use前缀 | `useUserState.ts`, `usePermission.ts` |
| 工具函数 | 动词开头 | `formatTime.ts`, `validateEmail.ts` |
| API服务 | 模块名称 | `userApi.ts`, `projectApi.ts` |
| 视图文件夹 | kebab-case | `user-management/`, `project-list/` |

### 组件命名约定

1. **组件名**：PascalCase，多个单词组成，与文件名保持一致
2. **Props**：camelCase，布尔类型使用 `is/has/should` 前缀
3. **事件名**：kebab-case，使用动词表示动作
4. **事件处理函数**：handle前缀，如 `handleClick`

### 变量命名规范

| 类型 | 命名规则 | 示例 |
|------|----------|------|
| 常量 | 全大写下划线 | `MAX_COUNT`, `API_URL` |
| 变量 | camelCase | `userData`, `projectList` |
| 布尔变量 | is/has/should前缀 | `isLoading`, `hasPermission` |
| 数组 | 复数形式 | `users`, `projects` |
| 私有属性 | 下划线前缀 | `_privateMethod`, `_internalData` |

### 样式命名

1. **类名**：kebab-case，使用BEM命名法则
2. **组件作用域**：使用scoped样式或CSS Modules
3. **CSS变量**：kebab-case，按功能分类

## 模块组织原则

### 业务模块划分
按功能域划分模块，同一业务功能的组件、API、类型、工具等放在一起：

```
views/project-management/
├── components/           # 该功能特有组件
│   ├── ProjectCard.vue
│   └── ProjectForm.vue
├── hooks/                # 该功能特有hooks
│   └── useProjectData.ts
├── api/                  # 可选，模块特定API
│   └── project.ts
├── constants.ts          # 常量定义
├── types.ts              # 类型定义
├── utils.ts              # 工具函数
├── list.vue              # 列表页面
├── detail.vue            # 详情页面
└── index.vue             # 入口页面
```

### 导入顺序规范

遵循以下导入顺序：

```typescript
// 1. 外部库导入
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';

// 2. 类型导入
import type { UserInfo } from '/@/types/user';

// 3. API导入
import { getUserInfo } from '/@/api/modules/user';

// 4. 组件导入
import UserAvatar from '/@/components/Xh/UserAvatar.vue';

// 5. 工具/Hook导入
import { usePermission } from '/@/hooks/usePermission';
import { formatDate } from '/@/utils/formatDate';

// 6. 常量/枚举导入
import { UserStatus } from '/@/enums/user';
import { MAX_FILE_SIZE } from '/@/constants';

// 7. 资源导入
import defaultAvatar from '/@/assets/images/default-avatar.png';

// 8. 样式导入
import styles from './index.module.less';
```

## 关键依赖版本

```json
{
  "vue": "3.3.4",
  "pinia": "2.1.3",
  "vue-router": "4.4.0",
  "ant-design-vue": "3.2.20",
  "typescript": "5.0.4",
  "vite": "4.3.8",
  "axios": "1.4.0",
  "vue-i18n": "9.2.2",
  "@vueuse/core": "10.1.2"
}
```

## 浏览器兼容性

项目支持以下现代浏览器：
- Chrome (最新2个版本)
- Firefox (最新2个版本)
- Edge (最新2个版本)
- Safari (最新2个版本)

> 不支持Internet Explorer浏览器

## 代码组织原则

1. **单一职责原则**：每个组件、函数、类只负责一个功能
2. **可复用性原则**：抽取通用逻辑到hooks或工具函数
3. **可测试性原则**：纯函数优先，UI与业务逻辑分离

## 参考文档

- [Vue 3 官方文档](https://cn.vuejs.org/)
- [TypeScript 官方文档](https://www.typescriptlang.org/)
- [Vite 官方文档](https://cn.vitejs.dev/)
- [Ant Design Vue 文档](https://antdv.com/)
- [Pinia 官方文档](https://pinia.vuejs.org/zh/)
- [Vue Router 官方文档](https://router.vuejs.org/zh/)
- [VueUse 官方文档](https://vueuse.org/)
- [Vue I18n 官方文档](https://vue-i18n.intlify.dev/)