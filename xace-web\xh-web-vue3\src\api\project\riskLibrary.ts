import { defHttp } from '/@/utils/http/axios';

enum Api {
  GetList = '/api/project/template/riskLibrary/getList',
  GetListByStatus = '/api/project/template/riskLibrary/getListByStatus',
  GetListByRiskCategory = '/api/project/template/riskLibrary/getListByRiskCategory',
  GetListByProbabilityLevel = '/api/project/template/riskLibrary/getListByProbabilityLevel',
  GetListByImpactLevel = '/api/project/template/riskLibrary/getListByImpactLevel',
  GetInfo = '/api/project/template/riskLibrary/getInfo',
  GetByCode = '/api/project/template/riskLibrary/getByCode',
  Create = '/api/project/template/riskLibrary/create',
  Update = '/api/project/template/riskLibrary/update',
  Delete = '/api/project/template/riskLibrary/delete',
  BatchDelete = '/api/project/template/riskLibrary/batchDelete',
  UpdateStatus = '/api/project/template/riskLibrary/updateStatus',
  BatchUpdateStatus = '/api/project/template/riskLibrary/batchUpdateStatus',
  Publish = '/api/project/template/riskLibrary/publish',
  Archive = '/api/project/template/riskLibrary/archive',
  BatchPublish = '/api/project/template/riskLibrary/batchPublish',
  BatchArchive = '/api/project/template/riskLibrary/batchArchive',
  Copy = '/api/project/template/riskLibrary/copy',
  CheckCodeExists = '/api/project/template/riskLibrary/checkCodeExists',
  CheckTitleExists = '/api/project/template/riskLibrary/checkTitleExists',
  GetSelectList = '/api/project/template/riskLibrary/getSelectList',
  GenerateCode = '/api/project/template/riskLibrary/generateCode',
  GetUsageInfo = '/api/project/template/riskLibrary/getRiskLibraryUsageInfo',
}

/**
 * 标准项目风险库接口
 */

// 获取风险库列表
export function getRiskLibraryList(params: any) {
  return defHttp.post({
    url: Api.GetList,
    data: params,
  });
}

// 根据状态获取风险库列表
export function getRiskLibraryListByStatus(status: string) {
  return defHttp.get({
    url: `${Api.GetListByStatus}/${status}`,
  });
}

// 根据风险类别获取风险库列表
export function getRiskLibraryListByRiskCategory(riskCategoryId: string) {
  return defHttp.get({
    url: `${Api.GetListByRiskCategory}/${riskCategoryId}`,
  });
}

// 根据概率等级获取风险库列表
export function getRiskLibraryListByProbabilityLevel(probabilityLevelId: string) {
  return defHttp.get({
    url: `${Api.GetListByProbabilityLevel}/${probabilityLevelId}`,
  });
}

// 根据影响等级获取风险库列表
export function getRiskLibraryListByImpactLevel(impactLevelId: string) {
  return defHttp.get({
    url: `${Api.GetListByImpactLevel}/${impactLevelId}`,
  });
}

// 获取风险库详情
export function getRiskLibraryInfo(id: string) {
  return defHttp.get({
    url: `${Api.GetInfo}/${id}`,
  });
}

// 根据编码获取风险库
export function getRiskLibraryByCode(code: string) {
  return defHttp.get({
    url: `${Api.GetByCode}/${code}`,
  });
}

// 创建风险库
export function createRiskLibrary(params: any) {
  return defHttp.post({
    url: Api.Create,
    data: params,
  });
}

// 更新风险库
export function updateRiskLibrary(id: string, params: any) {
  return defHttp.put({
    url: `${Api.Update}/${id}`,
    data: params,
  });
}

// 删除风险库
export function deleteRiskLibrary(id: string) {
  return defHttp.delete({
    url: `${Api.Delete}/${id}`,
  });
}

// 批量删除风险库
export function batchDeleteRiskLibrary(ids: string[]) {
  return defHttp.delete({
    url: Api.BatchDelete,
    data: ids,
  });
}

// 更新风险库状态
export function updateRiskLibraryStatus(id: string, status: string) {
  return defHttp.put({
    url: `${Api.UpdateStatus}/${id}?status=${status}`,
  });
}

// 批量更新状态
export function batchUpdateRiskLibraryStatus(ids: string[], status: string) {
  return defHttp.put({
    url: `${Api.BatchUpdateStatus}?status=${status}`,
    data: ids,
  });
}

// 发布风险库
export function publishRiskLibrary(id: string) {
  return defHttp.put({
    url: `${Api.Publish}/${id}`,
  });
}

// 归档风险库
export function archiveRiskLibrary(id: string) {
  return defHttp.put({
    url: `${Api.Archive}/${id}`,
  });
}

// 批量发布风险库
export function batchPublishRiskLibrary(ids: string[]) {
  return defHttp.put({
    url: Api.BatchPublish,
    data: ids,
  });
}

// 批量归档风险库
export function batchArchiveRiskLibrary(ids: string[]) {
  return defHttp.put({
    url: Api.BatchArchive,
    data: ids,
  });
}

// 复制风险库
export function copyRiskLibrary(id: string, newTitle: string) {
  return defHttp.post({
    url: `${Api.Copy}/${id}?newTitle=${encodeURIComponent(newTitle)}`,
  });
}

// 检查风险编码是否存在
export function checkRiskLibraryCodeExists(code: string, excludeId?: string) {
  return defHttp.get({
    url: Api.CheckCodeExists,
    params: { code, excludeId },
  });
}

// 检查风险标题是否存在
export function checkRiskLibraryTitleExists(title: string, excludeId?: string) {
  return defHttp.get({
    url: Api.CheckTitleExists,
    params: { title, excludeId },
  });
}

// 获取风险库选择列表
export function getRiskLibrarySelectList(keyword?: string) {
  return defHttp.get({
    url: Api.GetSelectList,
    params: { keyword },
  });
}

// 生成风险编码
export function generateRiskLibraryCode() {
  return defHttp.get({
    url: Api.GenerateCode,
  });
}

// 获取风险库使用情况
export function getRiskLibraryUsageInfo(id: string) {
  return defHttp.get({
    url: `${Api.GetUsageInfo}/${id}`,
  });
}
