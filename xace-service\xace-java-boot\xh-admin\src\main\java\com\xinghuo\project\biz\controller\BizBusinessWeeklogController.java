package com.xinghuo.project.biz.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.xinghuo.common.annotation.UserPermission;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.constant.MsgCode;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.permission.entity.OrganizeEntity;
import com.xinghuo.permission.entity.UserEntity;
import com.xinghuo.permission.service.OrganizeService;
import com.xinghuo.permission.service.UserService;
import com.xinghuo.project.biz.entity.BizBusinessWeeklogEntity;
import com.xinghuo.project.biz.entity.OpportunityEntity;
import com.xinghuo.project.biz.model.bizBusinessWeeklog.*;
import com.xinghuo.project.biz.service.BizBusinessWeeklogService;
import com.xinghuo.project.biz.service.OpportunityService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 商机周报管理控制器
 * 
 * <AUTHOR>
 * @date 2025-07-08
 */
@Slf4j
@Tag(name = "商机周报管理", description = "商机周报管理相关接口")
@RestController
@RequestMapping("/api/project/biz/businessWeeklog")
public class BizBusinessWeeklogController {

    @Resource
    private BizBusinessWeeklogService bizBusinessWeeklogService;

    @Resource
    private OpportunityService opportunityService;

    @Resource
    private UserService userService;

    @Resource
    private UserProvider userProvider;

    @Resource
    private OrganizeService organizeService;

    /**
     * 获取商机周报列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取商机周报列表")
    @SaCheckPermission("project.business.weeklog.view")
    @UserPermission
    public ActionResult<PageListVO<BizBusinessWeeklogVO>> list(@RequestBody BizBusinessWeeklogPagination pagination) {
        List<BizBusinessWeeklogEntity> list = bizBusinessWeeklogService.getList(pagination);
        List<BizBusinessWeeklogVO> listVOs = BeanCopierUtils.copyList(list, BizBusinessWeeklogVO.class);

        // 填充关联信息
        for (BizBusinessWeeklogVO vo : listVOs) {
            // 填充负责人名称
            if (StrXhUtil.isNotEmpty(vo.getOwnId())) {
                UserEntity user = userService.getInfo(vo.getOwnId());
                if (user != null) {
                    vo.setOwnName(user.getRealName());
                }
            }

            // 填充创建人名称
            if (StrXhUtil.isNotEmpty(vo.getCreatedBy())) {
                UserEntity user = userService.getInfo(vo.getCreatedBy());
                if (user != null) {
                    vo.setCreatedByName(user.getRealName());
                }
            }

            // 填充更新人名称
            if (StrXhUtil.isNotEmpty(vo.getLastUpdatedBy())) {
                UserEntity user = userService.getInfo(vo.getLastUpdatedBy());
                if (user != null) {
                    vo.setLastUpdatedByName(user.getRealName());
                }
            }

            // 设置状态名称
            vo.setStatusName(getStatusName(vo.getStatus()));
            vo.setShowStatusName(getShowStatusName(vo.getShowStatus()));

            // 填充商机信息（如果项目ID对应商机）
            if (StrXhUtil.isNotEmpty(vo.getProjId())) {
                OpportunityEntity opportunity = opportunityService.getById(vo.getProjId());
                if (opportunity != null) {
                    BizBusinessWeeklogVO.OpportunityInfo oppInfo = new BizBusinessWeeklogVO.OpportunityInfo();
                    oppInfo.setOpportunityId(opportunity.getId());
                    oppInfo.setOpportunityName(opportunity.getProjectName());
                    oppInfo.setOpportunityStatus(opportunity.getStatus());
//                    oppInfo.setOpportunityStage(opportunity.getStage());
//                    oppInfo.setExpectedAmount(opportunity());
                    vo.setOpportunityInfo(oppInfo);
                }
            }
        }

        PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
        return ActionResult.page(listVOs, page);
    }

    /**
     * 获取商机周报详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取商机周报详情")
    @Parameters({
            @Parameter(name = "id", description = "商机周报ID", required = true)
    })
    public ActionResult<BizBusinessWeeklogVO> getInfo(@PathVariable("id") String id) {
        BizBusinessWeeklogEntity entity = bizBusinessWeeklogService.getInfo(id);
        if (entity == null) {
            return ActionResult.fail("商机周报不存在");
        }

        BizBusinessWeeklogVO vo = BeanCopierUtils.copy(entity, BizBusinessWeeklogVO.class);

        // 填充关联信息（同上面的逻辑）
        if (StrXhUtil.isNotEmpty(vo.getOwnId())) {
            UserEntity user = userService.getInfo(vo.getOwnId());
            if (user != null) {
                vo.setOwnName(user.getRealName());
            }
        }

        vo.setStatusName(getStatusName(vo.getStatus()));
        vo.setShowStatusName(getShowStatusName(vo.getShowStatus()));

        return ActionResult.success(vo);
    }

    /**
     * 创建商机周报
     */
    @PostMapping
    @Operation(summary = "创建商机周报")
    @SaCheckPermission("project.business.weeklog.create")
    @UserPermission
    @Transactional(rollbackFor = Exception.class)
    public ActionResult<String> create(@RequestBody @Valid BizBusinessWeeklogForm form) {
        // 检查是否存在重复的商机周报
        if (bizBusinessWeeklogService.isExistByProjIdAndStartDate(form.getProjId(), form.getStartDate(), null)) {
            return ActionResult.fail("该项目在此时间段已存在商机周报");
        }

        BizBusinessWeeklogEntity entity = BeanCopierUtils.copy(form, BizBusinessWeeklogEntity.class);
        String id = bizBusinessWeeklogService.create(entity);
        return ActionResult.success(id, MsgCode.SU001.get());
    }

    /**
     * 更新商机周报
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新商机周报")
    @Parameters({
            @Parameter(name = "id", description = "商机周报ID", required = true)
    })
    @SaCheckPermission("project.business.weeklog.edit")
    @UserPermission
    @Transactional(rollbackFor = Exception.class)
    public ActionResult<String> update(@PathVariable("id") String id, @RequestBody @Valid BizBusinessWeeklogForm form) {
        // 检查是否存在重复的商机周报
        if (bizBusinessWeeklogService.isExistByProjIdAndStartDate(form.getProjId(), form.getStartDate(), id)) {
            return ActionResult.fail("该项目在此时间段已存在商机周报");
        }

        BizBusinessWeeklogEntity entity = BeanCopierUtils.copy(form, BizBusinessWeeklogEntity.class);
        bizBusinessWeeklogService.update(id, entity);
        return ActionResult.success(MsgCode.SU002.get());
    }

    /**
     * 删除商机周报
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除商机周报")
    @Parameters({
            @Parameter(name = "id", description = "商机周报ID", required = true)
    })
    @SaCheckPermission("project.business.weeklog.delete")
    @UserPermission
    @Transactional(rollbackFor = Exception.class)
    public ActionResult<String> delete(@PathVariable("id") String id) {
        bizBusinessWeeklogService.delete(id);
        return ActionResult.success(MsgCode.SU003.get());
    }

    /**
     * 根据时间段查询商机周报
     */
    @PostMapping("/getListByDateRange")
    @Operation(summary = "根据时间段查询商机周报")
    public ActionResult<List<BizBusinessWeeklogVO>> getListByDateRange(
            @RequestParam(required = false) Date startDate,
            @RequestParam(required = false) Date endDate) {
        List<BizBusinessWeeklogEntity> list = bizBusinessWeeklogService.getListByDateRange(startDate, endDate);
        List<BizBusinessWeeklogVO> listVOs = BeanCopierUtils.copyList(list, BizBusinessWeeklogVO.class);
        return ActionResult.success(listVOs);
    }

    /**
     * 根据负责人查询商机周报
     */
    @GetMapping("/getListByOwnId/{ownId}")
    @Operation(summary = "根据负责人查询商机周报")
    @Parameters({
            @Parameter(name = "ownId", description = "负责人ID", required = true)
    })
    public ActionResult<List<BizBusinessWeeklogVO>> getListByOwnId(@PathVariable("ownId") String ownId) {
        List<BizBusinessWeeklogEntity> list = bizBusinessWeeklogService.getListByOwnId(ownId);
        List<BizBusinessWeeklogVO> listVOs = BeanCopierUtils.copyList(list, BizBusinessWeeklogVO.class);
        return ActionResult.success(listVOs);
    }

    /**
     * 根据项目ID查询历史商机周报记录
     */
    @GetMapping("/getHistoryByProjId/{projId}")
    @Operation(summary = "根据项目ID查询历史商机周报记录")
    @Parameters({
            @Parameter(name = "projId", description = "项目ID", required = true)
    })
    public ActionResult<List<BizBusinessWeeklogHistoryVO>> getHistoryByProjId(@PathVariable("projId") String projId) {
        List<BizBusinessWeeklogHistoryVO> list = bizBusinessWeeklogService.getHistoryByProjId(projId);
        return ActionResult.success(list);
    }

    /**
     * 提交审核
     */
    @PostMapping("/{id}/submitForAudit")
    @Operation(summary = "提交审核")
    @Parameters({
            @Parameter(name = "id", description = "商机周报ID", required = true)
    })
    @SaCheckPermission("project.business.weeklog.submit")
    @UserPermission
    @Transactional(rollbackFor = Exception.class)
    public ActionResult<String> submitForAudit(@PathVariable("id") String id) {
        bizBusinessWeeklogService.submitForAudit(id);
        return ActionResult.success("提交审核成功");
    }

    /**
     * 审核商机周报
     * 只有分部经理或管理员可以进行审核
     */
    @PostMapping("/audit")
    @Operation(summary = "审核商机周报")
    @SaCheckPermission("project.business.weeklog.audit")
    @UserPermission
    @Transactional(rollbackFor = Exception.class)
    public ActionResult<String> audit(@RequestBody @Valid BizBusinessWeeklogAuditForm auditForm) {
        // 检查审核权限
        if (!hasAuditPermission(auditForm.getId())) {
            return ActionResult.fail("您没有权限审核此商机周报");
        }

        bizBusinessWeeklogService.audit(auditForm);
        return ActionResult.success("审核完成");
    }

    /**
     * 获取待审核的商机周报列表
     * 只显示当前用户有权限审核的商机周报
     */
    @PostMapping("/getPendingAuditList")
    @Operation(summary = "获取待审核的商机周报列表")
    @SaCheckPermission("project.business.weeklog.audit")
    @UserPermission
    public ActionResult<List<BizBusinessWeeklogVO>> getPendingAuditList() {
        List<BizBusinessWeeklogEntity> allPendingList = bizBusinessWeeklogService.getPendingAuditList();

        // 过滤出当前用户有权限审核的商机周报
        String currentUserId = userProvider.get().getUserId();
        boolean isAdmin = userProvider.get().getIsAdministrator();

        List<BizBusinessWeeklogEntity> filteredList;
        if (isAdmin) {
            // 管理员可以看到所有待审核的商机周报
            filteredList = allPendingList;
        } else {
            // 非管理员只能看到自己有权限审核的商机周报
            filteredList = allPendingList.stream()
                    .filter(entity -> isDepartmentManager(currentUserId, entity.getFbId()))
                    .collect(Collectors.toList());
        }

        List<BizBusinessWeeklogVO> listVOs = BeanCopierUtils.copyList(filteredList, BizBusinessWeeklogVO.class);

        // 填充关联信息
        for (BizBusinessWeeklogVO vo : listVOs) {
            if (StrXhUtil.isNotEmpty(vo.getOwnId())) {
                UserEntity user = userService.getInfo(vo.getOwnId());
                if (user != null) {
                    vo.setOwnName(user.getRealName());
                }
            }
            vo.setStatusName(getStatusName(vo.getStatus()));
        }

        return ActionResult.success(listVOs);
    }

    /**
     * 批量更新显示状态
     */
    @PostMapping("/batchUpdateShowStatus")
    @Operation(summary = "批量更新显示状态")
    @Transactional(rollbackFor = Exception.class)
    public ActionResult<String> batchUpdateShowStatus(
            @RequestBody List<String> ids,
            @RequestParam Integer showStatus) {
        bizBusinessWeeklogService.batchUpdateShowStatus(ids, showStatus);
        return ActionResult.success("批量更新成功");
    }

    /**
     * 获取状态名称
     */
    private String getStatusName(Integer status) {
        if (status == null) {
            return "未知";
        }
        
        switch (status) {
            case 0:
                return "未填写";
            case 1:
                return "已填写";
            case 2:
                return "提交审核";
            case 3:
                return "已发布";
            case -1:
                return "已驳回";
            default:
                return "未知";
        }
    }

    /**
     * 获取显示状态名称
     */
    private String getShowStatusName(Integer showStatus) {
        if (showStatus == null) {
            return "未知";
        }

        return showStatus == 1 ? "显示" : "未显示";
    }

    /**
     * 检查是否有审核权限
     * 只有分部经理或管理员可以审核
     */
    private boolean hasAuditPermission(String weeklogId) {
        // 获取当前用户信息
        String currentUserId = userProvider.get().getUserId();
        boolean isAdmin = userProvider.get().getIsAdministrator();

        // 管理员有所有权限
        if (isAdmin) {
            return true;
        }

        // 获取商机周报信息
        BizBusinessWeeklogEntity weeklog = bizBusinessWeeklogService.getInfo(weeklogId);
        if (weeklog == null) {
            return false;
        }

        // 检查是否是分部经理
        return isDepartmentManager(currentUserId, weeklog.getFbId());
    }

    /**
     * 检查用户是否是指定分部的经理
     */
    private boolean isDepartmentManager(String userId, String fbId) {
        if (StrXhUtil.isEmpty(fbId)) {
            return false;
        }

        try {
            // 获取分部信息
            OrganizeEntity organize = organizeService.getInfo(fbId);
            if (organize != null) {
                // 检查是否是直接负责人
                if (userId.equals(organize.getManager())) {
                    return true;
                }

                // 检查是否是上级分部的负责人（递归向上查找）
                if (StrXhUtil.isNotEmpty(organize.getParentId())) {
                    return isDepartmentManager(userId, organize.getParentId());
                }
            }
        } catch (Exception e) {
            log.error("检查分部经理权限失败，用户ID: {}, 分部ID: {}", userId, fbId, e);
        }

        return false;
    }
}
