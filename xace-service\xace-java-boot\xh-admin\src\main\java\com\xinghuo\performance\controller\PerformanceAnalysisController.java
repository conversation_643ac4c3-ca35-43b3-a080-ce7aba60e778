package com.xinghuo.performance.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.util.json.JsonXhUtil;
import com.xinghuo.performance.model.analysis.*;
import com.xinghuo.performance.service.PerformanceAnalysisService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 绩效报表分析控制器
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/performance/analysis")
@Tag(name = "绩效报表分析", description = "绩效报表分析相关接口")
public class PerformanceAnalysisController {

    @Resource
    private PerformanceAnalysisService performanceAnalysisService;

    /**
     * 获取绩效分析概览数据
     *
     * @param pagination 查询参数
     * @return 概览数据
     */
    @Operation(summary = "获取绩效分析概览数据")
    @GetMapping("/overview")
    public ActionResult<PerformanceAnalysisOverviewModel> getOverview(PerformanceAnalysisPagination pagination) {
        PerformanceAnalysisOverviewModel overview = performanceAnalysisService.getOverview(pagination);
        return ActionResult.success(overview);
    }

    /**
     * 获取绩效分析图表数据
     *
     * @param pagination 查询参数
     * @return 图表数据
     */
    @Operation(summary = "获取绩效分析图表数据")
    @GetMapping("/charts")
    public ActionResult<PerformanceAnalysisChartModel> getCharts(PerformanceAnalysisPagination pagination) {
        PerformanceAnalysisChartModel charts = performanceAnalysisService.getCharts(pagination);
        return ActionResult.success(charts);
    }

    /**
     * 获取个人绩效分析列表
     *
     * @param pagination 分页查询参数
     * @return 个人绩效分析列表
     */
    @Operation(summary = "获取个人绩效分析列表")
    @PostMapping("/personal/getList")
    public ActionResult<PageListVO<PersonalPerformanceAnalysisVO>> getPersonalAnalysisList(@RequestBody PerformanceAnalysisPagination pagination) {
        List<PersonalPerformanceAnalysisVO> list = performanceAnalysisService.getPersonalAnalysisList(pagination);
        PaginationVO page = JsonXhUtil.jsonDeepCopy(pagination, PaginationVO.class);
        return ActionResult.page(list, page);
    }

    /**
     * 获取分部绩效分析列表
     *
     * @param pagination 分页查询参数
     * @return 分部绩效分析列表
     */
    @Operation(summary = "获取分部绩效分析列表")
    @PostMapping("/department/getList")
    public ActionResult<PageListVO<DepartmentPerformanceAnalysisVO>> getDepartmentAnalysisList(@RequestBody PerformanceAnalysisPagination pagination) {
        List<DepartmentPerformanceAnalysisVO> list = performanceAnalysisService.getDepartmentAnalysisList(pagination);
        PaginationVO page = JsonXhUtil.jsonDeepCopy(pagination, PaginationVO.class);
        return ActionResult.page(list, page);
    }

    /**
     * 获取绩效排名列表
     *
     * @param pagination 分页查询参数
     * @return 绩效排名列表
     */
    @Operation(summary = "获取绩效排名列表")
    @PostMapping("/ranking/getList")
    public ActionResult<PageListVO<PerformanceRankingVO>> getRankingList(@RequestBody PerformanceAnalysisPagination pagination) {
        List<PerformanceRankingVO> list = performanceAnalysisService.getRankingList(pagination);
        PaginationVO page = JsonXhUtil.jsonDeepCopy(pagination, PaginationVO.class);
        return ActionResult.page(list, page);
    }

    /**
     * 获取个人绩效详情
     *
     * @param userId 用户ID
     * @param pagination 查询参数
     * @return 个人绩效详情
     */
    @Operation(summary = "获取个人绩效详情")
    @GetMapping("/personal/{userId}/detail")
    public ActionResult<PersonalPerformanceDetailVO> getPersonalDetail(@PathVariable String userId, PerformanceAnalysisPagination pagination) {
        PersonalPerformanceDetailVO detail = performanceAnalysisService.getPersonalDetail(userId, pagination);
        return ActionResult.success(detail);
    }

    /**
     * 获取分部绩效详情
     *
     * @param fbId 分部ID
     * @param pagination 查询参数
     * @return 分部绩效详情
     */
    @Operation(summary = "获取分部绩效详情")
    @GetMapping("/department/{fbId}/detail")
    public ActionResult<DepartmentPerformanceDetailVO> getDepartmentDetail(@PathVariable String fbId, PerformanceAnalysisPagination pagination) {
        DepartmentPerformanceDetailVO detail = performanceAnalysisService.getDepartmentDetail(fbId, pagination);
        return ActionResult.success(detail);
    }

    /**
     * 获取绩效维度统计
     *
     * @param pagination 查询参数
     * @return 维度统计数据
     */
    @Operation(summary = "获取绩效维度统计")
    @GetMapping("/dimension/stats")
    public ActionResult<List<PerformanceDimensionStatsVO>> getDimensionStats(PerformanceAnalysisPagination pagination) {
        List<PerformanceDimensionStatsVO> stats = performanceAnalysisService.getDimensionStats(pagination);
        return ActionResult.success(stats);
    }

    /**
     * 获取绩效趋势数据
     *
     * @param pagination 查询参数
     * @return 趋势数据
     */
    @Operation(summary = "获取绩效趋势数据")
    @GetMapping("/trend")
    public ActionResult<List<PerformanceTrendVO>> getTrend(PerformanceAnalysisPagination pagination) {
        List<PerformanceTrendVO> trend = performanceAnalysisService.getTrend(pagination);
        return ActionResult.success(trend);
    }

    /**
     * 导出绩效分析报表
     *
     * @param pagination 查询参数
     * @param response HTTP响应
     */
    @Operation(summary = "导出绩效分析报表")
    @PostMapping("/export")
    public void exportAnalysis(@RequestBody PerformanceAnalysisPagination pagination, HttpServletResponse response) {
        performanceAnalysisService.exportAnalysis(pagination, response);
    }
}
