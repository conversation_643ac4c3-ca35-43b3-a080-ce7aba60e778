package com.xinghuo.project.biz.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.project.biz.model.report.DashboardDataVO;
import com.xinghuo.project.biz.model.report.PaymentTrendVO;
import com.xinghuo.project.biz.model.report.ContractStatusVO;
import com.xinghuo.project.biz.model.report.DepartmentRankVO;
import com.xinghuo.project.biz.model.report.ConversionFunnelVO;
import com.xinghuo.project.biz.service.BizContractReportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 合同报表控制器
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/project/biz/report")
@Tag(name = "合同报表", description = "合同报表相关接口")
public class BizContractReportController {

    @Autowired
    private BizContractReportService bizContractReportService;

    /**
     * 获取仪表板概览数据
     */
    @GetMapping("/dashboard/data")
    @Operation(summary = "获取仪表板概览数据")
    public ActionResult<DashboardDataVO> getDashboardData(@RequestParam(required = false) Map<String, Object> params) {
        try {
            log.info("📊 [合同报表] 获取仪表板概览数据，参数: {}", params);
            DashboardDataVO data = bizContractReportService.getDashboardData(params);
            log.info("✅ [合同报表] 仪表板数据获取成功: {}", data);
            return ActionResult.success(data);
        } catch (Exception e) {
            log.error("❌ [合同报表] 获取仪表板数据失败", e);
            return ActionResult.fail("获取仪表板数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取收款趋势数据
     */
    @GetMapping("/dashboard/payment-trend")
    @Operation(summary = "获取收款趋势数据")
    public ActionResult<PaymentTrendVO> getPaymentTrend(@RequestParam(required = false) Map<String, Object> params) {
        try {
            log.info("📈 [合同报表] 获取收款趋势数据，参数: {}", params);
            PaymentTrendVO data = bizContractReportService.getPaymentTrend(params);
            return ActionResult.success(data);
        } catch (Exception e) {
            log.error("❌ [合同报表] 获取收款趋势数据失败", e);
            return ActionResult.fail("获取收款趋势数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取合同状态分布数据
     */
    @GetMapping("/dashboard/contract-status")
    @Operation(summary = "获取合同状态分布数据")
    public ActionResult<List<ContractStatusVO>> getContractStatus(@RequestParam(required = false) Map<String, Object> params) {
        try {
            log.info("📊 [合同报表] 获取合同状态分布数据，参数: {}", params);
            List<ContractStatusVO> data = bizContractReportService.getContractStatus(params);
            return ActionResult.success(data);
        } catch (Exception e) {
            log.error("❌ [合同报表] 获取合同状态分布数据失败", e);
            return ActionResult.fail("获取合同状态分布数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取部门收款排行数据
     */
    @GetMapping("/dashboard/department-rank")
    @Operation(summary = "获取部门收款排行数据")
    public ActionResult<DepartmentRankVO> getDepartmentRank(@RequestParam(required = false) Map<String, Object> params) {
        try {
            log.info("🏆 [合同报表] 获取部门收款排行数据，参数: {}", params);
            DepartmentRankVO data = bizContractReportService.getDepartmentRank(params);
            return ActionResult.success(data);
        } catch (Exception e) {
            log.error("❌ [合同报表] 获取部门收款排行数据失败", e);
            return ActionResult.fail("获取部门收款排行数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取商机转化漏斗数据
     */
    @GetMapping("/dashboard/conversion-funnel")
    @Operation(summary = "获取商机转化漏斗数据")
    public ActionResult<List<ConversionFunnelVO>> getConversionFunnel(@RequestParam(required = false) Map<String, Object> params) {
        try {
            log.info("🔄 [合同报表] 获取商机转化漏斗数据，参数: {}", params);
            List<ConversionFunnelVO> data = bizContractReportService.getConversionFunnel(params);
            return ActionResult.success(data);
        } catch (Exception e) {
            log.error("❌ [合同报表] 获取商机转化漏斗数据失败", e);
            return ActionResult.fail("获取商机转化漏斗数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取合同分析数据
     */
    @GetMapping("/contract/analysis")
    @Operation(summary = "获取合同分析数据")
    public ActionResult<Map<String, Object>> getContractAnalysis(
            @RequestParam(required = false) String projectId,
            @RequestParam(required = false) String contractId) {
        try {
            log.info("📋 [合同报表] 获取合同分析数据，项目ID: {}, 合同ID: {}", projectId, contractId);
            Map<String, Object> data = bizContractReportService.getContractAnalysis(projectId, contractId, null);
            return ActionResult.success(data);
        } catch (Exception e) {
            log.error("❌ [合同报表] 获取合同分析数据失败", e);
            return ActionResult.fail("获取合同分析数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取合同月度趋势数据
     */
    @GetMapping("/contract/monthly-trend")
    @Operation(summary = "获取合同月度趋势数据")
    public ActionResult<Map<String, Object>> getContractMonthlyTrend(@RequestParam String contractId) {
        try {
            log.info("📈 [合同报表] 获取合同月度趋势数据，合同ID: {}", contractId);
            Map<String, Object> data = bizContractReportService.getContractMonthlyTrend(contractId);
            return ActionResult.success(data);
        } catch (Exception e) {
            log.error("❌ [合同报表] 获取合同月度趋势数据失败", e);
            return ActionResult.fail("获取合同月度趋势数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取合同执行状态数据
     */
    @GetMapping("/contract/execution-status")
    @Operation(summary = "获取合同执行状态数据")
    public ActionResult<List<Map<String, Object>>> getContractExecutionStatus(@RequestParam String contractId) {
        try {
            log.info("📊 [合同报表] 获取合同执行状态数据，合同ID: {}", contractId);
            List<Map<String, Object>> data = bizContractReportService.getContractExecutionStatus(contractId);
            return ActionResult.success(data);
        } catch (Exception e) {
            log.error("❌ [合同报表] 获取合同执行状态数据失败", e);
            return ActionResult.fail("获取合同执行状态数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取合同风险提醒数据
     */
    @GetMapping("/contract/risk-alerts")
    @Operation(summary = "获取合同风险提醒数据")
    public ActionResult<List<Map<String, Object>>> getContractRiskAlerts(@RequestParam String contractId) {
        try {
            log.info("⚠️ [合同报表] 获取合同风险提醒数据，合同ID: {}", contractId);
            List<Map<String, Object>> data = bizContractReportService.getContractRiskAlerts(contractId);
            return ActionResult.success(data);
        } catch (Exception e) {
            log.error("❌ [合同报表] 获取合同风险提醒数据失败", e);
            return ActionResult.fail("获取合同风险提醒数据失败: " + e.getMessage());
        }
    }
}
