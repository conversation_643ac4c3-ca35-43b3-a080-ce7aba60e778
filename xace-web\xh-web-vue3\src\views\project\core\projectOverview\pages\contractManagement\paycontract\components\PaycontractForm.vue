<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" show-footer :title="getTitle" :width="900" @ok="handleSubmit">
    <div class="form-container">
      <!-- 基础信息 -->
      <div class="form-section">
        <div class="section-title">
          <i class="icon-ym icon-ym-info-circle"></i>
          <span>基础信息</span>
        </div>
        <BasicForm @register="registerBasicForm" :schemas="basicFormSchemas" :label-width="100" :show-action-button-group="false" />
      </div>

      <!-- 金额信息 -->
      <div class="form-section">
        <div class="section-title">
          <i class="icon-ym icon-ym-money"></i>
          <span>金额信息</span>
        </div>
        <BasicForm @register="registerAmountForm" :schemas="amountFormSchemas" :label-width="100" :show-action-button-group="false" />
      </div>

      <!-- 日期信息 -->
      <div class="form-section">
        <div class="section-title">
          <i class="icon-ym icon-ym-calendar"></i>
          <span>日期信息</span>
        </div>
        <BasicForm @register="registerDateForm" :schemas="dateFormSchemas" :label-width="100" :show-action-button-group="false" />
      </div>

      <!-- 备注信息 -->
      <div class="form-section">
        <div class="section-title">
          <i class="icon-ym icon-ym-edit"></i>
          <span>备注信息</span>
        </div>
        <BasicForm @register="registerNoteForm" :schemas="noteFormSchemas" :label-width="100" :show-action-button-group="false" />
      </div>
    </div>
  </BasicDrawer>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed, onMounted, unref } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { formatToDate } from '/@/utils/dateUtil';
  import paymentContractApi, { type PaymentContractVO, type PaymentContractForm } from '/@/api/project/paymentContract';
  import { getSupplierSelector } from '/@/api/project/supplier';
  import { getUserSelector } from '/@/api/permission/user';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { BasicForm, useForm, FormSchema } from '/@/components/Form';

  defineOptions({ name: 'PaycontractFormDrawer' });

  // Emits
  const emit = defineEmits(['success', 'register']);

  const { createMessage } = useMessage();

  // 状态管理
  const isUpdate = ref(true);
  const rowId = ref('');
  const contractId = ref('');
  const contractInfo = ref<any>(null);

  // 选项数据
  const supplierOptions = ref<Array<{ id: string; fullName: string }>>([]);
  const supplierLoading = ref(false);
  const userOptions = ref<Array<{ id: string; fullName: string }>>([]);
  const userLoading = ref(false);

  // 状态选项
  const statusOptions = [
    { id: 'draft', fullName: '草稿' },
    { id: 'signed', fullName: '已签订' },
    { id: 'executing', fullName: '执行中' },
    { id: 'completed', fullName: '已完成' },
    { id: 'cancelled', fullName: '已取消' },
    { id: 'suspended', fullName: '已暂停' },
  ];

  // 表单注册
  const [registerBasicForm, { setFieldsValue: setBasicFieldsValue, validate: validateBasic }] = useForm();
  const [registerAmountForm, { setFieldsValue: setAmountFieldsValue, validate: validateAmount }] = useForm();
  const [registerDateForm, { setFieldsValue: setDateFieldsValue, validate: validateDate }] = useForm();
  const [registerNoteForm, { setFieldsValue: setNoteFieldsValue, validate: validateNote }] = useForm();

  // 先定义处理函数
  const handleSupplierSearch = async (value: string) => {
    if (!value) {
      await loadSupplierOptions();
      return;
    }
    await loadSupplierOptions(value);
  };

  const handleSupplierDropdown = async (open: boolean) => {
    if (open && supplierOptions.value.length === 0) {
      await loadSupplierOptions();
    }
  };

  const handleUserSearch = async (value: string) => {
    if (!value) {
      await loadUserOptions();
      return;
    }
    // 在已有选项中过滤
    const keyword = value.toLowerCase();
    const allUsers = [...userOptions.value];
    userOptions.value = allUsers.filter(item => item.fullName.toLowerCase().includes(keyword));
  };

  const handleUserDropdown = async (open: boolean) => {
    if (open && userOptions.value.length === 0) {
      await loadUserOptions();
    }
  };

  // 基础信息表单配置
  const basicFormSchemas = computed<FormSchema[]>(() => [
    {
      field: 'contractInfoDisplay',
      label: '关联收款合同',
      component: 'Input',
      colProps: { span: 24 },
      componentProps: {
        value: contractInfo.value ? `📋 ${contractInfo.value.name} (${contractInfo.value.cno})` : '⚠️ 未关联收款合同',
        readonly: true,
        style: {
          backgroundColor: '#f5f5f5',
          color: contractInfo.value ? '#1890ff' : '#ff4d4f',
          fontWeight: '500',
          border: contractInfo.value ? '1px solid #d9f7be' : '1px solid #ffccc7',
        },
      },
    },
    {
      field: 'name',
      label: '合同名称',
      component: 'Input',
      required: true,
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请输入合同名称',
        maxlength: 500,
        showCount: true,
      },
    },
    {
      field: 'cno',
      label: '合同编号',
      component: 'Input',
      required: true,
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请输入合同编号',
        maxlength: 50,
        showCount: true,
      },
    },
    {
      field: 'suppilerId',
      label: '供应商',
      component: 'Select',
      required: true,
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请选择供应商',
        options: supplierOptions.value,
        fieldNames: { label: 'fullName', value: 'id' },
        showSearch: true,
        filterOption: false,
        notFoundContent: supplierLoading.value ? '加载中...' : '暂无数据',
        onSearch: handleSupplierSearch,
        onDropdownVisibleChange: handleSupplierDropdown,
      },
    },
    {
      field: 'ownId',
      label: '负责人',
      component: 'Select',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请选择负责人',
        options: userOptions.value,
        fieldNames: { label: 'fullName', value: 'id' },
        showSearch: true,
        filterOption: false,
        notFoundContent: userLoading.value ? '加载中...' : '暂无数据',
        onSearch: handleUserSearch,
        onDropdownVisibleChange: handleUserDropdown,
      },
    },
    {
      field: 'status',
      label: '合同状态',
      component: 'Select',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请选择合同状态',
        options: statusOptions,
      },
      defaultValue: 'draft',
    },
  ]);

  // 金额信息表单配置
  const amountFormSchemas: FormSchema[] = [
    {
      field: 'amount',
      label: '合同金额',
      component: 'InputNumber',
      required: true,
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请输入合同金额',
        min: 0,
        max: 999999999999,
        precision: 2,
        style: { width: '100%' },
      },
    },
    {
      field: 'kfybAmount',
      label: '开发一部金额',
      component: 'InputNumber',
      colProps: { span: 8 },
      componentProps: {
        placeholder: '开发一部金额',
        min: 0,
        precision: 2,
        style: { width: '100%' },
      },
      defaultValue: 0,
    },
    {
      field: 'kfebAmount',
      label: '开发二部金额',
      component: 'InputNumber',
      colProps: { span: 8 },
      componentProps: {
        placeholder: '开发二部金额',
        min: 0,
        precision: 2,
        style: { width: '100%' },
      },
      defaultValue: 0,
    },
    {
      field: 'otherAmount',
      label: '综合金额',
      component: 'InputNumber',
      colProps: { span: 8 },
      componentProps: {
        placeholder: '综合金额',
        min: 0,
        precision: 2,
        style: { width: '100%' },
      },
      defaultValue: 0,
    },
  ];

  // 日期信息表单配置
  const dateFormSchemas: FormSchema[] = [
    {
      field: 'signDate',
      label: '签订日期',
      component: 'DatePicker',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请选择签订日期',
        style: { width: '100%' },
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
    },
    {
      field: 'estSignDate',
      label: '预计签订日期',
      component: 'DatePicker',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请选择预计签订日期',
        style: { width: '100%' },
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
    },
  ];

  // 备注信息表单配置
  const noteFormSchemas: FormSchema[] = [
    {
      field: 'note',
      label: '备注',
      component: 'InputTextArea',
      colProps: { span: 24 },
      componentProps: {
        placeholder: '请输入备注信息',
        maxlength: 500,
        rows: 3,
        showCount: true,
      },
    },
  ];

  const getTitle = computed(() => (!unref(isUpdate) ? '新增付款合同' : '编辑付款合同'));

  // 初始化表单数据
  const initFormData = async (record?: PaymentContractVO) => {
    if (unref(isUpdate) && record) {
      // 编辑模式，设置表单数据
      const basicData = {
        name: record.name || '',
        cno: record.cno || '',
        suppilerId: record.suppilerId || '',
        ownId: record.ownId || '',
        status: record.status || 'draft',
      };

      const amountData = {
        amount: record.amount || undefined,
        kfybAmount: record.kfybAmount || 0,
        kfebAmount: record.kfebAmount || 0,
        otherAmount: record.otherAmount || 0,
      };

      const dateData = {
        signDate: record.signDate || '',
        estSignDate: record.estSignDate || '',
      };

      const noteData = {
        note: record.note || '',
      };

      await setBasicFieldsValue(basicData);
      await setAmountFieldsValue(amountData);
      await setDateFieldsValue(dateData);
      await setNoteFieldsValue(noteData);
    } else {
      // 新增模式，重置表单
      const basicData = {
        name: '',
        cno: '',
        suppilerId: '',
        ownId: '',
        status: 'draft',
      };

      const amountData = {
        amount: undefined,
        kfybAmount: 0,
        kfebAmount: 0,
        otherAmount: 0,
      };

      const dateData = {
        signDate: '',
        estSignDate: '',
      };

      const noteData = {
        note: '',
      };

      await setBasicFieldsValue(basicData);
      await setAmountFieldsValue(amountData);
      await setDateFieldsValue(dateData);
      await setNoteFieldsValue(noteData);
    }
  };

  // 加载供应商选项
  const loadSupplierOptions = async (keyword?: string) => {
    try {
      supplierLoading.value = true;
      const params = keyword ? { keyword } : {};
      const response = await getSupplierSelector(params);
      if (response && Array.isArray(response)) {
        supplierOptions.value = response;
      } else if (response && response.data) {
        supplierOptions.value = response.data || [];
      }
    } catch (error) {
      console.error('加载供应商选项失败:', error);
    } finally {
      supplierLoading.value = false;
    }
  };

  // 加载用户选项
  const loadUserOptions = async (keyword?: string) => {
    try {
      userLoading.value = true;
      const response = await getUserSelector();
      if (response) {
        // 处理不同的响应格式
        let users = [];
        if (Array.isArray(response)) {
          users = response;
        } else if (response.data && Array.isArray(response.data)) {
          users = response.data;
        } else if (response.data && Array.isArray(response.data.list)) {
          users = response.data.list;
        }

        // 过滤用户数据并格式化
        userOptions.value = users
          .filter((item: any) => item.type === 'user' || !item.type) // 显示用户类型或没有类型字段的项
          .map((item: any) => ({
            id: item.id,
            fullName: item.fullName || item.name || item.userName,
          }));
      }
    } catch (error) {
      console.error('加载用户选项失败:', error);
      userOptions.value = [];
    } finally {
      userLoading.value = false;
    }
  };

  // 加载收款合同信息
  const loadContractInfo = async (contractData?: any) => {
    if (!contractId.value) {
      contractInfo.value = null;
      return;
    }

    try {
      // 从传递的数据中获取收款合同信息
      if (contractData?.contractInfo) {
        contractInfo.value = contractData.contractInfo;
      } else {
        // 如果没有传递合同信息，设置一个默认的显示
        contractInfo.value = {
          name: '收款合同',
          cno: contractId.value,
        };
      }
    } catch (error) {
      console.error('加载收款合同信息失败:', error);
      contractInfo.value = null;
    }
  };

  // 检查合同编号唯一性
  const checkCnoUniqueness = async (cno: string) => {
    try {
      const response = await paymentContractApi.checkCnoExists(cno, rowId.value);
      if (response.code === 200) {
        return !response.data; // 返回true表示不存在，可以使用
      }
      return false;
    } catch (error) {
      console.error('检查合同编号失败:', error);
      return true; // 检查失败时允许提交，避免阻塞用户操作
    }
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      // 验证所有表单
      const basicData = await validateBasic();
      const amountData = await validateAmount();
      const dateData = await validateDate();
      const noteData = await validateNote();

      // 合并表单数据
      const submitData: PaymentContractForm = {
        ...basicData,
        ...amountData,
        ...dateData,
        ...noteData,
        cid: contractId.value, // 设置关联的收款合同ID
      };

      // 检查合同编号唯一性
      const isUniqueCno = await checkCnoUniqueness(submitData.cno);
      if (!isUniqueCno) {
        createMessage.error('合同编号已存在，请使用其他编号');
        return;
      }

      setDrawerProps({ confirmLoading: true });

      let response;
      if (unref(isUpdate)) {
        submitData.id = rowId.value;
        response = await paymentContractApi.update(submitData.id!, submitData);
      } else {
        response = await paymentContractApi.create(submitData);
      }

      if (response.code === 200) {
        createMessage.success(unref(isUpdate) ? '编辑成功' : '新增成功');
        closeDrawer();
        emit('success');
      } else {
        createMessage.error(response.msg || '操作失败');
      }
    } catch (error) {
      console.error('提交表单失败:', error);
      createMessage.error('操作失败');
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  };

  // 使用框架抽屉组件
  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async data => {
    setDrawerProps({ confirmLoading: false });

    isUpdate.value = !!data?.isUpdate;
    contractId.value = data?.contractId || '';
    contractInfo.value = data?.contractInfo || null;
    rowId.value = data?.record?.id || '';

    // 加载选项数据
    await loadSupplierOptions();
    await loadUserOptions();
    await loadContractInfo(data);

    // 初始化表单数据
    await initFormData(data?.record);
  });

  onMounted(() => {
    loadSupplierOptions();
    loadUserOptions();
  });
</script>

<style lang="less" scoped>
  .form-container {
    padding: 0 8px;
  }

  .form-section {
    margin-bottom: 32px;

    &:last-child {
      margin-bottom: 0;
    }

    .section-title {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #f0f0f0;
      font-size: 14px;
      font-weight: 500;
      color: #262626;

      i {
        margin-right: 8px;
        font-size: 16px;
        color: #1890ff;
      }
    }

    :deep(.ant-form) {
      .ant-form-item {
        margin-bottom: 16px;
      }

      .ant-form-item-label {
        font-weight: 500;
      }

      .ant-input,
      .ant-input-number,
      .ant-select-selector,
      .ant-picker {
        border-radius: 6px;
      }

      .ant-input-number {
        width: 100%;
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .form-container {
      padding: 0 4px;
    }

    .form-section {
      margin-bottom: 24px;

      .section-title {
        font-size: 13px;
        margin-bottom: 12px;
      }
    }
  }
</style>
