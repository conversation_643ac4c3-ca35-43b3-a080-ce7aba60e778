<template>
  <div class="xh-common-layout">
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="handleCreate"> 新建 </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="getTableActions(record)" />
        </template>
      </template>
    </BasicTable>
  </div>
</template>

<script lang="ts" setup>
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { getTableSchemas } from './tableSchema';
  import { useMessage } from '/@/hooks/web/useMessage';

  defineOptions({ name: 'ProgramProjectList' });

  const { createMessage } = useMessage();

  const [registerTable, { reload }] = useTable({
    api: async () => {
      return {
        list: [
          {
            id: '152068',
            projectName: '智能制造系统实施与优化项目',
            projectCode: '2025000026',
            projectManager: '高峰',
            status: '进行中',
            progress: 30,
            department: '易趋集团/投资管理事业部/智能制造事业部',
            category: '数字化-IT项目',
            planStartDate: '2024-12-31',
            actualStartDate: '2025-03-06',
            planEndDate: '2026-02-26',
            actualEndDate: '',
            planDuration: 141,
            sponsor: '陈涛民',
            programGroup: 'Demo-智能制造数字化转型项目群',
            client: '',
            currentPhase: '需求分析',
            budget: 2800000,
            actualCost: 840000,
            riskLevel: '中',
            healthStatus: '延期',
          },
          {
            id: '150163',
            projectName: 'Demo-组织结构优化与流程再造项目',
            projectCode: '**********',
            projectManager: '陈静,Demo演示用户,PM',
            status: '进行中',
            progress: 40,
            department: '易趋集团/战略决策委员会',
            category: '数字化-变革项目',
            planStartDate: '2025-03-04',
            actualStartDate: '2025-03-05',
            planEndDate: '2026-02-26',
            actualEndDate: '',
            planDuration: 69,
            sponsor: '曹静',
            programGroup: 'Demo-智能制造数字化转型项目群',
            client: '',
            currentPhase: '解决方案设计与规划',
            budget: 1500000,
            actualCost: 600000,
            riskLevel: '低',
            healthStatus: '健康',
          },
          {
            id: '151862',
            projectName: 'Demo-ERP系统实施与升级项目',
            projectCode: '**********',
            projectManager: '陈涛,Demo演示用户,PM',
            status: '进行中',
            progress: 46,
            department: '易趋集团/合规与审计中心',
            category: '数字化-IT项目',
            planStartDate: '2024-01-15',
            actualStartDate: '2025-03-06',
            planEndDate: '2026-03-27',
            actualEndDate: '',
            planDuration: 393,
            sponsor: '陈涛民,PM,DM,PMO,TM',
            programGroup: 'Demo-智能制造数字化转型项目群',
            client: '',
            currentPhase: '需求分析',
            budget: 5200000,
            actualCost: 2392000,
            riskLevel: '高',
            healthStatus: '风险',
          },
          {
            id: '150470',
            projectName: 'ERP系统优化项目',
            projectCode: '**********',
            projectManager: '陈静,王子豪',
            status: '进行中',
            progress: 44,
            department: '易趋集团/软件科技事业部',
            category: '数字化-变革项目',
            planStartDate: '2025-02-04',
            actualStartDate: '2025-03-05',
            planEndDate: '2025-10-21',
            actualEndDate: '',
            planDuration: 69,
            sponsor: '曹静',
            programGroup: 'Demo-智能制造数字化转型项目群',
            client: '',
            currentPhase: '解决方案设计与规划',
            budget: 1800000,
            actualCost: 792000,
            riskLevel: '中',
            healthStatus: '健康',
          },
          {
            id: '151649',
            projectName: '人力资源管理系统（HRMS）咨询项目',
            projectCode: '**********',
            projectManager: '王子豪,曹静',
            status: '进行中',
            progress: 7,
            department: '易趋集团/投资管理事业部/数字化创新研究院',
            category: '数字化-咨询项目',
            planStartDate: '2025-03-05',
            actualStartDate: '2025-03-06',
            planEndDate: '2025-12-31',
            actualEndDate: '',
            planDuration: 89,
            sponsor: '陈静龙',
            programGroup: 'Demo-智能制造数字化转型项目群',
            client: '',
            currentPhase: '需求调研阶段',
            budget: 980000,
            actualCost: 68600,
            riskLevel: '低',
            healthStatus: '健康',
          },
          {
            id: '153001',
            projectName: '数字化办公平台建设项目',
            projectCode: '**********',
            projectManager: '李明,张华',
            status: '规划中',
            progress: 10,
            department: '易趋集团/信息技术部',
            category: '数字化-IT项目',
            planStartDate: '2025-08-01',
            actualStartDate: '',
            planEndDate: '2026-06-30',
            actualEndDate: '',
            planDuration: 210,
            sponsor: '王总',
            programGroup: 'Demo-智能制造数字化转型项目群',
            client: '内部用户',
            currentPhase: '项目启动',
            budget: 3200000,
            actualCost: 320000,
            riskLevel: '低',
            healthStatus: '健康',
          },
          {
            id: '153002',
            projectName: '客户关系管理系统升级项目',
            projectCode: '**********',
            projectManager: '刘强,赵敏',
            status: '已完成',
            progress: 100,
            department: '易趋集团/销售事业部',
            category: '数字化-IT项目',
            planStartDate: '2024-05-01',
            actualStartDate: '2024-05-01',
            planEndDate: '2025-02-28',
            actualEndDate: '2025-02-20',
            planDuration: 180,
            sponsor: '销售总监',
            programGroup: 'Demo-智能制造数字化转型项目群',
            client: '销售团队',
            currentPhase: '项目收尾',
            budget: 1600000,
            actualCost: 1520000,
            riskLevel: '低',
            healthStatus: '健康',
          },
          {
            id: '153003',
            projectName: '供应链管理优化项目',
            projectCode: '**********',
            projectManager: '孙伟,钱丽',
            status: '暂停',
            progress: 25,
            department: '易趋集团/采购供应链部',
            category: '数字化-变革项目',
            planStartDate: '2024-10-01',
            actualStartDate: '2024-10-15',
            planEndDate: '2025-12-31',
            actualEndDate: '',
            planDuration: 320,
            sponsor: '供应链总监',
            programGroup: 'Demo-智能制造数字化转型项目群',
            client: '供应商',
            currentPhase: '需求分析',
            budget: 2200000,
            actualCost: 550000,
            riskLevel: '高',
            healthStatus: '风险',
          },
        ],
        pagination: {
          total: 8,
        },
      };
    },
    columns: getTableSchemas(),
    useSearchForm: true,
    formConfig: {
      labelWidth: 100,
      schemas: [
        {
          field: 'projectName',
          label: '项目名称',
          component: 'Input',
          colProps: { span: 6 },
        },
        {
          field: 'projectCode',
          label: '项目编号',
          component: 'Input',
          colProps: { span: 6 },
        },
        {
          field: 'status',
          label: '项目状态',
          component: 'Select',
          colProps: { span: 6 },
          componentProps: {
            options: [
              { label: '进行中', value: '进行中' },
              { label: '已完成', value: '已完成' },
              { label: '规划中', value: '规划中' },
              { label: '暂停', value: '暂停' },
            ],
          },
        },
        {
          field: 'category',
          label: '项目类别',
          component: 'Select',
          colProps: { span: 6 },
          componentProps: {
            options: [
              { label: '数字化-IT项目', value: '数字化-IT项目' },
              { label: '数字化-变革项目', value: '数字化-变革项目' },
              { label: '数字化-咨询项目', value: '数字化-咨询项目' },
            ],
          },
        },
      ],
    },
    actionColumn: {
      width: 150,
      title: '操作',
      dataIndex: 'action',
    },
  });

  function getTableActions(record: any) {
    return [
      {
        label: '编辑',
        onClick: () => handleEdit(record),
      },
      {
        label: '详情',
        onClick: () => handleDetail(record),
      },
      {
        label: '删除',
        color: 'error' as const,
        popConfirm: {
          title: '确定要删除吗？',
          confirm: () => handleDelete(record),
        },
      },
    ];
  }

  function handleCreate() {
    createMessage.info('新建项目');
  }

  function handleEdit(record: any) {
    createMessage.info(`编辑项目: ${record.projectName}`);
  }

  function handleDetail(record: any) {
    createMessage.info(`查看详情: ${record.projectName}`);
  }

  async function handleDelete(record: any) {
    createMessage.success(`删除成功: ${record.projectName}`);
    reload();
  }
</script>

<style scoped>
  .xh-common-layout {
    padding: 16px;
  }
</style>
