<template>
  <a-modal v-model:visible="modalVisible" title="更新合同状态" :confirm-loading="loading" :mask-closable="false" @ok="handleOk" @cancel="handleCancel">
    <div class="status-update-modal">
      <a-form ref="formRef" :model="formData" :rules="formRules" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <a-form-item label="合同名称">
          <span class="font-medium">{{ contractData?.name || '-' }}</span>
        </a-form-item>

        <a-form-item label="合同编号">
          <a-tag color="blue">{{ contractData?.cno || '-' }}</a-tag>
        </a-form-item>

        <a-form-item label="当前状态">
          <a-tag :color="getStatusColor(contractData?.contractStatus)">
            {{ getStatusText(contractData?.contractStatus) }}
          </a-tag>
        </a-form-item>

        <a-form-item label="新状态" name="contractStatus" required>
          <a-select v-model:value="formData.contractStatus" placeholder="请选择新的合同状态" @change="handleStatusChange">
            <a-select-option value="draft">草稿</a-select-option>
            <a-select-option value="signed">已签约</a-select-option>
            <a-select-option value="executing">执行中</a-select-option>
            <a-select-option value="completed">已完成</a-select-option>
            <a-select-option value="terminated">已终止</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="状态预览">
          <a-tag :color="getStatusColor(formData.contractStatus)">
            {{ getStatusText(formData.contractStatus) }}
          </a-tag>
        </a-form-item>

        <a-form-item label="备注" name="note">
          <a-textarea v-model:value="formData.note" placeholder="请输入状态变更备注" :rows="3" :maxlength="200" show-count />
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed, watch } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import type { FormInstance } from 'ant-design-vue';
  import type { ContractVO } from '/@/api/project/contract';

  // Props & Emits
  const props = defineProps<{
    visible: boolean;
    contractData: ContractVO | null;
  }>();

  const emit = defineEmits<{
    'update:visible': [value: boolean];
    ok: [data: { contractStatus: string; note?: string }];
    cancel: [];
  }>();

  const { createMessage } = useMessage();

  // Reactive state
  const formRef = ref<FormInstance>();
  const loading = ref(false);

  const modalVisible = computed({
    get: () => props.visible,
    set: value => emit('update:visible', value),
  });

  // Form data
  const formData = reactive({
    contractStatus: '',
    note: '',
  });

  // Form validation rules
  const formRules = {
    contractStatus: [{ required: true, message: '请选择新的合同状态', trigger: 'change' }],
  };

  // Status related methods
  const getStatusColor = (status: string | undefined) => {
    const colorMap: Record<string, string> = {
      draft: 'default',
      signed: 'blue',
      executing: 'processing',
      completed: 'success',
      terminated: 'error',
    };
    return colorMap[status || ''] || 'default';
  };

  const getStatusText = (status: string | undefined) => {
    const textMap: Record<string, string> = {
      draft: '草稿',
      signed: '已签约',
      executing: '执行中',
      completed: '已完成',
      terminated: '已终止',
    };
    return textMap[status || ''] || status || '-';
  };

  // Watch for props changes
  watch(
    () => props.contractData,
    newData => {
      if (newData) {
        formData.contractStatus = newData.contractStatus || '';
        formData.note = '';
      }
    },
    { immediate: true },
  );

  // Event handlers
  const handleStatusChange = (value: string) => {
    // 可以在这里添加状态变更的逻辑，比如提示信息
    if (value === 'terminated') {
      createMessage.warning('注意：终止状态一旦设置，可能需要管理员权限才能修改');
    }
  };

  const handleOk = async () => {
    try {
      await formRef.value?.validate();

      // 检查是否实际有状态变更
      if (formData.contractStatus === props.contractData?.contractStatus) {
        createMessage.warning('请选择不同的状态');
        return;
      }

      loading.value = true;
      emit('ok', {
        contractStatus: formData.contractStatus,
        note: formData.note || undefined,
      });
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const handleCancel = () => {
    formRef.value?.resetFields();
    emit('cancel');
  };
</script>

<style lang="less" scoped>
  .status-update-modal {
    .ant-form-item {
      margin-bottom: 16px;
    }

    .font-medium {
      font-weight: 500;
    }

    // 标签样式
    :deep(.ant-tag) {
      border-radius: 4px;
      font-size: 12px;
    }

    // 选择器样式
    .ant-select {
      &.ant-select-focused {
        .ant-select-selector {
          border-color: #40a9ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
      }
    }
  }
</style>
